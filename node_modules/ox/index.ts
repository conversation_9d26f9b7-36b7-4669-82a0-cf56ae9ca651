/** @entrypointCategory Core */
// biome-ignore lint/complexity/noUselessEmptyExport: tsdoc
export type {}

/**
 * Utilities & types for working with [Application Binary Interfaces (ABIs)](https://docs.soliditylang.org/en/latest/abi-spec.html)
 *
 * :::note
 *
 * If you are looking for ABI parameter **encoding** & **decoding** functions, see {@link ox#AbiParameters.(encode:function)} & {@link ox#AbiParameters.(decode:function)}.
 *
 * :::
 *
 * @example
 * ### Instantiating JSON ABIs
 *
 * An {@link ox#Abi.Abi} can be instantiated from a JSON ABI by using {@link ox#Abi.(from:function)}:
 *
 * ```ts twoslash
 * import { Abi } from 'ox'
 *
 * const abi = Abi.from([{
 *   type: 'function',
 *   name: 'approve',
 *   stateMutability: 'nonpayable',
 *   inputs: [
 *     {
 *       name: 'spender',
 *       type: 'address',
 *     },
 *     {
 *       name: 'amount',
 *       type: 'uint256',
 *     },
 *   ],
 *   outputs: [{ type: 'bool' }],
 * }])
 *
 * abi
 * //^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Instantiating Human Readable ABIs
 *
 * An {@link ox#Abi.Abi} can be instantiated from a human-readable ABI by using {@link ox#Abi.(from:function)}:
 *
 * ```ts twoslash
 * import { Abi } from 'ox'
 *
 * const abi = Abi.from([
 *   'function approve(address spender, uint256 amount) returns (bool)',
 * ])
 *
 * abi
 * //^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Formatting ABIs
 *
 * An {@link ox#Abi.Abi} can be formatted into a human-readable ABI by using {@link ox#Abi.(format:function)}:
 *
 * ```ts twoslash
 * import { Abi } from 'ox'
 * const abi = Abi.from([{
 *   type: 'function',
 *   name: 'approve',
 *   stateMutability: 'nonpayable',
 *   inputs: [
 *     {
 *       name: 'spender',
 *       type: 'address',
 *     },
 *     {
 *       name: 'amount',
 *       type: 'uint256',
 *     },
 *   ],
 *   outputs: [{ type: 'bool' }],
 * }])
 * //---cut---
 * const formatted = Abi.format(abi)
 *
 * formatted
 * //    ^?
 *
 *
 * ```
 *
 * @category ABI
 */
export * as Abi from './core/Abi.js'

/**
 * Utilities & types for working with [Constructors](https://docs.soliditylang.org/en/latest/abi-spec.html#json) on ABIs.
 *
 * `AbiConstructor` is a sub-type of [`AbiItem`](/api/AbiItem).
 *
 * @example
 * ### Instantiating via JSON ABI
 *
 * An `AbiConstructor` can be instantiated from a JSON ABI by using {@link ox#AbiConstructor.(fromAbi:function)}:
 *
 * ```ts twoslash
 * import { Abi, AbiConstructor } from 'ox'
 *
 * const abi = Abi.from([
 *   'constructor(address owner)',
 *   'function foo()',
 *   'event Transfer(address owner, address to, uint256 tokenId)',
 *   'function bar(string a) returns (uint256 x)',
 * ])
 *
 * const item = AbiConstructor.fromAbi(abi) // [!code focus]
 * //    ^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Instantiating via Human-Readable ABI Item
 *
 * An `AbiConstructor` can be instantiated from a human-readable ABI by using {@link ox#AbiConstructor.(from:function)}:
 *
 * ```ts twoslash
 * import { AbiConstructor } from 'ox'
 *
 * const constructor = AbiConstructor.from('constructor(address owner)')
 *
 * constructor
 * //^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Encoding to Deploy Data
 *
 * Constructor arguments can be ABI-encoded using {@link ox#AbiConstructor.(encode:function)} (with bytecode) into deploy data. This data can then be passed to a transaction to deploy a contract.
 *
 * ```ts twoslash
 * import { AbiConstructor } from 'ox'
 *
 * const constructor = AbiConstructor.from('constructor(address, uint256)')
 *
 * const data = AbiConstructor.encode(constructor, { // [!code focus]
 *   bytecode: '0x...', // [!code focus]
 *   args: ['******************************************', 123n], // [!code focus]
 * }) // [!code focus]
 * ```
 *
 * @category ABI
 */
export * as AbiConstructor from './core/AbiConstructor.js'

/**
 * Utilities & types for working with [Errors](https://docs.soliditylang.org/en/latest/abi-spec.html#json) on ABIs.
 *
 * `AbiError` is a sub-type of [`AbiItem`](/api/AbiItem).
 *
 * @example
 * ### Instantiating via JSON ABI
 *
 * An `AbiError` can be instantiated from a JSON ABI by using {@link ox#AbiError.(fromAbi:function)}:
 *
 * ```ts twoslash
 * import { Abi, AbiError } from 'ox'
 *
 * const abi = Abi.from([
 *   'function foo()',
 *   'error BadSignatureV(uint8 v)',
 *   'function bar(string a) returns (uint256 x)',
 * ])
 *
 * const item = AbiError.fromAbi(abi, 'BadSignatureV') // [!code focus]
 * //    ^?
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Instantiating via Human-Readable ABI Item
 *
 * An `AbiError` can be instantiated from a human-readable ABI by using {@link ox#AbiError.(from:function)}:
 *
 * ```ts twoslash
 * import { AbiError } from 'ox'
 *
 * const error = AbiError.from('error BadSignatureV(uint8 v)')
 *
 * error
 * //^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Decoding Error Data
 *
 * Error data can be ABI-decoded using the {@link ox#AbiError.(decode:function)} function.
 *
 * ```ts twoslash
 * // @noErrors
 * import { Abi, AbiError } from 'ox'
 *
 * const abi = Abi.from([...])
 * const error = AbiError.fromAbi(abi, 'InvalidSignature')
 *
 * const value = AbiError.decode(error, '0xecde634900000000000000000000000000000000000000000000000000000000000001a400000000000000000000000000000000000000000000000000000000000000450000000000000000000000000000000000000000000000000000000000000001') // [!code focus]
 * // @log: [420n, 69n, 1]
 * ```
 *
 * @category ABI
 */
export * as AbiError from './core/AbiError.js'

/**
 * Utilities & types for working with [Events](https://docs.soliditylang.org/en/latest/abi-spec.html#json) on ABIs.
 *
 * `AbiEvent` is a sub-type of [`AbiItem`](/api/AbiItem).
 *
 * @example
 * ### Instantiating via JSON ABI
 *
 * An `AbiEvent` can be instantiated from a JSON ABI by using {@link ox#AbiEvent.(fromAbi:function)}:
 *
 * ```ts twoslash
 * import { Abi, AbiEvent } from 'ox'
 *
 * const abi = Abi.from([
 *   'function foo()',
 *   'event Transfer(address owner, address to, uint256 tokenId)',
 *   'function bar(string a) returns (uint256 x)',
 * ])
 *
 * const item = AbiEvent.fromAbi(abi, 'Transfer') // [!code focus]
 * //    ^?
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Instantiating via Human-Readable ABI Item
 *
 * An `AbiEvent` can be instantiated from a human-readable ABI by using {@link ox#AbiEvent.(from:function)}:
 *
 * ```ts twoslash
 * import { AbiEvent } from 'ox'
 *
 * const transfer = AbiEvent.from(
 *   'event Transfer(address indexed from, address indexed to, uint256 value)' // [!code hl]
 * )
 *
 * transfer
 * //^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Encoding to Event Topics
 *
 * Encode an `AbiEvent` into topics using {@link ox#AbiEvent.(encode:function)}:
 *
 * ```ts twoslash
 * import { AbiEvent } from 'ox'
 *
 * const transfer = AbiEvent.from(
 *   'event Transfer(address indexed from, address indexed to, uint256 value)'
 * )
 *
 * const { topics } = AbiEvent.encode(transfer, {
 *   from: '0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266', // [!code hl]
 *   to: '******************************************' // [!code hl]
 * })
 * // @log: [
 * // @log:   '0x406dade31f7ae4b5dbc276258c28dde5ae6d5c2773c5745802c493a2360e55e0',
 * // @log:   '0x00000000000000000000000000000000f39fd6e51aad88f6f4ce6ab8827279cfffb92266',
 * // @log:   '0x0000000000000000000000000000000070997970c51812dc3a010c7d01b50e0d17dc79c8'
 * // @log: ]
 * ```
 *
 * @example
 * ### Decoding Event Topics and Data
 *
 * Event topics and data can be decoded using {@link ox#AbiEvent.(decode:function)}:
 *
 * ```ts twoslash
 * import { AbiEvent } from 'ox'
 *
 * const transfer = AbiEvent.from(
 *   'event Transfer(address indexed from, address indexed to, uint256 value)'
 * )
 *
 * const log = {
 *   // ...
 *   data: '******************************************000000000000000000000001',
 *   topics: [
 *     '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',
 *     '0x000000000000000000000000a5cc3c03994db5b0d9a5eedd10cabab0813678ac',
 *     '0x000000000000000000000000a5cc3c03994db5b0d9a5eedd10cabab0813678ac',
 *   ],
 * } as const
 *
 * const decoded = AbiEvent.decode(transfer, log)
 * // @log: {
 * // @log:   from: '0xa5cc3c03994db5b0d9a5eedd10cabab0813678ac',
 * // @log:   to: '0xa5cc3c03994db5b0d9a5eedd10cabab0813678ac',
 * // @log:   value: 1n
 * // @log: }
 * ```
 *
 * @category ABI
 */
export * as AbiEvent from './core/AbiEvent.js'

/**
 * Utilities & types for working with [Functions](https://docs.soliditylang.org/en/latest/abi-spec.html#json) on ABIs.
 *
 * `AbiFunction` is a sub-type of [`AbiItem`](/api/AbiItem).
 *
 * @example
 * ### Instantiating via JSON ABI
 *
 * An `AbiFunction` can be instantiated from a JSON ABI by using {@link ox#AbiFunction.(fromAbi:function)}:
 *
 * ```ts twoslash
 * import { Abi, AbiFunction } from 'ox'
 *
 * const abi = Abi.from([
 *   'function foo()',
 *   'event Transfer(address owner, address to, uint256 tokenId)',
 *   'function bar(string a) returns (uint256 x)',
 * ])
 *
 * const item = AbiFunction.fromAbi(abi, 'bar') // [!code focus]
 * //    ^?
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Instantiating via Human-Readable ABI Item
 *
 * An `AbiFunction` can be instantiated from a human-readable ABI by using {@link ox#AbiFunction.(from:function)}:
 *
 * ```ts twoslash
 * import { AbiFunction } from 'ox'
 *
 * const bar = AbiFunction.from('function bar(string a) returns (uint256 x)')
 *
 * bar
 * //^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Encoding to Function Data
 *
 * A Function and its arguments can be ABI-encoded into data using the {@link ox#AbiFunction.(encodeData:function)} function. The output of this function can then be passed to `eth_sendTransaction` or `eth_call` as the `data` parameter.
 *
 * ```ts twoslash
 * import { AbiFunction } from 'ox'
 *
 * const approve = AbiFunction.from('function approve(address, uint256)')
 *
 * const data = AbiFunction.encodeData( // [!code focus]
 *   approve, // [!code focus]
 *   ['******************************************', 69420n] // [!code focus]
 * ) // [!code focus]
 * // @log: '0x095ea7b3000000000000000000000000d8da6bf26964af9d7eed9e03e53415d37aa960450000000000000000000000000000000000000000000000000000000000010f2c'
 * ```
 *
 * @example
 * ### Decoding a Function's Result
 *
 * A Function's result can be ABI-decoded using the {@link ox#AbiFunction.(decodeResult:function)} function.
 *
 * ```ts twoslash
 * import { AbiFunction } from 'ox'
 *
 * const data = '******************************************00000000000000000000002a'
 * //    ↑ Example data that could be returned from a contract call via `eth_call`.
 *
 * const totalSupply = AbiFunction.from('function totalSupply() returns (uint256)')
 *
 * const output = AbiFunction.decodeResult(totalSupply, data) // [!code focus]
 * // @log: 42n
 * ```
 *
 * @category ABI
 */
export * as AbiFunction from './core/AbiFunction.js'

/**
 * Utilities & types for working with [ABI Items](https://docs.soliditylang.org/en/latest/abi-spec.html#json)
 *
 * The `AbiItem` type is a super-type of:
 * - [`AbiConstructor`](/api/AbiConstructor)
 * - [`AbiFunction`](/api/AbiFunction)
 * - [`AbiEvent`](/api/AbiEvent)
 * - [`AbiError`](/api/AbiError)
 *
 * @example
 * ### Instantiating via JSON ABI
 *
 * An `AbiItem` can be instantiated from a JSON ABI by using {@link ox#AbiItem.(fromAbi:function)}:
 *
 * ```ts twoslash
 * import { Abi, AbiItem } from 'ox'
 *
 * const abi = Abi.from([
 *   'function foo()',
 *   'event Transfer(address owner, address to, uint256 tokenId)',
 *   'function bar(string a) returns (uint256 x)',
 * ])
 *
 * const item = AbiItem.fromAbi(abi, 'Transfer') // [!code focus]
 * //    ^?
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Instantiating via Human-Readable ABI Item
 *
 * A Human Readable ABI can be parsed into a typed ABI object:
 *
 * ```ts twoslash
 * import { AbiItem } from 'ox'
 *
 * const abiItem = AbiItem.from('function approve(address spender, uint256 amount) returns (bool)')
 *
 * abiItem
 * //^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Formatting ABI Items
 *
 * An `AbiItem` can be formatted into a human-readable ABI Item by using {@link ox#AbiItem.(format:function)}:
 *
 * ```ts twoslash
 * import { AbiItem } from 'ox'
 *
 * const abiItem = AbiItem.from('function approve(address spender, uint256 amount) returns (bool)')
 *
 * const formatted = AbiItem.format(abiItem)
 * // @log: 'function approve(address spender, uint256 amount) returns (bool)'
 * ```
 *
 * @category ABI
 */
export * as AbiItem from './core/AbiItem.js'

/**
 * Utilities & types for encoding, decoding, and working with [ABI Parameters](https://docs.soliditylang.org/en/latest/abi-spec.html#types)
 *
 * @example
 * ### Encoding ABI Parameters
 *
 * ABI Parameters can be ABI-encoded as per the [Application Binary Interface (ABI) Specification](https://docs.soliditylang.org/en/latest/abi-spec) using {@link ox#AbiParameters.(encode:function)}:
 *
 * ```ts twoslash
 * import { AbiParameters } from 'ox'
 *
 * const data = AbiParameters.encode(
 *   AbiParameters.from('string, uint, bool'),
 *   ['wagmi', 420n, true],
 * )
 * ```
 *
 * :::tip
 *
 * The example above uses {@link ox#AbiParameters.(from:function)} to specify human-readable ABI Parameters.
 *
 * However, you can also pass JSON-ABI Parameters:
 *
 * ```ts
 * import { AbiParameters } from 'ox'
 *
 * const data = AbiParameters.encode(
 *   [{ type: 'string' }, { type: 'uint' }, { type: 'bool' }],
 *   ['wagmi', 420n, true],
 * )
 * ```
 *
 * :::
 *
 * @example
 * ### Decoding ABI Parameters
 *
 * ABI-encoded data can be decoded using {@link ox#AbiParameters.(decode:function)}:
 *
 * ```ts twoslash
 * import { AbiParameters } from 'ox'
 *
 * const data = AbiParameters.decode(
 *   AbiParameters.from('string, uint, bool'),
 *   '******************************************00000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',
 * )
 * // @log: ['wagmi', 420n, true]
 * ```
 *
 * @example
 * ### JSON-ABI Parameters
 *
 * JSON-ABI Parameters can be instantiated using {@link ox#AbiParameters.(from:function)}:
 *
 * ```ts twoslash
 * import { AbiParameters } from 'ox'
 *
 * const parameters = AbiParameters.from([
 *   {
 *     name: 'spender',
 *     type: 'address',
 *   },
 *   {
 *     name: 'amount',
 *     type: 'uint256',
 *   },
 * ])
 *
 * parameters
 * //^?
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @example
 * ### Human Readable ABI Parameters
 *
 * Human Readable ABI Parameters can be instantiated using {@link ox#AbiParameters.(from:function)}:
 *
 * ```ts twoslash
 * import { AbiParameters } from 'ox'
 *
 * const parameters = AbiParameters.from('address spender, uint256 amount')
 *
 * parameters
 * //^?
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @category ABI
 */
export * as AbiParameters from './core/AbiParameters.js'

/**
 * Utilities & types for working with Access Lists as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/4140e528360fea53c34a766d86a000c6c039100e/src/schemas/transaction.yaml#L73)
 *
 * @category Execution Spec
 */
export * as AccessList from './core/AccessList.js'

/**
 * Utilities & types for working with Account Proofs as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/state.yaml)
 *
 * @category Execution Spec
 */
export * as AccountProof from './core/AccountProof.js'

/**
 * Utility functions for working with Ethereum addresses.
 *
 * @example
 * ### Instantiating Addresses
 *
 * An {@link ox#Address.Address} can be instantiated from a hex string using {@link ox#Address.(from:function)}:
 *
 * ```ts twoslash
 * import { Address } from 'ox'
 *
 * const address = Address.from('******************************************')
 * // @log: '******************************************'
 * ```
 *
 * @example
 * ### Validating Addresses
 *
 * The {@link ox#Address.(validate:function)} function will return `true` if the address is valid, and `false` otherwise:
 *
 * ```ts twoslash
 * import { Address } from 'ox'
 *
 * const valid = Address.validate('******************************************')
 * // @log: true
 * ```
 *
 * The {@link ox#Address.(assert:function)} function will throw an error if the address is invalid:
 *
 * ```ts twoslash
 * import { Address } from 'ox'
 *
 * Address.assert('0xdeadbeef')
 * // @error: InvalidAddressError: Address "0xdeadbeef" is invalid.
 * ```
 *
 * @example
 * ### Addresses from ECDSA Public Keys
 *
 * An {@link ox#Address.Address} can be computed from an ECDSA public key using {@link ox#Address.(fromPublicKey:function)}:
 *
 * ```ts twoslash
 * import { Address, Secp256k1 } from 'ox'
 *
 * const privateKey = Secp256k1.randomPrivateKey()
 * const publicKey = Secp256k1.getPublicKey({ privateKey })
 *
 * const address = Address.fromPublicKey(publicKey)
 * // @log: '******************************************'
 * ```
 *
 * @category Addresses
 */
export * as Address from './core/Address.js'

/**
 * Utilities & types for working with AES-GCM encryption. Internally uses the [Web Crypto API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Crypto_API).
 *
 * @example
 * ### Encrypting Data
 *
 * Data can be encrypted using {@link ox#AesGcm.(encrypt:function)}:
 *
 * ```ts twoslash
 * import { AesGcm, Hex } from 'ox'
 *
 * const key = await AesGcm.getKey({ password: 'qwerty' })
 * const secret = Hex.fromString('i am a secret message')
 *
 * const encrypted = await AesGcm.encrypt(secret, key) // [!code focus]
 * // @log: '0x5e257b25bcf53d5431e54e5a68ca0138306d31bb6154f35a97bb8ea18111e7d82bcf619d3c76c4650688bc5310eed80b8fc86d1e3e'
 * ```
 *
 * @example
 * ### Decrypting Data
 *
 * Data can be decrypted using {@link ox#AesGcm.(decrypt:function)}:
 *
 * ```ts twoslash
 * import { AesGcm, Hex } from 'ox'
 *
 * const key = await AesGcm.getKey({ password: 'qwerty' })
 * const encrypted = await AesGcm.encrypt(Hex.fromString('i am a secret message'), key)
 *
 * const decrypted = await AesGcm.decrypt(encrypted, key) // [!code focus]
 * // @log: Hex.fromString('i am a secret message')
 * ```
 *
 * @category Crypto
 */
export * as AesGcm from './core/AesGcm.js'

/**
 * Utility functions for working with [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization lists & tuples.
 *
 * @example
 * ### Instantiating Authorizations
 *
 * An Authorization can be instantiated using {@link ox#Authorization.(from:function)}:
 *
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 69n,
 * })
 * ```
 *
 * @example
 * ### Computing Sign Payload
 *
 * A signing payload can be computed using {@link ox#Authorization.(getSignPayload:function)}. The result can then be passed to signing functions like {@link ox#Secp256k1.(sign:function)}.
 *
 * ```ts twoslash
 * import { Authorization, Secp256k1 } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 69n,
 * })
 *
 * const payload = Authorization.getSignPayload(authorization) // [!code focus]
 *
 * const signature = Secp256k1.sign({
 *   payload,
 *   privateKey: '0x...',
 * })
 * ```
 *
 * @example
 * ### Attaching Signatures to Authorizations
 *
 * A signature can be attached to an Authorization using {@link ox#Authorization.(from:function)}:
 *
 * ```ts twoslash
 * import { Authorization, Secp256k1, TransactionEnvelopeEip7702, Value } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '0xbe95c3f554e9fc85ec51be69a3d807a0d55bcf2c',
 *   chainId: 1,
 *   nonce: 40n,
 * })
 *
 * const signature = Secp256k1.sign({
 *   payload: Authorization.getSignPayload(authorization),
 *   privateKey: '0x...',
 * })
 *
 * const authorization_signed = Authorization.from(authorization, { signature }) // [!code focus]
 *
 * const envelope = TransactionEnvelopeEip7702.from({
 *   authorizationList: [authorization_signed],
 *   chainId: 1,
 *   maxFeePerGas: Value.fromGwei('10'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 * ```
 *
 * @category Authorization (EIP-7702)
 */
export * as Authorization from './core/Authorization.js'

/**
 * Utility functions for working with [Base58](https://digitalbazaar.github.io/base58-spec/) values.
 *
 * @example
 * ### Encoding to Base58
 *
 * Values can be encoded to Base58 with:
 *
 * - {@link ox#Base58.(fromString:function)}, or
 *
 * - {@link ox#Base58.(fromBytes:function)}, or
 *
 * - {@link ox#Base58.(fromHex:function)}
 *
 * ```ts twoslash
 * import { Base58 } from 'ox'
 *
 * const value_string = Base58.fromString('Hello World!')
 * // @log: '2NEpo7TZRRrLZSi2U'
 *
 * const value_bytes = Base58.fromBytes(new Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))
 * // @log: '2NEpo7TZRRrLZSi2U'
 *
 * const value_hex = Base58.fromHex('0x48656c6c6f20576f726c6421')
 * // @log: '2NEpo7TZRRrLZSi2U'
 * ```
 *
 * @example
 * ### Decoding Base58
 *
 * Values can be decoded from Base58 with:
 *
 * - {@link ox#Base58.(toString:function)}, or
 *
 * - {@link ox#Base58.(toBytes:function)}, or
 *
 * - {@link ox#Base58.(toHex:function)}
 *
 * ```ts twoslash
 * import { Base58 } from 'ox'
 *
 * const value_string = Base58.toString('2NEpo7TZRRrLZSi2U')
 * // @log: 'Hello World!'
 *
 * const value_bytes = Base58.toBytes('2NEpo7TZRRrLZSi2U')
 * // @log: Uint8Array [72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]
 *
 * const value_hex = Base58.toHex('2NEpo7TZRRrLZSi2U')
 * // @log: '0x48656c6c6f20576f726c6421'
 * ```
 *
 * @category Data
 */
export * as Base58 from './core/Base58.js'

/**
 * Utility functions for working with [RFC-4648](https://datatracker.ietf.org/doc/html/rfc4648) Base64.
 *
 * @example
 * ### Encoding to Base64
 *
 * Values can be encoded to Base64 with:
 *
 * - {@link ox#Base64.(fromString:function)}, or
 *
 * - {@link ox#Base64.(fromBytes:function)}, or
 *
 * - {@link ox#Base64.(fromHex:function)}
 *
 * ```ts twoslash
 * import { Base64 } from 'ox'
 *
 * const value_string = Base64.fromString('Hello World!')
 * // @log: 'SGVsbG8gV29ybGQh=='
 *
 * const value_bytes = Base64.fromBytes(new Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))
 * // @log: 'SGVsbG8gV29ybGQh=='
 *
 * const value_hex = Base64.fromHex('0x48656c6c6f20576f726c6421')
 * // @log: 'SGVsbG8gV29ybGQh=='
 * ```
 *
 * ### Decoding Base64
 *
 * Values can be decoded from Base64 with:
 *
 * - {@link ox#Base64.(toString:function)}, or
 *
 * - {@link ox#Base64.(toBytes:function)}, or
 *
 * - {@link ox#Base64.(toHex:function)}
 *
 * ```ts twoslash
 * import { Base64 } from 'ox'
 *
 * const value_string = Base64.toString('SGVsbG8gV29ybGQh==')
 * // @log: 'Hello World!'
 *
 * const value_bytes = Base64.toBytes('SGVsbG8gV29ybGQh==')
 * // @log: Uint8Array [72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]
 *
 * const value_hex = Base64.toHex('SGVsbG8gV29ybGQh==')
 * // @log: '0x48656c6c6f20576f726c6421'
 * ```
 *
 * @category Data
 */
export * as Base64 from './core/Base64.js'

/**
 * Utility functions for working with [EIP-7864](https://eips.ethereum.org/EIPS/eip-7864) Binary State Trees.
 *
 * @category Binary State Tree (EIP-7864)
 */
export * as BinaryStateTree from './core/BinaryStateTree.js'

/**
 * Utility functions for working with [EIP-4844](https://eips.ethereum.org/EIPS/eip-4844) Blobs.
 *
 * @category Blobs (EIP-4844)
 */
export * as Blobs from './core/Blobs.js'

/**
 * Utilities & types for working with Blocks as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/block.yaml)
 *
 * @example
 * ### Converting from RPC Format
 *
 * Blocks can be converted from RPC format to internal format using {@link ox#Block.(fromRpc:function)}:
 *
 * ```ts twoslash
 * import 'ox/window'
 * import { Block } from 'ox'
 *
 * const block = await window.ethereum!
 *   .request({
 *     method: 'eth_getBlockByNumber',
 *     params: ['latest', false],
 *   })
 *   .then(Block.fromRpc) // [!code hl]
 * // @log: {
 * // @log:   // ...
 * // @log:   hash: '0xebc3644804e4040c0a74c5a5bbbc6b46a71a5d4010fe0c92ebb2fdf4a43ea5dd',
 * // @log:   number: 19868020n,
 * // @log:   size: 520n,
 * // @log:   timestamp: 1662222222n,
 * // @log:   // ...
 * // @log: }
 * ```
 *
 * @category Execution Spec
 */
export * as Block from './core/Block.js'

/**
 * Utilities & types for working with **Block Overrides**.
 *
 * @category Execution Spec
 */
export * as BlockOverrides from './core/BlockOverrides.js'

/**
 * Utility functions for working with Bloom Filters as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/block.yaml)
 *
 * @category Execution Spec
 */
export * as Bloom from './core/Bloom.js'

/**
 * Utility functions for [BLS12-381](https://hackmd.io/\@benjaminion/bls12-381) cryptography.
 *
 * :::info
 *
 * The `Bls` module is a friendly wrapper over [`@noble/curves/bls12-381`](https://github.com/paulmillr/noble-curves), an **audited** implementation of BLS12-381.
 *
 * :::
 *
 * @example
 * ### Computing a Random Private Key
 *
 * A random private key can be computed using {@link ox#Bls.(randomPrivateKey:function)}:
 *
 * ```ts twoslash
 * import { Bls } from 'ox'
 *
 * const privateKey = Bls.randomPrivateKey()
 * // @log: '0x...'
 * ```
 *
 * @example
 * ### Getting a Public Key
 *
 * A public key can be derived from a private key using {@link ox#Bls.(getPublicKey:function)}:
 *
 * ```ts twoslash
 * import { Bls } from 'ox'
 *
 * const privateKey = Bls.randomPrivateKey()
 * const publicKey = Bls.getPublicKey({ privateKey })
 * // @log: { x: 3251...5152n, y: 1251...5152n, z: 1n }
 * ```
 *
 * @example
 * ### Signing a Payload
 *
 * A payload can be signed using {@link ox#Bls.(sign:function)}:
 *
 * ```ts twoslash
 * import { Bls } from 'ox'
 *
 * const privateKey = Bls.randomPrivateKey()
 * const signature = Bls.sign({ payload: '0xdeadbeef', privateKey })
 * // @log: { x: 1251...5152n, y: 1251...5152n, z: 1n }
 * ```
 *
 * @example
 * ### Verifying a Signature
 *
 * A signature can be verified using {@link ox#Secp256k1.(verify:function)}:
 *
 * ```ts twoslash
 * import { Bls } from 'ox'
 *
 * const privateKey = Bls.randomPrivateKey()
 * const publicKey = Bls.getPublicKey({ privateKey })
 * const signature = Bls.sign({ payload: '0xdeadbeef', privateKey })
 *
 * const isValid = Bls.verify({ // [!code focus]
 *   payload: '0xdeadbeef', // [!code focus]
 *   publicKey, // [!code focus]
 *   signature, // [!code focus]
 * }) // [!code focus]
 * // @log: true
 * ```
 *
 * @example
 * ### Aggregating Public Keys & Signatures
 *
 * Public keys and signatures can be aggregated using {@link ox#Bls.(aggregate:function)}:
 *
 * ```ts twoslash
 * import { Bls } from 'ox'
 *
 * const publicKeys = [
 *   Bls.getPublicKey({ privateKey: '0x...' }),
 *   Bls.getPublicKey({ privateKey: '0x...' }),
 * ]
 * const publicKey = Bls.aggregate(publicKeys)
 *
 * const signatures = [
 *   Bls.sign({ payload: '0x...', privateKey: '0x...' }),
 *   Bls.sign({ payload: '0x...', privateKey: '0x...' }),
 * ]
 * const signature = Bls.aggregate(signatures)
 * ```
 *
 * @example
 * ### Verify Aggregated Signatures
 *
 * We can also pass a public key and signature that was aggregated with {@link ox#Bls.(aggregate:function)} to `Bls.verify`.
 *
 * ```ts twoslash
 * import { Bls, Hex } from 'ox'
 *
 * const payload = Hex.random(32)
 * const privateKeys = Array.from({ length: 100 }, () => Bls.randomPrivateKey())
 *
 * const publicKeys = privateKeys.map((privateKey) =>
 *   Bls.getPublicKey({ privateKey }),
 * )
 * const signatures = privateKeys.map((privateKey) =>
 *   Bls.sign({ payload, privateKey }),
 * )
 *
 * const publicKey = Bls.aggregate(publicKeys) // [!code focus]
 * const signature = Bls.aggregate(signatures) // [!code focus]
 *
 * const valid = Bls.verify({ payload, publicKey, signature }) // [!code focus]
 * ```
 *
 * @category Crypto
 */
export * as Bls from './core/Bls.js'

/**
 * Utility functions for working with BLS12-381 points.
 *
 * :::info
 *
 * The `BlsPoint` module is a friendly wrapper over [`@noble/curves/bls12-381`](https://github.com/paulmillr/noble-curves), an **audited** implementation of BLS12-381.
 *
 * :::
 *
 * @example
 * ### Public Keys or Signatures to Hex
 *
 * BLS points can be converted to hex using {@link ox#BlsPoint.(toHex:function)}:
 *
 * ```ts twoslash
 * import { Bls, BlsPoint } from 'ox'
 *
 * const publicKey = Bls.getPublicKey({ privateKey: '0x...' })
 * const publicKeyHex = BlsPoint.toHex(publicKey)
 * // @log: '0xacafff52270773ad1728df2807c0f1b0b271fa6b37dfb8b2f75448573c76c81bcd6790328a60e40ef5a13343b32d9e66'
 *
 * const signature = Bls.sign({ payload: '0xdeadbeef', privateKey: '0x...' })
 * const signatureHex = BlsPoint.toHex(signature)
 * // @log: '0xb4698f7611999fba87033b9cf72312c76c683bbc48175e2d4cb275907d6a267ab9840a66e3051e5ed36fd13aa712f9a9024f9fa9b67f716dfb74ae4efb7d9f1b7b43b4679abed6644cf476c12e79f309351ea8452487cd93f66e29e04ebe427c'
 * ```
 *
 * @example
 * ### Hex to Public Keys or Signatures
 *
 * BLS points can be converted from hex using {@link ox#BlsPoint.(fromHex:function)}:
 *
 * ```ts twoslash
 * import { Bls, BlsPoint } from 'ox'
 *
 * const publicKey = BlsPoint.fromHex('0xacafff52270773ad1728df2807c0f1b0b271fa6b37dfb8b2f75448573c76c81bcd6790328a60e40ef5a13343b32d9e66', 'G1')
 * // @log: { x: 172...514n, y: 175...235n, z: 1n }
 *
 * const signature = BlsPoint.fromHex('0xb4698f7611999fba87033b9cf72312c76c683bbc48175e2d4cb275907d6a267ab9840a66e3051e5ed36fd13aa712f9a9024f9fa9b67f716dfb74ae4efb7d9f1b7b43b4679abed6644cf476c12e79f309351ea8452487cd93f66e29e04ebe427c', 'G2')
 * // @log: { x: 1251...5152n, y: 1251...5152n, z: 1n }
 * ```
 *
 * @category Crypto
 */
export * as BlsPoint from './core/BlsPoint.js'

/**
 * A set of Ethereum-related utility functions for working with [`Uint8Array`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array) instances.
 *
 * @example
 * ### Instantiating Bytes
 *
 * Values can be instantiated as {@link ox#Bytes.Bytes} using:
 *
 * - {@link ox#Bytes.(fromArray:function)}
 *
 * - {@link ox#Bytes.(fromBoolean:function)}
 *
 * - {@link ox#Bytes.(fromHex:function)}
 *
 * - {@link ox#Bytes.(fromNumber:function)}
 *
 * - {@link ox#Bytes.(fromString:function)}
 *
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const value_array = Bytes.from([1, 2, 3, 4, 5])
 * // @log: Uint8Array [1, 2, 3, 4, 5]
 *
 * const value_boolean = Bytes.fromBoolean(true)
 * // @log: Uint8Array [1]
 *
 * const value_hex = Bytes.fromHex('0x1234567890abcdef')
 * // @log: Uint8Array [18, 52, 86, 120, 144, 175, 207, 15]
 *
 * const value_number = Bytes.fromNumber(1234567890)
 * // @log: Uint8Array [4, 160, 216]
 *
 * const value_string = Bytes.fromString('Hello World!')
 * // @log: Uint8Array [72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]
 * ```
 *
 * @example
 * ### Converting from Bytes
 *
 * Values can be converted from {@link ox#Bytes.Bytes} using:
 *
 * - {@link ox#Bytes.(toBigInt:function)}
 *
 * - {@link ox#Bytes.(toBoolean:function)}
 *
 * - {@link ox#Bytes.(toHex:function)}
 *
 * - {@link ox#Bytes.(toNumber:function)}
 *
 * - {@link ox#Bytes.(toString:function)}
 *
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const value_bigint = Bytes.toBigInt(Bytes.from([4, 160, 216]))
 * // @log: 1234567890n
 *
 * const value_boolean = Bytes.toBoolean(Bytes.from([1]))
 * // @log: true
 *
 * const value_hex = Bytes.toHex(Bytes.from([222, 173, 190, 239]))
 * // @log: '0xdeadbeef'
 *
 * const value_number = Bytes.toNumber(Bytes.from([4, 160, 216]))
 * // @log: 1234567890
 *
 * const value_string = Bytes.toString(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))
 * // @log: 'Hello World!'
 * ```
 *
 * @example
 * ### Concatenating Bytes
 *
 * Values can be concatenated using {@link ox#Bytes.(concat:function)}:
 *
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const a = Bytes.from([1, 2, 3])
 * const b = Bytes.from([4, 5, 6])
 * const c = Bytes.concat(a, b)
 * // @log: Uint8Array [1, 2, 3, 4, 5, 6]
 * ```
 *
 * @example
 * ### Slicing Bytes
 *
 * Values can be sliced using {@link ox#Bytes.(slice:function)}:
 *
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const value = Bytes.slice(Bytes.from([1, 2, 3, 4, 5, 6]), 2, 4)
 * // @log: Uint8Array [3, 4]
 * ```
 *
 * @example
 * ### Padding Bytes
 *
 * Values can be padded with zeroes using {@link ox#Bytes.(padLeft:function)} or {@link ox#Bytes.(padRight:function)}:
 *
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const value_1 = Bytes.padLeft(Bytes.from([1, 2, 3]), 5)
 * // @log: Uint8Array [0, 0, 1, 2, 3]
 *
 * const value_2 = Bytes.padRight(Bytes.from([1, 2, 3]), 5)
 * // @log: Uint8Array [1, 2, 3, 0, 0]
 * ```
 *
 * @example
 * ### Trimming Bytes
 *
 * Zeroes in values can be trimmed using {@link ox#Bytes.(trimLeft:function)} or {@link ox#Bytes.(trimRight:function)}:
 *
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const value = Bytes.trimLeft(Bytes.from([0, 0, 1, 2, 3]))
 * // @log: Uint8Array [1, 2, 3]
 * ```
 *
 * @category Data
 */
export * as Bytes from './core/Bytes.js'

export * as Caches from './core/Caches.js'

/**
 * Utility functions for computing Contract Addresses.
 *
 * @example
 * ### Computing Contract Addresses (CREATE)
 *
 * A Contract Address that was instantiated using the `CREATE` opcode can be computed using {@link ox#ContractAddress.(fromCreate:function)}:
 *
 * ```ts twoslash
 * import { ContractAddress } from 'ox'
 *
 * ContractAddress.fromCreate({
 *   from: '0x1a1e021a302c237453d3d45c7b82b19ceeb7e2e6',
 *   nonce: 0n,
 * })
 * // @log: '0xFBA3912Ca04dd458c843e2EE08967fC04f3579c2'
 * ```
 *
 * @example
 * ### Computing Contract Addresses (CREATE2)
 *
 * A Contract Address that was instantiated using the `CREATE2` opcode can be computed using {@link ox#ContractAddress.(fromCreate2:function)}:
 *
 * ```ts twoslash
 * import { Bytes, ContractAddress, Hex } from 'ox'
 *
 * ContractAddress.fromCreate2({
 *   from: '0x1a1e021a302c237453d3d45c7b82b19ceeb7e2e6',
 *   bytecode: Bytes.from('0x6394198df16000526103ff60206004601c335afa6040516060f3'),
 *   salt: Hex.fromString('hello world'),
 * })
 * // @log: '******************************************'
 * ```
 *
 * @category Addresses
 */
export * as ContractAddress from './core/ContractAddress.js'

/**
 * Utility functions for working with ENS names.
 *
 * @example
 * ### Normalizing ENS Names
 *
 * ENS names can be normalized using {@link ox#Ens.(normalize:function)}:
 *
 * ```ts twoslash
 * import { Ens } from 'ox'
 *
 * const name = Ens.normalize('w𝝣vm.eth')
 * // @log: 'wξvm.eth'
 * ```
 *
 * @example
 * ### Namehashing ENS Names
 *
 * ENS names can be namehashed using {@link ox#Ens.(namehash:function)}:
 *
 * ```ts twoslash
 * import { Ens } from 'ox'
 *
 * const name = Ens.namehash('alice.eth')
 * // @log: '0x787192fc5378cc32aa956ddfdedbf26b24e8d78e40109add0eea2c1a012c3dec'
 * ```
 *
 * @category ENS
 */
export * as Ens from './core/Ens.js'

export * as Errors from './core/Errors.js'

/**
 * Utilities & types for working with Filters as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/filter.yaml)
 *
 * @category Execution Spec
 */
export * as Filter from './core/Filter.js'

/**
 * Utility functions for hashing (keccak256, sha256, etc).
 *
 * @example
 * ```ts twoslash
 * import { Hash } from 'ox'
 *
 * const value = Hash.keccak256('0xdeadbeef')
 * // '0xd4fd4e189132273036449fc9e11198c739161b4c0116a9a2dccdfa1c492006f1'
 * ```
 *
 * @category Crypto
 */
export * as Hash from './core/Hash.js'

/**
 * Utility functions for generating and working with [BIP-32](https://github.com/bitcoin/bips/blob/master/bip-0032.mediawiki) HD Wallets.
 *
 * :::info
 *
 * The `HdKey` module is a friendly wrapper over [`@scure/bip32`](https://github.com/paulmillr/scure-bip32), an **audited** implementation of [BIP-32](https://github.com/bitcoin/bips/blob/master/bip-0032.mediawiki) HD Wallets.
 *
 * :::
 *
 *
 * @category Crypto
 */
export * as HdKey from './core/HdKey.js'

/**
 * A set of Ethereum-related utility functions for working with hexadecimal string values (e.g. `"0xdeadbeef"`).
 *
 * @example
 * ### Instantiating Hex
 *
 * Values can be instantiated as {@link ox#Hex.Hex} using:
 *
 * - {@link ox#Hex.(fromBoolean:function)}
 *
 * - {@link ox#Hex.(fromBytes:function)}
 *
 * - {@link ox#Hex.(fromNumber:function)}
 *
 * - {@link ox#Hex.(fromString:function)}
 *
 * ```ts twoslash
 * import { Bytes, Hex } from 'ox'
 *
 * const value_boolean = Hex.fromBoolean(true)
 * // @log: '0x1'
 *
 * const value_bytes = Hex.fromBytes(Bytes.from([1, 2, 3]))
 * // @log: '0x010203'
 *
 * const value_number = Hex.fromNumber(1234567890)
 * // @log: '0x499602d2'
 *
 * const value_string = Hex.fromString('Hello World!')
 * // @log: '0x48656c6c6f20576f726c6421'
 * ```
 *
 * @example
 * ### Converting from Hex
 *
 * Values can be converted from {@link ox#Hex.Hex} using:
 *
 * - {@link ox#Hex.(toBoolean:function)}
 *
 * - {@link ox#Hex.(toBytes:function)}
 *
 * - {@link ox#Hex.(toNumber:function)}
 *
 * - {@link ox#Hex.(toString:function)}
 *
 * ```ts twoslash
 * import { Hex } from 'ox'
 *
 * const value_boolean = Hex.toBoolean('0x1')
 * // @log: true
 *
 * const value_bytes = Hex.toBytes('0x010203')
 * // @log: Uint8Array [1, 2, 3]
 *
 * const value_number = Hex.toNumber('0x499602d2')
 * // @log: 1234567890
 *
 * const value_string = Hex.toString('0x48656c6c6f20576f726c6421')
 * // @log: 'Hello World!'
 * ```
 *
 * @example
 * ### Concatenating Hex
 *
 * Hex values can be concatenated using {@link ox#Hex.(concat:function)}:
 *
 * ```ts twoslash
 * import { Hex } from 'ox'
 *
 * const a = Hex.fromString('0x1234567890abcdef')
 * const b = Hex.fromString('0xdeadbeef')
 * const c = Hex.concat(a, b)
 * // @log: '0x1234567890abcdefdeadbeef'
 * ```
 *
 * @example
 * ### Slicing Hex
 *
 * Hex values can be sliced using {@link ox#Hex.(slice:function)}:
 *
 * ```ts twoslash
 * import { Hex } from 'ox'
 *
 * const value = Hex.slice('0x1234567890abcdefdeadbeef', 2, 8)
 * // @log: '0x34567890'
 * ```
 *
 * @example
 * ### Padding Hex
 *
 * Hex values can be padded with zeroes using {@link ox#Hex.(padLeft:function)} or {@link ox#Hex.(padRight:function)}:
 *
 * ```ts twoslash
 * import { Hex } from 'ox'
 *
 * const value = Hex.padLeft('0x1234567890abcdef', 16)
 * // @log: '0x00000000000000001234567890abcdef'
 * ```
 *
 * @example
 * ### Trimming Hex
 *
 * Hex values can be trimmed of zeroes using {@link ox#Hex.(trimLeft:function)} or {@link ox#Hex.(trimRight:function)}:
 *
 * ```ts twoslash
 * import { Hex } from 'ox'
 *
 * const value = Hex.trimLeft('0x00000000000000001234567890abcdef')
 * // @log: '0x1234567890abcdef'
 * ```
 *
 * @category Data
 */
export * as Hex from './core/Hex.js'

/**
 * @category Execution Spec
 */
export * as Fee from './core/Fee.js'

/**
 * Utility functions for working with JSON (with support for `bigint`).
 *
 * @example
 * ### Stringifying JSON
 *
 * JSON values can be stringified (with `bigint` support) using {@link ox#Json.(stringify:function)}:
 *
 * ```ts twoslash
 * import { Json } from 'ox'
 *
 * const json = Json.stringify({
 *   foo: 'bar',
 *   baz: 69420694206942069420694206942069420694206942069420n,
 * })
 * // @log: '{"foo":"bar","baz":69420694206942069420694206942069420694206942069420}'
 * ```
 *
 * @example
 * ### Parsing JSON
 *
 * JSON values can be parsed (with `bigint` support) using {@link ox#Json.(parse:function)}:
 *
 * ```ts twoslash
 * import { Json } from 'ox'
 *
 * const value = Json.parse('{"foo":"bar","baz":69420694206942069420694206942069420694206942069420}')
 * // @log: { foo: 'bar', baz: 69420694206942069420694206942069420694206942069420n }
 * ```
 *
 * @category JSON
 */
export * as Json from './core/Json.js'

/**
 * Utilities & types for working with [Keystores](https://ethereum.org/en/developers/docs/data-structures-and-encoding/web3-secret-storage).
 *
 * @example
 * ### Encrypting Private Keys
 *
 * Private keys can be encrypted into a JSON keystore using {@link ox#Keystore.(encrypt:function)}:
 *
 * ```ts twoslash
 * import { Keystore, Secp256k1 } from 'ox'
 *
 * // Generate a random private key.
 * const privateKey = Secp256k1.randomPrivateKey()
 *
 * // Derive a key from a password.
 * const [key, opts] = Keystore.pbkdf2({ password: 'testpassword' })
 *
 * // Encrypt the private key.
 * const keystore = Keystore.encrypt(privateKey, key, opts)
 * // @log: {
 * // @log:   "crypto": {
 * // @log:     "cipher": "aes-128-ctr",
 * // @log:     "ciphertext": "...",
 * // @log:     "cipherparams": {
 * // @log:       "iv": "...",
 * // @log:     },
 * // @log:     "kdf": "pbkdf2",
 * // @log:     "kdfparams": {
 * // @log:       "salt": "...",
 * // @log:       "dklen": 32,
 * // @log:       "prf": "hmac-sha256",
 * // @log:       "c": 262144,
 * // @log:     },
 * // @log:     "mac": "...",
 * // @log:   },
 * // @log:   "id": "...",
 * // @log:   "version": 3,
 * // @log: }
 * ```
 *
 * @example
 * ### Decrypting Private Keys
 *
 * Private keys can be decrypted from a JSON keystore using {@link ox#Keystore.(decrypt:function)}:
 *
 * ```ts twoslash
 * // @noErrors
 * import { Keystore, Secp256k1 } from 'ox'
 *
 * const keystore = { crypto: { ... }, id: '...', version: 3 }
 *
 * // Derive the key.
 * const key = Keystore.toKey(keystore, { password: 'testpassword' })
 *
 * // Decrypt the private key.
 * const decrypted = Keystore.decrypt(keystore, key)
 * // @log: "0x..."
 *
 * ```
 *
 * @category Crypto
 */
export * as Keystore from './core/Keystore.js'

/**
 * Utility functions for working with KZG Commitments.
 *
 * Mainly for [EIP-4844](https://eips.ethereum.org/EIPS/eip-4844) Blobs.
 *
 * @category Blobs (EIP-4844)
 */
export * as Kzg from './core/Kzg.js'

/**
 * Utilities & types for working with Logs as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/receipt.yaml)
 *
 * :::tip
 *
 * Utilities for Log encoding & decoding can be found on the {@link ox#AbiEvent} module.
 *
 * :::
 *
 * @example
 * ### Converting from RPC Format
 *
 * Logs can be converted from their RPC format using {@link ox#Log.(fromRpc:function)}:
 *
 * ```ts twoslash
 * import 'ox/window'
 * import { AbiEvent, Hex, Log } from 'ox'
 *
 * const transfer = AbiEvent.from(
 *   'event Transfer(address indexed from, address indexed to, uint256 indexed value)',
 * )
 *
 * const { topics } = AbiEvent.encode(transfer)
 *
 * const logs = await window.ethereum!.request({
 *   method: 'eth_getLogs',
 *   params: [
 *     {
 *       address: '******************************************',
 *       fromBlock: Hex.fromNumber(19760235n),
 *       toBlock: Hex.fromNumber(19760240n),
 *       topics,
 *     },
 *   ],
 * })
 *
 * const log = Log.fromRpc(logs[0]) // [!code focus]
 * // @log: {
 * // @log:   address: '******************************************',
 * // @log:   blockHash: '0xabe69134e80a12f6a93d0aa18215b5b86c2fb338bae911790ca374a8716e01a4',
 * // @log:   blockNumber: 19760236n,
 * // @log:   data: '0x',
 * // @log:   logIndex: 271,
 * // @log:   removed: false,
 * // @log:   topics: [
 * // @log:     "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",
 * // @log:     "******************************************000000000000000000000000",
 * // @log:     "0x0000000000000000000000000c04d9e9278ec5e4d424476d3ebec70cb5d648d1",
 * // @log:     "******************************************00000000000000000000025b",
 * // @log:   transactionHash:
 * // @log:     '0xcfa52db0bc2cb5bdcb2c5bd8816df7a2f018a0e3964ab1ef4d794cf327966e93',
 * // @log:   transactionIndex: 145,
 * // @log: }
 * ```
 *
 * @category Execution Spec
 */
export * as Log from './core/Log.js'

/**
 * Utility functions for generating and working with [BIP-39](https://github.com/bitcoin/bips/blob/master/bip-0039.mediawiki) mnemonics.
 *
 * :::info
 *
 * The `Mnemonic` module is a friendly wrapper over [`@scure/bip39`](https://github.com/paulmillr/scure-bip39), an **audited** implementation of [BIP-39](https://github.com/bitcoin/bips/blob/master/bip-0039.mediawiki)
 *
 * :::
 *
 * @example
 * ### Generating a Random Mnemonic
 *
 * Random mnemonics can be generated using {@link ox#Mnemonic.(random:function)}:
 *
 * ```ts twoslash
 * import { Mnemonic } from 'ox'
 *
 * const mnemonic = Mnemonic.random(Mnemonic.english)
 * // @log: 'buyer zoo end danger ice capable shrug naive twist relief mass bonus'
 * ```
 *
 * @example
 * ### Converting to Private Key
 *
 * Mnemonics can be converted to a private key using {@link ox#Mnemonic.(toPrivateKey:function)}:
 *
 * ```ts twoslash
 * import { Mnemonic } from 'ox'
 *
 * const privateKey = Mnemonic.toPrivateKey('buyer zoo end danger ice capable shrug naive twist relief mass bonus')
 * // @log: '0x...'
 * ```
 *
 * @example
 * ### Converting to HD Key
 *
 * Mnemonics can be converted to a HD Key using {@link ox#Mnemonic.(toHdKey:function)}:
 *
 * ```ts twoslash
 * import { Mnemonic } from 'ox'
 *
 * const hdKey = Mnemonic.toHdKey('buyer zoo end danger ice capable shrug naive twist relief mass bonus')
 * ```
 *
 * @example
 * ### Converting to Seed
 *
 * Mnemonics can be converted to a master seed using {@link ox#Mnemonic.(toSeed:function)}:
 *
 * ```ts twoslash
 * import { Mnemonic } from 'ox'
 *
 * const mnemonic = 'buyer zoo end danger ice capable shrug naive twist relief mass bonus'
 * const seed = Mnemonic.toSeed(mnemonic)
 * // @log: Uint8Array [...64 bytes]
 * ```
 *
 * @category Crypto
 */
export * as Mnemonic from './core/Mnemonic.js'

/**
 * Utilities & types for working with [EIP-191 Personal Messages](https://eips.ethereum.org/EIPS/eip-191#version-0x45-e)
 *
 * @example
 * ### Computing Sign Payload
 *
 * An EIP-191 personal sign payload can be computed using {@link ox#PersonalMessage.(getSignPayload:function)}:
 *
 * ```ts twoslash
 * import { Hex, PersonalMessage, Secp256k1 } from 'ox'
 *
 * const payload = PersonalMessage.getSignPayload(Hex.fromString('hello world')) // [!code focus]
 *
 * const signature = Secp256k1.sign({ payload, privateKey: '0x...' })
 * ```
 *
 * @category Signed & Typed Data
 */
export * as PersonalMessage from './core/PersonalMessage.js'

/**
 * Utilities & types for working with [EIP-1193 Providers](https://eips.ethereum.org/EIPS/eip-1193)
 *
 * @example
 * ### Instantiating External Providers
 *
 * External EIP-1193 Providers can be instantiated with {@link ox#Provider.(from:function)}:
 *
 * ```ts twoslash
 * import 'ox/window'
 * import { Provider } from 'ox'
 *
 * const provider = Provider.from(window.ethereum)
 *
 * const blockNumber = await provider.request({ method: 'eth_blockNumber' })
 * ```
 *
 * :::tip
 *
 * There are also libraries that distribute EIP-1193 Provider objects that you can use with `Provider.from`:
 *
 * - [`@walletconnect/ethereum-provider`](https://www.npmjs.com/package/\@walletconnect/ethereum-provider)
 *
 * - [`@coinbase/wallet-sdk`](https://www.npmjs.com/package/\@coinbase/wallet-sdk)
 *
 * - [`@metamask/detect-provider`](https://www.npmjs.com/package/\@metamask/detect-provider)
 *
 * - [`@safe-global/safe-apps-provider`](https://github.com/safe-global/safe-apps-sdk/tree/main/packages/safe-apps-provider)
 *
 * - [`mipd`](https://github.com/wevm/mipd): EIP-6963 Multi Injected Providers
 *
 * :::
 *
 * @example
 * ### Instantiating with an RPC Transport
 *
 * Ox's {@link ox#RpcTransport} is also EIP-1193 compliant, and can be used to instantiate an EIP-1193 Provider. This means you can use any HTTP RPC endpoint as an EIP-1193 Provider.
 *
 * ```ts twoslash
 * import { Provider, RpcTransport } from 'ox'
 *
 * const transport = RpcTransport.fromHttp('https://1.rpc.thirdweb.com')
 * const provider = Provider.from(transport)
 * ```
 *
 * @example
 * ### Instantiating a Provider with Events
 *
 * Event emitters for EIP-1193 Providers can be created using {@link ox#Provider.(createEmitter:function)}:
 *
 * Useful for Wallets that distribute an EIP-1193 Provider (e.g. webpage injection via `window.ethereum`).
 *
 * ```ts twoslash
 * // @noErrors
 * import { Provider, RpcRequest, RpcResponse } from 'ox'
 *
 * // 1. Instantiate a Provider Emitter.
 * const emitter = Provider.createEmitter() // [!code ++]
 *
 * const store = RpcRequest.createStore()
 *
 * const provider = Provider.from({
 *   // 2. Pass the Emitter to the Provider.
 *   ...emitter, // [!code ++]
 *   async request(args) {
 *     return await fetch('https://1.rpc.thirdweb.com', {
 *       body: JSON.stringify(store.prepare(args)),
 *       method: 'POST',
 *       headers: {
 *         'Content-Type': 'application/json',
 *       },
 *     })
 *       .then((res) => res.json())
 *       .then(RpcResponse.parse)
 *   },
 * })
 *
 * // 3. Emit Provider Events.
 * emitter.emit('accountsChanged', ['0x...']) // [!code ++]
 * ```
 *
 * @category Providers (EIP-1193)
 */
export * as Provider from './core/Provider.js'

/**
 * Utility functions for working with ECDSA public keys.
 *
 * @example
 * ### Serializing Public Keys
 *
 * Public Keys can be serialized to Hex or Bytes using {@link ox#PublicKey.(toHex:function)}:
 *
 * ```ts twoslash
 * import { PublicKey } from 'ox'
 *
 * const publicKey = PublicKey.from({
 *   prefix: 4,
 *   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,
 *   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,
 * })
 *
 * const serialized = PublicKey.toHex(publicKey) // [!code focus]
 * // @log: '******************************************4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5'
 * ```
 *
 * @example
 * ### Deserializing Public Keys
 *
 * Public Keys can be deserialized from Hex or Bytes using {@link ox#PublicKey.(fromHex:function)}:
 *
 * ```ts twoslash
 * import { PublicKey } from 'ox'
 *
 * const publicKey = PublicKey.fromHex('0x8318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5')
 * // @log: {
 * // @log:   prefix: 4,
 * // @log:   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,
 * // @log:   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,
 * // @log: }
 * ```
 *
 * @category Crypto
 */
export * as PublicKey from './core/PublicKey.js'

export type { Register } from './core/internal/register.js'

/**
 * Utility functions for encoding and decoding [Recursive Length Prefix](https://ethereum.org/en/developers/docs/data-structures-and-encoding/rlp/) structures.
 *
 * @example
 * ```ts twoslash
 * import { Hex, Rlp } from 'ox'
 *
 * const data = Rlp.fromHex([Hex.fromString('hello'), Hex.fromString('world')])
 * // @log: '0xcc8568656c6c6f85776f726c64'
 *
 * const values = Rlp.toHex(data)
 * // @log: [Hex.fromString('hello'), Hex.fromString('world')]
 * ```
 *
 * @category Data
 */
export * as Rlp from './core/Rlp.js'

/**
 * Utility types for working with Ethereum JSON-RPC namespaces & schemas.
 *
 * @category JSON-RPC
 */
export * as RpcSchema from './core/RpcSchema.js'

/**
 * Utility types & functions for working with [JSON-RPC 2.0 Requests](https://www.jsonrpc.org/specification#request_object) and Ethereum JSON-RPC methods as
 * defined on the [Ethereum API specification](https://github.com/ethereum/execution-apis)
 *
 * @example
 * ### Instantiating a Request Store
 *
 * A Request Store can be instantiated using {@link ox#RpcRequest.(createStore:function)}:
 *
 * ```ts twoslash
 * import { RpcRequest } from 'ox'
 *
 * const store = RpcRequest.createStore()
 *
 * const request_1 = store.prepare({
 *   method: 'eth_blockNumber',
 * })
 * // @log: { id: 0, jsonrpc: '2.0', method: 'eth_blockNumber' }
 *
 * const request_2 = store.prepare({
 *   method: 'eth_call',
 *   params: [
 *     {
 *       to: '******************************************',
 *       data: '0xdeadbeef',
 *     },
 *   ],
 * })
 * // @log: { id: 1, jsonrpc: '2.0', method: 'eth_call', params: [{ to: '******************************************', data: '0xdeadbeef' }] }
 * ```
 *
 * @category JSON-RPC
 */
export * as RpcRequest from './core/RpcRequest.js'

/**
 * Utility types & functions for working with [JSON-RPC 2.0 Responses](https://www.jsonrpc.org/specification#response_object)
 *
 * @example
 * ### Instantiating an RPC Response
 *
 * RPC Responses can be instantiated using {@link ox#RpcResponse.(from:function)}:
 *
 * ```ts twoslash
 * import { RpcResponse } from 'ox'
 *
 * const response = RpcResponse.from({
 *   id: 0,
 *   jsonrpc: '2.0',
 *   result: '0x69420',
 * })
 * ```
 *
 * :::note
 *
 * Type-safe instantiation from a `request` object is also supported. If a `request` is provided, then the `id` and `jsonrpc` properties will be overridden with the values from the request.
 *
 * ```ts twoslash
 * import { RpcRequest, RpcResponse } from 'ox'
 *
 * const request = RpcRequest.from({ id: 0, method: 'eth_blockNumber' })
 *
 * const response = RpcResponse.from(
 *   { result: '0x69420' },
 *   { request },
 * )
 * ```
 *
 * :::
 *
 * @example
 * ### Parsing an RPC Response
 *
 * RPC Responses can be parsed using {@link ox#RpcResponse.(parse:function)}:
 *
 * ```ts twoslash
 * import { RpcRequest, RpcResponse } from 'ox'
 *
 * // 1. Create a request store.
 * const store = RpcRequest.createStore()
 *
 * // 2. Get a request object.
 * const request = store.prepare({
 *   method: 'eth_getBlockByNumber',
 *   params: ['0x1', false],
 * })
 *
 * // 3. Send the JSON-RPC request via HTTP.
 * const block = await fetch('https://1.rpc.thirdweb.com', {
 *   body: JSON.stringify(request),
 *   headers: {
 *     'Content-Type': 'application/json',
 *   },
 *   method: 'POST',
 * })
 *  .then((response) => response.json())
 *  // 4. Parse the JSON-RPC response into a type-safe result. // [!code focus]
 *  .then((response) => RpcResponse.parse(response, { request })) // [!code focus]
 *
 * block // [!code focus]
 * // ^?
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 * ```
 *
 * @category JSON-RPC
 */
export * as RpcResponse from './core/RpcResponse.js'

/**
 * Utility functions for working with JSON-RPC Transports.
 *
 * :::note
 * This is a convenience module distributed for experimenting with network connectivity on Ox.
 *
 * Consider using networking functionality from a higher-level library such as [Viem's Transports](https://viem.sh/docs/clients/transports/http)
 * if you need more features such as: retry logic, WebSockets/IPC, middleware, batch JSON-RPC, etc.
 * :::
 *
 * @example
 * ### HTTP Instantiation
 *
 * ```ts twoslash
 * import { RpcTransport } from 'ox'
 *
 * const transport = RpcTransport.fromHttp('https://1.rpc.thirdweb.com')
 *
 * const blockNumber = await transport.request({ method: 'eth_blockNumber' })
 * // @log: '0x1a2b3c'
 * ```
 *
 * @category JSON-RPC
 */
export * as RpcTransport from './core/RpcTransport.js'

/**
 * Utility functions for [secp256k1](https://www.secg.org/sec2-v2.pdf) ECDSA cryptography.
 *
 * :::info
 *
 * The `Secp256k1` module is a friendly wrapper over [`@noble/curves/secp256k1`](https://github.com/paulmillr/noble-curves), an **audited** implementation of [secp256k1](https://www.secg.org/sec2-v2.pdf)
 *
 * :::
 *
 * @example
 * ### Computing a Random Private Key
 *
 * A random private key can be computed using {@link ox#Secp256k1.(randomPrivateKey:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1 } from 'ox'
 *
 * const privateKey = Secp256k1.randomPrivateKey()
 * // @log: '0x...'
 * ```
 *
 * @example
 * ### Getting a Public Key
 *
 * A public key can be derived from a private key using {@link ox#Secp256k1.(getPublicKey:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1 } from 'ox'
 *
 * const privateKey = Secp256k1.randomPrivateKey()
 *
 * const publicKey = Secp256k1.getPublicKey({ privateKey }) // [!code focus]
 * // @log: { x: 3251...5152n, y: 1251...5152n }
 * ```
 *
 * @example
 * ### Signing a Payload
 *
 * A payload can be signed using {@link ox#Secp256k1.(sign:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1 } from 'ox'
 *
 * const privateKey = Secp256k1.randomPrivateKey()
 *
 * const signature = Secp256k1.sign({ payload: '0xdeadbeef', privateKey }) // [!code focus]
 * // @log: { r: 1251...5152n, s: 1251...5152n, yParity: 1 }
 * ```
 *
 * @example
 * ### Verifying a Signature
 *
 * A signature can be verified using {@link ox#Secp256k1.(verify:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1 } from 'ox'
 *
 * const privateKey = Secp256k1.randomPrivateKey()
 * const publicKey = Secp256k1.getPublicKey({ privateKey })
 * const signature = Secp256k1.sign({ payload: '0xdeadbeef', privateKey })
 *
 * const isValid = Secp256k1.verify({ // [!code focus]
 *   payload: '0xdeadbeef', // [!code focus]
 *   publicKey, // [!code focus]
 *   signature, // [!code focus]
 * }) // [!code focus]
 * // @log: true
 * ```
 *
 * @category Crypto
 */
export * as Secp256k1 from './core/Secp256k1.js'

/**
 * Utility functions for [NIST P256](https://csrc.nist.gov/csrc/media/events/workshop-on-elliptic-curve-cryptography-standards/documents/papers/session6-adalier-mehmet.pdf) ECDSA cryptography.
 *
 * :::info
 *
 * The `P256` module is a friendly wrapper over [`@noble/curves/p256`](https://github.com/paulmillr/noble-curves), an **audited** implementation of [P256](https://www.secg.org/sec2-v2.pdf)
 *
 * :::
 *
 * @example
 * ### Computing a Random Private Key
 *
 * A random private key can be computed using {@link ox#P256.(randomPrivateKey:function)}:
 *
 * ```ts twoslash
 * import { P256 } from 'ox'
 *
 * const privateKey = P256.randomPrivateKey()
 * // @log: '0x...'
 * ```
 *
 * @example
 * ### Getting a Public Key
 *
 * A public key can be derived from a private key using {@link ox#P256.(getPublicKey:function)}:
 *
 * ```ts twoslash
 * import { P256 } from 'ox'
 *
 * const privateKey = P256.randomPrivateKey()
 *
 * const publicKey = P256.getPublicKey({ privateKey }) // [!code focus]
 * // @log: { x: 3251...5152n, y: 1251...5152n }
 * ```
 *
 * @example
 * ### Signing a Payload
 *
 * A payload can be signed using {@link ox#P256.(sign:function)}:
 *
 * ```ts twoslash
 * import { P256 } from 'ox'
 *
 * const privateKey = P256.randomPrivateKey()
 *
 * const signature = P256.sign({ payload: '0xdeadbeef', privateKey }) // [!code focus]
 * // @log: { r: 1251...5152n, s: 1251...5152n, yParity: 1 }
 * ```
 *
 * @example
 * ### Verifying a Signature
 *
 * A signature can be verified using {@link ox#P256.(verify:function)}:
 *
 * ```ts twoslash
 * import { P256 } from 'ox'
 *
 * const privateKey = P256.randomPrivateKey()
 * const publicKey = P256.getPublicKey({ privateKey })
 * const signature = P256.sign({ payload: '0xdeadbeef', privateKey })
 *
 * const isValid = P256.verify({ // [!code focus]
 *   payload: '0xdeadbeef', // [!code focus]
 *   publicKey, // [!code focus]
 *   signature, // [!code focus]
 * }) // [!code focus]
 * // @log: true
 * ```
 *
 * @category Crypto
 */
export * as P256 from './core/P256.js'

/**
 * Utility functions for working with ECDSA signatures.
 *
 * @example
 * ### Serializing a Signature
 *
 * Signatures can be serialized to Hex or Bytes using {@link ox#Signature.(toHex:function)}:
 *
 * ```ts twoslash
 * import { Signature } from 'ox'
 *
 * const signature = Signature.toHex({
 *   r: 49782753348462494199823712700004552394425719014458918871452329774910450607807n,
 *   s: 33726695977844476214676913201140481102225469284307016937915595756355928419768n,
 *   yParity: 1
 * })
 * // @log: '0x6e100a352ec6ad1b70802290e18aeed190704973570f3b8ed42cb9808e2ea6bf4a90a229a244495b41890987806fcbd2d5d23fc0dbe5f5256c2613c039d76db81c'
 * ```
 *
 * @example
 * ### Deserializing a Signature
 *
 * Signatures can be deserialized from Hex or Bytes using {@link ox#Signature.(fromHex:function)}:
 *
 * ```ts twoslash
 * import { Signature } from 'ox'
 *
 * Signature.fromHex('0x6e100a352ec6ad1b70802290e18aeed190704973570f3b8ed42cb9808e2ea6bf4a90a229a244495b41890987806fcbd2d5d23fc0dbe5f5256c2613c039d76db81c')
 * // @log: { r: 5231...n, s: 3522...n, yParity: 0 }
 * ```
 *
 * @category Crypto
 */
export * as Signature from './core/Signature.js'

/**
 * Utility functions for working with
 * [EIP-4361: Sign-In with Ethereum](https://eips.ethereum.org/EIPS/eip-4361)
 *
 * @example
 * ### Creating a SIWE Message
 *
 * SIWE messages can be created using {@link ox#Siwe.(createMessage:function)}:
 *
 * ```ts twoslash
 * import { Siwe } from 'ox'
 *
 * Siwe.createMessage({
 *   address: '******************************************',
 *   chainId: 1,
 *   domain: 'example.com',
 *   nonce: 'foobarbaz',
 *   uri: 'https://example.com/path',
 *   version: '1',
 * })
 * // @log: "example.com wants you to sign in with your Ethereum account:
 * // @log: ******************************************
 * // @log:
 * // @log:
 * // @log: URI: https://example.com/path
 * // @log: Version: 1
 * // @log: Chain ID: 1
 * // @log: Nonce: foobarbaz
 * // @log: Issued At: 2023-02-01T00:00:00.000Z"
 * ```
 *
 * @example
 * ### Generating SIWE Nonces
 *
 * SIWE nonces can be generated using {@link ox#Siwe.(generateNonce:function)}:
 *
 * ```ts twoslash
 * import { Siwe } from 'ox'
 *
 * Siwe.generateNonce()
 * // @log: '65ed4681d4efe0270b923ff5f4b097b1c95974dc33aeebecd5724c42fd86dfd25dc70b27ef836b2aa22e68f19ebcccc1'
 * ```
 *
 * @example
 * ### Parsing a SIWE Message
 *
 * SIWE messages can be parsed using {@link ox#Siwe.(parseMessage:function)}:
 *
 * ```ts twoslash
 * import { Siwe } from 'ox'
 *
 * Siwe.parseMessage(`example.com wants you to sign in with your Ethereum account:
 * ******************************************
 *
 * I accept the ExampleOrg Terms of Service: https://example.com/tos
 *
 * URI: https://example.com/path
 * Version: 1
 * Chain ID: 1
 * Nonce: foobarbaz
 * Issued At: 2023-02-01T00:00:00.000Z`)
 * // @log: {
 * // @log:   address: '******************************************',
 * // @log:   chainId: 1,
 * // @log:   domain: 'example.com',
 * // @log:   issuedAt: '2023-02-01T00:00:00.000Z',
 * // @log:   nonce: 'foobarbaz',
 * // @log:   statement: 'I accept the ExampleOrg Terms of Service: https://example.com/tos',
 * // @log:   uri: 'https://example.com/path',
 * // @log:   version: '1',
 * // @log: }
 * ```
 *
 * @example
 * ### Validating a SIWE Message
 *
 * SIWE messages can be validated using {@link ox#Siwe.(validateMessage:function)}:
 *
 * ```ts twoslash
 * import { Siwe } from 'ox'
 *
 * Siwe.validateMessage({
 *   address: '******************************************',
 *   domain: 'example.com',
 *   message: {
 *     address: '******************************************',
 *     chainId: 1,
 *     domain: 'example.com',
 *     nonce: 'foobarbaz',
 *     uri: 'https://example.com/path',
 *     version: '1',
 *   },
 *   nonce: 'foobarbaz',
 * })
 * // @log: true
 * ```
 *
 * @category Sign-In with Ethereum (EIP-4361)
 */
export * as Siwe from './core/Siwe.js'

export * as Solidity from './core/Solidity.js'

/**
 * Utilities & types for working with **State Overrides**.
 *
 * @category Execution Spec
 */
export * as StateOverrides from './core/StateOverrides.js'

/**
 * Utilities & types for working with **Transactions** as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/transaction.yaml)
 *
 * @example
 * ### Converting from RPC Format
 *
 * Transactions can be converted from RPC format using {@link ox#Transaction.(fromRpc:function)}:
 *
 * ```ts twoslash
 * import { Transaction } from 'ox'
 *
 * const transaction = Transaction.fromRpc({
 *   hash: '0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0',
 *   nonce: '0x357',
 *   blockHash:
 *     '0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b',
 *   blockNumber: '0x12f296f',
 *   transactionIndex: '0x2',
 *   from: '******************************************',
 *   to: '******************************************',
 *   value: '0x9b6e64a8ec60000',
 *   gas: '0x43f5d',
 *   maxFeePerGas: '0x2ca6ae494',
 *   maxPriorityFeePerGas: '0x41cc3c0',
 *   input:
 *     '0x3593564c000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000006643504700000000000000000000000000000000000000000000000000000000000000040b080604000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002800000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000009b6e64a8ec600000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000009b6e64a8ec60000000000000000000000000000000000000000000000000000019124bb5ae978c000000000000000000000000000000000000000000000000000000000000000a000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b80000000000000000000000000000000000000000000000000000000000000060000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b8000000000000000000000000000000fee13a103a10d593b9ae06b3e05f2e7e1c00000000000000000000000000000000000000000000000000000000000000190000000000000000000000000000000000000000000000000000000000000060000000000000000000000000c56c7a0eaa804f854b536a5f3d5f49d2ec4b12b800000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000190240001b9872b',
 *   r: '0x635dc2033e60185bb36709c29c75d64ea51dfbd91c32ef4be198e4ceb169fb4d',
 *   s: '0x50c2667ac4c771072746acfdcf1f1483336dcca8bd2df47cd83175dbe60f0540',
 *   yParity: '0x0',
 *   chainId: '0x1',
 *   accessList: [],
 *   type: '0x2',
 * })
 * ```
 *
 * @category Execution Spec
 */
export * as Transaction from './core/Transaction.js'

/**
 * Errors & Types for working with Transaction Envelopes.
 *
 * :::note
 * Refer to the following modules for specific Transaction Envelope types:
 * - [`TransactionEnvelopeLegacy`](/api/TransactionEnvelopeLegacy)
 * - [`TransactionEnvelopeEip1559`](/api/TransactionEnvelopeEip1559)
 * - [`TransactionEnvelopeEip2930`](/api/TransactionEnvelopeEip2930)
 * - [`TransactionEnvelopeEip4844`](/api/TransactionEnvelopeEip4844)
 * - [`TransactionEnvelopeEip7702`](/api/TransactionEnvelopeEip7702)
 * :::
 *
 * @category Transaction Envelopes
 */
export * as TransactionEnvelope from './core/TransactionEnvelope.js'

/**
 * Utility functions for working
 * with **Legacy Transaction Envelopes**.
 *
 * @example
 * ### Instantiating
 *
 * Transaction Envelopes can be instantiated using {@link ox#TransactionEnvelopeLegacy.(from:function)}:
 *
 * ```ts twoslash
 * import { TransactionEnvelopeLegacy, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeLegacy.from({
 *   gasPrice: Value.fromGwei('10'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 * ```
 *
 * * @example
 * ### Signing
 *
 * Transaction Envelopes can be signed using {@link ox#TransactionEnvelopeLegacy.(getSignPayload:function)} and a signing function such as {@link ox#Secp256k1.(sign:function)} or {@link ox#P256.(sign:function)}:
 *
 * ```ts twoslash
 * // @noErrors
 * import { Secp256k1, TransactionEnvelopeLegacy } from 'ox'
 *
 * const envelope = TransactionEnvelopeLegacy.from({
 *   nonce: 0n,
 *   gasPrice: 1000000000n,
 *   gas: 21000n,
 *   to: '******************************************',
 *   value: 1000000000000000000n,
 * })
 *
 * const signature = Secp256k1.sign({ // [!code focus]
 *   payload: TransactionEnvelopeLegacy.getSignPayload(envelope), // [!code focus]
 *   privateKey: '0x...' // [!code focus]
 * }) // [!code focus]
 *
 * const envelope_signed = TransactionEnvelopeLegacy.from(envelope, { signature })
 * ```
 *
 * @example
 * ### Serializing
 *
 * Transaction Envelopes can be serialized using {@link ox#TransactionEnvelopeLegacy.(serialize:function)}:
 *
 * ```ts twoslash
 * import { TransactionEnvelopeLegacy, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeLegacy.from({
 *   chainId: 1,
 *   gasPrice: Value.fromGwei('10'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 *
 * const serialized = TransactionEnvelopeLegacy.serialize(envelope) // [!code focus]
 * ```
 *
 * @example
 * ### Sending
 *
 * We can send a Transaction Envelope to the network by serializing the signed envelope with `.serialize`, and then broadcasting it over JSON-RPC with `eth_sendRawTransaction`.
 *
 * In this example, we will use {@link ox#RpcTransport.(fromHttp:function)} to broadcast a `eth_sendRawTransaction` request over HTTP JSON-RPC.
 *
 * ```ts twoslash
 * import { RpcTransport, TransactionEnvelopeLegacy, Secp256k1, Value } from 'ox'
 *
 * // Construct the Envelope.
 * const envelope = TransactionEnvelopeLegacy.from({
 *   chainId: 1,
 *   gasPrice: Value.fromGwei('10'),
 *   nonce: 69n,
 *   to: '******************************************',
 *   value: Value.fromEther('1.5'),
 * })
 *
 * // Sign over the Envelope.
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeLegacy.getSignPayload(envelope),
 *   privateKey: '0x...',
 * })
 *
 * // Serialize the Envelope with the Signature. // [!code focus]
 * const serialized = TransactionEnvelopeLegacy.serialize(envelope, { // [!code focus]
 *   signature  // [!code focus]
 * }) // [!code focus]
 *
 * // Broadcast the Envelope with `eth_sendRawTransaction`. // [!code focus]
 * const transport = RpcTransport.fromHttp('https://1.rpc.thirdweb.com') // [!code focus]
 * const hash = await transport.request({ // [!code focus]
 *   method: 'eth_sendRawTransaction', // [!code focus]
 *   params: [serialized], // [!code focus]
 * }) // [!code focus]
 * ```
 *
 * If you are interfacing with an RPC that supports `eth_sendTransaction`, you can also use
 * {@link ox#TransactionEnvelopeLegacy.(toRpc:function)} to convert an Envelope to an RPC-compatible format.
 * This means you can skip the ceremony of manually filling & signing the Transaction.
 *
 * ```ts twoslash
 * import 'ox/window'
 * import { Provider, TransactionEnvelopeLegacy, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeLegacy.from({
 *   chainId: 1,
 *   to: '******************************************',
 *   value: Value.fromEther('1.5'),
 * })
 *
 * const envelope_rpc = TransactionEnvelopeLegacy.toRpc(envelope)
 *
 * const provider = Provider.from(window.ethereum)
 * const hash = await provider.request({
 *   method: 'eth_sendTransaction',
 *   params: [envelope_rpc],
 * })
 * ```
 *
 * @example
 * ### Computing Hashes
 *
 * Transaction Hashes can be computed using {@link ox#TransactionEnvelopeLegacy.(hash:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1, TransactionEnvelopeLegacy } from 'ox'
 *
 * const envelope = TransactionEnvelopeLegacy.from({
 *   chainId: 1,
 *   nonce: 0n,
 *   gasPrice: 1000000000n,
 *   gas: 21000n,
 *   to: '******************************************',
 *   value: 1000000000000000000n,
 *   data: '0x',
 * })
 *
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeLegacy.getSignPayload(envelope),
 *   privateKey: '0x...'
 * })
 *
 * const envelope_signed = TransactionEnvelopeLegacy.from(envelope, { signature })
 *
 * const hash = TransactionEnvelopeLegacy.hash(envelope_signed) // [!code focus]
 * ```
 *
 * @category Transaction Envelopes
 */
export * as TransactionEnvelopeLegacy from './core/TransactionEnvelopeLegacy.js'

/**
 * Utility functions for working with [EIP-1559 Typed Transaction Envelopes](https://eips.ethereum.org/EIPS/eip-1559)
 *
 *  @example
 * ### Instantiating
 *
 * Transaction Envelopes can be instantiated using {@link ox#TransactionEnvelopeEip1559.(from:function)}:
 *
 * ```ts twoslash
 * import { TransactionEnvelopeEip1559, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip1559.from({
 *   chainId: 1,
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 * // @log: {
 * // @log:   chainId: 1,
 * // @log:   maxFeePerGas: 10000000000n,
 * // @log:   maxPriorityFeePerGas: 1000000000n,
 * // @log:   to: '******************************************',
 * // @log:   type: 'eip1559',
 * // @log:   value: 1000000000000000000n,
 * // @log: }
 * ```
 *
 * @example
 * ### Signing
 *
 * Transaction Envelopes can be signed using {@link ox#TransactionEnvelopeEip1559.(getSignPayload:function)} and a signing function such as {@link ox#Secp256k1.(sign:function)} or {@link ox#P256.(sign:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1, TransactionEnvelopeEip1559 } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip1559.from({
 *   chainId: 1,
 *   nonce: 0n,
 *   gasPrice: 1000000000n,
 *   gas: 21000n,
 *   to: '******************************************',
 *   value: 1000000000000000000n,
 * })
 *
 * const signature = Secp256k1.sign({ // [!code focus]
 *   payload: TransactionEnvelopeEip1559.getSignPayload(envelope), // [!code focus]
 *   privateKey: '0x...' // [!code focus]
 * }) // [!code focus]
 *
 * const envelope_signed = TransactionEnvelopeEip1559.from(envelope, { signature })
 * ```
 *
 * @example
 * ### Serializing
 *
 * Transaction Envelopes can be serialized using {@link ox#TransactionEnvelopeEip1559.(serialize:function)}:
 *
 * ```ts twoslash
 * import { TransactionEnvelopeEip1559, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip1559.from({
 *   chainId: 1,
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 *
 * const serialized = TransactionEnvelopeEip1559.serialize(envelope) // [!code focus]
 * ```
 *
 * @example
 * ### Sending
 *
 * We can send a Transaction Envelope to the network by serializing the signed envelope with `.serialize`, and then broadcasting it over JSON-RPC with `eth_sendRawTransaction`.
 *
 * In this example, we will use {@link ox#RpcTransport.(fromHttp:function)} to broadcast a `eth_sendRawTransaction` request over HTTP JSON-RPC.
 *
 * ```ts twoslash
 * import { RpcTransport, TransactionEnvelopeEip1559, Secp256k1, Value } from 'ox'
 *
 * // Construct the Envelope.
 * const envelope = TransactionEnvelopeEip1559.from({
 *   chainId: 1,
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   nonce: 69n,
 *   to: '******************************************',
 *   value: Value.fromEther('1.5'),
 * })
 *
 * // Sign over the Envelope.
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeEip1559.getSignPayload(envelope),
 *   privateKey: '0x...',
 * })
 *
 * // Serialize the Envelope with the Signature. // [!code focus]
 * const serialized = TransactionEnvelopeEip1559.serialize(envelope, { // [!code focus]
 *   signature  // [!code focus]
 * }) // [!code focus]
 *
 * // Broadcast the Envelope with `eth_sendRawTransaction`. // [!code focus]
 * const transport = RpcTransport.fromHttp('https://1.rpc.thirdweb.com') // [!code focus]
 * const hash = await transport.request({ // [!code focus]
 *   method: 'eth_sendRawTransaction', // [!code focus]
 *   params: [serialized], // [!code focus]
 * }) // [!code focus]
 * ```
 *
 * If you are interfacing with an RPC that supports `eth_sendTransaction`, you can also use
 * {@link ox#TransactionEnvelopeEip1559.(toRpc:function)} to convert an Envelope to an RPC-compatible format.
 * This means you can skip the ceremony of manually filling & signing the Transaction.
 *
 * ```ts twoslash
 * import 'ox/window'
 * import { Provider, TransactionEnvelopeEip1559, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip1559.from({
 *   chainId: 1,
 *   to: '******************************************',
 *   value: Value.fromEther('1.5'),
 * })
 *
 * const envelope_rpc = TransactionEnvelopeEip1559.toRpc(envelope)
 *
 * const provider = Provider.from(window.ethereum)
 * const hash = await provider.request({
 *   method: 'eth_sendTransaction',
 *   params: [envelope_rpc],
 * })
 * ```
 *
 * @example
 * ### Computing Hashes
 *
 * Transaction Hashes can be computed using {@link ox#TransactionEnvelopeEip1559.(hash:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1, TransactionEnvelopeEip1559, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip1559.from({
 *   chainId: 1,
 *   nonce: 0n,
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   gas: 21000n,
 *   to: '******************************************',
 *   value: 1000000000000000000n,
 *   data: '0x',
 * })
 *
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeEip1559.getSignPayload(envelope),
 *   privateKey: '0x...'
 * })
 *
 * const envelope_signed = TransactionEnvelopeEip1559.from(envelope, { signature })
 *
 * const hash = TransactionEnvelopeEip1559.hash(envelope_signed) // [!code focus]
 * ```
 *
 * @category Transaction Envelopes
 */
export * as TransactionEnvelopeEip1559 from './core/TransactionEnvelopeEip1559.js'

/**
 * Utility functions for working with [EIP-2930 Typed Transaction Envelopes](https://eips.ethereum.org/EIPS/eip-2930)
 *
 * @example
 * ### Instantiating
 *
 * Transaction Envelopes can be instantiated using {@link ox#TransactionEnvelopeEip2930.(from:function)}:
 *
 * ```ts twoslash
 * // @noErrors
 * import { TransactionEnvelopeEip2930, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip2930.from({
 *   chainId: 1,
 *   accessList: [...],
 *   gasPrice: Value.fromGwei('10'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 * ```
 *
 * @example
 * ### Signing
 *
 * Transaction Envelopes can be signed using {@link ox#TransactionEnvelopeEip2930.(getSignPayload:function)} and a signing function such as {@link ox#Secp256k1.(sign:function)} or {@link ox#P256.(sign:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1, TransactionEnvelopeEip2930 } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip2930.from({
 *   chainId: 1,
 *   nonce: 0n,
 *   gasPrice: 1000000000n,
 *   gas: 21000n,
 *   to: '******************************************',
 *   value: 1000000000000000000n,
 * })
 *
 * const payload = TransactionEnvelopeEip2930.getSignPayload(envelope) // [!code focus]
 * // @log: '0x...'
 *
 * const signature = Secp256k1.sign({ payload, privateKey: '0x...' })
 * ```
 *
 * @example
 * ### Serializing
 *
 * Transaction Envelopes can be serialized using {@link ox#TransactionEnvelopeEip2930.(serialize:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1, TransactionEnvelopeEip2930, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip2930.from({
 *   chainId: 1,
 *   nonce: 0n,
 *   gasPrice: 1000000000n,
 *   gas: 21000n,
 *   to: '******************************************',
 *   value: 1000000000000000000n,
 * })
 *
 * const serialized = TransactionEnvelopeEip2930.serialize(envelope) // [!code focus]
 * ```
 *
 * @example
 * ### Sending
 *
 * We can send a Transaction Envelope to the network by serializing the signed envelope with `.serialize`, and then broadcasting it over JSON-RPC with `eth_sendRawTransaction`.
 *
 * In this example, we will use {@link ox#RpcTransport.(fromHttp:function)} to broadcast a `eth_sendRawTransaction` request over HTTP JSON-RPC.
 *
 * ```ts twoslash
 * import { RpcTransport, TransactionEnvelopeEip2930, Secp256k1, Value } from 'ox'
 *
 * // Construct the Envelope.
 * const envelope = TransactionEnvelopeEip2930.from({
 *   accessList: [],
 *   chainId: 1,
 *   gasPrice: Value.fromGwei('10'),
 *   nonce: 69n,
 *   to: '******************************************',
 *   value: Value.fromEther('1.5'),
 * })
 *
 * // Sign over the Envelope.
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeEip2930.getSignPayload(envelope),
 *   privateKey: '0x...',
 * })
 *
 * // Serialize the Envelope with the Signature. // [!code focus]
 * const serialized = TransactionEnvelopeEip2930.serialize(envelope, { // [!code focus]
 *   signature  // [!code focus]
 * }) // [!code focus]
 *
 * // Broadcast the Envelope with `eth_sendRawTransaction`. // [!code focus]
 * const transport = RpcTransport.fromHttp('https://1.rpc.thirdweb.com') // [!code focus]
 * const hash = await transport.request({ // [!code focus]
 *   method: 'eth_sendRawTransaction', // [!code focus]
 *   params: [serialized], // [!code focus]
 * }) // [!code focus]
 * ```
 *
 * If you are interfacing with an RPC that supports `eth_sendTransaction`, you can also use
 * {@link ox#TransactionEnvelopeEip2930.(toRpc:function)} to convert an Envelope to an RPC-compatible format.
 * This means you can skip the ceremony of manually filling & signing the Transaction.
 *
 * ```ts twoslash
 * import 'ox/window'
 * import { Provider, TransactionEnvelopeEip2930, Value } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip2930.from({
 *   accessList: [],
 *   chainId: 1,
 *   to: '******************************************',
 *   value: Value.fromEther('1.5'),
 * })
 *
 * const envelope_rpc = TransactionEnvelopeEip2930.toRpc(envelope)
 *
 * const provider = Provider.from(window.ethereum)
 * const hash = await provider.request({
 *   method: 'eth_sendTransaction',
 *   params: [envelope_rpc],
 * })
 * ```
 *
 * @example
 * ### Computing Hashes
 *
 * Transaction Hashes can be computed using {@link ox#TransactionEnvelopeEip2930.(hash:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1, TransactionEnvelopeEip2930 } from 'ox'
 *
 * const envelope = TransactionEnvelopeEip2930.from({
 *   chainId: 1,
 *   nonce: 0n,
 *   gasPrice: 1000000000n,
 *   gas: 21000n,
 *   to: '******************************************',
 *   value: 1000000000000000000n,
 *   data: '0x',
 * })
 *
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeEip2930.getSignPayload(envelope),
 *   privateKey: '0x...'
 * })
 *
 * const envelope_signed = TransactionEnvelopeEip2930.from(envelope, { signature })
 *
 * const hash = TransactionEnvelopeEip2930.hash(envelope_signed) // [!code focus]
 * ```
 *
 * @category Transaction Envelopes
 */
export * as TransactionEnvelopeEip2930 from './core/TransactionEnvelopeEip2930.js'

/**
 * Utility functions for working with [EIP-4844 Typed Transaction Envelopes](https://eips.ethereum.org/EIPS/eip-4844)
 *
 * @example
 * ### Instantiating Blobs
 *
 * Blobs can be instantiated using {@link ox#Blobs.(from:function)}:
 *
 * ```ts twoslash
 * import { Blobs, Hex } from 'ox'
 *
 * const blobs = Blobs.from(Hex.fromString('Hello World!'))
 * ```
 *
 * @example
 * ### Instantiating
 *
 * Transaction Envelopes can be instantiated using {@link ox#TransactionEnvelopeEip4844.(from:function)}:
 *
 * ```ts twoslash
 * // @noErrors
 * import { Blobs, Hex, TransactionEnvelopeEip4844, Value } from 'ox'
 * import { kzg } from './kzg'
 *
 * const blobs = Blobs.from(Hex.fromString('Hello World!'))
 * const blobVersionedHashes = Blobs.toVersionedHashes(blobs, { kzg })
 *
 * const envelope = TransactionEnvelopeEip4844.from({
 *   chainId: 1,
 *   blobVersionedHashes,
 *   maxFeePerBlobGas: Value.fromGwei('3'),
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 * ```
 *
 * @example
 * ### Signing
 *
 * Transaction Envelopes can be signed using {@link ox#TransactionEnvelopeEip4844.(getSignPayload:function)} and a signing function such as {@link ox#Secp256k1.(sign:function)} or {@link ox#P256.(sign:function)}:
 *
 * ```ts twoslash
 * // @noErrors
 * import { Blobs, Secp256k1, TransactionEnvelopeEip4844 } from 'ox'
 * import { kzg } from './kzg'
 *
 * const blobs = Blobs.from('0xdeadbeef')
 * const blobVersionedHashes = Blobs.toVersionedHashes(blobs, { kzg })
 * const sidecars = Blobs.toSidecars(blobs, { kzg })
 *
 * const envelope = TransactionEnvelopeEip4844.from({
 *   blobVersionedHashes,
 *   chainId: 1,
 *   nonce: 0n,
 *   maxFeePerBlobGas: Value.fromGwei('3'),
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 *
 * const signature = Secp256k1.sign({ // [!code focus]
 *   payload: TransactionEnvelopeEip4844.getSignPayload(envelope), // [!code focus]
 *   privateKey: '0x...' // [!code focus]
 * }) // [!code focus]
 *
 * const envelope_signed = TransactionEnvelopeEip4844.from(envelope, {
 *   sidecars,
 *   signature
 * })
 * ```
 *
 * @example
 * ### Serializing
 *
 * Transaction Envelopes can be serialized using {@link ox#TransactionEnvelopeEip4844.(serialize:function)}:
 *
 * ```ts twoslash
 * // @noErrors
 * import { Blobs, TransactionEnvelopeEip4844 } from 'ox'
 * import { kzg } from './kzg'
 *
 * const blobs = Blobs.from('0xdeadbeef')
 * const blobVersionedHashes = Blobs.toVersionedHashes(blobs, { kzg })
 *
 * const envelope = TransactionEnvelopeEip4844.from({
 *   blobVersionedHashes,
 *   chainId: 1,
 *   maxFeePerBlobGas: Value.fromGwei('3'),
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 *
 * const serialized = TransactionEnvelopeEip4844.serialize(envelope) // [!code focus]
 * ```
 *
 * @example
 * ### Sending
 *
 * We can send a Transaction Envelope to the network by serializing the signed envelope with `.serialize`, and then broadcasting it over JSON-RPC with `eth_sendRawTransaction`.
 *
 * In this example, we will use {@link ox#RpcTransport.(fromHttp:function)} to broadcast a `eth_sendRawTransaction` request over HTTP JSON-RPC.
 *
 * ```ts twoslash
 * // @noErrors
 * import { Blobs, RpcTransport, TransactionEnvelopeEip4844, Secp256k1, Value } from 'ox'
 * import { kzg } from './kzg'
 *
 * // Compute the Blob Versioned Hashes.
 * const blobs = Blobs.from('0xdeadbeef')
 * const blobVersionedHashes = Blobs.toVersionedHashes(blobs, { kzg })
 * const sidecars = Blobs.toSidecars(blobs, { kzg })
 *
 * // Construct the Envelope.
 * const envelope = TransactionEnvelopeEip4844.from({
 *   chainId: 1,
 *   blobVersionedHashes,
 *   maxFeePerBlobGas: Value.fromGwei('3'),
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   nonce: 0n,
 *   to: '******************************************',
 *   value: Value.fromEther('1.5'),
 * })
 *
 * // Sign over the Envelope.
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeEip4844.getSignPayload(envelope),
 *   privateKey: '0x...',
 * })
 *
 * // Serialize the Envelope with the Signature. // [!code focus]
 * const serialized = TransactionEnvelopeEip4844.serialize(envelope, { // [!code focus]
 *   sidecars, // [!code focus]
 *   signature  // [!code focus]
 * }) // [!code focus]
 *
 * // Broadcast the Envelope with `eth_sendRawTransaction`. // [!code focus]
 * const transport = RpcTransport.fromHttp('https://1.rpc.thirdweb.com') // [!code focus]
 * const hash = await transport.request({ // [!code focus]
 *   method: 'eth_sendRawTransaction', // [!code focus]
 *   params: [serialized], // [!code focus]
 * }) // [!code focus]
 * ```
 *
 * @example
 * ### Computing Hashes
 *
 * Transaction Hashes can be computed using {@link ox#TransactionEnvelopeEip4844.(hash:function)}:
 *
 * ```ts twoslash
 * // @noErrors
 * import { Blobs, Secp256k1, TransactionEnvelopeEip4844, Value } from 'ox'
 * import { kzg } from './kzg'
 *
 * const blobs = Blobs.from('0xdeadbeef')
 * const blobVersionedHashes = Blobs.toVersionedHashes(blobs, { kzg })
 *
 * const envelope = TransactionEnvelopeEip4844.from({
 *   blobVersionedHashes,
 *   chainId: 1,
 *   maxFeePerGas: Value.fromGwei('10'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 *
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeEip4844.getSignPayload(envelope),
 *   privateKey: '0x...'
 * })
 *
 * const envelope_signed = TransactionEnvelopeEip4844.from(envelope, { signature })
 *
 * const hash = TransactionEnvelopeEip4844.hash(envelope_signed) // [!code focus]
 * ```
 *
 * @category Transaction Envelopes
 */
export * as TransactionEnvelopeEip4844 from './core/TransactionEnvelopeEip4844.js'

/**
 * Utility functions for working with [EIP-7702 Typed Transaction Envelopes](https://eips.ethereum.org/EIPS/eip-7702)
 *
 * @example
 * ### Instantiating
 *
 * Transaction Envelopes can be instantiated using {@link ox#TransactionEnvelopeEip7702.(from:function)}:
 *
 * ```ts twoslash
 * import { Authorization, Secp256k1, TransactionEnvelopeEip7702, Value } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 0n,
 * })
 *
 * const signature = Secp256k1.sign({
 *   payload: Authorization.getSignPayload(authorization),
 *   privateKey: '0x...',
 * })
 *
 * const authorizationList = [Authorization.from(authorization, { signature })]
 *
 * const envelope = TransactionEnvelopeEip7702.from({ // [!code focus]
 *   authorizationList, // [!code focus]
 *   chainId: 1, // [!code focus]
 *   maxFeePerGas: Value.fromGwei('10'), // [!code focus]
 *   maxPriorityFeePerGas: Value.fromGwei('1'), // [!code focus]
 *   to: '******************************************', // [!code focus]
 *   value: Value.fromEther('1'), // [!code focus]
 * }) // [!code focus]
 * ```
 *
 * :::tip
 *
 * See {@link ox#Authorization} for more details on instantiating and signing EIP-7702 Authorizations.
 *
 * :::
 *
 * @example
 * ### Signing
 *
 * Transaction Envelopes can be signed using {@link ox#TransactionEnvelopeEip7702.(getSignPayload:function)} and a signing function such as {@link ox#Secp256k1.(sign:function)} or {@link ox#P256.(sign:function)}:
 *
 * ```ts twoslash
 * import { Authorization, Secp256k1, TransactionEnvelopeEip7702, Value } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 0n,
 * })
 *
 * const signature_auth = Secp256k1.sign({
 *   payload: Authorization.getSignPayload(authorization),
 *   privateKey: '0x...',
 * })
 *
 * const authorizationList = [Authorization.from(authorization, { signature: signature_auth })]
 *
 * const envelope = TransactionEnvelopeEip7702.from({
 *   authorizationList,
 *   chainId: 1,
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   to: '******************************************',
 *   value: Value.fromEther('1'),
 * })
 *
 * const signature = Secp256k1.sign({ // [!code focus]
 *   payload: TransactionEnvelopeEip7702.getSignPayload(envelope), // [!code focus]
 *   privateKey: '0x...', // [!code focus]
 * })
 *
 * const envelope_signed = TransactionEnvelopeEip7702.from(envelope, { signature })
 * ```
 *
 * @example
 * ### Sending
 *
 * We can send a Transaction Envelope to the network by serializing the signed envelope with `.serialize`, and then broadcasting it over JSON-RPC with `eth_sendRawTransaction`.
 *
 * In this example, we will use {@link ox#RpcTransport.(fromHttp:function)} to broadcast a `eth_sendRawTransaction` request over HTTP JSON-RPC.
 *
 * ```ts twoslash
 * import { Authorization, RpcTransport, TransactionEnvelopeEip7702, Secp256k1, Value } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 0n,
 * })
 *
 * const signature_auth = Secp256k1.sign({
 *   payload: Authorization.getSignPayload(authorization),
 *   privateKey: '0x...',
 * })
 *
 * const authorizationList = [Authorization.from(authorization, { signature: signature_auth })]
 *
 * const envelope = TransactionEnvelopeEip7702.from({
 *   authorizationList,
 *   chainId: 1,
 *   maxFeePerGas: Value.fromGwei('10'),
 *   maxPriorityFeePerGas: Value.fromGwei('1'),
 *   nonce: 69n,
 *   to: '******************************************',
 *   value: Value.fromEther('1.5'),
 * })
 *
 * const signature = Secp256k1.sign({
 *   payload: TransactionEnvelopeEip7702.getSignPayload(envelope),
 *   privateKey: '0x...',
 * })
 *
 * // Serialize the Envelope with the Signature. // [!code focus]
 * const serialized = TransactionEnvelopeEip7702.serialize(envelope, { // [!code focus]
 *   signature  // [!code focus]
 * }) // [!code focus]
 *
 * // Broadcast the Envelope with `eth_sendRawTransaction`. // [!code focus]
 * const transport = RpcTransport.fromHttp('https://1.rpc.thirdweb.com') // [!code focus]
 * const hash = await transport.request({ // [!code focus]
 *   method: 'eth_sendRawTransaction', // [!code focus]
 *   params: [serialized], // [!code focus]
 * }) // [!code focus]
 * ```
 *
 * @category Transaction Envelopes
 */
export * as TransactionEnvelopeEip7702 from './core/TransactionEnvelopeEip7702.js'

/**
 * Utilities & types for working with **Transaction Receipts** as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/receipt.yaml)
 *
 * @example
 * ### Converting from RPC Format
 *
 * Receipts can be converted from RPC format using {@link ox#TransactionReceipt.(fromRpc:function)}:
 *
 * ```ts twoslash
 * import 'ox/window'
 * import { TransactionReceipt } from 'ox'
 *
 * const receipt = await window.ethereum!
 *   .request({
 *     method: 'eth_getTransactionReceipt',
 *     params: [
 *       '0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0',
 *     ],
 *   })
 *   .then(TransactionReceipt.fromRpc) // [!code hl]
 * // @log: {
 * // @log:   blobGasPrice: 270441n,
 * // @log:   blobGasUsed: 4919n,
 * // @log:   blockHash: "0xc350d807505fb835650f0013632c5515592987ba169bbc6626d9fc54d91f0f0b",
 * // @log:   blockNumber: 19868015n,
 * // @log:   contractAddress: null,
 * // @log:   cumulativeGasUsed: 533781n,
 * // @log:   effectiveGasPrice: 9062804489n,
 * // @log:   from: "******************************************",
 * // @log:   gasUsed: 175034n,
 * // @log:   logs: [],
 * // @log:   logsBloom: "0x00200000000000000000008080000000000000000040000000000000000000000000000000000000000000000000000022000000080000000000000000000000000000080000000000000008000000200000000000000000000200008020400000000000000000280000000000100000000000000000000000000010000000000000000000020000000000000020000000000001000000080000004000000000000000000000000000000000000000000000400000000000001000000000000000000002000000000000000020000000000000000000001000000000000000000000200000000000000000000000000000001000000000c00000000000000000",
 * // @log:   root: undefined,
 * // @log:   status: "success",
 * // @log:   to: "******************************************",
 * // @log:   transactionHash: "0x353fdfc38a2f26115daadee9f5b8392ce62b84f410957967e2ed56b35338cdd0",
 * // @log:   transactionIndex: 2,
 * // @log:   type: "eip1559",
 * // @log: }
 * ```
 *
 * @category Execution Spec
 */
export * as TransactionReceipt from './core/TransactionReceipt.js'

/**
 * Utilities & types for working with **Transaction Requests** as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/4aca1d7a3e5aab24c8f6437131289ad386944eaa/src/schemas/transaction.yaml#L358-L423)
 *
 * @example
 * ```ts twoslash
 * import 'ox/window'
 * import { Provider, TransactionRequest, Value } from 'ox'
 *
 * const provider = Provider.from(window.ethereum!)
 *
 * const request = TransactionRequest.toRpc({ // [!code focus]
 *   to: '******************************************', // [!code focus]
 *   value: Value.fromEther('0.01'), // [!code focus]
 * }) // [!code focus]
 *
 * const hash = await provider.request({
 *   method: 'eth_sendTransaction',
 *   params: [request],
 * })
 * ```
 *
 * @category Execution Spec
 */
export * as TransactionRequest from './core/TransactionRequest.js'

/**
 * Utility functions for working with [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712)
 *
 * @example
 * ### Getting Sign Payloads
 *
 * Typed Data can be converted to a sign payload using {@link ox#TypedData.(getSignPayload:function)}:
 *
 * ```ts twoslash
 * import { Secp256k1, TypedData, Hash } from 'ox'
 *
 * const payload = TypedData.getSignPayload({ // [!code focus:99]
 *   domain: {
 *     name: 'Ether Mail',
 *     version: '1',
 *     chainId: 1,
 *     verifyingContract: '******************************************',
 *   },
 *   types: {
 *     Person: [
 *       { name: 'name', type: 'string' },
 *       { name: 'wallet', type: 'address' },
 *     ],
 *     Mail: [
 *       { name: 'from', type: 'Person' },
 *       { name: 'to', type: 'Person' },
 *       { name: 'contents', type: 'string' },
 *     ],
 *   },
 *   primaryType: 'Mail',
 *   message: {
 *     from: {
 *       name: 'Cow',
 *       wallet: '******************************************',
 *     },
 *     to: {
 *       name: 'Bob',
 *       wallet: '******************************************',
 *     },
 *     contents: 'Hello, Bob!',
 *   },
 * })
 *
 * const signature = Secp256k1.sign({ payload, privateKey: '0x...' })
 * ```
 *
 * @category Signed & Typed Data
 */
export * as TypedData from './core/TypedData.js'

/**
 * Utilities & types for working with [EIP-191 Validator Data](https://eips.ethereum.org/EIPS/eip-191#0x00)
 *
 * @category Signed & Typed Data
 */
export * as ValidatorData from './core/ValidatorData.js'

/**
 * Utility functions for displaying and parsing Ethereum Values as defined under **2.1. Value** in the [Ethereum Yellow Paper](https://ethereum.github.io/yellowpaper/paper.pdf)
 *
 * @example
 * ```ts twoslash
 * // @noErrors
 * import { Value } from 'ox'
 *
 * const value = Value.fromEther('1')
 * // @log: 1_000_000_000_000_000_000n
 *
 * const formattedValue = Value.formatEther(value)
 * // @log: '1'
 *
 * const value = Value.fromEther('1', 'szabo')
 * // @log: 1_000_000n
 * ```
 *
 * @category Data
 */
export * as Value from './core/Value.js'

/**
 * Utility functions for [NIST P256](https://csrc.nist.gov/csrc/media/events/workshop-on-elliptic-curve-cryptography-standards/documents/papers/session6-adalier-mehmet.pdf) ECDSA cryptography using the [Web Authentication API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Authentication_API)
 *
 * @example
 * ### Creating Credentials
 *
 * Credentials can be created using {@link ox#WebAuthnP256.(createCredential:function)}:
 *
 * ```ts twoslash
 * import { WebAuthnP256 } from 'ox'
 *
 * const credential = await WebAuthnP256.createCredential({ name: 'Example' }) // [!code focus]
 * // @log: {
 * // @log:   id: 'oZ48...',
 * // @log:   publicKey: { x: 51421...5123n, y: 12345...6789n },
 * // @log:   raw: PublicKeyCredential {},
 * // @log: }
 *
 * const { metadata, signature } = await WebAuthnP256.sign({
 *   credentialId: credential.id,
 *   challenge: '0xdeadbeef',
 * })
 * ```
 *
 * @example
 * ### Signing Payloads
 *
 * Payloads can be signed using {@link ox#WebAuthnP256.(sign:function)}:
 *
 * ```ts twoslash
 * import { WebAuthnP256 } from 'ox'
 *
 * const credential = await WebAuthnP256.createCredential({
 *   name: 'Example',
 * })
 *
 * const { metadata, signature } = await WebAuthnP256.sign({ // [!code focus]
 *   credentialId: credential.id, // [!code focus]
 *   challenge: '0xdeadbeef', // [!code focus]
 * }) // [!code focus]
 * // @log: {
 * // @log:   metadata: {
 * // @log:     authenticatorData: '0x49960de5880e8c687434170f6476605b8fe4aeb9a28632c7995cf3ba831d97630500000000',
 * // @log:     clientDataJSON: '{"type":"webauthn.get","challenge":"9jEFijuhEWrM4SOW-tChJbUEHEP44VcjcJ-Bqo1fTM8","origin":"http://localhost:5173","crossOrigin":false}',
 * // @log:     challengeIndex: 23,
 * // @log:     typeIndex: 1,
 * // @log:     userVerificationRequired: true,
 * // @log:   },
 * // @log:   signature: { r: 51231...4215n, s: 12345...6789n },
 * // @log: }
 * ```
 *
 * @example
 * ### Verifying Signatures
 *
 * Signatures can be verified using {@link ox#WebAuthnP256.(verify:function)}:
 *
 * ```ts twoslash
 * import { WebAuthnP256 } from 'ox'
 *
 * const credential = await WebAuthnP256.createCredential({
 *   name: 'Example',
 * })
 *
 * const { metadata, signature } = await WebAuthnP256.sign({
 *   credentialId: credential.id,
 *   challenge: '0xdeadbeef',
 * })
 *
 * const result = await WebAuthnP256.verify({ // [!code focus]
 *   metadata, // [!code focus]
 *   challenge: '0xdeadbeef', // [!code focus]
 *   publicKey: credential.publicKey, // [!code focus]
 *   signature, // [!code focus]
 * }) // [!code focus]
 * // @log: true
 * ```
 *
 * @category Crypto
 */
export * as WebAuthnP256 from './core/WebAuthnP256.js'

/**
 * Utility functions for [NIST P256](https://csrc.nist.gov/csrc/media/events/workshop-on-elliptic-curve-cryptography-standards/documents/papers/session6-adalier-mehmet.pdf) ECDSA cryptography using the [Web Crypto API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Crypto_API)
 *
 * @example
 * ### Creating Key Pairs
 *
 * Key pairs can be created using {@link ox#WebCryptoP256.(createKeyPair:function)}:
 *
 * ```ts twoslash
 * import { WebCryptoP256 } from 'ox'
 *
 * const { publicKey, privateKey } = await WebCryptoP256.createKeyPair()
 * // @log: {
 * // @log:   privateKey: CryptoKey {},
 * // @log:   publicKey: {
 * // @log:     x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,
 * // @log:     y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,
 * // @log:     prefix: 4,
 * // @log:   },
 * // @log: }
 * ```
 *
 * @example
 * ### Signing Payloads
 *
 * Payloads can be signed using {@link ox#WebCryptoP256.(sign:function)}:
 *
 * ```ts twoslash
 * import { WebCryptoP256 } from 'ox'
 *
 * const { privateKey } = await WebCryptoP256.createKeyPair()
 *
 * const signature = await WebCryptoP256.sign({ // [!code focus]
 *   payload: '0xdeadbeef', // [!code focus]
 *   privateKey, // [!code focus]
 * }) // [!code focus]
 * // @log: {
 * // @log:   r: 151231...4423n,
 * // @log:   s: 516123...5512n,
 * // @log: }
 * ```
 *
 * @example
 * ### Verifying Signatures
 *
 * Signatures can be verified using {@link ox#WebCryptoP256.(verify:function)}:
 *
 * ```ts twoslash
 * import { WebCryptoP256 } from 'ox'
 *
 * const { privateKey, publicKey } = await WebCryptoP256.createKeyPair()
 * const signature = await WebCryptoP256.sign({ payload: '0xdeadbeef', privateKey })
 *
 * const verified = await WebCryptoP256.verify({ // [!code focus]
 *   payload: '0xdeadbeef', // [!code focus]
 *   publicKey, // [!code focus]
 *   signature, // [!code focus]
 * }) // [!code focus]
 * // @log: true
 * ```
 *
 * @category Crypto
 */
export * as WebCryptoP256 from './core/WebCryptoP256.js'

/**
 * Utilities & types for working with Withdrawals as defined in the [Execution API specification](https://github.com/ethereum/execution-apis/blob/main/src/schemas/withdrawal.yaml)
 *
 * @category Execution Spec
 */
export * as Withdrawal from './core/Withdrawal.js'
