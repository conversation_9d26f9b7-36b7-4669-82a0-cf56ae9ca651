{"name": "ox", "description": "Ethereum Standard Library", "version": "0.8.1", "main": "./_cjs/index.js", "module": "./_esm/index.js", "types": "./_types/index.d.ts", "typings": "./_types/index.d.ts", "sideEffects": false, "files": ["*", "!**/_test/**", "!**/*.tsbuildinfo", "!tsconfig.build.json", "!jsr.json"], "dependencies": {"@adraffy/ens-normalize": "^1.11.0", "@noble/ciphers": "^1.3.0", "@noble/curves": "^1.9.1", "@noble/hashes": "^1.8.0", "@scure/bip32": "^1.7.0", "@scure/bip39": "^1.6.0", "abitype": "^1.0.8", "eventemitter3": "5.0.1"}, "peerDependencies": {"typescript": ">=5.4.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "license": "MIT", "homepage": "https://wevm.dev", "repository": "wevm/ox", "authors": ["awkweb", "jxom"], "funding": [{"type": "github", "url": "https://github.com/sponsors/wevm"}], "keywords": ["ethereum", "standard", "library", "typescript", "evm"], "exports": {"./Abi": {"types": "./_types/core/Abi.d.ts", "import": "./_esm/core/Abi.js", "default": "./_cjs/core/Abi.js"}, "./AbiConstructor": {"types": "./_types/core/AbiConstructor.d.ts", "import": "./_esm/core/AbiConstructor.js", "default": "./_cjs/core/AbiConstructor.js"}, "./AbiError": {"types": "./_types/core/AbiError.d.ts", "import": "./_esm/core/AbiError.js", "default": "./_cjs/core/AbiError.js"}, "./AbiEvent": {"types": "./_types/core/AbiEvent.d.ts", "import": "./_esm/core/AbiEvent.js", "default": "./_cjs/core/AbiEvent.js"}, "./AbiFunction": {"types": "./_types/core/AbiFunction.d.ts", "import": "./_esm/core/AbiFunction.js", "default": "./_cjs/core/AbiFunction.js"}, "./AbiItem": {"types": "./_types/core/AbiItem.d.ts", "import": "./_esm/core/AbiItem.js", "default": "./_cjs/core/AbiItem.js"}, "./AbiParameters": {"types": "./_types/core/AbiParameters.d.ts", "import": "./_esm/core/AbiParameters.js", "default": "./_cjs/core/AbiParameters.js"}, "./AccessList": {"types": "./_types/core/AccessList.d.ts", "import": "./_esm/core/AccessList.js", "default": "./_cjs/core/AccessList.js"}, "./AccountProof": {"types": "./_types/core/AccountProof.d.ts", "import": "./_esm/core/AccountProof.js", "default": "./_cjs/core/AccountProof.js"}, "./Address": {"types": "./_types/core/Address.d.ts", "import": "./_esm/core/Address.js", "default": "./_cjs/core/Address.js"}, "./AesGcm": {"types": "./_types/core/AesGcm.d.ts", "import": "./_esm/core/AesGcm.js", "default": "./_cjs/core/AesGcm.js"}, "./Authorization": {"types": "./_types/core/Authorization.d.ts", "import": "./_esm/core/Authorization.js", "default": "./_cjs/core/Authorization.js"}, "./Base58": {"types": "./_types/core/Base58.d.ts", "import": "./_esm/core/Base58.js", "default": "./_cjs/core/Base58.js"}, "./Base64": {"types": "./_types/core/Base64.d.ts", "import": "./_esm/core/Base64.js", "default": "./_cjs/core/Base64.js"}, "./BinaryStateTree": {"types": "./_types/core/BinaryStateTree.d.ts", "import": "./_esm/core/BinaryStateTree.js", "default": "./_cjs/core/BinaryStateTree.js"}, "./Blobs": {"types": "./_types/core/Blobs.d.ts", "import": "./_esm/core/Blobs.js", "default": "./_cjs/core/Blobs.js"}, "./Block": {"types": "./_types/core/Block.d.ts", "import": "./_esm/core/Block.js", "default": "./_cjs/core/Block.js"}, "./BlockOverrides": {"types": "./_types/core/BlockOverrides.d.ts", "import": "./_esm/core/BlockOverrides.js", "default": "./_cjs/core/BlockOverrides.js"}, "./Bloom": {"types": "./_types/core/Bloom.d.ts", "import": "./_esm/core/Bloom.js", "default": "./_cjs/core/Bloom.js"}, "./Bls": {"types": "./_types/core/Bls.d.ts", "import": "./_esm/core/Bls.js", "default": "./_cjs/core/Bls.js"}, "./BlsPoint": {"types": "./_types/core/BlsPoint.d.ts", "import": "./_esm/core/BlsPoint.js", "default": "./_cjs/core/BlsPoint.js"}, "./Bytes": {"types": "./_types/core/Bytes.d.ts", "import": "./_esm/core/Bytes.js", "default": "./_cjs/core/Bytes.js"}, "./Caches": {"types": "./_types/core/Caches.d.ts", "import": "./_esm/core/Caches.js", "default": "./_cjs/core/Caches.js"}, "./ContractAddress": {"types": "./_types/core/ContractAddress.d.ts", "import": "./_esm/core/ContractAddress.js", "default": "./_cjs/core/ContractAddress.js"}, "./Ens": {"types": "./_types/core/Ens.d.ts", "import": "./_esm/core/Ens.js", "default": "./_cjs/core/Ens.js"}, "./Errors": {"types": "./_types/core/Errors.d.ts", "import": "./_esm/core/Errors.js", "default": "./_cjs/core/Errors.js"}, "./Fee": {"types": "./_types/core/Fee.d.ts", "import": "./_esm/core/Fee.js", "default": "./_cjs/core/Fee.js"}, "./Filter": {"types": "./_types/core/Filter.d.ts", "import": "./_esm/core/Filter.js", "default": "./_cjs/core/Filter.js"}, "./Hash": {"types": "./_types/core/Hash.d.ts", "import": "./_esm/core/Hash.js", "default": "./_cjs/core/Hash.js"}, "./HdKey": {"types": "./_types/core/HdKey.d.ts", "import": "./_esm/core/HdKey.js", "default": "./_cjs/core/HdKey.js"}, "./Hex": {"types": "./_types/core/Hex.d.ts", "import": "./_esm/core/Hex.js", "default": "./_cjs/core/Hex.js"}, "./Json": {"types": "./_types/core/Json.d.ts", "import": "./_esm/core/Json.js", "default": "./_cjs/core/Json.js"}, "./Keystore": {"types": "./_types/core/Keystore.d.ts", "import": "./_esm/core/Keystore.js", "default": "./_cjs/core/Keystore.js"}, "./Kzg": {"types": "./_types/core/Kzg.d.ts", "import": "./_esm/core/Kzg.js", "default": "./_cjs/core/Kzg.js"}, "./Log": {"types": "./_types/core/Log.d.ts", "import": "./_esm/core/Log.js", "default": "./_cjs/core/Log.js"}, "./Mnemonic": {"types": "./_types/core/Mnemonic.d.ts", "import": "./_esm/core/Mnemonic.js", "default": "./_cjs/core/Mnemonic.js"}, "./P256": {"types": "./_types/core/P256.d.ts", "import": "./_esm/core/P256.js", "default": "./_cjs/core/P256.js"}, "./PersonalMessage": {"types": "./_types/core/PersonalMessage.d.ts", "import": "./_esm/core/PersonalMessage.js", "default": "./_cjs/core/PersonalMessage.js"}, "./Provider": {"types": "./_types/core/Provider.d.ts", "import": "./_esm/core/Provider.js", "default": "./_cjs/core/Provider.js"}, "./PublicKey": {"types": "./_types/core/PublicKey.d.ts", "import": "./_esm/core/PublicKey.js", "default": "./_cjs/core/PublicKey.js"}, "./Rlp": {"types": "./_types/core/Rlp.d.ts", "import": "./_esm/core/Rlp.js", "default": "./_cjs/core/Rlp.js"}, "./RpcRequest": {"types": "./_types/core/RpcRequest.d.ts", "import": "./_esm/core/RpcRequest.js", "default": "./_cjs/core/RpcRequest.js"}, "./RpcResponse": {"types": "./_types/core/RpcResponse.d.ts", "import": "./_esm/core/RpcResponse.js", "default": "./_cjs/core/RpcResponse.js"}, "./RpcSchema": {"types": "./_types/core/RpcSchema.d.ts", "import": "./_esm/core/RpcSchema.js", "default": "./_cjs/core/RpcSchema.js"}, "./RpcTransport": {"types": "./_types/core/RpcTransport.d.ts", "import": "./_esm/core/RpcTransport.js", "default": "./_cjs/core/RpcTransport.js"}, "./Secp256k1": {"types": "./_types/core/Secp256k1.d.ts", "import": "./_esm/core/Secp256k1.js", "default": "./_cjs/core/Secp256k1.js"}, "./Signature": {"types": "./_types/core/Signature.d.ts", "import": "./_esm/core/Signature.js", "default": "./_cjs/core/Signature.js"}, "./Siwe": {"types": "./_types/core/Siwe.d.ts", "import": "./_esm/core/Siwe.js", "default": "./_cjs/core/Siwe.js"}, "./Solidity": {"types": "./_types/core/Solidity.d.ts", "import": "./_esm/core/Solidity.js", "default": "./_cjs/core/Solidity.js"}, "./StateOverrides": {"types": "./_types/core/StateOverrides.d.ts", "import": "./_esm/core/StateOverrides.js", "default": "./_cjs/core/StateOverrides.js"}, "./Transaction": {"types": "./_types/core/Transaction.d.ts", "import": "./_esm/core/Transaction.js", "default": "./_cjs/core/Transaction.js"}, "./TransactionEnvelope": {"types": "./_types/core/TransactionEnvelope.d.ts", "import": "./_esm/core/TransactionEnvelope.js", "default": "./_cjs/core/TransactionEnvelope.js"}, "./TransactionEnvelopeEip1559": {"types": "./_types/core/TransactionEnvelopeEip1559.d.ts", "import": "./_esm/core/TransactionEnvelopeEip1559.js", "default": "./_cjs/core/TransactionEnvelopeEip1559.js"}, "./TransactionEnvelopeEip2930": {"types": "./_types/core/TransactionEnvelopeEip2930.d.ts", "import": "./_esm/core/TransactionEnvelopeEip2930.js", "default": "./_cjs/core/TransactionEnvelopeEip2930.js"}, "./TransactionEnvelopeEip4844": {"types": "./_types/core/TransactionEnvelopeEip4844.d.ts", "import": "./_esm/core/TransactionEnvelopeEip4844.js", "default": "./_cjs/core/TransactionEnvelopeEip4844.js"}, "./TransactionEnvelopeEip7702": {"types": "./_types/core/TransactionEnvelopeEip7702.d.ts", "import": "./_esm/core/TransactionEnvelopeEip7702.js", "default": "./_cjs/core/TransactionEnvelopeEip7702.js"}, "./TransactionEnvelopeLegacy": {"types": "./_types/core/TransactionEnvelopeLegacy.d.ts", "import": "./_esm/core/TransactionEnvelopeLegacy.js", "default": "./_cjs/core/TransactionEnvelopeLegacy.js"}, "./TransactionReceipt": {"types": "./_types/core/TransactionReceipt.d.ts", "import": "./_esm/core/TransactionReceipt.js", "default": "./_cjs/core/TransactionReceipt.js"}, "./TransactionRequest": {"types": "./_types/core/TransactionRequest.d.ts", "import": "./_esm/core/TransactionRequest.js", "default": "./_cjs/core/TransactionRequest.js"}, "./TypedData": {"types": "./_types/core/TypedData.d.ts", "import": "./_esm/core/TypedData.js", "default": "./_cjs/core/TypedData.js"}, "./ValidatorData": {"types": "./_types/core/ValidatorData.d.ts", "import": "./_esm/core/ValidatorData.js", "default": "./_cjs/core/ValidatorData.js"}, "./Value": {"types": "./_types/core/Value.d.ts", "import": "./_esm/core/Value.js", "default": "./_cjs/core/Value.js"}, "./WebAuthnP256": {"types": "./_types/core/WebAuthnP256.d.ts", "import": "./_esm/core/WebAuthnP256.js", "default": "./_cjs/core/WebAuthnP256.js"}, "./WebCryptoP256": {"types": "./_types/core/WebCryptoP256.d.ts", "import": "./_esm/core/WebCryptoP256.js", "default": "./_cjs/core/WebCryptoP256.js"}, "./Withdrawal": {"types": "./_types/core/Withdrawal.d.ts", "import": "./_esm/core/Withdrawal.js", "default": "./_cjs/core/Withdrawal.js"}, "./erc4337/EntryPoint": {"types": "./_types/erc4337/EntryPoint.d.ts", "import": "./_esm/erc4337/EntryPoint.js", "default": "./_cjs/erc4337/EntryPoint.js"}, "./erc4337/RpcSchema": {"types": "./_types/erc4337/RpcSchema.d.ts", "import": "./_esm/erc4337/RpcSchema.js", "default": "./_cjs/erc4337/RpcSchema.js"}, "./erc4337/UserOperation": {"types": "./_types/erc4337/UserOperation.d.ts", "import": "./_esm/erc4337/UserOperation.js", "default": "./_cjs/erc4337/UserOperation.js"}, "./erc4337/UserOperationGas": {"types": "./_types/erc4337/UserOperationGas.d.ts", "import": "./_esm/erc4337/UserOperationGas.js", "default": "./_cjs/erc4337/UserOperationGas.js"}, "./erc4337/UserOperationReceipt": {"types": "./_types/erc4337/UserOperationReceipt.d.ts", "import": "./_esm/erc4337/UserOperationReceipt.js", "default": "./_cjs/erc4337/UserOperationReceipt.js"}, "./erc4337": {"types": "./_types/erc4337/index.d.ts", "import": "./_esm/erc4337/index.js", "default": "./_cjs/erc4337/index.js"}, "./erc6492/WrappedSignature": {"types": "./_types/erc6492/WrappedSignature.d.ts", "import": "./_esm/erc6492/WrappedSignature.js", "default": "./_cjs/erc6492/WrappedSignature.js"}, "./erc6492": {"types": "./_types/erc6492/index.d.ts", "import": "./_esm/erc6492/index.js", "default": "./_cjs/erc6492/index.js"}, "./index.docs": {"types": "./_types/index.docs.d.ts", "import": "./_esm/index.docs.js", "default": "./_cjs/index.docs.js"}, ".": {"types": "./_types/index.d.ts", "import": "./_esm/index.js", "default": "./_cjs/index.js"}, "./package.json": "./package.json", "./trusted-setups/Paths": {"types": "./_types/trusted-setups/Paths.d.ts", "import": "./_esm/trusted-setups/Paths.js", "default": "./_cjs/trusted-setups/Paths.js"}, "./trusted-setups": {"types": "./_types/trusted-setups/index.d.ts", "import": "./_esm/trusted-setups/index.js", "default": "./_cjs/trusted-setups/index.js"}, "./window": {"types": "./_types/window/index.d.ts", "import": "./_esm/window/index.js", "default": "./_cjs/window/index.js"}}}