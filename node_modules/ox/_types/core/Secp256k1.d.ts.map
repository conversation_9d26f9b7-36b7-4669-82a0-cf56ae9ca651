{"version": 3, "file": "Secp256k1.d.ts", "sourceRoot": "", "sources": ["../../core/Secp256k1.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAC3C,OAAO,KAAK,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAEhD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAA;AAEhD,qDAAqD;AACrD,eAAO,MAAM,KAAK,yDAAY,CAAA;AAE9B;;;;;;;;;;;;GAYG;AACH,wBAAgB,YAAY,CAC1B,OAAO,EAAE,YAAY,CAAC,OAAO,GAC5B,SAAS,CAAC,SAAS,CAMrB;AAED,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC,KAAK,OAAO,GAAG;QACb;;WAEG;QACH,UAAU,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAA;KAClC,CAAA;IAED,KAAK,SAAS,GACV,GAAG,CAAC,IAAI,CAAC,SAAS,GAClB,SAAS,CAAC,IAAI,CAAC,SAAS,GACxB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;GAYG;AACH,wBAAgB,gBAAgB,CAAC,EAAE,SAAS,KAAK,GAAG,OAAO,GAAG,KAAK,EACjE,OAAO,GAAE,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAM,GACzC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAKjC;AAED,MAAM,CAAC,OAAO,WAAW,gBAAgB,CAAC;IACxC,KAAK,OAAO,CAAC,EAAE,SAAS,KAAK,GAAG,OAAO,GAAG,KAAK,IAAI;QACjD;;;WAGG;QACH,EAAE,CAAC,EAAE,EAAE,GAAG,KAAK,GAAG,OAAO,GAAG,SAAS,CAAA;KACtC,CAAA;IAED,KAAK,UAAU,CAAC,EAAE,SAAS,KAAK,GAAG,OAAO,IACtC,CAAC,EAAE,SAAS,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAC1C,CAAC,EAAE,SAAS,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAA;IAExC,KAAK,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAClE;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,cAAc,CAC5B,OAAO,EAAE,cAAc,CAAC,OAAO,GAC9B,cAAc,CAAC,UAAU,CAE3B;AAED,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC,KAAK,OAAO,GAAG;QACb,+BAA+B;QAC/B,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAA;QAC9B,gCAAgC;QAChC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAA;KAC/B,CAAA;IAED,KAAK,UAAU,GAAG,OAAO,CAAC,OAAO,CAAA;IAEjC,KAAK,SAAS,GACV,OAAO,CAAC,aAAa,CAAC,SAAS,GAC/B,gBAAgB,CAAC,SAAS,GAC1B,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,gBAAgB,CAC9B,OAAO,EAAE,gBAAgB,CAAC,OAAO,GAChC,SAAS,CAAC,SAAS,CASrB;AAED,MAAM,CAAC,OAAO,WAAW,gBAAgB,CAAC;IACxC,KAAK,OAAO,GAAG;QACb,+BAA+B;QAC/B,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAA;QAC9B,gCAAgC;QAChC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAA;KAC/B,CAAA;IAED,KAAK,SAAS,GACV,SAAS,CAAC,IAAI,CAAC,SAAS,GACxB,GAAG,CAAC,IAAI,CAAC,SAAS,GAClB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,SAAS,CAwB/D;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,OAAO,GAAG;QACb;;;WAGG;QACH,YAAY,CAAC,EAAE,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,SAAS,CAAA;QAC1D;;WAEG;QACH,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC1B;;WAEG;QACH,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAA;QAC9B;;WAEG;QACH,UAAU,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAA;KAClC,CAAA;IAED,KAAK,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC/D;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,wBAAgB,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAUvD;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,OAAO,GAAG;QACb,mFAAmF;QACnF,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC1B,+BAA+B;QAC/B,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAA;KAC/B,GAAG,KAAK,CACL;QACE,uCAAuC;QACvC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAA;QACxB,gCAAgC;QAChC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAA;KAC/B,GACD;QACE,0CAA0C;QAC1C,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QACvC,gCAAgC;QAChC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;KACtC,CACJ,CAAA;IAED,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC"}