{"name": "@metamask/sdk-communication-layer", "version": "0.32.0", "description": "", "homepage": "https://github.com/MetaMask/metamask-sdk#readme", "bugs": {"url": "https://github.com/MetaMask/metamask-sdk/issues"}, "repository": {"type": "git", "url": "https://github.com/MetaMask/metamask-sdk.git", "directory": "packages/sdk-communication-layer"}, "main": "dist/node/cjs/metamask-sdk-communication-layer.js", "unpkg": "dist/browser/umd/metamask-sdk-communication-layer.js", "module": "dist/node/es/metamask-sdk-communication-layer.js", "browser": "dist/browser/es/metamask-sdk-communication-layer.js", "react-native": "dist/react-native/es/metamask-sdk-communication-layer.js", "types": "dist/types/src/index.d.ts", "files": ["/dist"], "scripts": {"build:types": "tsc --project tsconfig.build.json --emitDeclarationOnly --outDir dist/types", "build:clean": "yarn clean && yarn build", "build": "yarn build:types && rollup -c --bundleConfigAsCjs", "build:dev": "yarn build:types && NODE_ENV=dev rollup -c --bundleConfigAsCjs", "dev": "concurrently \"tsc --watch\" \"rollup -c --bundleConfigAsCjs -w\"", "build:post-tsc": "echo 'N/A'", "build:pre-tsc": "echo 'N/A'", "size": "size-limit", "clean": "rimraf ./dist", "lint": "yarn lint:eslint && yarn lint:misc --check", "lint:changelog": "../../scripts/validate-changelog.sh @metamask/sdk-communication-layer", "lint:eslint": "eslint . --cache --ext js,ts", "lint:fix": "yarn lint:eslint --fix && yarn lint:misc --write", "lint:misc": "prettier '**/*.json' '**/*.md' '!CHANGELOG.md' --ignore-path ../../.gitignore", "publish:preview": "yarn npm publish --tag preview", "prepack": "../../scripts/prepack.sh", "reset": "yarn clean && rimraf ./node_modules/", "test": "jest --testPathIgnorePatterns \"/e2e/\"", "test:e2e": "jest --testPathPattern \"/e2e/\"", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --passWithNoTests --setupFilesAfterEnv ./jest-preload.js --testPathIgnorePatterns \"/e2e/\"", "test:dev": "jest", "watch": "rollup -c --bundleConfigAsCjs -w"}, "dependencies": {"bufferutil": "^4.0.8", "date-fns": "^2.29.3", "debug": "^4.3.4", "utf-8-validate": "^5.0.2", "uuid": "^8.3.2"}, "devDependencies": {"@jest/globals": "^29.3.1", "@lavamoat/allow-scripts": "^2.3.1", "@metamask/auto-changelog": "3.1.0", "@metamask/eslint-config": "^6.0.0", "@metamask/eslint-config-nodejs": "^6.0.0", "@metamask/eslint-config-typescript": "^6.0.0", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-replace": "^6.0.1", "@rollup/plugin-terser": "^0.4.4", "@size-limit/preset-big-lib": "^11.0.2", "@types/jest": "^29.2.4", "@types/node": "^20.1.3", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "concurrently": "^9.1.2", "cross-fetch": "^4.0.0", "eciesjs": "^0.4.11", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jest": "^24.4.0", "eslint-plugin-jsdoc": "^36.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "eventemitter2": "^6.4.9", "jest": "^29.3.1", "prettier": "^2.3.0", "rimraf": "^3.0.2", "rollup": "^4.26.0", "rollup-plugin-jscc": "^2.0.0", "rollup-plugin-natives": "^0.7.5", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-polyfill-node": "^0.13.0", "rollup-plugin-sizes": "^1.0.6", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-visualizer": "^5.12.0", "size-limit": "^11.1.6", "socket.io-client": "^4.5.1", "stream-browserify": "^3.0.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^5.6.3"}, "peerDependencies": {"cross-fetch": "^4.0.0", "eciesjs": "*", "eventemitter2": "^6.4.9", "readable-stream": "^3.6.2", "socket.io-client": "^4.5.1"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "lavamoat": {"allowScripts": {"@lavamoat/preinstall-always-fail": false, "canvas": true, "eciesjs>secp256k1": false, "socket.io-client>engine.io-client>ws>bufferutil": false, "socket.io-client>engine.io-client>ws>utf-8-validate": false, "bufferutil": false, "utf-8-validate": false}}}