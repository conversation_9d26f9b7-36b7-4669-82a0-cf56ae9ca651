import e from"cross-fetch";import t from"debug";import n,{<PERSON><PERSON><PERSON> as r}from"buffer";import i from"node:crypto";import o,{EventEmitter2 as s}from"eventemitter2";import{validate as a,v4 as c}from"uuid";import{io as l}from"socket.io-client";import d,{EventEmitter as u}from"events";import h from"@react-native-async-storage/async-storage";function f(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;var p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function m(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function g(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})})),n}var y={},v={},b={};function E(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`positive integer expected, not ${e}`)}function w(e){if("boolean"!=typeof e)throw new Error(`boolean expected, not ${e}`)}function S(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name}function _(e,...t){if(!S(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error(`Uint8Array expected of length ${t}, not of length=${e.length}`)}function C(e){if("function"!=typeof e||"function"!=typeof e.create)throw new Error("hash must be wrapped by utils.wrapConstructor");E(e.outputLen),E(e.blockLen)}function k(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function M(e,t){_(e);const n=t.outputLen;if(e.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}Object.defineProperty(b,"__esModule",{value:!0}),b.isBytes=S,b.number=E,b.bool=w,b.bytes=_,b.hash=C,b.exists=k,b.output=M;const A={number:E,bool:w,bytes:_,hash:C,exists:k,output:M};b.default=A,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.wrapCipher=e.Hash=e.nextTick=e.isLE=e.createView=e.u32=e.u16=e.u8=void 0,e.bytesToHex=r,e.hexToBytes=s,e.hexToNumber=a,e.bytesToNumberBE=function(e){return a(r(e))},e.numberToBytesBE=function(e,t){return s(e.toString(16).padStart(2*t,"0"))},e.asyncLoop=async function(t,n,r){let i=Date.now();for(let o=0;o<t;o++){r(o);const t=Date.now()-i;t>=0&&t<n||(await(0,e.nextTick)(),i+=t)}},e.utf8ToBytes=c,e.bytesToUtf8=function(e){return(new TextDecoder).decode(e)},e.toBytes=function(e){if("string"==typeof e)e=c(e);else{if(!(0,t.isBytes)(e))throw new Error("Uint8Array expected, got "+typeof e);e=d(e)}return e},e.concatBytes=function(...e){let n=0;for(let r=0;r<e.length;r++){const i=e[r];(0,t.bytes)(i),n+=i.length}const r=new Uint8Array(n);for(let t=0,n=0;t<e.length;t++){const i=e[t];r.set(i,n),n+=i.length}return r},e.checkOpts=function(e,t){if(null==t||"object"!=typeof t)throw new Error("options must be defined");return Object.assign(e,t)},e.equalBytes=function(e,t){if(e.length!==t.length)return!1;let n=0;for(let r=0;r<e.length;r++)n|=e[r]^t[r];return 0===n},e.setBigUint64=l,e.u64Lengths=function(t,n){const r=new Uint8Array(16),i=(0,e.createView)(r);return l(i,0,BigInt(n?n.length:0),!0),l(i,8,BigInt(t.length),!0),r},e.isAligned32=function(e){return e.byteOffset%4==0},e.copyBytes=d,e.clean=function(...e){for(let t=0;t<e.length;t++)e[t].fill(0)};const t=b;e.u8=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength);e.u16=e=>new Uint16Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/2));e.u32=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4));if(e.createView=e=>new DataView(e.buffer,e.byteOffset,e.byteLength),e.isLE=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0],!e.isLE)throw new Error("Non little-endian hardware is not supported");const n=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));function r(e){(0,t.bytes)(e);let r="";for(let t=0;t<e.length;t++)r+=n[e[t]];return r}const i={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function o(e){return e>=i._0&&e<=i._9?e-i._0:e>=i._A&&e<=i._F?e-(i._A-10):e>=i._a&&e<=i._f?e-(i._a-10):void 0}function s(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);const t=e.length,n=t/2;if(t%2)throw new Error("padded hex string expected, got unpadded hex of length "+t);const r=new Uint8Array(n);for(let t=0,i=0;t<n;t++,i+=2){const n=o(e.charCodeAt(i)),s=o(e.charCodeAt(i+1));if(void 0===n||void 0===s){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character "'+t+'" at index '+i)}r[t]=16*n+s}return r}function a(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);return BigInt(""===e?"0":`0x${e}`)}function c(e){if("string"!=typeof e)throw new Error("string expected, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))}e.nextTick=async()=>{};e.Hash=class{};function l(e,t,n,r){if("function"==typeof e.setBigUint64)return e.setBigUint64(t,n,r);const i=BigInt(32),o=BigInt(4294967295),s=Number(n>>i&o),a=Number(n&o),c=r?4:0,l=r?0:4;e.setUint32(t+c,s,r),e.setUint32(t+l,a,r)}function d(e){return Uint8Array.from(e)}e.wrapCipher=(e,t)=>(Object.assign(t,e),t)}(v);var x={},I={};Object.defineProperty(I,"__esModule",{value:!0}),I.AEAD_TAG_LENGTH=I.XCHACHA20_NONCE_LENGTH=I.CURVE25519_PUBLIC_KEY_SIZE=I.ETH_PUBLIC_KEY_SIZE=I.UNCOMPRESSED_PUBLIC_KEY_SIZE=I.COMPRESSED_PUBLIC_KEY_SIZE=I.SECRET_KEY_LENGTH=void 0,I.SECRET_KEY_LENGTH=32,I.COMPRESSED_PUBLIC_KEY_SIZE=33,I.UNCOMPRESSED_PUBLIC_KEY_SIZE=65,I.ETH_PUBLIC_KEY_SIZE=64,I.CURVE25519_PUBLIC_KEY_SIZE=32,I.XCHACHA20_NONCE_LENGTH=24,I.AEAD_TAG_LENGTH=16,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ephemeralKeySize=e.symmetricNonceLength=e.symmetricAlgorithm=e.isHkdfKeyCompressed=e.isEphemeralKeyCompressed=e.ellipticCurve=e.ECIES_CONFIG=void 0;var t=I,n=function(){this.ellipticCurve="secp256k1",this.isEphemeralKeyCompressed=!1,this.isHkdfKeyCompressed=!1,this.symmetricAlgorithm="aes-256-gcm",this.symmetricNonceLength=16};e.ECIES_CONFIG=new n;e.ellipticCurve=function(){return e.ECIES_CONFIG.ellipticCurve};e.isEphemeralKeyCompressed=function(){return e.ECIES_CONFIG.isEphemeralKeyCompressed};e.isHkdfKeyCompressed=function(){return e.ECIES_CONFIG.isHkdfKeyCompressed};e.symmetricAlgorithm=function(){return e.ECIES_CONFIG.symmetricAlgorithm};e.symmetricNonceLength=function(){return e.ECIES_CONFIG.symmetricNonceLength};e.ephemeralKeySize=function(){var n={secp256k1:e.ECIES_CONFIG.isEphemeralKeyCompressed?t.COMPRESSED_PUBLIC_KEY_SIZE:t.UNCOMPRESSED_PUBLIC_KEY_SIZE,x25519:t.CURVE25519_PUBLIC_KEY_SIZE,ed25519:t.CURVE25519_PUBLIC_KEY_SIZE};if(e.ECIES_CONFIG.ellipticCurve in n)return n[e.ECIES_CONFIG.ellipticCurve];throw new Error("Not implemented")}}(x);var R={},P={},O={},T={},N={},L={};Object.defineProperty(L,"__esModule",{value:!0}),L.crypto=void 0;const D=i;L.crypto=D&&"object"==typeof D&&"webcrypto"in D?D.webcrypto:D&&"object"==typeof D&&"randomBytes"in D?D:void 0,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.gcm=e.ctr=e.cbc=e.utils=void 0,e.randomBytes=i,e.getWebcryptoSubtle=o,e.managedNonce=function(e){return(0,n.number)(e.nonceLength),(t,...n)=>({encrypt(o,...s){const{nonceLength:a}=e,c=i(a),l=e(t,c,...n).encrypt(o,...s),d=(0,r.concatBytes)(c,l);return l.fill(0),d},decrypt(r,...i){const{nonceLength:o}=e,s=r.subarray(0,o),a=r.subarray(o);return e(t,s,...n).decrypt(a,...i)}})};const t=L,n=b,r=v;function i(e=32){if(t.crypto&&"function"==typeof t.crypto.getRandomValues)return t.crypto.getRandomValues(new Uint8Array(e));if(t.crypto&&"function"==typeof t.crypto.randomBytes)return t.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}function o(){if(t.crypto&&"object"==typeof t.crypto.subtle&&null!=t.crypto.subtle)return t.crypto.subtle;throw new Error("crypto.subtle must be defined")}e.utils={async encrypt(e,t,n,r){const i=o(),s=await i.importKey("raw",e,t,!0,["encrypt"]),a=await i.encrypt(n,s,r);return new Uint8Array(a)},async decrypt(e,t,n,r){const i=o(),s=await i.importKey("raw",e,t,!0,["decrypt"]),a=await i.decrypt(n,s,r);return new Uint8Array(a)}};const s={CBC:"AES-CBC",CTR:"AES-CTR",GCM:"AES-GCM"};function a(t){return(r,i,o)=>{(0,n.bytes)(r),(0,n.bytes)(i);const a={name:t,length:8*r.length},c=function(e,t,n){if(e===s.CBC)return{name:s.CBC,iv:t};if(e===s.CTR)return{name:s.CTR,counter:t,length:64};if(e===s.GCM)return n?{name:s.GCM,iv:t,additionalData:n}:{name:s.GCM,iv:t};throw new Error("unknown aes block mode")}(t,i,o);return{encrypt:t=>((0,n.bytes)(t),e.utils.encrypt(r,a,c,t)),decrypt:t=>((0,n.bytes)(t),e.utils.decrypt(r,a,c,t))}}}e.cbc=a(s.CBC),e.ctr=a(s.CTR),e.gcm=a(s.GCM)}(N);var B={},K={},j={},$={};function U(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`positive integer expected, not ${e}`)}function H(e){if("boolean"!=typeof e)throw new Error(`boolean expected, not ${e}`)}function F(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name}function q(e,...t){if(!F(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error(`Uint8Array expected of length ${t}, not of length=${e.length}`)}function z(e){if("function"!=typeof e||"function"!=typeof e.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");U(e.outputLen),U(e.blockLen)}function W(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function V(e,t){q(e);const n=t.outputLen;if(e.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}Object.defineProperty($,"__esModule",{value:!0}),$.isBytes=F,$.number=U,$.bool=H,$.bytes=q,$.hash=z,$.exists=W,$.output=V;const G={number:U,bool:H,bytes:q,hash:z,exists:W,output:V};$.default=G;var Y={},Z={};Object.defineProperty(Z,"__esModule",{value:!0}),Z.crypto=void 0;const J=i;Z.crypto=J&&"object"==typeof J&&"webcrypto"in J?J.webcrypto:J&&"object"==typeof J&&"randomBytes"in J?J:void 0,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Hash=e.nextTick=e.byteSwapIfBE=e.byteSwap=e.isLE=e.rotl=e.rotr=e.createView=e.u32=e.u8=void 0,e.isBytes=function(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name},e.byteSwap32=function(t){for(let n=0;n<t.length;n++)t[n]=(0,e.byteSwap)(t[n])},e.bytesToHex=function(e){(0,n.bytes)(e);let t="";for(let n=0;n<e.length;n++)t+=r[e[n]];return t},e.hexToBytes=function(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);const t=e.length,n=t/2;if(t%2)throw new Error("padded hex string expected, got unpadded hex of length "+t);const r=new Uint8Array(n);for(let t=0,i=0;t<n;t++,i+=2){const n=o(e.charCodeAt(i)),s=o(e.charCodeAt(i+1));if(void 0===n||void 0===s){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character "'+t+'" at index '+i)}r[t]=16*n+s}return r},e.asyncLoop=async function(t,n,r){let i=Date.now();for(let o=0;o<t;o++){r(o);const t=Date.now()-i;t>=0&&t<n||(await(0,e.nextTick)(),i+=t)}},e.utf8ToBytes=s,e.toBytes=a,e.concatBytes=function(...e){let t=0;for(let r=0;r<e.length;r++){const i=e[r];(0,n.bytes)(i),t+=i.length}const r=new Uint8Array(t);for(let t=0,n=0;t<e.length;t++){const i=e[t];r.set(i,n),n+=i.length}return r},e.checkOpts=function(e,t){if(void 0!==t&&"[object Object]"!==c.call(t))throw new Error("Options should be object or undefined");return Object.assign(e,t)},e.wrapConstructor=function(e){const t=t=>e().update(a(t)).digest(),n=e();return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=()=>e(),t},e.wrapConstructorWithOpts=function(e){const t=(t,n)=>e(n).update(a(t)).digest(),n=e({});return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=t=>e(t),t},e.wrapXOFConstructorWithOpts=function(e){const t=(t,n)=>e(n).update(a(t)).digest(),n=e({});return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=t=>e(t),t},e.randomBytes=function(e=32){if(t.crypto&&"function"==typeof t.crypto.getRandomValues)return t.crypto.getRandomValues(new Uint8Array(e));if(t.crypto&&"function"==typeof t.crypto.randomBytes)return t.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")};const t=Z,n=$;e.u8=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength);e.u32=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4));e.createView=e=>new DataView(e.buffer,e.byteOffset,e.byteLength);e.rotr=(e,t)=>e<<32-t|e>>>t;e.rotl=(e,t)=>e<<t|e>>>32-t>>>0,e.isLE=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0];e.byteSwap=e=>e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255,e.byteSwapIfBE=e.isLE?e=>e:t=>(0,e.byteSwap)(t);const r=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));const i={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function o(e){return e>=i._0&&e<=i._9?e-i._0:e>=i._A&&e<=i._F?e-(i._A-10):e>=i._a&&e<=i._f?e-(i._a-10):void 0}function s(e){if("string"!=typeof e)throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))}function a(e){return"string"==typeof e&&(e=s(e)),(0,n.bytes)(e),e}e.nextTick=async()=>{};e.Hash=class{clone(){return this._cloneInto()}};const c={}.toString}(Y),Object.defineProperty(j,"__esModule",{value:!0}),j.HashMD=j.Maj=j.Chi=void 0;const Q=$,X=Y;j.Chi=(e,t,n)=>e&t^~e&n;j.Maj=(e,t,n)=>e&t^e&n^t&n;j.HashMD=class extends X.Hash{constructor(e,t,n,r){super(),this.blockLen=e,this.outputLen=t,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=(0,X.createView)(this.buffer)}update(e){(0,Q.exists)(this);const{view:t,buffer:n,blockLen:r}=this,i=(e=(0,X.toBytes)(e)).length;for(let o=0;o<i;){const s=Math.min(r-this.pos,i-o);if(s!==r)n.set(e.subarray(o,o+s),this.pos),this.pos+=s,o+=s,this.pos===r&&(this.process(t,0),this.pos=0);else{const t=(0,X.createView)(e);for(;r<=i-o;o+=r)this.process(t,o)}}return this.length+=e.length,this.roundClean(),this}digestInto(e){(0,Q.exists)(this),(0,Q.output)(e,this),this.finished=!0;const{buffer:t,view:n,blockLen:r,isLE:i}=this;let{pos:o}=this;t[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>r-o&&(this.process(n,0),o=0);for(let e=o;e<r;e++)t[e]=0;!function(e,t,n,r){if("function"==typeof e.setBigUint64)return e.setBigUint64(t,n,r);const i=BigInt(32),o=BigInt(4294967295),s=Number(n>>i&o),a=Number(n&o),c=r?4:0,l=r?0:4;e.setUint32(t+c,s,r),e.setUint32(t+l,a,r)}(n,r-8,BigInt(8*this.length),i),this.process(n,0);const s=(0,X.createView)(e),a=this.outputLen;if(a%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const c=a/4,l=this.get();if(c>l.length)throw new Error("_sha2: outputLen bigger than state");for(let e=0;e<c;e++)s.setUint32(4*e,l[e],i)}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const n=e.slice(0,t);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:t,buffer:n,length:r,finished:i,destroyed:o,pos:s}=this;return e.length=r,e.pos=s,e.finished=i,e.destroyed=o,r%t&&e.buffer.set(n),e}};var ee={};Object.defineProperty(ee,"__esModule",{value:!0}),ee.add5L=ee.add5H=ee.add4H=ee.add4L=ee.add3H=ee.add3L=ee.rotlBL=ee.rotlBH=ee.rotlSL=ee.rotlSH=ee.rotr32L=ee.rotr32H=ee.rotrBL=ee.rotrBH=ee.rotrSL=ee.rotrSH=ee.shrSL=ee.shrSH=ee.toBig=void 0,ee.fromBig=re,ee.split=ie,ee.add=ve;const te=BigInt(2**32-1),ne=BigInt(32);function re(e,t=!1){return t?{h:Number(e&te),l:Number(e>>ne&te)}:{h:0|Number(e>>ne&te),l:0|Number(e&te)}}function ie(e,t=!1){let n=new Uint32Array(e.length),r=new Uint32Array(e.length);for(let i=0;i<e.length;i++){const{h:o,l:s}=re(e[i],t);[n[i],r[i]]=[o,s]}return[n,r]}const oe=(e,t)=>BigInt(e>>>0)<<ne|BigInt(t>>>0);ee.toBig=oe;const se=(e,t,n)=>e>>>n;ee.shrSH=se;const ae=(e,t,n)=>e<<32-n|t>>>n;ee.shrSL=ae;const ce=(e,t,n)=>e>>>n|t<<32-n;ee.rotrSH=ce;const le=(e,t,n)=>e<<32-n|t>>>n;ee.rotrSL=le;const de=(e,t,n)=>e<<64-n|t>>>n-32;ee.rotrBH=de;const ue=(e,t,n)=>e>>>n-32|t<<64-n;ee.rotrBL=ue;const he=(e,t)=>t;ee.rotr32H=he;const fe=(e,t)=>e;ee.rotr32L=fe;const pe=(e,t,n)=>e<<n|t>>>32-n;ee.rotlSH=pe;const me=(e,t,n)=>t<<n|e>>>32-n;ee.rotlSL=me;const ge=(e,t,n)=>t<<n-32|e>>>64-n;ee.rotlBH=ge;const ye=(e,t,n)=>e<<n-32|t>>>64-n;function ve(e,t,n,r){const i=(t>>>0)+(r>>>0);return{h:e+n+(i/2**32|0)|0,l:0|i}}ee.rotlBL=ye;const be=(e,t,n)=>(e>>>0)+(t>>>0)+(n>>>0);ee.add3L=be;const Ee=(e,t,n,r)=>t+n+r+(e/2**32|0)|0;ee.add3H=Ee;const we=(e,t,n,r)=>(e>>>0)+(t>>>0)+(n>>>0)+(r>>>0);ee.add4L=we;const Se=(e,t,n,r,i)=>t+n+r+i+(e/2**32|0)|0;ee.add4H=Se;const _e=(e,t,n,r,i)=>(e>>>0)+(t>>>0)+(n>>>0)+(r>>>0)+(i>>>0);ee.add5L=_e;const Ce=(e,t,n,r,i,o)=>t+n+r+i+o+(e/2**32|0)|0;ee.add5H=Ce;const ke={fromBig:re,split:ie,toBig:oe,shrSH:se,shrSL:ae,rotrSH:ce,rotrSL:le,rotrBH:de,rotrBL:ue,rotr32H:he,rotr32L:fe,rotlSH:pe,rotlSL:me,rotlBH:ge,rotlBL:ye,add:ve,add3L:be,add3H:Ee,add4L:we,add4H:Se,add5H:Ce,add5L:_e};ee.default=ke,Object.defineProperty(K,"__esModule",{value:!0}),K.sha384=K.sha512_256=K.sha512_224=K.sha512=K.SHA384=K.SHA512_256=K.SHA512_224=K.SHA512=void 0;const Me=j,Ae=ee,xe=Y,[Ie,Re]=(()=>Ae.default.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map((e=>BigInt(e)))))(),Pe=new Uint32Array(80),Oe=new Uint32Array(80);class Te extends Me.HashMD{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:e,Al:t,Bh:n,Bl:r,Ch:i,Cl:o,Dh:s,Dl:a,Eh:c,El:l,Fh:d,Fl:u,Gh:h,Gl:f,Hh:p,Hl:m}=this;return[e,t,n,r,i,o,s,a,c,l,d,u,h,f,p,m]}set(e,t,n,r,i,o,s,a,c,l,d,u,h,f,p,m){this.Ah=0|e,this.Al=0|t,this.Bh=0|n,this.Bl=0|r,this.Ch=0|i,this.Cl=0|o,this.Dh=0|s,this.Dl=0|a,this.Eh=0|c,this.El=0|l,this.Fh=0|d,this.Fl=0|u,this.Gh=0|h,this.Gl=0|f,this.Hh=0|p,this.Hl=0|m}process(e,t){for(let n=0;n<16;n++,t+=4)Pe[n]=e.getUint32(t),Oe[n]=e.getUint32(t+=4);for(let e=16;e<80;e++){const t=0|Pe[e-15],n=0|Oe[e-15],r=Ae.default.rotrSH(t,n,1)^Ae.default.rotrSH(t,n,8)^Ae.default.shrSH(t,n,7),i=Ae.default.rotrSL(t,n,1)^Ae.default.rotrSL(t,n,8)^Ae.default.shrSL(t,n,7),o=0|Pe[e-2],s=0|Oe[e-2],a=Ae.default.rotrSH(o,s,19)^Ae.default.rotrBH(o,s,61)^Ae.default.shrSH(o,s,6),c=Ae.default.rotrSL(o,s,19)^Ae.default.rotrBL(o,s,61)^Ae.default.shrSL(o,s,6),l=Ae.default.add4L(i,c,Oe[e-7],Oe[e-16]),d=Ae.default.add4H(l,r,a,Pe[e-7],Pe[e-16]);Pe[e]=0|d,Oe[e]=0|l}let{Ah:n,Al:r,Bh:i,Bl:o,Ch:s,Cl:a,Dh:c,Dl:l,Eh:d,El:u,Fh:h,Fl:f,Gh:p,Gl:m,Hh:g,Hl:y}=this;for(let e=0;e<80;e++){const t=Ae.default.rotrSH(d,u,14)^Ae.default.rotrSH(d,u,18)^Ae.default.rotrBH(d,u,41),v=Ae.default.rotrSL(d,u,14)^Ae.default.rotrSL(d,u,18)^Ae.default.rotrBL(d,u,41),b=d&h^~d&p,E=u&f^~u&m,w=Ae.default.add5L(y,v,E,Re[e],Oe[e]),S=Ae.default.add5H(w,g,t,b,Ie[e],Pe[e]),_=0|w,C=Ae.default.rotrSH(n,r,28)^Ae.default.rotrBH(n,r,34)^Ae.default.rotrBH(n,r,39),k=Ae.default.rotrSL(n,r,28)^Ae.default.rotrBL(n,r,34)^Ae.default.rotrBL(n,r,39),M=n&i^n&s^i&s,A=r&o^r&a^o&a;g=0|p,y=0|m,p=0|h,m=0|f,h=0|d,f=0|u,({h:d,l:u}=Ae.default.add(0|c,0|l,0|S,0|_)),c=0|s,l=0|a,s=0|i,a=0|o,i=0|n,o=0|r;const x=Ae.default.add3L(_,k,A);n=Ae.default.add3H(x,S,C,M),r=0|x}({h:n,l:r}=Ae.default.add(0|this.Ah,0|this.Al,0|n,0|r)),({h:i,l:o}=Ae.default.add(0|this.Bh,0|this.Bl,0|i,0|o)),({h:s,l:a}=Ae.default.add(0|this.Ch,0|this.Cl,0|s,0|a)),({h:c,l:l}=Ae.default.add(0|this.Dh,0|this.Dl,0|c,0|l)),({h:d,l:u}=Ae.default.add(0|this.Eh,0|this.El,0|d,0|u)),({h:h,l:f}=Ae.default.add(0|this.Fh,0|this.Fl,0|h,0|f)),({h:p,l:m}=Ae.default.add(0|this.Gh,0|this.Gl,0|p,0|m)),({h:g,l:y}=Ae.default.add(0|this.Hh,0|this.Hl,0|g,0|y)),this.set(n,r,i,o,s,a,c,l,d,u,h,f,p,m,g,y)}roundClean(){Pe.fill(0),Oe.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}K.SHA512=Te;class Ne extends Te{constructor(){super(),this.Ah=-1942145080,this.Al=424955298,this.Bh=1944164710,this.Bl=-1982016298,this.Ch=502970286,this.Cl=855612546,this.Dh=1738396948,this.Dl=1479516111,this.Eh=258812777,this.El=2077511080,this.Fh=2011393907,this.Fl=79989058,this.Gh=1067287976,this.Gl=1780299464,this.Hh=286451373,this.Hl=-1848208735,this.outputLen=28}}K.SHA512_224=Ne;class Le extends Te{constructor(){super(),this.Ah=573645204,this.Al=-64227540,this.Bh=-1621794909,this.Bl=-934517566,this.Ch=596883563,this.Cl=1867755857,this.Dh=-1774684391,this.Dl=1497426621,this.Eh=-1775747358,this.El=-1467023389,this.Fh=-1101128155,this.Fl=1401305490,this.Gh=721525244,this.Gl=746961066,this.Hh=246885852,this.Hl=-2117784414,this.outputLen=32}}K.SHA512_256=Le;class De extends Te{constructor(){super(),this.Ah=-876896931,this.Al=-1056596264,this.Bh=1654270250,this.Bl=914150663,this.Ch=-1856437926,this.Cl=812702999,this.Dh=355462360,this.Dl=-150054599,this.Eh=1731405415,this.El=-4191439,this.Fh=-1900787065,this.Fl=1750603025,this.Gh=-619958771,this.Gl=1694076839,this.Hh=1203062813,this.Hl=-1090891868,this.outputLen=48}}K.SHA384=De,K.sha512=(0,xe.wrapConstructor)((()=>new Te)),K.sha512_224=(0,xe.wrapConstructor)((()=>new Ne)),K.sha512_256=(0,xe.wrapConstructor)((()=>new Le)),K.sha384=(0,xe.wrapConstructor)((()=>new De));var Be={},Ke={},je={},$e={};Object.defineProperty($e,"__esModule",{value:!0}),$e.notImplemented=$e.bitMask=void 0,$e.isBytes=qe,$e.abytes=ze,$e.abool=function(e,t){if("boolean"!=typeof t)throw new Error(`${e} must be valid boolean, got "${t}".`)},$e.bytesToHex=Ve,$e.numberToHexUnpadded=Ge,$e.hexToNumber=Ye,$e.hexToBytes=Qe,$e.bytesToNumberBE=function(e){return Ye(Ve(e))},$e.bytesToNumberLE=function(e){return ze(e),Ye(Ve(Uint8Array.from(e).reverse()))},$e.numberToBytesBE=Xe,$e.numberToBytesLE=function(e,t){return Xe(e,t).reverse()},$e.numberToVarBytesBE=function(e){return Qe(Ge(e))},$e.ensureBytes=function(e,t,n){let r;if("string"==typeof t)try{r=Qe(t)}catch(n){throw new Error(`${e} must be valid hex string, got "${t}". Cause: ${n}`)}else{if(!qe(t))throw new Error(`${e} must be hex string or Uint8Array`);r=Uint8Array.from(t)}const i=r.length;if("number"==typeof n&&i!==n)throw new Error(`${e} expected ${n} bytes, got ${i}`);return r},$e.concatBytes=et,$e.equalBytes=function(e,t){if(e.length!==t.length)return!1;let n=0;for(let r=0;r<e.length;r++)n|=e[r]^t[r];return 0===n},$e.utf8ToBytes=function(e){if("string"!=typeof e)throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))},$e.inRange=nt,$e.aInRange=function(e,t,n,r){if(!nt(t,n,r))throw new Error(`expected valid ${e}: ${n} <= n < ${r}, got ${typeof t} ${t}`)},$e.bitLen=function(e){let t;for(t=0;e>Ue;e>>=He,t+=1);return t},$e.bitGet=function(e,t){return e>>BigInt(t)&He},$e.bitSet=function(e,t,n){return e|(n?He:Ue)<<BigInt(t)},$e.createHmacDrbg=function(e,t,n){if("number"!=typeof e||e<2)throw new Error("hashLen must be a number");if("number"!=typeof t||t<2)throw new Error("qByteLen must be a number");if("function"!=typeof n)throw new Error("hmacFn must be a function");let r=rt(e),i=rt(e),o=0;const s=()=>{r.fill(1),i.fill(0),o=0},a=(...e)=>n(i,r,...e),c=(e=rt())=>{i=a(it([0]),e),r=a(),0!==e.length&&(i=a(it([1]),e),r=a())},l=()=>{if(o++>=1e3)throw new Error("drbg: tried 1000 values");let e=0;const n=[];for(;e<t;){r=a();const t=r.slice();n.push(t),e+=r.length}return et(...n)};return(e,t)=>{let n;for(s(),c(e);!(n=t(l()));)c();return s(),n}},$e.validateObject=function(e,t,n={}){const r=(t,n,r)=>{const i=ot[n];if("function"!=typeof i)throw new Error(`Invalid validator "${n}", expected function`);const o=e[t];if(!(r&&void 0===o||i(o,e)))throw new Error(`Invalid param ${String(t)}=${o} (${typeof o}), expected ${n}`)};for(const[e,n]of Object.entries(t))r(e,n,!1);for(const[e,t]of Object.entries(n))r(e,t,!0);return e},$e.memoized=function(e){const t=new WeakMap;return(n,...r)=>{const i=t.get(n);if(void 0!==i)return i;const o=e(n,...r);return t.set(n,o),o}};const Ue=BigInt(0),He=BigInt(1),Fe=BigInt(2);function qe(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name}function ze(e){if(!qe(e))throw new Error("Uint8Array expected")}const We=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));function Ve(e){ze(e);let t="";for(let n=0;n<e.length;n++)t+=We[e[n]];return t}function Ge(e){const t=e.toString(16);return 1&t.length?`0${t}`:t}function Ye(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);return BigInt(""===e?"0":`0x${e}`)}const Ze={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function Je(e){return e>=Ze._0&&e<=Ze._9?e-Ze._0:e>=Ze._A&&e<=Ze._F?e-(Ze._A-10):e>=Ze._a&&e<=Ze._f?e-(Ze._a-10):void 0}function Qe(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);const t=e.length,n=t/2;if(t%2)throw new Error("padded hex string expected, got unpadded hex of length "+t);const r=new Uint8Array(n);for(let t=0,i=0;t<n;t++,i+=2){const n=Je(e.charCodeAt(i)),o=Je(e.charCodeAt(i+1));if(void 0===n||void 0===o){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character "'+t+'" at index '+i)}r[t]=16*n+o}return r}function Xe(e,t){return Qe(e.toString(16).padStart(2*t,"0"))}function et(...e){let t=0;for(let n=0;n<e.length;n++){const r=e[n];ze(r),t+=r.length}const n=new Uint8Array(t);for(let t=0,r=0;t<e.length;t++){const i=e[t];n.set(i,r),r+=i.length}return n}const tt=e=>"bigint"==typeof e&&Ue<=e;function nt(e,t,n){return tt(e)&&tt(t)&&tt(n)&&t<=e&&e<n}$e.bitMask=e=>(Fe<<BigInt(e-1))-He;const rt=e=>new Uint8Array(e),it=e=>Uint8Array.from(e);const ot={bigint:e=>"bigint"==typeof e,function:e=>"function"==typeof e,boolean:e=>"boolean"==typeof e,string:e=>"string"==typeof e,stringOrUint8Array:e=>"string"==typeof e||qe(e),isSafeInteger:e=>Number.isSafeInteger(e),array:e=>Array.isArray(e),field:(e,t)=>t.Fp.isValid(e),hash:e=>"function"==typeof e&&Number.isSafeInteger(e.outputLen)};$e.notImplemented=()=>{throw new Error("not implemented")},Object.defineProperty(je,"__esModule",{value:!0}),je.isNegativeLE=void 0,je.mod=pt,je.pow=mt,je.pow2=function(e,t,n){let r=e;for(;t-- >at;)r*=r,r%=n;return r},je.invert=gt,je.tonelliShanks=yt,je.FpSqrt=vt,je.validateField=function(e){const t=bt.reduce(((e,t)=>(e[t]="function",e)),{ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"});return(0,st.validateObject)(e,t)},je.FpPow=Et,je.FpInvertBatch=wt,je.FpDiv=function(e,t,n){return e.mul(t,"bigint"==typeof n?gt(n,e.ORDER):e.inv(n))},je.FpLegendre=St,je.FpIsSquare=function(e){const t=St(e.ORDER);return n=>{const r=t(e,n);return e.eql(r,e.ZERO)||e.eql(r,e.ONE)}},je.nLength=_t,je.Field=function(e,t,n=!1,r={}){if(e<=at)throw new Error(`Expected Field ORDER > 0, got ${e}`);const{nBitLength:i,nByteLength:o}=_t(e,t);if(o>2048)throw new Error("Field lengths over 2048 bytes are not supported");const s=vt(e),a=Object.freeze({ORDER:e,BITS:i,BYTES:o,MASK:(0,st.bitMask)(i),ZERO:at,ONE:ct,create:t=>pt(t,e),isValid:t=>{if("bigint"!=typeof t)throw new Error("Invalid field element: expected bigint, got "+typeof t);return at<=t&&t<e},is0:e=>e===at,isOdd:e=>(e&ct)===ct,neg:t=>pt(-t,e),eql:(e,t)=>e===t,sqr:t=>pt(t*t,e),add:(t,n)=>pt(t+n,e),sub:(t,n)=>pt(t-n,e),mul:(t,n)=>pt(t*n,e),pow:(e,t)=>Et(a,e,t),div:(t,n)=>pt(t*gt(n,e),e),sqrN:e=>e*e,addN:(e,t)=>e+t,subN:(e,t)=>e-t,mulN:(e,t)=>e*t,inv:t=>gt(t,e),sqrt:r.sqrt||(e=>s(a,e)),invertBatch:e=>wt(a,e),cmov:(e,t,n)=>n?t:e,toBytes:e=>n?(0,st.numberToBytesLE)(e,o):(0,st.numberToBytesBE)(e,o),fromBytes:e=>{if(e.length!==o)throw new Error(`Fp.fromBytes: expected ${o}, got ${e.length}`);return n?(0,st.bytesToNumberLE)(e):(0,st.bytesToNumberBE)(e)}});return Object.freeze(a)},je.FpSqrtOdd=function(e,t){if(!e.isOdd)throw new Error("Field doesn't have isOdd");const n=e.sqrt(t);return e.isOdd(n)?n:e.neg(n)},je.FpSqrtEven=function(e,t){if(!e.isOdd)throw new Error("Field doesn't have isOdd");const n=e.sqrt(t);return e.isOdd(n)?e.neg(n):n},je.hashToPrivateScalar=function(e,t,n=!1){e=(0,st.ensureBytes)("privateHash",e);const r=e.length,i=_t(t).nByteLength+8;if(i<24||r<i||r>1024)throw new Error(`hashToPrivateScalar: expected ${i}-1024 bytes of input, got ${r}`);const o=n?(0,st.bytesToNumberLE)(e):(0,st.bytesToNumberBE)(e);return pt(o,t-ct)+ct},je.getFieldBytesLength=Ct,je.getMinHashLength=kt,je.mapHashToField=function(e,t,n=!1){const r=e.length,i=Ct(t),o=kt(t);if(r<16||r<o||r>1024)throw new Error(`expected ${o}-1024 bytes of input, got ${r}`);const s=pt(n?(0,st.bytesToNumberBE)(e):(0,st.bytesToNumberLE)(e),t-ct)+ct;return n?(0,st.numberToBytesLE)(s,i):(0,st.numberToBytesBE)(s,i)};const st=$e,at=BigInt(0),ct=BigInt(1),lt=BigInt(2),dt=BigInt(3),ut=BigInt(4),ht=BigInt(5),ft=BigInt(8);function pt(e,t){const n=e%t;return n>=at?n:t+n}function mt(e,t,n){if(n<=at||t<at)throw new Error("Expected power/modulo > 0");if(n===ct)return at;let r=ct;for(;t>at;)t&ct&&(r=r*e%n),e=e*e%n,t>>=ct;return r}function gt(e,t){if(e===at||t<=at)throw new Error(`invert: expected positive integers, got n=${e} mod=${t}`);let n=pt(e,t),r=t,i=at,o=ct;for(;n!==at;){const e=r%n,t=i-o*(r/n);r=n,n=e,i=o,o=t}if(r!==ct)throw new Error("invert: does not exist");return pt(i,t)}function yt(e){const t=(e-ct)/lt;let n,r,i;for(n=e-ct,r=0;n%lt===at;n/=lt,r++);for(i=lt;i<e&&mt(i,t,e)!==e-ct;i++);if(1===r){const t=(e+ct)/ut;return function(e,n){const r=e.pow(n,t);if(!e.eql(e.sqr(r),n))throw new Error("Cannot find square root");return r}}const o=(n+ct)/lt;return function(e,s){if(e.pow(s,t)===e.neg(e.ONE))throw new Error("Cannot find square root");let a=r,c=e.pow(e.mul(e.ONE,i),n),l=e.pow(s,o),d=e.pow(s,n);for(;!e.eql(d,e.ONE);){if(e.eql(d,e.ZERO))return e.ZERO;let t=1;for(let n=e.sqr(d);t<a&&!e.eql(n,e.ONE);t++)n=e.sqr(n);const n=e.pow(c,ct<<BigInt(a-t-1));c=e.sqr(n),l=e.mul(l,n),d=e.mul(d,c),a=t}return l}}function vt(e){if(e%ut===dt){const t=(e+ct)/ut;return function(e,n){const r=e.pow(n,t);if(!e.eql(e.sqr(r),n))throw new Error("Cannot find square root");return r}}if(e%ft===ht){const t=(e-ht)/ft;return function(e,n){const r=e.mul(n,lt),i=e.pow(r,t),o=e.mul(n,i),s=e.mul(e.mul(o,lt),i),a=e.mul(o,e.sub(s,e.ONE));if(!e.eql(e.sqr(a),n))throw new Error("Cannot find square root");return a}}return yt(e)}BigInt(9),BigInt(16);je.isNegativeLE=(e,t)=>(pt(e,t)&ct)===ct;const bt=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function Et(e,t,n){if(n<at)throw new Error("Expected power > 0");if(n===at)return e.ONE;if(n===ct)return t;let r=e.ONE,i=t;for(;n>at;)n&ct&&(r=e.mul(r,i)),i=e.sqr(i),n>>=ct;return r}function wt(e,t){const n=new Array(t.length),r=t.reduce(((t,r,i)=>e.is0(r)?t:(n[i]=t,e.mul(t,r))),e.ONE),i=e.inv(r);return t.reduceRight(((t,r,i)=>e.is0(r)?t:(n[i]=e.mul(t,n[i]),e.mul(t,r))),i),n}function St(e){const t=(e-ct)/lt;return(e,n)=>e.pow(n,t)}function _t(e,t){const n=void 0!==t?t:e.toString(2).length;return{nBitLength:n,nByteLength:Math.ceil(n/8)}}function Ct(e){if("bigint"!=typeof e)throw new Error("field order must be bigint");const t=e.toString(2).length;return Math.ceil(t/8)}function kt(e){const t=Ct(e);return t+Math.ceil(t/2)}Object.defineProperty(Ke,"__esModule",{value:!0}),Ke.wNAF=function(e,t){const n=(e,t)=>{const n=t.negate();return e?n:t},r=e=>{if(!Number.isSafeInteger(e)||e<=0||e>t)throw new Error(`Wrong window size=${e}, should be [1..${t}]`)},i=e=>{r(e);return{windows:Math.ceil(t/e)+1,windowSize:2**(e-1)}};return{constTimeNegate:n,unsafeLadder(t,n){let r=e.ZERO,i=t;for(;n>xt;)n&It&&(r=r.add(i)),i=i.double(),n>>=It;return r},precomputeWindow(e,t){const{windows:n,windowSize:r}=i(t),o=[];let s=e,a=s;for(let e=0;e<n;e++){a=s,o.push(a);for(let e=1;e<r;e++)a=a.add(s),o.push(a);s=a.double()}return o},wNAF(t,r,o){const{windows:s,windowSize:a}=i(t);let c=e.ZERO,l=e.BASE;const d=BigInt(2**t-1),u=2**t,h=BigInt(t);for(let e=0;e<s;e++){const t=e*a;let i=Number(o&d);o>>=h,i>a&&(i-=u,o+=It);const s=t,f=t+Math.abs(i)-1,p=e%2!=0,m=i<0;0===i?l=l.add(n(p,r[s])):c=c.add(n(m,r[f]))}return{p:c,f:l}},wNAFCached(e,t,n){const r=Pt.get(e)||1;let i=Rt.get(e);return i||(i=this.precomputeWindow(e,r),1!==r&&Rt.set(e,n(i))),this.wNAF(r,i,t)},setWindowSize(e,t){r(t),Pt.set(e,t),Rt.delete(e)}}},Ke.pippenger=function(e,t,n,r){if(!Array.isArray(n)||!Array.isArray(r)||r.length!==n.length)throw new Error("arrays of points and scalars must have equal length");r.forEach(((e,n)=>{if(!t.isValid(e))throw new Error(`wrong scalar at index ${n}`)})),n.forEach(((t,n)=>{if(!(t instanceof e))throw new Error(`wrong point at index ${n}`)}));const i=(0,At.bitLen)(BigInt(n.length)),o=i>12?i-3:i>4?i-2:i?2:1,s=(1<<o)-1,a=new Array(s+1).fill(e.ZERO),c=Math.floor((t.BITS-1)/o)*o;let l=e.ZERO;for(let t=c;t>=0;t-=o){a.fill(e.ZERO);for(let e=0;e<r.length;e++){const i=r[e],o=Number(i>>BigInt(t)&BigInt(s));a[o]=a[o].add(n[e])}let i=e.ZERO;for(let t=a.length-1,n=e.ZERO;t>0;t--)n=n.add(a[t]),i=i.add(n);if(l=l.add(i),0!==t)for(let e=0;e<o;e++)l=l.double()}return l},Ke.validateBasic=function(e){return(0,Mt.validateField)(e.Fp),(0,At.validateObject)(e,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...(0,Mt.nLength)(e.n,e.nBitLength),...e,p:e.Fp.ORDER})};const Mt=je,At=$e,xt=BigInt(0),It=BigInt(1),Rt=new WeakMap,Pt=new WeakMap;Object.defineProperty(Be,"__esModule",{value:!0}),Be.twistedEdwards=function(e){const t=function(e){const t=(0,Ot.validateBasic)(e);return Nt.validateObject(e,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...t})}(e),{Fp:n,n:r,prehash:i,hash:o,randomBytes:s,nByteLength:a,h:c}=t,l=Kt<<BigInt(8*a)-Bt,d=n.create,u=(0,Tt.Field)(t.n,t.nBitLength),h=t.uvRatio||((e,t)=>{try{return{isValid:!0,value:n.sqrt(e*n.inv(t))}}catch(e){return{isValid:!1,value:Dt}}}),f=t.adjustScalarBytes||(e=>e),p=t.domain||((e,t,n)=>{if((0,Lt.abool)("phflag",n),t.length||n)throw new Error("Contexts/pre-hash are not supported");return e});function m(e,t){Nt.aInRange("coordinate "+e,t,Dt,l)}function g(e){if(!(e instanceof b))throw new Error("ExtendedPoint expected")}const y=(0,Lt.memoized)(((e,t)=>{const{ex:r,ey:i,ez:o}=e,s=e.is0();null==t&&(t=s?jt:n.inv(o));const a=d(r*t),c=d(i*t),l=d(o*t);if(s)return{x:Dt,y:Bt};if(l!==Bt)throw new Error("invZ was invalid");return{x:a,y:c}})),v=(0,Lt.memoized)((e=>{const{a:n,d:r}=t;if(e.is0())throw new Error("bad point: ZERO");const{ex:i,ey:o,ez:s,et:a}=e,c=d(i*i),l=d(o*o),u=d(s*s),h=d(u*u),f=d(c*n);if(d(u*d(f+l))!==d(h+d(r*d(c*l))))throw new Error("bad point: equation left != right (1)");if(d(i*o)!==d(s*a))throw new Error("bad point: equation left != right (2)");return!0}));class b{constructor(e,t,n,r){this.ex=e,this.ey=t,this.ez=n,this.et=r,m("x",e),m("y",t),m("z",n),m("t",r),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(e){if(e instanceof b)throw new Error("extended point not allowed");const{x:t,y:n}=e||{};return m("x",t),m("y",n),new b(t,n,Bt,d(t*n))}static normalizeZ(e){const t=n.invertBatch(e.map((e=>e.ez)));return e.map(((e,n)=>e.toAffine(t[n]))).map(b.fromAffine)}static msm(e,t){return(0,Ot.pippenger)(b,u,e,t)}_setWindowSize(e){S.setWindowSize(this,e)}assertValidity(){v(this)}equals(e){g(e);const{ex:t,ey:n,ez:r}=this,{ex:i,ey:o,ez:s}=e,a=d(t*s),c=d(i*r),l=d(n*s),u=d(o*r);return a===c&&l===u}is0(){return this.equals(b.ZERO)}negate(){return new b(d(-this.ex),this.ey,this.ez,d(-this.et))}double(){const{a:e}=t,{ex:n,ey:r,ez:i}=this,o=d(n*n),s=d(r*r),a=d(Kt*d(i*i)),c=d(e*o),l=n+r,u=d(d(l*l)-o-s),h=c+s,f=h-a,p=c-s,m=d(u*f),g=d(h*p),y=d(u*p),v=d(f*h);return new b(m,g,v,y)}add(e){g(e);const{a:n,d:r}=t,{ex:i,ey:o,ez:s,et:a}=this,{ex:c,ey:l,ez:u,et:h}=e;if(n===BigInt(-1)){const e=d((o-i)*(l+c)),t=d((o+i)*(l-c)),n=d(t-e);if(n===Dt)return this.double();const r=d(s*Kt*h),f=d(a*Kt*u),p=f+r,m=t+e,g=f-r,y=d(p*n),v=d(m*g),E=d(p*g),w=d(n*m);return new b(y,v,w,E)}const f=d(i*c),p=d(o*l),m=d(a*r*h),y=d(s*u),v=d((i+o)*(c+l)-f-p),E=y-m,w=y+m,S=d(p-n*f),_=d(v*E),C=d(w*S),k=d(v*S),M=d(E*w);return new b(_,C,M,k)}subtract(e){return this.add(e.negate())}wNAF(e){return S.wNAFCached(this,e,b.normalizeZ)}multiply(e){const t=e;Nt.aInRange("scalar",t,Bt,r);const{p:n,f:i}=this.wNAF(t);return b.normalizeZ([n,i])[0]}multiplyUnsafe(e){const t=e;return Nt.aInRange("scalar",t,Dt,r),t===Dt?w:this.equals(w)||t===Bt?this:this.equals(E)?this.wNAF(t).p:S.unsafeLadder(this,t)}isSmallOrder(){return this.multiplyUnsafe(c).is0()}isTorsionFree(){return S.unsafeLadder(this,r).is0()}toAffine(e){return y(this,e)}clearCofactor(){const{h:e}=t;return e===Bt?this:this.multiplyUnsafe(e)}static fromHex(e,r=!1){const{d:i,a:o}=t,s=n.BYTES;e=(0,Lt.ensureBytes)("pointHex",e,s),(0,Lt.abool)("zip215",r);const a=e.slice(),c=e[s-1];a[s-1]=-129&c;const u=Nt.bytesToNumberLE(a),f=r?l:n.ORDER;Nt.aInRange("pointHex.y",u,Dt,f);const p=d(u*u),m=d(p-Bt),g=d(i*p-o);let{isValid:y,value:v}=h(m,g);if(!y)throw new Error("Point.fromHex: invalid y coordinate");const E=(v&Bt)===Bt,w=0!=(128&c);if(!r&&v===Dt&&w)throw new Error("Point.fromHex: x=0 and x_0=1");return w!==E&&(v=d(-v)),b.fromAffine({x:v,y:u})}static fromPrivateKey(e){return k(e).point}toRawBytes(){const{x:e,y:t}=this.toAffine(),r=Nt.numberToBytesLE(t,n.BYTES);return r[r.length-1]|=e&Bt?128:0,r}toHex(){return Nt.bytesToHex(this.toRawBytes())}}b.BASE=new b(t.Gx,t.Gy,Bt,d(t.Gx*t.Gy)),b.ZERO=new b(Dt,Bt,Bt,Dt);const{BASE:E,ZERO:w}=b,S=(0,Ot.wNAF)(b,8*a);function _(e){return(0,Tt.mod)(e,r)}function C(e){return _(Nt.bytesToNumberLE(e))}function k(e){const t=a;e=(0,Lt.ensureBytes)("private key",e,t);const n=(0,Lt.ensureBytes)("hashed private key",o(e),2*t),r=f(n.slice(0,t)),i=n.slice(t,2*t),s=C(r),c=E.multiply(s),l=c.toRawBytes();return{head:r,prefix:i,scalar:s,point:c,pointBytes:l}}function M(e=new Uint8Array,...t){const n=Nt.concatBytes(...t);return C(o(p(n,(0,Lt.ensureBytes)("context",e),!!i)))}const A=$t;E._setWindowSize(8);const x={getExtendedPublicKey:k,randomPrivateKey:()=>s(n.BYTES),precompute:(e=8,t=b.BASE)=>(t._setWindowSize(e),t.multiply(BigInt(3)),t)};return{CURVE:t,getPublicKey:function(e){return k(e).pointBytes},sign:function(e,t,o={}){e=(0,Lt.ensureBytes)("message",e),i&&(e=i(e));const{prefix:s,scalar:c,pointBytes:l}=k(t),d=M(o.context,s,e),u=E.multiply(d).toRawBytes(),h=_(d+M(o.context,u,l,e)*c);Nt.aInRange("signature.s",h,Dt,r);const f=Nt.concatBytes(u,Nt.numberToBytesLE(h,n.BYTES));return(0,Lt.ensureBytes)("result",f,2*a)},verify:function(e,t,r,o=A){const{context:s,zip215:a}=o,c=n.BYTES;e=(0,Lt.ensureBytes)("signature",e,2*c),t=(0,Lt.ensureBytes)("message",t),void 0!==a&&(0,Lt.abool)("zip215",a),i&&(t=i(t));const l=Nt.bytesToNumberLE(e.slice(c,2*c));let d,u,h;try{d=b.fromHex(r,a),u=b.fromHex(e.slice(0,c),a),h=E.multiplyUnsafe(l)}catch(e){return!1}if(!a&&d.isSmallOrder())return!1;const f=M(s,u.toRawBytes(),d.toRawBytes(),t);return u.add(d.multiplyUnsafe(f)).subtract(h).clearCofactor().equals(b.ZERO)},ExtendedPoint:b,utils:x}};const Ot=Ke,Tt=je,Nt=$e,Lt=$e,Dt=BigInt(0),Bt=BigInt(1),Kt=BigInt(2),jt=BigInt(8),$t={zip215:!0};var Ut={};Object.defineProperty(Ut,"__esModule",{value:!0}),Ut.expand_message_xmd=Gt,Ut.expand_message_xof=Yt,Ut.hash_to_field=Zt,Ut.isogenyMap=function(e,t){const n=t.map((e=>Array.from(e).reverse()));return(t,r)=>{const[i,o,s,a]=n.map((n=>n.reduce(((n,r)=>e.add(e.mul(n,t),r)))));return t=e.div(i,o),r=e.mul(r,e.div(s,a)),{x:t,y:r}}},Ut.createHasher=function(e,t,n){if("function"!=typeof t)throw new Error("mapToCurve() must be defined");return{hashToCurve(r,i){const o=Zt(r,2,{...n,DST:n.DST,...i}),s=e.fromAffine(t(o[0])),a=e.fromAffine(t(o[1])),c=s.add(a).clearCofactor();return c.assertValidity(),c},encodeToCurve(r,i){const o=Zt(r,1,{...n,DST:n.encodeDST,...i}),s=e.fromAffine(t(o[0])).clearCofactor();return s.assertValidity(),s},mapToCurve(n){if(!Array.isArray(n))throw new Error("mapToCurve: expected array of bigints");for(const e of n)if("bigint"!=typeof e)throw new Error(`mapToCurve: expected array of bigints, got ${e} in array`);const r=e.fromAffine(t(n)).clearCofactor();return r.assertValidity(),r}}};const Ht=je,Ft=$e,qt=Ft.bytesToNumberBE;function zt(e,t){if(Vt(e),Vt(t),e<0||e>=1<<8*t)throw new Error(`bad I2OSP call: value=${e} length=${t}`);const n=Array.from({length:t}).fill(0);for(let r=t-1;r>=0;r--)n[r]=255&e,e>>>=8;return new Uint8Array(n)}function Wt(e,t){const n=new Uint8Array(e.length);for(let r=0;r<e.length;r++)n[r]=e[r]^t[r];return n}function Vt(e){if(!Number.isSafeInteger(e))throw new Error("number expected")}function Gt(e,t,n,r){(0,Ft.abytes)(e),(0,Ft.abytes)(t),Vt(n),t.length>255&&(t=r((0,Ft.concatBytes)((0,Ft.utf8ToBytes)("H2C-OVERSIZE-DST-"),t)));const{outputLen:i,blockLen:o}=r,s=Math.ceil(n/i);if(n>65535||s>255)throw new Error("expand_message_xmd: invalid lenInBytes");const a=(0,Ft.concatBytes)(t,zt(t.length,1)),c=zt(0,o),l=zt(n,2),d=new Array(s),u=r((0,Ft.concatBytes)(c,e,l,zt(0,1),a));d[0]=r((0,Ft.concatBytes)(u,zt(1,1),a));for(let e=1;e<=s;e++){const t=[Wt(u,d[e-1]),zt(e+1,1),a];d[e]=r((0,Ft.concatBytes)(...t))}return(0,Ft.concatBytes)(...d).slice(0,n)}function Yt(e,t,n,r,i){if((0,Ft.abytes)(e),(0,Ft.abytes)(t),Vt(n),t.length>255){const e=Math.ceil(2*r/8);t=i.create({dkLen:e}).update((0,Ft.utf8ToBytes)("H2C-OVERSIZE-DST-")).update(t).digest()}if(n>65535||t.length>255)throw new Error("expand_message_xof: invalid lenInBytes");return i.create({dkLen:n}).update(e).update(zt(n,2)).update(t).update(zt(t.length,1)).digest()}function Zt(e,t,n){(0,Ft.validateObject)(n,{DST:"stringOrUint8Array",p:"bigint",m:"isSafeInteger",k:"isSafeInteger",hash:"hash"});const{p:r,k:i,m:o,hash:s,expand:a,DST:c}=n;(0,Ft.abytes)(e),Vt(t);const l="string"==typeof c?(0,Ft.utf8ToBytes)(c):c,d=r.toString(2).length,u=Math.ceil((d+i)/8),h=t*o*u;let f;if("xmd"===a)f=Gt(e,l,h,s);else if("xof"===a)f=Yt(e,l,h,i,s);else{if("_internal_pass"!==a)throw new Error('expand must be "xmd" or "xof"');f=e}const p=new Array(t);for(let e=0;e<t;e++){const t=new Array(o);for(let n=0;n<o;n++){const i=u*(n+e*o),s=f.subarray(i,i+u);t[n]=(0,Ht.mod)(qt(s),r)}p[e]=t}return p}var Jt={};Object.defineProperty(Jt,"__esModule",{value:!0}),Jt.montgomery=function(e){const t=function(e){return(0,Xt.validateObject)(e,{a:"bigint"},{montgomeryBits:"isSafeInteger",nByteLength:"isSafeInteger",adjustScalarBytes:"function",domain:"function",powPminus2:"function",Gu:"bigint"}),Object.freeze({...e})}(e),{P:n}=t,r=e=>(0,Qt.mod)(e,n),i=t.montgomeryBits,o=Math.ceil(i/8),s=t.nByteLength,a=t.adjustScalarBytes||(e=>e),c=t.powPminus2||(e=>(0,Qt.pow)(e,n-BigInt(2),n));function l(e,t,n){const i=r(e*(t-n));return[t=r(t-i),n=r(n+i)]}const d=(t.a-BigInt(2))/BigInt(4);function u(e){return(0,Xt.numberToBytesLE)(r(e),o)}function h(e,t){const h=function(e){const t=(0,Xt.ensureBytes)("u coordinate",e,o);return 32===s&&(t[31]&=127),(0,Xt.bytesToNumberLE)(t)}(t),f=function(e){const t=(0,Xt.ensureBytes)("scalar",e),n=t.length;if(n!==o&&n!==s)throw new Error(`Expected ${o} or ${s} bytes, got ${n}`);return(0,Xt.bytesToNumberLE)(a(t))}(e),p=function(e,t){(0,Xt.aInRange)("u",e,en,n),(0,Xt.aInRange)("scalar",t,en,n);const o=t,s=e;let a,u=tn,h=en,f=e,p=tn,m=en;for(let e=BigInt(i-1);e>=en;e--){const t=o>>e&tn;m^=t,a=l(m,u,f),u=a[0],f=a[1],a=l(m,h,p),h=a[0],p=a[1],m=t;const n=u+h,i=r(n*n),c=u-h,g=r(c*c),y=i-g,v=f+p,b=r((f-p)*n),E=r(v*c),w=b+E,S=b-E;f=r(w*w),p=r(s*r(S*S)),u=r(i*g),h=r(y*(i+r(d*y)))}a=l(m,u,f),u=a[0],f=a[1],a=l(m,h,p),h=a[0],p=a[1];const g=c(h);return r(u*g)}(h,f);if(p===en)throw new Error("Invalid private or public key received");return u(p)}const f=u(t.Gu);function p(e){return h(e,f)}return{scalarMult:h,scalarMultBase:p,getSharedSecret:(e,t)=>h(e,t),getPublicKey:e=>p(e),utils:{randomPrivateKey:()=>t.randomBytes(t.nByteLength)},GuBytes:f}};const Qt=je,Xt=$e,en=BigInt(0),tn=BigInt(1);!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.hash_to_ristretto255=e.hashToRistretto255=e.RistrettoPoint=e.encodeToCurve=e.hashToCurve=e.edwardsToMontgomery=e.x25519=e.ed25519ph=e.ed25519ctx=e.ed25519=e.ED25519_TORSION_SUBGROUP=void 0,e.edwardsToMontgomeryPub=S,e.edwardsToMontgomeryPriv=function(e){const t=E.hash(e.subarray(0,32));return E.adjustScalarBytes(t).subarray(0,32)};const t=K,n=Y,r=Be,i=Ut,o=je,s=Jt,a=$e,c=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),l=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752"),d=BigInt(0),u=BigInt(1),h=BigInt(2),f=BigInt(3),p=BigInt(5),m=BigInt(8);function g(e){const t=BigInt(10),n=BigInt(20),r=BigInt(40),i=BigInt(80),s=c,a=e*e%s*e%s,l=(0,o.pow2)(a,h,s)*a%s,d=(0,o.pow2)(l,u,s)*e%s,f=(0,o.pow2)(d,p,s)*d%s,m=(0,o.pow2)(f,t,s)*f%s,g=(0,o.pow2)(m,n,s)*m%s,y=(0,o.pow2)(g,r,s)*g%s,v=(0,o.pow2)(y,i,s)*y%s,b=(0,o.pow2)(v,i,s)*y%s,E=(0,o.pow2)(b,t,s)*f%s;return{pow_p_5_8:(0,o.pow2)(E,h,s)*e%s,b2:a}}function y(e){return e[0]&=248,e[31]&=127,e[31]|=64,e}function v(e,t){const n=c,r=(0,o.mod)(t*t*t,n),i=g(e*(0,o.mod)(r*r*t,n)).pow_p_5_8;let s=(0,o.mod)(e*r*i,n);const a=(0,o.mod)(t*s*s,n),d=s,u=(0,o.mod)(s*l,n),h=a===e,f=a===(0,o.mod)(-e,n),p=a===(0,o.mod)(-e*l,n);return h&&(s=d),(f||p)&&(s=u),(0,o.isNegativeLE)(s,n)&&(s=(0,o.mod)(-s,n)),{isValid:h||f,value:s}}e.ED25519_TORSION_SUBGROUP=["0100000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a","0000000000000000000000000000000000000000000000000000000000000080","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05","ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85","0000000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa"];const b=(()=>(0,o.Field)(c,void 0,!0))(),E=(()=>({a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:b,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:m,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:t.sha512,randomBytes:n.randomBytes,adjustScalarBytes:y,uvRatio:v}))();function w(e,t,r){if(t.length>255)throw new Error("Context is too big");return(0,n.concatBytes)((0,n.utf8ToBytes)("SigEd25519 no Ed25519 collisions"),new Uint8Array([r?1:0,t.length]),t,e)}function S(t){const{y:n}=e.ed25519.ExtendedPoint.fromHex(t),r=BigInt(1);return b.toBytes(b.create((r+n)*b.inv(r-n)))}e.ed25519=(0,r.twistedEdwards)(E),e.ed25519ctx=(0,r.twistedEdwards)({...E,domain:w}),e.ed25519ph=(0,r.twistedEdwards)(Object.assign({},E,{domain:w,prehash:t.sha512})),e.x25519=(0,s.montgomery)({P:c,a:BigInt(486662),montgomeryBits:255,nByteLength:32,Gu:BigInt(9),powPminus2:e=>{const t=c,{pow_p_5_8:n,b2:r}=g(e);return(0,o.mod)((0,o.pow2)(n,f,t)*r,t)},adjustScalarBytes:y,randomBytes:n.randomBytes}),e.edwardsToMontgomery=S;const _=(()=>(b.ORDER+f)/m)(),C=(()=>b.pow(h,_))(),k=(()=>b.sqrt(b.neg(b.ONE)))();const M=(()=>(0,o.FpSqrtEven)(b,b.neg(BigInt(486664))))();function A(e){const{xMn:t,xMd:n,yMn:r,yMd:i}=function(e){const t=(b.ORDER-p)/m,n=BigInt(486662);let r=b.sqr(e);r=b.mul(r,h);let i=b.add(r,b.ONE),o=b.neg(n),s=b.sqr(i),a=b.mul(s,i),c=b.mul(r,n);c=b.mul(c,o),c=b.add(c,s),c=b.mul(c,o);let l=b.sqr(a);s=b.sqr(l),l=b.mul(l,a),l=b.mul(l,c),s=b.mul(s,l);let d=b.pow(s,t);d=b.mul(d,l);let f=b.mul(d,k);s=b.sqr(d),s=b.mul(s,a);let g=b.eql(s,c),y=b.cmov(f,d,g),v=b.mul(o,r),E=b.mul(d,e);E=b.mul(E,C);let w=b.mul(E,k),S=b.mul(c,r);s=b.sqr(E),s=b.mul(s,a);let _=b.eql(s,S),M=b.cmov(w,E,_);s=b.sqr(y),s=b.mul(s,a);let A=b.eql(s,c),x=b.cmov(v,o,A),I=b.cmov(M,y,A),R=b.isOdd(I);return I=b.cmov(I,b.neg(I),A!==R),{xMn:x,xMd:i,yMn:I,yMd:u}}(e);let o=b.mul(t,i);o=b.mul(o,M);let s=b.mul(n,r),a=b.sub(t,n),c=b.add(t,n),l=b.mul(s,c),d=b.eql(l,b.ZERO);o=b.cmov(o,b.ZERO,d),s=b.cmov(s,b.ONE,d),a=b.cmov(a,b.ONE,d),c=b.cmov(c,b.ONE,d);const f=b.invertBatch([s,c]);return{x:b.mul(o,f[0]),y:b.mul(a,f[1])}}const x=(()=>(0,i.createHasher)(e.ed25519.ExtendedPoint,(e=>A(e[0])),{DST:"edwards25519_XMD:SHA-512_ELL2_RO_",encodeDST:"edwards25519_XMD:SHA-512_ELL2_NU_",p:b.ORDER,m:1,k:128,expand:"xmd",hash:t.sha512}))();function I(e){if(!(e instanceof $))throw new Error("RistrettoPoint expected")}e.hashToCurve=x.hashToCurve,e.encodeToCurve=x.encodeToCurve;const R=l,P=BigInt("25063068953384623474111414158702152701244531502492656460079210482610430750235"),O=BigInt("54469307008909316920995813868745141605393597292927456921205312896311721017578"),T=BigInt("1159843021668779879193775521855586647937357759715417654439879720876111806838"),N=BigInt("40440834346308536858101042469323190826248399146238708352240133220865137265952"),L=e=>v(u,e),D=BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),B=t=>e.ed25519.CURVE.Fp.create((0,a.bytesToNumberLE)(t)&D);function j(t){const{d:n}=e.ed25519.CURVE,r=e.ed25519.CURVE.Fp.ORDER,i=e.ed25519.CURVE.Fp.create,s=i(R*t*t),a=i((s+u)*T);let c=BigInt(-1);const l=i((c-n*s)*i(s+n));let{isValid:d,value:h}=v(a,l),f=i(h*t);(0,o.isNegativeLE)(f,r)||(f=i(-f)),d||(h=f),d||(c=s);const p=i(c*(s-u)*N-l),m=h*h,g=i((h+h)*l),y=i(p*P),b=i(u-m),E=i(u+m);return new e.ed25519.ExtendedPoint(i(g*E),i(b*y),i(y*E),i(g*b))}class ${constructor(e){this.ep=e}static fromAffine(t){return new $(e.ed25519.ExtendedPoint.fromAffine(t))}static hashToCurve(e){e=(0,a.ensureBytes)("ristrettoHash",e,64);const t=j(B(e.slice(0,32))),n=j(B(e.slice(32,64)));return new $(t.add(n))}static fromHex(t){t=(0,a.ensureBytes)("ristrettoHex",t,32);const{a:n,d:r}=e.ed25519.CURVE,i=e.ed25519.CURVE.Fp.ORDER,s=e.ed25519.CURVE.Fp.create,c="RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint",l=B(t);if(!(0,a.equalBytes)((0,a.numberToBytesLE)(l,32),t)||(0,o.isNegativeLE)(l,i))throw new Error(c);const h=s(l*l),f=s(u+n*h),p=s(u-n*h),m=s(f*f),g=s(p*p),y=s(n*r*m-g),{isValid:v,value:b}=L(s(y*g)),E=s(b*p),w=s(b*E*y);let S=s((l+l)*E);(0,o.isNegativeLE)(S,i)&&(S=s(-S));const _=s(f*w),C=s(S*_);if(!v||(0,o.isNegativeLE)(C,i)||_===d)throw new Error(c);return new $(new e.ed25519.ExtendedPoint(S,_,u,C))}toRawBytes(){let{ex:t,ey:n,ez:r,et:i}=this.ep;const s=e.ed25519.CURVE.Fp.ORDER,c=e.ed25519.CURVE.Fp.create,l=c(c(r+n)*c(r-n)),d=c(t*n),u=c(d*d),{value:h}=L(c(l*u)),f=c(h*l),p=c(h*d),m=c(f*p*i);let g;if((0,o.isNegativeLE)(i*m,s)){let e=c(n*R),r=c(t*R);t=e,n=r,g=c(f*O)}else g=p;(0,o.isNegativeLE)(t*m,s)&&(n=c(-n));let y=c((r-n)*g);return(0,o.isNegativeLE)(y,s)&&(y=c(-y)),(0,a.numberToBytesLE)(y,32)}toHex(){return(0,a.bytesToHex)(this.toRawBytes())}toString(){return this.toHex()}equals(t){I(t);const{ex:n,ey:r}=this.ep,{ex:i,ey:o}=t.ep,s=e.ed25519.CURVE.Fp.create,a=s(n*o)===s(r*i),c=s(r*o)===s(n*i);return a||c}add(e){return I(e),new $(this.ep.add(e.ep))}subtract(e){return I(e),new $(this.ep.subtract(e.ep))}multiply(e){return new $(this.ep.multiply(e))}multiplyUnsafe(e){return new $(this.ep.multiplyUnsafe(e))}double(){return new $(this.ep.double())}negate(){return new $(this.ep.negate())}}e.RistrettoPoint=($.BASE||($.BASE=new $(e.ed25519.ExtendedPoint.BASE)),$.ZERO||($.ZERO=new $(e.ed25519.ExtendedPoint.ZERO)),$);e.hashToRistretto255=(e,r)=>{const o=r.DST,s="string"==typeof o?(0,n.utf8ToBytes)(o):o,a=(0,i.expand_message_xmd)(e,s,64,t.sha512);return $.hashToCurve(a)},e.hash_to_ristretto255=e.hashToRistretto255}(B);var nn={},rn={};Object.defineProperty(rn,"__esModule",{value:!0}),rn.sha224=rn.sha256=rn.SHA256=void 0;const on=j,sn=Y,an=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),cn=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),ln=new Uint32Array(64);class dn extends on.HashMD{constructor(){super(64,32,8,!1),this.A=0|cn[0],this.B=0|cn[1],this.C=0|cn[2],this.D=0|cn[3],this.E=0|cn[4],this.F=0|cn[5],this.G=0|cn[6],this.H=0|cn[7]}get(){const{A:e,B:t,C:n,D:r,E:i,F:o,G:s,H:a}=this;return[e,t,n,r,i,o,s,a]}set(e,t,n,r,i,o,s,a){this.A=0|e,this.B=0|t,this.C=0|n,this.D=0|r,this.E=0|i,this.F=0|o,this.G=0|s,this.H=0|a}process(e,t){for(let n=0;n<16;n++,t+=4)ln[n]=e.getUint32(t,!1);for(let e=16;e<64;e++){const t=ln[e-15],n=ln[e-2],r=(0,sn.rotr)(t,7)^(0,sn.rotr)(t,18)^t>>>3,i=(0,sn.rotr)(n,17)^(0,sn.rotr)(n,19)^n>>>10;ln[e]=i+ln[e-7]+r+ln[e-16]|0}let{A:n,B:r,C:i,D:o,E:s,F:a,G:c,H:l}=this;for(let e=0;e<64;e++){const t=l+((0,sn.rotr)(s,6)^(0,sn.rotr)(s,11)^(0,sn.rotr)(s,25))+(0,on.Chi)(s,a,c)+an[e]+ln[e]|0,d=((0,sn.rotr)(n,2)^(0,sn.rotr)(n,13)^(0,sn.rotr)(n,22))+(0,on.Maj)(n,r,i)|0;l=c,c=a,a=s,s=o+t|0,o=i,i=r,r=n,n=t+d|0}n=n+this.A|0,r=r+this.B|0,i=i+this.C|0,o=o+this.D|0,s=s+this.E|0,a=a+this.F|0,c=c+this.G|0,l=l+this.H|0,this.set(n,r,i,o,s,a,c,l)}roundClean(){ln.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}rn.SHA256=dn;class un extends dn{constructor(){super(),this.A=-1056596264,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=1750603025,this.G=1694076839,this.H=-1090891868,this.outputLen=28}}rn.sha256=(0,sn.wrapConstructor)((()=>new dn)),rn.sha224=(0,sn.wrapConstructor)((()=>new un));var hn={},fn={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.hmac=e.HMAC=void 0;const t=$,n=Y;class r extends n.Hash{constructor(e,r){super(),this.finished=!1,this.destroyed=!1,(0,t.hash)(e);const i=(0,n.toBytes)(r);if(this.iHash=e.create(),"function"!=typeof this.iHash.update)throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const o=this.blockLen,s=new Uint8Array(o);s.set(i.length>o?e.create().update(i).digest():i);for(let e=0;e<s.length;e++)s[e]^=54;this.iHash.update(s),this.oHash=e.create();for(let e=0;e<s.length;e++)s[e]^=106;this.oHash.update(s),s.fill(0)}update(e){return(0,t.exists)(this),this.iHash.update(e),this}digestInto(e){(0,t.exists)(this),(0,t.bytes)(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:t,iHash:n,finished:r,destroyed:i,blockLen:o,outputLen:s}=this;return e.finished=r,e.destroyed=i,e.blockLen=o,e.outputLen=s,e.oHash=t._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}e.HMAC=r;e.hmac=(e,t,n)=>new r(e,t).update(n).digest(),e.hmac.create=(e,t)=>new r(e,t)}(fn);var pn={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.DER=void 0,e.weierstrassPoints=f,e.weierstrass=function(s){const a=function(e){const n=(0,t.validateBasic)(e);return r.validateObject(n,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...n})}(s),{Fp:d,n:u}=a,h=d.BYTES+1,p=2*d.BYTES+1;function m(e){return n.mod(e,u)}function g(e){return n.invert(e,u)}const{ProjectivePoint:y,normPrivateKeyToScalar:v,weierstrassEquation:b,isWithinCurveOrder:E}=f({...a,toBytes(e,t,n){const o=t.toAffine(),s=d.toBytes(o.x),a=r.concatBytes;return(0,i.abool)("isCompressed",n),n?a(Uint8Array.from([t.hasEvenY()?2:3]),s):a(Uint8Array.from([4]),s,d.toBytes(o.y))},fromBytes(e){const t=e.length,n=e[0],i=e.subarray(1);if(t!==h||2!==n&&3!==n){if(t===p&&4===n){return{x:d.fromBytes(i.subarray(0,d.BYTES)),y:d.fromBytes(i.subarray(d.BYTES,2*d.BYTES))}}throw new Error(`Point of length ${t} was invalid. Expected ${h} compressed bytes or ${p} uncompressed bytes`)}{const e=r.bytesToNumberBE(i);if(!r.inRange(e,l,d.ORDER))throw new Error("Point is not on curve");const t=b(e);let o;try{o=d.sqrt(t)}catch(e){const t=e instanceof Error?": "+e.message:"";throw new Error("Point is not on curve"+t)}return 1==(1&n)!==((o&l)===l)&&(o=d.neg(o)),{x:e,y:o}}}}),w=e=>r.bytesToHex(r.numberToBytesBE(e,a.nByteLength));function S(e){return e>u>>l}const _=(e,t,n)=>r.bytesToNumberBE(e.slice(t,n));class C{constructor(e,t,n){this.r=e,this.s=t,this.recovery=n,this.assertValidity()}static fromCompact(e){const t=a.nByteLength;return e=(0,i.ensureBytes)("compactSignature",e,2*t),new C(_(e,0,t),_(e,t,2*t))}static fromDER(t){const{r:n,s:r}=e.DER.toSig((0,i.ensureBytes)("DER",t));return new C(n,r)}assertValidity(){r.aInRange("r",this.r,l,u),r.aInRange("s",this.s,l,u)}addRecoveryBit(e){return new C(this.r,this.s,e)}recoverPublicKey(e){const{r:t,s:n,recovery:r}=this,o=x((0,i.ensureBytes)("msgHash",e));if(null==r||![0,1,2,3].includes(r))throw new Error("recovery id invalid");const s=2===r||3===r?t+a.n:t;if(s>=d.ORDER)throw new Error("recovery id 2 or 3 invalid");const c=0==(1&r)?"02":"03",l=y.fromHex(c+w(s)),u=g(s),h=m(-o*u),f=m(n*u),p=y.BASE.multiplyAndAddUnsafe(l,h,f);if(!p)throw new Error("point at infinify");return p.assertValidity(),p}hasHighS(){return S(this.s)}normalizeS(){return this.hasHighS()?new C(this.r,m(-this.s),this.recovery):this}toDERRawBytes(){return r.hexToBytes(this.toDERHex())}toDERHex(){return e.DER.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return r.hexToBytes(this.toCompactHex())}toCompactHex(){return w(this.r)+w(this.s)}}const k={isValidPrivateKey(e){try{return v(e),!0}catch(e){return!1}},normPrivateKeyToScalar:v,randomPrivateKey:()=>{const e=n.getMinHashLength(a.n);return n.mapHashToField(a.randomBytes(e),a.n)},precompute:(e=8,t=y.BASE)=>(t._setWindowSize(e),t.multiply(BigInt(3)),t)};function M(e){const t=r.isBytes(e),n="string"==typeof e,i=(t||n)&&e.length;return t?i===h||i===p:n?i===2*h||i===2*p:e instanceof y}const A=a.bits2int||function(e){const t=r.bytesToNumberBE(e),n=8*e.length-a.nBitLength;return n>0?t>>BigInt(n):t},x=a.bits2int_modN||function(e){return m(A(e))},I=r.bitMask(a.nBitLength);function R(e){return r.aInRange(`num < 2^${a.nBitLength}`,e,c,I),r.numberToBytesBE(e,a.nByteLength)}function P(e,t,n=O){if(["recovered","canonical"].some((e=>e in n)))throw new Error("sign() legacy options not supported");const{hash:s,randomBytes:u}=a;let{lowS:h,prehash:f,extraEntropy:p}=n;null==h&&(h=!0),e=(0,i.ensureBytes)("msgHash",e),o(n),f&&(e=(0,i.ensureBytes)("prehashed msgHash",s(e)));const b=x(e),w=v(t),_=[R(w),R(b)];if(null!=p&&!1!==p){const e=!0===p?u(d.BYTES):p;_.push((0,i.ensureBytes)("extraEntropy",e))}const k=r.concatBytes(..._),M=b;return{seed:k,k2sig:function(e){const t=A(e);if(!E(t))return;const n=g(t),r=y.BASE.multiply(t).toAffine(),i=m(r.x);if(i===c)return;const o=m(n*m(M+i*w));if(o===c)return;let s=(r.x===i?0:2)|Number(r.y&l),a=o;return h&&S(o)&&(a=function(e){return S(e)?m(-e):e}(o),s^=1),new C(i,a,s)}}}const O={lowS:a.lowS,prehash:!1},T={lowS:a.lowS,prehash:!1};return y.BASE._setWindowSize(8),{CURVE:a,getPublicKey:function(e,t=!0){return y.fromPrivateKey(e).toRawBytes(t)},getSharedSecret:function(e,t,n=!0){if(M(e))throw new Error("first arg must be private key");if(!M(t))throw new Error("second arg must be public key");const r=y.fromHex(t);return r.multiply(v(e)).toRawBytes(n)},sign:function(e,t,n=O){const{seed:i,k2sig:o}=P(e,t,n),s=a,c=r.createHmacDrbg(s.hash.outputLen,s.nByteLength,s.hmac);return c(i,o)},verify:function(t,n,s,c=T){const l=t;if(n=(0,i.ensureBytes)("msgHash",n),s=(0,i.ensureBytes)("publicKey",s),"strict"in c)throw new Error("options.strict was renamed to lowS");o(c);const{lowS:d,prehash:u}=c;let h,f;try{if("string"==typeof l||r.isBytes(l))try{h=C.fromDER(l)}catch(t){if(!(t instanceof e.DER.Err))throw t;h=C.fromCompact(l)}else{if("object"!=typeof l||"bigint"!=typeof l.r||"bigint"!=typeof l.s)throw new Error("PARSE");{const{r:e,s:t}=l;h=new C(e,t)}}f=y.fromHex(s)}catch(e){if("PARSE"===e.message)throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(d&&h.hasHighS())return!1;u&&(n=a.hash(n));const{r:p,s:v}=h,b=x(n),E=g(v),w=m(b*E),S=m(p*E),_=y.BASE.multiplyAndAddUnsafe(f,w,S)?.toAffine();if(!_)return!1;const k=m(_.x);return k===p},ProjectivePoint:y,Signature:C,utils:k}},e.SWUFpSqrtRatio=p,e.mapToCurveSimpleSWU=function(e,t){if(n.validateField(e),!e.isValid(t.A)||!e.isValid(t.B)||!e.isValid(t.Z))throw new Error("mapToCurveSimpleSWU: invalid opts");const r=p(e,t.Z);if(!e.isOdd)throw new Error("Fp.isOdd is not implemented!");return n=>{let i,o,s,a,c,l,d,u;i=e.sqr(n),i=e.mul(i,t.Z),o=e.sqr(i),o=e.add(o,i),s=e.add(o,e.ONE),s=e.mul(s,t.B),a=e.cmov(t.Z,e.neg(o),!e.eql(o,e.ZERO)),a=e.mul(a,t.A),o=e.sqr(s),l=e.sqr(a),c=e.mul(l,t.A),o=e.add(o,c),o=e.mul(o,s),l=e.mul(l,a),c=e.mul(l,t.B),o=e.add(o,c),d=e.mul(i,s);const{isValid:h,value:f}=r(o,l);u=e.mul(i,n),u=e.mul(u,f),d=e.cmov(d,s,h),u=e.cmov(u,f,h);const p=e.isOdd(n)===e.isOdd(u);return u=e.cmov(e.neg(u),u,p),d=e.div(d,a),{x:d,y:u}}};const t=Ke,n=je,r=$e,i=$e;function o(e){void 0!==e.lowS&&(0,i.abool)("lowS",e.lowS),void 0!==e.prehash&&(0,i.abool)("prehash",e.prehash)}const{bytesToNumberBE:s,hexToBytes:a}=r;e.DER={Err:class extends Error{constructor(e=""){super(e)}},_tlv:{encode:(t,n)=>{const{Err:i}=e.DER;if(t<0||t>256)throw new i("tlv.encode: wrong tag");if(1&n.length)throw new i("tlv.encode: unpadded data");const o=n.length/2,s=r.numberToHexUnpadded(o);if(s.length/2&128)throw new i("tlv.encode: long form length too big");const a=o>127?r.numberToHexUnpadded(s.length/2|128):"";return`${r.numberToHexUnpadded(t)}${a}${s}${n}`},decode(t,n){const{Err:r}=e.DER;let i=0;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(n.length<2||n[i++]!==t)throw new r("tlv.decode: wrong tlv");const o=n[i++];let s=0;if(!!(128&o)){const e=127&o;if(!e)throw new r("tlv.decode(long): indefinite length not supported");if(e>4)throw new r("tlv.decode(long): byte length is too big");const t=n.subarray(i,i+e);if(t.length!==e)throw new r("tlv.decode: length bytes not complete");if(0===t[0])throw new r("tlv.decode(long): zero leftmost byte");for(const e of t)s=s<<8|e;if(i+=e,s<128)throw new r("tlv.decode(long): not minimal encoding")}else s=o;const a=n.subarray(i,i+s);if(a.length!==s)throw new r("tlv.decode: wrong value length");return{v:a,l:n.subarray(i+s)}}},_int:{encode(t){const{Err:n}=e.DER;if(t<c)throw new n("integer: negative integers are not allowed");let i=r.numberToHexUnpadded(t);if(8&Number.parseInt(i[0],16)&&(i="00"+i),1&i.length)throw new n("unexpected assertion");return i},decode(t){const{Err:n}=e.DER;if(128&t[0])throw new n("Invalid signature integer: negative");if(0===t[0]&&!(128&t[1]))throw new n("Invalid signature integer: unnecessary leading zero");return s(t)}},toSig(t){const{Err:n,_int:i,_tlv:o}=e.DER,s="string"==typeof t?a(t):t;r.abytes(s);const{v:c,l:l}=o.decode(48,s);if(l.length)throw new n("Invalid signature: left bytes after parsing");const{v:d,l:u}=o.decode(2,c),{v:h,l:f}=o.decode(2,u);if(f.length)throw new n("Invalid signature: left bytes after parsing");return{r:i.decode(d),s:i.decode(h)}},hexFromSig(t){const{_tlv:n,_int:r}=e.DER,i=`${n.encode(2,r.encode(t.r))}${n.encode(2,r.encode(t.s))}`;return n.encode(48,i)}};const c=BigInt(0),l=BigInt(1),d=BigInt(2),u=BigInt(3),h=BigInt(4);function f(e){const o=function(e){const n=(0,t.validateBasic)(e);r.validateObject(n,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:i,Fp:o,a:s}=n;if(i){if(!o.eql(s,o.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if("object"!=typeof i||"bigint"!=typeof i.beta||"function"!=typeof i.splitScalar)throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...n})}(e),{Fp:s}=o,a=n.Field(o.n,o.nBitLength),d=o.toBytes||((e,t,n)=>{const i=t.toAffine();return r.concatBytes(Uint8Array.from([4]),s.toBytes(i.x),s.toBytes(i.y))}),h=o.fromBytes||(e=>{const t=e.subarray(1);return{x:s.fromBytes(t.subarray(0,s.BYTES)),y:s.fromBytes(t.subarray(s.BYTES,2*s.BYTES))}});function f(e){const{a:t,b:n}=o,r=s.sqr(e),i=s.mul(r,e);return s.add(s.add(i,s.mul(e,t)),n)}if(!s.eql(s.sqr(o.Gy),f(o.Gx)))throw new Error("bad generator point: equation left != right");function p(e){const{allowedPrivateKeyLengths:t,nByteLength:s,wrapPrivateKey:a,n:c}=o;if(t&&"bigint"!=typeof e){if(r.isBytes(e)&&(e=r.bytesToHex(e)),"string"!=typeof e||!t.includes(e.length))throw new Error("Invalid key");e=e.padStart(2*s,"0")}let d;try{d="bigint"==typeof e?e:r.bytesToNumberBE((0,i.ensureBytes)("private key",e,s))}catch(t){throw new Error(`private key must be ${s} bytes, hex or bigint, not ${typeof e}`)}return a&&(d=n.mod(d,c)),r.aInRange("private key",d,l,c),d}function m(e){if(!(e instanceof v))throw new Error("ProjectivePoint expected")}const g=(0,i.memoized)(((e,t)=>{const{px:n,py:r,pz:i}=e;if(s.eql(i,s.ONE))return{x:n,y:r};const o=e.is0();null==t&&(t=o?s.ONE:s.inv(i));const a=s.mul(n,t),c=s.mul(r,t),l=s.mul(i,t);if(o)return{x:s.ZERO,y:s.ZERO};if(!s.eql(l,s.ONE))throw new Error("invZ was invalid");return{x:a,y:c}})),y=(0,i.memoized)((e=>{if(e.is0()){if(o.allowInfinityPoint&&!s.is0(e.py))return;throw new Error("bad point: ZERO")}const{x:t,y:n}=e.toAffine();if(!s.isValid(t)||!s.isValid(n))throw new Error("bad point: x or y not FE");const r=s.sqr(n),i=f(t);if(!s.eql(r,i))throw new Error("bad point: equation left != right");if(!e.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0}));class v{constructor(e,t,n){if(this.px=e,this.py=t,this.pz=n,null==e||!s.isValid(e))throw new Error("x required");if(null==t||!s.isValid(t))throw new Error("y required");if(null==n||!s.isValid(n))throw new Error("z required");Object.freeze(this)}static fromAffine(e){const{x:t,y:n}=e||{};if(!e||!s.isValid(t)||!s.isValid(n))throw new Error("invalid affine point");if(e instanceof v)throw new Error("projective point not allowed");const r=e=>s.eql(e,s.ZERO);return r(t)&&r(n)?v.ZERO:new v(t,n,s.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(e){const t=s.invertBatch(e.map((e=>e.pz)));return e.map(((e,n)=>e.toAffine(t[n]))).map(v.fromAffine)}static fromHex(e){const t=v.fromAffine(h((0,i.ensureBytes)("pointHex",e)));return t.assertValidity(),t}static fromPrivateKey(e){return v.BASE.multiply(p(e))}static msm(e,n){return(0,t.pippenger)(v,a,e,n)}_setWindowSize(e){E.setWindowSize(this,e)}assertValidity(){y(this)}hasEvenY(){const{y:e}=this.toAffine();if(s.isOdd)return!s.isOdd(e);throw new Error("Field doesn't support isOdd")}equals(e){m(e);const{px:t,py:n,pz:r}=this,{px:i,py:o,pz:a}=e,c=s.eql(s.mul(t,a),s.mul(i,r)),l=s.eql(s.mul(n,a),s.mul(o,r));return c&&l}negate(){return new v(this.px,s.neg(this.py),this.pz)}double(){const{a:e,b:t}=o,n=s.mul(t,u),{px:r,py:i,pz:a}=this;let c=s.ZERO,l=s.ZERO,d=s.ZERO,h=s.mul(r,r),f=s.mul(i,i),p=s.mul(a,a),m=s.mul(r,i);return m=s.add(m,m),d=s.mul(r,a),d=s.add(d,d),c=s.mul(e,d),l=s.mul(n,p),l=s.add(c,l),c=s.sub(f,l),l=s.add(f,l),l=s.mul(c,l),c=s.mul(m,c),d=s.mul(n,d),p=s.mul(e,p),m=s.sub(h,p),m=s.mul(e,m),m=s.add(m,d),d=s.add(h,h),h=s.add(d,h),h=s.add(h,p),h=s.mul(h,m),l=s.add(l,h),p=s.mul(i,a),p=s.add(p,p),h=s.mul(p,m),c=s.sub(c,h),d=s.mul(p,f),d=s.add(d,d),d=s.add(d,d),new v(c,l,d)}add(e){m(e);const{px:t,py:n,pz:r}=this,{px:i,py:a,pz:c}=e;let l=s.ZERO,d=s.ZERO,h=s.ZERO;const f=o.a,p=s.mul(o.b,u);let g=s.mul(t,i),y=s.mul(n,a),b=s.mul(r,c),E=s.add(t,n),w=s.add(i,a);E=s.mul(E,w),w=s.add(g,y),E=s.sub(E,w),w=s.add(t,r);let S=s.add(i,c);return w=s.mul(w,S),S=s.add(g,b),w=s.sub(w,S),S=s.add(n,r),l=s.add(a,c),S=s.mul(S,l),l=s.add(y,b),S=s.sub(S,l),h=s.mul(f,w),l=s.mul(p,b),h=s.add(l,h),l=s.sub(y,h),h=s.add(y,h),d=s.mul(l,h),y=s.add(g,g),y=s.add(y,g),b=s.mul(f,b),w=s.mul(p,w),y=s.add(y,b),b=s.sub(g,b),b=s.mul(f,b),w=s.add(w,b),g=s.mul(y,w),d=s.add(d,g),g=s.mul(S,w),l=s.mul(E,l),l=s.sub(l,g),g=s.mul(E,y),h=s.mul(S,h),h=s.add(h,g),new v(l,d,h)}subtract(e){return this.add(e.negate())}is0(){return this.equals(v.ZERO)}wNAF(e){return E.wNAFCached(this,e,v.normalizeZ)}multiplyUnsafe(e){r.aInRange("scalar",e,c,o.n);const t=v.ZERO;if(e===c)return t;if(e===l)return this;const{endo:n}=o;if(!n)return E.unsafeLadder(this,e);let{k1neg:i,k1:a,k2neg:d,k2:u}=n.splitScalar(e),h=t,f=t,p=this;for(;a>c||u>c;)a&l&&(h=h.add(p)),u&l&&(f=f.add(p)),p=p.double(),a>>=l,u>>=l;return i&&(h=h.negate()),d&&(f=f.negate()),f=new v(s.mul(f.px,n.beta),f.py,f.pz),h.add(f)}multiply(e){const{endo:t,n:n}=o;let i,a;if(r.aInRange("scalar",e,l,n),t){const{k1neg:n,k1:r,k2neg:o,k2:c}=t.splitScalar(e);let{p:l,f:d}=this.wNAF(r),{p:u,f:h}=this.wNAF(c);l=E.constTimeNegate(n,l),u=E.constTimeNegate(o,u),u=new v(s.mul(u.px,t.beta),u.py,u.pz),i=l.add(u),a=d.add(h)}else{const{p:t,f:n}=this.wNAF(e);i=t,a=n}return v.normalizeZ([i,a])[0]}multiplyAndAddUnsafe(e,t,n){const r=v.BASE,i=(e,t)=>t!==c&&t!==l&&e.equals(r)?e.multiply(t):e.multiplyUnsafe(t),o=i(this,t).add(i(e,n));return o.is0()?void 0:o}toAffine(e){return g(this,e)}isTorsionFree(){const{h:e,isTorsionFree:t}=o;if(e===l)return!0;if(t)return t(v,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:e,clearCofactor:t}=o;return e===l?this:t?t(v,this):this.multiplyUnsafe(o.h)}toRawBytes(e=!0){return(0,i.abool)("isCompressed",e),this.assertValidity(),d(v,this,e)}toHex(e=!0){return(0,i.abool)("isCompressed",e),r.bytesToHex(this.toRawBytes(e))}}v.BASE=new v(o.Gx,o.Gy,s.ONE),v.ZERO=new v(s.ZERO,s.ONE,s.ZERO);const b=o.nBitLength,E=(0,t.wNAF)(v,o.endo?Math.ceil(b/2):b);return{CURVE:o,ProjectivePoint:v,normPrivateKeyToScalar:p,weierstrassEquation:f,isWithinCurveOrder:function(e){return r.inRange(e,l,o.n)}}}function p(e,t){const n=e.ORDER;let r=c;for(let e=n-l;e%d===c;e/=d)r+=l;const i=r,o=d<<i-l-l,s=o*d,a=(n-l)/s,f=(a-l)/d,p=s-l,m=o,g=e.pow(t,a),y=e.pow(t,(a+l)/d);let v=(t,n)=>{let r=g,o=e.pow(n,p),s=e.sqr(o);s=e.mul(s,n);let a=e.mul(t,s);a=e.pow(a,f),a=e.mul(a,o),o=e.mul(a,n),s=e.mul(a,t);let c=e.mul(s,o);a=e.pow(c,m);let u=e.eql(a,e.ONE);o=e.mul(s,y),a=e.mul(c,r),s=e.cmov(o,s,u),c=e.cmov(a,c,u);for(let t=i;t>l;t--){let n=t-d;n=d<<n-l;let i=e.pow(c,n);const a=e.eql(i,e.ONE);o=e.mul(s,r),r=e.mul(r,r),i=e.mul(c,r),s=e.cmov(o,s,a),c=e.cmov(i,c,a)}return{isValid:u,value:s}};if(e.ORDER%h===u){const n=(e.ORDER-u)/h,r=e.sqrt(e.neg(t));v=(t,i)=>{let o=e.sqr(i);const s=e.mul(t,i);o=e.mul(o,s);let a=e.pow(o,n);a=e.mul(a,s);const c=e.mul(a,r),l=e.mul(e.sqr(a),i),d=e.eql(l,t);return{isValid:d,value:e.cmov(c,a,d)}}}return v}}(pn),Object.defineProperty(hn,"__esModule",{value:!0}),hn.getHash=vn,hn.createCurve=function(e,t){const n=t=>(0,yn.weierstrass)({...e,...vn(t)});return Object.freeze({...n(t),create:n})};const mn=fn,gn=Y,yn=pn;function vn(e){return{hash:e,hmac:(t,...n)=>(0,mn.hmac)(e,t,(0,gn.concatBytes)(...n)),randomBytes:gn.randomBytes}}!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.encodeToCurve=e.hashToCurve=e.schnorr=e.secp256k1=void 0;const t=rn,n=Y,r=hn,i=Ut,o=je,s=$e,a=pn,c=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),l=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),d=BigInt(1),u=BigInt(2),h=(e,t)=>(e+t/u)/t;function f(e){const t=c,n=BigInt(3),r=BigInt(6),i=BigInt(11),s=BigInt(22),a=BigInt(23),l=BigInt(44),d=BigInt(88),h=e*e*e%t,f=h*h*e%t,m=(0,o.pow2)(f,n,t)*f%t,g=(0,o.pow2)(m,n,t)*f%t,y=(0,o.pow2)(g,u,t)*h%t,v=(0,o.pow2)(y,i,t)*y%t,b=(0,o.pow2)(v,s,t)*v%t,E=(0,o.pow2)(b,l,t)*b%t,w=(0,o.pow2)(E,d,t)*E%t,S=(0,o.pow2)(w,l,t)*b%t,_=(0,o.pow2)(S,n,t)*f%t,C=(0,o.pow2)(_,a,t)*v%t,k=(0,o.pow2)(C,r,t)*h%t,M=(0,o.pow2)(k,u,t);if(!p.eql(p.sqr(M),e))throw new Error("Cannot find square root");return M}const p=(0,o.Field)(c,void 0,void 0,{sqrt:f});e.secp256k1=(0,r.createCurve)({a:BigInt(0),b:BigInt(7),Fp:p,n:l,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:e=>{const t=l,n=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-d*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),s=n,a=BigInt("0x100000000000000000000000000000000"),c=h(s*e,t),u=h(-r*e,t);let f=(0,o.mod)(e-c*n-u*i,t),p=(0,o.mod)(-c*r-u*s,t);const m=f>a,g=p>a;if(m&&(f=t-f),g&&(p=t-p),f>a||p>a)throw new Error("splitScalar: Endomorphism failed, k="+e);return{k1neg:m,k1:f,k2neg:g,k2:p}}}},t.sha256);const m=BigInt(0),g={};function y(e,...n){let r=g[e];if(void 0===r){const n=(0,t.sha256)(Uint8Array.from(e,(e=>e.charCodeAt(0))));r=(0,s.concatBytes)(n,n),g[e]=r}return(0,t.sha256)((0,s.concatBytes)(r,...n))}const v=e=>e.toRawBytes(!0).slice(1),b=e=>(0,s.numberToBytesBE)(e,32),E=e=>(0,o.mod)(e,c),w=e=>(0,o.mod)(e,l),S=e.secp256k1.ProjectivePoint,_=(e,t,n)=>S.BASE.multiplyAndAddUnsafe(e,t,n);function C(t){let n=e.secp256k1.utils.normPrivateKeyToScalar(t),r=S.fromPrivateKey(n);return{scalar:r.hasEvenY()?n:w(-n),bytes:v(r)}}function k(e){(0,s.aInRange)("x",e,d,c);const t=E(e*e);let n=f(E(t*e+BigInt(7)));n%u!==m&&(n=E(-n));const r=new S(e,n,d);return r.assertValidity(),r}const M=s.bytesToNumberBE;function A(...e){return w(M(y("BIP0340/challenge",...e)))}function x(e){return C(e).bytes}function I(e,t,r=(0,n.randomBytes)(32)){const i=(0,s.ensureBytes)("message",e),{bytes:o,scalar:a}=C(t),c=(0,s.ensureBytes)("auxRand",r,32),l=b(a^M(y("BIP0340/aux",c))),d=y("BIP0340/nonce",l,o,i),u=w(M(d));if(u===m)throw new Error("sign failed: k is zero");const{bytes:h,scalar:f}=C(u),p=A(h,o,i),g=new Uint8Array(64);if(g.set(h,0),g.set(b(w(f+p*a)),32),!R(g,i,o))throw new Error("sign: Invalid signature produced");return g}function R(e,t,n){const r=(0,s.ensureBytes)("signature",e,64),i=(0,s.ensureBytes)("message",t),o=(0,s.ensureBytes)("publicKey",n,32);try{const e=k(M(o)),t=M(r.subarray(0,32));if(!(0,s.inRange)(t,d,c))return!1;const n=M(r.subarray(32,64));if(!(0,s.inRange)(n,d,l))return!1;const a=A(b(t),v(e),i),u=_(e,n,w(-a));return!(!u||!u.hasEvenY()||u.toAffine().x!==t)}catch(e){return!1}}e.schnorr={getPublicKey:x,sign:I,verify:R,utils:{randomPrivateKey:e.secp256k1.utils.randomPrivateKey,lift_x:k,pointToBytes:v,numberToBytesBE:s.numberToBytesBE,bytesToNumberBE:s.bytesToNumberBE,taggedHash:y,mod:o.mod}};const P=(()=>(0,i.isogenyMap)(p,[["0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7","0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581","0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262","0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c"],["0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b","0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14","0x0000000000000000000000000000000000000000000000000000000000000001"],["0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c","0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3","0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931","0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84"],["0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b","0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573","0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f","0x0000000000000000000000000000000000000000000000000000000000000001"]].map((e=>e.map((e=>BigInt(e)))))))(),O=(()=>(0,a.mapToCurveSimpleSWU)(p,{A:BigInt("0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533"),B:BigInt("1771"),Z:p.create(BigInt("-11"))}))(),T=(()=>(0,i.createHasher)(e.secp256k1.ProjectivePoint,(e=>{const{x:t,y:n}=O(p.create(e[0]));return P(t,n)}),{DST:"secp256k1_XMD:SHA-256_SSWU_RO_",encodeDST:"secp256k1_XMD:SHA-256_SSWU_NU_",p:p.ORDER,m:1,k:128,expand:"xmd",hash:t.sha256}))();e.hashToCurve=T.hashToCurve,e.encodeToCurve=T.encodeToCurve}(nn);var bn={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.decodeHex=e.remove0x=void 0;var t=v;e.remove0x=function(e){return e.startsWith("0x")||e.startsWith("0X")?e.slice(2):e};e.decodeHex=function(n){return(0,t.hexToBytes)((0,e.remove0x)(n))}}(bn),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.hexToPublicKey=e.convertPublicKeyFormat=e.getSharedPoint=e.getPublicKey=e.isValidPrivateKey=e.getValidSecret=void 0;var t=N,n=B,r=nn,i=x,o=I,s=bn;e.getValidSecret=function(){var n;do{n=(0,t.randomBytes)(o.SECRET_KEY_LENGTH)}while(!(0,e.isValidPrivateKey)(n));return n};e.isValidPrivateKey=function(e){return a((0,i.ellipticCurve)(),(function(t){return t.utils.isValidPrivateKey(e)}),(function(){return!0}),(function(){return!0}))};e.getPublicKey=function(e){return a((0,i.ellipticCurve)(),(function(t){return t.getPublicKey(e)}),(function(t){return t.getPublicKey(e)}),(function(t){return t.getPublicKey(e)}))};e.getSharedPoint=function(e,t,n){return a((0,i.ellipticCurve)(),(function(r){return r.getSharedSecret(e,t,n)}),(function(n){return n.getSharedSecret(e,t)}),(function(n){return l(n,e,t)}))};e.convertPublicKeyFormat=function(e,t){return a((0,i.ellipticCurve)(),(function(n){return n.getSharedSecret(BigInt(1),e,t)}),(function(){return e}),(function(){return e}))};function a(e,t,i,o){if("secp256k1"===e)return t(r.secp256k1);if("x25519"===e)return i(n.x25519);if("ed25519"===e)return o(n.ed25519);throw new Error("Not implemented")}e.hexToPublicKey=function(e){var t=(0,s.decodeHex)(e);return a((0,i.ellipticCurve)(),(function(){return c(t)}),(function(){return t}),(function(){return t}))};var c=function(e){if(e.length===o.ETH_PUBLIC_KEY_SIZE){var t=new Uint8Array(1+e.length);return t.set([4]),t.set(e,1),t}return e},l=function(e,t,n){var r=e.utils.getExtendedPublicKey(t).scalar;return e.ExtendedPoint.fromHex(n).multiply(r).toRawBytes()}}(T);var En={},wn={};Object.defineProperty(wn,"__esModule",{value:!0}),wn.hkdf=void 0,wn.extract=kn,wn.expand=xn;const Sn=$,_n=Y,Cn=fn;function kn(e,t,n){return(0,Sn.hash)(e),void 0===n&&(n=new Uint8Array(e.outputLen)),(0,Cn.hmac)(e,(0,_n.toBytes)(n),(0,_n.toBytes)(t))}const Mn=new Uint8Array([0]),An=new Uint8Array;function xn(e,t,n,r=32){if((0,Sn.hash)(e),(0,Sn.number)(r),r>255*e.outputLen)throw new Error("Length should be <= 255*HashLen");const i=Math.ceil(r/e.outputLen);void 0===n&&(n=An);const o=new Uint8Array(i*e.outputLen),s=Cn.hmac.create(e,t),a=s._cloneInto(),c=new Uint8Array(s.outputLen);for(let t=0;t<i;t++)Mn[0]=t+1,a.update(0===t?An:c).update(n).update(Mn).digestInto(c),o.set(c,e.outputLen*t),s._cloneInto(a);return s.destroy(),a.destroy(),c.fill(0),Mn.fill(0),o.slice(0,r)}wn.hkdf=(e,t,n,r,i)=>xn(e,kn(e,t,n),r,i),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getSharedKey=e.deriveKey=void 0;var t=v,n=wn,r=rn;e.deriveKey=function(e,t,i){return(0,n.hkdf)(r.sha256,e,t,i,32)};e.getSharedKey=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return(0,e.deriveKey)(t.concatBytes.apply(void 0,n))}}(En);var In={},Rn={},Pn={},On={};Object.defineProperty(On,"__esModule",{value:!0}),On.polyval=On.ghash=void 0,On._toGHASHKey=jn;const Tn=b,Nn=v,Ln=16,Dn=new Uint8Array(16),Bn=(0,Nn.u32)(Dn),Kn=e=>(e>>>0&255)<<24|(e>>>8&255)<<16|(e>>>16&255)<<8|e>>>24&255|0;function jn(e){e.reverse();const t=1&e[15];let n=0;for(let t=0;t<e.length;t++){const r=e[t];e[t]=r>>>1|n,n=(1&r)<<7}return e[0]^=225&-t,e}class $n{constructor(e,t){this.blockLen=Ln,this.outputLen=Ln,this.s0=0,this.s1=0,this.s2=0,this.s3=0,this.finished=!1,e=(0,Nn.toBytes)(e),(0,Tn.bytes)(e,16);const n=(0,Nn.createView)(e);let r=n.getUint32(0,!1),i=n.getUint32(4,!1),o=n.getUint32(8,!1),s=n.getUint32(12,!1);const a=[];for(let e=0;e<128;e++)a.push({s0:Kn(r),s1:Kn(i),s2:Kn(o),s3:Kn(s)}),({s0:r,s1:i,s2:o,s3:s}={s3:(d=o)<<31|(u=s)>>>1,s2:(l=i)<<31|d>>>1,s1:(c=r)<<31|l>>>1,s0:c>>>1^225<<24&-(1&u)});var c,l,d,u;const h=(e=>e>65536?8:e>1024?4:2)(t||1024);if(![1,2,4,8].includes(h))throw new Error(`ghash: wrong window size=${h}, should be 2, 4 or 8`);this.W=h;const f=128/h,p=this.windowSize=2**h,m=[];for(let e=0;e<f;e++)for(let t=0;t<p;t++){let n=0,r=0,i=0,o=0;for(let s=0;s<h;s++){if(!(t>>>h-s-1&1))continue;const{s0:c,s1:l,s2:d,s3:u}=a[h*e+s];n^=c,r^=l,i^=d,o^=u}m.push({s0:n,s1:r,s2:i,s3:o})}this.t=m}_updateBlock(e,t,n,r){e^=this.s0,t^=this.s1,n^=this.s2,r^=this.s3;const{W:i,t:o,windowSize:s}=this;let a=0,c=0,l=0,d=0;const u=(1<<i)-1;let h=0;for(const f of[e,t,n,r])for(let e=0;e<4;e++){const t=f>>>8*e&255;for(let e=8/i-1;e>=0;e--){const n=t>>>i*e&u,{s0:r,s1:f,s2:p,s3:m}=o[h*s+n];a^=r,c^=f,l^=p,d^=m,h+=1}}this.s0=a,this.s1=c,this.s2=l,this.s3=d}update(e){e=(0,Nn.toBytes)(e),(0,Tn.exists)(this);const t=(0,Nn.u32)(e),n=Math.floor(e.length/Ln),r=e.length%Ln;for(let e=0;e<n;e++)this._updateBlock(t[4*e+0],t[4*e+1],t[4*e+2],t[4*e+3]);return r&&(Dn.set(e.subarray(n*Ln)),this._updateBlock(Bn[0],Bn[1],Bn[2],Bn[3]),(0,Nn.clean)(Bn)),this}destroy(){const{t:e}=this;for(const t of e)t.s0=0,t.s1=0,t.s2=0,t.s3=0}digestInto(e){(0,Tn.exists)(this),(0,Tn.output)(e,this),this.finished=!0;const{s0:t,s1:n,s2:r,s3:i}=this,o=(0,Nn.u32)(e);return o[0]=t,o[1]=n,o[2]=r,o[3]=i,e}digest(){const e=new Uint8Array(Ln);return this.digestInto(e),this.destroy(),e}}class Un extends $n{constructor(e,t){e=(0,Nn.toBytes)(e);const n=jn((0,Nn.copyBytes)(e));super(n,t),(0,Nn.clean)(n)}update(e){e=(0,Nn.toBytes)(e),(0,Tn.exists)(this);const t=(0,Nn.u32)(e),n=e.length%Ln,r=Math.floor(e.length/Ln);for(let e=0;e<r;e++)this._updateBlock(Kn(t[4*e+3]),Kn(t[4*e+2]),Kn(t[4*e+1]),Kn(t[4*e+0]));return n&&(Dn.set(e.subarray(r*Ln)),this._updateBlock(Kn(Bn[3]),Kn(Bn[2]),Kn(Bn[1]),Kn(Bn[0])),(0,Nn.clean)(Bn)),this}digestInto(e){(0,Tn.exists)(this),(0,Tn.output)(e,this),this.finished=!0;const{s0:t,s1:n,s2:r,s3:i}=this,o=(0,Nn.u32)(e);return o[0]=t,o[1]=n,o[2]=r,o[3]=i,e.reverse()}}function Hn(e){const t=(t,n)=>e(n,t.length).update((0,Nn.toBytes)(t)).digest(),n=e(new Uint8Array(16),0);return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=(t,n)=>e(t,n),t}On.ghash=Hn(((e,t)=>new $n(e,t))),On.polyval=Hn(((e,t)=>new Un(e,t))),Object.defineProperty(Pn,"__esModule",{value:!0}),Pn.unsafe=Pn.aeskwp=Pn.aeskw=Pn.siv=Pn.gcm=Pn.cfb=Pn.cbc=Pn.ecb=Pn.ctr=void 0,Pn.expandKeyLE=sr,Pn.expandKeyDecLE=ar;const Fn=b,qn=On,zn=v,Wn=16,Vn=new Uint8Array(Wn),Gn=283;function Yn(e){return e<<1^Gn&-(e>>7)}function Zn(e,t){let n=0;for(;t>0;t>>=1)n^=e&-(1&t),e=Yn(e);return n}const Jn=(()=>{const e=new Uint8Array(256);for(let t=0,n=1;t<256;t++,n^=Yn(n))e[t]=n;const t=new Uint8Array(256);t[0]=99;for(let n=0;n<255;n++){let r=e[255-n];r|=r<<8,t[e[n]]=255&(r^r>>4^r>>5^r>>6^r>>7^99)}return(0,zn.clean)(e),t})(),Qn=Jn.map(((e,t)=>Jn.indexOf(t))),Xn=e=>e<<24|e>>>8,er=e=>e<<8|e>>>24,tr=e=>e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255;function nr(e,t){if(256!==e.length)throw new Error("Wrong sbox length");const n=new Uint32Array(256).map(((n,r)=>t(e[r]))),r=n.map(er),i=r.map(er),o=i.map(er),s=new Uint32Array(65536),a=new Uint32Array(65536),c=new Uint16Array(65536);for(let t=0;t<256;t++)for(let l=0;l<256;l++){const d=256*t+l;s[d]=n[t]^r[l],a[d]=i[t]^o[l],c[d]=e[t]<<8|e[l]}return{sbox:e,sbox2:c,T0:n,T1:r,T2:i,T3:o,T01:s,T23:a}}const rr=nr(Jn,(e=>Zn(e,3)<<24|e<<16|e<<8|Zn(e,2))),ir=nr(Qn,(e=>Zn(e,11)<<24|Zn(e,13)<<16|Zn(e,9)<<8|Zn(e,14))),or=(()=>{const e=new Uint8Array(16);for(let t=0,n=1;t<16;t++,n=Yn(n))e[t]=n;return e})();function sr(e){(0,Fn.bytes)(e);const t=e.length;if(![16,24,32].includes(t))throw new Error(`aes: wrong key size: should be 16, 24 or 32, got: ${t}`);const{sbox2:n}=rr,r=[];(0,zn.isAligned32)(e)||r.push(e=(0,zn.copyBytes)(e));const i=(0,zn.u32)(e),o=i.length,s=e=>lr(n,e,e,e,e),a=new Uint32Array(t+28);a.set(i);for(let e=o;e<a.length;e++){let t=a[e-1];e%o==0?t=s(Xn(t))^or[e/o-1]:o>6&&e%o==4&&(t=s(t)),a[e]=a[e-o]^t}return(0,zn.clean)(...r),a}function ar(e){const t=sr(e),n=t.slice(),r=t.length,{sbox2:i}=rr,{T0:o,T1:s,T2:a,T3:c}=ir;for(let e=0;e<r;e+=4)for(let i=0;i<4;i++)n[e+i]=t[r-e-4+i];(0,zn.clean)(t);for(let e=4;e<r-4;e++){const t=n[e],r=lr(i,t,t,t,t);n[e]=o[255&r]^s[r>>>8&255]^a[r>>>16&255]^c[r>>>24]}return n}function cr(e,t,n,r,i,o){return e[n<<8&65280|r>>>8&255]^t[i>>>8&65280|o>>>24&255]}function lr(e,t,n,r,i){return e[255&t|65280&n]|e[r>>>16&255|i>>>16&65280]<<16}function dr(e,t,n,r,i){const{sbox2:o,T01:s,T23:a}=rr;let c=0;t^=e[c++],n^=e[c++],r^=e[c++],i^=e[c++];const l=e.length/4-2;for(let o=0;o<l;o++){const o=e[c++]^cr(s,a,t,n,r,i),l=e[c++]^cr(s,a,n,r,i,t),d=e[c++]^cr(s,a,r,i,t,n),u=e[c++]^cr(s,a,i,t,n,r);t=o,n=l,r=d,i=u}return{s0:e[c++]^lr(o,t,n,r,i),s1:e[c++]^lr(o,n,r,i,t),s2:e[c++]^lr(o,r,i,t,n),s3:e[c++]^lr(o,i,t,n,r)}}function ur(e,t,n,r,i){const{sbox2:o,T01:s,T23:a}=ir;let c=0;t^=e[c++],n^=e[c++],r^=e[c++],i^=e[c++];const l=e.length/4-2;for(let o=0;o<l;o++){const o=e[c++]^cr(s,a,t,i,r,n),l=e[c++]^cr(s,a,n,t,i,r),d=e[c++]^cr(s,a,r,n,t,i),u=e[c++]^cr(s,a,i,r,n,t);t=o,n=l,r=d,i=u}return{s0:e[c++]^lr(o,t,i,r,n),s1:e[c++]^lr(o,n,t,i,r),s2:e[c++]^lr(o,r,n,t,i),s3:e[c++]^lr(o,i,r,n,t)}}function hr(e,t){if(void 0===t)return new Uint8Array(e);if((0,Fn.bytes)(t),t.length<e)throw new Error(`aes: wrong destination length, expected at least ${e}, got: ${t.length}`);if(!(0,zn.isAligned32)(t))throw new Error("unaligned dst");return t}function fr(e,t,n,r){(0,Fn.bytes)(t,Wn),(0,Fn.bytes)(n);const i=n.length;r=hr(i,r);const o=t,s=(0,zn.u32)(o);let{s0:a,s1:c,s2:l,s3:d}=dr(e,s[0],s[1],s[2],s[3]);const u=(0,zn.u32)(n),h=(0,zn.u32)(r);for(let t=0;t+4<=u.length;t+=4){h[t+0]=u[t+0]^a,h[t+1]=u[t+1]^c,h[t+2]=u[t+2]^l,h[t+3]=u[t+3]^d;let n=1;for(let e=o.length-1;e>=0;e--)n=n+(255&o[e])|0,o[e]=255&n,n>>>=8;({s0:a,s1:c,s2:l,s3:d}=dr(e,s[0],s[1],s[2],s[3]))}const f=Wn*Math.floor(u.length/4);if(f<i){const e=new Uint32Array([a,c,l,d]),t=(0,zn.u8)(e);for(let e=f,o=0;e<i;e++,o++)r[e]=n[e]^t[o];(0,zn.clean)(e)}return r}function pr(e,t,n,r,i){(0,Fn.bytes)(n,Wn),(0,Fn.bytes)(r),i=hr(r.length,i);const o=n,s=(0,zn.u32)(o),a=(0,zn.createView)(o),c=(0,zn.u32)(r),l=(0,zn.u32)(i),d=t?0:12,u=r.length;let h=a.getUint32(d,t),{s0:f,s1:p,s2:m,s3:g}=dr(e,s[0],s[1],s[2],s[3]);for(let n=0;n+4<=c.length;n+=4)l[n+0]=c[n+0]^f,l[n+1]=c[n+1]^p,l[n+2]=c[n+2]^m,l[n+3]=c[n+3]^g,h=h+1>>>0,a.setUint32(d,h,t),({s0:f,s1:p,s2:m,s3:g}=dr(e,s[0],s[1],s[2],s[3]));const y=Wn*Math.floor(c.length/4);if(y<u){const e=new Uint32Array([f,p,m,g]),t=(0,zn.u8)(e);for(let e=y,n=0;e<u;e++,n++)i[e]=r[e]^t[n];(0,zn.clean)(e)}return i}function mr(e){if((0,Fn.bytes)(e),e.length%Wn!=0)throw new Error("aes/(cbc-ecb).decrypt ciphertext should consist of blocks with size 16")}function gr(e,t,n){(0,Fn.bytes)(e);let r=e.length;const i=r%Wn;if(!t&&0!==i)throw new Error("aec/(cbc-ecb): unpadded plaintext with disabled padding");(0,zn.isAligned32)(e)||(e=(0,zn.copyBytes)(e));const o=(0,zn.u32)(e);if(t){let e=Wn-i;e||(e=Wn),r+=e}const s=hr(r,n);return{b:o,o:(0,zn.u32)(s),out:s}}function yr(e,t){if(!t)return e;const n=e.length;if(!n)throw new Error("aes/pcks5: empty ciphertext not allowed");const r=e[n-1];if(r<=0||r>16)throw new Error("aes/pcks5: wrong padding");const i=e.subarray(0,-r);for(let t=0;t<r;t++)if(e[n-t-1]!==r)throw new Error("aes/pcks5: wrong padding");return i}function vr(e){const t=new Uint8Array(16),n=(0,zn.u32)(t);t.set(e);const r=Wn-e.length;for(let e=Wn-r;e<Wn;e++)t[e]=r;return n}function br(e,t,n,r,i){const o=null==i?0:i.length,s=e.create(n,r.length+o);i&&s.update(i),s.update(r);const a=new Uint8Array(16),c=(0,zn.createView)(a);i&&(0,zn.setBigUint64)(c,0,BigInt(8*o),t),(0,zn.setBigUint64)(c,8,BigInt(8*r.length),t),s.update(a);const l=s.digest();return(0,zn.clean)(a),l}Pn.ctr=(0,zn.wrapCipher)({blockSize:16,nonceLength:16},(function(e,t){function n(n,r){if((0,Fn.bytes)(n),void 0!==r&&((0,Fn.bytes)(r),!(0,zn.isAligned32)(r)))throw new Error("unaligned destination");const i=sr(e),o=(0,zn.copyBytes)(t),s=[i,o];(0,zn.isAligned32)(n)||s.push(n=(0,zn.copyBytes)(n));const a=fr(i,o,n,r);return(0,zn.clean)(...s),a}return(0,Fn.bytes)(e),(0,Fn.bytes)(t,Wn),{encrypt:(e,t)=>n(e,t),decrypt:(e,t)=>n(e,t)}})),Pn.ecb=(0,zn.wrapCipher)({blockSize:16},(function(e,t={}){(0,Fn.bytes)(e);const n=!t.disablePadding;return{encrypt(t,r){const{b:i,o:o,out:s}=gr(t,n,r),a=sr(e);let c=0;for(;c+4<=i.length;){const{s0:e,s1:t,s2:n,s3:r}=dr(a,i[c+0],i[c+1],i[c+2],i[c+3]);o[c++]=e,o[c++]=t,o[c++]=n,o[c++]=r}if(n){const e=vr(t.subarray(4*c)),{s0:n,s1:r,s2:i,s3:s}=dr(a,e[0],e[1],e[2],e[3]);o[c++]=n,o[c++]=r,o[c++]=i,o[c++]=s}return(0,zn.clean)(a),s},decrypt(t,r){mr(t);const i=ar(e),o=hr(t.length,r),s=[i];(0,zn.isAligned32)(t)||s.push(t=(0,zn.copyBytes)(t));const a=(0,zn.u32)(t),c=(0,zn.u32)(o);for(let e=0;e+4<=a.length;){const{s0:t,s1:n,s2:r,s3:o}=ur(i,a[e+0],a[e+1],a[e+2],a[e+3]);c[e++]=t,c[e++]=n,c[e++]=r,c[e++]=o}return(0,zn.clean)(...s),yr(o,n)}}})),Pn.cbc=(0,zn.wrapCipher)({blockSize:16,nonceLength:16},(function(e,t,n={}){(0,Fn.bytes)(e),(0,Fn.bytes)(t,16);const r=!n.disablePadding;return{encrypt(n,i){const o=sr(e),{b:s,o:a,out:c}=gr(n,r,i);let l=t;const d=[o];(0,zn.isAligned32)(l)||d.push(l=(0,zn.copyBytes)(l));const u=(0,zn.u32)(l);let h=u[0],f=u[1],p=u[2],m=u[3],g=0;for(;g+4<=s.length;)h^=s[g+0],f^=s[g+1],p^=s[g+2],m^=s[g+3],({s0:h,s1:f,s2:p,s3:m}=dr(o,h,f,p,m)),a[g++]=h,a[g++]=f,a[g++]=p,a[g++]=m;if(r){const e=vr(n.subarray(4*g));h^=e[0],f^=e[1],p^=e[2],m^=e[3],({s0:h,s1:f,s2:p,s3:m}=dr(o,h,f,p,m)),a[g++]=h,a[g++]=f,a[g++]=p,a[g++]=m}return(0,zn.clean)(...d),c},decrypt(n,i){mr(n);const o=ar(e);let s=t;const a=[o];(0,zn.isAligned32)(s)||a.push(s=(0,zn.copyBytes)(s));const c=(0,zn.u32)(s),l=hr(n.length,i);(0,zn.isAligned32)(n)||a.push(n=(0,zn.copyBytes)(n));const d=(0,zn.u32)(n),u=(0,zn.u32)(l);let h=c[0],f=c[1],p=c[2],m=c[3];for(let e=0;e+4<=d.length;){const t=h,n=f,r=p,i=m;h=d[e+0],f=d[e+1],p=d[e+2],m=d[e+3];const{s0:s,s1:a,s2:c,s3:l}=ur(o,h,f,p,m);u[e++]=s^t,u[e++]=a^n,u[e++]=c^r,u[e++]=l^i}return(0,zn.clean)(...a),yr(l,r)}}})),Pn.cfb=(0,zn.wrapCipher)({blockSize:16,nonceLength:16},(function(e,t){function n(n,r,i){(0,Fn.bytes)(n);const o=n.length;i=hr(o,i);const s=sr(e);let a=t;const c=[s];(0,zn.isAligned32)(a)||c.push(a=(0,zn.copyBytes)(a)),(0,zn.isAligned32)(n)||c.push(n=(0,zn.copyBytes)(n));const l=(0,zn.u32)(n),d=(0,zn.u32)(i),u=r?d:l,h=(0,zn.u32)(a);let f=h[0],p=h[1],m=h[2],g=h[3];for(let e=0;e+4<=l.length;){const{s0:t,s1:n,s2:r,s3:i}=dr(s,f,p,m,g);d[e+0]=l[e+0]^t,d[e+1]=l[e+1]^n,d[e+2]=l[e+2]^r,d[e+3]=l[e+3]^i,f=u[e++],p=u[e++],m=u[e++],g=u[e++]}const y=Wn*Math.floor(l.length/4);if(y<o){({s0:f,s1:p,s2:m,s3:g}=dr(s,f,p,m,g));const e=(0,zn.u8)(new Uint32Array([f,p,m,g]));for(let t=y,r=0;t<o;t++,r++)i[t]=n[t]^e[r];(0,zn.clean)(e)}return(0,zn.clean)(...c),i}return(0,Fn.bytes)(e),(0,Fn.bytes)(t,16),{encrypt:(e,t)=>n(e,!0,t),decrypt:(e,t)=>n(e,!1,t)}})),Pn.gcm=(0,zn.wrapCipher)({blockSize:16,nonceLength:12,tagLength:16},(function(e,t,n){if((0,Fn.bytes)(e),(0,Fn.bytes)(t),void 0!==n&&(0,Fn.bytes)(n),t.length<8)throw new Error("aes/gcm: invalid nonce length");const r=16;function i(e,t,r){const i=br(qn.ghash,!1,e,r,n);for(let e=0;e<t.length;e++)i[e]^=t[e];return i}function o(){const n=sr(e),r=Vn.slice(),i=Vn.slice();if(pr(n,!1,i,i,r),12===t.length)i.set(t);else{const e=Vn.slice(),n=(0,zn.createView)(e);(0,zn.setBigUint64)(n,8,BigInt(8*t.length),!1);const o=qn.ghash.create(r).update(t).update(e);o.digestInto(i),o.destroy()}return{xk:n,authKey:r,counter:i,tagMask:pr(n,!1,i,Vn)}}return{encrypt(e){(0,Fn.bytes)(e);const{xk:t,authKey:n,counter:s,tagMask:a}=o(),c=new Uint8Array(e.length+r),l=[t,n,s,a];(0,zn.isAligned32)(e)||l.push(e=(0,zn.copyBytes)(e)),pr(t,!1,s,e,c);const d=i(n,a,c.subarray(0,c.length-r));return l.push(d),c.set(d,e.length),(0,zn.clean)(...l),c},decrypt(e){if((0,Fn.bytes)(e),e.length<r)throw new Error("aes/gcm: ciphertext less than tagLen (16)");const{xk:t,authKey:n,counter:s,tagMask:a}=o(),c=[t,n,a,s];(0,zn.isAligned32)(e)||c.push(e=(0,zn.copyBytes)(e));const l=e.subarray(0,-16),d=e.subarray(-16),u=i(n,a,l);if(c.push(u),!(0,zn.equalBytes)(u,d))throw new Error("aes/gcm: invalid ghash tag");const h=pr(t,!1,s,l);return(0,zn.clean)(...c),h}}}));const Er=(e,t,n)=>r=>{if(!Number.isSafeInteger(r)||t>r||r>n)throw new Error(`${e}: invalid value=${r}, must be [${t}..${n}]`)};function wr(e){return null!=e&&"object"==typeof e&&(e instanceof Uint32Array||"Uint32Array"===e.constructor.name)}function Sr(e,t){if((0,Fn.bytes)(t,16),!wr(e))throw new Error("_encryptBlock accepts result of expandKeyLE");const n=(0,zn.u32)(t);let{s0:r,s1:i,s2:o,s3:s}=dr(e,n[0],n[1],n[2],n[3]);return n[0]=r,n[1]=i,n[2]=o,n[3]=s,t}function _r(e,t){if((0,Fn.bytes)(t,16),!wr(e))throw new Error("_decryptBlock accepts result of expandKeyLE");const n=(0,zn.u32)(t);let{s0:r,s1:i,s2:o,s3:s}=ur(e,n[0],n[1],n[2],n[3]);return n[0]=r,n[1]=i,n[2]=o,n[3]=s,t}Pn.siv=(0,zn.wrapCipher)({blockSize:16,nonceLength:12,tagLength:16},(function(e,t,n){const r=Er("AAD",0,2**36),i=Er("plaintext",0,2**36),o=Er("nonce",12,12),s=Er("ciphertext",16,2**36+16);function a(){const n=sr(e),r=new Uint8Array(e.length),i=new Uint8Array(16),o=[n,r];let s=t;(0,zn.isAligned32)(s)||o.push(s=(0,zn.copyBytes)(s));const a=(0,zn.u32)(s);let c=0,l=a[0],d=a[1],u=a[2],h=0;for(const e of[i,r].map(zn.u32)){const t=(0,zn.u32)(e);for(let e=0;e<t.length;e+=2){const{s0:r,s1:i}=dr(n,c,l,d,u);t[e+0]=r,t[e+1]=i,c=++h}}const f={authKey:i,encKey:sr(r)};return(0,zn.clean)(...o),f}function c(e,r,i){const o=br(qn.polyval,!0,r,i,n);for(let e=0;e<12;e++)o[e]^=t[e];o[15]&=127;const s=(0,zn.u32)(o);let a=s[0],c=s[1],l=s[2],d=s[3];return({s0:a,s1:c,s2:l,s3:d}=dr(e,a,c,l,d)),s[0]=a,s[1]=c,s[2]=l,s[3]=d,o}function l(e,t,n){let r=(0,zn.copyBytes)(t);r[15]|=128;const i=pr(e,!0,r,n);return(0,zn.clean)(r),i}return(0,Fn.bytes)(e,16,24,32),(0,Fn.bytes)(t),o(t.length),void 0!==n&&((0,Fn.bytes)(n),r(n.length)),{encrypt(e){(0,Fn.bytes)(e),i(e.length);const{encKey:t,authKey:n}=a(),r=c(t,n,e),o=[t,n,r];(0,zn.isAligned32)(e)||o.push(e=(0,zn.copyBytes)(e));const s=new Uint8Array(e.length+16);return s.set(r,e.length),s.set(l(t,r,e)),(0,zn.clean)(...o),s},decrypt(e){(0,Fn.bytes)(e),s(e.length);const t=e.subarray(-16),{encKey:n,authKey:r}=a(),i=[n,r];(0,zn.isAligned32)(e)||i.push(e=(0,zn.copyBytes)(e));const o=l(n,t,e.subarray(0,-16)),d=c(n,r,o);if(i.push(d),!(0,zn.equalBytes)(t,d))throw(0,zn.clean)(...i),new Error("invalid polyval tag");return(0,zn.clean)(...i),o}}}));const Cr={encrypt(e,t){if(t.length>=2**32)throw new Error("plaintext should be less than 4gb");const n=sr(e);if(16===t.length)Sr(n,t);else{const e=(0,zn.u32)(t);let r=e[0],i=e[1];for(let t=0,o=1;t<6;t++)for(let t=2;t<e.length;t+=2,o++){const{s0:s,s1:a,s2:c,s3:l}=dr(n,r,i,e[t],e[t+1]);r=s,i=a^tr(o),e[t]=c,e[t+1]=l}e[0]=r,e[1]=i}n.fill(0)},decrypt(e,t){if(t.length-8>=2**32)throw new Error("ciphertext should be less than 4gb");const n=ar(e),r=t.length/8-1;if(1===r)_r(n,t);else{const e=(0,zn.u32)(t);let i=e[0],o=e[1];for(let t=0,s=6*r;t<6;t++)for(let t=2*r;t>=1;t-=2,s--){o^=tr(s);const{s0:r,s1:a,s2:c,s3:l}=ur(n,i,o,e[t],e[t+1]);i=r,o=a,e[t]=c,e[t+1]=l}e[0]=i,e[1]=o}n.fill(0)}},kr=new Uint8Array(8).fill(166);Pn.aeskw=(0,zn.wrapCipher)({blockSize:8},(e=>({encrypt(t){if((0,Fn.bytes)(t),!t.length||t.length%8!=0)throw new Error("invalid plaintext length");if(8===t.length)throw new Error("8-byte keys not allowed in AESKW, use AESKWP instead");const n=(0,zn.concatBytes)(kr,t);return Cr.encrypt(e,n),n},decrypt(t){if((0,Fn.bytes)(t),t.length%8!=0||t.length<24)throw new Error("invalid ciphertext length");const n=(0,zn.copyBytes)(t);if(Cr.decrypt(e,n),!(0,zn.equalBytes)(n.subarray(0,8),kr))throw new Error("integrity check failed");return n.subarray(0,8).fill(0),n.subarray(8)}})));const Mr=2790873510;Pn.aeskwp=(0,zn.wrapCipher)({blockSize:8},(e=>({encrypt(t){if((0,Fn.bytes)(t),!t.length)throw new Error("invalid plaintext length");const n=8*Math.ceil(t.length/8),r=new Uint8Array(8+n);r.set(t,8);const i=(0,zn.u32)(r);return i[0]=Mr,i[1]=tr(t.length),Cr.encrypt(e,r),r},decrypt(t){if((0,Fn.bytes)(t),t.length<16)throw new Error("invalid ciphertext length");const n=(0,zn.copyBytes)(t),r=(0,zn.u32)(n);Cr.decrypt(e,n);const i=tr(r[1])>>>0,o=8*Math.ceil(i/8);if(r[0]!==Mr||n.length-8!==o)throw new Error("integrity check failed");for(let e=i;e<o;e++)if(0!==n[8+e])throw new Error("integrity check failed");return n.subarray(0,8).fill(0),n.subarray(8,8+i)}}))),Pn.unsafe={expandKeyLE:sr,expandKeyDecLE:ar,encrypt:dr,decrypt:ur,encryptBlock:Sr,decryptBlock:_r,ctrCounter:fr,ctr32:pr},Object.defineProperty(Rn,"__esModule",{value:!0}),Rn.aes256cbc=Rn.aes256gcm=void 0;var Ar=Pn;Rn.aes256gcm=function(e,t,n){return(0,Ar.gcm)(e,t,n)};Rn.aes256cbc=function(e,t,n){return(0,Ar.cbc)(e,t)};var xr={},Ir={},Rr={};Object.defineProperty(Rr,"__esModule",{value:!0}),Rr.sigma=void 0,Rr.rotl=function(e,t){return e<<t|e>>>32-t},Rr.createCipher=function(e,t){const{allowShortKeys:n,extendNonceFn:r,counterLength:i,counterRight:o,rounds:s}=(0,Or.checkOpts)({allowShortKeys:!1,counterLength:8,counterRight:!1,rounds:20},t);if("function"!=typeof e)throw new Error("core must be a function");return(0,Pr.number)(i),(0,Pr.number)(s),(0,Pr.bool)(o),(0,Pr.bool)(n),(t,a,c,l,d=0)=>{(0,Pr.bytes)(t),(0,Pr.bytes)(a),(0,Pr.bytes)(c);const u=c.length;if(void 0===l&&(l=new Uint8Array(u)),(0,Pr.bytes)(l),(0,Pr.number)(d),d<0||d>=Ur)throw new Error("arx: counter overflow");if(l.length<u)throw new Error(`arx: output (${l.length}) is shorter than data (${u})`);const h=[];let f,p,m=t.length;if(32===m)h.push(f=(0,Or.copyBytes)(t)),p=Br;else{if(16!==m||!n)throw new Error(`arx: invalid 32-byte key, got length=${m}`);f=new Uint8Array(32),f.set(t),f.set(t,16),p=Dr,h.push(f)}Kr(a)||h.push(a=(0,Or.copyBytes)(a));const g=(0,Or.u32)(f);if(r){if(24!==a.length)throw new Error("arx: extended nonce must be 24 bytes");r(p,g,(0,Or.u32)(a.subarray(0,16)),g),a=a.subarray(16)}const y=16-i;if(y!==a.length)throw new Error(`arx: nonce must be ${y} or 16 bytes`);if(12!==y){const e=new Uint8Array(12);e.set(a,o?0:12-a.length),a=e,h.push(a)}const v=(0,Or.u32)(a);return function(e,t,n,r,i,o,s,a){const c=i.length,l=new Uint8Array(jr),d=(0,Or.u32)(l),u=Kr(i)&&Kr(o),h=u?(0,Or.u32)(i):Hr,f=u?(0,Or.u32)(o):Hr;for(let p=0;p<c;s++){if(e(t,n,r,d,s,a),s>=Ur)throw new Error("arx: counter overflow");const m=Math.min(jr,c-p);if(u&&m===jr){const e=p/4;if(p%4!=0)throw new Error("arx: invalid block position");for(let t,n=0;n<$r;n++)t=e+n,f[t]=h[t]^d[n];p+=jr}else{for(let e,t=0;t<m;t++)e=p+t,o[e]=i[e]^l[t];p+=m}}}(e,p,g,v,c,l,d,s),(0,Or.clean)(...h),l}};const Pr=b,Or=v,Tr=e=>Uint8Array.from(e.split("").map((e=>e.charCodeAt(0)))),Nr=Tr("expand 16-byte k"),Lr=Tr("expand 32-byte k"),Dr=(0,Or.u32)(Nr),Br=(0,Or.u32)(Lr);function Kr(e){return e.byteOffset%4==0}Rr.sigma=Br.slice();const jr=64,$r=16,Ur=2**32-1,Hr=new Uint32Array;var Fr={};Object.defineProperty(Fr,"__esModule",{value:!0}),Fr.poly1305=void 0,Fr.wrapConstructorWithKey=Gr;const qr=b,zr=v,Wr=(e,t)=>255&e[t++]|(255&e[t++])<<8;class Vr{constructor(e){this.blockLen=16,this.outputLen=16,this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.pos=0,this.finished=!1,e=(0,zr.toBytes)(e),(0,qr.bytes)(e,32);const t=Wr(e,0),n=Wr(e,2),r=Wr(e,4),i=Wr(e,6),o=Wr(e,8),s=Wr(e,10),a=Wr(e,12),c=Wr(e,14);this.r[0]=8191&t,this.r[1]=8191&(t>>>13|n<<3),this.r[2]=7939&(n>>>10|r<<6),this.r[3]=8191&(r>>>7|i<<9),this.r[4]=255&(i>>>4|o<<12),this.r[5]=o>>>1&8190,this.r[6]=8191&(o>>>14|s<<2),this.r[7]=8065&(s>>>11|a<<5),this.r[8]=8191&(a>>>8|c<<8),this.r[9]=c>>>5&127;for(let t=0;t<8;t++)this.pad[t]=Wr(e,16+2*t)}process(e,t,n=!1){const r=n?0:2048,{h:i,r:o}=this,s=o[0],a=o[1],c=o[2],l=o[3],d=o[4],u=o[5],h=o[6],f=o[7],p=o[8],m=o[9],g=Wr(e,t+0),y=Wr(e,t+2),v=Wr(e,t+4),b=Wr(e,t+6),E=Wr(e,t+8),w=Wr(e,t+10),S=Wr(e,t+12),_=Wr(e,t+14);let C=i[0]+(8191&g),k=i[1]+(8191&(g>>>13|y<<3)),M=i[2]+(8191&(y>>>10|v<<6)),A=i[3]+(8191&(v>>>7|b<<9)),x=i[4]+(8191&(b>>>4|E<<12)),I=i[5]+(E>>>1&8191),R=i[6]+(8191&(E>>>14|w<<2)),P=i[7]+(8191&(w>>>11|S<<5)),O=i[8]+(8191&(S>>>8|_<<8)),T=i[9]+(_>>>5|r),N=0,L=N+C*s+k*(5*m)+M*(5*p)+A*(5*f)+x*(5*h);N=L>>>13,L&=8191,L+=I*(5*u)+R*(5*d)+P*(5*l)+O*(5*c)+T*(5*a),N+=L>>>13,L&=8191;let D=N+C*a+k*s+M*(5*m)+A*(5*p)+x*(5*f);N=D>>>13,D&=8191,D+=I*(5*h)+R*(5*u)+P*(5*d)+O*(5*l)+T*(5*c),N+=D>>>13,D&=8191;let B=N+C*c+k*a+M*s+A*(5*m)+x*(5*p);N=B>>>13,B&=8191,B+=I*(5*f)+R*(5*h)+P*(5*u)+O*(5*d)+T*(5*l),N+=B>>>13,B&=8191;let K=N+C*l+k*c+M*a+A*s+x*(5*m);N=K>>>13,K&=8191,K+=I*(5*p)+R*(5*f)+P*(5*h)+O*(5*u)+T*(5*d),N+=K>>>13,K&=8191;let j=N+C*d+k*l+M*c+A*a+x*s;N=j>>>13,j&=8191,j+=I*(5*m)+R*(5*p)+P*(5*f)+O*(5*h)+T*(5*u),N+=j>>>13,j&=8191;let $=N+C*u+k*d+M*l+A*c+x*a;N=$>>>13,$&=8191,$+=I*s+R*(5*m)+P*(5*p)+O*(5*f)+T*(5*h),N+=$>>>13,$&=8191;let U=N+C*h+k*u+M*d+A*l+x*c;N=U>>>13,U&=8191,U+=I*a+R*s+P*(5*m)+O*(5*p)+T*(5*f),N+=U>>>13,U&=8191;let H=N+C*f+k*h+M*u+A*d+x*l;N=H>>>13,H&=8191,H+=I*c+R*a+P*s+O*(5*m)+T*(5*p),N+=H>>>13,H&=8191;let F=N+C*p+k*f+M*h+A*u+x*d;N=F>>>13,F&=8191,F+=I*l+R*c+P*a+O*s+T*(5*m),N+=F>>>13,F&=8191;let q=N+C*m+k*p+M*f+A*h+x*u;N=q>>>13,q&=8191,q+=I*d+R*l+P*c+O*a+T*s,N+=q>>>13,q&=8191,N=(N<<2)+N|0,N=N+L|0,L=8191&N,N>>>=13,D+=N,i[0]=L,i[1]=D,i[2]=B,i[3]=K,i[4]=j,i[5]=$,i[6]=U,i[7]=H,i[8]=F,i[9]=q}finalize(){const{h:e,pad:t}=this,n=new Uint16Array(10);let r=e[1]>>>13;e[1]&=8191;for(let t=2;t<10;t++)e[t]+=r,r=e[t]>>>13,e[t]&=8191;e[0]+=5*r,r=e[0]>>>13,e[0]&=8191,e[1]+=r,r=e[1]>>>13,e[1]&=8191,e[2]+=r,n[0]=e[0]+5,r=n[0]>>>13,n[0]&=8191;for(let t=1;t<10;t++)n[t]=e[t]+r,r=n[t]>>>13,n[t]&=8191;n[9]-=8192;let i=(1^r)-1;for(let e=0;e<10;e++)n[e]&=i;i=~i;for(let t=0;t<10;t++)e[t]=e[t]&i|n[t];e[0]=65535&(e[0]|e[1]<<13),e[1]=65535&(e[1]>>>3|e[2]<<10),e[2]=65535&(e[2]>>>6|e[3]<<7),e[3]=65535&(e[3]>>>9|e[4]<<4),e[4]=65535&(e[4]>>>12|e[5]<<1|e[6]<<14),e[5]=65535&(e[6]>>>2|e[7]<<11),e[6]=65535&(e[7]>>>5|e[8]<<8),e[7]=65535&(e[8]>>>8|e[9]<<5);let o=e[0]+t[0];e[0]=65535&o;for(let n=1;n<8;n++)o=(e[n]+t[n]|0)+(o>>>16)|0,e[n]=65535&o;(0,zr.clean)(n)}update(e){(0,qr.exists)(this);const{buffer:t,blockLen:n}=this,r=(e=(0,zr.toBytes)(e)).length;for(let i=0;i<r;){const o=Math.min(n-this.pos,r-i);if(o!==n)t.set(e.subarray(i,i+o),this.pos),this.pos+=o,i+=o,this.pos===n&&(this.process(t,0,!1),this.pos=0);else for(;n<=r-i;i+=n)this.process(e,i)}return this}destroy(){(0,zr.clean)(this.h,this.r,this.buffer,this.pad)}digestInto(e){(0,qr.exists)(this),(0,qr.output)(e,this),this.finished=!0;const{buffer:t,h:n}=this;let{pos:r}=this;if(r){for(t[r++]=1;r<16;r++)t[r]=0;this.process(t,0,!0)}this.finalize();let i=0;for(let t=0;t<8;t++)e[i++]=n[t]>>>0,e[i++]=n[t]>>>8;return e}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const n=e.slice(0,t);return this.destroy(),n}}function Gr(e){const t=(t,n)=>e(n).update((0,zr.toBytes)(t)).digest(),n=e(new Uint8Array(32));return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=t=>e(t),t}Fr.poly1305=Gr((e=>new Vr(e))),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.xchacha20poly1305=e.chacha20poly1305=e._poly1305_aead=e.chacha12=e.chacha8=e.xchacha20=e.chacha20=e.chacha20orig=void 0,e.hchacha=s;const t=Rr,n=b,r=Fr,i=v;function o(e,n,r,i,o,s=20){let a=e[0],c=e[1],l=e[2],d=e[3],u=n[0],h=n[1],f=n[2],p=n[3],m=n[4],g=n[5],y=n[6],v=n[7],b=o,E=r[0],w=r[1],S=r[2],_=a,C=c,k=l,M=d,A=u,x=h,I=f,R=p,P=m,O=g,T=y,N=v,L=b,D=E,B=w,K=S;for(let e=0;e<s;e+=2)_=_+A|0,L=(0,t.rotl)(L^_,16),P=P+L|0,A=(0,t.rotl)(A^P,12),_=_+A|0,L=(0,t.rotl)(L^_,8),P=P+L|0,A=(0,t.rotl)(A^P,7),C=C+x|0,D=(0,t.rotl)(D^C,16),O=O+D|0,x=(0,t.rotl)(x^O,12),C=C+x|0,D=(0,t.rotl)(D^C,8),O=O+D|0,x=(0,t.rotl)(x^O,7),k=k+I|0,B=(0,t.rotl)(B^k,16),T=T+B|0,I=(0,t.rotl)(I^T,12),k=k+I|0,B=(0,t.rotl)(B^k,8),T=T+B|0,I=(0,t.rotl)(I^T,7),M=M+R|0,K=(0,t.rotl)(K^M,16),N=N+K|0,R=(0,t.rotl)(R^N,12),M=M+R|0,K=(0,t.rotl)(K^M,8),N=N+K|0,R=(0,t.rotl)(R^N,7),_=_+x|0,K=(0,t.rotl)(K^_,16),T=T+K|0,x=(0,t.rotl)(x^T,12),_=_+x|0,K=(0,t.rotl)(K^_,8),T=T+K|0,x=(0,t.rotl)(x^T,7),C=C+I|0,L=(0,t.rotl)(L^C,16),N=N+L|0,I=(0,t.rotl)(I^N,12),C=C+I|0,L=(0,t.rotl)(L^C,8),N=N+L|0,I=(0,t.rotl)(I^N,7),k=k+R|0,D=(0,t.rotl)(D^k,16),P=P+D|0,R=(0,t.rotl)(R^P,12),k=k+R|0,D=(0,t.rotl)(D^k,8),P=P+D|0,R=(0,t.rotl)(R^P,7),M=M+A|0,B=(0,t.rotl)(B^M,16),O=O+B|0,A=(0,t.rotl)(A^O,12),M=M+A|0,B=(0,t.rotl)(B^M,8),O=O+B|0,A=(0,t.rotl)(A^O,7);let j=0;i[j++]=a+_|0,i[j++]=c+C|0,i[j++]=l+k|0,i[j++]=d+M|0,i[j++]=u+A|0,i[j++]=h+x|0,i[j++]=f+I|0,i[j++]=p+R|0,i[j++]=m+P|0,i[j++]=g+O|0,i[j++]=y+T|0,i[j++]=v+N|0,i[j++]=b+L|0,i[j++]=E+D|0,i[j++]=w+B|0,i[j++]=S+K|0}function s(e,n,r,i){let o=e[0],s=e[1],a=e[2],c=e[3],l=n[0],d=n[1],u=n[2],h=n[3],f=n[4],p=n[5],m=n[6],g=n[7],y=r[0],v=r[1],b=r[2],E=r[3];for(let e=0;e<20;e+=2)o=o+l|0,y=(0,t.rotl)(y^o,16),f=f+y|0,l=(0,t.rotl)(l^f,12),o=o+l|0,y=(0,t.rotl)(y^o,8),f=f+y|0,l=(0,t.rotl)(l^f,7),s=s+d|0,v=(0,t.rotl)(v^s,16),p=p+v|0,d=(0,t.rotl)(d^p,12),s=s+d|0,v=(0,t.rotl)(v^s,8),p=p+v|0,d=(0,t.rotl)(d^p,7),a=a+u|0,b=(0,t.rotl)(b^a,16),m=m+b|0,u=(0,t.rotl)(u^m,12),a=a+u|0,b=(0,t.rotl)(b^a,8),m=m+b|0,u=(0,t.rotl)(u^m,7),c=c+h|0,E=(0,t.rotl)(E^c,16),g=g+E|0,h=(0,t.rotl)(h^g,12),c=c+h|0,E=(0,t.rotl)(E^c,8),g=g+E|0,h=(0,t.rotl)(h^g,7),o=o+d|0,E=(0,t.rotl)(E^o,16),m=m+E|0,d=(0,t.rotl)(d^m,12),o=o+d|0,E=(0,t.rotl)(E^o,8),m=m+E|0,d=(0,t.rotl)(d^m,7),s=s+u|0,y=(0,t.rotl)(y^s,16),g=g+y|0,u=(0,t.rotl)(u^g,12),s=s+u|0,y=(0,t.rotl)(y^s,8),g=g+y|0,u=(0,t.rotl)(u^g,7),a=a+h|0,v=(0,t.rotl)(v^a,16),f=f+v|0,h=(0,t.rotl)(h^f,12),a=a+h|0,v=(0,t.rotl)(v^a,8),f=f+v|0,h=(0,t.rotl)(h^f,7),c=c+l|0,b=(0,t.rotl)(b^c,16),p=p+b|0,l=(0,t.rotl)(l^p,12),c=c+l|0,b=(0,t.rotl)(b^c,8),p=p+b|0,l=(0,t.rotl)(l^p,7);let w=0;i[w++]=o,i[w++]=s,i[w++]=a,i[w++]=c,i[w++]=y,i[w++]=v,i[w++]=b,i[w++]=E}e.chacha20orig=(0,t.createCipher)(o,{counterRight:!1,counterLength:8,allowShortKeys:!0}),e.chacha20=(0,t.createCipher)(o,{counterRight:!1,counterLength:4,allowShortKeys:!1}),e.xchacha20=(0,t.createCipher)(o,{counterRight:!1,counterLength:8,extendNonceFn:s,allowShortKeys:!1}),e.chacha8=(0,t.createCipher)(o,{counterRight:!1,counterLength:4,rounds:8}),e.chacha12=(0,t.createCipher)(o,{counterRight:!1,counterLength:4,rounds:12});const a=new Uint8Array(16),c=(e,t)=>{e.update(t);const n=t.length%16;n&&e.update(a.subarray(n))},l=new Uint8Array(32);function d(e,t,n,o,s){const a=e(t,n,l),d=r.poly1305.create(a);s&&c(d,s),c(d,o);const u=new Uint8Array(16),h=(0,i.createView)(u);(0,i.setBigUint64)(h,0,BigInt(s?s.length:0),!0),(0,i.setBigUint64)(h,8,BigInt(o.length),!0),d.update(u);const f=d.digest();return(0,i.clean)(a,u),f}e._poly1305_aead=e=>(t,r,o)=>{const s=16;return(0,n.bytes)(t,32),(0,n.bytes)(r),{encrypt(a,c){const l=a.length,u=l+s;c?(0,n.bytes)(c,u):c=new Uint8Array(u),e(t,r,a,c,1);const h=d(e,t,r,c.subarray(0,-16),o);return c.set(h,l),(0,i.clean)(h),c},decrypt(a,c){const l=a.length,u=l-s;if(l<s)throw new Error("encrypted data must be at least 16 bytes");c?(0,n.bytes)(c,u):c=new Uint8Array(u);const h=a.subarray(0,-16),f=a.subarray(-16),p=d(e,t,r,h,o);if(!(0,i.equalBytes)(f,p))throw new Error("invalid tag");return e(t,r,h,c,1),(0,i.clean)(p),c}}},e.chacha20poly1305=(0,i.wrapCipher)({blockSize:64,nonceLength:12,tagLength:16},(0,e._poly1305_aead)(e.chacha20)),e.xchacha20poly1305=(0,i.wrapCipher)({blockSize:64,nonceLength:24,tagLength:16},(0,e._poly1305_aead)(e.xchacha20))}(Ir),Object.defineProperty(xr,"__esModule",{value:!0}),xr.xchacha20=void 0;var Yr,Zr,Jr,Qr=Ir;xr.xchacha20=function(e,t,n){return(0,Qr.xchacha20poly1305)(e,t,n)},function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.aesDecrypt=e.aesEncrypt=e.symDecrypt=e.symEncrypt=void 0;var t=v,n=N,r=Rn,i=xr,o=x,s=I;e.symEncrypt=function(e,t,n){return a(c,e,t,n)};function a(e,t,n,a){var c=(0,o.symmetricAlgorithm)();if("aes-256-gcm"===c)return e(r.aes256gcm,t,n,(0,o.symmetricNonceLength)(),s.AEAD_TAG_LENGTH,a);if("xchacha20"===c)return e(i.xchacha20,t,n,s.XCHACHA20_NONCE_LENGTH,s.AEAD_TAG_LENGTH,a);if("aes-256-cbc"===c)return e(r.aes256cbc,t,n,16,0);throw new Error("Not implemented")}function c(e,r,i,o,s,a){var c=(0,n.randomBytes)(o),l=e(r,c,a).encrypt(i);if(0===s)return(0,t.concatBytes)(c,l);var d=l.length-s,u=l.subarray(0,d),h=l.subarray(d);return(0,t.concatBytes)(c,h,u)}function l(e,n,r,i,o,s){var a=r.subarray(0,i),c=e(n,Uint8Array.from(a),s),l=r.subarray(i);if(0===o)return c.decrypt(l);var d=l.subarray(0,o),u=l.subarray(o);return c.decrypt((0,t.concatBytes)(u,d))}e.symDecrypt=function(e,t,n){return a(l,e,t,n)},e.aesEncrypt=e.symEncrypt,e.aesDecrypt=e.symDecrypt}(In),Yr=O,Zr=p&&p.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),Jr=p&&p.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||Zr(t,e,n)},Object.defineProperty(Yr,"__esModule",{value:!0}),Jr(T,Yr),Jr(En,Yr),Jr(bn,Yr),Jr(In,Yr);var Xr={};Object.defineProperty(Xr,"__esModule",{value:!0}),Xr.PublicKey=void 0;var ei=v,ti=O,ni=function(){function e(e){this.data=(0,ti.convertPublicKeyFormat)(e,!0)}return e.fromHex=function(t){return new e((0,ti.hexToPublicKey)(t))},Object.defineProperty(e.prototype,"uncompressed",{get:function(){return Buffer.from((0,ti.convertPublicKeyFormat)(this.data,!1))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"compressed",{get:function(){return Buffer.from(this.data)},enumerable:!1,configurable:!0}),e.prototype.toHex=function(e){return void 0===e&&(e=!0),(0,ei.bytesToHex)(e?this.data:this.uncompressed)},e.prototype.decapsulate=function(e,t){void 0===t&&(t=!1);var n=t?this.data:this.uncompressed,r=e.multiply(this,t);return(0,ti.getSharedKey)(n,r)},e.prototype.equals=function(e){return(0,ei.equalBytes)(this.data,e.data)},e}();Xr.PublicKey=ni,Object.defineProperty(P,"__esModule",{value:!0}),P.PrivateKey=void 0;var ri=v,ii=O,oi=Xr,si=function(){function e(e){if(void 0===e)this.data=(0,ii.getValidSecret)();else{if(!(0,ii.isValidPrivateKey)(e))throw new Error("Invalid private key");this.data=e}this.publicKey=new oi.PublicKey((0,ii.getPublicKey)(this.data))}return e.fromHex=function(t){return new e((0,ii.decodeHex)(t))},Object.defineProperty(e.prototype,"secret",{get:function(){return Buffer.from(this.data)},enumerable:!1,configurable:!0}),e.prototype.toHex=function(){return(0,ri.bytesToHex)(this.data)},e.prototype.encapsulate=function(e,t){void 0===t&&(t=!1);var n=t?this.publicKey.compressed:this.publicKey.uncompressed,r=this.multiply(e,t);return(0,ii.getSharedKey)(n,r)},e.prototype.multiply=function(e,t){return void 0===t&&(t=!1),(0,ii.getSharedPoint)(this.data,e.compressed,t)},e.prototype.equals=function(e){return(0,ri.equalBytes)(this.data,e.data)},e}();P.PrivateKey=si,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.PublicKey=e.PrivateKey=void 0;var t=P;Object.defineProperty(e,"PrivateKey",{enumerable:!0,get:function(){return t.PrivateKey}});var n=Xr;Object.defineProperty(e,"PublicKey",{enumerable:!0,get:function(){return n.PublicKey}})}(R),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.utils=e.PublicKey=e.PrivateKey=e.ECIES_CONFIG=void 0,e.encrypt=function(e,o){var s=new r.PrivateKey,a=e instanceof Uint8Array?new r.PublicKey(e):r.PublicKey.fromHex(e),c=s.encapsulate(a,(0,n.isHkdfKeyCompressed)()),l=(0,n.isEphemeralKeyCompressed)()?s.publicKey.compressed:s.publicKey.uncompressed,d=(0,i.symEncrypt)(c,o);return Buffer.from((0,t.concatBytes)(l,d))},e.decrypt=function(e,t){var o=e instanceof Uint8Array?new r.PrivateKey(e):r.PrivateKey.fromHex(e),s=(0,n.ephemeralKeySize)(),a=new r.PublicKey(t.subarray(0,s)),c=t.subarray(s),l=a.decapsulate(o,(0,n.isHkdfKeyCompressed)());return Buffer.from((0,i.symDecrypt)(l,c))};var t=v,n=x,r=R,i=O;var o=x;Object.defineProperty(e,"ECIES_CONFIG",{enumerable:!0,get:function(){return o.ECIES_CONFIG}});var s=R;Object.defineProperty(e,"PrivateKey",{enumerable:!0,get:function(){return s.PrivateKey}}),Object.defineProperty(e,"PublicKey",{enumerable:!0,get:function(){return s.PublicKey}}),e.utils={aesEncrypt:i.aesEncrypt,aesDecrypt:i.aesDecrypt,symEncrypt:i.symEncrypt,symDecrypt:i.symDecrypt,decodeHex:i.decodeHex,getValidSecret:i.getValidSecret,remove0x:i.remove0x}}(y);const ai=t("KeyExchange:Layer"),ci=t("SocketService:Layer"),li=t("Ecies:Layer"),di=t("RemoteCommunication:Layer");ai.color="##95c44e",ci.color="#f638d7",li.color="#465b9c",di.color="#47a2be";const ui={KeyExchange:ai,SocketService:ci,Ecies:li,RemoteCommunication:di};let hi,fi=[],pi=[];const mi=(t,n)=>f(void 0,void 0,void 0,(function*(){hi=n,pi.push(t),function(t){return f(this,void 0,void 0,(function*(){if(!hi||!t)return;!function(){const e=pi;pi=fi,fi=e}();const n=hi.endsWith("/")?`${hi}evt`:`${hi}/evt`,r=Object.assign({},t);if(delete r.params,t.params)for(const[e,n]of Object.entries(t.params))r[e]=n;const i=JSON.stringify(r);ui.RemoteCommunication(`[sendBufferedEvents] Sending ${fi.length} analytics events to ${n}`);try{const t=yield e(n,{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json"},body:i}),r=yield t.text();ui.RemoteCommunication(`[sendBufferedEvents] Response: ${r}`),fi.length=0}catch(n){}}))}(t).catch((()=>{}))}));class gi{constructor(e){this.enabled=!0,(null==e?void 0:e.debug)&&t.enable("Ecies:Layer"),this.ecies=(null==e?void 0:e.privateKey)?y.PrivateKey.fromHex(e.privateKey):new y.PrivateKey,ui.Ecies("[ECIES constructor()] initialized secret: ",this.ecies.toHex()),ui.Ecies("[ECIES constructor()] initialized public: ",this.ecies.publicKey.toHex()),ui.Ecies("[ECIES constructor()] init with",this)}generateECIES(){this.ecies=new y.PrivateKey}getPublicKey(){return this.ecies.publicKey.toHex()}encrypt(e,t){let n=e;if(this.enabled)try{ui.Ecies("[ECIES: encrypt()] using otherPublicKey",t);const i=r.from(e),o=y.encrypt(t,i);n=r.from(o).toString("base64")}catch(n){throw ui.Ecies("[ECIES: encrypt()] error encrypt:",n),ui.Ecies("[ECIES: encrypt()] private: ",this.ecies.toHex()),ui.Ecies("[ECIES: encrypt()] data: ",e),ui.Ecies("[ECIES: encrypt()] otherkey: ",t),n}return n}decrypt(e){let t=e;if(this.enabled)try{ui.Ecies("[ECIES: decrypt()] using privateKey",this.ecies.toHex());const n=r.from(e.toString(),"base64");t=y.decrypt(this.ecies.toHex(),n).toString()}catch(t){throw ui.Ecies("[ECIES: decrypt()] error decrypt",t),ui.Ecies("[ECIES: decrypt()] private: ",this.ecies.toHex()),ui.Ecies("[ECIES: decrypt()] encryptedData: ",e),t}return t}getKeyInfo(){return{private:this.ecies.toHex(),public:this.ecies.publicKey.toHex()}}toString(){ui.Ecies("[ECIES: toString()]",this.getKeyInfo())}}var yi={name:"@metamask/sdk-communication-layer",version:"0.32.0",description:"",homepage:"https://github.com/MetaMask/metamask-sdk#readme",bugs:{url:"https://github.com/MetaMask/metamask-sdk/issues"},repository:{type:"git",url:"https://github.com/MetaMask/metamask-sdk.git",directory:"packages/sdk-communication-layer"},main:"dist/node/cjs/metamask-sdk-communication-layer.js",unpkg:"dist/browser/umd/metamask-sdk-communication-layer.js",module:"dist/node/es/metamask-sdk-communication-layer.js",browser:"dist/browser/es/metamask-sdk-communication-layer.js","react-native":"dist/react-native/es/metamask-sdk-communication-layer.js",types:"dist/types/src/index.d.ts",files:["/dist"],scripts:{"build:types":"tsc --project tsconfig.build.json --emitDeclarationOnly --outDir dist/types","build:clean":"yarn clean && yarn build",build:"yarn build:types && rollup -c --bundleConfigAsCjs","build:dev":"yarn build:types && NODE_ENV=dev rollup -c --bundleConfigAsCjs",dev:'concurrently "tsc --watch" "rollup -c --bundleConfigAsCjs -w"',"build:post-tsc":"echo 'N/A'","build:pre-tsc":"echo 'N/A'",size:"size-limit",clean:"rimraf ./dist",lint:"yarn lint:eslint && yarn lint:misc --check","lint:changelog":"../../scripts/validate-changelog.sh @metamask/sdk-communication-layer","lint:eslint":"eslint . --cache --ext js,ts","lint:fix":"yarn lint:eslint --fix && yarn lint:misc --write","lint:misc":"prettier '**/*.json' '**/*.md' '!CHANGELOG.md' --ignore-path ../../.gitignore","publish:preview":"yarn npm publish --tag preview",prepack:"../../scripts/prepack.sh",reset:"yarn clean && rimraf ./node_modules/",test:'jest --testPathIgnorePatterns "/e2e/"',"test:e2e":'jest --testPathPattern "/e2e/"',"test:coverage":"jest --coverage","test:ci":'jest --coverage --passWithNoTests --setupFilesAfterEnv ./jest-preload.js --testPathIgnorePatterns "/e2e/"',"test:dev":"jest",watch:"rollup -c --bundleConfigAsCjs -w"},dependencies:{bufferutil:"^4.0.8","date-fns":"^2.29.3",debug:"^4.3.4","utf-8-validate":"^5.0.2",uuid:"^8.3.2"},devDependencies:{"@jest/globals":"^29.3.1","@lavamoat/allow-scripts":"^2.3.1","@metamask/auto-changelog":"3.1.0","@metamask/eslint-config":"^6.0.0","@metamask/eslint-config-nodejs":"^6.0.0","@metamask/eslint-config-typescript":"^6.0.0","@rollup/plugin-commonjs":"^25.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.0.2","@rollup/plugin-replace":"^6.0.1","@rollup/plugin-terser":"^0.4.4","@size-limit/preset-big-lib":"^11.0.2","@types/jest":"^29.2.4","@types/node":"^20.1.3","@types/uuid":"^9.0.0","@typescript-eslint/eslint-plugin":"^4.26.0","@typescript-eslint/parser":"^4.26.0",concurrently:"^9.1.2","cross-fetch":"^4.0.0",eciesjs:"^0.4.11",eslint:"^7.30.0","eslint-config-prettier":"^8.3.0","eslint-plugin-import":"^2.23.4","eslint-plugin-jest":"^24.4.0","eslint-plugin-jsdoc":"^36.1.0","eslint-plugin-node":"^11.1.0","eslint-plugin-prettier":"^3.4.0",eventemitter2:"^6.4.9",jest:"^29.3.1",prettier:"^2.3.0",rimraf:"^3.0.2",rollup:"^4.26.0","rollup-plugin-jscc":"^2.0.0","rollup-plugin-natives":"^0.7.5","rollup-plugin-node-builtins":"^2.1.2","rollup-plugin-node-globals":"^1.4.0","rollup-plugin-peer-deps-external":"^2.2.4","rollup-plugin-polyfill-node":"^0.13.0","rollup-plugin-sizes":"^1.0.6","rollup-plugin-typescript2":"^0.31.2","rollup-plugin-visualizer":"^5.12.0","size-limit":"^11.1.6","socket.io-client":"^4.5.1","stream-browserify":"^3.0.0","ts-jest":"^29.0.3","ts-node":"^10.9.1",typescript:"^5.6.3"},peerDependencies:{"cross-fetch":"^4.0.0",eciesjs:"*",eventemitter2:"^6.4.9","readable-stream":"^3.6.2","socket.io-client":"^4.5.1"},publishConfig:{access:"public",registry:"https://registry.npmjs.org/"},lavamoat:{allowScripts:{"@lavamoat/preinstall-always-fail":!1,canvas:!0,"eciesjs>secp256k1":!1,"socket.io-client>engine.io-client>ws>bufferutil":!1,"socket.io-client>engine.io-client>ws>utf-8-validate":!1,bufferutil:!1,"utf-8-validate":!1}}};const vi="https://metamask-sdk.api.cx.metamask.io/",bi=["websocket"],Ei=6048e5,wi=3e3,Si={METAMASK_GETPROVIDERSTATE:"metamask_getProviderState",ETH_REQUESTACCOUNTS:"eth_requestAccounts"};function _i(e){const{context:t}=e;ui.RemoteCommunication(`[RemoteCommunication: clean()] context=${t}`),e.channelConfig=void 0,e.ready=!1,e.originatorConnectStarted=!1}var Ci,ki,Mi,Ai,xi,Ii,Ri;(Ri=Ci||(Ci={})).DISCONNECTED="disconnected",Ri.WAITING="waiting",Ri.TIMEOUT="timeout",Ri.LINKED="linked",Ri.PAUSED="paused",Ri.TERMINATED="terminated",function(e){e.KEY_INFO="key_info",e.SERVICE_STATUS="service_status",e.PROVIDER_UPDATE="provider_update",e.RPC_UPDATE="rpc_update",e.KEYS_EXCHANGED="keys_exchanged",e.JOIN_CHANNEL="join_channel",e.PUBLIC_KEY="public_key",e.CHANNEL_CREATED="channel_created",e.CLIENTS_CONNECTED="clients_connected",e.CLIENTS_DISCONNECTED="clients_disconnected",e.CLIENTS_WAITING="clients_waiting",e.CLIENTS_READY="clients_ready",e.REJECTED="rejected",e.WALLET_INIT="wallet_init",e.CHANNEL_PERSISTENCE="channel_persistence",e.CONFIG="config",e.MESSAGE_ACK="ack",e.SOCKET_DISCONNECTED="socket_disconnected",e.SOCKET_RECONNECT="socket_reconnect",e.OTP="otp",e.SDK_RPC_CALL="sdk_rpc_call",e.AUTHORIZED="authorized",e.CONNECTION_STATUS="connection_status",e.MESSAGE="message",e.TERMINATE="terminate"}(ki||(ki={})),(Mi||(Mi={})).KEY_EXCHANGE="key_exchange",function(e){e.KEY_HANDSHAKE_START="key_handshake_start",e.KEY_HANDSHAKE_CHECK="key_handshake_check",e.KEY_HANDSHAKE_SYN="key_handshake_SYN",e.KEY_HANDSHAKE_SYNACK="key_handshake_SYNACK",e.KEY_HANDSHAKE_ACK="key_handshake_ACK",e.KEY_HANDSHAKE_WALLET="key_handshake_wallet",e.KEY_HANDSHAKE_NONE="none"}(Ai||(Ai={}));class Pi extends s{constructor({communicationLayer:e,otherPublicKey:t,context:n,ecies:r,logging:i}){super(),this.keysExchanged=!1,this.step=Ai.KEY_HANDSHAKE_NONE,this.debug=!1,this.context=n,this.communicationLayer=e,(null==r?void 0:r.privateKey)&&t&&(ui.KeyExchange(`[KeyExchange: constructor()] otherPubKey=${t} set keysExchanged to true!`,r),this.keysExchanged=!0),this.myECIES=new gi(Object.assign(Object.assign({},r),{debug:null==i?void 0:i.eciesLayer})),this.communicationLayer.state.eciesInstance=this.myECIES,this.myPublicKey=this.myECIES.getPublicKey(),this.debug=!0===(null==i?void 0:i.keyExchangeLayer),t&&this.setOtherPublicKey(t),this.communicationLayer.on(Mi.KEY_EXCHANGE,this.onKeyExchangeMessage.bind(this))}onKeyExchangeMessage(e){const{relayPersistence:t}=this.communicationLayer.remote.state;if(ui.KeyExchange(`[KeyExchange: onKeyExchangeMessage()] context=${this.context} keysExchanged=${this.keysExchanged} relayPersistence=${t}`,e),t)return void ui.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Ignoring key exchange message because relay persistence is activated");const{message:n}=e;this.keysExchanged&&ui.KeyExchange(`[KeyExchange: onKeyExchangeMessage()] context=${this.context} received handshake while already exchanged. step=${this.step} otherPubKey=${this.otherPublicKey}`),this.emit(ki.KEY_INFO,n.type),n.type===Ai.KEY_HANDSHAKE_SYN?(this.checkStep([Ai.KEY_HANDSHAKE_NONE,Ai.KEY_HANDSHAKE_ACK]),ui.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_SYN",n),n.pubkey&&this.setOtherPublicKey(n.pubkey),this.communicationLayer.sendMessage({type:Ai.KEY_HANDSHAKE_SYNACK,pubkey:this.myPublicKey}).catch((e=>{ui.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Error sending KEY_HANDSHAKE_SYNACK",e)})),this.setStep(Ai.KEY_HANDSHAKE_ACK)):n.type===Ai.KEY_HANDSHAKE_SYNACK?(this.checkStep([Ai.KEY_HANDSHAKE_SYNACK,Ai.KEY_HANDSHAKE_ACK,Ai.KEY_HANDSHAKE_NONE]),ui.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_SYNACK"),n.pubkey&&this.setOtherPublicKey(n.pubkey),this.communicationLayer.sendMessage({type:Ai.KEY_HANDSHAKE_ACK}).catch((e=>{ui.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Error sending KEY_HANDSHAKE_ACK",e)})),this.keysExchanged=!0,this.setStep(Ai.KEY_HANDSHAKE_ACK),this.emit(ki.KEYS_EXCHANGED)):n.type===Ai.KEY_HANDSHAKE_ACK&&(ui.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_ACK set keysExchanged to true!"),this.checkStep([Ai.KEY_HANDSHAKE_ACK,Ai.KEY_HANDSHAKE_NONE]),this.keysExchanged=!0,this.setStep(Ai.KEY_HANDSHAKE_ACK),this.emit(ki.KEYS_EXCHANGED))}resetKeys(e){this.clean(),this.myECIES=new gi(e)}clean(){ui.KeyExchange(`[KeyExchange: clean()] context=${this.context} reset handshake state`),this.setStep(Ai.KEY_HANDSHAKE_NONE),this.emit(ki.KEY_INFO,this.step),this.keysExchanged=!1}start({isOriginator:e,force:t}){const{relayPersistence:n,protocolVersion:r}=this.communicationLayer.remote.state,i=r>=2;n?ui.KeyExchange("[KeyExchange: start()] Ignoring key exchange message because relay persistence is activated"):(ui.KeyExchange(`[KeyExchange: start()] context=${this.context} protocolVersion=${r} isOriginator=${e} step=${this.step} force=${t} relayPersistence=${n} keysExchanged=${this.keysExchanged}`),e?!(this.keysExchanged||this.step!==Ai.KEY_HANDSHAKE_NONE&&this.step!==Ai.KEY_HANDSHAKE_SYNACK)||t?(ui.KeyExchange(`[KeyExchange: start()] context=${this.context} -- start key exchange (force=${t}) -- step=${this.step}`,this.step),this.clean(),this.setStep(Ai.KEY_HANDSHAKE_SYNACK),this.communicationLayer.sendMessage({type:Ai.KEY_HANDSHAKE_SYN,pubkey:this.myPublicKey,v:2}).catch((e=>{ui.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_SYN",e)}))):ui.KeyExchange(`[KeyExchange: start()] context=${this.context} -- key exchange already ${this.keysExchanged?"done":"in progress"} -- aborted.`,this.step):this.keysExchanged&&!0!==t?ui.KeyExchange("[KeyExchange: start()] don't send KEY_HANDSHAKE_START -- exchange already done."):i?this.communicationLayer.sendMessage({type:Ai.KEY_HANDSHAKE_SYNACK,pubkey:this.myPublicKey,v:2}).catch((e=>{ui.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_SYNACK",e)})):(this.communicationLayer.sendMessage({type:Ai.KEY_HANDSHAKE_START}).catch((e=>{ui.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_START",e)})),this.clean()))}setStep(e){this.step=e,this.emit(ki.KEY_INFO,e)}checkStep(e){e.length>0&&e.indexOf(this.step.toString())}setRelayPersistence({localKey:e,otherKey:t}){this.otherPublicKey=t,this.myECIES=new gi({privateKey:e,debug:this.debug}),this.keysExchanged=!0}setKeysExchanged(e){this.keysExchanged=e}areKeysExchanged(){return this.keysExchanged}getMyPublicKey(){return this.myPublicKey}getOtherPublicKey(){return this.otherPublicKey}setOtherPublicKey(e){ui.KeyExchange("[KeyExchange: setOtherPubKey()]",e),this.otherPublicKey=e}encryptMessage(e){if(!this.otherPublicKey)throw new Error("encryptMessage: Keys not exchanged - missing otherPubKey");return this.myECIES.encrypt(e,this.otherPublicKey)}decryptMessage(e){if(!this.otherPublicKey)throw new Error("decryptMessage: Keys not exchanged - missing otherPubKey");return this.myECIES.decrypt(e)}getKeyInfo(){return{ecies:Object.assign(Object.assign({},this.myECIES.getKeyInfo()),{otherPubKey:this.otherPublicKey}),step:this.step,keysExchanged:this.areKeysExchanged()}}toString(){const e={keyInfo:this.getKeyInfo(),keysExchanged:this.keysExchanged,step:this.step};return JSON.stringify(e)}}!function(e){e.TERMINATE="terminate",e.ANSWER="answer",e.OFFER="offer",e.CANDIDATE="candidate",e.JSONRPC="jsonrpc",e.WALLET_INFO="wallet_info",e.WALLET_INIT="wallet_init",e.ORIGINATOR_INFO="originator_info",e.PAUSE="pause",e.OTP="otp",e.AUTHORIZED="authorized",e.PING="ping",e.READY="ready"}(xi||(xi={})),function(e){e.REQUEST="sdk_connect_request_started",e.REQUEST_MOBILE="sdk_connect_request_started_mobile",e.RECONNECT="sdk_reconnect_request_started",e.CONNECTED="sdk_connection_established",e.CONNECTED_MOBILE="sdk_connection_established_mobile",e.AUTHORIZED="sdk_connection_authorized",e.REJECTED="sdk_connection_rejected",e.TERMINATED="sdk_connection_terminated",e.DISCONNECTED="sdk_disconnected",e.SDK_USE_EXTENSION="sdk_use_extension",e.SDK_RPC_REQUEST="sdk_rpc_request",e.SDK_RPC_REQUEST_RECEIVED="sdk_rpc_request_received",e.SDK_RPC_REQUEST_DONE="sdk_rpc_request_done",e.SDK_EXTENSION_UTILIZED="sdk_extension_utilized",e.SDK_USE_INAPP_BROWSER="sdk_use_inapp_browser"}(Ii||(Ii={}));const Oi=(e,t,n)=>f(void 0,void 0,void 0,(function*(){var r,i,o,s,a,c;const{remote:l,state:d}=e,{channelId:u,isOriginator:h}=d;if("error_terminated"===t)return ui.SocketService(`handleJoinChannelResults: Channel ${u} terminated`),void e.emit(ki.TERMINATE);if(!n)return void ui.SocketService(`handleJoinChannelResults: No result for channel ${u}`);const{persistence:f,walletKey:p,rejected:m}=n;if(ui.SocketService(`handleJoinChannelResults: Channel ${u} persistence=${f} walletKey=${p} rejected=${m}`),m)return ui.SocketService(`handleJoinChannelResults: Channel ${u} rejected`),yield e.remote.disconnect({terminate:!0}),e.remote.emit(ki.REJECTED,{channelId:u}),void e.remote.emitServiceStatusEvent();if(p&&!(null===(r=l.state.channelConfig)||void 0===r?void 0:r.otherKey)){e.getKeyExchange().setOtherPublicKey(p),null===(i=e.state.keyExchange)||void 0===i||i.setKeysExchanged(!0),l.state.ready=!0,l.state.authorized=!0,l.emit(ki.AUTHORIZED);const{communicationLayer:t,storageManager:n}=l.state,r=Object.assign(Object.assign({},l.state.channelConfig),{channelId:null!==(o=l.state.channelId)&&void 0!==o?o:"",validUntil:Date.now()+Ei,localKey:null==t?void 0:t.getKeyInfo().ecies.private,otherKey:p});e.sendMessage({type:Ai.KEY_HANDSHAKE_ACK}).catch((e=>{})),null===(s=e.state.socket)||void 0===s||s.emit(xi.PING,{id:u,clientType:h?"dapp":"wallet",context:"on_channel_reconnect",message:""}),yield null==n?void 0:n.persistChannelConfig(r),l.emitServiceStatusEvent(),l.setConnectionStatus(Ci.LINKED)}f&&(e.emit(ki.CHANNEL_PERSISTENCE),null===(a=e.state.keyExchange)||void 0===a||a.setKeysExchanged(!0),l.state.ready=!0,l.state.authorized=!0,l.emit(ki.AUTHORIZED),mi(Object.assign(Object.assign({id:null!=u?u:"",event:h?Ii.CONNECTED:Ii.CONNECTED_MOBILE},e.remote.state.originatorInfo),{sdkVersion:e.remote.state.sdkVersion,commLayer:e.state.communicationLayerPreference,commLayerVersion:yi.version,walletVersion:null===(c=e.remote.state.walletInfo)||void 0===c?void 0:c.version}),d.communicationServerUrl).catch((e=>{})))})),Ti=e=>new Promise((t=>{setTimeout(t,e)})),Ni=(e,t,...n)=>f(void 0,[e,t,...n],void 0,(function*(e,t,n=200){let r;const i=Date.now();let o=!1;for(;!o;){if(o=Date.now()-i>3e5,r=t[e],void 0!==r.elapsedTime)return r;yield Ti(n)}throw new Error(`RPC ${e} timed out`)})),Li=e=>f(void 0,void 0,void 0,(function*(){const{state:t}=e,{socket:n,channelId:r,context:i,isOriginator:o,isReconnecting:s}=t;if(s)return ui.SocketService("[SocketService: reconnectSocket()] Reconnection already in progress, skipping",e),!1;if(!n)return ui.SocketService("[SocketService: reconnectSocket()] socket is not defined",e),!1;if(!r)return!1;const{connected:a}=n;t.isReconnecting=!0,t.reconnectionAttempts=0,ui.SocketService(`[SocketService: reconnectSocket()] connected=${a} trying to reconnect after socketio disconnection`,e);try{for(;3>t.reconnectionAttempts;){if(ui.SocketService(`[SocketService: reconnectSocket()] Attempt ${t.reconnectionAttempts+1} of 3`,e),yield Ti(200),n.connected)return ui.SocketService("Socket already connected --- ping to retrieve messages"),n.emit(xi.PING,{id:r,clientType:o?"dapp":"wallet",context:"on_channel_config",message:""}),!0;t.resumed=!0,n.connect(),e.emit(ki.SOCKET_RECONNECT);try{if(yield new Promise(((t,s)=>{n.emit(ki.JOIN_CHANNEL,{channelId:r,context:`${i}connect_again`,clientType:o?"dapp":"wallet"},((n,r)=>f(void 0,void 0,void 0,(function*(){try{yield Oi(e,n,r),t()}catch(e){s(e)}}))))})),yield Ti(100),n.connected)return ui.SocketService(`Reconnection successful on attempt ${t.reconnectionAttempts+1}`),!0}catch(e){ui.SocketService(`Error during reconnection attempt ${t.reconnectionAttempts+1}:`,e)}t.reconnectionAttempts+=1,3>t.reconnectionAttempts&&(yield Ti(200))}return ui.SocketService("Failed to reconnect after 3 attempts"),!1}finally{t.isReconnecting=!1,t.reconnectionAttempts=0}}));function Di(e,t){return f(this,void 0,void 0,(function*(){var n;const r=null===(n=e.state.keyExchange)||void 0===n?void 0:n.encryptMessage(JSON.stringify(t)),i={id:e.state.channelId,context:e.state.context,clientType:e.state.isOriginator?"dapp":"wallet",message:r,plaintext:e.state.hasPlaintext?JSON.stringify(t):void 0};return ui.SocketService(`[SocketService: encryptAndSendMessage()] context=${e.state.context}`,i),t.type===xi.TERMINATE&&(e.state.manualDisconnect=!0),new Promise(((t,n)=>{var r;null===(r=e.state.socket)||void 0===r||r.emit(ki.MESSAGE,i,((e,r)=>{var i;e&&(ui.SocketService(`[SocketService: encryptAndSendMessage()] error=${e}`),n(e)),ui.SocketService("[encryptAndSendMessage] response",r),t(null!==(i=null==r?void 0:r.success)&&void 0!==i&&i)}))}))}))}var Bi;!function(e){e.RPC_CHECK="rpcCheck",e.SKIPPED_RPC="skippedRpc"}(Bi||(Bi={}));const Ki=["eth_sendTransaction","eth_signTypedData","eth_signTransaction","personal_sign","wallet_requestPermissions","wallet_switchEthereumChain","eth_signTypedData_v3","eth_signTypedData_v4","metamask_connectSign","metamask_connectWith","metamask_batch"].map((e=>e.toLowerCase()));const ji=[{event:ki.CLIENTS_CONNECTED,handler:function(e,t){return n=>f(this,void 0,void 0,(function*(){var n,r,i,o,s,a,c,l,d,u,h;const f=null!==(r=null===(n=e.remote.state.channelConfig)||void 0===n?void 0:n.relayPersistence)&&void 0!==r&&r;if(ui.SocketService(`[SocketService: handleClientsConnected()] context=${e.state.context} on 'clients_connected-${t}' relayPersistence=${f} resumed=${e.state.resumed}  clientsPaused=${e.state.clientsPaused} keysExchanged=${null===(i=e.state.keyExchange)||void 0===i?void 0:i.areKeysExchanged()} isOriginator=${e.state.isOriginator}`),e.emit(ki.CLIENTS_CONNECTED,{isOriginator:e.state.isOriginator,keysExchanged:null===(o=e.state.keyExchange)||void 0===o?void 0:o.areKeysExchanged(),context:e.state.context}),e.state.resumed)e.state.isOriginator||(ui.SocketService(`[SocketService: handleClientsConnected()] context=${e.state.context} 'clients_connected' / keysExchanged=${null===(s=e.state.keyExchange)||void 0===s?void 0:s.areKeysExchanged()} -- backward compatibility`),null===(a=e.state.keyExchange)||void 0===a||a.start({isOriginator:null!==(c=e.state.isOriginator)&&void 0!==c&&c})),e.state.resumed=!1;else if(e.state.clientsPaused)ui.SocketService("[SocketService: handleClientsConnected()] 'clients_connected' skip sending originatorInfo on pause");else if(!e.state.isOriginator){const t=!f;ui.SocketService(`[SocketService: handleClientsConnected()] context=${e.state.context} on 'clients_connected' / keysExchanged=${null===(l=e.state.keyExchange)||void 0===l?void 0:l.areKeysExchanged()} -- force=${t} -- backward compatibility`),ui.SocketService(`[SocketService: handleClientsConnected()] context=${e.state.context} on 'clients_connected' / keysExchanged=${null===(d=e.state.keyExchange)||void 0===d?void 0:d.areKeysExchanged()} -- force=${t} -- backward compatibility`),null===(u=e.state.keyExchange)||void 0===u||u.start({isOriginator:null!==(h=e.state.isOriginator)&&void 0!==h&&h,force:t})}e.state.clientsConnected=!0,e.state.clientsPaused=!1}))}},{event:ki.CHANNEL_CREATED,handler:function(e,t){return n=>{ui.SocketService(`[SocketService: handleChannelCreated()] context=${e.state.context} on 'channel_created-${t}'`,n),e.emit(ki.CHANNEL_CREATED,n)}}},{event:ki.CLIENTS_DISCONNECTED,handler:function(e,t){return()=>{var n;e.state.clientsConnected=!1,ui.SocketService(`[SocketService: handlesClientsDisconnected()] context=${e.state.context} on 'clients_disconnected-${t}'`),e.remote.state.relayPersistence?ui.SocketService(`[SocketService: handlesClientsDisconnected()] context=${e.state.context} on 'clients_disconnected-${t}' - relayPersistence enabled, skipping key exchange cleanup.`):(e.state.isOriginator&&!e.state.clientsPaused&&(null===(n=e.state.keyExchange)||void 0===n||n.clean()),e.emit(ki.CLIENTS_DISCONNECTED,t))}}},{event:ki.CONFIG,handler:function(e,t){return n=>f(this,void 0,void 0,(function*(){var r,i,o;ui.SocketService(`[SocketService: handleChannelConfig()] update relayPersistence on 'config-${t}'`,n);const{persistence:s,walletKey:a}=n;e.state.isOriginator&&e.remote.state.channelConfig?(n.walletKey&&!e.remote.state.channelConfig.otherKey&&(ui.SocketService(`Setting wallet key ${a}`),e.remote.state.channelConfig.otherKey=a,e.getKeyExchange().setOtherPublicKey(n.walletKey),null===(r=e.state.keyExchange)||void 0===r||r.setKeysExchanged(!0),yield e.remote.sendMessage({type:Ai.KEY_HANDSHAKE_ACK}),yield e.remote.sendMessage({type:xi.PING}),yield null===(i=e.remote.state.storageManager)||void 0===i?void 0:i.persistChannelConfig(e.remote.state.channelConfig)),!0!==s||e.remote.state.channelConfig.relayPersistence||(ui.SocketService(`Setting relay persistence ${s}`),e.remote.state.channelConfig.relayPersistence=s,e.remote.state.relayPersistence=!0,e.remote.emit(ki.CHANNEL_PERSISTENCE),e.remote.state.authorized=!0,e.remote.state.ready=!0,e.remote.emit(ki.AUTHORIZED),yield null===(o=e.remote.state.storageManager)||void 0===o?void 0:o.persistChannelConfig(e.remote.state.channelConfig))):e.state.isOriginator||n.persistence&&(e.remote.state.relayPersistence=!0,e.remote.emit(ki.CHANNEL_PERSISTENCE))}))}},{event:ki.MESSAGE,handler:function(e,t){return n=>{var r,i,o,s,a,c,l,d,u,h,f,p,m,g,y,v,b,E;const{ackId:w,message:S,error:_}=n,C=null!==(r=e.remote.state.relayPersistence)&&void 0!==r&&r;if(ui.SocketService(`[SocketService handleMessage()]  relayPersistence=${C}  context=${e.state.context} on 'message' ${t} keysExchanged=${null===(i=e.state.keyExchange)||void 0===i?void 0:i.areKeysExchanged()}`,n),_)throw ui.SocketService(`\n      [SocketService handleMessage()] context=${e.state.context}::on 'message' error=${_}`),new Error(_);const k="string"==typeof S;if(!k&&(null==S?void 0:S.type)===Ai.KEY_HANDSHAKE_START){if(C)return;return ui.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' received HANDSHAKE_START isOriginator=${e.state.isOriginator}`,S),void(null===(o=e.state.keyExchange)||void 0===o||o.start({isOriginator:null!==(s=e.state.isOriginator)&&void 0!==s&&s,force:!0}))}if(!k&&(null===(a=null==S?void 0:S.type)||void 0===a?void 0:a.startsWith("key_handshake"))){if(C)return;return ui.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' emit KEY_EXCHANGE`,S),void e.emit(Mi.KEY_EXCHANGE,{message:S,context:e.state.context})}if(k&&!(null===(c=e.state.keyExchange)||void 0===c?void 0:c.areKeysExchanged())){let t=!1;try{ui.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' trying to decrypt message`),null===(l=e.state.keyExchange)||void 0===l||l.decryptMessage(S),t=!0}catch(t){ui.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' error`,t)}if(!t)return e.state.isOriginator?null===(u=e.state.keyExchange)||void 0===u||u.start({isOriginator:null!==(h=e.state.isOriginator)&&void 0!==h&&h}):e.sendMessage({type:Ai.KEY_HANDSHAKE_START}).catch((e=>{})),void ui.SocketService(`Message ignored because invalid key exchange status. step=${null===(f=e.state.keyExchange)||void 0===f?void 0:f.getKeyInfo().step}`,null===(p=e.state.keyExchange)||void 0===p?void 0:p.getKeyInfo(),S);ui.SocketService("Invalid key exchange status detected --- updating it."),null===(d=e.state.keyExchange)||void 0===d||d.setKeysExchanged(!0)}else if(!k&&(null==S?void 0:S.type))return void e.emit(ki.MESSAGE,S);if(!k)return void e.emit(ki.MESSAGE,S);const M=null===(m=e.state.keyExchange)||void 0===m?void 0:m.decryptMessage(S),A=JSON.parse(null!=M?M:"{}");if(w&&(null==w?void 0:w.length)>0&&(ui.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' ackid=${w} channelId=${t}`),null===(g=e.state.socket)||void 0===g||g.emit(ki.MESSAGE_ACK,{ackId:w,channelId:t,clientType:e.state.isOriginator?"dapp":"wallet"})),e.state.clientsPaused=(null==A?void 0:A.type)===xi.PAUSE,e.state.isOriginator&&A.data){const t=A.data,n=e.state.rpcMethodTracker[t.id];if(n){const r=Date.now()-n.timestamp;ui.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' received answer for id=${t.id} method=${n.method} responseTime=${r}`,A),e.remote.state.analytics&&Ki.includes(n.method.toLowerCase())&&mi(Object.assign(Object.assign({id:null!==(y=e.remote.state.channelId)&&void 0!==y?y:"",event:Ii.SDK_RPC_REQUEST_DONE,sdkVersion:e.remote.state.sdkVersion,commLayerVersion:yi.version},e.remote.state.originatorInfo),{walletVersion:null===(v=e.remote.state.walletInfo)||void 0===v?void 0:v.version,params:{method:n.method,from:"mobile"}}),e.remote.state.communicationServerUrl).catch((e=>{}));const i=Object.assign(Object.assign({},n),{result:t.result,error:t.error?{code:null===(b=t.error)||void 0===b?void 0:b.code,message:null===(E=t.error)||void 0===E?void 0:E.message}:void 0,elapsedTime:r});e.state.rpcMethodTracker[t.id]=i,e.emit(ki.RPC_UPDATE,i)}}e.emit(ki.MESSAGE,{message:A})}}},{event:ki.REJECTED,handler:function(e,t){return n=>f(this,void 0,void 0,(function*(){var n;e.state.isOriginator&&!e.remote.state.ready?(ui.SocketService(`[SocketService: handleChannelRejected()] context=${e.state.context} channelId=${t} isOriginator=${e.state.isOriginator} ready=${e.remote.state.ready}`,e.remote.state.originatorInfo),mi(Object.assign(Object.assign({id:t,event:Ii.REJECTED},e.remote.state.originatorInfo),{sdkVersion:e.remote.state.sdkVersion,commLayer:e.state.communicationLayerPreference,commLayerVersion:yi.version,walletVersion:null===(n=e.remote.state.walletInfo)||void 0===n?void 0:n.version}),e.remote.state.communicationServerUrl).catch((e=>{})),yield e.remote.disconnect({terminate:!0}),e.remote.emit(ki.REJECTED,{channelId:t}),e.remote.setConnectionStatus(Ci.DISCONNECTED)):ui.SocketService(`[SocketService: handleChannelRejected()] SKIP -- channelId=${t} isOriginator=${e.state.isOriginator} ready=${e.remote.state.ready}`)}))}},{event:"clients_waiting_to_join",handler:function(e,t){return n=>{ui.SocketService(`[SocketService: handleClientsWaitingToJoin()] context=${e.state.context} on 'clients_waiting_to_join-${t}'`,n),e.emit(ki.CLIENTS_WAITING,n)}}}],$i=[{event:ki.KEY_INFO,handler:function(e){return t=>{ui.SocketService("[SocketService: handleKeyInfo()] on 'KEY_INFO'",t),e.emit(ki.KEY_INFO,t)}}},{event:ki.KEYS_EXCHANGED,handler:function(e){return()=>{var t,n,r;ui.SocketService(`[SocketService: handleKeysExchanged()] on 'keys_exchanged' keyschanged=${null===(t=e.state.keyExchange)||void 0===t?void 0:t.areKeysExchanged()}`);const{channelConfig:i}=e.remote.state;if(i){const t=e.getKeyExchange().getKeyInfo().ecies;i.localKey=t.private,i.otherKey=t.otherPubKey,e.remote.state.channelConfig=i,null===(n=e.remote.state.storageManager)||void 0===n||n.persistChannelConfig(i).catch((e=>{}))}e.emit(ki.KEYS_EXCHANGED,{keysExchanged:null===(r=e.state.keyExchange)||void 0===r?void 0:r.areKeysExchanged(),isOriginator:e.state.isOriginator});const o={keyInfo:e.getKeyInfo()};e.emit(ki.SERVICE_STATUS,o)}}}];function Ui(e,t){ui.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} setting socket listeners for channel ${t}...`);const{socket:n}=e.state,{keyExchange:r}=e.state;n&&e.state.isOriginator&&(e.state.debug&&(null==n||n.io.on("error",(t=>{ui.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=error`,t)})),null==n||n.io.on("reconnect",(t=>{ui.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect`,t),Li(e).catch((e=>{}))})),null==n||n.io.on("reconnect_error",(t=>{ui.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect_error`,t)})),null==n||n.io.on("reconnect_failed",(()=>{ui.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect_failed`)}))),null==n||n.on("disconnect",(t=>(ui.SocketService(`[SocketService: setupChannelListener()] on 'disconnect' -- MetaMaskSDK socket disconnected '${t}' begin recovery...`),function(e){return t=>{ui.SocketService(`[SocketService: handleDisconnect()] on 'disconnect' manualDisconnect=${e.state.manualDisconnect}`,t),e.state.manualDisconnect||(e.emit(ki.SOCKET_DISCONNECTED),Li(e).catch((e=>{})))}}(e)(t))))),ji.forEach((({event:r,handler:i})=>{null==n||n.on(`${r}-${t}`,i(e,t))})),$i.forEach((({event:t,handler:n})=>{null==r||r.on(t,n(e))})),e.state.setupChannelListeners=!0}class Hi extends s{constructor(e){super(),this.state={clientsConnected:!1,clientsPaused:!1,manualDisconnect:!1,lastRpcId:void 0,rpcMethodTracker:{},hasPlaintext:!1,communicationServerUrl:"",focusListenerAdded:!1,removeFocusListener:void 0,isReconnecting:!1,reconnectionAttempts:0},this.options=e;const{reconnect:n,communicationLayerPreference:r,communicationServerUrl:i,context:o,remote:s,logging:a}=e;this.state.resumed=n,this.state.context=o,this.state.isOriginator=s.state.isOriginator,this.state.communicationLayerPreference=r,this.state.debug=!0===(null==a?void 0:a.serviceLayer),this.remote=s,!0===(null==a?void 0:a.serviceLayer)&&t.enable("SocketService:Layer"),this.state.communicationServerUrl=i,this.state.hasPlaintext=this.state.communicationServerUrl!==vi&&!0===(null==a?void 0:a.plaintext),ui.SocketService(`[SocketService: constructor()] Socket IO url: ${this.state.communicationServerUrl}`),this.initSocket()}initSocket(){var e;const{otherPublicKey:t,ecies:n,logging:r}=this.options,i={autoConnect:!1,transports:bi,withCredentials:!0},o=this.state.communicationServerUrl;ui.SocketService(`[SocketService: initSocket()] Socket IO url: ${o}`),this.state.socket=l(o,i),function(e){if("undefined"!=typeof window&&"undefined"!=typeof document&&(ui.SocketService(`[SocketService: setupSocketFocusListener()] hasFocus=${document.hasFocus()}`,e),!e.state.focusListenerAdded)){const t=()=>{ui.SocketService("Document has focus --- reconnecting socket"),Li(e).catch((e=>{}))};window.addEventListener("focus",t),e.state.focusListenerAdded=!0,e.state.removeFocusListener=()=>{window.removeEventListener("focus",t),e.state.focusListenerAdded=!1}}}(this);const s={communicationLayer:this,otherPublicKey:t,sendPublicKey:!1,context:null!==(e=this.state.context)&&void 0!==e?e:"",ecies:n,logging:r};this.state.keyExchange=new Pi(s)}resetKeys(){return ui.SocketService("[SocketService: resetKeys()] Resetting keys."),void(null===(e=this.state.keyExchange)||void 0===e||e.resetKeys());var e}createChannel(){return f(this,void 0,void 0,(function*(){return function(e){return f(this,void 0,void 0,(function*(){var t,n,r;if(ui.SocketService(`[SocketService: createChannel()] context=${e.state.context}`),e.state.socket||e.initSocket(),null===(t=e.state.socket)||void 0===t?void 0:t.connected)throw new Error("socket already connected");null===(n=e.state.socket)||void 0===n||n.connect(),e.state.manualDisconnect=!1,e.state.isOriginator=!0;const i=c();e.state.channelId=i,Ui(e,i),yield new Promise(((t,n)=>{var r;null===(r=e.state.socket)||void 0===r||r.emit(ki.JOIN_CHANNEL,{channelId:i,context:`${e.state.context}createChannel`,clientType:"dapp"},((r,i)=>f(this,void 0,void 0,(function*(){try{yield Oi(e,r,i),t()}catch(e){n(e)}}))))}));const o=null===(r=e.state.keyExchange)||void 0===r?void 0:r.getKeyInfo();return{channelId:i,pubKey:(null==o?void 0:o.ecies.public)||"",privKey:(null==o?void 0:o.ecies.private)||""}}))}(this)}))}connectToChannel({channelId:e,withKeyExchange:t=!1,authorized:n}){return function(e){return f(this,arguments,void 0,(function*({options:e,instance:t}){const{channelId:n,authorized:r,withKeyExchange:i}=e,{state:o,remote:s}=t,{isOriginator:a=!1,socket:c,keyExchange:l}=o,{channelConfig:d}=s.state;if(null==c?void 0:c.connected)throw new Error("socket already connected");if(a&&(null==d?void 0:d.relayPersistence)){const{localKey:e,otherKey:t}=d;e&&t&&(null==l||l.setRelayPersistence({localKey:e,otherKey:t}))}return Object.assign(o,{manualDisconnect:!1,withKeyExchange:i,isOriginator:a,channelId:n}),null==c||c.connect(),Ui(t,n),!a&&r&&(null==l||l.setKeysExchanged(!0),Object.assign(s.state,{ready:!0,authorized:!0})),new Promise((e=>{var i;const s=null===(i=null==l?void 0:l.getKeyInfo())||void 0===i?void 0:i.ecies.public;null==c||c.emit(ki.JOIN_CHANNEL,{channelId:n,context:`${o.context}_connectToChannel`,clientType:a?"dapp":"wallet",publicKey:r&&!a?s:void 0},((n,r)=>f(this,void 0,void 0,(function*(){yield Oi(t,n,r),e()}))))}))}))}({options:{channelId:e,withKeyExchange:t,authorized:n},instance:this})}getKeyInfo(){return this.state.keyExchange.getKeyInfo()}keyCheck(){var e,t;null===(t=(e=this).state.socket)||void 0===t||t.emit(ki.MESSAGE,{id:e.state.channelId,context:e.state.context,message:{type:Ai.KEY_HANDSHAKE_CHECK,pubkey:e.getKeyInfo().ecies.otherPubKey}})}getKeyExchange(){return this.state.keyExchange}sendMessage(e){return f(this,void 0,void 0,(function*(){return function(e,t){return f(this,void 0,void 0,(function*(){var n,r,i;if(!e.state.channelId)throw ui.SocketService("handleSendMessage: no channelId - Create a channel first"),new Error("Create a channel first");if(ui.SocketService(`[SocketService: handleSendMessage()] context=${e.state.context} areKeysExchanged=${null===(n=e.state.keyExchange)||void 0===n?void 0:n.areKeysExchanged()}`,t),null===(r=null==t?void 0:t.type)||void 0===r?void 0:r.startsWith("key_handshake"))return function(e,t){var n;ui.SocketService(`[SocketService: handleKeyHandshake()] context=${e.state.context}`,t),null===(n=e.state.socket)||void 0===n||n.emit(ki.MESSAGE,{id:e.state.channelId,context:e.state.context,clientType:e.state.isOriginator?"dapp":"wallet",message:t})}(e,t),!0;!function(e,t){var n;if(!(null===(n=e.state.keyExchange)||void 0===n?void 0:n.areKeysExchanged())&&!e.remote.state.relayPersistence)throw ui.SocketService(`[SocketService: validateKeyExchange()] context=${e.state.context} ERROR keys not exchanged`,t),new Error("Keys not exchanged BBB")}(e,t),function(e,t){var n;const r=null!==(n=null==t?void 0:t.method)&&void 0!==n?n:"",i=null==t?void 0:t.id;e.state.isOriginator&&i&&(e.state.rpcMethodTracker[i]={id:i,timestamp:Date.now(),method:r},e.emit(ki.RPC_UPDATE,e.state.rpcMethodTracker[i]))}(e,t);const o=yield Di(e,t);return e.remote.state.analytics&&e.remote.state.isOriginator&&t.method&&Ki.includes(t.method.toLowerCase())&&mi({id:null!==(i=e.remote.state.channelId)&&void 0!==i?i:"",event:Ii.SDK_RPC_REQUEST,params:{method:t.method,from:"mobile"}},e.remote.state.communicationServerUrl).catch((e=>{})),function(e,t){return f(this,void 0,void 0,(function*(){var n;const r=null==t?void 0:t.id,i=null!==(n=null==t?void 0:t.method)&&void 0!==n?n:"";if(e.state.isOriginator&&r)try{const n=Ni(r,e.state.rpcMethodTracker,200).then((e=>({type:Bi.RPC_CHECK,result:e}))),o=(()=>f(this,void 0,void 0,(function*(){const t=yield(e=>f(void 0,[e],void 0,(function*({rpcId:e,instance:t}){for(;t.state.lastRpcId===e||void 0===t.state.lastRpcId;)yield Ti(200);return t.state.lastRpcId})))({instance:e,rpcId:r}),n=yield Ni(t,e.state.rpcMethodTracker,200);return{type:Bi.SKIPPED_RPC,result:n}})))(),s=yield Promise.race([n,o]);if(s.type===Bi.RPC_CHECK){const e=s.result;ui.SocketService(`[SocketService:handleRpcReplies()] id=${t.id} ${i} ( ${e.elapsedTime} ms)`,e.result)}else{if(s.type!==Bi.SKIPPED_RPC)throw new Error(`Error handling RPC replies for ${r}`);{const t=Object.assign(Object.assign({},e.state.rpcMethodTracker[r]),{error:new Error("SDK_CONNECTION_ISSUE")});e.emit(ki.RPC_UPDATE,t);const n={data:Object.assign(Object.assign({},t),{jsonrpc:"2.0"}),name:"metamask-provider"};e.emit(ki.MESSAGE,{message:n})}}}catch(e){throw e}}))}(e,t).catch((e=>{})),o}))}(this,e)}))}ping(){return function(e){return f(this,void 0,void 0,(function*(){var t,n;ui.SocketService(`[SocketService: ping()] context=${e.state.context} originator=${e.state.isOriginator} keysExchanged=${null===(t=e.state.keyExchange)||void 0===t?void 0:t.areKeysExchanged()}`),null===(n=e.state.socket)||void 0===n||n.emit(xi.PING,{id:e.state.channelId,context:"ping",clientType:e.remote.state.isOriginator?"dapp":"wallet",message:""})}))}(this)}pause(){return function(e){return f(this,void 0,void 0,(function*(){var t,n;ui.SocketService(`[SocketService: pause()] context=${e.state.context}`),e.state.manualDisconnect=!0,(null===(t=e.state.keyExchange)||void 0===t?void 0:t.areKeysExchanged())&&(yield e.sendMessage({type:xi.PAUSE})),null===(n=e.state.socket)||void 0===n||n.disconnect()}))}(this)}isConnected(){var e;return null===(e=this.state.socket)||void 0===e?void 0:e.connected}resume(){return function(e){return f(this,void 0,void 0,(function*(){const{state:t,remote:n}=e,{socket:r,channelId:i,context:o,keyExchange:s,isOriginator:a}=t,{isOriginator:c}=n.state;if(ui.SocketService(`[SocketService: resume()] channelId=${i} context=${o} connected=${null==r?void 0:r.connected} manualDisconnect=${t.manualDisconnect} resumed=${t.resumed} keysExchanged=${null==s?void 0:s.areKeysExchanged()}`),!i)throw ui.SocketService("[SocketService: resume()] channelId is not defined"),new Error("ChannelId is not defined");(null==r?void 0:r.connected)?(ui.SocketService("[SocketService: resume()] already connected."),r.emit(xi.PING,{id:i,clientType:c?"dapp":"wallet",context:"on_channel_config",message:""}),n.hasRelayPersistence()||(null==s?void 0:s.areKeysExchanged())||(a?yield e.sendMessage({type:xi.READY}):null==s||s.start({isOriginator:!1}))):(null==r||r.connect(),ui.SocketService(`[SocketService: resume()] after connecting socket --\x3e connected=${null==r?void 0:r.connected}`),null==r||r.emit(ki.JOIN_CHANNEL,{channelId:i,context:`${o}_resume`,clientType:c?"dapp":"wallet"},((t,n)=>f(this,void 0,void 0,(function*(){try{yield Oi(e,t,n)}catch(e){}}))))),t.manualDisconnect=!1,t.resumed=!0}))}(this)}getRPCMethodTracker(){return this.state.rpcMethodTracker}disconnect(e){return function(e,t){var n,r,i,o,s;ui.SocketService(`[SocketService: disconnect()] context=${e.state.context}`,t),(null==t?void 0:t.terminate)&&(null===(r=(n=e.state).removeFocusListener)||void 0===r||r.call(n),e.state.channelId=t.channelId,null===(i=e.state.socket)||void 0===i||i.removeAllListeners(),null===(o=e.state.keyExchange)||void 0===o||o.clean(),e.remote.state.ready=!1,e.state.socket=void 0,e.state.rpcMethodTracker={}),e.state.manualDisconnect=!0,null===(s=e.state.socket)||void 0===s||s.disconnect()}(this,e)}}var Fi,qi,zi;function Wi(e){return()=>f(this,void 0,void 0,(function*(){var t,n,r;const{state:i}=e;if(i.authorized)return;yield(()=>f(this,void 0,void 0,(function*(){for(;!i.walletInfo;)yield Ti(500)})))();const o="7.3".localeCompare((null===(t=i.walletInfo)||void 0===t?void 0:t.version)||"");if(ui.RemoteCommunication(`[RemoteCommunication: handleAuthorizedEvent()] HACK 'authorized' version=${null===(n=i.walletInfo)||void 0===n?void 0:n.version} compareValue=${o}`),1!==o)return;const s=i.platformType===qi.MobileWeb||i.platformType===qi.ReactNative||i.platformType===qi.MetaMaskMobileWebview;ui.RemoteCommunication(`[RemoteCommunication: handleAuthorizedEvent()] HACK 'authorized' platform=${i.platformType} secure=${s} channel=${i.channelId} walletVersion=${null===(r=i.walletInfo)||void 0===r?void 0:r.version}`),s&&(i.authorized=!0,e.emit(ki.AUTHORIZED))}))}function Vi(e){return t=>{const{state:n}=e;ui.RemoteCommunication(`[RemoteCommunication: handleChannelCreatedEvent()] context=${n.context} on 'channel_created' channelId=${t}`),e.emit(ki.CHANNEL_CREATED,t)}}function Gi(e,t){return()=>{var n,r,i,o;const{state:s}=e;ui.RemoteCommunication(`[RemoteCommunication: handleClientsConnectedEvent()] on 'clients_connected' channel=${s.channelId} keysExchanged=${null===(r=null===(n=s.communicationLayer)||void 0===n?void 0:n.getKeyInfo())||void 0===r?void 0:r.keysExchanged}`),s.analytics&&mi(Object.assign(Object.assign({id:null!==(i=s.channelId)&&void 0!==i?i:"",event:s.reconnection?Ii.RECONNECT:s.isOriginator?Ii.REQUEST:Ii.REQUEST_MOBILE},s.originatorInfo),{commLayer:t,sdkVersion:s.sdkVersion,walletVersion:null===(o=s.walletInfo)||void 0===o?void 0:o.version,commLayerVersion:yi.version}),s.communicationServerUrl).catch((e=>{})),s.clientsConnected=!0,s.originatorInfoSent=!1,e.emit(ki.CLIENTS_CONNECTED)}}function Yi(e){return t=>{const{state:n}=e;ui.RemoteCommunication(`[RemoteCommunication: handleClientsDisconnectedEvent()] context=${n.context} on 'clients_disconnected' channelId=${t}`),n.relayPersistence||(n.clientsConnected=!1,n.ready=!1,n.authorized=!1),e.emit(ki.CLIENTS_DISCONNECTED,n.channelId),e.setConnectionStatus(Ci.DISCONNECTED)}}function Zi(e){return t=>{var n;const{state:r}=e;if(ui.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] context=${r.context} on 'clients_waiting' numberUsers=${t} ready=${r.ready} autoStarted=${r.originatorConnectStarted}`),e.setConnectionStatus(Ci.WAITING),e.emit(ki.CLIENTS_WAITING,t),r.originatorConnectStarted){ui.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] on 'clients_waiting' watch autoStarted=${r.originatorConnectStarted} timeout`,r.autoConnectOptions);const t=(null===(n=r.autoConnectOptions)||void 0===n?void 0:n.timeout)||3e3,i=setTimeout((()=>{ui.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] setTimeout(${t}) terminate channelConfig`,r.autoConnectOptions),r.originatorConnectStarted=!1,r.ready||e.setConnectionStatus(Ci.TIMEOUT),clearTimeout(i)}),t)}}}function Ji(e,t){return n=>{var r,i,o,s,a,c,l,d;const{state:u}=e;if(ui.RemoteCommunication(`[RemoteCommunication: handleKeysExchangedEvent()] context=${u.context} on commLayer.'keys_exchanged' channel=${u.channelId}`,n),null===(i=null===(r=u.communicationLayer)||void 0===r?void 0:r.getKeyInfo())||void 0===i?void 0:i.keysExchanged){const t=Object.assign(Object.assign({},u.channelConfig),{channelId:null!==(o=u.channelId)&&void 0!==o?o:"",validUntil:(null===(s=u.channelConfig)||void 0===s?void 0:s.validUntil)||Ei,localKey:u.communicationLayer.getKeyInfo().ecies.private,otherKey:u.communicationLayer.getKeyInfo().ecies.otherPubKey});null===(a=u.storageManager)||void 0===a||a.persistChannelConfig(t).catch((e=>{})),e.setConnectionStatus(Ci.LINKED)}!function(e,t){var n,r,i,o,s,a,c,l;const{state:d}=e;ui.RemoteCommunication(`[RemoteCommunication: setLastActiveDate()] channel=${d.channelId}`,t);const u=Object.assign(Object.assign({},d.channelConfig),{channelId:null!==(n=d.channelId)&&void 0!==n?n:"",validUntil:null!==(i=null===(r=d.channelConfig)||void 0===r?void 0:r.validUntil)&&void 0!==i?i:0,relayPersistence:d.relayPersistence,localKey:null===(s=null===(o=d.communicationLayer)||void 0===o?void 0:o.state.keyExchange)||void 0===s?void 0:s.getKeyInfo().ecies.private,otherKey:null===(c=null===(a=d.communicationLayer)||void 0===a?void 0:a.state.keyExchange)||void 0===c?void 0:c.getKeyInfo().ecies.otherPubKey,lastActive:t.getTime()});null===(l=d.storageManager)||void 0===l||l.persistChannelConfig(u)}(e,new Date),u.analytics&&u.channelId&&mi(Object.assign(Object.assign({id:u.channelId,event:n.isOriginator?Ii.CONNECTED:Ii.CONNECTED_MOBILE},u.originatorInfo),{sdkVersion:u.sdkVersion,commLayer:t,commLayerVersion:yi.version,walletVersion:null===(c=u.walletInfo)||void 0===c?void 0:c.version}),u.communicationServerUrl).catch((e=>{})),u.isOriginator=n.isOriginator,n.isOriginator||(null===(l=u.communicationLayer)||void 0===l||l.sendMessage({type:xi.READY}),u.ready=!0,u.paused=!1),n.isOriginator&&!u.originatorInfoSent&&(null===(d=u.communicationLayer)||void 0===d||d.sendMessage({type:xi.ORIGINATOR_INFO,originatorInfo:u.originatorInfo,originator:u.originatorInfo}),u.originatorInfoSent=!0)}}function Qi(e){return t=>{let n=t;t.message&&(n=n.message),function(e,t){const{state:n}=t;if(ui.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] context=${n.context} on 'message' typeof=${typeof e}`,e),t.state.ready=!0,n.isOriginator||e.type!==xi.ORIGINATOR_INFO)if(n.isOriginator&&e.type===xi.WALLET_INFO)!function(e,t){const{state:n}=e;n.walletInfo=t.walletInfo,n.paused=!1}(t,e);else{if(n.isOriginator&&e.type===xi.WALLET_INIT)(function(e,t){return f(this,void 0,void 0,(function*(){var n,r,i;const{state:o}=e;if(o.isOriginator){const o=t.data||{};if("object"==typeof o&&"accounts"in o&&"chainId"in o&&"walletKey"in o)try{const{channelConfig:t}=e.state;if(ui.RemoteCommunication("WALLET_INIT: channelConfig",JSON.stringify(t,null,2)),t){const s=o.accounts,a=o.chainId,c=o.walletKey;let l,d=!1;"deeplinkProtocol"in o&&(d=Boolean(o.deeplinkProtocol),e.state.deeplinkProtocolAvailable=d),"walletVersion"in o&&(l=o.walletVersion),yield null===(n=e.state.storageManager)||void 0===n?void 0:n.persistChannelConfig(Object.assign(Object.assign({},t),{otherKey:c,walletVersion:l,deeplinkProtocolAvailable:d,relayPersistence:!0})),yield null===(r=e.state.storageManager)||void 0===r?void 0:r.persistAccounts(s),yield null===(i=e.state.storageManager)||void 0===i?void 0:i.persistChainId(a)}e.emit(ki.WALLET_INIT,{accounts:o.accounts,chainId:o.chainId})}catch(n){}}}))})(t,e).catch((e=>{ui.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] error=${e}`)}));else if(e.type===xi.TERMINATE)(function(e){return f(this,void 0,void 0,(function*(){const{state:t}=e;t.isOriginator&&(yield ro({options:{terminate:!0,sendMessage:!1},instance:e}),e.emit(ki.TERMINATE))}))})(t).catch((e=>{ui.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] error=${e}`)}));else if(e.type===xi.PAUSE)!function(e){const{state:t}=e;t.paused=!0,e.setConnectionStatus(Ci.PAUSED)}(t);else if(e.type===xi.READY&&n.isOriginator)!function(e){const{state:t}=e;e.setConnectionStatus(Ci.LINKED);const n=t.paused;t.paused=!1,e.emit(ki.CLIENTS_READY,{isOriginator:t.isOriginator,walletInfo:t.walletInfo}),n&&(t.authorized=!0,e.emit(ki.AUTHORIZED))}(t);else{if(e.type===xi.OTP&&n.isOriginator)return void function(e,t){var n;const{state:r}=e;e.emit(ki.OTP,t.otpAnswer),1==="6.6".localeCompare((null===(n=r.walletInfo)||void 0===n?void 0:n.version)||"")&&e.emit(ki.SDK_RPC_CALL,{method:Si.ETH_REQUESTACCOUNTS,params:[]})}(t,e);e.type===xi.AUTHORIZED&&n.isOriginator&&function(e){const{state:t}=e;t.authorized=!0,e.emit(ki.AUTHORIZED)}(t)}t.emit(ki.MESSAGE,e)}else!function(e,t){var n;const{state:r}=e;null===(n=r.communicationLayer)||void 0===n||n.sendMessage({type:xi.WALLET_INFO,walletInfo:r.walletInfo}),r.originatorInfo=t.originatorInfo||t.originator,e.emit(ki.CLIENTS_READY,{isOriginator:r.isOriginator,originatorInfo:r.originatorInfo}),r.paused=!1}(t,e)}(n,e)}}function Xi(e){return()=>{const{state:t}=e;ui.RemoteCommunication("[RemoteCommunication: handleSocketReconnectEvent()] on 'socket_reconnect' -- reset key exchange status / set ready to false"),t.ready=!1,t.authorized=!1,_i(t),e.emitServiceStatusEvent({context:"socket_reconnect"})}}function eo(e){return()=>{const{state:t}=e;ui.RemoteCommunication("[RemoteCommunication: handleSocketDisconnectedEvent()] on 'socket_Disconnected' set ready to false"),t.ready=!1}}function to(e){return()=>f(this,void 0,void 0,(function*(){var t,n,r,i,o,s,a;const{state:c}=e;ui.RemoteCommunication(`[RemoteCommunication: handleFullPersistenceEvent()] context=${c.context}`),e.state.ready=!0,e.state.clientsConnected=!0,e.state.authorized=!0,e.state.relayPersistence=!0,null===(t=e.state.communicationLayer)||void 0===t||t.getKeyExchange().setKeysExchanged(!0),e.emit(ki.KEYS_EXCHANGED,{keysExchanged:!0,isOriginator:!0}),e.emit(ki.AUTHORIZED),e.emit(ki.CLIENTS_READY),e.emit(ki.CHANNEL_PERSISTENCE);try{c.channelConfig=Object.assign(Object.assign({},c.channelConfig),{localKey:null===(n=c.communicationLayer)||void 0===n?void 0:n.getKeyExchange().getKeyInfo().ecies.private,otherKey:null===(r=c.communicationLayer)||void 0===r?void 0:r.getKeyExchange().getOtherPublicKey(),channelId:null!==(i=c.channelId)&&void 0!==i?i:"",validUntil:null!==(s=null===(o=c.channelConfig)||void 0===o?void 0:o.validUntil)&&void 0!==s?s:Ei,relayPersistence:!0}),yield null===(a=c.storageManager)||void 0===a?void 0:a.persistChannelConfig(c.channelConfig)}catch(t){}}))}function no({communicationLayerPreference:e,otherPublicKey:t,reconnect:n,ecies:r,communicationServerUrl:i=vi,instance:o}){var s,a,c,l,d,u,h,f,p,m,g;const{state:y}=o;if(ui.RemoteCommunication("[initCommunicationLayer()] ",JSON.stringify(y,null,2)),e!==Fi.SOCKET)throw new Error("Invalid communication protocol");y.communicationLayer=new Hi({communicationLayerPreference:e,otherPublicKey:t,reconnect:n,transports:y.transports,communicationServerUrl:i,context:y.context,ecies:r,logging:y.logging,remote:o});let v="undefined"!=typeof document&&document.URL||"",b="undefined"!=typeof document&&document.title||"";(null===(s=y.dappMetadata)||void 0===s?void 0:s.url)&&(v=y.dappMetadata.url),(null===(a=y.dappMetadata)||void 0===a?void 0:a.name)&&(b=y.dappMetadata.name);const E=null!==(u=null!==(l=null===(c=y.dappMetadata)||void 0===c?void 0:c.name)&&void 0!==l?l:null===(d=y.dappMetadata)||void 0===d?void 0:d.url)&&void 0!==u?u:"N/A",w="undefined"!=typeof window&&void 0!==window.location&&null!==(h=window.location.hostname)&&void 0!==h?h:E,S={url:v,title:b,source:null===(f=y.dappMetadata)||void 0===f?void 0:f.source,dappId:w,icon:(null===(p=y.dappMetadata)||void 0===p?void 0:p.iconUrl)||(null===(m=y.dappMetadata)||void 0===m?void 0:m.base64Icon),platform:y.platformType,apiVersion:yi.version,connector:null===(g=y.dappMetadata)||void 0===g?void 0:g.connector};y.originatorInfo=S;const _={[ki.AUTHORIZED]:Wi(o),[ki.MESSAGE]:Qi(o),[ki.CHANNEL_PERSISTENCE]:to(o),[ki.CLIENTS_CONNECTED]:Gi(o,e),[ki.KEYS_EXCHANGED]:Ji(o,e),[ki.SOCKET_DISCONNECTED]:eo(o),[ki.SOCKET_RECONNECT]:Xi(o),[ki.CLIENTS_DISCONNECTED]:Yi(o),[ki.KEY_INFO]:()=>{},[ki.CHANNEL_CREATED]:Vi(o),[ki.CLIENTS_WAITING]:Zi(o),[ki.RPC_UPDATE]:e=>{o.emit(ki.RPC_UPDATE,e)}};for(const[t,n]of Object.entries(_))try{y.communicationLayer.on(t,n)}catch(e){}}function ro(e){return f(this,arguments,void 0,(function*({options:e,instance:t}){const{state:n}=t;return ui.RemoteCommunication(`[RemoteCommunication: disconnect()] channel=${n.channelId}`,e),new Promise(((r,i)=>{var o,s,a,l,d,u;(null==e?void 0:e.terminate)?(t.state.ready&&mi({id:null!==(o=t.state.channelId)&&void 0!==o?o:"",event:Ii.TERMINATED},t.state.communicationServerUrl).catch((e=>{})),n.ready=!1,n.paused=!1,null===(s=n.storageManager)||void 0===s||s.terminate(null!==(a=n.channelId)&&void 0!==a?a:""),t.state.terminated=!0,e.sendMessage?(null===(l=n.communicationLayer)||void 0===l?void 0:l.getKeyInfo().keysExchanged)&&t.state.communicationLayer&&Di(t.state.communicationLayer,{type:xi.TERMINATE}).then((()=>{r(!0)})).catch((e=>{i(e)})):r(!0),n.authorized=!1,n.relayPersistence=!1,n.channelId=c(),e.channelId=n.channelId,n.channelConfig=void 0,n.originatorConnectStarted=!1,null===(d=n.communicationLayer)||void 0===d||d.disconnect(e),t.setConnectionStatus(Ci.TERMINATED)):(null===(u=n.communicationLayer)||void 0===u||u.disconnect(e),t.setConnectionStatus(Ci.DISCONNECTED),r(!0))}))}))}(Fi||(Fi={})).SOCKET="socket",function(e){e.NonBrowser="nodejs",e.MetaMaskMobileWebview="in-app-browser",e.DesktopWeb="web-desktop",e.MobileWeb="web-mobile",e.ReactNative="react-native"}(qi||(qi={}));class io extends s{constructor(e){super(),this.state={ready:!1,authorized:!1,isOriginator:!1,terminated:!1,protocolVersion:1,paused:!1,deeplinkProtocolAvailable:!1,platformType:"metamask-mobile",analytics:!1,reconnection:!1,originatorInfoSent:!1,communicationServerUrl:vi,context:"",persist:!1,clientsConnected:!1,sessionDuration:Ei,originatorConnectStarted:!1,debug:!1,_connectionStatus:Ci.DISCONNECTED},this._options=e;const{platformType:n,communicationLayerPreference:r,otherPublicKey:i,reconnect:o,walletInfo:s,dappMetadata:a,protocolVersion:c,transports:l,context:d,relayPersistence:u,ecies:h,analytics:f=!1,storage:p,sdkVersion:m,communicationServerUrl:g=vi,logging:y,autoConnect:v={timeout:wi}}=e;this.state.otherPublicKey=i,this.state.dappMetadata=a,this.state.walletInfo=s,this.state.transports=l,this.state.platformType=n,this.state.analytics=f,this.state.protocolVersion=null!=c?c:1,this.state.isOriginator=!i,this.state.relayPersistence=u,this.state.communicationServerUrl=g,this.state.context=d,this.state.terminated=!1,this.state.sdkVersion=m,this.setMaxListeners(50),this.setConnectionStatus(Ci.DISCONNECTED),(null==p?void 0:p.duration)&&(this.state.sessionDuration=Ei),this.state.storageOptions=p,this.state.autoConnectOptions=v,this.state.debug=!0===(null==y?void 0:y.remoteLayer),!0===(null==y?void 0:y.remoteLayer)&&t.enable("RemoteCommunication:Layer"),!0===(null==y?void 0:y.serviceLayer)&&t.enable("SocketService:Layer"),!0===(null==y?void 0:y.eciesLayer)&&t.enable("ECIES:Layer"),!0===(null==y?void 0:y.keyExchangeLayer)&&t.enable("KeyExchange:Layer"),this.state.logging=y,(null==p?void 0:p.storageManager)&&(this.state.storageManager=p.storageManager),ui.RemoteCommunication(`[RemoteCommunication: constructor()] protocolVersion=${c} relayPersistence=${u} isOriginator=${this.state.isOriginator} communicationLayerPreference=${r} otherPublicKey=${i} reconnect=${o}`),this.state.isOriginator||no({communicationLayerPreference:r,otherPublicKey:i,reconnect:o,ecies:h,communicationServerUrl:g,instance:this}),this.emitServiceStatusEvent({context:"constructor"})}initFromDappStorage(){return f(this,void 0,void 0,(function*(){var e;if(this.state.storageManager){const t=yield this.state.storageManager.getPersistedChannelConfig({});t&&(this.state.channelConfig=t,this.state.channelId=t.channelId,this.state.deeplinkProtocolAvailable=null!==(e=t.deeplinkProtocolAvailable)&&void 0!==e&&e,t.relayPersistence&&(this.state.authorized=!0,this.state.ready=!0,this.setConnectionStatus(Ci.LINKED),yield this.connectToChannel({channelId:t.channelId})))}no({communicationLayerPreference:Fi.SOCKET,otherPublicKey:this.state.otherPublicKey,reconnect:this._options.reconnect,ecies:this._options.ecies,communicationServerUrl:this.state.communicationServerUrl,instance:this})}))}originatorSessionConnect(){return f(this,void 0,void 0,(function*(){return yield function(e){return f(this,void 0,void 0,(function*(){var t;const{state:n}=e;if(!n.storageManager)return void ui.RemoteCommunication("[RemoteCommunication: originatorSessionConnect()] no storage manager defined - skip");const r=yield n.storageManager.getPersistedChannelConfig({});if(ui.RemoteCommunication(`[RemoteCommunication: originatorSessionConnect()] autoStarted=${n.originatorConnectStarted} channelConfig`,r),null===(t=n.communicationLayer)||void 0===t?void 0:t.isConnected())return ui.RemoteCommunication("[RemoteCommunication: originatorSessionConnect()] socket already connected - skip"),r;if(r){if(r.validUntil>Date.now())return n.channelConfig=r,n.originatorConnectStarted=!0,n.channelId=null==r?void 0:r.channelId,n.reconnection=!0,r;ui.RemoteCommunication("[RemoteCommunication: autoConnect()] Session has expired")}n.originatorConnectStarted=!1}))}(this)}))}generateChannelIdConnect(){return f(this,void 0,void 0,(function*(){return function(e){return f(this,void 0,void 0,(function*(){var t,n,r,i,o,s;if(!e.communicationLayer)throw new Error("communication layer not initialized");if(e.ready)throw new Error("Channel already connected");if(e.channelId&&(null===(t=e.communicationLayer)||void 0===t?void 0:t.isConnected()))return e.channelConfig=Object.assign(Object.assign({},e.channelConfig),{channelId:e.channelId,validUntil:Date.now()+e.sessionDuration}),null===(n=e.storageManager)||void 0===n||n.persistChannelConfig(e.channelConfig),{channelId:e.channelId,privKey:null===(i=null===(r=e.communicationLayer)||void 0===r?void 0:r.getKeyInfo())||void 0===i?void 0:i.ecies.private,pubKey:null===(s=null===(o=e.communicationLayer)||void 0===o?void 0:o.getKeyInfo())||void 0===s?void 0:s.ecies.public};ui.RemoteCommunication("[RemoteCommunication: generateChannelId()]");const a=yield e.communicationLayer.createChannel();ui.RemoteCommunication("[RemoteCommunication: generateChannelId()] channel created",a);const c=Object.assign(Object.assign({},e.channelConfig),{channelId:a.channelId,localKey:a.privKey,validUntil:Date.now()+e.sessionDuration});return e.channelId=a.channelId,e.channelConfig=c,{channelId:e.channelId,pubKey:a.pubKey,privKey:a.privKey}}))}(this.state)}))}clean(){return _i(this.state)}connectToChannel({channelId:e,withKeyExchange:t,authorized:n}){return function(e){return f(this,arguments,void 0,(function*({channelId:e,withKeyExchange:t,authorized:n,state:r}){var i,o,s;if(!a(e))throw ui.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${r.context} invalid channel channelId=${e}`),new Error(`Invalid channel ${e}`);if(ui.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${r.context} channelId=${e} withKeyExchange=${t}`),null===(i=r.communicationLayer)||void 0===i?void 0:i.isConnected())return void ui.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${r.context} already connected - interrupt connection.`);r.channelId=e,yield null===(o=r.communicationLayer)||void 0===o?void 0:o.connectToChannel({channelId:e,authorized:n,withKeyExchange:t});const c=Object.assign(Object.assign({},r.channelConfig),{channelId:e,validUntil:Date.now()+r.sessionDuration});r.channelConfig=c,null===(s=r.storageManager)||void 0===s||s.persistChannelConfig(c)}))}({channelId:e,authorized:n,withKeyExchange:t,state:this.state})}sendMessage(e){return function(e,t){return f(this,void 0,void 0,(function*(){var n,r;const{state:i}=e;ui.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${i.context} paused=${i.paused} ready=${i.ready} relayPersistence=${i.relayPersistence} authorized=${i.authorized} socket=${null===(n=i.communicationLayer)||void 0===n?void 0:n.isConnected()} clientsConnected=${i.clientsConnected} status=${i._connectionStatus}`,t),i.relayPersistence||i.ready&&(null===(r=i.communicationLayer)||void 0===r?void 0:r.isConnected())&&i.clientsConnected||(ui.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${i.context}  SKIP message waiting for MM mobile readiness.`),yield new Promise((t=>{e.once(ki.CLIENTS_READY,t)})),ui.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${i.context}  AFTER SKIP / READY -- sending pending message`));try{const n=yield function(e,t){return f(this,void 0,void 0,(function*(){return new Promise((n=>{var r;const{state:i}=e;ui.RemoteCommunication(`[RemoteCommunication: handleAuthorization()] context=${i.context} ready=${i.ready} authorized=${i.authorized} method=${t.method}`),!i.isOriginator||i.authorized||i.relayPersistence?null===(r=i.communicationLayer)||void 0===r||r.sendMessage(t).then((e=>{n(e)})).catch((e=>{n(!1)})):e.once(ki.AUTHORIZED,(()=>{var e;ui.RemoteCommunication(`[RemoteCommunication: handleAuthorization()] context=${i.context}  AFTER SKIP / AUTHORIZED -- sending pending message`),null===(e=i.communicationLayer)||void 0===e||e.sendMessage(t).then((e=>{n(e)})).catch((e=>{n(!1)}))}))}))}))}(e,t);return n}catch(e){throw e}}))}(this,e)}testStorage(){return f(this,void 0,void 0,(function*(){return function(e){return f(this,void 0,void 0,(function*(){var t;const n=yield null===(t=e.storageManager)||void 0===t?void 0:t.getPersistedChannelConfig();ui.RemoteCommunication("[RemoteCommunication: testStorage()] res",n)}))}(this.state)}))}hasDeeplinkProtocol(){return this.state.deeplinkProtocolAvailable}getChannelConfig(){return this.state.channelConfig}isReady(){return this.state.ready}isConnected(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.isConnected()}isAuthorized(){return this.state.authorized}isPaused(){return this.state.paused}getCommunicationLayer(){return this.state.communicationLayer}ping(){return f(this,void 0,void 0,(function*(){var e;ui.RemoteCommunication(`[RemoteCommunication: ping()] channel=${this.state.channelId}`),yield null===(e=this.state.communicationLayer)||void 0===e?void 0:e.ping()}))}testLogger(){ui.RemoteCommunication(`testLogger() channel=${this.state.channelId}`),ui.SocketService(`testLogger() channel=${this.state.channelId}`),ui.Ecies(`testLogger() channel=${this.state.channelId}`),ui.KeyExchange(`testLogger() channel=${this.state.channelId}`)}keyCheck(){var e;ui.RemoteCommunication(`[RemoteCommunication: keyCheck()] channel=${this.state.channelId}`),null===(e=this.state.communicationLayer)||void 0===e||e.keyCheck()}setConnectionStatus(e){this.state._connectionStatus!==e&&(this.state._connectionStatus=e,this.emit(ki.CONNECTION_STATUS,e),this.emitServiceStatusEvent({context:"setConnectionStatus"}))}emitServiceStatusEvent(e={}){this.emit(ki.SERVICE_STATUS,this.getServiceStatus())}getConnectionStatus(){return this.state._connectionStatus}getServiceStatus(){return{originatorInfo:this.state.originatorInfo,keyInfo:this.getKeyInfo(),connectionStatus:this.state._connectionStatus,channelConfig:this.state.channelConfig,channelId:this.state.channelId}}getKeyInfo(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.getKeyInfo()}resetKeys(){var e;null===(e=this.state.communicationLayer)||void 0===e||e.resetKeys()}setOtherPublicKey(e){var t;const n=null===(t=this.state.communicationLayer)||void 0===t?void 0:t.getKeyExchange();if(!n)throw new Error("KeyExchange is not initialized.");n.getOtherPublicKey()!==e&&n.setOtherPublicKey(e)}pause(){return f(this,void 0,void 0,(function*(){var e;ui.RemoteCommunication(`[RemoteCommunication: pause()] channel=${this.state.channelId}`),yield null===(e=this.state.communicationLayer)||void 0===e?void 0:e.pause(),this.setConnectionStatus(Ci.PAUSED)}))}getVersion(){return yi.version}hasRelayPersistence(){var e;return null!==(e=this.state.relayPersistence)&&void 0!==e&&e}resume(){return f(this,void 0,void 0,(function*(){return function(e){return f(this,void 0,void 0,(function*(){var t;const{state:n}=e;ui.RemoteCommunication(`[RemoteCommunication: resume()] channel=${n.channelId}`),yield null===(t=n.communicationLayer)||void 0===t?void 0:t.resume(),e.setConnectionStatus(Ci.LINKED)}))}(this)}))}encrypt(e){var t,n,r;const i=null===(t=this.state.communicationLayer)||void 0===t?void 0:t.getKeyExchange(),o=null==i?void 0:i.getOtherPublicKey();if(!o)throw new Error("KeyExchange not completed");return null===(r=null===(n=this.state.communicationLayer)||void 0===n?void 0:n.state.eciesInstance)||void 0===r?void 0:r.encrypt(e,o)}decrypt(e){var t,n,r;if(!(null===(t=this.state.communicationLayer)||void 0===t?void 0:t.state.eciesInstance))throw new Error("ECIES instance is not initialized");return null===(r=null===(n=this.state.communicationLayer)||void 0===n?void 0:n.state.eciesInstance)||void 0===r?void 0:r.decrypt(e)}getChannelId(){return this.state.channelId}getRPCMethodTracker(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.getRPCMethodTracker()}reject({channelId:e}){return function(e){return f(this,arguments,void 0,(function*({channelId:e,state:t}){var n,r,i;if(!a(e))throw ui.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${t.context} invalid channel channelId=${e}`),new Error(`Invalid channel ${e}`);if(t.isOriginator)return void ui.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} isOriginator=${t.isOriginator} channelId=${e}`);const{socket:o}=null!==(r=null===(n=t.communicationLayer)||void 0===n?void 0:n.state)&&void 0!==r?r:{};(null==o?void 0:o.connected)||(ui.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} socket already connected`),null==o||o.connect()),mi(Object.assign(Object.assign({id:e,event:Ii.REJECTED},t.originatorInfo),{sdkVersion:t.sdkVersion,commLayerVersion:yi.version,walletVersion:null===(i=t.walletInfo)||void 0===i?void 0:i.version}),t.communicationServerUrl).catch((e=>{})),yield new Promise(((n,r)=>{null==o||o.emit(ki.REJECTED,{channelId:e},((e,i)=>{ui.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} socket=${null==o?void 0:o.id}`,{error:e,response:i}),e?r(e):n(i)}))}))}))}({channelId:e,state:this.state})}disconnect(e){return f(this,void 0,void 0,(function*(){return ro({options:e,instance:this})}))}}!function(e){e.RENEW="renew",e.LINK="link"}(zi||(zi={}));var oo="ERC721",so="ERC1155",ao={errors:{disconnected:()=>"MetaMask: Disconnected from chain. Attempting to connect.",permanentlyDisconnected:()=>"MetaMask: Disconnected from MetaMask background. Page reload required.",sendSiteMetadata:()=>"MetaMask: Failed to send site metadata. This is an internal error, please report this bug.",unsupportedSync:e=>`MetaMask: The MetaMask Ethereum provider does not support synchronous methods like ${e} without a callback parameter.`,invalidDuplexStream:()=>"Must provide a Node.js-style duplex stream.",invalidNetworkParams:()=>"MetaMask: Received invalid network parameters. Please report this bug.",invalidRequestArgs:()=>"Expected a single, non-array, object argument.",invalidRequestMethod:()=>"'args.method' must be a non-empty string.",invalidRequestParams:()=>"'args.params' must be an object or array if provided.",invalidLoggerObject:()=>"'args.logger' must be an object if provided.",invalidLoggerMethod:e=>`'args.logger' must include required method '${e}'.`},info:{connected:e=>`MetaMask: Connected to chain with ID "${e}".`},warnings:{chainIdDeprecation:"MetaMask: 'ethereum.chainId' is deprecated and may be removed in the future. Please use the 'eth_chainId' RPC method instead.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/discussions/23",networkVersionDeprecation:"MetaMask: 'ethereum.networkVersion' is deprecated and may be removed in the future. Please use the 'net_version' RPC method instead.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/discussions/23",selectedAddressDeprecation:"MetaMask: 'ethereum.selectedAddress' is deprecated and may be removed in the future. Please use the 'eth_accounts' RPC method instead.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/discussions/23",enableDeprecation:"MetaMask: 'ethereum.enable()' is deprecated and may be removed in the future. Please use the 'eth_requestAccounts' RPC method instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1102",sendDeprecation:"MetaMask: 'ethereum.send(...)' is deprecated and may be removed in the future. Please use 'ethereum.sendAsync(...)' or 'ethereum.request(...)' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193",events:{close:"MetaMask: The event 'close' is deprecated and may be removed in the future. Please use 'disconnect' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#disconnect",data:"MetaMask: The event 'data' is deprecated and will be removed in the future. Use 'message' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message",networkChanged:"MetaMask: The event 'networkChanged' is deprecated and may be removed in the future. Use 'chainChanged' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#chainchanged",notification:"MetaMask: The event 'notification' is deprecated and may be removed in the future. Use 'message' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message"},rpc:{ethDecryptDeprecation:"MetaMask: The RPC method 'eth_decrypt' is deprecated and may be removed in the future.\nFor more information, see: https://medium.com/metamask/metamask-api-method-deprecation-2b0564a84686",ethGetEncryptionPublicKeyDeprecation:"MetaMask: The RPC method 'eth_getEncryptionPublicKey' is deprecated and may be removed in the future.\nFor more information, see: https://medium.com/metamask/metamask-api-method-deprecation-2b0564a84686",walletWatchAssetNFTExperimental:"MetaMask: The RPC method 'wallet_watchAsset' is experimental for ERC721/ERC1155 assets and may change in the future.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-1.md and https://github.com/MetaMask/metamask-improvement-proposals/blob/main/PROCESS-GUIDE.md#proposal-lifecycle"},experimentalMethods:"MetaMask: 'ethereum._metamask' exposes non-standard, experimental methods. They may be removed or changed without warning."}};function co(e){const t={ethDecryptDeprecation:!1,ethGetEncryptionPublicKeyDeprecation:!1,walletWatchAssetNFTExperimental:!1};return(n,r,i)=>{t.ethDecryptDeprecation||"eth_decrypt"!==n.method?t.ethGetEncryptionPublicKeyDeprecation||"eth_getEncryptionPublicKey"!==n.method?!t.walletWatchAssetNFTExperimental&&"wallet_watchAsset"===n.method&&[oo,so].includes(n.params?.type||"")&&(e.warn(ao.warnings.rpc.walletWatchAssetNFTExperimental),t.walletWatchAssetNFTExperimental=!0):(e.warn(ao.warnings.rpc.ethGetEncryptionPublicKeyDeprecation),t.ethGetEncryptionPublicKeyDeprecation=!0):(e.warn(ao.warnings.rpc.ethDecryptDeprecation),t.ethDecryptDeprecation=!0),i()}}var lo=4294967295,uo=Math.floor(Math.random()*lo);function ho(){return(e,t,n,r)=>{const i=e.id,o=uo=(uo+1)%lo;e.id=o,t.id=o,n((n=>{e.id=i,t.id=i,n()}))}}var fo=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},po=(e,t,n)=>(fo(e,t,"read from private field"),n?n.call(e):t.get(e)),mo=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},go=(e,t,n,r)=>(fo(e,t,"write to private field"),t.set(e,n),n),yo=(e,t,n)=>(fo(e,t,"access private method"),n),vo={invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},bo={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}};function Eo(e){return Boolean(e)&&"object"==typeof e&&!Array.isArray(e)}var wo=(e,t)=>Object.hasOwnProperty.call(e,t);var So={exports:{}};!function(e){class t extends TypeError{constructor(e,t){let n;const{message:r,explanation:i,...o}=e,{path:s}=e,a=0===s.length?r:`At path: ${s.join(".")} -- ${r}`;super(i??a),null!=i&&(this.cause=a),Object.assign(this,o),this.name=this.constructor.name,this.failures=()=>n??(n=[e,...t()])}}function n(e){return r(e)&&"function"==typeof e[Symbol.iterator]}function r(e){return"object"==typeof e&&null!=e}function i(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function o(e){return"symbol"==typeof e?e.toString():"string"==typeof e?JSON.stringify(e):`${e}`}function s(e){const{done:t,value:n}=e.next();return t?void 0:n}function a(e,t,n,r){if(!0===e)return;!1===e?e={}:"string"==typeof e&&(e={message:e});const{path:i,branch:s}=t,{type:a}=n,{refinement:c,message:l=`Expected a value of type \`${a}\`${c?` with refinement \`${c}\``:""}, but received: \`${o(r)}\``}=e;return{value:r,type:a,refinement:c,key:i[i.length-1],path:i,branch:s,...e,message:l}}function*c(e,t,r,i){n(e)||(e=[e]);for(const n of e){const e=a(n,t,r,i);e&&(yield e)}}function*l(e,t,n={}){const{path:i=[],branch:o=[e],coerce:s=!1,mask:a=!1}=n,c={path:i,branch:o};if(s&&(e=t.coercer(e,c),a&&"type"!==t.type&&r(t.schema)&&r(e)&&!Array.isArray(e)))for(const n in e)void 0===t.schema[n]&&delete e[n];let d="valid";for(const r of t.validator(e,c))r.explanation=n.message,d="not_valid",yield[r,void 0];for(let[u,h,f]of t.entries(e,c)){const t=l(h,f,{path:void 0===u?i:[...i,u],branch:void 0===u?o:[...o,h],coerce:s,mask:a,message:n.message});for(const n of t)n[0]?(d=null!=n[0].refinement?"not_refined":"not_valid",yield[n[0],void 0]):s&&(h=n[1],void 0===u?e=h:e instanceof Map?e.set(u,h):e instanceof Set?e.add(h):r(e)&&(void 0!==h||u in e)&&(e[u]=h))}if("not_valid"!==d)for(const r of t.refiner(e,c))r.explanation=n.message,d="not_refined",yield[r,void 0];"valid"===d&&(yield[void 0,e])}class d{constructor(e){const{type:t,schema:n,validator:r,refiner:i,coercer:o=(e=>e),entries:s=function*(){}}=e;this.type=t,this.schema=n,this.entries=s,this.coercer=o,this.validator=r?(e,t)=>c(r(e,t),t,this,e):()=>[],this.refiner=i?(e,t)=>c(i(e,t),t,this,e):()=>[]}assert(e,t){return u(e,this,t)}create(e,t){return h(e,this,t)}is(e){return p(e,this)}mask(e,t){return f(e,this,t)}validate(e,t={}){return m(e,this,t)}}function u(e,t,n){const r=m(e,t,{message:n});if(r[0])throw r[0]}function h(e,t,n){const r=m(e,t,{coerce:!0,message:n});if(r[0])throw r[0];return r[1]}function f(e,t,n){const r=m(e,t,{coerce:!0,mask:!0,message:n});if(r[0])throw r[0];return r[1]}function p(e,t){return!m(e,t)[0]}function m(e,n,r={}){const i=l(e,n,r),o=s(i);if(o[0]){const e=new t(o[0],(function*(){for(const e of i)e[0]&&(yield e[0])}));return[e,void 0]}return[void 0,o[1]]}function g(...e){const t="type"===e[0].type,n=e.map((e=>e.schema)),r=Object.assign({},...n);return t?V(r):$(r)}function y(e,t){return new d({type:e,schema:null,validator:t})}function v(e,t){return new d({...e,refiner:(t,n)=>void 0===t||e.refiner(t,n),validator:(n,r)=>void 0===n||(t(n,r),e.validator(n,r))})}function b(e){return new d({type:"dynamic",schema:null,*entries(t,n){const r=e(t,n);yield*r.entries(t,n)},validator:(t,n)=>e(t,n).validator(t,n),coercer:(t,n)=>e(t,n).coercer(t,n),refiner:(t,n)=>e(t,n).refiner(t,n)})}function E(e){let t;return new d({type:"lazy",schema:null,*entries(n,r){t??(t=e()),yield*t.entries(n,r)},validator:(n,r)=>(t??(t=e()),t.validator(n,r)),coercer:(n,r)=>(t??(t=e()),t.coercer(n,r)),refiner:(n,r)=>(t??(t=e()),t.refiner(n,r))})}function w(e,t){const{schema:n}=e,r={...n};for(const e of t)delete r[e];return"type"===e.type?V(r):$(r)}function S(e){const t=e instanceof d?{...e.schema}:{...e};for(const e in t)t[e]=U(t[e]);return $(t)}function _(e,t){const{schema:n}=e,r={};for(const e of t)r[e]=n[e];return $(r)}function C(e,t){return console.warn("superstruct@0.11 - The `struct` helper has been renamed to `define`."),y(e,t)}function k(){return y("any",(()=>!0))}function M(e){return new d({type:"array",schema:e,*entries(t){if(e&&Array.isArray(t))for(const[n,r]of t.entries())yield[n,r,e]},coercer:e=>Array.isArray(e)?e.slice():e,validator:e=>Array.isArray(e)||`Expected an array value, but received: ${o(e)}`})}function A(){return y("bigint",(e=>"bigint"==typeof e))}function x(){return y("boolean",(e=>"boolean"==typeof e))}function I(){return y("date",(e=>e instanceof Date&&!isNaN(e.getTime())||`Expected a valid \`Date\` object, but received: ${o(e)}`))}function R(e){const t={},n=e.map((e=>o(e))).join();for(const n of e)t[n]=n;return new d({type:"enums",schema:t,validator:t=>e.includes(t)||`Expected one of \`${n}\`, but received: ${o(t)}`})}function P(){return y("func",(e=>"function"==typeof e||`Expected a function, but received: ${o(e)}`))}function O(e){return y("instance",(t=>t instanceof e||`Expected a \`${e.name}\` instance, but received: ${o(t)}`))}function T(){return y("integer",(e=>"number"==typeof e&&!isNaN(e)&&Number.isInteger(e)||`Expected an integer, but received: ${o(e)}`))}function N(e){return new d({type:"intersection",schema:null,*entries(t,n){for(const r of e)yield*r.entries(t,n)},*validator(t,n){for(const r of e)yield*r.validator(t,n)},*refiner(t,n){for(const r of e)yield*r.refiner(t,n)}})}function L(e){const t=o(e),n=typeof e;return new d({type:"literal",schema:"string"===n||"number"===n||"boolean"===n?e:null,validator:n=>n===e||`Expected the literal \`${t}\`, but received: ${o(n)}`})}function D(e,t){return new d({type:"map",schema:null,*entries(n){if(e&&t&&n instanceof Map)for(const[r,i]of n.entries())yield[r,r,e],yield[r,i,t]},coercer:e=>e instanceof Map?new Map(e):e,validator:e=>e instanceof Map||`Expected a \`Map\` object, but received: ${o(e)}`})}function B(){return y("never",(()=>!1))}function K(e){return new d({...e,validator:(t,n)=>null===t||e.validator(t,n),refiner:(t,n)=>null===t||e.refiner(t,n)})}function j(){return y("number",(e=>"number"==typeof e&&!isNaN(e)||`Expected a number, but received: ${o(e)}`))}function $(e){const t=e?Object.keys(e):[],n=B();return new d({type:"object",schema:e||null,*entries(i){if(e&&r(i)){const r=new Set(Object.keys(i));for(const n of t)r.delete(n),yield[n,i[n],e[n]];for(const e of r)yield[e,i[e],n]}},validator:e=>r(e)||`Expected an object, but received: ${o(e)}`,coercer:e=>r(e)?{...e}:e})}function U(e){return new d({...e,validator:(t,n)=>void 0===t||e.validator(t,n),refiner:(t,n)=>void 0===t||e.refiner(t,n)})}function H(e,t){return new d({type:"record",schema:null,*entries(n){if(r(n))for(const r in n){const i=n[r];yield[r,r,e],yield[r,i,t]}},validator:e=>r(e)||`Expected an object, but received: ${o(e)}`})}function F(){return y("regexp",(e=>e instanceof RegExp))}function q(e){return new d({type:"set",schema:null,*entries(t){if(e&&t instanceof Set)for(const n of t)yield[n,n,e]},coercer:e=>e instanceof Set?new Set(e):e,validator:e=>e instanceof Set||`Expected a \`Set\` object, but received: ${o(e)}`})}function z(){return y("string",(e=>"string"==typeof e||`Expected a string, but received: ${o(e)}`))}function W(e){const t=B();return new d({type:"tuple",schema:null,*entries(n){if(Array.isArray(n)){const r=Math.max(e.length,n.length);for(let i=0;i<r;i++)yield[i,n[i],e[i]||t]}},validator:e=>Array.isArray(e)||`Expected an array, but received: ${o(e)}`})}function V(e){const t=Object.keys(e);return new d({type:"type",schema:e,*entries(n){if(r(n))for(const r of t)yield[r,n[r],e[r]]},validator:e=>r(e)||`Expected an object, but received: ${o(e)}`,coercer:e=>r(e)?{...e}:e})}function G(e){const t=e.map((e=>e.type)).join(" | ");return new d({type:"union",schema:null,coercer(t){for(const n of e){const[e,r]=n.validate(t,{coerce:!0});if(!e)return r}return t},validator(n,r){const i=[];for(const t of e){const[...e]=l(n,t,r),[o]=e;if(!o[0])return[];for(const[t]of e)t&&i.push(t)}return[`Expected the value to satisfy a union of \`${t}\`, but received: ${o(n)}`,...i]}})}function Y(){return y("unknown",(()=>!0))}function Z(e,t,n){return new d({...e,coercer:(r,i)=>p(r,t)?e.coercer(n(r,i),i):e.coercer(r,i)})}function J(e,t,n={}){return Z(e,Y(),(e=>{const r="function"==typeof t?t():t;if(void 0===e)return r;if(!n.strict&&i(e)&&i(r)){const t={...e};let n=!1;for(const e in r)void 0===t[e]&&(t[e]=r[e],n=!0);if(n)return t}return e}))}function Q(e){return Z(e,z(),(e=>e.trim()))}function X(e){return se(e,"empty",(t=>{const n=ee(t);return 0===n||`Expected an empty ${e.type} but received one with a size of \`${n}\``}))}function ee(e){return e instanceof Map||e instanceof Set?e.size:e.length}function te(e,t,n={}){const{exclusive:r}=n;return se(e,"max",(n=>r?n<t:n<=t||`Expected a ${e.type} less than ${r?"":"or equal to "}${t} but received \`${n}\``))}function ne(e,t,n={}){const{exclusive:r}=n;return se(e,"min",(n=>r?n>t:n>=t||`Expected a ${e.type} greater than ${r?"":"or equal to "}${t} but received \`${n}\``))}function re(e){return se(e,"nonempty",(t=>ee(t)>0||`Expected a nonempty ${e.type} but received an empty one`))}function ie(e,t){return se(e,"pattern",(n=>t.test(n)||`Expected a ${e.type} matching \`/${t.source}/\` but received "${n}"`))}function oe(e,t,n=t){const r=`Expected a ${e.type}`,i=t===n?`of \`${t}\``:`between \`${t}\` and \`${n}\``;return se(e,"size",(e=>{if("number"==typeof e||e instanceof Date)return t<=e&&e<=n||`${r} ${i} but received \`${e}\``;if(e instanceof Map||e instanceof Set){const{size:o}=e;return t<=o&&o<=n||`${r} with a size ${i} but received one with a size of \`${o}\``}{const{length:o}=e;return t<=o&&o<=n||`${r} with a length ${i} but received one with a length of \`${o}\``}}))}function se(e,t,n){return new d({...e,*refiner(r,i){yield*e.refiner(r,i);const o=c(n(r,i),i,e,r);for(const e of o)yield{...e,refinement:t}}})}e.Struct=d,e.StructError=t,e.any=k,e.array=M,e.assert=u,e.assign=g,e.bigint=A,e.boolean=x,e.coerce=Z,e.create=h,e.date=I,e.defaulted=J,e.define=y,e.deprecated=v,e.dynamic=b,e.empty=X,e.enums=R,e.func=P,e.instance=O,e.integer=T,e.intersection=N,e.is=p,e.lazy=E,e.literal=L,e.map=D,e.mask=f,e.max=te,e.min=ne,e.never=B,e.nonempty=re,e.nullable=K,e.number=j,e.object=$,e.omit=w,e.optional=U,e.partial=S,e.pattern=ie,e.pick=_,e.record=H,e.refine=se,e.regexp=F,e.set=q,e.size=oe,e.string=z,e.struct=C,e.trimmed=Q,e.tuple=W,e.type=V,e.union=G,e.unknown=Y,e.validate=m}(So.exports);var _o=So.exports;function Co(e){return function(e){return function(e){return"object"==typeof e&&null!==e&&"message"in e}(e)&&"string"==typeof e.message?e.message:null==e?"":String(e)}(e).replace(/\.$/u,"")}function ko(e,t){return n=e,Boolean("string"==typeof n?.prototype?.constructor?.name)?new e({message:t}):e({message:t});var n}var Mo=class extends Error{constructor(e){super(e.message),this.code="ERR_ASSERTION"}};var Ao=Object.freeze({__proto__:null,default:{}}),xo=e=>_o.object(e);function Io({path:e,branch:t}){const n=e[e.length-1];return wo(t[t.length-2],n)}function Ro(e){return new _o.Struct({...e,type:`optional ${e.type}`,validator:(t,n)=>!Io(n)||e.validator(t,n),refiner:(t,n)=>!Io(n)||e.refiner(t,n)})}var Po=_o.union([_o.literal(null),_o.boolean(),_o.define("finite number",(e=>_o.is(e,_o.number())&&Number.isFinite(e))),_o.string(),_o.array(_o.lazy((()=>Po))),_o.record(_o.string(),_o.lazy((()=>Po)))]),Oo=_o.coerce(Po,_o.any(),(e=>(function(e,t,n="Assertion failed",r=Mo){try{_o.assert(e,t)}catch(e){throw ko(r,`${n}: ${Co(e)}.`)}}(e,Po),JSON.parse(JSON.stringify(e,((e,t)=>{if("__proto__"!==e&&"constructor"!==e)return t}))))));function To(e){try{return function(e){_o.create(e,Oo)}(e),!0}catch{return!1}}var No=_o.literal("2.0"),Lo=_o.nullable(_o.union([_o.number(),_o.string()])),Do=xo({code:_o.integer(),message:_o.string(),data:Ro(Oo),stack:Ro(_o.string())}),Bo=_o.union([_o.record(_o.string(),Oo),_o.array(Oo)]),Ko=xo({id:Lo,jsonrpc:No,method:_o.string(),params:Ro(Bo)}),jo=xo({jsonrpc:No,method:_o.string(),params:Ro(Bo)});function $o(e){return _o.is(e,Ko)}_o.object({id:Lo,jsonrpc:No,result:_o.optional(_o.unknown()),error:_o.optional(Do)});var Uo=xo({id:Lo,jsonrpc:No,result:Oo}),Ho=xo({id:Lo,jsonrpc:No,error:Do});function Fo(e){return _o.is(e,Do)}_o.union([Uo,Ho]);var qo=vo.internal,zo="Unspecified error message. This is a bug, please report it.",Wo={code:qo,message:Go(qo)},Vo="Unspecified server error.";function Go(e,t=zo){if(function(e){return Number.isInteger(e)}(e)){const t=e.toString();if(wo(bo,t))return bo[t].message;if(function(e){return e>=-32099&&e<=-32e3}(e))return Vo}return t}function Yo(e,{fallbackError:t=Wo,shouldIncludeStack:n=!0}={}){if(!Fo(t))throw new Error("Must provide fallback error with integer number code and string message.");const r=function(e,t){if(e&&"object"==typeof e&&"serialize"in e&&"function"==typeof e.serialize)return e.serialize();if(Fo(e))return e;const n=Zo(e),r={...t,data:{cause:n}};return r}(e,t);return n||delete r.stack,r}function Zo(e){return Array.isArray(e)?e.map((e=>To(e)?e:Eo(e)?Jo(e):null)):Eo(e)?Jo(e):To(e)?e:null}function Jo(e){return Object.getOwnPropertyNames(e).reduce(((t,n)=>{const r=e[n];return To(r)&&(t[n]=r),t}),{})}var Qo=is;is.default=is,is.stable=cs,is.stableStringify=cs;var Xo="[...]",es="[Circular]",ts=[],ns=[];function rs(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function is(e,t,n,r){var i;void 0===r&&(r=rs()),ss(e,"",0,[],void 0,0,r);try{i=0===ns.length?JSON.stringify(e,t,n):JSON.stringify(e,ds(t),n)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==ts.length;){var o=ts.pop();4===o.length?Object.defineProperty(o[0],o[1],o[3]):o[0][o[1]]=o[2]}}return i}function os(e,t,n,r){var i=Object.getOwnPropertyDescriptor(r,n);void 0!==i.get?i.configurable?(Object.defineProperty(r,n,{value:e}),ts.push([r,n,t,i])):ns.push([t,n,e]):(r[n]=e,ts.push([r,n,t]))}function ss(e,t,n,r,i,o,s){var a;if(o+=1,"object"==typeof e&&null!==e){for(a=0;a<r.length;a++)if(r[a]===e)return void os(es,e,t,i);if(void 0!==s.depthLimit&&o>s.depthLimit)return void os(Xo,e,t,i);if(void 0!==s.edgesLimit&&n+1>s.edgesLimit)return void os(Xo,e,t,i);if(r.push(e),Array.isArray(e))for(a=0;a<e.length;a++)ss(e[a],a,a,r,e,o,s);else{var c=Object.keys(e);for(a=0;a<c.length;a++){var l=c[a];ss(e[l],l,a,r,e,o,s)}}r.pop()}}function as(e,t){return e<t?-1:e>t?1:0}function cs(e,t,n,r){void 0===r&&(r=rs());var i,o=ls(e,"",0,[],void 0,0,r)||e;try{i=0===ns.length?JSON.stringify(o,t,n):JSON.stringify(o,ds(t),n)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==ts.length;){var s=ts.pop();4===s.length?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}}return i}function ls(e,t,n,r,i,o,s){var a;if(o+=1,"object"==typeof e&&null!==e){for(a=0;a<r.length;a++)if(r[a]===e)return void os(es,e,t,i);try{if("function"==typeof e.toJSON)return}catch(e){return}if(void 0!==s.depthLimit&&o>s.depthLimit)return void os(Xo,e,t,i);if(void 0!==s.edgesLimit&&n+1>s.edgesLimit)return void os(Xo,e,t,i);if(r.push(e),Array.isArray(e))for(a=0;a<e.length;a++)ls(e[a],a,a,r,e,o,s);else{var c={},l=Object.keys(e).sort(as);for(a=0;a<l.length;a++){var d=l[a];ls(e[d],d,a,r,e,o,s),c[d]=e[d]}if(void 0===i)return c;ts.push([i,t,e]),i[t]=c}r.pop()}}function ds(e){return e=void 0!==e?e:function(e,t){return t},function(t,n){if(ns.length>0)for(var r=0;r<ns.length;r++){var i=ns[r];if(i[1]===t&&i[0]===n){n=i[2],ns.splice(r,1);break}}return e.call(this,t,n)}}var us=m(Qo),hs=class extends Error{constructor(e,t,n){if(!Number.isInteger(e))throw new Error('"code" must be an integer.');if(!t||"string"!=typeof t)throw new Error('"message" must be a non-empty string.');super(t),this.code=e,void 0!==n&&(this.data=n)}serialize(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data,function(e){if("object"!=typeof e||null===e)return!1;try{let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}catch(e){return!1}}(this.data)&&(e.data.cause=Zo(this.data.cause))),this.stack&&(e.stack=this.stack),e}toString(){return us(this.serialize(),fs,2)}};function fs(e,t){if("[Circular]"!==t)return t}var ps=e=>ms(vo.invalidRequest,e);function ms(e,t){const[n,r]=function(e){if(e){if("string"==typeof e)return[e];if("object"==typeof e&&!Array.isArray(e)){const{message:t,data:n}=e;if(t&&"string"!=typeof t)throw new Error("Must specify string message.");return[t??void 0,n]}}return[]}(t);return new hs(e,n??Go(e),r)}var gs={};Object.defineProperty(gs,"__esModule",{value:!0});const ys=d;function vs(e,t,n){try{Reflect.apply(e,t,n)}catch(e){setTimeout((()=>{throw e}))}}let bs=class extends ys.EventEmitter{emit(e,...t){let n="error"===e;const r=this._events;if(void 0!==r)n=n&&void 0===r.error;else if(!n)return!1;if(n){let e;if(t.length>0&&([e]=t),e instanceof Error)throw e;const n=new Error("Unhandled error."+(e?` (${e.message})`:""));throw n.context=e,n}const i=r[e];if(void 0===i)return!1;if("function"==typeof i)vs(i,this,t);else{const e=i.length,n=function(e){const t=e.length,n=new Array(t);for(let r=0;r<t;r+=1)n[r]=e[r];return n}(i);for(let r=0;r<e;r+=1)vs(n[r],this,t)}return!0}};var Es,ws,Ss,_s,Cs,ks,Ms,As,xs,Is,Rs,Ps,Os,Ts,Ns,Ls,Ds,Bs,Ks,js=gs.default=bs,$s=class e extends js{constructor({notificationHandler:e}={}){super(),mo(this,_s),mo(this,ks),mo(this,As),mo(this,Es,!1),mo(this,ws,void 0),mo(this,Ss,void 0),go(this,ws,[]),go(this,Ss,e)}destroy(){po(this,ws).forEach((e=>{"destroy"in e&&"function"==typeof e.destroy&&e.destroy()})),go(this,ws,[]),go(this,Es,!0)}push(e){yo(this,_s,Cs).call(this),po(this,ws).push(e)}handle(e,t){if(yo(this,_s,Cs).call(this),t&&"function"!=typeof t)throw new Error('"callback" must be a function if provided.');return Array.isArray(e)?t?yo(this,ks,Ms).call(this,e,t):yo(this,ks,Ms).call(this,e):t?yo(this,As,xs).call(this,e,t):this._promiseHandle(e)}asMiddleware(){return yo(this,_s,Cs).call(this),async(t,n,r,i)=>{var o,s;try{const[a,c,l]=await yo(o=e,Ps,Os).call(o,t,n,po(this,ws));return c?(await yo(s=e,Ls,Ds).call(s,l),i(a)):r((async t=>{var n;try{await yo(n=e,Ls,Ds).call(n,l)}catch(e){return t(e)}return t()}))}catch(e){return i(e)}}}async _promiseHandle(e){return new Promise(((t,n)=>{yo(this,As,xs).call(this,e,((e,r)=>{e&&void 0===r?n(e):t(r)})).catch(n)}))}};Es=new WeakMap,ws=new WeakMap,Ss=new WeakMap,_s=new WeakSet,Cs=function(){if(po(this,Es))throw new Error("This engine is destroyed and can no longer be used.")},ks=new WeakSet,Ms=async function(e,t){try{if(0===e.length){const e=[{id:null,jsonrpc:"2.0",error:new hs(vo.invalidRequest,"Request batch must contain plain objects. Received an empty array")}];return t?t(null,e):e}const n=(await Promise.all(e.map(this._promiseHandle.bind(this)))).filter((e=>void 0!==e));return t?t(null,n):n}catch(e){if(t)return t(e);throw e}},As=new WeakSet,xs=async function(e,t){var n,r;if(!e||Array.isArray(e)||"object"!=typeof e){const n=new hs(vo.invalidRequest,"Requests must be plain objects. Received: "+typeof e,{request:e});return t(n,{id:null,jsonrpc:"2.0",error:n})}if("string"!=typeof e.method){const n=new hs(vo.invalidRequest,"Must specify a string method. Received: "+typeof e.method,{request:e});return po(this,Ss)&&!$o(e)?t(null):t(n,{id:e.id??null,jsonrpc:"2.0",error:n})}if(po(this,Ss)&&(r=e,_o.is(r,jo))&&!$o(e)){try{await po(this,Ss).call(this,e)}catch(e){return t(e)}return t(null)}let i=null;const o={...e},s={id:o.id,jsonrpc:o.jsonrpc};try{await yo(n=$s,Is,Rs).call(n,o,s,po(this,ws))}catch(e){i=e}return i&&(delete s.result,s.error||(s.error=Yo(i))),t(i,s)},Is=new WeakSet,Rs=async function(e,t,n){var r,i,o;const[s,a,c]=await yo(r=$s,Ps,Os).call(r,e,t,n);if(yo(i=$s,Bs,Ks).call(i,e,t,a),await yo(o=$s,Ls,Ds).call(o,c),s)throw s},Ps=new WeakSet,Os=async function(e,t,n){var r;const i=[];let o=null,s=!1;for(const a of n)if([o,s]=await yo(r=$s,Ts,Ns).call(r,e,t,a,i),s)break;return[o,s,i.reverse()]},Ts=new WeakSet,Ns=async function(e,t,n,r){return new Promise((i=>{const o=e=>{const n=e||t.error;n&&(t.error=Yo(n)),i([n,!0])},s=n=>{t.error?o(t.error):(n&&("function"!=typeof n&&o(new hs(vo.internal,`JsonRpcEngine: "next" return handlers must be functions. Received "${typeof n}" for request:\n${Hs(e)}`,{request:e})),r.push(n)),i([null,!1]))};try{n(e,t,s,o)}catch(e){o(e)}}))},Ls=new WeakSet,Ds=async function(e){for(const t of e)await new Promise(((e,n)=>{t((t=>t?n(t):e()))}))},Bs=new WeakSet,Ks=function(e,t,n){if(!wo(t,"result")&&!wo(t,"error"))throw new hs(vo.internal,`JsonRpcEngine: Response has no error or result for request:\n${Hs(e)}`,{request:e});if(!n)throw new hs(vo.internal,`JsonRpcEngine: Nothing ended request:\n${Hs(e)}`,{request:e})},mo($s,Is),mo($s,Ps),mo($s,Ts),mo($s,Ls),mo($s,Bs);var Us=$s;function Hs(e){return JSON.stringify(e,null,2)}var Fs=Object.freeze(["eth_subscription"]),qs=(e=console)=>{return[ho(),(t=e,(e,n,r)=>{"string"==typeof e.method&&e.method||(n.error=ps({message:"The request 'method' must be a non-empty string.",data:e})),r((e=>{const{error:r}=n;return r?(t.error(`MetaMask - RPC Error: ${r.message}`,r),e()):e()}))}),co(e)];var t};var zs=(e,t,n=!0)=>(r,i)=>{r||i.error?t(r||i.error):!n||Array.isArray(i)?e(i):e(i.result)},Ws=e=>Boolean(e)&&"string"==typeof e&&e.startsWith("0x"),Vs=()=>{};async function Gs(e,t){try{const t=await async function(){return{name:Ys(window),icon:await Zs(window)}}();e.handle({jsonrpc:"2.0",id:1,method:"metamask_sendDomainMetadata",params:t},Vs)}catch(e){t.error({message:ao.errors.sendSiteMetadata(),originalError:e})}}function Ys(e){const{document:t}=e,n=t.querySelector('head > meta[property="og:site_name"]');if(n)return n.content;const r=t.querySelector('head > meta[name="title"]');return r?r.content:t.title&&t.title.length>0?t.title:window.location.hostname}async function Zs(e){const{document:t}=e,n=t.querySelectorAll('head > link[rel~="icon"]');for(const e of Array.from(n))if(e&&await Js(e.href))return e.href;return null}async function Js(e){return new Promise(((t,n)=>{try{const n=document.createElement("img");n.onload=()=>t(!0),n.onerror=()=>t(!1),n.src=e}catch(e){n(e)}}))}var Qs=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},Xs=(e,t,n)=>(Qs(e,t,"read from private field"),n?n.call(e):t.get(e)),ea=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},ta=(e,t,n,r)=>(Qs(e,t,"write to private field"),t.set(e,n),n);function na(e,t,n){try{Reflect.apply(e,t,n)}catch(e){setTimeout((()=>{throw e}))}}class ra extends u{emit(e,...t){let n="error"===e;const r=this._events;if(void 0!==r)n=n&&void 0===r.error;else if(!n)return!1;if(n){let e;if(t.length>0&&([e]=t),e instanceof Error)throw e;const n=new Error("Unhandled error."+(e?` (${e.message})`:""));throw n.context=e,n}const i=r[e];if(void 0===i)return!1;if("function"==typeof i)na(i,this,t);else{const e=i.length,n=function(e){const t=e.length,n=new Array(t);for(let r=0;r<t;r+=1)n[r]=e[r];return n}(i);for(let r=0;r<e;r+=1)na(n[r],this,t)}return!0}}var ia,oa,sa=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,i,o;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(!e(t[i],n[i]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var s=o[i];if(!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n},aa=m(sa),ca=class e extends ra{constructor({logger:t=console,maxEventListeners:n=100,rpcMiddleware:r=[]}={}){super(),ea(this,ia,void 0),ea(this,oa,void 0),this._log=t,this.setMaxListeners(n),this._state={...e._defaultState},ta(this,oa,null),ta(this,ia,null),this._handleAccountsChanged=this._handleAccountsChanged.bind(this),this._handleConnect=this._handleConnect.bind(this),this._handleChainChanged=this._handleChainChanged.bind(this),this._handleDisconnect=this._handleDisconnect.bind(this),this._handleUnlockStateChanged=this._handleUnlockStateChanged.bind(this),this._rpcRequest=this._rpcRequest.bind(this),this.request=this.request.bind(this);const i=new Us;r.forEach((e=>i.push(e))),this._rpcEngine=i}get chainId(){return Xs(this,ia)}get selectedAddress(){return Xs(this,oa)}isConnected(){return this._state.isConnected}async request(e){if(!e||"object"!=typeof e||Array.isArray(e))throw ps({message:ao.errors.invalidRequestArgs(),data:e});const{method:t,params:n}=e;if("string"!=typeof t||0===t.length)throw ps({message:ao.errors.invalidRequestMethod(),data:e});if(void 0!==n&&!Array.isArray(n)&&("object"!=typeof n||null===n))throw ps({message:ao.errors.invalidRequestParams(),data:e});const r=null==n?{method:t}:{method:t,params:n};return new Promise(((e,t)=>{this._rpcRequest(r,zs(e,t))}))}_initializeState(e){if(this._state.initialized)throw new Error("Provider already initialized.");if(e){const{accounts:t,chainId:n,isUnlocked:r,networkVersion:i}=e;this._handleConnect(n),this._handleChainChanged({chainId:n,networkVersion:i}),this._handleUnlockStateChanged({accounts:t,isUnlocked:r}),this._handleAccountsChanged(t)}this._state.initialized=!0,this.emit("_initialized")}_rpcRequest(e,t){let n=t;return Array.isArray(e)||(e.jsonrpc||(e.jsonrpc="2.0"),"eth_accounts"!==e.method&&"eth_requestAccounts"!==e.method||(n=(n,r)=>{this._handleAccountsChanged(r.result??[],"eth_accounts"===e.method),t(n,r)})),this._rpcEngine.handle(e,n)}_handleConnect(e){this._state.isConnected||(this._state.isConnected=!0,this.emit("connect",{chainId:e}),this._log.debug(ao.info.connected(e)))}_handleDisconnect(e,t){if(this._state.isConnected||!this._state.isPermanentlyDisconnected&&!e){let n;this._state.isConnected=!1,e?(n=new hs(1013,t??ao.errors.disconnected()),this._log.debug(n)):(n=new hs(1011,t??ao.errors.permanentlyDisconnected()),this._log.error(n),ta(this,ia,null),this._state.accounts=null,ta(this,oa,null),this._state.isUnlocked=!1,this._state.isPermanentlyDisconnected=!0),this.emit("disconnect",n)}}_handleChainChanged({chainId:e}={}){Ws(e)?(this._handleConnect(e),e!==Xs(this,ia)&&(ta(this,ia,e),this._state.initialized&&this.emit("chainChanged",Xs(this,ia)))):this._log.error(ao.errors.invalidNetworkParams(),{chainId:e})}_handleAccountsChanged(e,t=!1){let n=e;Array.isArray(e)||(this._log.error("MetaMask: Received invalid accounts parameter. Please report this bug.",e),n=[]);for(const t of e)if("string"!=typeof t){this._log.error("MetaMask: Received non-string account. Please report this bug.",e),n=[];break}if(!aa(this._state.accounts,n)&&(t&&null!==this._state.accounts&&this._log.error("MetaMask: 'eth_accounts' unexpectedly updated accounts. Please report this bug.",n),this._state.accounts=n,Xs(this,oa)!==n[0]&&ta(this,oa,n[0]||null),this._state.initialized)){const e=[...n];this.emit("accountsChanged",e)}}_handleUnlockStateChanged({accounts:e,isUnlocked:t}={}){"boolean"==typeof t?t!==this._state.isUnlocked&&(this._state.isUnlocked=t,this._handleAccountsChanged(e??[])):this._log.error("MetaMask: Received invalid isUnlocked parameter. Please report this bug.")}};ia=new WeakMap,oa=new WeakMap,ca._defaultState={accounts:null,isConnected:!1,isUnlocked:!1,initialized:!1,isPermanentlyDisconnected:!1};var la,da,ua=ca,ha={exports:{}},fa=d.EventEmitter,pa=g(Ao);function ma(){if(da)return la;function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function t(t){for(var n=1;n<arguments.length;n++){var i=null!=arguments[n]?arguments[n]:{};n%2?e(Object(i),!0).forEach((function(e){r(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):e(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function r(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,o(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}da=1;var s=n.Buffer,a=pa.inspect,c=a&&a.custom||"inspect";return la=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}return i(e,[{key:"push",value:function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length}},{key:"unshift",value:function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length}},{key:"shift",value:function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(e){if(0===this.length)return"";for(var t=this.head,n=""+t.data;t=t.next;)n+=e+t.data;return n}},{key:"concat",value:function(e){if(0===this.length)return s.alloc(0);for(var t,n,r,i=s.allocUnsafe(e>>>0),o=this.head,a=0;o;)t=o.data,n=i,r=a,s.prototype.copy.call(t,n,r),a+=o.data.length,o=o.next;return i}},{key:"consume",value:function(e,t){var n;return e<this.head.data.length?(n=this.head.data.slice(0,e),this.head.data=this.head.data.slice(e)):n=e===this.head.data.length?this.shift():t?this._getString(e):this._getBuffer(e),n}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(e){var t=this.head,n=1,r=t.data;for(e-=r.length;t=t.next;){var i=t.data,o=e>i.length?i.length:e;if(o===i.length?r+=i:r+=i.slice(0,e),0===(e-=o)){o===i.length?(++n,t.next?this.head=t.next:this.head=this.tail=null):(this.head=t,t.data=i.slice(o));break}++n}return this.length-=n,r}},{key:"_getBuffer",value:function(e){var t=s.allocUnsafe(e),n=this.head,r=1;for(n.data.copy(t),e-=n.data.length;n=n.next;){var i=n.data,o=e>i.length?i.length:e;if(i.copy(t,t.length-e,0,o),0===(e-=o)){o===i.length?(++r,n.next?this.head=n.next:this.head=this.tail=null):(this.head=n,n.data=i.slice(o));break}++r}return this.length-=r,t}},{key:c,value:function(e,n){return a(this,t(t({},n),{},{depth:0,customInspect:!1}))}}]),e}(),la}function ga(e,t){va(e,t),ya(e)}function ya(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function va(e,t){e.emit("error",t)}var ba={destroy:function(e,t){var n=this,r=this._readableState&&this._readableState.destroyed,i=this._writableState&&this._writableState.destroyed;return r||i?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(va,this,e)):process.nextTick(va,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?n._writableState?n._writableState.errorEmitted?process.nextTick(ya,n):(n._writableState.errorEmitted=!0,process.nextTick(ga,n,e)):process.nextTick(ga,n,e):t?(process.nextTick(ya,n),t(e)):process.nextTick(ya,n)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(e,t){var n=e._readableState,r=e._writableState;n&&n.autoDestroy||r&&r.autoDestroy?e.destroy(t):e.emit("error",t)}},Ea={};var wa={};function Sa(e,t,n){n||(n=Error);var r=function(e){var n,r;function i(n,r,i){return e.call(this,function(e,n,r){return"string"==typeof t?t:t(e,n,r)}(n,r,i))||this}return r=e,(n=i).prototype=Object.create(r.prototype),n.prototype.constructor=n,n.__proto__=r,i}(n);r.prototype.name=n.name,r.prototype.code=e,wa[e]=r}function _a(e,t){if(Array.isArray(e)){var n=e.length;return e=e.map((function(e){return String(e)})),n>2?"one of ".concat(t," ").concat(e.slice(0,n-1).join(", "),", or ")+e[n-1]:2===n?"one of ".concat(t," ").concat(e[0]," or ").concat(e[1]):"of ".concat(t," ").concat(e[0])}return"of ".concat(t," ").concat(String(e))}Sa("ERR_INVALID_OPT_VALUE",(function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'}),TypeError),Sa("ERR_INVALID_ARG_TYPE",(function(e,t,n){var r,i,o;if("string"==typeof t&&(i="not ",t.substr(0,i.length)===i)?(r="must not be",t=t.replace(/^not /,"")):r="must be",function(e,t,n){return(void 0===n||n>e.length)&&(n=e.length),e.substring(n-t.length,n)===t}(e," argument"))o="The ".concat(e," ").concat(r," ").concat(_a(t,"type"));else{var s=function(e,t,n){return"number"!=typeof n&&(n=0),!(n+t.length>e.length)&&-1!==e.indexOf(t,n)}(e,".")?"property":"argument";o='The "'.concat(e,'" ').concat(s," ").concat(r," ").concat(_a(t,"type"))}return o+=". Received type ".concat(typeof n)}),TypeError),Sa("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),Sa("ERR_METHOD_NOT_IMPLEMENTED",(function(e){return"The "+e+" method is not implemented"})),Sa("ERR_STREAM_PREMATURE_CLOSE","Premature close"),Sa("ERR_STREAM_DESTROYED",(function(e){return"Cannot call "+e+" after a stream was destroyed"})),Sa("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),Sa("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),Sa("ERR_STREAM_WRITE_AFTER_END","write after end"),Sa("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),Sa("ERR_UNKNOWN_ENCODING",(function(e){return"Unknown encoding: "+e}),TypeError),Sa("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),Ea.codes=wa;var Ca=Ea.codes.ERR_INVALID_OPT_VALUE;var ka={getHighWaterMark:function(e,t,n,r){var i=function(e,t,n){return null!=e.highWaterMark?e.highWaterMark:t?e[n]:null}(t,r,n);if(null!=i){if(!isFinite(i)||Math.floor(i)!==i||i<0)throw new Ca(r?n:"highWaterMark",i);return Math.floor(i)}return e.objectMode?16:16384}},Ma={exports:{}};"function"==typeof Object.create?Ma.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:Ma.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}};var Aa,xa,Ia,Ra,Pa=Ma.exports,Oa=function(e,t){if(Ta("noDeprecation"))return e;var n=!1;return function(){if(!n){if(Ta("throwDeprecation"))throw new Error(t);Ta("traceDeprecation")?console.trace(t):console.warn(t),n=!0}return e.apply(this,arguments)}};function Ta(e){try{if(!p.localStorage)return!1}catch(e){return!1}var t=p.localStorage[e];return null!=t&&"true"===String(t).toLowerCase()}function Na(){if(xa)return Aa;function e(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,n){var r=e.entry;e.entry=null;for(;r;){var i=r.callback;t.pendingcb--,i(n),r=r.next}t.corkedRequestsFree.next=e}(t,e)}}var t;xa=1,Aa=_,_.WritableState=S;var r={deprecate:Oa},i=fa,o=n.Buffer,s=(void 0!==p?p:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var a,c=ba,l=ka.getHighWaterMark,d=Ea.codes,u=d.ERR_INVALID_ARG_TYPE,h=d.ERR_METHOD_NOT_IMPLEMENTED,f=d.ERR_MULTIPLE_CALLBACK,m=d.ERR_STREAM_CANNOT_PIPE,g=d.ERR_STREAM_DESTROYED,y=d.ERR_STREAM_NULL_VALUES,v=d.ERR_STREAM_WRITE_AFTER_END,b=d.ERR_UNKNOWN_ENCODING,E=c.errorOrDestroy;function w(){}function S(n,r,i){t=t||La(),n=n||{},"boolean"!=typeof i&&(i=r instanceof t),this.objectMode=!!n.objectMode,i&&(this.objectMode=this.objectMode||!!n.writableObjectMode),this.highWaterMark=l(this,n,"writableHighWaterMark",i),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var o=!1===n.decodeStrings;this.decodeStrings=!o,this.defaultEncoding=n.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var n=e._writableState,r=n.sync,i=n.writecb;if("function"!=typeof i)throw new f;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(n),t)!function(e,t,n,r,i){--t.pendingcb,n?(process.nextTick(i,r),process.nextTick(I,e,t),e._writableState.errorEmitted=!0,E(e,r)):(i(r),e._writableState.errorEmitted=!0,E(e,r),I(e,t))}(e,n,r,t,i);else{var o=A(n)||e.destroyed;o||n.corked||n.bufferProcessing||!n.bufferedRequest||M(e,n),r?process.nextTick(k,e,n,o,i):k(e,n,o,i)}}(r,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==n.emitClose,this.autoDestroy=!!n.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new e(this)}function _(e){var n=this instanceof(t=t||La());if(!n&&!a.call(_,this))return new _(e);this._writableState=new S(e,this,n),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),i.call(this)}function C(e,t,n,r,i,o,s){t.writelen=r,t.writecb=s,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new g("write")):n?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function k(e,t,n,r){n||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,r(),I(e,t)}function M(t,n){n.bufferProcessing=!0;var r=n.bufferedRequest;if(t._writev&&r&&r.next){var i=n.bufferedRequestCount,o=new Array(i),s=n.corkedRequestsFree;s.entry=r;for(var a=0,c=!0;r;)o[a]=r,r.isBuf||(c=!1),r=r.next,a+=1;o.allBuffers=c,C(t,n,!0,n.length,o,"",s.finish),n.pendingcb++,n.lastBufferedRequest=null,s.next?(n.corkedRequestsFree=s.next,s.next=null):n.corkedRequestsFree=new e(n),n.bufferedRequestCount=0}else{for(;r;){var l=r.chunk,d=r.encoding,u=r.callback;if(C(t,n,!1,n.objectMode?1:l.length,l,d,u),r=r.next,n.bufferedRequestCount--,n.writing)break}null===r&&(n.lastBufferedRequest=null)}n.bufferedRequest=r,n.bufferProcessing=!1}function A(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function x(e,t){e._final((function(n){t.pendingcb--,n&&E(e,n),t.prefinished=!0,e.emit("prefinish"),I(e,t)}))}function I(e,t){var n=A(t);if(n&&(function(e,t){t.prefinished||t.finalCalled||("function"!=typeof e._final||t.destroyed?(t.prefinished=!0,e.emit("prefinish")):(t.pendingcb++,t.finalCalled=!0,process.nextTick(x,e,t)))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var r=e._readableState;(!r||r.autoDestroy&&r.endEmitted)&&e.destroy()}return n}return Pa(_,i),S.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(S.prototype,"buffer",{get:r.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(a=Function.prototype[Symbol.hasInstance],Object.defineProperty(_,Symbol.hasInstance,{value:function(e){return!!a.call(this,e)||this===_&&(e&&e._writableState instanceof S)}})):a=function(e){return e instanceof this},_.prototype.pipe=function(){E(this,new m)},_.prototype.write=function(e,t,n){var r,i=this._writableState,a=!1,c=!i.objectMode&&(r=e,o.isBuffer(r)||r instanceof s);return c&&!o.isBuffer(e)&&(e=function(e){return o.from(e)}(e)),"function"==typeof t&&(n=t,t=null),c?t="buffer":t||(t=i.defaultEncoding),"function"!=typeof n&&(n=w),i.ending?function(e,t){var n=new v;E(e,n),process.nextTick(t,n)}(this,n):(c||function(e,t,n,r){var i;return null===n?i=new y:"string"==typeof n||t.objectMode||(i=new u("chunk",["string","Buffer"],n)),!i||(E(e,i),process.nextTick(r,i),!1)}(this,i,e,n))&&(i.pendingcb++,a=function(e,t,n,r,i,s){if(!n){var a=function(e,t,n){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=o.from(t,n));return t}(t,r,i);r!==a&&(n=!0,i="buffer",r=a)}var c=t.objectMode?1:r.length;t.length+=c;var l=t.length<t.highWaterMark;l||(t.needDrain=!0);if(t.writing||t.corked){var d=t.lastBufferedRequest;t.lastBufferedRequest={chunk:r,encoding:i,isBuf:n,callback:s,next:null},d?d.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else C(e,t,!1,c,r,i,s);return l}(this,i,c,e,t,n)),a},_.prototype.cork=function(){this._writableState.corked++},_.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||M(this,e))},_.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new b(e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(_.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(_.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),_.prototype._write=function(e,t,n){n(new h("_write()"))},_.prototype._writev=null,_.prototype.end=function(e,t,n){var r=this._writableState;return"function"==typeof e?(n=e,e=null,t=null):"function"==typeof t&&(n=t,t=null),null!=e&&this.write(e,t),r.corked&&(r.corked=1,this.uncork()),r.ending||function(e,t,n){t.ending=!0,I(e,t),n&&(t.finished?process.nextTick(n):e.once("finish",n));t.ended=!0,e.writable=!1}(this,r,n),this},Object.defineProperty(_.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(_.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),_.prototype.destroy=c.destroy,_.prototype._undestroy=c.undestroy,_.prototype._destroy=function(e,t){t(e)},Aa}function La(){if(Ra)return Ia;Ra=1;var e=Object.keys||function(e){var t=[];for(var n in e)t.push(n);return t};Ia=s;var t=Ja(),n=Na();Pa(s,t);for(var r=e(n.prototype),i=0;i<r.length;i++){var o=r[i];s.prototype[o]||(s.prototype[o]=n.prototype[o])}function s(e){if(!(this instanceof s))return new s(e);t.call(this,e),n.call(this,e),this.allowHalfOpen=!0,e&&(!1===e.readable&&(this.readable=!1),!1===e.writable&&(this.writable=!1),!1===e.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",a)))}function a(){this._writableState.ended||process.nextTick(c,this)}function c(e){e.end()}return Object.defineProperty(s.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(s.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(s.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(s.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),Ia}var Da,Ba,Ka={},ja={exports:{}};function $a(){if(Ba)return Ka;Ba=1;var e=(Da||(Da=1,function(e,t){var r=n,i=r.Buffer;function o(e,t){for(var n in e)t[n]=e[n]}function s(e,t,n){return i(e,t,n)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=r:(o(r,t),t.Buffer=s),s.prototype=Object.create(i.prototype),o(i,s),s.from=function(e,t,n){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,n)},s.alloc=function(e,t,n){if("number"!=typeof e)throw new TypeError("Argument must be a number");var r=i(e);return void 0!==t?"string"==typeof n?r.fill(t,n):r.fill(t):r.fill(0),r},s.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},s.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return r.SlowBuffer(e)}}(ja,ja.exports)),ja.exports).Buffer,t=e.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function r(n){var r;switch(this.encoding=function(n){var r=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(n);if("string"!=typeof r&&(e.isEncoding===t||!t(n)))throw new Error("Unknown encoding: "+n);return r||n}(n),this.encoding){case"utf16le":this.text=s,this.end=a,r=4;break;case"utf8":this.fillLast=o,r=4;break;case"base64":this.text=c,this.end=l,r=3;break;default:return this.write=d,void(this.end=u)}this.lastNeed=0,this.lastTotal=0,this.lastChar=e.allocUnsafe(r)}function i(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function o(e){var t=this.lastTotal-this.lastNeed,n=function(e,t,n){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==n?n:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function s(e,t){if((e.length-t)%2==0){var n=e.toString("utf16le",t);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function a(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,n)}return t}function c(e,t){var n=(e.length-t)%3;return 0===n?e.toString("base64",t):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-n))}function l(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function d(e){return e.toString(this.encoding)}function u(e){return e&&e.length?this.write(e):""}return Ka.StringDecoder=r,r.prototype.write=function(e){if(0===e.length)return"";var t,n;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<e.length?t?t+this.text(e,n):this.text(e,n):t||""},r.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},r.prototype.text=function(e,t){var n=function(e,t,n){var r=t.length-1;if(r<n)return 0;var o=i(t[r]);if(o>=0)return o>0&&(e.lastNeed=o-1),o;if(--r<n||-2===o)return 0;if(o=i(t[r]),o>=0)return o>0&&(e.lastNeed=o-2),o;if(--r<n||-2===o)return 0;if(o=i(t[r]),o>=0)return o>0&&(2===o?o=0:e.lastNeed=o-3),o;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=n;var r=e.length-(n-this.lastNeed);return e.copy(this.lastChar,0,r),e.toString("utf8",t,r)},r.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length},Ka}var Ua=Ea.codes.ERR_STREAM_PREMATURE_CLOSE;function Ha(){}var Fa,qa,za,Wa,Va,Ga,Ya=function e(t,n,r){if("function"==typeof n)return e(t,null,n);n||(n={}),r=function(e){var t=!1;return function(){if(!t){t=!0;for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];e.apply(this,r)}}}(r||Ha);var i=n.readable||!1!==n.readable&&t.readable,o=n.writable||!1!==n.writable&&t.writable,s=function(){t.writable||c()},a=t._writableState&&t._writableState.finished,c=function(){o=!1,a=!0,i||r.call(t)},l=t._readableState&&t._readableState.endEmitted,d=function(){i=!1,l=!0,o||r.call(t)},u=function(e){r.call(t,e)},h=function(){var e;return i&&!l?(t._readableState&&t._readableState.ended||(e=new Ua),r.call(t,e)):o&&!a?(t._writableState&&t._writableState.ended||(e=new Ua),r.call(t,e)):void 0},f=function(){t.req.on("finish",c)};return!function(e){return e.setHeader&&"function"==typeof e.abort}(t)?o&&!t._writableState&&(t.on("end",s),t.on("close",s)):(t.on("complete",c),t.on("abort",h),t.req?f():t.on("request",f)),t.on("end",d),t.on("finish",c),!1!==n.error&&t.on("error",u),t.on("close",h),function(){t.removeListener("complete",c),t.removeListener("abort",h),t.removeListener("request",f),t.req&&t.req.removeListener("finish",c),t.removeListener("end",s),t.removeListener("close",s),t.removeListener("finish",c),t.removeListener("end",d),t.removeListener("error",u),t.removeListener("close",h)}};function Za(){if(qa)return Fa;var e;function t(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}qa=1;var n=Ya,r=Symbol("lastResolve"),i=Symbol("lastReject"),o=Symbol("error"),s=Symbol("ended"),a=Symbol("lastPromise"),c=Symbol("handlePromise"),l=Symbol("stream");function d(e,t){return{value:e,done:t}}function u(e){var t=e[r];if(null!==t){var n=e[l].read();null!==n&&(e[a]=null,e[r]=null,e[i]=null,t(d(n,!1)))}}function h(e){process.nextTick(u,e)}var f=Object.getPrototypeOf((function(){})),p=Object.setPrototypeOf((t(e={get stream(){return this[l]},next:function(){var e=this,t=this[o];if(null!==t)return Promise.reject(t);if(this[s])return Promise.resolve(d(void 0,!0));if(this[l].destroyed)return new Promise((function(t,n){process.nextTick((function(){e[o]?n(e[o]):t(d(void 0,!0))}))}));var n,r=this[a];if(r)n=new Promise(function(e,t){return function(n,r){e.then((function(){t[s]?n(d(void 0,!0)):t[c](n,r)}),r)}}(r,this));else{var i=this[l].read();if(null!==i)return Promise.resolve(d(i,!1));n=new Promise(this[c])}return this[a]=n,n}},Symbol.asyncIterator,(function(){return this})),t(e,"return",(function(){var e=this;return new Promise((function(t,n){e[l].destroy(null,(function(e){e?n(e):t(d(void 0,!0))}))}))})),e),f);return Fa=function(e){var u,f=Object.create(p,(t(u={},l,{value:e,writable:!0}),t(u,r,{value:null,writable:!0}),t(u,i,{value:null,writable:!0}),t(u,o,{value:null,writable:!0}),t(u,s,{value:e._readableState.endEmitted,writable:!0}),t(u,c,{value:function(e,t){var n=f[l].read();n?(f[a]=null,f[r]=null,f[i]=null,e(d(n,!1))):(f[r]=e,f[i]=t)},writable:!0}),u));return f[a]=null,n(e,(function(e){if(e&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code){var t=f[i];return null!==t&&(f[a]=null,f[r]=null,f[i]=null,t(e)),void(f[o]=e)}var n=f[r];null!==n&&(f[a]=null,f[r]=null,f[i]=null,n(d(void 0,!0))),f[s]=!0})),e.on("readable",h.bind(null,f)),f}}function Ja(){if(Ga)return Va;var e;Ga=1,Va=C,C.ReadableState=_,d.EventEmitter;var t=function(e,t){return e.listeners(t).length},r=fa,i=n.Buffer,o=(void 0!==p?p:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var s,a=pa;s=a&&a.debuglog?a.debuglog("stream"):function(){};var c,l,u,h=ma(),f=ba,m=ka.getHighWaterMark,g=Ea.codes,y=g.ERR_INVALID_ARG_TYPE,v=g.ERR_STREAM_PUSH_AFTER_EOF,b=g.ERR_METHOD_NOT_IMPLEMENTED,E=g.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;Pa(C,r);var w=f.errorOrDestroy,S=["error","close","destroy","pause","resume"];function _(t,n,r){e=e||La(),t=t||{},"boolean"!=typeof r&&(r=n instanceof e),this.objectMode=!!t.objectMode,r&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=m(this,t,"readableHighWaterMark",r),this.buffer=new h,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(c||(c=$a().StringDecoder),this.decoder=new c(t.encoding),this.encoding=t.encoding)}function C(t){if(e=e||La(),!(this instanceof C))return new C(t);var n=this instanceof e;this._readableState=new _(t,this,n),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),r.call(this)}function k(e,t,n,r,a){s("readableAddChunk",t);var c,l=e._readableState;if(null===t)l.reading=!1,function(e,t){if(s("onEofChunk"),t.ended)return;if(t.decoder){var n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,t.sync?I(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,R(e)))}(e,l);else if(a||(c=function(e,t){var n;r=t,i.isBuffer(r)||r instanceof o||"string"==typeof t||void 0===t||e.objectMode||(n=new y("chunk",["string","Buffer","Uint8Array"],t));var r;return n}(l,t)),c)w(e,c);else if(l.objectMode||t&&t.length>0)if("string"==typeof t||l.objectMode||Object.getPrototypeOf(t)===i.prototype||(t=function(e){return i.from(e)}(t)),r)l.endEmitted?w(e,new E):M(e,l,t,!0);else if(l.ended)w(e,new v);else{if(l.destroyed)return!1;l.reading=!1,l.decoder&&!n?(t=l.decoder.write(t),l.objectMode||0!==t.length?M(e,l,t,!1):P(e,l)):M(e,l,t,!1)}else r||(l.reading=!1,P(e,l));return!l.ended&&(l.length<l.highWaterMark||0===l.length)}function M(e,t,n,r){t.flowing&&0===t.length&&!t.sync?(t.awaitDrain=0,e.emit("data",n)):(t.length+=t.objectMode?1:n.length,r?t.buffer.unshift(n):t.buffer.push(n),t.needReadable&&I(e)),P(e,t)}Object.defineProperty(C.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),C.prototype.destroy=f.destroy,C.prototype._undestroy=f.undestroy,C.prototype._destroy=function(e,t){t(e)},C.prototype.push=function(e,t){var n,r=this._readableState;return r.objectMode?n=!0:"string"==typeof e&&((t=t||r.defaultEncoding)!==r.encoding&&(e=i.from(e,t),t=""),n=!0),k(this,e,t,!1,n)},C.prototype.unshift=function(e){return k(this,e,null,!0,!1)},C.prototype.isPaused=function(){return!1===this._readableState.flowing},C.prototype.setEncoding=function(e){c||(c=$a().StringDecoder);var t=new c(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;for(var n=this._readableState.buffer.head,r="";null!==n;)r+=t.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==r&&this._readableState.buffer.push(r),this._readableState.length=r.length,this};var A=1073741824;function x(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=A?e=A:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function I(e){var t=e._readableState;s("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(s("emitReadable",t.flowing),t.emittedReadable=!0,process.nextTick(R,e))}function R(e){var t=e._readableState;s("emitReadable_",t.destroyed,t.length,t.ended),t.destroyed||!t.length&&!t.ended||(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,D(e)}function P(e,t){t.readingMore||(t.readingMore=!0,process.nextTick(O,e,t))}function O(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){var n=t.length;if(s("maybeReadMore read 0"),e.read(0),n===t.length)break}t.readingMore=!1}function T(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function N(e){s("readable nexttick read 0"),e.read(0)}function L(e,t){s("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),D(e),t.flowing&&!t.reading&&e.read(0)}function D(e){var t=e._readableState;for(s("flow",t.flowing);t.flowing&&null!==e.read(););}function B(e,t){return 0===t.length?null:(t.objectMode?n=t.buffer.shift():!e||e>=t.length?(n=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):n=t.buffer.consume(e,t.decoder),n);var n}function K(e){var t=e._readableState;s("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,process.nextTick(j,t,e))}function j(e,t){if(s("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&0===e.length&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var n=t._writableState;(!n||n.autoDestroy&&n.finished)&&t.destroy()}}function $(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}return C.prototype.read=function(e){s("read",e),e=parseInt(e,10);var t=this._readableState,n=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&((0!==t.highWaterMark?t.length>=t.highWaterMark:t.length>0)||t.ended))return s("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?K(this):I(this),null;if(0===(e=x(e,t))&&t.ended)return 0===t.length&&K(this),null;var r,i=t.needReadable;return s("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&s("length less than watermark",i=!0),t.ended||t.reading?s("reading or ended",i=!1):i&&(s("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=x(n,t))),null===(r=e>0?B(e,t):null)?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.awaitDrain=0),0===t.length&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&K(this)),null!==r&&this.emit("data",r),r},C.prototype._read=function(e){w(this,new b("_read()"))},C.prototype.pipe=function(e,n){var r=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=e;break;case 1:i.pipes=[i.pipes,e];break;default:i.pipes.push(e)}i.pipesCount+=1,s("pipe count=%d opts=%j",i.pipesCount,n);var o=(!n||!1!==n.end)&&e!==process.stdout&&e!==process.stderr?c:m;function a(t,n){s("onunpipe"),t===r&&n&&!1===n.hasUnpiped&&(n.hasUnpiped=!0,s("cleanup"),e.removeListener("close",f),e.removeListener("finish",p),e.removeListener("drain",l),e.removeListener("error",h),e.removeListener("unpipe",a),r.removeListener("end",c),r.removeListener("end",m),r.removeListener("data",u),d=!0,!i.awaitDrain||e._writableState&&!e._writableState.needDrain||l())}function c(){s("onend"),e.end()}i.endEmitted?process.nextTick(o):r.once("end",o),e.on("unpipe",a);var l=function(e){return function(){var n=e._readableState;s("pipeOnDrain",n.awaitDrain),n.awaitDrain&&n.awaitDrain--,0===n.awaitDrain&&t(e,"data")&&(n.flowing=!0,D(e))}}(r);e.on("drain",l);var d=!1;function u(t){s("ondata");var n=e.write(t);s("dest.write",n),!1===n&&((1===i.pipesCount&&i.pipes===e||i.pipesCount>1&&-1!==$(i.pipes,e))&&!d&&(s("false write response, pause",i.awaitDrain),i.awaitDrain++),r.pause())}function h(n){s("onerror",n),m(),e.removeListener("error",h),0===t(e,"error")&&w(e,n)}function f(){e.removeListener("finish",p),m()}function p(){s("onfinish"),e.removeListener("close",f),m()}function m(){s("unpipe"),r.unpipe(e)}return r.on("data",u),function(e,t,n){if("function"==typeof e.prependListener)return e.prependListener(t,n);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(n):e._events[t]=[n,e._events[t]]:e.on(t,n)}(e,"error",h),e.once("close",f),e.once("finish",p),e.emit("pipe",r),i.flowing||(s("pipe resume"),r.resume()),e},C.prototype.unpipe=function(e){var t=this._readableState,n={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,n)),this;if(!e){var r=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)r[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=$(t.pipes,e);return-1===s||(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,n)),this},C.prototype.on=function(e,t){var n=r.prototype.on.call(this,e,t),i=this._readableState;return"data"===e?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"===e&&(i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,s("on readable",i.length,i.reading),i.length?I(this):i.reading||process.nextTick(N,this))),n},C.prototype.addListener=C.prototype.on,C.prototype.removeListener=function(e,t){var n=r.prototype.removeListener.call(this,e,t);return"readable"===e&&process.nextTick(T,this),n},C.prototype.removeAllListeners=function(e){var t=r.prototype.removeAllListeners.apply(this,arguments);return"readable"!==e&&void 0!==e||process.nextTick(T,this),t},C.prototype.resume=function(){var e=this._readableState;return e.flowing||(s("resume"),e.flowing=!e.readableListening,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,process.nextTick(L,e,t))}(this,e)),e.paused=!1,this},C.prototype.pause=function(){return s("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(s("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},C.prototype.wrap=function(e){var t=this,n=this._readableState,r=!1;for(var i in e.on("end",(function(){if(s("wrapped end"),n.decoder&&!n.ended){var e=n.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(i){(s("wrapped data"),n.decoder&&(i=n.decoder.write(i)),n.objectMode&&null==i)||(n.objectMode||i&&i.length)&&(t.push(i)||(r=!0,e.pause()))})),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<S.length;o++)e.on(S[o],this.emit.bind(this,S[o]));return this._read=function(t){s("wrapped _read",t),r&&(r=!1,e.resume())},this},"function"==typeof Symbol&&(C.prototype[Symbol.asyncIterator]=function(){return void 0===l&&(l=Za()),l(this)}),Object.defineProperty(C.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(C.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(C.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}}),C._fromList=B,Object.defineProperty(C.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(C.from=function(e,t){return void 0===u&&(u=Wa?za:(Wa=1,za=function(){throw new Error("Readable.from is not available in the browser")})),u(C,e,t)}),Va}var Qa=sc,Xa=Ea.codes,ec=Xa.ERR_METHOD_NOT_IMPLEMENTED,tc=Xa.ERR_MULTIPLE_CALLBACK,nc=Xa.ERR_TRANSFORM_ALREADY_TRANSFORMING,rc=Xa.ERR_TRANSFORM_WITH_LENGTH_0,ic=La();function oc(e,t){var n=this._transformState;n.transforming=!1;var r=n.writecb;if(null===r)return this.emit("error",new tc);n.writechunk=null,n.writecb=null,null!=t&&this.push(t),r(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function sc(e){if(!(this instanceof sc))return new sc(e);ic.call(this,e),this._transformState={afterTransform:oc.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",ac)}function ac(){var e=this;"function"!=typeof this._flush||this._readableState.destroyed?cc(this,null,null):this._flush((function(t,n){cc(e,t,n)}))}function cc(e,t,n){if(t)return e.emit("error",t);if(null!=n&&e.push(n),e._writableState.length)throw new rc;if(e._transformState.transforming)throw new nc;return e.push(null)}Pa(sc,ic),sc.prototype.push=function(e,t){return this._transformState.needTransform=!1,ic.prototype.push.call(this,e,t)},sc.prototype._transform=function(e,t,n){n(new ec("_transform()"))},sc.prototype._write=function(e,t,n){var r=this._transformState;if(r.writecb=n,r.writechunk=e,r.writeencoding=t,!r.transforming){var i=this._readableState;(r.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},sc.prototype._read=function(e){var t=this._transformState;null===t.writechunk||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))},sc.prototype._destroy=function(e,t){ic.prototype._destroy.call(this,e,(function(e){t(e)}))};var lc,dc=hc,uc=Qa;function hc(e){if(!(this instanceof hc))return new hc(e);uc.call(this,e)}Pa(hc,uc),hc.prototype._transform=function(e,t,n){n(null,e)};var fc=Ea.codes,pc=fc.ERR_MISSING_ARGS,mc=fc.ERR_STREAM_DESTROYED;function gc(e){if(e)throw e}function yc(e){e()}function vc(e,t){return e.pipe(t)}var bc=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=function(e){return e.length?"function"!=typeof e[e.length-1]?gc:e.pop():gc}(t);if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new pc("streams");var o=t.map((function(e,n){var s=n<t.length-1;return function(e,t,n,r){r=function(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}(r);var i=!1;e.on("close",(function(){i=!0})),void 0===lc&&(lc=Ya),lc(e,{readable:t,writable:n},(function(e){if(e)return r(e);i=!0,r()}));var o=!1;return function(t){if(!i&&!o)return o=!0,function(e){return e.setHeader&&"function"==typeof e.abort}(e)?e.abort():"function"==typeof e.destroy?e.destroy():void r(t||new mc("pipe"))}}(e,s,n>0,(function(e){r||(r=e),e&&o.forEach(yc),s||(o.forEach(yc),i(r))}))}));return t.reduce(vc)};!function(e,t){(t=ha.exports=Ja()).Stream=t,t.Readable=t,t.Writable=Na(),t.Duplex=La(),t.Transform=Qa,t.PassThrough=dc,t.finished=Ya,t.pipeline=bc}(0,ha.exports);var Ec=ha.exports;function wc(e={}){const t={},n=new Ec.Duplex({objectMode:!0,read:()=>{},write:function(n,o,s){let a=null;try{!n.id?function(n){e?.retryOnMessage&&n.method===e.retryOnMessage&&Object.values(t).forEach((({req:e,retryCount:n=0})=>{if(!e.id)return;if(n>=3)throw new Error(`StreamMiddleware - Retry limit exceeded for request id "${e.id}"`);const r=t[e.id];r&&(r.retryCount=n+1),i(e)}));r.emit("notification",n)}(n):function(e){const{id:n}=e;if(null===n)return;const r=t[n];if(!r)return void console.warn(`StreamMiddleware - Unknown response id "${n}"`);delete t[n],Object.assign(r.res,e),setTimeout(r.end)}(n)}catch(e){a=e}s(a)}}),r=new js;return{events:r,middleware:(e,n,r,o)=>{t[e.id]={req:e,res:n,next:r,end:o},i(e)},stream:n};function i(e){n.push(e)}}var Sc={},_c={exports:{}},Cc=function e(t,n){if(t&&n)return e(t)(n);if("function"!=typeof t)throw new TypeError("need wrapper function");return Object.keys(t).forEach((function(e){r[e]=t[e]})),r;function r(){for(var e=new Array(arguments.length),n=0;n<e.length;n++)e[n]=arguments[n];var r=t.apply(this,e),i=e[e.length-1];return"function"==typeof r&&r!==i&&Object.keys(i).forEach((function(e){r[e]=i[e]})),r}};var kc=Cc;function Mc(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function Ac(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},n=e.name||"Function wrapped with `once`";return t.onceError=n+" shouldn't be called more than once",t.called=!1,t}_c.exports=kc(Mc),_c.exports.strict=kc(Ac),Mc.proto=Mc((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return Mc(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return Ac(this)},configurable:!0})}));var xc=_c.exports,Ic={};Object.defineProperty(Ic,"__esModule",{value:!0}),Ic.Substream=void 0;const Rc=Ec;class Pc extends Rc.Duplex{constructor({parent:e,name:t}){super({objectMode:!0}),this._parent=e,this._name=t}_read(){}_write(e,t,n){this._parent.push({name:this._name,data:e}),n()}}Ic.Substream=Pc;var Oc=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Sc,"__esModule",{value:!0}),Sc.ObjectMultiplex=void 0;const Tc=Ec,Nc=Oc(xc),Lc=Ic,Dc=Symbol("IGNORE_SUBSTREAM");let Bc=class extends Tc.Duplex{constructor(e={}){super(Object.assign(Object.assign({},e),{objectMode:!0})),this._substreams={}}createStream(e){if(this.destroyed)throw new Error(`ObjectMultiplex - parent stream for name "${e}" already destroyed`);if(this._readableState.ended||this._writableState.ended)throw new Error(`ObjectMultiplex - parent stream for name "${e}" already ended`);if(!e)throw new Error("ObjectMultiplex - name must not be empty");if(this._substreams[e])throw new Error(`ObjectMultiplex - Substream for name "${e}" already exists`);const t=new Lc.Substream({parent:this,name:e});return this._substreams[e]=t,function(e,t){const n=(0,Nc.default)(t);(0,Tc.finished)(e,{readable:!1},n),(0,Tc.finished)(e,{writable:!1},n)}(this,(e=>t.destroy(e||void 0))),t}ignoreStream(e){if(!e)throw new Error("ObjectMultiplex - name must not be empty");if(this._substreams[e])throw new Error(`ObjectMultiplex - Substream for name "${e}" already exists`);this._substreams[e]=Dc}_read(){}_write(e,t,n){const{name:r,data:i}=e;if(!r)return console.warn(`ObjectMultiplex - malformed chunk without name "${e}"`),n();const o=this._substreams[r];return o?(o!==Dc&&o.push(i),n()):(console.warn(`ObjectMultiplex - orphaned data for stream "${r}"`),n())}};Sc.ObjectMultiplex=Bc;var Kc=m(Sc.ObjectMultiplex);const jc=e=>null!==e&&"object"==typeof e&&"function"==typeof e.pipe;jc.writable=e=>jc(e)&&!1!==e.writable&&"function"==typeof e._write&&"object"==typeof e._writableState,jc.readable=e=>jc(e)&&!1!==e.readable&&"function"==typeof e._read&&"object"==typeof e._readableState,jc.duplex=e=>jc.writable(e)&&jc.readable(e),jc.transform=e=>jc.duplex(e)&&"function"==typeof e._transform;var $c,Uc=jc,Hc=class extends ua{constructor(e,{jsonRpcStreamName:t,logger:n=console,maxEventListeners:r=100,rpcMiddleware:i=[]}){if(super({logger:n,maxEventListeners:r,rpcMiddleware:i}),!Uc.duplex(e))throw new Error(ao.errors.invalidDuplexStream());this._handleStreamDisconnect=this._handleStreamDisconnect.bind(this);const o=new Kc;Ec.pipeline(e,o,e,this._handleStreamDisconnect.bind(this,"MetaMask")),this._jsonRpcConnection=wc({retryOnMessage:"METAMASK_EXTENSION_CONNECT_CAN_RETRY"}),Ec.pipeline(this._jsonRpcConnection.stream,o.createStream(t),this._jsonRpcConnection.stream,this._handleStreamDisconnect.bind(this,"MetaMask RpcProvider")),this._rpcEngine.push(this._jsonRpcConnection.middleware),this._jsonRpcConnection.events.on("notification",(t=>{const{method:n,params:r}=t;"metamask_accountsChanged"===n?this._handleAccountsChanged(r):"metamask_unlockStateChanged"===n?this._handleUnlockStateChanged(r):"metamask_chainChanged"===n?this._handleChainChanged(r):Fs.includes(n)?this.emit("message",{type:n,data:r}):"METAMASK_STREAM_FAILURE"===n&&e.destroy(new Error(ao.errors.permanentlyDisconnected()))}))}async _initializeStateAsync(){let e;try{e=await this.request({method:"metamask_getProviderState"})}catch(e){this._log.error("MetaMask: Failed to get initial state. Please report this bug.",e)}this._initializeState(e)}_handleStreamDisconnect(e,t){let n=`MetaMask: Lost connection to "${e}".`;t?.stack&&(n+=`\n${t.stack}`),this._log.warn(n),this.listenerCount("error")>0&&this.emit("error",n),this._handleDisconnect(!1,t?t.message:void 0)}_handleChainChanged({chainId:e,networkVersion:t}={}){Ws(e)&&(e=>Boolean(e)&&"string"==typeof e)(t)?"loading"===t?this._handleDisconnect(!0):super._handleChainChanged({chainId:e}):this._log.error(ao.errors.invalidNetworkParams(),{chainId:e,networkVersion:t})}},Fc=class extends Hc{constructor(e,{jsonRpcStreamName:t="metamask-provider",logger:n=console,maxEventListeners:r=100,shouldSendMetadata:i}={}){if(super(e,{jsonRpcStreamName:t,logger:n,maxEventListeners:r,rpcMiddleware:qs(n)}),this._sentWarnings={chainId:!1,networkVersion:!1,selectedAddress:!1,enable:!1,experimentalMethods:!1,send:!1,events:{close:!1,data:!1,networkChanged:!1,notification:!1}},ea(this,$c,void 0),this._initializeStateAsync(),ta(this,$c,null),this.isMetaMask=!0,this._sendSync=this._sendSync.bind(this),this.enable=this.enable.bind(this),this.send=this.send.bind(this),this.sendAsync=this.sendAsync.bind(this),this._warnOfDeprecation=this._warnOfDeprecation.bind(this),this._metamask=this._getExperimentalApi(),this._jsonRpcConnection.events.on("notification",(e=>{const{method:t}=e;Fs.includes(t)&&(this.emit("data",e),this.emit("notification",e.params.result))})),i)if("complete"===document.readyState)Gs(this._rpcEngine,this._log);else{const e=()=>{Gs(this._rpcEngine,this._log),window.removeEventListener("DOMContentLoaded",e)};window.addEventListener("DOMContentLoaded",e)}}get chainId(){return this._sentWarnings.chainId||(this._log.warn(ao.warnings.chainIdDeprecation),this._sentWarnings.chainId=!0),super.chainId}get networkVersion(){return this._sentWarnings.networkVersion||(this._log.warn(ao.warnings.networkVersionDeprecation),this._sentWarnings.networkVersion=!0),Xs(this,$c)}get selectedAddress(){return this._sentWarnings.selectedAddress||(this._log.warn(ao.warnings.selectedAddressDeprecation),this._sentWarnings.selectedAddress=!0),super.selectedAddress}sendAsync(e,t){this._rpcRequest(e,t)}addListener(e,t){return this._warnOfDeprecation(e),super.addListener(e,t)}on(e,t){return this._warnOfDeprecation(e),super.on(e,t)}once(e,t){return this._warnOfDeprecation(e),super.once(e,t)}prependListener(e,t){return this._warnOfDeprecation(e),super.prependListener(e,t)}prependOnceListener(e,t){return this._warnOfDeprecation(e),super.prependOnceListener(e,t)}_handleDisconnect(e,t){super._handleDisconnect(e,t),Xs(this,$c)&&!e&&ta(this,$c,null)}_warnOfDeprecation(e){!1===this._sentWarnings?.events[e]&&(this._log.warn(ao.warnings.events[e]),this._sentWarnings.events[e]=!0)}async enable(){return this._sentWarnings.enable||(this._log.warn(ao.warnings.enableDeprecation),this._sentWarnings.enable=!0),new Promise(((e,t)=>{try{this._rpcRequest({method:"eth_requestAccounts",params:[]},zs(e,t))}catch(e){t(e)}}))}send(e,t){return this._sentWarnings.send||(this._log.warn(ao.warnings.sendDeprecation),this._sentWarnings.send=!0),"string"!=typeof e||t&&!Array.isArray(t)?e&&"object"==typeof e&&"function"==typeof t?this._rpcRequest(e,t):this._sendSync(e):new Promise(((n,r)=>{try{this._rpcRequest({method:e,params:t},zs(n,r,!1))}catch(e){r(e)}}))}_sendSync(e){let t;switch(e.method){case"eth_accounts":t=this.selectedAddress?[this.selectedAddress]:[];break;case"eth_coinbase":t=this.selectedAddress??null;break;case"eth_uninstallFilter":this._rpcRequest(e,Vs),t=!0;break;case"net_version":t=Xs(this,$c)??null;break;default:throw new Error(ao.errors.unsupportedSync(e.method))}return{id:e.id,jsonrpc:e.jsonrpc,result:t}}_getExperimentalApi(){return new Proxy({isUnlocked:async()=>(this._state.initialized||await new Promise((e=>{this.on("_initialized",(()=>e()))})),this._state.isUnlocked),requestBatch:async e=>{if(!Array.isArray(e))throw ps({message:"Batch requests must be made with an array of request objects.",data:e});return new Promise(((t,n)=>{this._rpcRequest(e,zs(t,n))}))}},{get:(e,t,...n)=>(this._sentWarnings.experimentalMethods||(this._log.warn(ao.warnings.experimentalMethods),this._sentWarnings.experimentalMethods=!0),Reflect.get(e,t,...n))})}_handleChainChanged({chainId:e,networkVersion:t}={}){super._handleChainChanged({chainId:e,networkVersion:t}),this._state.isConnected&&t!==Xs(this,$c)&&(ta(this,$c,t),this._state.initialized&&this.emit("networkChanged",Xs(this,$c)))}};$c=new WeakMap;const qc=t("MM_SDK");qc.color="#FFAC1C";var zc={},Wc={};Object.defineProperty(Wc,"__esModule",{value:!0}),Wc.EthereumProviderError=Wc.EthereumRpcError=void 0;const Vc=Qo;class Gc extends Error{constructor(e,t,n){if(!Number.isInteger(e))throw new Error('"code" must be an integer.');if(!t||"string"!=typeof t)throw new Error('"message" must be a nonempty string.');super(t),this.code=e,void 0!==n&&(this.data=n)}serialize(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),this.stack&&(e.stack=this.stack),e}toString(){return Vc.default(this.serialize(),Yc,2)}}Wc.EthereumRpcError=Gc;function Yc(e,t){if("[Circular]"!==t)return t}Wc.EthereumProviderError=class extends Gc{constructor(e,t,n){if(!function(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}(e))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(e,t,n)}};var Zc={},Jc={};Object.defineProperty(Jc,"__esModule",{value:!0}),Jc.errorValues=Jc.errorCodes=void 0,Jc.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},Jc.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}},function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.serializeError=e.isValidCode=e.getMessageFromCode=e.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const t=Jc,n=Wc,r=t.errorCodes.rpc.internal,i="Unspecified error message. This is a bug, please report it.",o={code:r,message:s(r)};function s(n,r=i){if(Number.isInteger(n)){const r=n.toString();if(d(t.errorValues,r))return t.errorValues[r].message;if(c(n))return e.JSON_RPC_SERVER_ERROR_MESSAGE}return r}function a(e){if(!Number.isInteger(e))return!1;const n=e.toString();return!!t.errorValues[n]||!!c(e)}function c(e){return e>=-32099&&e<=-32e3}function l(e){return e&&"object"==typeof e&&!Array.isArray(e)?Object.assign({},e):e}function d(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.",e.getMessageFromCode=s,e.isValidCode=a,e.serializeError=function(e,{fallbackError:t=o,shouldIncludeStack:r=!1}={}){var i,c;if(!t||!Number.isInteger(t.code)||"string"!=typeof t.message)throw new Error("Must provide fallback error with integer number code and string message.");if(e instanceof n.EthereumRpcError)return e.serialize();const u={};if(e&&"object"==typeof e&&!Array.isArray(e)&&d(e,"code")&&a(e.code)){const t=e;u.code=t.code,t.message&&"string"==typeof t.message?(u.message=t.message,d(t,"data")&&(u.data=t.data)):(u.message=s(u.code),u.data={originalError:l(e)})}else{u.code=t.code;const n=null===(i=e)||void 0===i?void 0:i.message;u.message=n&&"string"==typeof n?n:t.message,u.data={originalError:l(e)}}const h=null===(c=e)||void 0===c?void 0:c.stack;return r&&e&&h&&"string"==typeof h&&(u.stack=h),u}}(Zc);var Qc={};Object.defineProperty(Qc,"__esModule",{value:!0}),Qc.ethErrors=void 0;const Xc=Wc,el=Zc,tl=Jc;function nl(e,t){const[n,r]=il(t);return new Xc.EthereumRpcError(e,n||el.getMessageFromCode(e),r)}function rl(e,t){const[n,r]=il(t);return new Xc.EthereumProviderError(e,n||el.getMessageFromCode(e),r)}function il(e){if(e){if("string"==typeof e)return[e];if("object"==typeof e&&!Array.isArray(e)){const{message:t,data:n}=e;if(t&&"string"!=typeof t)throw new Error("Must specify string message.");return[t||void 0,n]}}return[]}Qc.ethErrors={rpc:{parse:e=>nl(tl.errorCodes.rpc.parse,e),invalidRequest:e=>nl(tl.errorCodes.rpc.invalidRequest,e),invalidParams:e=>nl(tl.errorCodes.rpc.invalidParams,e),methodNotFound:e=>nl(tl.errorCodes.rpc.methodNotFound,e),internal:e=>nl(tl.errorCodes.rpc.internal,e),server:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return nl(t,e)},invalidInput:e=>nl(tl.errorCodes.rpc.invalidInput,e),resourceNotFound:e=>nl(tl.errorCodes.rpc.resourceNotFound,e),resourceUnavailable:e=>nl(tl.errorCodes.rpc.resourceUnavailable,e),transactionRejected:e=>nl(tl.errorCodes.rpc.transactionRejected,e),methodNotSupported:e=>nl(tl.errorCodes.rpc.methodNotSupported,e),limitExceeded:e=>nl(tl.errorCodes.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>rl(tl.errorCodes.provider.userRejectedRequest,e),unauthorized:e=>rl(tl.errorCodes.provider.unauthorized,e),unsupportedMethod:e=>rl(tl.errorCodes.provider.unsupportedMethod,e),disconnected:e=>rl(tl.errorCodes.provider.disconnected,e),chainDisconnected:e=>rl(tl.errorCodes.provider.chainDisconnected,e),custom:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum Provider custom errors must provide single object argument.");const{code:t,message:n,data:r}=e;if(!n||"string"!=typeof n)throw new Error('"message" must be a nonempty string');return new Xc.EthereumProviderError(t,n,r)}}},function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getMessageFromCode=e.serializeError=e.EthereumProviderError=e.EthereumRpcError=e.ethErrors=e.errorCodes=void 0;const t=Wc;Object.defineProperty(e,"EthereumRpcError",{enumerable:!0,get:function(){return t.EthereumRpcError}}),Object.defineProperty(e,"EthereumProviderError",{enumerable:!0,get:function(){return t.EthereumProviderError}});const n=Zc;Object.defineProperty(e,"serializeError",{enumerable:!0,get:function(){return n.serializeError}}),Object.defineProperty(e,"getMessageFromCode",{enumerable:!0,get:function(){return n.getMessageFromCode}});const r=Qc;Object.defineProperty(e,"ethErrors",{enumerable:!0,get:function(){return r.ethErrors}});const i=Jc;Object.defineProperty(e,"errorCodes",{enumerable:!0,get:function(){return i.errorCodes}})}(zc);var ol={exports:{}};ol.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=90)}({17:function(e,t,n){t.__esModule=!0,t.default=void 0;var r=n(18),i=function(){function e(){}return e.getFirstMatch=function(e,t){var n=t.match(e);return n&&n.length>0&&n[1]||""},e.getSecondMatch=function(e,t){var n=t.match(e);return n&&n.length>1&&n[2]||""},e.matchAndReturnConst=function(e,t,n){if(e.test(t))return n},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,n,r){void 0===r&&(r=!1);var i=e.getVersionPrecision(t),o=e.getVersionPrecision(n),s=Math.max(i,o),a=0,c=e.map([t,n],(function(t){var n=s-e.getVersionPrecision(t),r=t+new Array(n+1).join(".0");return e.map(r.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(r&&(a=s-Math.min(i,o)),s-=1;s>=a;){if(c[0][s]>c[1][s])return 1;if(c[0][s]===c[1][s]){if(s===a)return 0;s-=1}else if(c[0][s]<c[1][s])return-1}},e.map=function(e,t){var n,r=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(n=0;n<e.length;n+=1)r.push(t(e[n]));return r},e.find=function(e,t){var n,r;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(n=0,r=e.length;n<r;n+=1){var i=e[n];if(t(i,n))return i}},e.assign=function(e){for(var t,n,r=e,i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];if(Object.assign)return Object.assign.apply(Object,[e].concat(o));var a=function(){var e=o[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){r[t]=e[t]}))};for(t=0,n=o.length;t<n;t+=1)a();return e},e.getBrowserAlias=function(e){return r.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return r.BROWSER_MAP[e]||""},e}();t.default=i,e.exports=t.default},18:function(e,t,n){t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,n){t.__esModule=!0,t.default=void 0;var r,i=(r=n(91))&&r.__esModule?r:{default:r},o=n(18);function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var a=function(){function e(){}var t,n,r;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new i.default(e,t)},e.parse=function(e){return new i.default(e).getResult()},t=e,r=[{key:"BROWSER_MAP",get:function(){return o.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return o.ENGINE_MAP}},{key:"OS_MAP",get:function(){return o.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return o.PLATFORMS_MAP}}],(n=null)&&s(t.prototype,n),r&&s(t,r),e}();t.default=a,e.exports=t.default},91:function(e,t,n){t.__esModule=!0,t.default=void 0;var r=c(n(92)),i=c(n(93)),o=c(n(94)),s=c(n(95)),a=c(n(17));function c(e){return e&&e.__esModule?e:{default:e}}var l=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=a.default.find(r.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=a.default.find(i.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=a.default.find(o.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=a.default.find(s.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return a.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,n={},r=0,i={},o=0;if(Object.keys(e).forEach((function(t){var s=e[t];"string"==typeof s?(i[t]=s,o+=1):"object"==typeof s&&(n[t]=s,r+=1)})),r>0){var s=Object.keys(n),c=a.default.find(s,(function(e){return t.isOS(e)}));if(c){var l=this.satisfies(n[c]);if(void 0!==l)return l}var d=a.default.find(s,(function(e){return t.isPlatform(e)}));if(d){var u=this.satisfies(n[d]);if(void 0!==u)return u}}if(o>0){var h=Object.keys(i),f=a.default.find(h,(function(e){return t.isBrowser(e,!0)}));if(void 0!==f)return this.compareVersion(i[f])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var n=this.getBrowserName().toLowerCase(),r=e.toLowerCase(),i=a.default.getBrowserTypeByAlias(r);return t&&i&&(r=i.toLowerCase()),r===n},t.compareVersion=function(e){var t=[0],n=e,r=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(n=e.substr(1),"="===e[1]?(r=!0,n=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?n=e.substr(1):"~"===e[0]&&(r=!0,n=e.substr(1)),t.indexOf(a.default.compareVersions(i,n,r))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=l,e.exports=t.default},92:function(e,t,n){t.__esModule=!0,t.default=void 0;var r,i=(r=n(17))&&r.__esModule?r:{default:r},o=/version\/(\d+(\.?_?\d+)+)/i,s=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},n=i.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},n=i.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},n=i.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},n=i.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},n=i.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},n=i.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},n=i.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},n=i.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},n=i.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},n=i.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},n=i.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},n=i.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},n=i.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},n=i.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},n=i.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return n&&(t.version=n),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},n=i.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},n=i.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},n=i.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},n=i.default.getFirstMatch(o,e)||i.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},n=i.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},n=i.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},n=i.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},n=i.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},n=i.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},n=i.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},n=i.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},n=i.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},n=i.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:function(e){var t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe:function(e){var t={name:"Android Browser"},n=i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},n=i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},n=i.default.getFirstMatch(o,e);return n&&(t.version=n),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:i.default.getFirstMatch(t,e),version:i.default.getSecondMatch(t,e)}}}];t.default=s,e.exports=t.default},93:function(e,t,n){t.__esModule=!0,t.default=void 0;var r,i=(r=n(17))&&r.__esModule?r:{default:r},o=n(18),s=[{test:[/Roku\/DVP/],describe:function(e){var t=i.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:o.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=i.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=i.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),n=i.default.getWindowsVersionName(t);return{name:o.OS_MAP.Windows,version:t,versionName:n}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:o.OS_MAP.iOS},n=i.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return n&&(t.version=n),t}},{test:[/macintosh/i],describe:function(e){var t=i.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),n=i.default.getMacOSVersionName(t),r={name:o.OS_MAP.MacOS,version:t};return n&&(r.versionName=n),r}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=i.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:o.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe:function(e){var t=i.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),n=i.default.getAndroidVersionName(t),r={name:o.OS_MAP.Android,version:t};return n&&(r.versionName=n),r}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=i.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),n={name:o.OS_MAP.WebOS};return t&&t.length&&(n.version=t),n}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=i.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||i.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||i.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:o.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=i.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=i.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:o.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:o.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=i.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.PlayStation4,version:t}}}];t.default=s,e.exports=t.default},94:function(e,t,n){t.__esModule=!0,t.default=void 0;var r,i=(r=n(17))&&r.__esModule?r:{default:r},o=n(18),s=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=i.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",n={type:o.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(n.model=t),n}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),n=e.test(/like (ipod|iphone)/i);return t&&!n},describe:function(e){var t=i.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:o.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}}];t.default=s,e.exports=t.default},95:function(e,t,n){t.__esModule=!0,t.default=void 0;var r,i=(r=n(17))&&r.__esModule?r:{default:r},o=n(18),s=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:o.ENGINE_MAP.Blink};var t=i.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:o.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:o.ENGINE_MAP.Trident},n=i.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:o.ENGINE_MAP.Presto},n=i.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:function(e){var t=e.test(/gecko/i),n=e.test(/like gecko/i);return t&&!n},describe:function(e){var t={name:o.ENGINE_MAP.Gecko},n=i.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:o.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:o.ENGINE_MAP.WebKit},n=i.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}}];t.default=s,e.exports=t.default}});var sl=ol.exports,al=m(sl);const cl={Initialized:"initialized",DisplayURI:"display_uri",ProviderUpdate:"provider_update",ConnectWithResponse:"connectWithResponse",ConnectionStatus:"connection_status",ServiceStatus:"service_status"};class ll{constructor({shouldSetOnWindow:e,connectionStream:t,shouldSendMetadata:n=!1,shouldShimWeb3:r,sdkInstance:i}){const o=new hl({connectionStream:t,shouldSendMetadata:n,shouldSetOnWindow:e,shouldShimWeb3:r,autoRequestAccounts:!1}),s=new Proxy(o,{deleteProperty:()=>!0});if(this.provider=s,this.sdkInstance=i,e&&"undefined"!=typeof window)try{a=o,window.ethereum=a,window.dispatchEvent(new Event("ethereum#initialized"))}catch(e){qc("[Ethereum] Unable to set global provider - window.ethereum may be read-only",e)}var a;if(r&&"undefined"!=typeof window)try{!function(e,t=console){let n=!1,r=!1;if(!window.web3){const i="__isMetaMaskShim__";let o={currentProvider:e};Object.defineProperty(o,i,{value:!0,enumerable:!0,configurable:!1,writable:!1}),o=new Proxy(o,{get:(o,s,...a)=>("currentProvider"!==s||n?"currentProvider"===s||s===i||r||(r=!0,t.error("MetaMask no longer injects web3. For details, see: https://docs.metamask.io/guide/provider-migration.html#replacing-window-web3"),e.request({method:"metamask_logWeb3ShimUsage"}).catch((e=>{t.debug("MetaMask: Failed to log web3 shim usage.",e)}))):(n=!0,t.warn("You are accessing the MetaMask window.web3.currentProvider shim. This property is deprecated; use window.ethereum instead. For details, see: https://docs.metamask.io/guide/provider-migration.html#replacing-window-web3")),Reflect.get(o,s,...a)),set:(...e)=>(t.warn("You are accessing the MetaMask window.web3 shim. This object is deprecated; use window.ethereum instead. For details, see: https://docs.metamask.io/guide/provider-migration.html#replacing-window-web3"),Reflect.set(...e))}),Object.defineProperty(window,"web3",{value:o,enumerable:!1,configurable:!0,writable:!0})}}(this.provider)}catch(e){qc("[Ethereum] Unable to shim web3 - window.web3 may be read-only",e)}this.provider.on("display_uri",(e=>{this.sdkInstance.emit(cl.DisplayURI,e)})),this.provider.on("_initialized",(()=>{const e={chainId:this.provider.getChainId(),isConnected:this.provider.isConnected(),isMetaMask:this.provider.isMetaMask,selectedAddress:this.provider.getSelectedAddress(),networkVersion:this.provider.getNetworkVersion()};this.sdkInstance.emit(cl.Initialized,e),qc("[Ethereum: constructor()] provider initialized",e)}))}static init(e){var t;return qc("[Ethereum: init()] Initializing Ethereum service"),this.instance=new ll(e),null===(t=this.instance)||void 0===t?void 0:t.provider}static destroy(){}static getInstance(){var e;if(!(null===(e=this.instance)||void 0===e?void 0:e.provider))throw new Error("Ethereum instance not intiialized - call Ethereum.factory first.");return this.instance}static getProvider(){var e;if(!(null===(e=this.instance)||void 0===e?void 0:e.provider))throw new Error("Ethereum instance not intiialized - call Ethereum.factory first.");return this.instance.provider}}class dl{constructor({useDeepLink:e,preferredOpenLink:t,debug:n=!1}){this.state={platformType:void 0,useDeeplink:!1,preferredOpenLink:void 0,debug:!1},this.state.platformType=this.getPlatformType(),this.state.useDeeplink=e,this.state.preferredOpenLink=t,this.state.debug=n}openDeeplink(e,t,n){return function(e,t,n,r){const{state:i}=e;qc(`[PlatfformManager: openDeeplink()] universalLink --\x3e ${t}`),qc(`[PlatfformManager: openDeeplink()] deepLink --\x3e ${n}`);try{if(i.preferredOpenLink)return void i.preferredOpenLink(i.useDeeplink?n:t,r);if(qc(`[PlatfformManager: openDeeplink()] open link now useDeepLink=${i.useDeeplink} link=${i.useDeeplink?n:t}`),i.useDeeplink)"undefined"!=typeof window&&(window.location.href=n);else if("undefined"!=typeof document){const e=document.createElement("a");e.href=t,e.target="_self",e.rel="noreferrer noopener",e.click()}}catch(e){console.log("[PlatfformManager: openDeeplink()] can't open link",e)}}(this,e,t,n)}isReactNative(){var e;return this.isNotBrowser()&&"undefined"!=typeof window&&(null===window||void 0===window?void 0:window.navigator)&&"ReactNative"===(null===(e=window.navigator)||void 0===e?void 0:e.product)}isMetaMaskInstalled(){return function(){const e=ll.getProvider()||(null===window||void 0===window?void 0:window.ethereum);return qc(`[PlatfformManager: isMetaMaskInstalled()] isMetaMask=${null==e?void 0:e.isMetaMask} isConnected=${null==e?void 0:e.isConnected()}`),(null==e?void 0:e.isMetaMask)&&(null==e?void 0:e.isConnected())}()}isDesktopWeb(){return this.isBrowser()&&!this.isMobileWeb()}isMobile(){var e,t;const n=al.parse(window.navigator.userAgent);return"mobile"===(null===(e=null==n?void 0:n.platform)||void 0===e?void 0:e.type)||"tablet"===(null===(t=null==n?void 0:n.platform)||void 0===t?void 0:t.type)}isSecure(){return this.isReactNative()||this.isMobileWeb()}isMetaMaskMobileWebView(){return"undefined"!=typeof window&&(Boolean(window.ReactNativeWebView)&&Boolean(navigator.userAgent.endsWith("MetaMaskMobile")))}isMobileWeb(){return this.state.platformType===qi.MobileWeb}static isNotBrowser(){var e;return"undefined"==typeof window||!(null===window||void 0===window?void 0:window.navigator)||"undefined"!=typeof global&&"ReactNative"===(null===(e=null===global||void 0===global?void 0:global.navigator)||void 0===e?void 0:e.product)||"ReactNative"===(null===navigator||void 0===navigator?void 0:navigator.product)}isNotBrowser(){return dl.isNotBrowser()}static isBrowser(){return!this.isNotBrowser()}isBrowser(){return dl.isBrowser()}isNodeJS(){return this.isNotBrowser()&&!this.isReactNative()}isUseDeepLink(){return this.state.useDeeplink}getPlatformType(){return function(e){const{state:t}=e;return t.platformType?t.platformType:e.isReactNative()?qi.ReactNative:e.isNotBrowser()?qi.NonBrowser:e.isMetaMaskMobileWebView()?qi.MetaMaskMobileWebview:e.isMobile()?qi.MobileWeb:qi.DesktopWeb}(this)}}const ul=e=>f(void 0,void 0,void 0,(function*(){if(dl.isBrowser()){const{StorageManagerWeb:t}=yield Promise.resolve().then((function(){return Cd}));return new t(e)}const t={persistChannelConfig:()=>f(void 0,void 0,void 0,(function*(){})),getPersistedChannelConfig:()=>f(void 0,void 0,void 0,(function*(){})),persistAccounts:()=>f(void 0,void 0,void 0,(function*(){})),getCachedAccounts:()=>f(void 0,void 0,void 0,(function*(){return[]})),persistChainId:()=>f(void 0,void 0,void 0,(function*(){})),getCachedChainId:()=>f(void 0,void 0,void 0,(function*(){})),terminate:()=>f(void 0,void 0,void 0,(function*(){}))};return Promise.resolve(t)}));class hl extends Fc{constructor({connectionStream:e,shouldSendMetadata:t,autoRequestAccounts:n=!1}){super(e,{logger:console,maxEventListeners:100,shouldSendMetadata:t}),this.state={accounts:null,autoRequestAccounts:!1,providerStateRequested:!1,chainId:"",networkVersion:""},qc(`[SDKProvider: constructor()] autoRequestAccounts=${n}`),this.state.autoRequestAccounts=n}forceInitializeState(){return f(this,void 0,void 0,(function*(){return qc(`[SDKProvider: forceInitializeState()] autoRequestAccounts=${this.state.autoRequestAccounts}`),this._initializeStateAsync()}))}_setConnected(){qc("[SDKProvider: _setConnected()] Setting connected state"),this._state.isConnected=!0}getState(){return this._state}getSDKProviderState(){return this.state}getSelectedAddress(){var e;const{accounts:t}=this._state;return t&&0!==t.length?(null===(e=t[0])||void 0===e?void 0:e.toLowerCase())||"":(qc("[SDKProvider: getSelectedAddress] No accounts found"),null)}getChainId(){return this.state.chainId}getNetworkVersion(){return this.state.networkVersion}setSDKProviderState(e){this.state=Object.assign(Object.assign({},this.state),e)}handleAccountsChanged(e,t){return this._handleAccountsChanged(e,t)}handleDisconnect({terminate:e=!1}){!function({terminate:e=!1,instance:t}){const{state:n}=t;qc(`[SDKProvider: handleDisconnect()] cleaning up provider state terminate=${e}`,t),e&&(t._state.accounts=null,t._state.isUnlocked=!1,t._state.isPermanentlyDisconnected=!0,t._state.initialized=!1),t._handleAccountsChanged([]),t._state.isConnected=!1,t.emit("disconnect",zc.ethErrors.provider.disconnected()),n.providerStateRequested=!1}({terminate:e,instance:this})}_initializeStateAsync(){return f(this,void 0,void 0,(function*(){return function(e){var t,n;return f(this,void 0,void 0,(function*(){void 0===e.state&&(e.state={accounts:null,autoRequestAccounts:!1,providerStateRequested:!1,chainId:""});const{state:r}=e;let i;if(r.providerStateRequested)qc("[SDKProvider: initializeStateAsync()] initialization already in progress");else{let o;r.providerStateRequested=!0;let s=null,a=!1,c=!1;const l=yield ul({enabled:!0});if(l){const e=yield l.getPersistedChannelConfig({});a=null!==(t=null==e?void 0:e.relayPersistence)&&void 0!==t&&t,o=yield l.getCachedChainId();const n=yield l.getCachedAccounts();n.length>0&&(s=n[0])}if(qc(`[SDKProvider: initializeStateAsync()] relayPersistence=${a}`,{relayPersistence:a,cachedChainId:o,cachedSelectedAddress:s}),a)if(o&&s)i={accounts:[s],chainId:o,isUnlocked:!1},c=!0;else try{i=yield e.request({method:"metamask_getProviderState"})}catch(t){return e._log.error("MetaMask: Failed to get initial state. Please report this bug.",t),void(r.providerStateRequested=!1)}if(0===(null===(n=null==i?void 0:i.accounts)||void 0===n?void 0:n.length))if(e.getSelectedAddress())i.accounts=[e.getSelectedAddress()];else{qc("[SDKProvider: initializeStateAsync()] Fetch accounts remotely.");const t=yield e.request({method:"eth_requestAccounts",params:[]});i.accounts=t}e._initializeState(i),r.providerStateRequested=!1,c&&(e._state.isConnected=!0,e.emit("connect",{chainId:null==i?void 0:i.chainId}))}}))}(this)}))}_initializeState(e){return qc("[SDKProvider: _initializeState()]",e),function(e,t,n){return qc("[SDKProvider: initializeState()] set state._initialized to false"),e._state.initialized=!1,t(n)}(this,super._initializeState.bind(this),e)}_handleChainChanged({chainId:e,networkVersion:t}={}){this.state.chainId=e,this.state.networkVersion=t,function({instance:e,chainId:t,networkVersion:n,superHandleChainChanged:r}){qc(`[SDKProvider: handleChainChanged()] chainId=${t} networkVersion=${n}`);let i=n;n||(qc("[SDKProvider: handleChainChanged()] forced network version to prevent provider error"),i="1"),e._state.isConnected=!0,e.emit("connect",{chainId:t}),r({chainId:t,networkVersion:i})}({instance:this,chainId:e,networkVersion:t,superHandleChainChanged:super._handleChainChanged.bind(this)})}}var fl,pl={name:"@metamask/sdk",version:"0.32.0",description:"",homepage:"https://github.com/MetaMask/metamask-sdk#readme",bugs:{url:"https://github.com/MetaMask/metamask-sdk/issues"},repository:{type:"git",url:"https://github.com/MetaMask/metamask-sdk",directory:"packages/sdk"},main:"dist/node/cjs/metamask-sdk.js",module:"dist/browser/es/metamask-sdk.js",browser:"dist/browser/es/metamask-sdk.js",unpkg:"dist/browser/umd/metamask-sdk.js","react-native":"dist/react-native/es/metamask-sdk.js",types:"dist/types/src/index.d.ts",sideEffects:!1,files:["/dist"],scripts:{"build:types":"tsc --project tsconfig.build.json --emitDeclarationOnly --outDir dist/types",build:"yarn build:types && rollup -c --bundleConfigAsCjs","build:clean":"yarn clean && yarn build","build:post-tsc":"echo 'N/A'","build:pre-tsc":"echo 'N/A'",typecheck:"tsc --noEmit",clean:"rimraf ./dist",size:"node bundle-size && size-limit",lint:"yarn lint:eslint && yarn lint:misc --check","lint:changelog":"../../scripts/validate-changelog.sh @metamask/sdk","lint:eslint":"eslint . --cache --ext js,ts","lint:fix":"yarn lint:eslint --fix && yarn lint:misc --write","lint:misc":"prettier '**/*.json' '**/*.md' '!CHANGELOG.md' --ignore-path ../../.gitignore",prepack:"../../scripts/prepack.sh","publish:preview":"yarn npm publish --tag preview",reset:"yarn clean && rimraf ./node_modules/",test:'jest --testPathIgnorePatterns "/e2e/"',"test:coverage":'jest --coverage --testPathIgnorePatterns "/e2e/"',"test:e2e":'jest --testPathPattern "/e2e/"',"test:ci":'jest --coverage --passWithNoTests --setupFilesAfterEnv ./jest-preload.js --testPathIgnorePatterns "/e2e/"',"test:dev":'jest -c ./jest.config.ts --detectOpenHandles  --testPathIgnorePatterns "/e2e/"',watch:"rollup -c -w",dev:'concurrently "tsc --watch" "rollup -c -w --bundleConfigAsCjs"',"build:dev":"yarn build:types && NODE_ENV=dev rollup -c --bundleConfigAsCjs"},dependencies:{"@babel/runtime":"^7.26.0","@metamask/onboarding":"^1.0.1","@metamask/providers":"16.1.0","@metamask/sdk-communication-layer":"workspace:*","@metamask/sdk-install-modal-web":"workspace:*","@paulmillr/qr":"^0.2.1",bowser:"^2.9.0","cross-fetch":"^4.0.0",debug:"^4.3.4",eciesjs:"^0.4.11","eth-rpc-errors":"^4.0.3",eventemitter2:"^6.4.9","obj-multiplex":"^1.0.0",pump:"^3.0.0","readable-stream":"^3.6.2","socket.io-client":"^4.5.1",tslib:"^2.6.0",util:"^0.12.4",uuid:"^8.3.2"},devDependencies:{"@jest/globals":"^29.3.1","@lavamoat/allow-scripts":"^2.3.1","@metamask/auto-changelog":"3.1.0","@metamask/eslint-config":"^6.0.0","@metamask/eslint-config-nodejs":"^6.0.0","@metamask/eslint-config-typescript":"^6.0.0","@react-native-async-storage/async-storage":"^1.19.6","@rollup/plugin-alias":"^5.1.1","@rollup/plugin-commonjs":"^25.0.7","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.0.2","@rollup/plugin-replace":"^6.0.1","@rollup/plugin-terser":"^0.4.1","@size-limit/preset-big-lib":"^11.0.2","@types/dom-screen-wake-lock":"^1.0.2","@types/node":"^20.1.3","@types/pump":"^1.1.1","@types/qrcode-terminal":"^0.12.0","@types/uuid":"^10.0.0","@typescript-eslint/eslint-plugin":"^4.26.0","@typescript-eslint/parser":"^4.26.0","browserify-zlib":"^0.2.0",buffer:"^6.0.3",concurrently:"^9.1.2","crypto-browserify":"^3.12.0",eslint:"^7.30.0","eslint-config-prettier":"^8.3.0","eslint-plugin-import":"^2.23.4","eslint-plugin-jest":"^24.4.0","eslint-plugin-jsdoc":"^36.1.0","eslint-plugin-node":"^11.1.0","eslint-plugin-prettier":"^3.4.0","https-browserify":"^1.0.0",jest:"^29.3.1","jest-environment-jsdom":"^29.3.1",prettier:"^2.3.0",process:"^0.11.10",rimraf:"^4.4.0",rollup:"^4.26.0","rollup-plugin-analyzer":"^4.0.0","rollup-plugin-jscc":"^2.0.0","rollup-plugin-natives":"^0.7.5","rollup-plugin-node-builtins":"^2.1.2","rollup-plugin-polyfill-node":"^0.13.0","rollup-plugin-sizes":"^1.0.6","rollup-plugin-typescript2":"^0.31.2","rollup-plugin-visualizer":"^5.12.0","size-limit":"^11.0.2","stream-browserify":"^3.0.0","stream-http":"^3.2.0","ts-jest":"^29.0.3","ts-node":"^10.9.1",typescript:"^4.3.2",url:"^0.11.0",webpack:"^5.0.0"},publishConfig:{access:"public",registry:"https://registry.npmjs.org/"},lavamoat:{allowScripts:{"eciesjs>secp256k1":!1,"socket.io-client>engine.io-client>ws>bufferutil":!1,"socket.io-client>engine.io-client>ws>utf-8-validate":!1,"@metamask/sdk-communication-layer>bufferutil":!1,"@metamask/sdk-communication-layer>eciesjs>secp256k1":!1,"@metamask/sdk-communication-layer>utf-8-validate":!1}}};!function(e){e.INPAGE="metamask-inpage",e.CONTENT_SCRIPT="metamask-contentscript",e.PROVIDER="metamask-provider"}(fl||(fl={}));const ml="direct",gl="https://metamask.app.link/connect",yl="metamask://connect",vl={NAME:"MetaMask",RDNS:["io.metamask","io.metamask.flask"]},bl=/(?:^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}$)|(?:^0{8}-0{4}-0{4}-0{4}-0{12}$)/u,El={METAMASK_GETPROVIDERSTATE:"metamask_getProviderState",METAMASK_CONNECTSIGN:"metamask_connectSign",METAMASK_CONNECTWITH:"metamask_connectWith",METAMASK_OPEN:"metamask_open",METAMASK_BATCH:"metamask_batch",PERSONAL_SIGN:"personal_sign",WALLET_REQUESTPERMISSIONS:"wallet_requestPermissions",WALLET_REVOKEPERMISSIONS:"wallet_revokePermissions",WALLET_GETPERMISSIONS:"wallet_getPermissions",WALLET_WATCHASSET:"wallet_watchAsset",WALLET_ADDETHEREUMCHAIN:"wallet_addEthereumChain",WALLET_SWITCHETHETHEREUMCHAIN:"wallet_switchEthereumChain",ETH_REQUESTACCOUNTS:"eth_requestAccounts",ETH_ACCOUNTS:"eth_accounts",ETH_CHAINID:"eth_chainId",ETH_SENDTRANSACTION:"eth_sendTransaction",ETH_SIGNTYPEDDATA:"eth_signTypedData",ETH_SIGNTYPEDDATA_V3:"eth_signTypedData_v3",ETH_SIGNTYPEDDATA_V4:"eth_signTypedData_v4",ETH_SIGNTRANSACTION:"eth_signTransaction",ETH_SIGN:"eth_sign",PERSONAL_EC_RECOVER:"personal_ecRecover"},wl={[El.ETH_REQUESTACCOUNTS]:!0,[El.ETH_SENDTRANSACTION]:!0,[El.ETH_SIGNTRANSACTION]:!0,[El.ETH_SIGN]:!0,[El.PERSONAL_SIGN]:!0,[El.ETH_ACCOUNTS]:!1,[El.ETH_CHAINID]:!1,[El.PERSONAL_SIGN]:!0,[El.ETH_SIGNTYPEDDATA]:!0,[El.ETH_SIGNTYPEDDATA_V3]:!0,[El.ETH_SIGNTYPEDDATA_V4]:!0,[El.WALLET_REQUESTPERMISSIONS]:!0,[El.WALLET_GETPERMISSIONS]:!0,[El.WALLET_WATCHASSET]:!0,[El.WALLET_ADDETHEREUMCHAIN]:!0,[El.WALLET_SWITCHETHETHEREUMCHAIN]:!0,[El.METAMASK_CONNECTSIGN]:!0,[El.METAMASK_CONNECTWITH]:!0,[El.PERSONAL_EC_RECOVER]:!0,[El.METAMASK_BATCH]:!0,[El.METAMASK_OPEN]:!0},Sl=Object.keys(wl).map((e=>e.toLowerCase())),_l=["eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4","eth_sign"].map((e=>e.toLowerCase())),Cl=".sdk-comm",kl="providerType",Ml=".MMSDK_cached_address",Al=".MMSDK_cached_chainId",xl={CHAIN_CHANGED:"chainChanged",ACCOUNTS_CHANGED:"accountsChanged",DISCONNECT:"disconnect",CONNECT:"connect",CONNECTED:"connected"},Il=1e6;var Rl;!function(e){e.TERMINATE="terminate",e.EXTENSION="extension",e.INITIALIZED="initialized"}(Rl||(Rl={}));const Pl="undefined"!=typeof window&&window.localStorage;function Ol({instance:e,msg:t}){return f(this,void 0,void 0,(function*(){if(e._initialized||(qc("[MetaMaskSDK: connectAndSign()] provider not ready -- wait for init()"),yield e.init()),qc(`[MetaMaskSDK: connectAndSign()] activeProvider=${e.activeProvider}`),!e.activeProvider)throw new Error("SDK state invalid -- undefined provider");const n=/^0x([0-9A-Fa-f]{2})*$/u.test(t)?t:function(e){let t;if("undefined"!=typeof Buffer)t=Buffer.from(e,"utf8").toString("hex");else if("undefined"!=typeof TextEncoder){const n=(new TextEncoder).encode(e);t=Array.from(n).map((e=>e.toString(16).padStart(2,"0"))).join("")}else{if("object"!=typeof global||!("Buffer"in global))throw new Error("Unable to convert string to hex: No available method.");t=global.Buffer.from(e,"utf8").toString("hex")}return`0x${t}`}(t);return e.activeProvider.request({method:El.METAMASK_CONNECTWITH,params:[{method:El.PERSONAL_SIGN,params:[n]}]})}))}function Tl(e){var t,n;return f(this,void 0,void 0,(function*(){qc("[MetaMaskSDK: connectWithExtensionProvider()] ",e),e.sdkProvider=e.activeProvider,e.activeProvider=window.extension,window.ethereum=window.extension;try{const e=yield null===(t=window.extension)||void 0===t?void 0:t.request({method:"eth_requestAccounts"});qc(`[MetaMaskSDK: connectWithExtensionProvider()] accounts=${e}`)}catch(e){return void console.warn("[MetaMaskSDK: connectWithExtensionProvider()] can't request accounts error",e)}localStorage.setItem(kl,"extension"),e.extensionActive=!0,e.emit(cl.ProviderUpdate,Rl.EXTENSION),e.options.enableAnalytics&&(null===(n=e.analytics)||void 0===n||n.send({event:Ii.SDK_USE_EXTENSION}))}))}function Nl(e){let t;if("undefined"!=typeof Buffer)t=Buffer.from(e,"utf8").toString("base64");else if("function"==typeof btoa)t=btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/gu,((e,t)=>String.fromCharCode(parseInt(t,16)))));else{if("object"!=typeof global||!("Buffer"in global))throw new Error("Unable to base64 encode: No available method.");t=global.Buffer.from(e,"utf8").toString("base64")}return t}function Ll(e,t,n,i){var o,s,a,c,l,d,u,h,p,m,g,y,v,b,E,w,S,_,C,k;return f(this,void 0,void 0,(function*(){const n=null===(o=e.state.remote)||void 0===o?void 0:o.isReady(),f=null===(s=e.state.remote)||void 0===s?void 0:s.isConnected(),M=null===(a=e.state.remote)||void 0===a?void 0:a.isPaused(),A=ll.getProvider(),x=null===(c=e.state.remote)||void 0===c?void 0:c.getChannelId(),I=null===(l=e.state.remote)||void 0===l?void 0:l.isAuthorized(),{deeplinkProtocol:R}=e.state,{method:P,data:O,triggeredInstaller:T}=(e=>{var t,n,i,o;let s;r.isBuffer(e)?(s=e.toJSON(),s._isBuffer=!0):s=e;const a=null===(t=null==s?void 0:s.data)||void 0===t?void 0:t.method;let c=!1;return"object"==typeof(null===(n=null==s?void 0:s.data)||void 0===n?void 0:n.params)&&!0===(null===(o=null===(i=null==s?void 0:s.data)||void 0===i?void 0:i.params)||void 0===o?void 0:o.__triggeredInstaller)&&(c=!0,s.data.params=s.data.params.wrappedParams),{method:a,data:s,triggeredInstaller:c}})(t);if(qc(`[RCPMS: write()] method='${P}' isRemoteReady=${n} channelId=${x} isSocketConnected=${f} isRemotePaused=${M} providerConnected=${A.isConnected()}`,t),!x)return P!==El.METAMASK_GETPROVIDERSTATE&&qc(`[RCPMS: write()] ${P} --\x3e channelId is undefined`),i(new Error("disconnected"));qc(`[RCPMS: write()] remote.isPaused()=${null===(d=e.state.remote)||void 0===d?void 0:d.isPaused()} authorized=${I} ready=${n} socketConnected=${f}`,t);const N=null===(u=e.state.platformManager)||void 0===u?void 0:u.isSecure(),L=null!==(p=null===(h=e.state.platformManager)||void 0===h?void 0:h.isMobileWeb())&&void 0!==p&&p,D=null!==(g=null===(m=e.state.remote)||void 0===m?void 0:m.hasDeeplinkProtocol())&&void 0!==g&&g&&L&&I;try{if(!T){const t=JSON.stringify(null==O?void 0:O.data);if(t.length>Il)return i(new Error(`Message size ${t.length} exceeds maximum allowed size of 1000000 bytes`));null===(y=e.state.remote)||void 0===y||y.sendMessage(null==O?void 0:O.data).then((()=>{qc(`[RCPMS: _write()] ${P} sent successfully`)})).catch((e=>{qc("[RCPMS: _write()] error sending message",e)}))}if(!N)return qc(`[RCPMS: _write()] unsecure platform for method ${P} -- return callback`),i();if(T)return qc("[RCPMS: _write()] prevent deeplink -- installation completed separately."),i();const t=null!==(E=null===(b=null===(v=e.state.remote)||void 0===v?void 0:v.getKeyInfo())||void 0===b?void 0:b.ecies.public)&&void 0!==E?E:"";let n=encodeURI(`channelId=${x}&pubkey=${t}&comm=socket&t=d&v=2`);if(D){const t=JSON.stringify(null==O?void 0:O.data),r=null===(w=e.state.remote)||void 0===w?void 0:w.encrypt(t);if(!r)return qc("[RCPMS: _write()] error encrypting message"),i(new Error("RemoteCommunicationPostMessageStream - disconnected"));n+=`&scheme=${R}&rpc=${Nl(r)}`}if(!(null===(S=e.state.platformManager)||void 0===S?void 0:S.isMetaMaskInstalled()))return qc("[RCPMS: _write()] prevent deeplink until installation is completed."),i();wl[P]?(qc(`[RCPMS: _write()] redirect link for '${P}' socketConnected=${f} connect?${n}`),null===(_=e.state.platformManager)||void 0===_||_.openDeeplink(`${gl}?${n}`,`${yl}?${n}`,"_self")):(null===(C=e.state.remote)||void 0===C?void 0:C.isPaused())?(qc(`[RCPMS: _write()] MM is PAUSED! deeplink with connect! targetMethod=${P}`),null===(k=e.state.platformManager)||void 0===k||k.openDeeplink(`${gl}?redirect=true&${n}`,`${yl}?redirect=true&${n}`,"_self")):qc(`[RCPMS: _write()] method ${P} doesn't need redirect.`)}catch(e){return qc("[RCPMS: _write()] error sending message",e),i(new Error("RemoteCommunicationPostMessageStream - disconnected"))}return i()}))}class Dl extends Ec.Duplex{constructor({name:e,remote:t,deeplinkProtocol:n,platformManager:r}){super({objectMode:!0}),this.state={_name:null,remote:null,deeplinkProtocol:!1,platformManager:null},this.state._name=e,this.state.remote=t,this.state.deeplinkProtocol=n,this.state.platformManager=r,this._onMessage=this._onMessage.bind(this),this.state.remote.on(ki.MESSAGE,this._onMessage)}_write(e,t,n){return f(this,void 0,void 0,(function*(){return Ll(this,e,0,n)}))}_read(){}_onMessage(e){return function(e,t){try{if(qc("[RCPMS: onMessage()] message",t),!t||"object"!=typeof t)return;if("object"!=typeof(null==t?void 0:t.data))return;if(!(null==t?void 0:t.name))return void qc("[RCPMS: onMessage()] ignore message without name",t);if((null==t?void 0:t.name)!==fl.PROVIDER)return void qc(`[RCPMS: onMessage()] ignore message with wrong name message=${t}`);if(r.isBuffer(t)){const n=r.from(t);e.push(n)}else e.push(t)}catch(e){qc(`[RCPMS: onMessage()] ignore message error err=${e}`)}}(this,e)}start(){}}let Bl=1;const Kl=e=>new Promise((t=>{setTimeout((()=>{t(!0)}),e)})),jl=({checkInstallationOnAllCalls:t=!1,communicationLayerPreference:n,injectProvider:r,shouldShimWeb3:i,platformManager:o,installer:s,sdk:a,remoteConnection:c,debug:l})=>f(void 0,void 0,void 0,(function*(){var d,u;const h=(({name:e,remoteConnection:t})=>{if(!t||!(null==t?void 0:t.getConnector()))throw new Error("Missing remote connection parameter");return new Dl({name:e,remote:null==t?void 0:t.getConnector(),deeplinkProtocol:null==t?void 0:t.state.deeplinkProtocol,platformManager:null==t?void 0:t.getPlatformManager()})})({name:fl.INPAGE,target:fl.CONTENT_SCRIPT,platformManager:o,communicationLayerPreference:n,remoteConnection:c}),p=o.getPlatformType(),m=a.options.dappMetadata,g=`Sdk/Javascript SdkVersion/${pl.version} Platform/${p} dApp/${null!==(d=m.url)&&void 0!==d?d:m.name} dAppTitle/${m.name}`;let y=null,v=null;const b=null===(u=a.options.storage)||void 0===u?void 0:u.storageManager;if(b){try{const e=yield b.getCachedAccounts();e.length>0&&(y=e[0])}catch(e){console.error(`[initializeMobileProvider] failed to get cached addresses: ${e}`)}try{const e=yield b.getCachedChainId();e&&(v=e)}catch(e){console.error(`[initializeMobileProvider] failed to parse cached chainId: ${e}`)}}qc(`[initializeMobileProvider] cachedAccountAddress: ${y}, cachedChainId: ${v}`);const E=!(!r||p===qi.NonBrowser||p===qi.ReactNative),w=ll.init({shouldSetOnWindow:E,connectionStream:h,shouldShimWeb3:i,sdkInstance:a});let S=!1;const _=e=>{S=e},C=()=>S,k=(n,r,i,l)=>f(void 0,void 0,void 0,(function*(){var d,u,h,p,m,E,w,k,M;const A=ll.getProvider();if(S){A.emit("display_uri",(null==c?void 0:c.state.qrcodeLink)||""),null==c||c.showActiveModal();let e=C();for(;e;){const t=C(),n=null==c?void 0:c.isAuthorized();e=t&&!n,qc(`[initializeMobileProvider: sendRequest()] waiting for initialization to complete - initializing: ${t} authorized: ${n}`),yield Kl(1e3)}return qc("[initializeMobileProvider: sendRequest()] initial method completed -- prevent installation and call provider"),i(...r)}const x=o.isMetaMaskInstalled(),I=null==c?void 0:c.isConnected();let R=null,P=null,O=null;if(R=null!==(d=A.getSelectedAddress())&&void 0!==d?d:y,O=A.getChainId()||v,R&&b&&R!==y&&b.persistAccounts([R]).catch((e=>{console.error(`[initializeMobileProvider] failed to persist account: ${e}`)})),O&&(v=O,b&&b.persistChainId(O).catch((e=>{console.error(`[initializeMobileProvider] failed to persist chainId: ${e}`)}))),qc("[initializeMobileProvider: sendRequest()]",{selectedAddress:R,chainId:O}),l&&qc(`[initializeMobileProvider: sendRequest()] method=${n} ongoing=${S} selectedAddress=${R} isInstalled=${x} checkInstallationOnAllCalls=${t} socketConnected=${I}`),R&&n.toLowerCase()===El.ETH_ACCOUNTS.toLowerCase())return[R];if(O&&n.toLowerCase()===El.ETH_CHAINID.toLowerCase())return O;const T=[El.ETH_REQUESTACCOUNTS,El.WALLET_REQUESTPERMISSIONS,El.METAMASK_CONNECTSIGN,El.METAMASK_CONNECTWITH],N=!wl[n],L=null===(u=a.options.readonlyRPCMap)||void 0===u?void 0:u[O];if(L&&N)try{const t=null===(h=null==r?void 0:r[0])||void 0===h?void 0:h.params,i=yield(({rpcEndpoint:t,method:n,sdkInfo:r,params:i})=>f(void 0,void 0,void 0,(function*(){const o=JSON.stringify({jsonrpc:"2.0",method:n,params:i,id:(Bl+=1,Bl)}),s={Accept:"application/json","Content-Type":"application/json"};let a;t.includes("infura")&&(s["Metamask-Sdk-Info"]=r);try{a=yield e(t,{method:"POST",headers:s,body:o})}catch(e){throw e instanceof Error?new Error(`Failed to fetch from RPC: ${e.message}`):new Error(`Failed to fetch from RPC: ${e}`)}if(!a.ok)throw new Error(`Server responded with a status of ${a.status}`);return(yield a.json()).result})))({rpcEndpoint:L,sdkInfo:g,method:n,params:t||[]});return l&&qc(`initializeProvider::ReadOnlyRPCResponse ${i}`),i}catch(e){console.warn(`[initializeMobileProvider: sendRequest()] method=${n} readOnlyRPCRequest failed:`,e)}if((!x||x&&!I)&&n!==El.METAMASK_GETPROVIDERSTATE){const e=(null===(p=null==r?void 0:r[0])||void 0===p?void 0:p.params)||[];if(-1!==T.indexOf(n)||t){_(!0);const t=n===El.METAMASK_CONNECTWITH,o=`${Date.now()}`;try{yield s.start({wait:!1,connectWith:t?{method:n,id:o,params:e}:void 0}),yield new Promise(((e,t)=>{(null==c?void 0:c.isAuthorized())&&(qc("[initializeMobileProvider: sendRequest()] already authorized"),e(!0)),null==c||c.getConnector().once(ki.AUTHORIZED,(()=>{e(!0)})),a.once(ki.PROVIDER_UPDATE,(e=>{qc(`[initializeMobileProvider: sendRequest()] PROVIDER_UPDATE --- remote provider request interupted type=${e}`),e===Rl.EXTENSION?t(ki.PROVIDER_UPDATE):t(new Error("Connection Terminated"))}))}))}catch(t){if(Rl.EXTENSION===t){if(qc(`[initializeMobileProvider: sendRequest()] extension provider detect: re-create ${n} on the active provider`),n.toLowerCase()===El.METAMASK_CONNECTSIGN.toLowerCase()){const t=yield null===(m=a.getProvider())||void 0===m?void 0:m.request({method:El.ETH_REQUESTACCOUNTS,params:[]});if(!t.length)throw new Error("SDK state invalid -- undefined accounts");const n=yield null===(E=a.getProvider())||void 0===E?void 0:E.request({method:El.PERSONAL_SIGN,params:[e[0],t[0]]});return a.emit(cl.ConnectWithResponse,n),n}if(n.toLowerCase()===El.METAMASK_CONNECTWITH.toLowerCase()){const[t]=e,n=yield(({method:e,sdk:t,params:n})=>f(void 0,void 0,void 0,(function*(){var r,i,o,s;if(!t.isExtensionActive())throw new Error("SDK state invalid -- extension is not active");qc("[MetaMaskProvider: extensionConnectWithOverwrite()] Overwriting request method",e,n);const a=yield null===(r=t.getProvider())||void 0===r?void 0:r.request({method:El.ETH_REQUESTACCOUNTS,params:[]});if(!a.length)throw new Error("SDK state invalid -- undefined accounts");if((null==e?void 0:e.toLowerCase())===El.PERSONAL_SIGN.toLowerCase()){const r={method:e,params:[n[0],a[0]]};return yield null===(i=t.getProvider())||void 0===i?void 0:i.request(r)}if((null==e?void 0:e.toLowerCase())===El.ETH_SENDTRANSACTION.toLowerCase()){const r={method:e,params:[Object.assign(Object.assign({},n[0]),{from:a[0]})]};return yield null===(o=t.getProvider())||void 0===o?void 0:o.request(r)}return _l.includes(e.toLowerCase())?(console.warn(`MetaMaskSDK connectWith method=${e} -- not handled by the extension -- call separately`),a):yield null===(s=t.getProvider())||void 0===s?void 0:s.request({method:e,params:n})})))({method:t.method,sdk:a,params:t.params});return a.emit(cl.ConnectWithResponse,n),n}return qc(`[initializeMobileProvider: sendRequest()] sending '${n}' on active provider`,e),yield null===(w=a.getProvider())||void 0===w?void 0:w.request({method:n,params:e})}if(t===ki.REJECTED)throw null==c||c.closeModal(),null===(k=a.getProvider())||void 0===k||k.handleDisconnect({terminate:!1}),Object.assign(new Error("User rejected connection"),{code:4001});throw qc(`[initializeMobileProvider: sendRequest()] failed to start installer: ${t}`),t}finally{_(!1)}if(n===El.ETH_REQUESTACCOUNTS)return P=yield new Promise((e=>{const t=setInterval((()=>{const{accounts:n}=A.getState();n&&(clearInterval(t),e(n))}),100)})),qc(`[initializeMobileProvider: sendRequest()] selectedAddress: ${R} --- SKIP rpc call`),P;if(n===El.METAMASK_CONNECTWITH)try{let e=0;const t=5,n=({resolve:n,reject:r})=>{e+=1;const i=null==c?void 0:c.getConnector().getRPCMethodTracker(),s=null==i?void 0:i[o];return qc(`TRACKER: update method ${o}`,s),(null==s?void 0:s.result)?(qc("[initializeMobileProvider: sendRequest()] found result",s.result),a.emit(cl.ConnectWithResponse,s.result),void n(s.result)):(null==s?void 0:s.error)?(qc("[initializeMobileProvider: sendRequest()] found error",s.error),void r(s.error)):e>=t?(qc("[initializeMobileProvider: sendRequest()] max message count reached without result"),void r(new Error("Max message count reached without result"))):void qc("[initializeMobileProvider: sendRequest()] not found yet, need to wait for next update")};let r,i;const s=yield new Promise(((e,t)=>{const s=null==c?void 0:c.getConnector().getRPCMethodTracker();qc(`TRACKER: method ${o}`,s),(null==s?void 0:s[o].result)?(qc("[initializeMobileProvider: sendRequest()] found result",null==s?void 0:s[o].result),e(null==s?void 0:s[o].result)):(null==s?void 0:s[o].error)&&(qc("[initializeMobileProvider: sendRequest()] found error",null==s?void 0:s[o].error),t(null==s?void 0:s[o].error)),i=()=>n({resolve:e,reject:t}),r=null==c?void 0:c.getConnector().on(ki.RPC_UPDATE,i)}));return i&&(null==r||r.off(ki.RPC_UPDATE,i)),qc("TRACKER: result",s),s}catch(e){throw qc("[initializeMobileProvider: sendRequest()] error:",e),e}r[0]&&"object"==typeof r[0]&&(r[0].params={__triggeredInstaller:!0,wrappedParams:r[0].params});return i(...r)}if(o.isSecure()&&wl[n])return i(...r);if(a.isExtensionActive())return qc(`[initializeMobileProvider: sendRequest()] EXTENSION active - redirect request '${n}' to it`,r,e),yield null===(M=a.getProvider())||void 0===M?void 0:M.request({method:n,params:e});throw qc(`[initializeMobileProvider: sendRequest()] method=${n} --- skip --- not connected/installed`),new Error("MetaMask is not connected/installed, please call eth_requestAccounts to connect first.")}try{const e=yield i(...r);if(qc(`[initializeMobileProvider: sendRequest()] method=${n} rpcResponse`,e),n===El.WALLET_REQUESTPERMISSIONS){const t=e.reduce(((e,t)=>{var n;if("eth_accounts"===t.parentCapability){const r=null===(n=t.caveats.find((e=>"restrictReturnedAccounts"===e.type)))||void 0===n?void 0:n.value;r&&e.push(...r)}return e}),[]);qc("[initializeMobileProvider: sendRequest()] accountsToPersist:",t),t.length>0&&(A.handleAccountsChanged(t,!1),null==b||b.persistAccounts(t))}return e}catch(e){throw console.error("[initializeMobileProvider: sendRequest()] error:",e),e}})),{request:M}=w;w.request=(...e)=>f(void 0,void 0,void 0,(function*(){return k(null==e?void 0:e[0].method,e,M,l)}));const{send:A}=w;return w.send=(...e)=>f(void 0,void 0,void 0,(function*(){return k(null==e?void 0:e[0],e,A,l)})),qc("[initializeMobileProvider: sendRequest()] metamaskStream.start()"),h.start(),w}));function $l(e){var t,n,r,i;return f(this,void 0,void 0,(function*(){const{options:o}=e,s={communicationLayerPreference:null!==(t=o.communicationLayerPreference)&&void 0!==t?t:Fi.SOCKET,platformManager:e.platformManager,sdk:e,checkInstallationOnAllCalls:o.checkInstallationOnAllCalls,injectProvider:null===(n=o.injectProvider)||void 0===n||n,shouldShimWeb3:null===(r=o.shouldShimWeb3)||void 0===r||r,extensionOnly:null===(i=o.extensionOnly)||void 0===i||i,installer:e.installer,remoteConnection:e.remoteConnection,debug:e.debug},a=yield jl(s);e.activeProvider=a,function(e){var t,n,r,i;null===(n=null===(t=e.remoteConnection)||void 0===t?void 0:t.getConnector())||void 0===n||n.on(cl.ConnectionStatus,(t=>{e.emit(cl.ConnectionStatus,t)})),null===(i=null===(r=e.remoteConnection)||void 0===r?void 0:r.getConnector())||void 0===i||i.on(cl.ServiceStatus,(t=>{e.emit(cl.ServiceStatus,t)}))}(e)}))}const Ul="sdk";class Hl{constructor({serverUrl:e,enabled:t,originatorInfo:n}){this.serverURL=vi,this.serverURL=e,this.originatorInfo=n,this.enabled=null==t||t}send({event:e,params:t}){if(!this.enabled)return;const n=Object.assign(Object.assign({id:Ul,event:e,sdkVersion:pl.version},this.originatorInfo),{params:t});qc(`[Analytics: send()] event: ${e}`,n),mi(n,this.serverURL).catch((e=>{qc(`[Analytics: send()] error: ${e}`)}))}}const Fl=()=>{if("undefined"==typeof document)return;let e;const t=document.getElementsByTagName("link");for(let n=0;n<t.length;n++)"icon"!==t[n].getAttribute("rel")&&"shortcut icon"!==t[n].getAttribute("rel")||(e=t[n].getAttribute("href"));return e},ql=163400;function zl(e){var t,n,r;const{dappMetadata:i}=e,o=function({url:e,name:t}){var n;const r=e+t,i=Nl(r);if(!localStorage)return"";let o=null!==(n=localStorage.getItem(i))&&void 0!==n?n:"";if(!o){o=c();try{localStorage.setItem(i,o)}catch(e){return""}}return o}({url:null!==(t=null==i?void 0:i.url)&&void 0!==t?t:"no_url",name:null!==(n=null==i?void 0:i.name)&&void 0!==n?n:"no_name"}),s=null===(r=e.platformManager)||void 0===r?void 0:r.getPlatformType(),a=s===qi.DesktopWeb,l=s===qi.MetaMaskMobileWebview;let d="N/A";return a?d="extension":l&&(d="mobile"),{id:o,from:d}}const Wl=({provider:e,sdkInstance:t})=>{if("state"in e)throw new Error("INVALID EXTENSION PROVIDER");return new Proxy(e,{get:(n,r)=>"request"===r?function(e){var r,i;return f(this,void 0,void 0,(function*(){qc("[wrapExtensionProvider()] Overwriting request method",e);const{method:o,params:s}=e,a=Sl.includes(o.toLowerCase()),{id:c,from:l}=zl(t);if(a&&(null===(r=t.analytics)||void 0===r||r.send({event:Ii.SDK_RPC_REQUEST,params:{method:o,from:l,id:c}})),o===El.METAMASK_BATCH&&Array.isArray(s))return(({target:e,args:t,trackEvent:n,sdkInstance:r})=>f(void 0,void 0,void 0,(function*(){var i,o;if("metamask_batch"!==t.method)throw new Error("Invalid usage");const s=[],a=null!==(i=null==t?void 0:t.params)&&void 0!==i?i:[];for(const t of a){const n=yield null==e?void 0:e.request({method:t.method,params:t.params});s.push(n)}const{id:c,from:l}=zl(r);return n&&(null===(o=r.analytics)||void 0===o||o.send({event:Ii.SDK_RPC_REQUEST_DONE,params:{method:t.method,from:l,id:c}})),s})))({target:n,args:e,trackEvent:a,sdkInstance:t});if(o.toLowerCase()===El.METAMASK_CONNECTSIGN.toLowerCase()&&Array.isArray(s))return(({target:e,params:t})=>f(void 0,void 0,void 0,(function*(){const n=yield e.request({method:El.ETH_REQUESTACCOUNTS,params:[]});if(!n.length)throw new Error("SDK state invalid -- undefined accounts");return yield e.request({method:El.PERSONAL_SIGN,params:[t[0],n[0]]})})))({target:n,params:s});if(o.toLowerCase()===El.METAMASK_CONNECTWITH.toLowerCase()&&Array.isArray(s))return(({target:e,params:t})=>f(void 0,void 0,void 0,(function*(){const[n]=t,r=n.method,i=n.params,o=yield e.request({method:El.ETH_REQUESTACCOUNTS,params:[]});if(!o.length)throw new Error("SDK state invalid -- undefined accounts");return(null==r?void 0:r.toLowerCase())===El.PERSONAL_SIGN.toLowerCase()?yield e.request({method:r,params:[i[0],o[0]]}):(null==r?void 0:r.toLowerCase())===El.ETH_SENDTRANSACTION.toLowerCase()?yield e.request({method:r,params:[Object.assign(Object.assign({},i[0]),{from:o[0]})]}):_l.includes(r.toLowerCase())?(console.warn(`MetaMaskSDK connectWith method=${r} -- not handled by the extension -- call separately`),o):yield e.request({method:r,params:i})})))({target:n,params:s});let d;try{return d=yield n.request(e),d}finally{a&&(null===(i=t.analytics)||void 0===i||i.send({event:Ii.SDK_RPC_REQUEST_DONE,params:{method:o,from:l,id:c}}))}}))}:"getChainId"===r?function(){return e.chainId}:"getNetworkVersion"===r?function(){return e.networkVersion}:"getSelectedAddress"===r?function(){return e.selectedAddress}:"isConnected"===r?function(){return e._state.isConnected}:n[r]})};var Vl;function Gl({mustBeMetaMask:e,sdkInstance:t}){return f(this,void 0,void 0,(function*(){if("undefined"==typeof window)throw new Error("window not available");try{const e=yield new Promise(((e,t)=>{const n=setTimeout((()=>{t(new Error("eip6963RequestProvider timed out"))}),500);window.addEventListener(Vl.Announce,(t=>{const r=t,{detail:{info:i,provider:o}={}}=r,{name:s,rdns:a,uuid:c}=null!=i?i:{};bl.test(c)&&s.startsWith(vl.NAME)&&vl.RDNS.includes(a)&&(clearTimeout(n),e(o))})),window.dispatchEvent(new Event(Vl.Request))}));return Wl({provider:e,sdkInstance:t})}catch(n){if(!e&&window.ethereum)return Wl({provider:window.ethereum,sdkInstance:t});throw new Error("Provider not found")}}))}!function(e){e.Announce="eip6963:announceProvider",e.Request="eip6963:requestProvider"}(Vl||(Vl={}));const Yl=e=>f(void 0,void 0,void 0,(function*(){const{options:t}=e,{infuraAPIKey:n}=t;if(!n)return;const r={"0x1":`https://mainnet.infura.io/v3/${n}`,"0x5":`https://goerli.infura.io/v3/${n}`,"0xaa36a7":`https://sepolia.infura.io/v3/${n}`,"0xe708":`https://linea-mainnet.infura.io/v3/${n}`,"0xe704":`https://linea-goerli.infura.io/v3/${n}`,"0x89":`https://polygon-mainnet.infura.io/v3/${n}`,"0x13881":`https://polygon-mumbai.infura.io/v3/${n}`,"0x45":`https://optimism-mainnet.infura.io/v3/${n}`,"0x1a4":`https://optimism-goerli.infura.io/v3/${n}`,"0xa4b1":`https://arbitrum-mainnet.infura.io/v3/${n}`,"0x66eed":`https://arbitrum-goerli.infura.io/v3/${n}`,"0x2a15c308d":`https://palm-mainnet.infura.io/v3/${n}`,"0x2a15c3083":`https://palm-testnet.infura.io/v3/${n}`,"0xa86a":`https://avalanche-mainnet.infura.io/v3/${n}`,"0xa869":`https://avalanche-fuji.infura.io/v3/${n}`,"0x4e454152":`https://aurora-mainnet.infura.io/v3/${n}`,"0x4e454153":`https://aurora-testnet.infura.io/v3/${n}`,"0x534e5f4d41494e":`https://starknet-mainnet.infura.io/v3/${n}`,"0x534e5f474f45524c49":`https://starknet-goerli.infura.io/v3/${n}`,"0x534e5f474f45524c4932":`https://starknet-goerli2.infura.io/v3/${n}`,"0xa4ec":`https://celo-mainnet.infura.io/v3/${n}`,"0xaef3":`https://celo-alfajores.infura.io/v3/${n}`};e.options.readonlyRPCMap?e.options.readonlyRPCMap=Object.assign(Object.assign({},e.options.readonlyRPCMap),r):e.options.readonlyRPCMap=r}));const Zl=e=>f(void 0,void 0,void 0,(function*(){const{options:t}=e,{readonlyRPCMap:n}=t;if(n)try{qc("[MetaMaskSDK: setupReadOnlyRPCProviders()] Setting up Readonly RPC Providers",n),e.setReadOnlyRPCCalls(!0)}catch(e){throw new Error("Invalid Infura Settings")}}));var Jl,Ql=(Jl=sl)&&"object"==typeof Jl&&"default"in Jl?Jl.default:Jl;function Xl(e,t,n,r){return new(n||(n=Promise))((function(t,i){function o(e){try{a(r.next(e))}catch(e){i(e)}}function s(e){try{a(r.throw(e))}catch(e){i(e)}}function a(e){var r;e.done?t(e.value):(r=e.value,r instanceof n?r:new n((function(e){e(r)}))).then(o,s)}a((r=r.apply(e,[])).next())}))}function ed(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var td="INSTALLED",nd="NOT_INSTALLED",rd="REGISTERED",id="REGISTERING",od="RELOADING",sd={CHROME:"https://chrome.google.com/webstore/detail/metamask/nkbihfbeogaeaoehlefnkodbefgpgknn",FIREFOX:"https://addons.mozilla.org/firefox/addon/ether-metamask/",DEFAULT:"https://metamask.io"},ad="REGISTRATION_IN_PROGRESS",cd="FORWARDER_ID",ld=function(){function e(t){var n=void 0===t?{}:t,r=n.forwarderOrigin,i=void 0===r?"https://fwd.metamask.io":r,o=n.forwarderMode,s=void 0===o?e.FORWARDER_MODE.INJECT:o;this.forwarderOrigin=i,this.forwarderMode=s,this.state=e.isMetaMaskInstalled()?td:nd;var a=e._detectBrowser();this.downloadUrl=a?sd[a]:sd.DEFAULT,this._onMessage=this._onMessage.bind(this),this._onMessageFromForwarder=this._onMessageFromForwarder.bind(this),this._openForwarder=this._openForwarder.bind(this),this._openDownloadPage=this._openDownloadPage.bind(this),this.startOnboarding=this.startOnboarding.bind(this),this.stopOnboarding=this.stopOnboarding.bind(this),window.addEventListener("message",this._onMessage),s===e.FORWARDER_MODE.INJECT&&"true"===sessionStorage.getItem(ad)&&e._injectForwarder(this.forwarderOrigin)}return e.prototype._onMessage=function(e){if(e.origin===this.forwarderOrigin)return"metamask:reload"===e.data.type?this._onMessageFromForwarder(e):void console.debug("Unknown message from '"+e.origin+"' with data "+JSON.stringify(e.data))},e.prototype._onMessageUnknownStateError=function(e){throw new Error("Unknown state: '"+e+"'")},e.prototype._onMessageFromForwarder=function(t){return Xl(this,0,void 0,(function(){return ed(this,(function(n){switch(n.label){case 0:switch(this.state){case od:return[3,1];case nd:return[3,2];case td:return[3,3];case id:return[3,5];case rd:return[3,6]}return[3,7];case 1:return console.debug("Ignoring message while reloading"),[3,8];case 2:return console.debug("Reloading now to register with MetaMask"),this.state=od,location.reload(),[3,8];case 3:return console.debug("Registering with MetaMask"),this.state=id,[4,e._register()];case 4:return n.sent(),this.state=rd,t.source.postMessage({type:"metamask:registrationCompleted"},t.origin),this.stopOnboarding(),[3,8];case 5:return console.debug("Already registering - ignoring reload message"),[3,8];case 6:return console.debug("Already registered - ignoring reload message"),[3,8];case 7:this._onMessageUnknownStateError(this.state),n.label=8;case 8:return[2]}}))}))},e.prototype.startOnboarding=function(){sessionStorage.setItem(ad,"true"),this._openDownloadPage(),this._openForwarder()},e.prototype.stopOnboarding=function(){"true"===sessionStorage.getItem(ad)&&(this.forwarderMode===e.FORWARDER_MODE.INJECT&&(console.debug("Removing forwarder"),e._removeForwarder()),sessionStorage.setItem(ad,"false"))},e.prototype._openForwarder=function(){this.forwarderMode===e.FORWARDER_MODE.OPEN_TAB?window.open(this.forwarderOrigin,"_blank"):e._injectForwarder(this.forwarderOrigin)},e.prototype._openDownloadPage=function(){window.open(this.downloadUrl,"_blank")},e.isMetaMaskInstalled=function(){return Boolean(window.ethereum&&window.ethereum.isMetaMask)},e._register=function(){return window.ethereum.request({method:"wallet_registerOnboarding"})},e._injectForwarder=function(e){var t=document.body,n=document.createElement("iframe");n.setAttribute("height","0"),n.setAttribute("width","0"),n.setAttribute("style","display: none;"),n.setAttribute("src",e),n.setAttribute("id",cd),t.insertBefore(n,t.children[0])},e._removeForwarder=function(){var e;null===(e=document.getElementById(cd))||void 0===e||e.remove()},e._detectBrowser=function(){var e=Ql.parse(window.navigator.userAgent);return"Firefox"===e.browser.name?"FIREFOX":["Chrome","Chromium"].includes(e.browser.name||"")?"CHROME":null},e.FORWARDER_MODE={INJECT:"INJECT",OPEN_TAB:"OPEN_TAB"},e}(),dd=m(ld);function ud(e,{wait:t=!1}){return f(this,void 0,void 0,(function*(){return qc(`[MetamaskInstaller: startInstaller()] wait=${t}`),t&&(yield Kl(1e3)),yield e.checkInstallation()}))}class hd{constructor({remote:e,preferDesktop:t,platformManager:n,debug:r=!1}){this.state={isInstalling:!1,hasInstalled:!1,resendRequest:null,preferDesktop:!1,platformManager:null,remote:null,debug:!1,connectWith:void 0},this.state.remote=e,this.state.preferDesktop=t,this.state.platformManager=n,this.state.debug=r}startDesktopOnboarding(){return function(){return f(this,void 0,void 0,(function*(){qc("[MetamaskInstaller: startDesktopOnboarding() starting desktop onboarding"),window.ethereum&&(window.ethereum=void 0),(new dd).startOnboarding()}))}()}redirectToProperInstall(){return f(this,void 0,void 0,(function*(){return function(e){var t,n;return f(this,void 0,void 0,(function*(){const{state:r}=e,i=null===(t=r.platformManager)||void 0===t?void 0:t.getPlatformType();if(qc(`[MetamaskInstaller: redirectToProperInstall()] platform=${i}`),i===qi.MetaMaskMobileWebview)return!1;r.isInstalling=!0;try{yield null===(n=r.remote)||void 0===n?void 0:n.startConnection({connectWith:r.connectWith}),r.isInstalling=!1,r.hasInstalled=!0}catch(e){throw r.isInstalling=!1,e}return!0}))}(this)}))}checkInstallation(){return f(this,void 0,void 0,(function*(){return function(e){var t;return f(this,void 0,void 0,(function*(){const{state:n}=e,r=null===(t=n.platformManager)||void 0===t?void 0:t.isMetaMaskInstalled();return qc(`[MetamaskInstaller: checkInstallation()] isInstalled=${r}`),!!r||(yield e.redirectToProperInstall())}))}(this)}))}start({wait:e=!1,connectWith:t}){return f(this,void 0,void 0,(function*(){this.state.connectWith=t,qc(`[MetaMaskInstaller: start()] wait=${e}`,t),yield ud(this,{wait:e})}))}}const fd=({link:e})=>(qc(`[UI: InstallModal-nonweb()] INSTALL MODAL link=${e}`),{unmount:()=>{}}),pd=()=>(qc("[UI: pendingModal-nodejs: PendingModal()] Please open the MetaMask wallet app and confirm the connection. Thank you!"),{unmount:()=>!1,updateOTPValue:e=>{""!==e&&qc(`[UI: pendingModal-nodejs: PendingModal()] Choose the following value on your metamask mobile wallet: ${e}`)},mount:()=>!1});function md(e,t){var n,r,i,o;e.connector||(qc("[RemoteConnection: initializeConnector()] initialize connector"),e.connector=new io({platformType:t.platformManager.getPlatformType(),communicationLayerPreference:t.communicationLayerPreference,transports:t.transports,dappMetadata:Object.assign(Object.assign({},t.dappMetadata),{source:t._source}),analytics:t.enableAnalytics,communicationServerUrl:t.communicationServerUrl,sdkVersion:pl.version,context:"dapp",ecies:t.ecies,storage:t.storage,logging:t.logging}),t.timer&&(qc("[RemoteConnection: initializeConnector()] reset background timer",t.timer),null===(r=null===(n=t.timer)||void 0===n?void 0:n.stopBackgroundTimer)||void 0===r||r.call(n),null===(o=null===(i=t.timer)||void 0===i?void 0:i.runBackgroundTimer)||void 0===o||o.call(i,(()=>!1),1e4)))}function gd(e){e.listeners.forEach((({event:t,handler:n})=>{var r;null===(r=e.connector)||void 0===r||r.off(t,n)})),e.listeners=[]}function yd(e,t,n){return f(this,void 0,void 0,(function*(){return new Promise(((r,i)=>{if(!e.connector)return void i(new Error("No connector available"));qc("[RemoteConnection: connectWithModalInstaller()]",{state:e,options:t,linkParams:n});const o=`${e.useDeeplink?yl:gl}?${n}`;!function(e,t,n){var r,i,o,s;e.installModal=null===(i=(r=t.modals).install)||void 0===i?void 0:i.call(r,{link:n,preferDesktop:e.preferDesktop,installer:t.getMetaMaskInstaller(),terminate:()=>{qc("[RemoteConnection: showInstallModal() => terminate()] terminate connection"),t.sdk.terminate().catch((e=>{console.warn("[MMSDK] failed to terminate connection",e)}))},debug:e.developerMode,connectWithExtension:()=>{var e;return null===(e=t.connectWithExtensionProvider)||void 0===e||e.call(t),!1},onAnalyticsEvent:({event:n,params:r})=>{var i,o,s;const a=Object.assign(Object.assign({},r),{sdkVersion:t.sdk.getVersion(),dappId:null===(i=t.dappMetadata)||void 0===i?void 0:i.name,source:t._source,url:null===(o=t.dappMetadata)||void 0===o?void 0:o.url});null===(s=e.analytics)||void 0===s||s.send({event:n,params:a})}}),null===(s=null===(o=e.installModal)||void 0===o?void 0:o.mount)||void 0===s||s.call(o,n)}(e,t,o),t.sdk.once(ki.PROVIDER_UPDATE,(e=>f(this,void 0,void 0,(function*(){if(qc("[RemoteConnection: connectWithModalInstaller()] once provider_update -- resolving startConnection promise"),e!==Rl.TERMINATE)i(e);else{i({code:4001,message:"User rejected the request."})}})))),e.connector.once(ki.AUTHORIZED,(()=>{r()})),e.connector.once(ki.REJECTED,(()=>{i(ki.REJECTED)})),e.connector.once(ki.CLIENTS_READY,(()=>f(this,void 0,void 0,(function*(){qc("[RemoteConnection: connectWithModalInstaller()] once clients_ready -- resolving startConnection promise"),r()}))))}))}))}function vd(e,t){function n(t,n){var r;null===(r=e.connector)||void 0===r||r.on(t,n),e.listeners.push({event:t,handler:n})}e.connector&&(gd(e),n(ki.WALLET_INIT,(({accounts:e,chainId:t})=>f(this,void 0,void 0,(function*(){qc(`[RemoteConnection: setupListeners() => EventType.WALLET_INIT] 'wallet_init' accounts=${e} chainId=${t}`);const n=ll.getProvider();n._setConnected();const r={accounts:e,chainId:t,isUnlocked:!1};n._initializeState(r),n.emit("chainChanged",t),n.emit("accountsChanged",e)})))),n(ki.AUTHORIZED,(()=>f(this,void 0,void 0,(function*(){var t,n,r,i;try{qc("[RemoteConnection: setupListeners() => EventType.AUTHORIZED] 'authorized' closing modals",e.pendingModal,e.installModal);const o=ll.getProvider();o._setConnected(),null===(n=null===(t=e.pendingModal)||void 0===t?void 0:t.unmount)||void 0===n||n.call(t),null===(i=null===(r=e.installModal)||void 0===r?void 0:r.unmount)||void 0===i||i.call(r,!1),e.otpAnswer=void 0,e.authorized=!0,qc("[RemoteConnection: setupListeners() => EventType.AUTHORIZED] 'authorized' provider.state",o.getState()),yield o.forceInitializeState()}catch(e){}})))),n(ki.TERMINATE,(()=>{var t,n,r,i,o;null===(n=null===(t=e.pendingModal)||void 0===t?void 0:t.unmount)||void 0===n||n.call(t),null===(i=null===(r=e.installModal)||void 0===r?void 0:r.unmount)||void 0===i||i.call(r,!0),e.pendingModal=void 0,e.installModal=void 0,e.otpAnswer=void 0,null===(o=e.connector)||void 0===o||o.disconnect({terminate:!0}),e.authorized=!1;ll.getProvider().handleDisconnect({terminate:!0}),gd(e),qc("[RemoteConnection: setupListeners()] All listeners cleaned up")})))}function bd(e,t,{initialCheck:n,connectWith:r}={}){var i,o,s,a,c,l,d,u,h,p,m,g,y,v,b,E,w,S,_;return f(this,void 0,void 0,(function*(){try{if(md(e,t),!e.connector)throw new Error("no connector defined");vd(e);const C=ll.getProvider();e.authorized=!1,C.emit("connecting");const k=yield null===(i=e.connector)||void 0===i?void 0:i.originatorSessionConnect();qc(`[RemoteConnection: startConnection()] after originatorSessionConnect initialCheck=${n}`,k);let M=null!==(o=null==k?void 0:k.channelId)&&void 0!==o?o:"",A=null!==(a=null===(s=e.connector.getKeyInfo())||void 0===s?void 0:s.ecies.public)&&void 0!==a?a:"",x=null!==(l=null===(c=e.connector.getKeyInfo())||void 0===c?void 0:c.ecies.private)&&void 0!==l?l:"";if(n&&!k)return Promise.resolve();if(!k&&!n){const t=yield e.connector.generateChannelIdConnect();M=null!==(d=t.channelId)&&void 0!==d?d:"",A=null!==(u=t.pubKey)&&void 0!==u?u:"",x=null!==(h=t.privKey)&&void 0!==h?h:"";const n=Date.now();null===(p=e.connector.state.storageManager)||void 0===p||p.persistChannelConfig({channelId:M,localKey:x,lastActive:n,validUntil:n+Ei})}if(n&&(null==k?void 0:k.channelId))return(null===(m=e.connector)||void 0===m?void 0:m.isConnected())||(qc(`[RemoteConnection: startConnection()] reconnecting to channel initialCheck=${n}`,k),yield null===(g=e.connector)||void 0===g?void 0:g.connectToChannel({channelId:M})),Promise.resolve();k&&!(null===(y=e.connector)||void 0===y?void 0:y.isConnected())&&(qc("[RemoteConnection: startConnection()] reconnecting to channel",k),yield null===(v=e.connector)||void 0===v?void 0:v.connectToChannel({channelId:M}));const I=(null===(b=e.platformManager)||void 0===b?void 0:b.isSecure())?"":"&t=q",R=pl.version,{iconUrl:P,name:O,url:T,scheme:N}=t.dappMetadata||{},L=null===(E=e.platformManager)||void 0===E?void 0:E.getPlatformType();let D="N/A";"undefined"!=typeof window&&window.location&&window.location.hostname?D=window.location.hostname:void 0!==O?D=O:void 0!==T&&(D=T);const B={url:null!=T?T:"",title:null!=O?O:"",icon:P,scheme:null!=N?N:"",apiVersion:R,dappId:D||T||"N/A",platform:null!=L?L:"",source:null!==(w=t._source)&&void 0!==w?w:""},K=Nl(JSON.stringify(B));let j=`channelId=${M}&v=2&comm=${null!==(S=e.communicationLayerPreference)&&void 0!==S?S:""}&pubkey=${A}${I}&originatorInfo=${K}`;if(r){j+=`&rpc=${Nl(JSON.stringify(r))}`;const t=e.connector.getRPCMethodTracker();t&&(t[`${r.id}`]=Object.assign(Object.assign({},r),{id:`${r.id}`,timestamp:Date.now()}))}const $=encodeURI(j),U=`${e.useDeeplink?yl:gl}?${j}`;return e.qrcodeLink=U,e.developerMode&&qc(`[RemoteConnection: startConnection()] qrcodeLink=${$}`),C.emit("display_uri",U),(null===(_=e.platformManager)||void 0===_?void 0:_.isSecure())?(yield function(e,t){var n,r;return f(this,void 0,void 0,(function*(){const i=`${gl}?${t}`,o=`${yl}?${t}`;null===(r=null===(n=e.platformManager)||void 0===n?void 0:n.openDeeplink)||void 0===r||r.call(n,i,o,"_self")}))}(e,$),new Promise(((t,n)=>{var r,i,o;(null===(r=e.connector)||void 0===r?void 0:r.isAuthorized())?t():(null===(i=e.connector)||void 0===i||i.once(ki.AUTHORIZED,(()=>{t()})),null===(o=e.connector)||void 0===o||o.once(ki.REJECTED,(()=>{n(ki.REJECTED)})))}))):yd(e,t,$)}catch(e){throw console.error("[startConnection] error",e),e}}))}class Ed{constructor(e){var t,n,r;this.state={connector:void 0,qrcodeLink:void 0,analytics:void 0,developerMode:!1,authorized:!1,reconnection:!1,preferDesktop:!1,deeplinkProtocol:!1,listeners:[],communicationLayerPreference:void 0,platformManager:void 0,pendingModal:void 0,installModal:void 0,otpAnswer:void 0},this.options=e;const i=!0===(null===(t=e.logging)||void 0===t?void 0:t.developerMode)||!0===(null===(n=e.logging)||void 0===n?void 0:n.sdk);this.state.developerMode=i,this.state.analytics=e.analytics,this.state.preferDesktop=null!==(r=e.preferDesktop)&&void 0!==r&&r,this.state.useDeeplink=e.sdk.options.useDeeplink,this.state.communicationLayerPreference=e.communicationLayerPreference,this.state.platformManager=e.platformManager,e.modals.install||(e.modals.install=fd),e.modals.otp||(e.modals.otp=pd)}startConnection(e){return f(this,void 0,void 0,(function*(){return bd(this.state,this.options,e)}))}initRemoteCommunication({sdkInstance:e}){var t,n,r;return f(this,void 0,void 0,(function*(){const i=yield null===(n=null===(t=e.options.storage)||void 0===t?void 0:t.storageManager)||void 0===n?void 0:n.getPersistedChannelConfig();if(!this.options.ecies){const e={privateKey:null==i?void 0:i.localKey};this.options.ecies=e}md(this.state,this.options),yield null===(r=this.getConnector())||void 0===r?void 0:r.initFromDappStorage(),vd(this.state,this.options)}))}showActiveModal(){return function(e){var t,n,r,i;e.authorized?qc("[RemoteConnection: showActiveModal()] already authorized"):e.pendingModal?null===(n=(t=e.pendingModal).mount)||void 0===n||n.call(t):e.installModal&&(null===(i=(r=e.installModal).mount)||void 0===i||i.call(r,e.qrcodeLink||""))}(this.state)}closeModal(){var e,t,n,r;null===(t=null===(e=this.state.pendingModal)||void 0===e?void 0:e.unmount)||void 0===t||t.call(e),null===(r=null===(n=this.state.installModal)||void 0===n?void 0:n.unmount)||void 0===r||r.call(n,!1)}getUniversalLink(){if(!this.state.qrcodeLink)throw new Error("connection not started. run startConnection() first.");return this.state.qrcodeLink}getChannelConfig(){var e;return null===(e=this.state.connector)||void 0===e?void 0:e.getChannelConfig()}getKeyInfo(){var e;return null===(e=this.state.connector)||void 0===e?void 0:e.getKeyInfo()}getConnector(){if(!this.state.connector)throw new Error("invalid remote connector");return this.state.connector}getPlatformManager(){if(!this.state.platformManager)throw new Error("PlatformManager not available");return this.state.platformManager}isConnected(){var e;return(null===(e=this.state.connector)||void 0===e?void 0:e.isReady())||!1}isAuthorized(){var e;return(null===(e=this.state.connector)||void 0===e?void 0:e.isAuthorized())||!1}isPaused(){var e;return null===(e=this.state.connector)||void 0===e?void 0:e.isPaused()}disconnect(e){var t,n,r;qc("[RemoteConnection: disconnect()]",e),(null==e?void 0:e.terminate)&&(ll.getProvider().handleDisconnect({terminate:!0}),null===(n=null===(t=this.state.pendingModal)||void 0===t?void 0:t.unmount)||void 0===n||n.call(t),this.state.otpAnswer=void 0),null===(r=this.state.connector)||void 0===r||r.disconnect(e),function(e){qc("[RemoteConnection: cleanupConnector()] cleaning up connector"),e.connector&&(gd(e),e.connector.disconnect({terminate:!0}).catch((e=>{qc("[RemoteConnection: cleanupConnector()] error disconnecting connector",e)})))}(this.state)}}function wd(e){var n,r,i,o,s,a,c,l,d,u,h;return f(this,void 0,void 0,(function*(){const{options:p}=e;if(p.logging=null!==(n=p.logging)&&void 0!==n?n:{},p.communicationLayerPreference=null!==(r=p.communicationLayerPreference)&&void 0!==r?r:Fi.SOCKET,void 0!==p.enableDebug&&(t.enable("MM_SDK"),console.warn("enableDebug is removed. Please use enableAnalytics instead.")),p.enableAnalytics=null===(i=p.enableAnalytics)||void 0===i||i,p.injectProvider=null===(o=p.injectProvider)||void 0===o||o,p.shouldShimWeb3=null===(s=p.shouldShimWeb3)||void 0===s||s,p.extensionOnly=null===(a=p.extensionOnly)||void 0===a||a,p.useDeeplink=null===(c=p.useDeeplink)||void 0===c||c,p.storage=null!==(l=p.storage)&&void 0!==l?l:{enabled:!0},p.headless){t("[MetaMaskSDK: performSDKInitialization()] headless mode enabled");const e=()=>{},n={install:()=>({mount:e,unmount:e})},r={installer:e};p.modals=n,p.ui=r}const m=!0===(null===(d=p.logging)||void 0===d?void 0:d.developerMode);e.debug=(null===(u=p.logging)||void 0===u?void 0:u.sdk)||m,qc("[MetaMaskSDK: performSDKInitialization()] options",e.options);const g=Object.assign({},p.logging);m&&(g.sdk=!0,g.eciesLayer=!0,g.keyExchangeLayer=!0,g.remoteLayer=!0,g.serviceLayer=!0,g.plaintext=!0),yield function(e){var t;return f(this,void 0,void 0,(function*(){const{options:n}=e;e.platformManager=new dl({useDeepLink:null!==(t=n.useDeeplink)&&void 0!==t&&t,preferredOpenLink:n.openDeeplink,debug:e.debug})}))}(e),yield function(e){var t,n,r,i,o,s,a,c,l;return f(this,void 0,void 0,(function*(){const{options:d}=e,u=null===(t=e.platformManager)||void 0===t?void 0:t.getPlatformType();e.analytics=new Hl({serverUrl:null!==(n=d.communicationServerUrl)&&void 0!==n?n:vi,enabled:d.enableAnalytics,originatorInfo:{url:null!==(r=d.dappMetadata.url)&&void 0!==r?r:"",title:null!==(i=d.dappMetadata.name)&&void 0!==i?i:"",dappId:"undefined"==typeof window||void 0===window.location?null!==(c=null!==(s=null===(o=d.dappMetadata)||void 0===o?void 0:o.name)&&void 0!==s?s:null===(a=d.dappMetadata)||void 0===a?void 0:a.url)&&void 0!==c?c:"N/A":window.location.hostname,platform:null!=u?u:"",source:null!==(l=d._source)&&void 0!==l?l:""}})}))}(e),yield function(e){var t;return f(this,void 0,void 0,(function*(){const{options:n}=e;!0!==(null===(t=n.storage)||void 0===t?void 0:t.enabled)||n.storage.storageManager||(n.storage.storageManager=yield ul(n.storage))}))}(e),yield function(e){return f(this,void 0,void 0,(function*(){const{options:t}=e,n=/^(http|https):\/\/[^\s]*$/;if(t.dappMetadata){t.dappMetadata.iconUrl&&!n.test(t.dappMetadata.iconUrl)&&(console.warn("Invalid dappMetadata.iconUrl: URL must start with http:// or https://"),t.dappMetadata.iconUrl=void 0),t.dappMetadata.base64Icon&&t.dappMetadata.base64Icon.length>ql&&(console.warn("Invalid dappMetadata.base64Icon: Base64-encoded icon string length must be less than 163400 characters"),t.dappMetadata.base64Icon=void 0),t.dappMetadata.url&&!n.test(t.dappMetadata.url)&&console.warn("Invalid dappMetadata.url: URL must start with http:// or https://");const e=Fl();if(e&&!t.dappMetadata.iconUrl&&!t.dappMetadata.base64Icon){const n=`${window.location.protocol}//${window.location.host}${e}`;t.dappMetadata.iconUrl=n}}e.dappMetadata=t.dappMetadata}))}(e),yield Yl(e),yield Zl(e);const{metamaskBrowserExtension:y,preferExtension:v,shouldReturn:b}=yield function(e){var t,n,r,i;return f(this,void 0,void 0,(function*(){const{options:o}=e;let s,a=!1,c=!1;if("undefined"!=typeof window&&window.ethereum&&!(null===(t=e.platformManager)||void 0===t?void 0:t.isMetaMaskMobileWebView())){a="extension"===localStorage.getItem(kl);try{s=yield Gl({mustBeMetaMask:!0,sdkInstance:e}),window.extension=s,s.on(xl.CHAIN_CHANGED,(t=>{qc(`[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE chainChanged chainId=${t}`),Boolean(e.sdkProvider)&&e.getMobileProvider().emit(xl.CHAIN_CHANGED,t)})),s.on(xl.ACCOUNTS_CHANGED,(t=>f(this,void 0,void 0,(function*(){var n;qc(`[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE accountsChanged accounts=${t}`);const r=Boolean(e.sdkProvider),i=Boolean(e.extensionActive);if(r&&e.getMobileProvider().emit(xl.ACCOUNTS_CHANGED,t),i&&0===(null==t?void 0:t.length)&&0===(yield null===(n=e.getProvider())||void 0===n?void 0:n.request({method:El.WALLET_GETPERMISSIONS,params:[]})).length)try{yield e.terminate()}catch(e){qc("[MetaMaskSDK: setupExtensionPreferences()] error terminating on permissions revoked",e)}})))),s.on(xl.DISCONNECT,(t=>{qc(`[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE disconnect error=${t}`),Boolean(e.sdkProvider)&&e.getMobileProvider().emit(xl.DISCONNECT,t)})),s.on(xl.CONNECT,(t=>{qc(`[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE connect args=${t}`),Boolean(e.sdkProvider)&&e.getMobileProvider().emit(xl.CONNECT,t)})),s.on(xl.CONNECTED,(t=>{qc("[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE connected",t),Boolean(e.sdkProvider)&&e.getMobileProvider().emit(xl.CONNECTED,t)}))}catch(e){window.extension=void 0}}else(null===(n=e.platformManager)||void 0===n?void 0:n.isMetaMaskMobileWebView())&&(null===(r=e.analytics)||void 0===r||r.send({event:Ii.SDK_USE_INAPP_BROWSER}),e.activeProvider=Wl({provider:window.ethereum,sdkInstance:e}),e._initialized=!0,c=!0);return s&&o.extensionOnly&&(qc("[MetaMaskSDK: setupExtensionPreferences()] EXTENSION ONLY --- prevent sdk initialization"),null===(i=e.analytics)||void 0===i||i.send({event:Ii.SDK_USE_EXTENSION}),e.activeProvider=s,e.extensionActive=!0,e.extension=s,e._initialized=!0,c=!0),{preferExtension:a,shouldReturn:c,metamaskBrowserExtension:s}}))}(e);if(b)qc("[MetaMaskSDK: performSDKInitialization()] shouldReturn=true --- prevent sdk initialization");else{yield function(e,t){var n,r,i,o,s;return f(this,void 0,void 0,(function*(){const{options:a}=e,c=Object.assign({},a.logging);e.remoteConnection=new Ed({preferDesktop:null!==(n=a.preferDesktop)&&void 0!==n&&n,communicationLayerPreference:null!==(r=a.communicationLayerPreference)&&void 0!==r?r:Fi.SOCKET,analytics:e.analytics,dappMetadata:a.dappMetadata,_source:a._source,enableAnalytics:null===(i=a.enableAnalytics)||void 0===i||i,timer:a.timer,sdk:e,platformManager:e.platformManager,transports:a.transports,communicationServerUrl:a.communicationServerUrl,storage:null!==(o=a.storage)&&void 0!==o?o:{enabled:!0},getMetaMaskInstaller:()=>{if(!e.installer)throw new Error("Invalid SDK status -- installer not initialized");return e.installer},logging:c,connectWithExtensionProvider:void 0===t?void 0:()=>Tl(e),modals:Object.assign(Object.assign({},a.modals),{onPendingModalDisconnect:e.terminate.bind(e)})}),yield e.remoteConnection.initRemoteCommunication({sdkInstance:e}),e.installer=new hd({remote:e.remoteConnection,preferDesktop:null!==(s=a.preferDesktop)&&void 0!==s&&s,platformManager:e.platformManager,debug:e.debug})}))}(e,y),yield $l(e),yield function(e,t){var n,r;return f(this,void 0,void 0,(function*(){const{options:i}=e;t?(qc("[MetaMaskSDK: handleAutoAndExtensionConnections()] preferExtension is detected -- connect with it."),null===(n=e.analytics)||void 0===n||n.send({event:Ii.SDK_EXTENSION_UTILIZED}),Tl(e).catch((e=>{console.warn("Can't connect with MetaMask extension...",e),localStorage.removeItem(kl)}))):i.checkInstallationImmediately&&((null===(r=e.platformManager)||void 0===r?void 0:r.isDesktopWeb())?(qc("[MetaMaskSDK: handleAutoAndExtensionConnections()] checkInstallationImmediately"),e.connect().catch((e=>{qc(`[MetaMaskSDK: handleAutoAndExtensionConnections()] checkInstallationImmediately --- IGNORED --- error on autoconnect _err=${e}`)}))):console.warn("[handleAutoAndExtensionConnections()] checkInstallationImmediately --- IGNORED --- only for web desktop")),e._initialized=!0}))}(e,v);try{yield null===(h=e.remoteConnection)||void 0===h?void 0:h.startConnection({initialCheck:!0})}catch(e){console.error("[MetaMaskSDK: setupRemoteConnectionAndInstaller()] Error while checking installation",e)}e.emit(cl.ProviderUpdate,Rl.INITIALIZED)}}))}class Sd extends o{constructor(e={storage:{enabled:!0},injectProvider:!0,forceInjectProvider:!1,enableAnalytics:!0,shouldShimWeb3:!0,useDeeplink:!0,extensionOnly:!0,headless:!1,dappMetadata:{name:"",url:"",iconUrl:""},_source:ml,i18nOptions:{enabled:!1}}){var n,r,i;super(),this.extensionActive=!1,this._initialized=!1,this.sdkInitPromise=void 0,this.debug=!1,this.readonlyRPCCalls=!1,this.availableLanguages=["en"],t.disable();const o=!0===(null===(n=e.logging)||void 0===n?void 0:n.developerMode);if(((null===(r=e.logging)||void 0===r?void 0:r.sdk)||o)&&t.enable("MM_SDK"),qc("[MetaMaskSDK: constructor()]: begin."),this.setMaxListeners(50),!(null===(i=e.dappMetadata)||void 0===i?void 0:i.url)){if("undefined"==typeof window||"undefined"==typeof document)throw new Error("You must provide dAppMetadata url");e.dappMetadata=Object.assign(Object.assign({},e.dappMetadata),{url:`${window.location.protocol}//${window.location.host}`})}this.options=e,this.options._source||(e._source=ml),this.init().then((()=>{qc("[MetaMaskSDK: constructor()]: initialized successfully."),"undefined"!=typeof window&&(window.mmsdk=this)})).catch((e=>{console.error("[MetaMaskSDK: constructor()] error during initialization",e)}))}init(){return f(this,void 0,void 0,(function*(){return function(e){var t;return f(this,void 0,void 0,(function*(){if("undefined"!=typeof window&&(null===(t=window.mmsdk)||void 0===t?void 0:t.isInitialized()))return qc("[MetaMaskSDK: initializeMetaMaskSDK()] already initialized"),Promise.resolve(window.mmsdk);if(e._initialized)return qc("[MetaMaskSDK: initializeMetaMaskSDK()] already initialized"),e.sdkInitPromise;if(e.sdkInitPromise)return qc("[MetaMaskSDK: initializeMetaMaskSDK()] already initializing"),e.sdkInitPromise;try{e.sdkInitPromise=wd(e),yield e.sdkInitPromise}catch(e){throw console.error(e),e}return e.sdkInitPromise}))}(this)}))}isExtensionActive(){return this.extensionActive}checkExtensionAvailability(){var e;return"undefined"!=typeof window&&Boolean(null===(e=window.ethereum)||void 0===e?void 0:e.isMetaMask)}connect(){return f(this,void 0,void 0,(function*(){return function(e){return f(this,void 0,void 0,(function*(){if(e._initialized||(qc("[MetaMaskSDK: connect()] provider not ready -- wait for init()"),yield e.init()),qc(`[MetaMaskSDK: connect()] isExtensionActive=${e.isExtensionActive()} activeProvider`,e.activeProvider),!e.activeProvider)throw new Error("SDK state invalid -- undefined provider");const t=e.activeProvider.getSelectedAddress();return t?[t]:e.activeProvider.request({method:El.ETH_REQUESTACCOUNTS,params:[]})}))}(this)}))}connectAndSign({msg:e}){return f(this,void 0,void 0,(function*(){return Ol({instance:this,msg:e})}))}connectWith(e){return f(this,void 0,void 0,(function*(){return function({instance:e,rpc:t}){return f(this,void 0,void 0,(function*(){if(e._initialized||(qc("[MetaMaskSDK: connectWith()] provider not ready -- wait for init()"),yield e.init()),qc(`[MetaMaskSDK: connectWith()] method: ${t.method} rpc=${t}`),!e.activeProvider)throw new Error("SDK state invalid -- undefined provider");return e.activeProvider.request({method:El.METAMASK_CONNECTWITH,params:[t]})}))}({instance:this,rpc:e})}))}resume(){return function(e){var t,n,r;return f(this,void 0,void 0,(function*(){if(!(null===(n=null===(t=e.remoteConnection)||void 0===t?void 0:t.getConnector())||void 0===n?void 0:n.isReady()))return qc("[MetaMaskSDK: resume()] channel is not ready -- starting connection"),void(null===(r=e.remoteConnection)||void 0===r||r.startConnection());qc("[MetaMaskSDK: resume()] channel is ready")}))}(this)}disconnect(){return console.warn("MetaMaskSDK.disconnect() is deprecated, use terminate()"),this.terminate()}isAuthorized(){var e;null===(e=this.remoteConnection)||void 0===e||e.isAuthorized()}terminate(){return function(e){var t,n,r;return f(this,void 0,void 0,(function*(){if(!(null===(t=e.platformManager)||void 0===t?void 0:t.isMetaMaskMobileWebView())){if(Pl&&(window.localStorage.removeItem(kl),window.localStorage.removeItem(Al),window.localStorage.removeItem(Ml)),e.extensionActive){try{yield null===(n=e.activeProvider)||void 0===n?void 0:n.request({method:El.WALLET_REVOKEPERMISSIONS,params:[{eth_accounts:{}}]})}catch(e){qc("[MetaMaskSDK: terminate()] error revoking permissions",e)}return e.options.extensionOnly?(e.emit(cl.ProviderUpdate,Rl.TERMINATE),void qc("[MetaMaskSDK: terminate()] extensionOnly --- prevent switching providers")):(e.activeProvider=e.sdkProvider,window.ethereum=e.activeProvider,e.extensionActive=!1,void e.emit(cl.ProviderUpdate,Rl.TERMINATE))}e.emit(cl.ProviderUpdate,Rl.TERMINATE),qc(`[MetaMaskSDK: terminate()] remoteConnection=${e.remoteConnection}`),null===(r=e.remoteConnection)||void 0===r||r.disconnect({terminate:!0,sendMessage:!0})}}))}(this)}isInitialized(){return this._initialized}setReadOnlyRPCCalls(e){this.readonlyRPCCalls=e}hasReadOnlyRPCCalls(){return this.readonlyRPCCalls}getProvider(){if(this.activeProvider)return this.activeProvider;console.warn("MetaMaskSDK: No active provider found")}getMobileProvider(){if(!this.sdkProvider)throw new Error("SDK state invalid -- undefined mobile provider");return this.sdkProvider}getUniversalLink(){var e;const t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getUniversalLink();if(!t)throw new Error("No Universal Link available, please call eth_requestAccounts first.");return t}getChannelId(){var e,t;return null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getChannelConfig())||void 0===t?void 0:t.channelId}getRPCHistory(){var e,t;return null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t?void 0:t.getRPCMethodTracker()}getVersion(){return pl.version}getWalletStatus(){var e,t;return null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t?void 0:t.getConnectionStatus()}_getChannelConfig(){var e;return null===(e=this.remoteConnection)||void 0===e?void 0:e.getChannelConfig()}_ping(){var e,t;null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t||t.ping()}_keyCheck(){var e,t;null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t||t.keyCheck()}_getServiceStatus(){var e,t;return null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t?void 0:t.getServiceStatus()}_getRemoteConnection(){return this.remoteConnection}_getDappMetadata(){return this.dappMetadata}_getKeyInfo(){var e;return null===(e=this.remoteConnection)||void 0===e?void 0:e.getKeyInfo()}_resetKeys(){var e,t;null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t||t.resetKeys()}_getConnection(){return this.remoteConnection}emit(e,t){return super.emit(e,t)}on(e,t){return super.on(e,t)}}class _d{constructor({enabled:e}={enabled:!1}){this.enabled=!1,this.enabled=e}persistChannelConfig(e,t){return f(this,void 0,void 0,(function*(){const n=JSON.stringify(e);qc(`[StorageManagerAS: persistChannelConfig()] context=${t} enabled=${this.enabled}`,e),yield h.setItem(Cl,n)}))}persistAccounts(e,t){return f(this,void 0,void 0,(function*(){qc(`[StorageManagerAS: persistAccounts()] context=${t} enabled=${this.enabled}`,e);try{const t=JSON.stringify(e);yield h.setItem(Ml,t)}catch(e){console.error("[StorageManagerAS: persistAccounts()] Error persisting accounts",e)}}))}getCachedAccounts(){return f(this,void 0,void 0,(function*(){let e;try{return e=yield h.getItem(Ml),e?JSON.parse(e):[]}catch(e){return console.error("[StorageManagerAS: getCachedAccounts()] Error getting cached accounts",e),[]}}))}getCachedChainId(){var e;return f(this,void 0,void 0,(function*(){try{const t=null!==(e=yield h.getItem(Al))&&void 0!==e?e:void 0;return-1!==(null==t?void 0:t.indexOf("0x"))?t:void 0}catch(e){return void console.error("[StorageManagerAS: getCachedChainId()] Error getting cached chainId",e)}}))}persistChainId(e,t){return f(this,void 0,void 0,(function*(){qc(`[StorageManagerAS: persistChainId()] context=${t} enabled=${this.enabled}`,e);try{yield h.setItem(Al,e)}catch(e){console.error("[StorageManagerAS: persistChainId()] Error persisting chainId",e)}}))}getPersistedChannelConfig(e){return f(this,void 0,void 0,(function*(){let t;const{context:n}=e||{};try{if(qc(`[StorageManagerAS: getPersistedChannelConfig()] context=${n} enabled=${this.enabled}`),t=yield h.getItem(Cl),!t)return;const e=JSON.parse(t);return qc(`[StorageManagerAS: getPersistedChannelConfig()] context=${n}  channelConfig`,e),e}catch(e){return void console.error("[StorageManagerAS: getPersistedChannelConfig()] Can't find existing channel config",e)}}))}terminate(){return f(this,void 0,void 0,(function*(){qc(`[StorageManagerAS: terminate()] enabled=${this.enabled}`),yield h.removeItem(Cl),yield h.removeItem(Al),yield h.removeItem(Ml)}))}}var Cd=Object.freeze({__proto__:null,StorageManagerWeb:class{constructor({enabled:e}={enabled:!1}){this.enabled=!1,this.enabled=e}persistChannelConfig(e){return f(this,void 0,void 0,(function*(){const t=JSON.stringify(e);qc(`[StorageManagerWeb: persistChannelConfig()] enabled=${this.enabled}`,e),localStorage.setItem(Cl,t)}))}getPersistedChannelConfig(){return f(this,void 0,void 0,(function*(){let e;try{if(qc(`[StorageManagerWeb: getPersistedChannelConfig()] enabled=${this.enabled}`),e=localStorage.getItem(Cl),qc("[StorageManagerWeb: getPersistedChannelConfig()]",e),!e)return;const t=JSON.parse(e);return qc("[StorageManagerWeb: getPersistedChannelConfig()] channelConfig",t),t}catch(e){return void console.error("[StorageManagerWeb: getPersistedChannelConfig()] Can't find existing channel config",e)}}))}persistAccounts(e){return f(this,void 0,void 0,(function*(){qc(`[StorageManagerWeb: persistAccounts()] enabled=${this.enabled}`,e);const t=JSON.stringify(e);localStorage.setItem(Ml,t)}))}getCachedAccounts(){return f(this,void 0,void 0,(function*(){try{const e=localStorage.getItem(Ml);return e?JSON.parse(e):[]}catch(e){throw console.error("[StorageManagerWeb: getCachedAccounts()] Error reading cached accounts",e),e}}))}persistChainId(e){return f(this,void 0,void 0,(function*(){qc(`[StorageManagerWeb: persistChainId()] enabled=${this.enabled}`,e),localStorage.setItem(Al,e)}))}getCachedChainId(){return f(this,void 0,void 0,(function*(){try{const e=localStorage.getItem(Al);return null!=e?e:void 0}catch(e){throw console.error("[StorageManagerWeb: getCachedChainId()] Error reading cached chainId",e),e}}))}terminate(){return f(this,void 0,void 0,(function*(){qc(`[StorageManagerWeb: terminate()] enabled=${this.enabled}`),localStorage.removeItem(Cl)}))}}});export{Fi as CommunicationLayerPreference,Ci as ConnectionStatus,vi as DEFAULT_SERVER_URL,ki as EventType,xi as MessageType,Sd as MetaMaskSDK,cl as MetaMaskSDKEvent,Rl as PROVIDER_UPDATE_TYPE,qi as PlatformType,hl as SDKProvider,_d as StorageManagerAS,Sd as default};
//# sourceMappingURL=metamask-sdk.js.map
