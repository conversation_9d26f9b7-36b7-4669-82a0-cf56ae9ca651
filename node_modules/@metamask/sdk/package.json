{"name": "@metamask/sdk", "version": "0.32.0", "description": "", "homepage": "https://github.com/MetaMask/metamask-sdk#readme", "bugs": {"url": "https://github.com/MetaMask/metamask-sdk/issues"}, "repository": {"type": "git", "url": "https://github.com/MetaMask/metamask-sdk", "directory": "packages/sdk"}, "main": "dist/node/cjs/metamask-sdk.js", "module": "dist/browser/es/metamask-sdk.js", "browser": "dist/browser/es/metamask-sdk.js", "unpkg": "dist/browser/umd/metamask-sdk.js", "react-native": "dist/react-native/es/metamask-sdk.js", "types": "dist/types/src/index.d.ts", "sideEffects": false, "files": ["/dist"], "scripts": {"build:types": "tsc --project tsconfig.build.json --emitDeclarationOnly --outDir dist/types", "build": "yarn build:types && rollup -c --bundleConfigAsCjs", "build:clean": "yarn clean && yarn build", "build:post-tsc": "echo 'N/A'", "build:pre-tsc": "echo 'N/A'", "typecheck": "tsc --noEmit", "clean": "rimraf ./dist", "size": "node bundle-size && size-limit", "lint": "yarn lint:eslint && yarn lint:misc --check", "lint:changelog": "../../scripts/validate-changelog.sh @metamask/sdk", "lint:eslint": "eslint . --cache --ext js,ts", "lint:fix": "yarn lint:eslint --fix && yarn lint:misc --write", "lint:misc": "prettier '**/*.json' '**/*.md' '!CHANGELOG.md' --ignore-path ../../.gitignore", "prepack": "../../scripts/prepack.sh", "publish:preview": "yarn npm publish --tag preview", "reset": "yarn clean && rimraf ./node_modules/", "test": "jest --testPathIgnorePatterns \"/e2e/\"", "test:coverage": "jest --coverage --testPathIgnorePatterns \"/e2e/\"", "test:e2e": "jest --testPathPattern \"/e2e/\"", "test:ci": "jest --coverage --passWithNoTests --setupFilesAfterEnv ./jest-preload.js --testPathIgnorePatterns \"/e2e/\"", "test:dev": "jest -c ./jest.config.ts --detectOpenHandles  --testPathIgnorePatterns \"/e2e/\"", "watch": "rollup -c -w", "dev": "concurrently \"tsc --watch\" \"rollup -c -w --bundleConfigAsCjs\"", "build:dev": "yarn build:types && NODE_ENV=dev rollup -c --bundleConfigAsCjs"}, "dependencies": {"@babel/runtime": "^7.26.0", "@metamask/onboarding": "^1.0.1", "@metamask/providers": "16.1.0", "@metamask/sdk-communication-layer": "0.32.0", "@metamask/sdk-install-modal-web": "0.32.0", "@paulmillr/qr": "^0.2.1", "bowser": "^2.9.0", "cross-fetch": "^4.0.0", "debug": "^4.3.4", "eciesjs": "^0.4.11", "eth-rpc-errors": "^4.0.3", "eventemitter2": "^6.4.9", "obj-multiplex": "^1.0.0", "pump": "^3.0.0", "readable-stream": "^3.6.2", "socket.io-client": "^4.5.1", "tslib": "^2.6.0", "util": "^0.12.4", "uuid": "^8.3.2"}, "devDependencies": {"@jest/globals": "^29.3.1", "@lavamoat/allow-scripts": "^2.3.1", "@metamask/auto-changelog": "3.1.0", "@metamask/eslint-config": "^6.0.0", "@metamask/eslint-config-nodejs": "^6.0.0", "@metamask/eslint-config-typescript": "^6.0.0", "@react-native-async-storage/async-storage": "^1.19.6", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-replace": "^6.0.1", "@rollup/plugin-terser": "^0.4.1", "@size-limit/preset-big-lib": "^11.0.2", "@types/dom-screen-wake-lock": "^1.0.2", "@types/node": "^20.1.3", "@types/pump": "^1.1.1", "@types/qrcode-terminal": "^0.12.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "concurrently": "^9.1.2", "crypto-browserify": "^3.12.0", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jest": "^24.4.0", "eslint-plugin-jsdoc": "^36.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "https-browserify": "^1.0.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "prettier": "^2.3.0", "process": "^0.11.10", "rimraf": "^4.4.0", "rollup": "^4.26.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-jscc": "^2.0.0", "rollup-plugin-natives": "^0.7.5", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-polyfill-node": "^0.13.0", "rollup-plugin-sizes": "^1.0.6", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-visualizer": "^5.12.0", "size-limit": "^11.0.2", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.3.2", "url": "^0.11.0", "webpack": "^5.0.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "lavamoat": {"allowScripts": {"eciesjs>secp256k1": false, "socket.io-client>engine.io-client>ws>bufferutil": false, "socket.io-client>engine.io-client>ws>utf-8-validate": false, "@metamask/sdk-communication-layer>bufferutil": false, "@metamask/sdk-communication-layer>eciesjs>secp256k1": false, "@metamask/sdk-communication-layer>utf-8-validate": false}}}