{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.5.2", "description": "Construct Merkle Trees and verify proofs", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./MerkleTree": {"import": "./dist/MerkleTree.js", "require": "./dist/MerkleTree.js", "types": "./dist/MerkleTree.d.ts"}, "./UnifiedBinaryTree": {"import": "./dist/UnifiedBinaryTree.js", "require": "./dist/UnifiedBinaryTree.js", "types": "./dist/UnifiedBinaryTree.d.ts"}, "./MerkleRadixTree": {"import": "./dist/MerkleRadixTree.js", "require": "./dist/MerkleRadixTree.js", "types": "./dist/MerkleRadixTree.d.ts"}, "./IncrementalMerkleTree": {"import": "./dist/IncrementalMerkleTree.js", "require": "./dist/IncrementalMerkleTree.js", "types": "./dist/IncrementalMerkleTree.d.ts"}, "./MerkleMountainRange": {"import": "./dist/MerkleMountainRange.js", "require": "./dist/MerkleMountainRange.js", "types": "./dist/MerkleMountainRange.d.ts"}}, "files": ["dist", "merkletree.js"], "scripts": {"test": "tape test/*.js", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc -w", "build": "npm run clean && tsc && npm run build:browser", "build:browser": "browserify -t [ babelify --presets [ @babel/preset-env ] ] dist/index.js | uglifyjs > merkletree.js", "lint": "standardx --fix src/*.ts test/*.js", "lint:example": "standardx --fix example/*.js", "docs": "rimraf docs/ && typedoc --plugin typedoc-plugin-markdown --hideSources --theme markdown --hideGenerator --excludeExternals --excludePrivate --out docs src/", "prepare": "npm run lint && npm run build && npm run build:browser && npm run docs"}, "repository": {"type": "git", "url": "https://github.com/merkletreejs/merkletreejs"}, "keywords": ["merkle", "tree", "trie", "hash", "leaves", "algorithm", "crypto", "bitcoin", "ethereum", "generate", "proof", "structure", "json", "radix", "patricia", "binarytree", "merk<PERSON><PERSON>", "merklemountainrange", "merklesumtree", "incrementalmerkletree", "merklepatriciatree", "merkleradixtree", "merklestatetree", "unifiedbinarytree", "eip7864"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://miguelmota.com/"}, "license": {"type": "MIT", "url": "https://github.com/merkletreejs/merkletreejs/blob/master/LICENSE"}, "bugs": {"url": "https://github.com/merkletreejs/merkletreejs/issues"}, "homepage": "https://github.com/merkletreejs/merkletreejs", "devDependencies": {"@babel/core": "^7.10.2", "@babel/preset-env": "^7.10.2", "@noble/hashes": "^1.7.1", "@types/node": "^11.12.1", "@typescript-eslint/eslint-plugin": "^3.0.2", "@typescript-eslint/parser": "^3.0.2", "babelify": "^10.0.0", "bignumber.js": "^9.0.1", "blake3": "^3.0.0", "browserify": "^16.5.1", "circomlibjs": "^0.1.7", "crypto": "^1.0.1", "ethereum-cryptography": "^1.0.3", "ethereumjs-util": "^7.1.5", "hash-wasm": "^4.12.0", "keccak256": "^1.0.6", "rimraf": "^2.6.3", "sha1": "^1.1.1", "standard": "^14.3.4", "standardx": "^5.0.0", "tape": "^4.9.2", "typedoc": "^0.17.7", "typedoc-plugin-markdown": "^2.3.1", "typescript": "^3.4.1", "web3-utils": "^4.3.3"}, "engines": {"node": ">= 7.6.0"}, "dependencies": {"buffer-reverse": "^1.0.1", "crypto-js": "^4.2.0", "treeify": "^1.1.0"}, "eslintConfig": {"rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "error"}}, "standardx": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint/eslint-plugin"], "globals": ["BigInt"]}, "overrides": {"blake3-wasm": "^3.0.0", "@c4312/blake3-internal": "^3.0.0"}}