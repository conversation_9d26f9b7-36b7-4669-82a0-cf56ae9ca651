/// <reference types="node" />
export declare class Base {
    /**
     * print
     * @desc Prints out a visual representation of the merkle tree.
     * @example
     *```js
     *tree.print()
     *```
     */
    print(): void;
    /**
     * bufferIndexOf
     * @desc Returns the first index of which given buffer is found in array.
     * @param {Buffer[]} haystack - Array of buffers.
     * @param {<PERSON>uffer} needle - Buffer to find.
     * @return {Number} - Index number
     *
     * @example
     * ```js
     *const index = tree.bufferIndexOf(haystack, needle)
     *```
     */
    protected bufferIndexOf(array: Buffer[], element: Buffer, isSorted?: boolean): number;
    /**
     * binarySearch
     * @desc Returns the first index of which given item is found in array using binary search.
     * @param {Buffer[]} array - Array of items.
     * @param {Buffer} element - Item to find.
     * @param {Function} compareFunction
     * @return {Number} - Index number
     *
     * @example
     * ```js
     *const index = MerkleTree.binarySearch(array, element, Buffer.compare)
     *```
     */
    static binarySearch(array: <PERSON>uffer[], element: <PERSON>uffer, compareFunction: (a: unknown, b: unknown) => number): number;
    /**
     * binarySearch
     * @desc Returns the first index of which given item is found in array using binary search.
     * @param {Buffer[]} array - Array of items.
     * @param {Buffer} element - Item to find.
     * @param {Function} compareFunction
     * @return {Number} - Index number
     *
     * @example
     * ```js
     *const index = tree.binarySearch(array, element, Buffer.compare)
     *```
     */
    binarySearch(array: Buffer[], element: Buffer, compareFunction: (a: unknown, b: unknown) => number): number;
    /**
     * linearSearch
     * @desc Returns the first index of which given item is found in array using linear search.
     * @param {Buffer[]} array - Array of items.
     * @param {Buffer} element - Item to find.
     * @param {Function} eqChecker
     * @return {Number} - Index number
     *
     * @example
     * ```js
     *const index = MerkleTree.linearSearch(array, element, (a, b) => a === b)
     *```
     */
    static linearSearch(array: Buffer[], element: Buffer, eqChecker: (a: unknown, b: unknown) => boolean): number;
    /**
     * linearSearch
     * @desc Returns the first index of which given item is found in array using linear search.
     * @param {Buffer[]} array - Array of items.
     * @param {Buffer} element - Item to find.
     * @param {Function} eqChecker
     * @return {Number} - Index number
     *
     * @example
     * ```js
     *const index = tree.linearSearch(array, element, (a, b) => a === b)
     *```
     */
    linearSearch(array: Buffer[], element: Buffer, eqChecker: (a: unknown, b: unknown) => boolean): number;
    /**
     * bufferify
     * @desc Returns a buffer type for the given value.
     * @param {String|Number|Object|Buffer|ArrayBuffer} value
     * @return {Buffer}
     *
     * @example
     * ```js
     *const buf = MerkleTree.bufferify('0x1234')
     *```
     */
    static bufferify(value: any): Buffer;
    /**
     * bufferifyFn
     * @desc Returns a function that will bufferify the return value.
     * @param {Function}
     * @return {Function}
     *
     * @example
     * ```js
     *const fn = MerkleTree.bufferifyFn((value) => sha256(value))
     *```
     */
    static bufferifyFn(f: any): any;
    bigNumberify(value: any): BigInt;
    static bigNumberify(value: any): BigInt;
    /**
     * isHexString
     * @desc Returns true if value is a hex string.
     * @param {String} value
     * @return {Boolean}
     *
     * @example
     * ```js
     *console.log(MerkleTree.isHexString('0x1234'))
     *```
     */
    static isHexString(v: string): boolean;
    /**
     * print
     * @desc Prints out a visual representation of the given merkle tree.
     * @param {Object} tree - Merkle tree instance.
     * @return {String}
     * @example
     *```js
     *MerkleTree.print(tree)
     *```
     */
    static print(tree: any): void;
    /**
     * bufferToHex
     * @desc Returns a hex string with 0x prefix for given buffer.
     * @param {Buffer} value
     * @return {String}
     * @example
     *```js
     *const hexStr = tree.bufferToHex(Buffer.from('A'))
     *```
     */
    bufferToHex(value: Buffer, withPrefix?: boolean): string;
    /**
     * bufferToHex
     * @desc Returns a hex string with 0x prefix for given buffer.
     * @param {Buffer} value
     * @return {String}
     * @example
     *```js
     *const hexStr = MerkleTree.bufferToHex(Buffer.from('A'))
     *```
     */
    static bufferToHex(value: Buffer, withPrefix?: boolean): string;
    /**
     * bufferify
     * @desc Returns a buffer type for the given value.
     * @param {String|Number|Object|Buffer} value
     * @return {Buffer}
     *
     * @example
     * ```js
     *const buf = tree.bufferify('0x1234')
     *```
     */
    bufferify(value: any): Buffer;
    /**
     * bufferifyFn
     * @desc Returns a function that will bufferify the return value.
     * @param {Function}
     * @return {Function}
     *
     * @example
     * ```js
     *const fn = tree.bufferifyFn((value) => sha256(value))
     *```
     */
    bufferifyFn(f: any): any;
    /**
     * isHexString
     * @desc Returns true if value is a hex string.
     * @param {String} value
     * @return {Boolean}
     *
     * @example
     * ```js
     *console.log(MerkleTree.isHexString('0x1234'))
     *```
     */
    protected isHexString(value: string): boolean;
    /**
     * log2
     * @desc Returns the log2 of number.
     * @param {Number} value
     * @return {Number}
     */
    protected log2(n: number): number;
    /**
     * zip
     * @desc Returns true if value is a hex string.
     * @param {String[]|Number[]|Buffer[]} a - first array
     * @param {String[]|Number[]|Buffer[]} b -  second array
     * @return {String[][]|Number[][]|Buffer[][]}
     *
     * @example
     * ```js
     *const zipped = tree.zip(['a', 'b'],['A', 'B'])
     *console.log(zipped) // [ [ 'a', 'A' ], [ 'b', 'B' ] ]
     *```
     */
    protected zip(a: any[], b: any[]): any[][];
    static hexZeroPad(hexStr: string, length: number): string;
    bufferArrayIncludes(bufferArray: Buffer[], targetBuffer: Buffer): boolean;
}
export default Base;
