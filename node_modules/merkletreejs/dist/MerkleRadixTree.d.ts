/// <reference types="node" />
import Base from './Base';
declare type TValue = Buffer | BigInt | string | number | null | undefined;
declare type THashFn = (value: TValue) => Buffer;
declare type ProofItem = {
    key: string;
    hash: Buffer;
    siblings: {
        key: string;
        hash: <PERSON><PERSON><PERSON>;
    }[];
};
declare class MerkleRadixNode {
    key: string;
    value: TValue;
    children: Map<string, MerkleRadixNode>;
    hash: Buffer;
    hashFn: THashFn;
    constructor(key: string, value: any, hashFn: THashFn);
    computeHash(): Buffer;
    updateHash(): void;
}
export declare class MerkleRadixTree extends Base {
    root: MerkleRadixNode;
    hashFn: THashFn;
    constructor(hashFn: THashFn);
    insert(key: string, value: TValue): void;
    lookup(key: string): TValue;
    private commonPrefixLength;
    generateProof(key: string): any[];
    verifyProof(proof: ProofItem[], rootHash: Buffer): boolean;
}
export {};
