<!DOCTYPE html>
<html>
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">

      <script src="../treeify.js"></script>
      <script>
         // when a loader isn't present the UMD falls back to using a window 'global'
         window.require = function() {
            return window.treeify;
         };
      </script>
      <script src="./eukaryotes.js"></script>

      <style>
         body {
            display: block;
            width: 630px;
            margin: auto;
            font-family: sans-serif;
         }
      </style>
   </head>
   <body>
      <h1>Eukaryotes</h1>
      <pre id="pre"></pre>
      <script>
         document.getElementById('pre').innerText = treeify.asTree(Eukaryotes, true);
      </script>
   </body>
</html>
