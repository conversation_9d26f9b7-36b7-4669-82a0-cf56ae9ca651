{"name": "hardhat", "version": "2.25.0", "author": "Nomic Labs LLC", "license": "MIT", "homepage": "https://hardhat.org", "repository": "github:nomiclabs/hardhat", "main": "internal/lib/hardhat-lib.js", "types": "internal/lib/hardhat-lib.d.ts", "description": "Hardhat is an extensible developer tool that helps smart contract developers increase productivity by reliably bringing together the tools they want.", "keywords": ["ethereum", "smart-contracts", "hardhat", "blockchain", "dapps", "javascript", "tooling", "task-runner", "solidity"], "bin": {"hardhat": "internal/cli/bootstrap.js"}, "files": ["builtin-tasks/", "common/", "internal/", "sample-projects/", "src/", "types/", "utils/", "*.d.ts", "*.d.ts.map", "*.js", "*.js.map", "LICENSE", "README.md", "recommended-gitignore.txt", "console.sol"], "devDependencies": {"@types/async-eventemitter": "^0.2.1", "@types/bn.js": "^5.1.0", "@types/chai": "^4.2.0", "@types/chai-as-promised": "^7.1.3", "@types/ci-info": "^2.0.0", "@types/debug": "^4.1.4", "@types/fs-extra": "^5.1.0", "@types/keccak": "^3.0.1", "@types/lodash": "^4.14.123", "@types/mocha": ">=9.1.0", "@types/node": "^18.0.0", "@types/resolve": "^1.17.1", "@types/semver": "^6.0.2", "@types/sinon": "^9.0.8", "@types/uuid": "^8.3.1", "@types/ws": "^7.2.1", "@typescript-eslint/eslint-plugin": "5.61.0", "@typescript-eslint/parser": "5.61.0", "bignumber.js": "^9.0.2", "bn.js": "^5.1.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^8.44.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-mocha": "10.4.1", "eslint-plugin-prettier": "3.4.0", "ethers": "^6.14.0", "ethers-v5": "npm:ethers@5", "mocha": "^10.0.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "sinon": "^9.0.0", "time-require": "^0.1.2", "ts-node": "^10.8.0", "typescript": "~5.0.0", "@nomicfoundation/eslint-plugin-hardhat-internal-rules": "^1.0.2", "@nomicfoundation/eslint-plugin-slow-imports": "^1.0.0"}, "dependencies": {"@ethereumjs/util": "^9.1.0", "@ethersproject/abi": "^5.1.2", "@nomicfoundation/edr": "^0.11.1", "@nomicfoundation/solidity-analyzer": "^0.1.0", "@sentry/node": "^5.18.1", "@types/bn.js": "^5.1.0", "@types/lru-cache": "^5.1.0", "adm-zip": "^0.4.16", "aggregate-error": "^3.0.0", "ansi-escapes": "^4.3.0", "boxen": "^5.1.2", "chokidar": "^4.0.0", "ci-info": "^2.0.0", "debug": "^4.1.1", "enquirer": "^2.3.0", "env-paths": "^2.2.0", "ethereum-cryptography": "^1.0.3", "find-up": "^5.0.0", "fp-ts": "1.19.3", "fs-extra": "^7.0.1", "immutable": "^4.0.0-rc.12", "io-ts": "1.10.4", "json-stream-stringify": "^3.1.4", "keccak": "^3.0.2", "lodash": "^4.17.11", "micro-eth-signer": "^0.14.0", "mnemonist": "^0.38.0", "mocha": "^10.0.0", "p-map": "^4.0.0", "picocolors": "^1.1.0", "raw-body": "^2.4.1", "resolve": "1.17.0", "semver": "^6.3.0", "solc": "0.8.26", "source-map-support": "^0.5.13", "stacktrace-parser": "^0.1.10", "tinyglobby": "^0.2.6", "tsort": "0.0.1", "undici": "^5.14.0", "uuid": "^8.3.2", "ws": "^7.4.6"}, "peerDependencies": {"ts-node": "*", "typescript": "*"}, "peerDependenciesMeta": {"ts-node": {"optional": true}, "typescript": {"optional": true}}, "nyc": {"extension": [".ts"], "exclude": ["**/*.d.ts", "dev-build", "sample-projects", "test", "coverage", ".nyc_output", "*.ts", "*.js", "internal", "builtin-tasks", "build-test"], "reporter": ["text", "lcovonly"], "all": true}, "scripts": {"lint": "pnpm prettier --check && pnpm eslint", "lint:fix": "pnpm prettier --write && pnpm eslint --fix", "eslint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "prettier": "prettier \"**/*.{js,md,json}\"", "pretest": "cd ../.. && pnpm build", "test": "mocha --recursive \"test/**/*.ts\"", "test:except-provider": "mocha --recursive \"test/**/*.ts\" --exclude \"test/internal/hardhat-network/provider/**/*.ts\"", "test:except-tracing": "mocha --recursive \"test/**/*.ts\" --exclude \"test/internal/hardhat-network/stack-traces/**/*.ts\"", "test:provider": "mocha --recursive \"test/internal/hardhat-network/provider/**/*.ts\"", "test:tracing": "mocha --recursive \"test/internal/hardhat-network/stack-traces/**/*.ts\"", "postbuild": "cp src/internal/solidity/compiler/solcjs-runner.js internal/solidity/compiler/solcjs-runner.js", "build": "tsc --build --force --incremental .", "build:tracing": "tsc --build --force --incremental .", "clean": "rimraf builtin-tasks internal types utils *.d.ts *.map *.js build-test tsconfig.tsbuildinfo test/internal/hardhat-network/stack-traces/test-files/artifacts"}}