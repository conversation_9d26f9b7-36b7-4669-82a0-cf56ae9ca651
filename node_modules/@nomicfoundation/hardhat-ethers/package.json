{"name": "@nomicfoundation/hardhat-ethers", "version": "3.0.9", "description": "Hardhat plugin for ethers", "homepage": "https://github.com/nomicfoundation/hardhat/tree/main/packages/hardhat-ethers", "repository": "github:nomicfoundation/hardhat", "author": "Nomic Foundation", "license": "MIT", "main": "internal/index.js", "types": "internal/index.d.ts", "keywords": ["ethereum", "smart-contracts", "hardhat", "hardhat-plugin", "ethers.js"], "files": ["dist/src/", "src/", "internal/", "types/", "*.d.ts", "*.d.ts.map", "*.js", "*.js.map", "LICENSE", "README.md"], "dependencies": {"debug": "^4.1.1", "lodash.isequal": "^4.5.0"}, "devDependencies": {"@types/chai": "^4.2.0", "@types/chai-as-promised": "^7.1.3", "@types/debug": "^4.1.4", "@types/lodash.isequal": "^4.5.6", "@types/mocha": ">=9.1.0", "@types/node": "^18.0.0", "@types/sinon": "^9.0.8", "@typescript-eslint/eslint-plugin": "5.61.0", "@typescript-eslint/parser": "5.61.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^8.44.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-mocha": "10.4.1", "eslint-plugin-prettier": "3.4.0", "ethers": "^6.14.0", "hardhat": "^2.0.0", "mocha": "^10.0.0", "picocolors": "^1.1.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "sinon": "^9.0.0", "ts-node": "^10.8.0", "typescript": "~5.0.0", "@nomicfoundation/eslint-plugin-slow-imports": "^1.0.0", "@nomicfoundation/eslint-plugin-hardhat-internal-rules": "^1.0.2"}, "peerDependencies": {"ethers": "^6.14.0", "hardhat": "^2.0.0"}, "scripts": {"lint": "pnpm prettier --check && pnpm eslint", "lint:fix": "pnpm prettier --write && pnpm eslint --fix", "eslint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "prettier": "prettier \"**/*.{js,md,json}\"", "pretest": "cd ../.. && pnpm build", "test": "mocha --recursive \"test/**/*.ts\" --exit", "build": "tsc --build .", "clean": "rimraf dist internal types *.{d.ts,js}{,.map} build-test tsconfig.tsbuildinfo"}}