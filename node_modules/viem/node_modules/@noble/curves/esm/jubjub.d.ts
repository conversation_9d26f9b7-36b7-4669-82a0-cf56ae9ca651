/**
 * @deprecated
 * @module
 */
import { jubjub_findGroupHash, jubjub_groupHash, jubjub as jubjubn } from './misc.ts';
/** @deprecated Use `@noble/curves/misc` module directly. */
export declare const jubjub: typeof jubjubn;
/** @deprecated Use `@noble/curves/misc` module directly. */
export declare const findGroupHash: typeof jubjub_findGroupHash;
/** @deprecated Use `@noble/curves/misc` module directly. */
export declare const groupHash: typeof jubjub_groupHash;
//# sourceMappingURL=jubjub.d.ts.map