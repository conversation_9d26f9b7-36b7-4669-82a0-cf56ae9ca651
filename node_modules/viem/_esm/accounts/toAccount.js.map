{"version": 3, "file": "toAccount.js", "sourceRoot": "", "sources": ["../../accounts/toAccount.ts"], "names": [], "mappings": "AAAA,oHAAoH;AAIpH,OAAO,EACL,mBAAmB,GAEpB,MAAM,sBAAsB,CAAA;AAC7B,OAAO,EAEL,SAAS,GACV,MAAM,+BAA+B,CAAA;AAmBtC;;;;GAIG;AACH,MAAM,UAAU,SAAS,CACvB,MAAqB;IAErB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YACvC,MAAM,IAAI,mBAAmB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAA;QACpD,OAAO;YACL,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,UAAU;SACsB,CAAA;IAC1C,CAAC;IAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;QAC/C,MAAM,IAAI,mBAAmB,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,CAAA;IAC5D,OAAO;QACL,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,YAAY,EAAE,MAAM,CAAC,YAAY;QACjC,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;QAC3C,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,eAAe,EAAE,MAAM,CAAC,eAAe;QACvC,aAAa,EAAE,MAAM,CAAC,aAAa;QACnC,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,OAAO;KACyB,CAAA;AAC1C,CAAC"}