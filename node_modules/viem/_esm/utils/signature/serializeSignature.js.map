{"version": 3, "file": "serializeSignature.js", "sourceRoot": "", "sources": ["../../../utils/signature/serializeSignature.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAA;AAInD,OAAO,EAA6B,WAAW,EAAE,MAAM,wBAAwB,CAAA;AAC/E,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAA;AAkBnD;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,kBAAkB,CAAwB,EACxD,CAAC,EACD,CAAC,EACD,EAAE,GAAG,KAAK,EACV,CAAC,EACD,OAAO,GAC0B;IACjC,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE;QACrB,IAAI,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC;YAAE,OAAO,OAAO,CAAA;QAClD,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC;YAAE,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAC3E,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;IACnD,CAAC,CAAC,EAAE,CAAA;IACJ,MAAM,SAAS,GAAG,KAAK,IAAI,SAAS,CAAC,SAAS,CAC5C,WAAW,CAAC,CAAC,CAAC,EACd,WAAW,CAAC,CAAC,CAAC,CACf,CAAC,YAAY,EAAE,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAW,CAAA;IAE1D,IAAI,EAAE,KAAK,KAAK;QAAE,OAAO,SAA6C,CAAA;IACtE,OAAO,UAAU,CAAC,SAAS,CAAqC,CAAA;AAClE,CAAC"}