import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const bscTestnet = /*#__PURE__*/ defineChain({
    id: 97,
    name: 'BNB Smart Chain Testnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON><PERSON>',
        symbol: 'tBNB',
    },
    rpcUrls: {
        default: { http: ['https://data-seed-prebsc-1-s1.bnbchain.org:8545'] },
    },
    blockExplorers: {
        default: {
            name: 'BscScan',
            url: 'https://testnet.bscscan.com',
            apiUrl: 'https://api-testnet.bscscan.com/api',
        },
    },
    contracts: {
        multicall3: {
            address: '0xca11bde05977b3631167028862be2a173976ca11',
            blockCreated: 17422483,
        },
    },
    testnet: true,
});
//# sourceMappingURL=bscTestnet.js.map