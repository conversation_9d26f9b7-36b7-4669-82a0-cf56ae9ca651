import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const etherlinkTestnet = /*#__PURE__*/ defineChain({
  id: 128123,
  name: 'Etherlink Testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'XTZ',
  },
  rpcUrls: {
    default: { http: ['https://node.ghostnet.etherlink.com'] },
  },
  blockExplorers: {
    default: {
      name: 'Etherlink Testnet',
      url: 'https://testnet.explorer.etherlink.com',
    },
  },
  testnet: true,
})
