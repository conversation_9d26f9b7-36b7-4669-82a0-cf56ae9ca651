import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 1 // mainnet

export const blast = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 81457,
  name: 'Blast',
  nativeCurrency: {
    decimals: 18,
    name: 'Ether',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: { http: ['https://rpc.blast.io'] },
  },
  blockExplorers: {
    default: {
      name: 'Blastscan',
      url: 'https://blastscan.io',
      apiUrl: 'https://api.blastscan.io/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    multicall3: {
      address: '******************************************',
      blockCreated: 212929,
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 19300358,
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 19300357,
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 19300360,
      },
    },
  },
  sourceId,
})
