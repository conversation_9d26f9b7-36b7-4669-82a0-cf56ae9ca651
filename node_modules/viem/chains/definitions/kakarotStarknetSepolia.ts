import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const kakarotStarknetSepolia = /*#__PURE__*/ define<PERSON>hain({
  id: 920637907288165,
  name: 'Kakarot Starknet Sepolia',
  nativeCurrency: {
    name: '<PERSON><PERSON>',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://sepolia-rpc.kakarot.org'],
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON>kar<PERSON>an',
      url: 'https://sepolia.kakarotscan.org',
    },
  },
  testnet: true,
})
