import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const lyra = /*#__PURE__*/ defineChain({
  id: 957,
  name: 'Lyra Chain',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.lyra.finance'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Lyra Explorer',
      url: 'https://explorer.lyra.finance',
      apiUrl: 'https://explorer.lyra.finance/api/v2',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 1935198,
    },
  },
})
