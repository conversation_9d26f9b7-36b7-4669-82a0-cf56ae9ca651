import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const saigon = /*#__PURE__*/ defineChain({
  id: 2021,
  name: 'Saigon Testnet',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: '<PERSON><PERSON>', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://saigon-testnet.roninchain.com/rpc'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Saigon Explorer',
      url: 'https://saigon-app.roninchain.com',
    },
  },
  contracts: {
    multicall3: {
      address: '0xca11bde05977b3631167028862be2a173976ca11',
      blockCreated: 18736871,
    },
  },
  testnet: true,
})
