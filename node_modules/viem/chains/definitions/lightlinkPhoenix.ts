import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const lightlinkPhoenix = /*#__PURE__*/ define<PERSON>hain({
  id: 1_890,
  name: 'LightLink Phoenix Mainnet',
  network: 'lightlink-phoenix',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: {
      http: ['https://replicator.phoenix.lightlink.io/rpc/v1'],
    },
  },
  blockExplorers: {
    default: {
      name: 'LightLink Phoenix Explorer',
      url: 'https://phoenix.lightlink.io',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 125_499_184,
    },
  },
  testnet: false,
})
