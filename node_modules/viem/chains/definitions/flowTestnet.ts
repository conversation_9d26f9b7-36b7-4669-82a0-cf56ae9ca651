import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const flowTestnet = /*#__PURE__*/ defineChain({
  id: 545,
  name: 'Flow EVM Testnet',
  nativeCurrency: {
    decimals: 18,
    name: 'Flow',
    symbol: 'FLOW',
  },
  rpcUrls: {
    default: {
      http: ['https://testnet.evm.nodes.onflow.org'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Flow Diver',
      url: 'https://evm-testnet.flowscan.io',
    },
  },
  contracts: {
    multicall3: {
      address: '0xca11bde05977b3631167028862be2a173976ca11',
      blockCreated: 137518,
    },
  },
  testnet: true,
  blockTime: 800,
})
