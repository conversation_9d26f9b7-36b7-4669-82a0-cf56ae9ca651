{"version": 3, "file": "cursor.d.ts", "sourceRoot": "", "sources": ["../../utils/cursor.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,KAAK,uBAAuB,EAE5B,KAAK,4BAA4B,EAEjC,KAAK,mCAAmC,EACzC,MAAM,qBAAqB,CAAA;AAC5B,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAA;AACnD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAA;AAEjD,MAAM,MAAM,MAAM,GAAG;IACnB,KAAK,EAAE,SAAS,CAAA;IAChB,QAAQ,EAAE,QAAQ,CAAA;IAClB,QAAQ,EAAE,MAAM,CAAA;IAChB,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IACtC,kBAAkB,EAAE,MAAM,CAAA;IAC1B,kBAAkB,EAAE,MAAM,CAAA;IAC1B,SAAS,EAAE,MAAM,CAAA;IACjB,eAAe,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACxC,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAA;IACtC,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAA;IACvC,YAAY,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACvC,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAA;IACvC,WAAW,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;IACjD,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC1D,YAAY,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACvC,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACxC,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACxC,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;IACxC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA;IACvC,SAAS,CAAC,KAAK,EAAE,SAAS,GAAG,IAAI,CAAA;IACjC,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAA;IAC9B,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAA;IAC/B,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAA;IAC/B,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAA;IAC/B,QAAQ,IAAI,SAAS,CAAC,MAAM,CAAC,CAAA;IAC7B,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACnD,SAAS,IAAI,MAAM,CAAA;IACnB,UAAU,IAAI,MAAM,CAAA;IACpB,UAAU,IAAI,MAAM,CAAA;IACpB,UAAU,IAAI,MAAM,CAAA;IACpB,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,IAAI,CAAA;IACzC,MAAM,IAAI,IAAI,CAAA;CACf,CAAA;AAED,KAAK,eAAe,GAChB,6BAA6B,GAC7B,gCAAgC,GAChC,gCAAgC,GAChC,SAAS,CAAA;AAEb,KAAK,6BAA6B,GAAG,4BAA4B,GAAG,SAAS,CAAA;AAE7E,KAAK,gCAAgC,GAAG,uBAAuB,GAAG,SAAS,CAAA;AAE3E,KAAK,gCAAgC,GAAG,uBAAuB,GAAG,SAAS,CAAA;AAE3E,KAAK,qBAAqB,GACtB,uBAAuB,GACvB,mCAAmC,CAAA;AAiKvC,KAAK,YAAY,GAAG;IAAE,kBAAkB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAAE,CAAA;AAE/D,MAAM,MAAM,qBAAqB,GAC7B,eAAe,GACf,qBAAqB,GACrB,SAAS,CAAA;AAEb,wBAAgB,YAAY,CAC1B,KAAK,EAAE,SAAS,EAChB,EAAE,kBAA0B,EAAE,GAAE,YAAiB,GAChD,MAAM,CAWR"}