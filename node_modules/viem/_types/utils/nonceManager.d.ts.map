{"version": 3, "file": "nonceManager.d.ts", "sourceRoot": "", "sources": ["../../utils/nonceManager.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,SAAS,CAAA;AAGtC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,4BAA4B,CAAA;AACxD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAGrD,MAAM,MAAM,4BAA4B,GAAG;IACzC,MAAM,EAAE,kBAAkB,CAAA;CAC3B,CAAA;AAED,KAAK,kBAAkB,GAAG;IACxB,OAAO,EAAE,OAAO,CAAA;IAChB,OAAO,EAAE,MAAM,CAAA;CAChB,CAAA;AAED,MAAM,MAAM,YAAY,GAAG;IACzB,iCAAiC;IACjC,OAAO,EAAE,CACP,UAAU,EAAE,kBAAkB,GAAG;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,KAChD,OAAO,CAAC,MAAM,CAAC,CAAA;IACpB,yBAAyB;IACzB,SAAS,EAAE,CAAC,OAAO,EAAE,kBAAkB,KAAK,IAAI,CAAA;IAChD,mBAAmB;IACnB,GAAG,EAAE,CAAC,OAAO,EAAE,kBAAkB,GAAG;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA;IAC1E,qBAAqB;IACrB,KAAK,EAAE,CAAC,OAAO,EAAE,kBAAkB,KAAK,IAAI,CAAA;CAC7C,CAAA;AAED;;;;;;;;;;;GAWG;AACH,wBAAgB,kBAAkB,CAChC,UAAU,EAAE,4BAA4B,GACvC,YAAY,CAyDd;AAKD,MAAM,MAAM,kBAAkB,GAAG;IAC/B,mBAAmB;IACnB,GAAG,CAAC,UAAU,EAAE,kBAAkB,GAAG;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;IAC9E,mBAAmB;IACnB,GAAG,CAAC,UAAU,EAAE,kBAAkB,EAAE,KAAK,EAAE,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;CACvE,CAAA;AAED,2CAA2C;AAC3C,wBAAgB,OAAO,IAAI,kBAAkB,CAW5C;AAKD,oDAAoD;AACpD,eAAO,MAAM,YAAY,cAEvB,CAAA"}