import { type NegativeOffsetErrorType, type PositionOutOfBoundsErrorType, type RecursiveReadLimitExceededErrorType } from '../errors/cursor.js';
import type { ErrorType } from '../errors/utils.js';
import type { ByteArray } from '../types/misc.js';
export type Cursor = {
    bytes: ByteArray;
    dataView: DataView;
    position: number;
    positionReadCount: Map<number, number>;
    recursiveReadCount: number;
    recursiveReadLimit: number;
    remaining: number;
    assertReadLimit(position?: number): void;
    assertPosition(position: number): void;
    decrementPosition(offset: number): void;
    getReadCount(position?: number): number;
    incrementPosition(offset: number): void;
    inspectByte(position?: number): ByteArray[number];
    inspectBytes(length: number, position?: number): ByteArray;
    inspectUint8(position?: number): number;
    inspectUint16(position?: number): number;
    inspectUint24(position?: number): number;
    inspectUint32(position?: number): number;
    pushByte(byte: ByteArray[number]): void;
    pushBytes(bytes: ByteArray): void;
    pushUint8(value: number): void;
    pushUint16(value: number): void;
    pushUint24(value: number): void;
    pushUint32(value: number): void;
    readByte(): ByteArray[number];
    readBytes(length: number, size?: number): ByteArray;
    readUint8(): number;
    readUint16(): number;
    readUint24(): number;
    readUint32(): number;
    setPosition(position: number): () => void;
    _touch(): void;
};
type CursorErrorType = CursorAssertPositionErrorType | CursorDecrementPositionErrorType | CursorIncrementPositionErrorType | ErrorType;
type CursorAssertPositionErrorType = PositionOutOfBoundsErrorType | ErrorType;
type CursorDecrementPositionErrorType = NegativeOffsetErrorType | ErrorType;
type CursorIncrementPositionErrorType = NegativeOffsetErrorType | ErrorType;
type StaticCursorErrorType = NegativeOffsetErrorType | RecursiveReadLimitExceededErrorType;
type CursorConfig = {
    recursiveReadLimit?: number | undefined;
};
export type CreateCursorErrorType = CursorErrorType | StaticCursorErrorType | ErrorType;
export declare function createCursor(bytes: ByteArray, { recursiveReadLimit }?: CursorConfig): Cursor;
export {};
//# sourceMappingURL=cursor.d.ts.map