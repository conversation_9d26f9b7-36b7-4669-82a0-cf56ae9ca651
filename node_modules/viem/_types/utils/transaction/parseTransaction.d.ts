import { type InvalidAddressErrorType } from '../../errors/address.js';
import { type InvalidLegacyVErrorType, type InvalidSerializedTransactionErrorType } from '../../errors/transaction.js';
import type { ErrorType } from '../../errors/utils.js';
import type { Hex } from '../../types/misc.js';
import type { AccessList, TransactionSerializable, TransactionSerializableEIP1559, TransactionSerializableEIP2930, TransactionSerializableEIP4844, TransactionSerializableEIP7702, TransactionSerializableLegacy, TransactionSerialized, TransactionSerializedGeneric, TransactionType } from '../../types/transaction.js';
import type { IsNarrowable } from '../../types/utils.js';
import { type IsAddressErrorType } from '../address/isAddress.js';
import { type IsHexErrorType } from '../data/isHex.js';
import { type PadHexErrorType } from '../data/pad.js';
import { type HexToBigIntErrorType, type HexToNumberErrorType } from '../encoding/fromHex.js';
import { type FromRlpErrorType } from '../encoding/fromRlp.js';
import type { RecursiveArray } from '../encoding/toRlp.js';
import { type AssertTransactionEIP1559ErrorType, type AssertTransactionEIP2930ErrorType, type AssertTransactionEIP4844ErrorType, type AssertTransactionEIP7702ErrorType, type AssertTransactionLegacyErrorType } from './assertTransaction.js';
import { type GetSerializedTransactionType, type GetSerializedTransactionTypeErrorType } from './getSerializedTransactionType.js';
export type ParseTransactionReturnType<serialized extends TransactionSerializedGeneric = TransactionSerialized, type extends TransactionType = GetSerializedTransactionType<serialized>> = IsNarrowable<serialized, Hex> extends true ? (type extends 'eip1559' ? TransactionSerializableEIP1559 : never) | (type extends 'eip2930' ? TransactionSerializableEIP2930 : never) | (type extends 'eip4844' ? TransactionSerializableEIP4844 : never) | (type extends 'eip7702' ? TransactionSerializableEIP7702 : never) | (type extends 'legacy' ? TransactionSerializableLegacy : never) : TransactionSerializable;
export type ParseTransactionErrorType = GetSerializedTransactionTypeErrorType | ParseTransactionEIP1559ErrorType | ParseTransactionEIP2930ErrorType | ParseTransactionEIP4844ErrorType | ParseTransactionEIP7702ErrorType | ParseTransactionLegacyErrorType;
export declare function parseTransaction<const serialized extends TransactionSerializedGeneric>(serializedTransaction: serialized): ParseTransactionReturnType<serialized>;
type ParseTransactionEIP7702ErrorType = ToTransactionArrayErrorType | AssertTransactionEIP7702ErrorType | ToTransactionArrayErrorType | HexToBigIntErrorType | HexToNumberErrorType | InvalidLegacyVErrorType | InvalidSerializedTransactionErrorType | IsHexErrorType | ParseAuthorizationListErrorType | ParseEIP155SignatureErrorType | ErrorType;
type ParseTransactionEIP4844ErrorType = ToTransactionArrayErrorType | AssertTransactionEIP4844ErrorType | ToTransactionArrayErrorType | HexToBigIntErrorType | HexToNumberErrorType | InvalidLegacyVErrorType | InvalidSerializedTransactionErrorType | IsHexErrorType | ParseEIP155SignatureErrorType | ErrorType;
type ParseTransactionEIP1559ErrorType = ToTransactionArrayErrorType | AssertTransactionEIP1559ErrorType | ToTransactionArrayErrorType | HexToBigIntErrorType | HexToNumberErrorType | InvalidLegacyVErrorType | InvalidSerializedTransactionErrorType | IsHexErrorType | ParseEIP155SignatureErrorType | ParseAccessListErrorType | ErrorType;
type ParseTransactionEIP2930ErrorType = ToTransactionArrayErrorType | AssertTransactionEIP2930ErrorType | ToTransactionArrayErrorType | HexToBigIntErrorType | HexToNumberErrorType | InvalidLegacyVErrorType | InvalidSerializedTransactionErrorType | IsHexErrorType | ParseEIP155SignatureErrorType | ParseAccessListErrorType | ErrorType;
type ParseTransactionLegacyErrorType = AssertTransactionLegacyErrorType | FromRlpErrorType | HexToBigIntErrorType | HexToNumberErrorType | InvalidLegacyVErrorType | InvalidSerializedTransactionErrorType | IsHexErrorType | ErrorType;
type ToTransactionArrayErrorType = FromRlpErrorType | ErrorType;
export declare function toTransactionArray(serializedTransaction: string): import("../encoding/fromRlp.js").FromRlpReturnType<"hex">;
type ParseAccessListErrorType = InvalidAddressErrorType | IsAddressErrorType | ErrorType;
export declare function parseAccessList(accessList_: RecursiveArray<Hex>): AccessList;
type ParseAuthorizationListErrorType = HexToNumberErrorType | ParseEIP155SignatureErrorType | ErrorType;
type ParseEIP155SignatureErrorType = HexToBigIntErrorType | PadHexErrorType | ErrorType;
export {};
//# sourceMappingURL=parseTransaction.d.ts.map