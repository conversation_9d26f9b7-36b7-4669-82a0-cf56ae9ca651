{"version": 3, "file": "watchBlockNumber.d.ts", "sourceRoot": "", "sources": ["../../../actions/public/watchBlockNumber.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAA;AAC3D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,6CAA6C,CAAA;AAC5E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAA;AACjD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAA;AAIhE,OAAO,EAAE,KAAK,aAAa,EAAQ,MAAM,qBAAqB,CAAA;AAG9D,OAAO,EACL,KAAK,wBAAwB,EAE9B,MAAM,qBAAqB,CAAA;AAE5B,MAAM,MAAM,sBAAsB,GAAG,wBAAwB,CAAA;AAC7D,MAAM,MAAM,eAAe,GAAG,CAC5B,WAAW,EAAE,sBAAsB,EACnC,eAAe,EAAE,sBAAsB,GAAG,SAAS,KAChD,IAAI,CAAA;AAET,MAAM,MAAM,0BAA0B,CACpC,SAAS,SAAS,SAAS,GAAG,SAAS,IACrC;IACF,gEAAgE;IAChE,aAAa,EAAE,eAAe,CAAA;IAC9B,sFAAsF;IACtF,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,GAAG,SAAS,CAAA;CAC/C,GAAG,CACA,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,SAAS,IAAI,GAC1D;IACE,UAAU,CAAC,EAAE,SAAS,CAAA;IACtB,WAAW,CAAC,EAAE,SAAS,CAAA;IACvB,0GAA0G;IAC1G,IAAI,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;IACxB,eAAe,CAAC,EAAE,SAAS,CAAA;CAC5B,GACD,KAAK,CAAC,GACV;IACE,uEAAuE;IACvE,UAAU,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAChC,kGAAkG;IAClG,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACjC,IAAI,CAAC,EAAE,IAAI,GAAG,SAAS,CAAA;IACvB,8EAA8E;IAC9E,eAAe,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CACrC,CACJ,CAAA;AAED,MAAM,MAAM,0BAA0B,GAAG,MAAM,IAAI,CAAA;AAEnD,MAAM,MAAM,yBAAyB,GAAG,aAAa,GAAG,SAAS,CAAA;AAEjE;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAgB,gBAAgB,CAC9B,KAAK,SAAS,KAAK,GAAG,SAAS,EAC/B,SAAS,SAAS,SAAS,EAE3B,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,EAChC,EACE,WAAmB,EACnB,UAAkB,EAClB,aAAa,EACb,OAAO,EACP,IAAI,EAAE,KAAK,EACX,eAAwC,GACzC,EAAE,0BAA0B,CAAC,SAAS,CAAC,GACvC,0BAA0B,CAwH5B"}