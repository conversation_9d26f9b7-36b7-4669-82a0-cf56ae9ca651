export declare const chainConfig: {
    readonly blockTime: 1000;
    readonly contracts: {
        readonly gasPriceOracle: {
            readonly address: "0x420000000000000000000000000000000000000F";
        };
        readonly l1Block: {
            readonly address: "0x4200000000000000000000000000000000000015";
        };
        readonly l2CrossDomainMessenger: {
            readonly address: "0x4200000000000000000000000000000000000007";
        };
        readonly l2Erc721Bridge: {
            readonly address: "0x4200000000000000000000000000000000000014";
        };
        readonly l2StandardBridge: {
            readonly address: "0x4200000000000000000000000000000000000010";
        };
        readonly l2ToL1MessagePasser: {
            readonly address: "0x4200000000000000000000000000000000000016";
        };
    };
    readonly formatters: {
        readonly block: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloRpcBlock) => {
                baseFeePerGas: bigint | null;
                blobGasUsed: bigint;
                difficulty: bigint;
                excessBlobGas: bigint;
                extraData: import("../index.js").Hex;
                gasLimit: bigint;
                gasUsed: bigint;
                hash: `0x${string}` | null;
                logsBloom: `0x${string}` | null;
                miner: import("abitype").Address;
                mixHash: import("../index.js").Hash;
                nonce: `0x${string}` | null;
                number: bigint | null;
                parentBeaconBlockRoot?: `0x${string}` | undefined;
                parentHash: import("../index.js").Hash;
                receiptsRoot: import("../index.js").Hex;
                sealFields: import("../index.js").Hex[];
                sha3Uncles: import("../index.js").Hash;
                size: bigint;
                stateRoot: import("../index.js").Hash;
                timestamp: bigint;
                totalDifficulty: bigint | null;
                transactions: `0x${string}`[] | import("./types.js").CeloTransaction<boolean>[];
                transactionsRoot: import("../index.js").Hash;
                uncles: import("../index.js").Hash[];
                withdrawals?: import("../index.js").Withdrawal[] | undefined | undefined;
                withdrawalsRoot?: `0x${string}` | undefined;
            } & {};
            type: "block";
        };
        readonly transaction: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloRpcTransaction) => ({
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                v: bigint;
                to: import("abitype").Address | null;
                from: import("abitype").Address;
                gas: bigint;
                nonce: number;
                value: bigint;
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                accessList?: undefined | undefined;
                authorizationList?: undefined | undefined;
                blobVersionedHashes?: undefined | undefined;
                chainId?: number | undefined;
                yParity?: undefined | undefined;
                type: "legacy";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: undefined | undefined;
                maxPriorityFeePerGas?: undefined | undefined;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined | undefined;
                blobVersionedHashes?: undefined | undefined;
                chainId: number;
                type: "eip2930";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: undefined | undefined;
                maxPriorityFeePerGas?: undefined | undefined;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined | undefined;
                blobVersionedHashes?: undefined | undefined;
                chainId: number;
                type: "eip1559";
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined | undefined;
                blobVersionedHashes: readonly import("../index.js").Hex[];
                chainId: number;
                type: "eip4844";
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas: bigint;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList: import("../index.js").SignedAuthorizationList;
                blobVersionedHashes?: undefined | undefined;
                chainId: number;
                type: "eip7702";
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                accessList: import("../index.js").AccessList;
                chainId: number;
                feeCurrency: import("abitype").Address | null;
                gatewayFee: bigint | null;
                gatewayFeeRecipient: import("abitype").Address | null;
                type: "cip42";
                blobVersionedHashes?: undefined;
                authorizationList?: undefined;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                accessList: import("../index.js").AccessList;
                chainId: number;
                feeCurrency: import("abitype").Address | null;
                type: "cip64";
                blobVersionedHashes?: undefined;
                authorizationList?: undefined;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                isSystemTx?: boolean;
                mint?: bigint | undefined | undefined;
                sourceHash: import("../index.js").Hex;
                type: "deposit";
                accessList?: undefined;
                blobVersionedHashes?: undefined;
                authorizationList?: undefined;
                chainId?: undefined;
                feeCurrency?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            }) & {};
            type: "transaction";
        };
        readonly transactionRequest: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloTransactionRequest) => ({
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                type?: "0x0" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: undefined | undefined;
                maxPriorityFeePerGas?: undefined | undefined;
                blobs?: undefined;
                accessList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                authorizationList?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                type?: "0x1" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: undefined | undefined;
                maxPriorityFeePerGas?: undefined | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                authorizationList?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                type?: "0x2" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                authorizationList?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                type?: "0x3" | undefined;
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                to: `0x${string}` | null;
                gasPrice?: undefined | undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                maxFeePerBlobGas: `0x${string}`;
                accessList?: import("../index.js").AccessList | undefined;
                blobs: readonly import("../index.js").Hex[] | readonly import("../index.js").ByteArray[];
                blobVersionedHashes?: readonly `0x${string}`[] | undefined;
                kzg?: import("../index.js").Kzg | undefined;
                sidecars?: readonly import("../index.js").BlobSidecar<`0x${string}`>[] | undefined;
                authorizationList?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                type?: "0x4" | undefined;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                authorizationList?: import("../index.js").RpcAuthorizationList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                type?: "0x7b" | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                feeCurrency?: `0x${string}` | undefined;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                authorizationList?: undefined;
            }) & {};
            type: "transactionRequest";
        };
    };
    readonly serializers: {
        readonly transaction: typeof import("./serializers.js").serializeTransaction;
    };
    readonly fees: import("../index.js").ChainFees<{
        readonly block: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloRpcBlock) => {
                baseFeePerGas: bigint | null;
                blobGasUsed: bigint;
                difficulty: bigint;
                excessBlobGas: bigint;
                extraData: import("../index.js").Hex;
                gasLimit: bigint;
                gasUsed: bigint;
                hash: `0x${string}` | null;
                logsBloom: `0x${string}` | null;
                miner: import("abitype").Address;
                mixHash: import("../index.js").Hash;
                nonce: `0x${string}` | null;
                number: bigint | null;
                parentBeaconBlockRoot?: `0x${string}` | undefined;
                parentHash: import("../index.js").Hash;
                receiptsRoot: import("../index.js").Hex;
                sealFields: import("../index.js").Hex[];
                sha3Uncles: import("../index.js").Hash;
                size: bigint;
                stateRoot: import("../index.js").Hash;
                timestamp: bigint;
                totalDifficulty: bigint | null;
                transactions: `0x${string}`[] | import("./types.js").CeloTransaction<boolean>[];
                transactionsRoot: import("../index.js").Hash;
                uncles: import("../index.js").Hash[];
                withdrawals?: import("../index.js").Withdrawal[] | undefined | undefined;
                withdrawalsRoot?: `0x${string}` | undefined;
            } & {};
            type: "block";
        };
        readonly transaction: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloRpcTransaction) => ({
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                v: bigint;
                to: import("abitype").Address | null;
                from: import("abitype").Address;
                gas: bigint;
                nonce: number;
                value: bigint;
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                accessList?: undefined | undefined;
                authorizationList?: undefined | undefined;
                blobVersionedHashes?: undefined | undefined;
                chainId?: number | undefined;
                yParity?: undefined | undefined;
                type: "legacy";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: undefined | undefined;
                maxPriorityFeePerGas?: undefined | undefined;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined | undefined;
                blobVersionedHashes?: undefined | undefined;
                chainId: number;
                type: "eip2930";
                gasPrice: bigint;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: undefined | undefined;
                maxPriorityFeePerGas?: undefined | undefined;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined | undefined;
                blobVersionedHashes?: undefined | undefined;
                chainId: number;
                type: "eip1559";
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList?: undefined | undefined;
                blobVersionedHashes: readonly import("../index.js").Hex[];
                chainId: number;
                type: "eip4844";
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas: bigint;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                accessList: import("../index.js").AccessList;
                authorizationList: import("../index.js").SignedAuthorizationList;
                blobVersionedHashes?: undefined | undefined;
                chainId: number;
                type: "eip7702";
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                feeCurrency: import("abitype").Address | null;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                accessList: import("../index.js").AccessList;
                chainId: number;
                feeCurrency: import("abitype").Address | null;
                gatewayFee: bigint | null;
                gatewayFeeRecipient: import("abitype").Address | null;
                type: "cip42";
                blobVersionedHashes?: undefined;
                authorizationList?: undefined;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                accessList: import("../index.js").AccessList;
                chainId: number;
                feeCurrency: import("abitype").Address | null;
                type: "cip64";
                blobVersionedHashes?: undefined;
                authorizationList?: undefined;
                mint?: undefined;
                isSystemTx?: undefined;
                sourceHash?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            } | {
                blockHash: `0x${string}` | null;
                blockNumber: bigint | null;
                from: import("abitype").Address;
                gas: bigint;
                hash: import("../index.js").Hash;
                input: import("../index.js").Hex;
                nonce: number;
                r: import("../index.js").Hex;
                s: import("../index.js").Hex;
                to: import("abitype").Address | null;
                transactionIndex: number | null;
                typeHex: import("../index.js").Hex | null;
                v: bigint;
                value: bigint;
                yParity: number;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas: bigint;
                maxPriorityFeePerGas: bigint;
                isSystemTx?: boolean;
                mint?: bigint | undefined | undefined;
                sourceHash: import("../index.js").Hex;
                type: "deposit";
                accessList?: undefined;
                blobVersionedHashes?: undefined;
                authorizationList?: undefined;
                chainId?: undefined;
                feeCurrency?: undefined;
                gatewayFee?: undefined;
                gatewayFeeRecipient?: undefined;
            }) & {};
            type: "transaction";
        };
        readonly transactionRequest: {
            exclude: [] | undefined;
            format: (args: import("./types.js").CeloTransactionRequest) => ({
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                type?: "0x0" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: undefined | undefined;
                maxPriorityFeePerGas?: undefined | undefined;
                blobs?: undefined;
                accessList?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                authorizationList?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                type?: "0x1" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: `0x${string}` | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: undefined | undefined;
                maxPriorityFeePerGas?: undefined | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                authorizationList?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                type?: "0x2" | undefined;
                value?: `0x${string}` | undefined;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                authorizationList?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                type?: "0x3" | undefined;
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                to: `0x${string}` | null;
                gasPrice?: undefined | undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                maxFeePerBlobGas: `0x${string}`;
                accessList?: import("../index.js").AccessList | undefined;
                blobs: readonly import("../index.js").Hex[] | readonly import("../index.js").ByteArray[];
                blobVersionedHashes?: readonly `0x${string}`[] | undefined;
                kzg?: import("../index.js").Kzg | undefined;
                sidecars?: readonly import("../index.js").BlobSidecar<`0x${string}`>[] | undefined;
                authorizationList?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                type?: "0x4" | undefined;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                authorizationList?: import("../index.js").RpcAuthorizationList | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                feeCurrency?: `0x${string}` | undefined;
            } | {
                data?: `0x${string}` | undefined;
                from?: `0x${string}` | undefined;
                gas?: `0x${string}` | undefined;
                nonce?: `0x${string}` | undefined;
                to?: `0x${string}` | null | undefined;
                type?: "0x7b" | undefined;
                value?: `0x${string}` | undefined;
                accessList?: import("../index.js").AccessList | undefined;
                feeCurrency?: `0x${string}` | undefined;
                gasPrice?: undefined | undefined;
                maxFeePerBlobGas?: undefined | undefined;
                maxFeePerGas?: `0x${string}` | undefined;
                maxPriorityFeePerGas?: `0x${string}` | undefined;
                blobs?: undefined;
                blobVersionedHashes?: undefined;
                kzg?: undefined;
                sidecars?: undefined;
                authorizationList?: undefined;
            }) & {};
            type: "transactionRequest";
        };
    }>;
};
//# sourceMappingURL=chainConfig.d.ts.map