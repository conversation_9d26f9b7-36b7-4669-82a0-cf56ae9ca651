"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.pgnTestnet = void 0;
const formatters_js_1 = require("../../op-stack/formatters.js");
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
const sourceId = 11_155_111;
exports.pgnTestnet = (0, defineChain_js_1.defineChain)({
    id: 58008,
    network: 'pgn-testnet',
    name: 'PGN ',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://sepolia.publicgoods.network'],
        },
    },
    blockExplorers: {
        default: {
            name: 'PGN Testnet Explorer',
            url: 'https://explorer.sepolia.publicgoods.network',
            apiUrl: 'https://explorer.sepolia.publicgoods.network/api',
        },
    },
    contracts: {
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        multicall3: {
            address: '******************************************',
            blockCreated: 3754925,
        },
    },
    formatters: formatters_js_1.formatters,
    sourceId,
    testnet: true,
});
//# sourceMappingURL=pgnTestnet.js.map