"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bitkub = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.bitkub = (0, defineChain_js_1.defineChain)({
    id: 96,
    name: 'KUB Mainnet',
    nativeCurrency: { name: 'KUB Coin', symbol: 'KUB', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.bitkubchain.io'],
        },
    },
    blockExplorers: {
        default: {
            name: 'KUB Chain Mainnet Explorer',
            url: 'https://www.bkcscan.com',
            apiUrl: 'https://www.bkcscan.com/api',
        },
    },
});
//# sourceMappingURL=bitkub.js.map