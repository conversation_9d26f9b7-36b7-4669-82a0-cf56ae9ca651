"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.zora = void 0;
const chainConfig_js_1 = require("../../op-stack/chainConfig.js");
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
const sourceId = 1;
exports.zora = (0, defineChain_js_1.defineChain)({
    ...chainConfig_js_1.chainConfig,
    id: 7777777,
    name: 'Zora',
    nativeCurrency: {
        decimals: 18,
        name: 'Ether',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['https://rpc.zora.energy'],
            webSocket: ['wss://rpc.zora.energy'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Explorer',
            url: 'https://explorer.zora.energy',
            apiUrl: 'https://explorer.zora.energy/api',
        },
    },
    contracts: {
        ...chainConfig_js_1.chainConfig.contracts,
        l2OutputOracle: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        multicall3: {
            address: '******************************************',
            blockCreated: 5882,
        },
        portal: {
            [sourceId]: {
                address: '******************************************',
            },
        },
        l1StandardBridge: {
            [sourceId]: {
                address: '******************************************',
            },
        },
    },
    sourceId,
});
//# sourceMappingURL=zora.js.map