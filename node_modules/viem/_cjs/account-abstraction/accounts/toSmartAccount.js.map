{"version": 3, "file": "toSmartAccount.js", "sourceRoot": "", "sources": ["../../../account-abstraction/accounts/toSmartAccount.ts"], "names": [], "mappings": ";;AA4BA,wCAgHC;AA5ID,qCAA4C;AAE5C,gEAAyD;AACzD,0EAAmE;AAEnE,2DAAoD;AACpD,iEAAgE;AAChE,qGAA8F;AAqBvF,KAAK,UAAU,cAAc,CAGlC,cAA8B;IAE9B,MAAM,EACJ,MAAM,EACN,eAAe,GAAG,IAAA,oCAAkB,EAAC;QACnC,MAAM,EAAE;YACN,GAAG;gBACD,OAAO,IAAI,CAAC,GAAG,EAAE,CAAA;YACnB,CAAC;YACD,GAAG,KAAI,CAAC;SACT;KACF,CAAC,EACF,GAAG,IAAI,EACR,GAAG,cAAc,CAAA;IAElB,IAAI,QAAQ,GAAG,KAAK,CAAA;IAEpB,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,UAAU,EAAE,CAAA;IAEjD,OAAO;QACL,GAAG,MAAM;QACT,GAAG,IAAI;QACP,OAAO;QACP,KAAK,CAAC,cAAc;YAClB,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,CAAA;YACvD,OAAO,cAAc,CAAC,cAAc,EAAE,CAAA;QACxC,CAAC;QACD,KAAK,CAAC,QAAQ,CAAC,UAAU;YACvB,MAAM,GAAG,GACP,UAAU,EAAE,GAAG;gBACf,MAAM,CACJ,MAAM,eAAe,CAAC,OAAO,CAAC;oBAC5B,OAAO;oBACP,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC,KAAM,CAAC,EAAG;oBACzC,MAAM,EAAE,cAAc,CAAC,MAAM;iBAC9B,CAAC,CACH,CAAA;YAEH,IAAI,cAAc,CAAC,QAAQ;gBACzB,OAAO,MAAM,cAAc,CAAC,QAAQ,CAAC,EAAE,GAAG,UAAU,EAAE,GAAG,EAAE,CAAC,CAAA;YAE9D,MAAM,KAAK,GAAG,MAAM,IAAA,8BAAY,EAAC,cAAc,CAAC,MAAM,EAAE;gBACtD,GAAG,EAAE,IAAA,kBAAQ,EAAC;oBACZ,4DAA4D;iBAC7D,CAAC;gBACF,OAAO,EAAE,cAAc,CAAC,UAAU,CAAC,OAAO;gBAC1C,YAAY,EAAE,UAAU;gBACxB,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC;aACrB,CAAC,CAAA;YACF,OAAO,KAAK,CAAA;QACd,CAAC;QACD,KAAK,CAAC,UAAU;YACd,IAAI,QAAQ;gBAAE,OAAO,IAAI,CAAA;YACzB,MAAM,IAAI,GAAG,MAAM,IAAA,wBAAS,EAC1B,cAAc,CAAC,MAAM,EACrB,oBAAO,EACP,SAAS,CACV,CAAC;gBACA,OAAO;aACR,CAAC,CAAA;YACF,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;YACxB,OAAO,QAAQ,CAAA;QACjB,CAAC;QACD,GAAG,CAAC,cAAc,CAAC,IAAI;YACrB,CAAC,CAAC;gBACE,KAAK,CAAC,IAAI,CAAC,UAAU;oBACnB,MAAM,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;wBAC9D,IAAI,CAAC,cAAc,EAAE;wBACrB,cAAc,CAAC,IAAK,CAAC,UAAU,CAAC;qBACjC,CAAC,CAAA;oBACF,IAAI,OAAO,IAAI,WAAW;wBACxB,OAAO,IAAA,wDAAyB,EAAC;4BAC/B,OAAO,EAAE,OAAO;4BAChB,IAAI,EAAE,WAAW;4BACjB,SAAS;yBACV,CAAC,CAAA;oBACJ,OAAO,SAAS,CAAA;gBAClB,CAAC;aACF;YACH,CAAC,CAAC,EAAE,CAAC;QACP,KAAK,CAAC,WAAW,CAAC,UAAU;YAC1B,MAAM,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC9D,IAAI,CAAC,cAAc,EAAE;gBACrB,cAAc,CAAC,WAAW,CAAC,UAAU,CAAC;aACvC,CAAC,CAAA;YACF,IAAI,OAAO,IAAI,WAAW,IAAI,OAAO,KAAK,QAAQ;gBAChD,OAAO,IAAA,wDAAyB,EAAC;oBAC/B,OAAO,EAAE,OAAO;oBAChB,IAAI,EAAE,WAAW;oBACjB,SAAS;iBACV,CAAC,CAAA;YACJ,OAAO,SAAS,CAAA;QAClB,CAAC;QACD,KAAK,CAAC,aAAa,CAAC,UAAU;YAC5B,MAAM,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC9D,IAAI,CAAC,cAAc,EAAE;gBACrB,cAAc,CAAC,aAAa,CAAC,UAAU,CAAC;aACzC,CAAC,CAAA;YACF,IAAI,OAAO,IAAI,WAAW,IAAI,OAAO,KAAK,QAAQ;gBAChD,OAAO,IAAA,wDAAyB,EAAC;oBAC/B,OAAO,EAAE,OAAO;oBAChB,IAAI,EAAE,WAAW;oBACjB,SAAS;iBACV,CAAC,CAAA;YACJ,OAAO,SAAS,CAAA;QAClB,CAAC;QACD,IAAI,EAAE,OAAO;KAC8B,CAAA;AAC/C,CAAC"}