{"version": 3, "file": "toWebAuthnAccount.js", "sourceRoot": "", "sources": ["../../../account-abstraction/accounts/toWebAuthnAccount.ts"], "names": [], "mappings": ";;AAuCA,8CA6BC;AApED,0CAAyC;AACzC,gDAA+C;AAG/C,yEAAkE;AAClE,6EAAsE;AAkCtE,SAAgB,iBAAiB,CAC/B,UAAuC;IAEvC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,UAAU,CAAA;IAClC,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,UAAU,CAAA;IAC/C,OAAO;QACL,EAAE;QACF,SAAS;QACT,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE;YACjB,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC;gBAC3D,YAAY,EAAE,EAAE;gBAChB,KAAK;gBACL,SAAS,EAAE,IAAI;gBACf,IAAI;aACL,CAAC,CAAA;YACF,OAAO;gBACL,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;gBACrC,GAAG;gBACH,QAAQ,EAAE,QAAQ;aACnB,CAAA;QACH,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE;YAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,4BAAW,EAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QAClD,CAAC;QACD,KAAK,CAAC,aAAa,CAAC,UAAU;YAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,gCAAa,EAAC,UAAU,CAAC,EAAE,CAAC,CAAA;QACvD,CAAC;QACD,IAAI,EAAE,UAAU;KACjB,CAAA;AACH,CAAC"}