"use client";

// src/wallets/walletConnectors/xPortalWallet/xPortalWallet.svg
var xPortalWallet_default = "data:image/svg+xml,%3Csvg%20viewBox%3D%220%200%20400%20400%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%3Crect%20width%3D%22400%22%20height%3D%22400%22%20fill%3D%22%2323F7DD%22%20%2F%3E%0A%20%20%3Cpath%0A%20%20%20%20d%3D%22M213.494%20200.154L213.594%20200.252L213.642%20200.227L326.371%20258.235L307.544%20293.089L204.199%20252.828C201.488%20251.771%20198.482%20251.771%20195.772%20252.828L92.4265%20293.089L73.5996%20258.235L186.459%20200.188L186.797%20200.014L186.459%20199.84L73.5996%20141.765L92.4265%20106.911L195.801%20147.172C198.511%20148.229%20201.517%20148.229%20204.228%20147.172L307.573%20106.911L326.4%20141.765L213.54%20199.84L213.594%20199.946L213.465%20200.126L213.494%20200.154Z%22%0A%20%20%20%20fill%3D%22black%22%20stroke%3D%22%231F1F1F%22%20stroke-width%3D%220.390625%22%20%2F%3E%0A%3C%2Fsvg%3E";
export {
  xPortalWallet_default as default
};
