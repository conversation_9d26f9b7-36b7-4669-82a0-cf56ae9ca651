import { argentWallet } from './argentWallet/argentWallet';
import { backpackWallet } from './backpackWallet/backpackWallet';
import { berasigWallet } from './berasigWallet/berasigWallet';
import { bestWallet } from './bestWallet/bestWallet';
import { bifrostWallet } from './bifrostWallet/bifrostWallet';
import { binanceWallet } from './binanceWallet/binanceWallet';
import { bitgetWallet } from './bitgetWallet/bitgetWallet';
import { bitskiWallet } from './bitskiWallet/bitskiWallet';
import { bitverseWallet } from './bitverseWallet/bitverseWallet';
import { bloomWallet } from './bloomWallet/bloomWallet';
import { braveWallet } from './braveWallet/braveWallet';
import { bybitWallet } from './bybitWallet/bybitWallet';
import { clvWallet } from './clvWallet/clvWallet';
import { coin98Wallet } from './coin98Wallet/coin98Wallet';
import { coinbaseWallet } from './coinbaseWallet/coinbaseWallet';
import { compassWallet } from './compassWallet/compassWallet';
import { coreWallet } from './coreWallet/coreWallet';
import { dawnWallet } from './dawnWallet/dawnWallet';
import { desigWallet } from './desigWallet/desigWallet';
import { enkryptWallet } from './enkryptWallet/enkryptWallet';
import { foxWallet } from './foxWallet/foxWallet';
import { frameWallet } from './frameWallet/frameWallet';
import { frontierWallet } from './frontierWallet/frontierWallet';
import { gateWallet } from './gateWallet/gateWallet';
import { imTokenWallet } from './imTokenWallet/imTokenWallet';
import { injectedWallet } from './injectedWallet/injectedWallet';
import { iopayWallet } from './iopayWallet/iopayWallet';
import { kaiaWallet } from './kaiaWallet/kaiaWallet';
import { kaikasWallet } from './kaikasWallet/kaikasWallet';
import { krakenWallet } from './krakenWallet/krakenWallet';
import { kresusWallet } from './kresusWallet/kresusWallet';
import { ledgerWallet } from './ledgerWallet/ledgerWallet';
import { magicEdenWallet } from './magicEdenWallet/magicEdenWallet';
import { metaMaskWallet } from './metaMaskWallet/metaMaskWallet';
import { mewWallet } from './mewWallet/mewWallet';
import { nestWallet } from './nestWallet/nestWallet';
import { oktoWallet } from './oktoWallet/oktoWallet';
import { okxWallet } from './okxWallet/okxWallet';
import { omniWallet } from './omniWallet/omniWallet';
import { oneInchWallet } from './oneInchWallet/oneInchWallet';
import { oneKeyWallet } from './oneKeyWallet/oneKeyWallet';
import { paraSwapWallet } from './paraSwapWallet/paraswapWallet';
import { phantomWallet } from './phantomWallet/phantomWallet';
import { rabbyWallet } from './rabbyWallet/rabbyWallet';
import { rainbowWallet } from './rainbowWallet/rainbowWallet';
import { ramperWallet } from './ramperWallet/ramperWallet';
import { roninWallet } from './roninWallet/roninWallet';
import { safeWallet } from './safeWallet/safeWallet';
import { safeheronWallet } from './safeheronWallet/safeheronWallet';
import { safepalWallet } from './safepalWallet/safepalWallet';
import { seifWallet } from './seifWallet/seifWallet';
import { subWallet } from './subWallet/subWallet';
import { tahoWallet } from './tahoWallet/tahoWallet';
import { talismanWallet } from './talismanWallet/talismanWallet';
import { tokenPocketWallet } from './tokenPocketWallet/tokenPocketWallet';
import { tokenaryWallet } from './tokenaryWallet/tokenaryWallet';
import { trustWallet } from './trustWallet/trustWallet';
import { uniswapWallet } from './uniswapWallet/uniswapWallet';
import { valoraWallet } from './valoraWallet/valoraWallet';
import { walletConnectWallet } from './walletConnectWallet/walletConnectWallet';
import { wigwamWallet } from './wigwamWallet/wigwamWallet';
import { xdefiWallet } from './xdefiWallet/xdefiWallet';
import { xPortalWallet } from './xPortalWallet/xPortalWallet';
import { zealWallet } from './zealWallet/zealWallet';
import { zerionWallet } from './zerionWallet/zerionWallet';
import { zilPayWallet } from './ZilPayWallet/zilPayWallet';
export { argentWallet, backpackWallet, berasigWallet, bestWallet, bifrostWallet, binanceWallet, bitgetWallet, bitskiWallet, bitverseWallet, bloomWallet, braveWallet, bybitWallet, clvWallet, coin98Wallet, coinbaseWallet, compassWallet, coreWallet, dawnWallet, desigWallet, enkryptWallet, foxWallet, frameWallet, frontierWallet, gateWallet, imTokenWallet, injectedWallet, iopayWallet, kaiaWallet, kaikasWallet, krakenWallet, kresusWallet, ledgerWallet, magicEdenWallet, metaMaskWallet, mewWallet, nestWallet, oktoWallet, okxWallet, omniWallet, oneInchWallet, oneKeyWallet, paraSwapWallet, phantomWallet, rabbyWallet, rainbowWallet, ramperWallet, roninWallet, safeheronWallet, safepalWallet, safeWallet, seifWallet, subWallet, tahoWallet, talismanWallet, tokenaryWallet, tokenPocketWallet, trustWallet, uniswapWallet, valoraWallet, walletConnectWallet, wigwamWallet, xdefiWallet, xPortalWallet, zealWallet, zerionWallet, zilPayWallet, };
