import React, { type ReactNode } from 'react';
import type { ResponsiveValue } from '../../css/sprinkles.css';
interface ShowBalanceContextValue {
    showBalance: ResponsiveValue<boolean> | undefined;
    setShowBalance: (showBalance: ResponsiveValue<boolean>) => void;
}
interface ShowBalanceProviderProps {
    children: ReactNode;
}
export declare function ShowBalanceProvider({ children }: ShowBalanceProviderProps): React.JSX.Element;
export declare const useShowBalance: () => ShowBalanceContextValue;
export {};
