import React from 'react';
import { type BoxProps } from '../Box/Box';
export type TextProps = {
    id?: string;
    as?: 'code' | 'div' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'label' | 'p' | 'span';
    children?: React.ReactNode;
    color: BoxProps['color'];
    font?: BoxProps['fontFamily'];
    size?: BoxProps['fontSize'];
    style?: React.CSSProperties;
    weight?: BoxProps['fontWeight'];
    className?: string;
    tabIndex?: number;
    textAlign?: BoxProps['textAlign'];
    display?: BoxProps['display'];
    testId?: string;
};
export declare const Text: React.ForwardRefExoticComponent<TextProps & React.RefAttributes<HTMLElement>>;
