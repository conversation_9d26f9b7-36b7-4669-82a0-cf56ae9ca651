import React from 'react';
import { type BoxProps } from '../Box/Box';
import { type AsyncImageSrc } from './useAsyncImage';
type CustomBorderColor = {
    custom: string;
};
interface AsyncImageProps {
    alt?: string;
    src: string | AsyncImageSrc | undefined;
    width: BoxProps['width'] | number;
    height: BoxProps['height'] | number;
    background?: string;
    borderRadius?: BoxProps['borderRadius'];
    useAsImage?: boolean;
    borderColor?: BoxProps['borderColor'] | CustomBorderColor;
    boxShadow?: BoxProps['boxShadow'];
    testId?: string;
}
export declare function AsyncImage({ alt, background, borderColor, borderRadius, useAsImage, boxShadow, height, src: srcProp, width, testId, }: AsyncImageProps): React.JSX.Element;
export {};
