export declare function emojiAvatarForAddress(address: string): {
    readonly color: "#FC5C54";
    readonly emoji: "🌶";
} | {
    readonly color: "#FFD95A";
    readonly emoji: "🤑";
} | {
    readonly color: "#E95D72";
    readonly emoji: "🐙";
} | {
    readonly color: "#6A87C8";
    readonly emoji: "🫐";
} | {
    readonly color: "#5FD0F3";
    readonly emoji: "🐳";
} | {
    readonly color: "#FC5C54";
    readonly emoji: "🤶";
} | {
    readonly color: "#75C06B";
    readonly emoji: "🌲";
} | {
    readonly color: "#FFDD86";
    readonly emoji: "🌞";
} | {
    readonly color: "#5FC6D4";
    readonly emoji: "🐒";
} | {
    readonly color: "#FF949A";
    readonly emoji: "🐵";
} | {
    readonly color: "#FF8024";
    readonly emoji: "🦊";
} | {
    readonly color: "#9BA1A4";
    readonly emoji: "🐼";
} | {
    readonly color: "#EC66FF";
    readonly emoji: "🦄";
} | {
    readonly color: "#FF8CBC";
    readonly emoji: "🐷";
} | {
    readonly color: "#FF9A23";
    readonly emoji: "🐧";
} | {
    readonly color: "#FF949A";
    readonly emoji: "🦩";
} | {
    readonly color: "#C5DADB";
    readonly emoji: "👽";
} | {
    readonly color: "#FC5C54";
    readonly emoji: "🎈";
} | {
    readonly color: "#FF949A";
    readonly emoji: "🍉";
} | {
    readonly color: "#FFD95A";
    readonly emoji: "🎉";
} | {
    readonly color: "#A8CE63";
    readonly emoji: "🐲";
} | {
    readonly color: "#71ABFF";
    readonly emoji: "🌎";
} | {
    readonly color: "#FFE279";
    readonly emoji: "🍊";
} | {
    readonly color: "#B6B1B6";
    readonly emoji: "🐭";
} | {
    readonly color: "#FF6780";
    readonly emoji: "🍣";
} | {
    readonly color: "#FFD95A";
    readonly emoji: "🐥";
} | {
    readonly color: "#A575FF";
    readonly emoji: "👾";
} | {
    readonly color: "#A8CE63";
    readonly emoji: "🥦";
} | {
    readonly color: "#FC5C54";
    readonly emoji: "👹";
} | {
    readonly color: "#FFE279";
    readonly emoji: "🙀";
} | {
    readonly color: "#5FD0F3";
    readonly emoji: "⛱";
} | {
    readonly color: "#4D82FF";
    readonly emoji: "⛵️";
} | {
    readonly color: "#FFE279";
    readonly emoji: "🥳";
} | {
    readonly color: "#FF949A";
    readonly emoji: "🤯";
} | {
    readonly color: "#FFB35A";
    readonly emoji: "🤠";
};
