import React from 'react';
export type ErrorCorrectionLevel = 'low' | 'medium' | 'quartile' | 'high';
interface Props {
    ecc?: ErrorCorrectionLevel;
    logoBackground?: string;
    logoUrl?: string | (() => Promise<string>);
    logoSize?: number;
    size?: number;
    uri: string;
}
export declare function QRCode({ ecc, logoBackground, logoSize, logoUrl, size: sizeProp, uri, }: Props): React.JSX.Element;
export {};
