import React, { type ReactNode } from 'react';
import { type BoxProps } from '../Box/Box';
interface DialogContentProps {
    children: ReactNode;
    bottomSheetOnMobile?: boolean;
    padding?: BoxProps['padding'];
    paddingBottom?: BoxProps['paddingBottom'];
    marginTop?: BoxProps['marginTop'];
    wide?: boolean;
}
export declare function DialogContent({ bottomSheetOnMobile, children, marginTop, padding, paddingBottom, wide, }: DialogContentProps): React.JSX.Element;
export {};
