import { LitElement } from 'lit';
import type { IconType } from '@reown/appkit-ui';
import '@reown/appkit-ui/wui-button';
import '@reown/appkit-ui/wui-flex';
import '@reown/appkit-ui/wui-icon';
import '@reown/appkit-ui/wui-icon-box';
import '@reown/appkit-ui/wui-link';
import '@reown/appkit-ui/wui-loading-thumbnail';
import '@reown/appkit-ui/wui-text';
import '@reown/appkit-ui/wui-wallet-image';
import '../../partials/w3m-mobile-download-links/index.js';
export declare class W3mConnectingWidget extends LitElement {
    static styles: import("lit").CSSResult;
    protected readonly wallet: import("@reown/appkit-controllers").WcWallet | undefined;
    protected readonly connector: import("@reown/appkit-controllers").Connector | undefined;
    protected timeout?: ReturnType<typeof setTimeout>;
    protected secondaryBtnIcon: IconType;
    protected onConnect?: (() => void) | (() => Promise<void>);
    protected onRender?: (() => void) | (() => Promise<void>);
    protected onAutoConnect?: (() => void) | (() => Promise<void>);
    protected isWalletConnect: boolean;
    protected unsubscribe: (() => void)[];
    private imageSrc;
    private name;
    protected isRetrying: boolean;
    protected uri: string | undefined;
    protected error: boolean | undefined;
    protected ready: boolean;
    private showRetry;
    protected secondaryBtnLabel?: string | undefined;
    protected secondaryLabel: string;
    protected isLoading: boolean;
    isMobile: boolean;
    onRetry?: (() => void) | (() => Promise<void>);
    constructor();
    firstUpdated(): void;
    disconnectedCallback(): void;
    render(): import("lit").TemplateResult<1>;
    private onShowRetry;
    protected onTryAgain(): void;
    private loaderTemplate;
    protected onCopyUri(): void;
}
