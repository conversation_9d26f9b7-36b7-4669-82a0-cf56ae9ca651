{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../src/modal/w3m-account-button/index.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,KAAK,CAAA;AACtC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,8BAA8B,CAAA;AAGxD,OAAO,EACL,iBAAiB,EACjB,eAAe,EACf,SAAS,EACT,eAAe,EACf,cAAc,EACd,eAAe,EACf,iBAAiB,EAClB,MAAM,2BAA2B,CAAA;AAClC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAA;AAEhD,OAAO,qCAAqC,CAAA;AAE5C,MAAM,oBAAqB,SAAQ,UAAU;IAA7C;;QAEU,gBAAW,GAAmB,EAAE,CAAA;QAGJ,aAAQ,GAAkC,KAAK,CAAA;QAEhE,YAAO,GAAqB,MAAM,CAAA;QAElC,eAAU,GAAoC,CAAC,CAAA;QAE/C,aAAQ,GAAkC,CAAC,CAAA;QAE3C,cAAS,GAAoB,SAAS,CAAA;QAExC,gBAAW,GAAG,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,WAAW,CAAA;QAEzE,eAAU,GAAG,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,CAAA;QAEpE,kBAAa,GAAG,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,CAAA;QAE7E,gBAAW,GAAG,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,WAAW,CAAA;QAEzE,iBAAY,GAAG,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,CAAA;QAE3E,YAAO,GAAG,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,WAAW,CAAA;QAErE,iBAAY,GAAG,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAGtD,gBAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,qBAAqB;YAC1E,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW;gBACjC,CAAC,CAAC,eAAe,CAAC,uBAAuB,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC;gBAC5E,CAAC,CAAC,IAAI,CAAA;IA2GZ,CAAC;IAxGiB,YAAY;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAEhC,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,eAAe,CAAC,kBAAkB,CAChC,cAAc,EACd,GAAG,CAAC,EAAE;gBACJ,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,WAAW,CAAA;gBACnC,IAAI,CAAC,UAAU,GAAG,GAAG,EAAE,OAAO,CAAA;gBAC9B,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE,aAAa,CAAA;gBACvC,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,WAAW,CAAA;gBACnC,IAAI,CAAC,YAAY,GAAG,GAAG,EAAE,YAAY,CAAA;YACvC,CAAC,EACD,SAAS,CACV,EACD,eAAe,CAAC,kBAAkB,CAChC,cAAc,EACd,GAAG,CAAC,EAAE;gBACJ,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE,WAAW,CAAA;gBAC/B,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC,uBAAuB,CAAC,SAAS,EAAE,GAAG,EAAE,WAAW,CAAC,CAAA;gBACvF,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,EAAE,WAAW,CAAC,CAAA;YACjE,CAAC,EACD,SAAS,CACV,CACF,CAAA;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,eAAe,CAAC,sBAAsB,CAAC,GAAG,EAAE;gBAC1C,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC7D,CAAC,CAAC,EACF,eAAe,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,CAAC,EAAE;gBACtD,IAAI,CAAC,WAAW,GAAG,GAAG,CAAA;YACxB,CAAC,CAAC,EACF,iBAAiB,CAAC,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,EACzE,iBAAiB,CAAC,YAAY,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC,EAClF,iBAAiB,CAAC,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,EAC9E,iBAAiB,CAAC,YAAY,CAAC,cAAc,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,EAChF,eAAe,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,CAAC,EAAE;gBACtD,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;gBAClB,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;gBAClD,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,cAAc;oBACpC,CAAC,CAAC,eAAe,CAAC,uBAAuB,CAAC,GAAG,EAAE,cAAc,CAAC;oBAC9D,CAAC,CAAC,IAAI,CAAA;gBACR,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;YAC7B,CAAC,CAAC,CACH,CAAA;QACH,CAAC;IACH,CAAC;IAEe,OAAO;QACrB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACtC,CAAC;IAEe,oBAAoB;QAClC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAA;IACxD,CAAC;IAGe,MAAM;QACpB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvC,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,CAAA;QACjD,MAAM,iBAAiB,GAAG,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAA;QAE7D,OAAO,IAAI,CAAA;;oBAEK,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;8BACZ,iBAAiB,CAAC,KAAK,CAAC,qBAAqB;YACjE,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW;kBACX,SAAS,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;sBACvD,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC;qBAC5B,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC;oBAC7B,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC;kBAC9B,iBAAiB;YACzB,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC;YACnE,CAAC,CAAC,EAAE;iBACG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;sBAClB,iBAAiB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;sBAC7D,IAAI,CAAC,UAAU;oBACjB,IAAI,CAAC,QAAQ;mBACd,iBAAiB;;;KAG/B,CAAA;IACH,CAAC;IAGO,OAAO;QACb,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;YACtE,eAAe,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;QACrD,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;QACpD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAqB;QACnD,IAAI,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,GAAG,MAAM,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;QACjF,CAAC;IACH,CAAC;CACF;AAxIqC;IAAnC,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;sDAAuD;AAEhE;IAAlB,QAAQ,EAAE;qDAA0C;AAElC;IAAlB,QAAQ,EAAE;wDAAuD;AAE/C;IAAlB,QAAQ,EAAE;sDAAmD;AAE3C;IAAlB,QAAQ,EAAE;uDAA8C;AAExC;IAAhB,KAAK,EAAE;yDAAkF;AAEzE;IAAhB,KAAK,EAAE;wDAA6E;AAEpE;IAAhB,KAAK,EAAE;2DAAsF;AAE7E;IAAhB,KAAK,EAAE;yDAAkF;AAEzE;IAAhB,KAAK,EAAE;0DAAoF;AAE3E;IAAhB,KAAK,EAAE;qDAA8E;AAErE;IAAhB,KAAK,EAAE;0DAA+D;AAGtD;IAAhB,KAAK,EAAE;yDAIE;AA8GL,IAAM,gBAAgB,GAAtB,MAAM,gBAAiB,SAAQ,oBAAoB;CAAG,CAAA;AAAhD,gBAAgB;IAD5B,aAAa,CAAC,oBAAoB,CAAC;GACvB,gBAAgB,CAAgC;;AAGtD,IAAM,mBAAmB,GAAzB,MAAM,mBAAoB,SAAQ,oBAAoB;CAAG,CAAA;AAAnD,mBAAmB;IAD/B,aAAa,CAAC,uBAAuB,CAAC;GAC1B,mBAAmB,CAAgC"}