{"version": 3, "file": "WalletUtil.js", "sourceRoot": "", "sources": ["../../../../src/utils/WalletUtil.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,aAAa,EACb,mBAAmB,EACnB,cAAc,EACd,iBAAiB,EACjB,WAAW,EACZ,MAAM,2BAA2B,CAAA;AAElC,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAEjD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAA;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAA;AAMlD,MAAM,CAAC,MAAM,UAAU,GAAG;IACxB,yBAAyB,CAAC,OAAmB;QAC3C,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,aAAa;YACtD,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU;YACtC,CAAC,CAAC,EAAE,CAAA;QACN,MAAM,MAAM,GAAG,WAAW,CAAC,gBAAgB,EAAE,CAAA;QAE7C,MAAM,cAAc,GAAG,UAAU;aAC9B,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC;aACtC,MAAM,CAAC,OAAO,CAAa,CAAA;QAE9B,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAa,CAAA;QACjF,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;QACnD,IAAI,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,cAAc,CAAC,QAAQ,EAAE,EAAE,CAAC;YACzE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAA;YACpD,QAAQ,CAAC,KAAK,CAAC,GAAG,aAAa,CAAA;QACjC,CAAC;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;QAEnF,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,wBAAwB,CAAC,OAAmB;QAC1C,MAAM,UAAU,GAAG,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAC5D,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,CAC7E,CAAA;QACD,MAAM,MAAM,GAAG,WAAW,CAAC,gBAAgB,EAAE,CAAA;QAE7C,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;QAEtE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QAEjD,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QAE7C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAA;QAEvE,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,yBAAyB,CAAC,OAAmB;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAA;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAA;QAEjE,OAAO,aAAa,CAAA;IACtB,CAAC;IASD,sBAAsB,CAAC,OAAmB;QACxC,MAAM,EAAE,UAAU,EAAE,GAAG,mBAAmB,CAAC,KAAK,CAAA;QAChD,MAAM,EAAE,iBAAiB,EAAE,GAAG,iBAAiB,CAAC,KAAK,CAAA;QAErD,MAAM,sBAAsB,GAAG,UAAU;aACtC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW,CAAC;aACnD,MAAM,CAA0B,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;YACtD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;gBAC1B,OAAO,OAAO,CAAA;YAChB,CAAC;YACD,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;YAEnC,OAAO,OAAO,CAAA;QAChB,CAAC,EAAE,EAAE,CAAC,CAAA;QAGR,MAAM,6BAA6B,GAAmB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC3E,GAAG,MAAM;YACT,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;SACtF,CAAC,CAAC,CAAA;QAEH,MAAM,aAAa,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YAC5E,MAAM,sBAAsB,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;YACpF,IAAI,sBAAsB,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,sBAAsB,CAAA;YAC/B,CAAC;YAED,IAAI,iBAAiB,EAAE,MAAM,EAAE,CAAC;gBAC9B,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;gBAClE,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;gBAElE,IAAI,oBAAoB,KAAK,CAAC,CAAC,IAAI,oBAAoB,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC/D,OAAO,oBAAoB,GAAG,oBAAoB,CAAA;gBACpD,CAAC;gBAGD,IAAI,oBAAoB,KAAK,CAAC,CAAC,EAAE,CAAC;oBAChC,OAAO,CAAC,CAAC,CAAA;gBACX,CAAC;gBAGD,IAAI,oBAAoB,KAAK,CAAC,CAAC,EAAE,CAAC;oBAChC,OAAO,CAAC,CAAA;gBACV,CAAC;YACH,CAAC;YAED,OAAO,CAAC,CAAA;QACV,CAAC,CAAC,CAAA;QAEF,OAAO,aAAa,CAAA;IACtB,CAAC;IAED,qBAAqB,CAAC,SAA+B,EAAE,WAAwB;QAC7E,MAAM,kBAAkB,GACtB,SAAS,EAAE,mBAAmB,IAAI,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,mBAAmB,CAAA;QACzF,MAAM,UAAU,GAAG,WAAW,IAAI,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAA;QAEtE,IAAI,kBAAkB,EAAE,CAAC;YACvB,OAAO,kBAAkB,CAAA;QAC3B,CAAC;QAED,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,aAAa,CAAC,mBAAmB,CAC/D,UAAU,EACV,aAAa,CAAC,KAAK,CAAC,WAAW,EAC/B,aAAa,CAAC,KAAK,CAAC,QAAQ,CAC7B,CAAA;QAED,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAA;QAClE,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAA;QAEpE,IAAI,aAAa,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAClD,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAoB,CAAA;QACzD,CAAC;QAED,OAAO,aAAa,CAAC,4BAA4B,CAAA;IACnD,CAAC;IACD,UAAU,CAAC,MAAgB;QACzB,MAAM,cAAc,GAClB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA;QAE/F,MAAM,cAAc,GAClB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;YACpB,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAC3C,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAClD,CAAA;QAEH,OAAO,cAAc,IAAI,cAAc,CAAA;IACzC,CAAC;CACF,CAAA"}