{"version": 3, "file": "ConnectorUtil.js", "sourceRoot": "", "sources": ["../../../../src/utils/ConnectorUtil.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAA;AACpD,OAAO,EACL,aAAa,EACb,eAAe,EACf,oBAAoB,EACpB,mBAAmB,EAGnB,cAAc,EAEd,iBAAiB,EACjB,WAAW,EAEZ,MAAM,2BAA2B,CAAA;AAClC,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAEjD,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAA;AAc5C,MAAM,CAAC,MAAM,aAAa,GAAG;IAC3B,mBAAmB,CACjB,UAAoC,EACpC,WAAuB,EACvB,QAAoB;QAEpB,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,CAAC,KAAK,CAAA;QACjD,MAAM,MAAM,GAAG,WAAW,CAAC,gBAAgB,EAAE,CAAA;QAE7C,MAAM,mBAAmB,GAAG,UAAU,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAA;QAC7E,MAAM,gBAAgB,GAAG,UAAU,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA;QAEvE,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,aAAa,CAAC,CAAA;QACnF,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QAChF,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,CAAC,CAAA;QAC9E,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,CAAC,CAAA;QAE9E,OAAO;YACL,MAAM,EAAE,aAAa;YACrB,MAAM;YACN,QAAQ;YACR,UAAU;YACV,SAAS;YACT,QAAQ;YACR,WAAW,EAAE,mBAAmB;YAChC,QAAQ,EAAE,gBAAgB;SAC3B,CAAA;IACH,CAAC;IAED,aAAa,CAAC,SAAiC;QAC7C,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAA;QAEjC,MAAM,cAAc,GAClB,OAAO,CAAC,IAAI,CAAC;YACb,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CACtC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CACvD,CAAA;QAEH,MAAM,cAAc,GAClB,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;YACvB,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAChD,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAC1D,CAAA;QAEH,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAClC,MAAM,eAAe,GAAG,SAAS,CAAC,IAAI,KAAK,gBAAgB,CAAA;YAE3D,IAAI,eAAe,EAAE,CAAC;gBACpB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,CAAC;oBAC/B,OAAO,KAAK,CAAA;gBACd,CAAC;gBAED,IAAI,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,EAAE,CAAC;oBACjF,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;YAED,IAAI,cAAc,IAAI,cAAc,EAAE,CAAC;gBACrC,OAAO,KAAK,CAAA;YACd,CAAC;QACH,CAAC;QAED,IACE,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,CAAC;YACjE,CAAC,cAAc,IAAI,cAAc,CAAC,EAClC,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAMD,oBAAoB;QAClB,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAA;QAChE,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC5C,MAAM,WAAW,GAAG,mBAAmB,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YAEvE,OAAO,WAAW,KAAK,aAAa,CAAC,YAAY,CAAC,cAAc,CAAA;QAClE,CAAC,CAAC,CAAA;QAEF,OAAO,iBAAiB,CAAA;IAC1B,CAAC;IAMD,qBAAqB,CAAC,EACpB,WAAW,EACX,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,oBAAoB,GAAG,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,kBAAkB,IAAI,EAAE,EACjD;QAChC,MAAM,iBAAiB,GAAG,aAAa,CAAC,oBAAoB,EAAE,CAAA;QAC9D,MAAM,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,mBAAmB,CAAA;QAE/D,MAAM,aAAa,GAAG;YACpB,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,IAAI,CAAC,iBAAiB,EAAE;YACvE,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAChD,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAG,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YACtF,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACpD,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1D,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACpD,EAAE,IAAI,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;SAC3D,CAAA;QAED,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QAE1E,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;QAEnF,MAAM,qBAAqB,GAAG,oBAAoB;aAC/C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC/C,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;QAE3C,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,EAAE,EAAE;YACtF,MAAM,uBAAuB,GAAG,qBAAqB,CAAC,IAAI,CACxD,CAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,EAAE,EAAE,CAAC,wBAAwB,KAAK,oBAAoB,CAC1F,CAAA;YAED,OAAO,CAAC,uBAAuB,CAAA;QACjC,CAAC,CAAC,CAAA;QAEF,OAAO,KAAK,CAAC,IAAI,CACf,IAAI,GAAG,CAAC,CAAC,GAAG,qBAAqB,EAAE,GAAG,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CACpF,CAAA;IACH,CAAC;CACF,CAAA"}