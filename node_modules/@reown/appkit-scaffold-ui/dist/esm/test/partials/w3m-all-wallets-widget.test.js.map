{"version": 3, "file": "w3m-all-wallets-widget.test.js", "sourceRoot": "", "sources": ["../../../../test/partials/w3m-all-wallets-widget.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAA;AAC1C,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAA;AAEvE,OAAO,EAAE,IAAI,EAAE,MAAM,KAAK,CAAA;AAE1B,OAAO,EACL,aAAa,EAEb,mBAAmB,EAGnB,cAAc,EACd,gBAAgB,EAChB,iBAAiB,EAIjB,gBAAgB,EAEjB,MAAM,2BAA2B,CAAA;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAA;AAC/E,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAA;AAGlD,MAAM,mBAAmB,GAAG,aAAa,CAAA;AACzC,MAAM,iBAAiB,GAAG,eAAe,CAAA;AAEzC,MAAM,kBAAkB,GAA6B;IACnD,UAAU,EAAE,EAAE;IACd,eAAe,EAAE,SAAS;IAC1B,aAAa,EAAE,EAAE;IACjB,iBAAiB,EAAE,SAAS;IAC5B,kBAAkB,EAAE;QAClB,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;KAClB;IACD,oBAAoB,EAAE;QACpB,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;KACb;CACF,CAAA;AAED,MAAM,gBAAgB,GAA4D;IAChF,UAAU,EAAE,MAAe;IAC3B,SAAS,EAAE,iBAAiB;IAC5B,UAAU,EAAE,OAAqB;IACjC,OAAO,EAAE,QAAiB;IAC1B,mBAAmB,EAAE,EAA2B;CACjD,CAAA;AAED,MAAM,aAAa,GAA2B;IAC5C,EAAE,EAAE,iBAAiB;IACrB,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE,eAAe;IACrB,KAAK,EAAE,QAAQ;CAChB,CAAA;AAED,MAAM,eAAe,GAAG;IACtB;QACE,EAAE,EAAE,GAAG;QACP,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,kBAAkB;QAC5B,SAAS,EAAE,4BAA4B;KACxC;CACF,CAAA;AAED,MAAM,YAAY,GAAuB;IACvC,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,QAAQ,EAAE,eAAe;IACzB,WAAW,EAAE,eAAe;IAC5B,cAAc,EAAE,EAAE;IAClB,QAAQ,EAAE,EAAE;IACZ,WAAW,EAAE,EAAE;IACf,OAAO,EAAE,EAAE;IACX,MAAM,EAAE,EAAE;IACV,kBAAkB,EAAE,KAAK;IACzB,eAAe,EAAE,EAAE;IACnB,4BAA4B,EAAE,KAAK;IACnC,eAAe,EAAE,EAAE;CACpB,CAAA;AAED,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;IAC7D,CAAC,CAAC,CAAA;IAEF,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,aAAa,EAAE,CAAA;IACpB,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;QACzE,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAA;QAEjF,MAAM,OAAO,GAAwB,MAAM,OAAO,CAChD,IAAI,CAAA,mDAAmD,CACxD,CAAA;QAED,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;IAC1E,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;QAC9D,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YAC5D,GAAG,kBAAkB;YACrB,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QACF,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YAC1D,UAAU,EAAE,MAAe;SAC+B,CAAC,CAAA;QAE7D,MAAM,OAAO,GAAwB,MAAM,OAAO,CAChD,IAAI,CAAA,mDAAmD,CACxD,CAAA;QAED,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;IAC1E,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,kEAAkE,EAAE,KAAK,IAAI,EAAE;QAChF,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YAC5D,GAAG,kBAAkB;YACrB,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QACF,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YAC1D,UAAU,EAAE,aAAsB;SACwB,CAAC,CAAA;QAC7D,EAAE,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAE3D,MAAM,OAAO,GAAwB,MAAM,OAAO,CAChD,IAAI,CAAA,mDAAmD,CACxD,CAAA;QAED,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;IAC1E,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;QAC3D,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YAC5D,GAAG,kBAAkB;YACrB,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QACF,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAA;QAC7E,EAAE,CAAC,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;QAErE,MAAM,OAAO,GAAwB,MAAM,OAAO,CAChD,IAAI,CAAA,mDAAmD,CACxD,CAAA;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAA;QACxE,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAA;QACjC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;QAC3E,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YAC5D,GAAG,kBAAkB;YACrB,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QACF,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAA;QAC7E,EAAE,CAAC,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;QAErE,MAAM,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;QACxD,MAAM,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAA;QAE5D,MAAM,OAAO,GAAwB,MAAM,OAAO,CAChD,IAAI,CAAA,mDAAmD,CACxD,CAAA;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAA;QACxE,UAAU,CAAC,KAAK,EAAE,CAAA;QAElB,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAA;QACxF,MAAM,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAA;IAC1D,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;QACvE,MAAM,sBAAsB,GAAG;YAC7B;gBACE,EAAE,EAAE,GAAG;gBACP,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,sBAAsB;gBAChC,SAAS,EAAE,gCAAgC;aAC5C;SACF,CAAA;QAED,MAAM,QAAQ,GAAG;YACf,GAAG,YAAY;YACf,eAAe,EAAE,sBAAsB;SACxC,CAAA;QAED,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YAC5D,GAAG,kBAAkB;YACrB,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QACF,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAA;QAC7E,EAAE,CAAC,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QAEjE,MAAM,OAAO,GAAwB,MAAM,OAAO,CAChD,IAAI,CAAA,mDAAmD,CACxD,CAAA;QAED,IAAI,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAA;QACtE,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAErD,QAAQ,CAAC,eAAe,GAAG;YACzB,GAAG,sBAAsB;YACzB;gBACE,EAAE,EAAE,GAAG;gBACP,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,qBAAqB;gBAC/B,SAAS,EAAE,+BAA+B;aAC3C;SACF,CAAA;QAGD,OAAO,CAAC,aAAa,EAAE,CAAA;QACvB,MAAM,OAAO,CAAC,cAAc,CAAA;QAE5B,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAA;QAClE,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;QAC5E,EAAE,CAAC,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YAC5D,GAAG,kBAAkB;YACrB,UAAU,EAAE,CAAC,aAAa,CAAC;SAC5B,CAAC,CAAA;QACF,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAA;QAC7E,EAAE,CAAC,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,eAAe,CAAC;YACtD,GAAG,YAAY;YACf,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,eAAe;YACzB,eAAe,EAAE,EAAE;SACpB,CAAC,CAAA;QAEF,MAAM,OAAO,GAAwB,MAAM,OAAO,CAChD,IAAI,CAAA,mDAAmD,CACxD,CAAA;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAA;QACxE,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAA;QACjC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACzD,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA"}