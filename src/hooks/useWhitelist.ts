'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';

interface WhitelistData {
  address: string;
  type: 'whitelist' | 'student';
  isEligible: boolean;
  merkleProof: string[];
  merkleRoot: string;
}

interface UseWhitelistReturn {
  whitelistData: WhitelistData | null;
  studentData: WhitelistData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useWhitelist(): UseWhitelistReturn {
  const { address } = useAccount();
  const [whitelistData, setWhitelistData] = useState<WhitelistData | null>(null);
  const [studentData, setStudentData] = useState<WhitelistData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchWhitelistStatus = async () => {
    if (!address) {
      setWhitelistData(null);
      setStudentData(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 并行检查白名单和学生名单
      const [whitelistResponse, studentResponse] = await Promise.all([
        fetch(`/api/whitelist?address=${address}&type=whitelist`),
        fetch(`/api/whitelist?address=${address}&type=student`),
      ]);

      if (!whitelistResponse.ok || !studentResponse.ok) {
        throw new Error('获取白名单状态失败');
      }

      const whitelistResult = await whitelistResponse.json();
      const studentResult = await studentResponse.json();

      setWhitelistData(whitelistResult);
      setStudentData(studentResult);
    } catch (err) {
      console.error('Whitelist fetch error:', err);
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchWhitelistStatus();
  }, [address]);

  return {
    whitelistData,
    studentData,
    isLoading,
    error,
    refetch: fetchWhitelistStatus,
  };
}

// 单独的hook用于获取特定类型的白名单状态
export function useWhitelistStatus(type: 'whitelist' | 'student') {
  const { whitelistData, studentData, isLoading, error } = useWhitelist();
  
  const data = type === 'whitelist' ? whitelistData : studentData;
  
  return {
    isEligible: data?.isEligible || false,
    merkleProof: data?.merkleProof || [],
    merkleRoot: data?.merkleRoot || '',
    isLoading,
    error,
  };
}
