import { getDefaultConfig } from '@rainbow-me/rainbowkit';
import { mainnet, sepolia, hardhat } from 'wagmi/chains';

// 合约地址配置
export const CONTRACT_ADDRESSES = {
  // 在部署后更新这些地址
  AKASHA_NFT: process.env.NEXT_PUBLIC_AKASHA_NFT_ADDRESS || '',
  ACCESS_CONTROL: process.env.NEXT_PUBLIC_ACCESS_CONTROL_ADDRESS || '',
  MERKLE_GENERATOR: process.env.NEXT_PUBLIC_MERKLE_GENERATOR_ADDRESS || '',
} as const;

// 支持的网络
export const SUPPORTED_CHAINS = [
  hardhat,
  sepolia,
  ...(process.env.NODE_ENV === 'production' ? [mainnet] : []),
];

// Wagmi配置
export const config = getDefaultConfig({
  appName: 'AkashaDao Community Pass',
  projectId: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'demo-project-id',
  chains: SUPPORTED_CHAINS as any,
  ssr: true,
});

// NFT层级枚举
export enum NFTTier {
  WHITELIST = 0,
  STUDENT = 1,
}

// 访问权限类型
export enum AccessType {
  UNLIMITED_READING = 0,
  VOTING_RIGHTS = 1,
  EXCLUSIVE_EVENTS = 2,
  VC_NETWORKING = 3,
}

// NFT信息接口
export interface NFTInfo {
  tier: NFTTier;
  mintTime: bigint;
  hasVotingRights: boolean;
  hasUnlimitedAccess: boolean;
}

// 价格配置
export const PRICES = {
  WHITELIST: '0.05', // ETH
  PUBLIC: '0.08',    // ETH
  STUDENT: '0.03',   // ETH
} as const;

// 供应量配置
export const SUPPLY_LIMITS = {
  WHITELIST_TOTAL: 100,
  WHITELIST_PERCENTAGE: 80, // 80%
  LOTTERY_PERCENTAGE: 20,   // 20%
  STUDENT_TOTAL: 500,
} as const;

// 合约ABI片段（主要函数）
export const AKASHA_NFT_ABI = [
  // 查询函数
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function totalSupply() view returns (uint256)',
  'function balanceOf(address owner) view returns (uint256)',
  'function ownerOf(uint256 tokenId) view returns (address)',
  'function tokenURI(uint256 tokenId) view returns (string)',
  'function getNFTInfo(uint256 tokenId) view returns (tuple(uint8 tier, uint256 mintTime, bool hasVotingRights, bool hasUnlimitedAccess))',
  
  // 价格查询
  'function whitelistPrice() view returns (uint256)',
  'function publicPrice() view returns (uint256)',
  'function studentPrice() view returns (uint256)',
  
  // 供应量查询
  'function whitelistMinted() view returns (uint256)',
  'function studentMinted() view returns (uint256)',
  'function remainingWhitelistSupply() view returns (uint256)',
  'function remainingLotterySupply() view returns (uint256)',
  'function remainingStudentSupply() view returns (uint256)',
  
  // 销售状态查询
  'function whitelistSaleActive() view returns (bool)',
  'function lotterySaleActive() view returns (bool)',
  'function studentSaleActive() view returns (bool)',
  
  // Mint函数
  'function whitelistMint(bytes32[] calldata merkleProof) payable',
  'function lotteryMint(bytes32[] calldata merkleProof) payable',
  'function studentMint(bytes32[] calldata merkleProof) payable',
  
  // 权限查询
  'function hasVotingRights(uint256 tokenId) view returns (bool)',
  'function hasUnlimitedAccess(uint256 tokenId) view returns (bool)',
  
  // 事件
  'event WhitelistMint(address indexed user, uint256 indexed tokenId)',
  'event LotteryMint(address indexed user, uint256 indexed tokenId)',
  'event StudentMint(address indexed user, uint256 indexed tokenId)',
] as const;

export const ACCESS_CONTROL_ABI = [
  // 访问权限验证
  'function hasAccess(address user, uint8 accessType) view returns (bool)',
  'function isWhitelistMember(address user) view returns (bool)',
  'function isStudentMember(address user) view returns (bool)',
  
  // 活动管理
  'function createEvent(string memory name, string memory description, uint256 startTime, uint256 endTime, bool requiresWhitelist)',
  'function participateInEvent(uint256 eventId)',
  'function eventCounter() view returns (uint256)',
  
  // 提案和投票
  'function createProposal(string memory title, string memory description, uint256 votingPeriod)',
  'function vote(uint256 proposalId, bool support)',
  'function getProposalResults(uint256 proposalId) view returns (uint256 forVotes, uint256 againstVotes)',
  'function proposalCounter() view returns (uint256)',
  
  // 访问时间更新
  'function updateAccessTime(address user)',
  'function lastAccessTime(address user) view returns (uint256)',
  
  // 事件
  'event EventCreated(uint256 indexed eventId, string name)',
  'event EventParticipation(uint256 indexed eventId, address indexed user)',
  'event ProposalCreated(uint256 indexed proposalId, string title)',
  'event VoteCast(uint256 indexed proposalId, address indexed voter, bool support)',
] as const;

export const MERKLE_GENERATOR_ABI = [
  // Merkle Root管理
  'function getMerkleRoot(string memory listType) view returns (bytes32)',
  'function setMerkleRoot(string memory listType, bytes32 root)',
  
  // 地址验证
  'function verifyAddress(string memory listType, address user, bytes32[] calldata merkleProof) view returns (bool)',
  'function verifyAddresses(string memory listType, address[] calldata users, bytes32[][] calldata merkleProofs) view returns (bool[])',
  
  // 验证状态
  'function isVerified(string memory listType, address user) view returns (bool)',
  'function markAsVerified(string memory listType, address user, bytes32[] calldata merkleProof)',
  
  // 工具函数
  'function generateLeaf(address user) pure returns (bytes32)',
  'function generateLeaves(address[] calldata users) pure returns (bytes32[])',
  
  // 事件
  'event MerkleRootSet(string indexed listType, bytes32 root)',
  'event AddressVerified(string indexed listType, address indexed user)',
] as const;

// 错误处理工具
export class Web3Error extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'Web3Error';
  }
}

// 格式化ETH数量
export function formatEther(value: bigint): string {
  return (Number(value) / 1e18).toFixed(4);
}

// 解析ETH数量
export function parseEther(value: string): bigint {
  return BigInt(Math.floor(parseFloat(value) * 1e18));
}

// 截断地址显示
export function truncateAddress(address: string): string {
  if (!address) return '';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

// 获取网络名称
export function getNetworkName(chainId: number): string {
  switch (chainId) {
    case 1:
      return 'Mainnet';
    case 11155111:
      return 'Sepolia';
    case 31337:
      return 'Hardhat';
    default:
      return 'Unknown';
  }
}

// 检查是否为支持的网络
export function isSupportedChain(chainId: number): boolean {
  return SUPPORTED_CHAINS.some(chain => chain.id === chainId);
}
