'use client';

import { useState, useEffect } from 'react';
import { useReadContract } from 'wagmi';
import { CONTRACT_ADDRESSES, AKASHA_NFT_ABI, NFTTier, NFTInfo } from '@/lib/web3';
import Image from 'next/image';

interface NFTCardProps {
  tokenId: number;
  showDetails?: boolean;
}

export function NFTCard({ tokenId, showDetails = true }: NFTCardProps) {
  const [imageError, setImageError] = useState(false);

  // 读取NFT信息
  const { data: nftInfo } = useReadContract({
    address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
    abi: AKASHA_NFT_ABI,
    functionName: 'getNFTInfo',
    args: [BigInt(tokenId)],
  }) as { data: NFTInfo | undefined };

  // 读取tokenURI
  const { data: tokenURI } = useReadContract({
    address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
    abi: AKASHA_NFT_ABI,
    functionName: 'tokenURI',
    args: [BigInt(tokenId)],
  });

  // 读取owner
  const { data: owner } = useReadContract({
    address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
    abi: AKASHA_NFT_ABI,
    functionName: 'ownerOf',
    args: [BigInt(tokenId)],
  });

  const getTierInfo = (tier: NFTTier) => {
    switch (tier) {
      case NFTTier.WHITELIST:
        return {
          name: '白名单成员',
          color: 'from-amber-500 to-amber-600',
          bgColor: 'bg-amber-50 dark:bg-amber-900/20',
          textColor: 'text-amber-800 dark:text-amber-200',
          image: '/images/whitelist-nft.png',
        };
      case NFTTier.STUDENT:
        return {
          name: '学生成员',
          color: 'from-blue-500 to-blue-600',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          textColor: 'text-blue-800 dark:text-blue-200',
          image: '/images/student-nft.png',
        };
      default:
        return {
          name: '未知层级',
          color: 'from-gray-500 to-gray-600',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20',
          textColor: 'text-gray-800 dark:text-gray-200',
          image: '/images/default-nft.png',
        };
    }
  };

  if (!nftInfo) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 animate-pulse">
        <div className="h-64 bg-gray-300 dark:bg-gray-600"></div>
        <div className="p-6">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  const tierInfo = getTierInfo(nftInfo.tier);
  const mintDate = new Date(Number(nftInfo.mintTime) * 1000);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-shadow duration-300">
      {/* NFT图片 */}
      <div className="relative h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
        {!imageError ? (
          <Image
            src={tierInfo.image}
            alt={`${tierInfo.name} NFT #${tokenId}`}
            fill
            className="object-cover"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-gray-500 dark:text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                NFT #{tokenId}
              </p>
            </div>
          </div>
        )}
        
        {/* 层级标签 */}
        <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium ${tierInfo.bgColor} ${tierInfo.textColor}`}>
          {tierInfo.name}
        </div>
      </div>

      {/* NFT详情 */}
      {showDetails && (
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              AkashaDao Pass #{tokenId}
            </h3>
            <div className={`px-2 py-1 rounded text-xs font-medium ${tierInfo.bgColor} ${tierInfo.textColor}`}>
              {tierInfo.name}
            </div>
          </div>

          {/* 权益信息 */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">投票权</span>
              <span className={`font-medium ${nftInfo.hasVotingRights ? 'text-green-600' : 'text-red-600'}`}>
                {nftInfo.hasVotingRights ? '✓' : '✗'}
              </span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">无限访问</span>
              <span className={`font-medium ${nftInfo.hasUnlimitedAccess ? 'text-green-600' : 'text-red-600'}`}>
                {nftInfo.hasUnlimitedAccess ? '✓' : '✗'}
              </span>
            </div>
          </div>

          {/* 铸造信息 */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              铸造时间
            </div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {mintDate.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              })}
            </div>
          </div>

          {/* 持有者信息 */}
          {owner && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                持有者
              </div>
              <div className="text-sm font-mono text-gray-900 dark:text-white">
                {owner.slice(0, 6)}...{owner.slice(-4)}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// NFT网格展示组件
interface NFTGridProps {
  tokenIds: number[];
  loading?: boolean;
}

export function NFTGrid({ tokenIds, loading = false }: NFTGridProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 animate-pulse"
          >
            <div className="h-64 bg-gray-300 dark:bg-gray-600"></div>
            <div className="p-6">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mb-4"></div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (tokenIds.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-gray-400 dark:text-gray-500"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          暂无NFT
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          您还没有任何AkashaDao社区通行证
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {tokenIds.map((tokenId) => (
        <NFTCard key={tokenId} tokenId={tokenId} />
      ))}
    </div>
  );
}
