<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Akasha<PERSON>ao - 全新奢华NFT铸造页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(139, 92, 246, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(139, 92, 246, 0.8)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen relative">
    <!-- 奢华背景系统 -->
    <div class="fixed inset-0 z-0">
        <!-- 主背景 -->
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
             style="background-image: url('/images/luxury-mint-background.webp');"></div>
        
        <!-- 深色叠加层 -->
        <div class="absolute inset-0 bg-black/60"></div>
        
        <!-- 渐变装饰 -->
        <div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-amber-900/20"></div>
        
        <!-- 动态光效 -->
        <div class="absolute top-20 left-20 w-72 h-72 bg-purple-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 right-20 w-96 h-96 bg-amber-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
    </div>

    <!-- 简洁Header -->
    <header class="relative z-20 flex justify-between items-center p-6 bg-black/30 backdrop-blur-xl">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-amber-500 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-lg">A</span>
            </div>
            <span class="text-white font-bold text-xl">AkashaDao</span>
        </div>
        <button class="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-300">
            Connect Wallet
        </button>
    </header>

    <!-- 主要内容区域 -->
    <div class="relative z-10 min-h-screen flex items-center">
        <div class="container mx-auto px-6">
            <div class="max-w-7xl mx-auto">
                
                <!-- 顶部标题区域 -->
                <div class="text-center mb-16">
                    <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">
                        <span class="bg-gradient-to-r from-amber-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                            AKASHA DAO
                        </span>
                    </h1>
                    <p class="text-xl text-white/70 mb-8">Exclusive Community Membership Pass</p>
                    
                    <!-- 状态指示器 -->
                    <div class="flex justify-center items-center space-x-6 text-sm">
                        <div class="flex items-center space-x-2 px-4 py-2 bg-green-500/10 rounded-full border border-green-500/20">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-green-400 font-medium">Live Mint</span>
                        </div>
                        <div class="flex items-center space-x-2 px-4 py-2 bg-purple-500/10 rounded-full border border-purple-500/20">
                            <span class="text-purple-400 font-medium">450/500 Minted</span>
                        </div>
                    </div>
                </div>

                <!-- 主要布局 - 居中卡片式设计 -->
                <div class="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
                    
                    <!-- 左侧 - NFT展示 -->
                    <div class="space-y-8">
                        <!-- NFT预览卡片 -->
                        <div class="relative group">
                            <!-- 发光效果 -->
                            <div class="absolute -inset-1 bg-gradient-to-r from-amber-500/50 via-purple-500/50 to-cyan-500/50 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000"></div>
                            
                            <div class="relative bg-black/60 backdrop-blur-xl rounded-2xl p-8 border border-white/20">
                                <!-- 顶部选择器 -->
                                <div class="flex justify-center mb-8">
                                    <div class="flex bg-black/40 rounded-xl p-1 border border-white/10">
                                        <button class="px-6 py-3 rounded-lg font-medium bg-gradient-to-r from-amber-500 to-orange-500 text-black shadow-lg">
                                            Whitelist
                                        </button>
                                        <button class="px-6 py-3 rounded-lg font-medium text-white/70 hover:text-white hover:bg-white/10">
                                            Student
                                        </button>
                                    </div>
                                </div>

                                <!-- NFT预览 -->
                                <div class="relative mb-8">
                                    <div class="aspect-square bg-gradient-to-br from-amber-500/20 to-purple-500/20 rounded-2xl border border-white/20 flex items-center justify-center">
                                        <div class="text-center">
                                            <div class="w-24 h-24 bg-gradient-to-r from-amber-500 to-purple-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                                                <span class="text-white font-bold text-2xl">A</span>
                                            </div>
                                            <h3 class="text-white font-bold text-xl mb-2">Whitelist Pass</h3>
                                            <p class="text-white/60 text-sm">Premium Membership</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 权益展示 -->
                                <div class="pt-8 border-t border-white/10">
                                    <h3 class="text-lg font-bold text-white mb-6 text-center">
                                        Whitelist Holder Benefits
                                    </h3>
                                    
                                    <div class="grid grid-cols-1 gap-3">
                                        <div class="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                                            <div class="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                                            <span class="text-white/90 text-sm">投票权和治理参与</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                                            <div class="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                                            <span class="text-white/90 text-sm">无限制内容访问权限</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                                            <div class="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                                            <span class="text-white/90 text-sm">优先客户支持</span>
                                        </div>
                                        <div class="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                                            <div class="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                                            <span class="text-white/90 text-sm">独家活动和空投</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧 - 铸造界面 -->
                    <div class="space-y-8">
                        <!-- 主要铸造卡片 -->
                        <div class="relative group">
                            <!-- 发光效果 -->
                            <div class="absolute -inset-1 bg-gradient-to-r from-purple-500/50 via-pink-500/50 to-cyan-500/50 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000"></div>
                            
                            <div class="relative bg-black/60 backdrop-blur-xl rounded-2xl p-8 border border-white/20">
                                <div class="text-center mb-8">
                                    <h2 class="text-2xl font-bold text-white mb-2">Mint Your Pass</h2>
                                    <p class="text-white/60">Join the exclusive AkashaDao community</p>
                                </div>

                                <!-- 铸造表单 -->
                                <div class="space-y-6">
                                    <div>
                                        <label class="block text-sm font-medium text-white/80 mb-3">数量</label>
                                        <div class="flex items-center space-x-4">
                                            <button class="w-12 h-12 bg-white/10 hover:bg-white/20 rounded-lg flex items-center justify-center text-white font-bold transition-all">-</button>
                                            <div class="flex-1 text-center">
                                                <input type="number" value="1" class="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white text-center font-medium focus:outline-none focus:border-purple-500">
                                            </div>
                                            <button class="w-12 h-12 bg-white/10 hover:bg-white/20 rounded-lg flex items-center justify-center text-white font-bold transition-all">+</button>
                                        </div>
                                    </div>

                                    <div class="bg-white/5 rounded-lg p-4 border border-white/10">
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="text-white/70">价格</span>
                                            <span class="text-white font-medium">0.05 ETH</span>
                                        </div>
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="text-white/70">Gas费</span>
                                            <span class="text-white font-medium">~0.003 ETH</span>
                                        </div>
                                        <div class="border-t border-white/10 pt-2 mt-2">
                                            <div class="flex justify-between items-center">
                                                <span class="text-white font-medium">总计</span>
                                                <span class="text-white font-bold text-lg">0.053 ETH</span>
                                            </div>
                                        </div>
                                    </div>

                                    <button class="w-full py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold rounded-xl hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300">
                                        Mint Now
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 铸造阶段信息 -->
                        <div class="relative group">
                            <div class="absolute -inset-1 bg-gradient-to-r from-green-500/30 to-blue-500/30 rounded-2xl blur opacity-20"></div>
                            
                            <div class="relative bg-black/60 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                <h3 class="text-lg font-bold text-white mb-6 text-center">Mint Phases</h3>
                                
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-4 bg-green-500/10 rounded-xl border border-green-500/20">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                                            <div>
                                                <span class="text-white font-medium text-sm">Whitelist Phase</span>
                                                <p class="text-green-400 text-xs">Active Now</p>
                                            </div>
                                        </div>
                                        <span class="px-2 py-1 bg-green-500/20 text-green-400 rounded-full text-xs font-medium">LIVE</span>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-4 bg-yellow-500/10 rounded-xl border border-yellow-500/20">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-yellow-400 rounded-full mr-3"></div>
                                            <div>
                                                <span class="text-white font-medium text-sm">Student Phase</span>
                                                <p class="text-yellow-400 text-xs">Coming Soon</p>
                                            </div>
                                        </div>
                                        <span class="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium">SOON</span>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-white/30 rounded-full mr-3"></div>
                                            <div>
                                                <span class="text-white font-medium text-sm">Public Phase</span>
                                                <p class="text-white/40 text-xs">To Be Announced</p>
                                            </div>
                                        </div>
                                        <span class="px-2 py-1 bg-white/10 text-white/60 rounded-full text-xs font-medium">TBA</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 统计信息 -->
                        <div class="relative group">
                            <div class="absolute -inset-1 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-2xl blur opacity-20"></div>
                            
                            <div class="relative bg-black/60 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                                <h3 class="text-lg font-bold text-white mb-6 text-center">Live Stats</h3>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center p-4 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-500/20">
                                        <div class="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-1">450</div>
                                        <div class="text-xs text-white/60">Total Minted</div>
                                    </div>
                                    <div class="text-center p-4 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-xl border border-cyan-500/20">
                                        <div class="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1">50</div>
                                        <div class="text-xs text-white/60">Remaining</div>
                                    </div>
                                    <div class="text-center p-4 bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-xl border border-amber-500/20">
                                        <div class="text-2xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-1">0.05</div>
                                        <div class="text-xs text-white/60">ETH Price</div>
                                    </div>
                                    <div class="text-center p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-xl border border-green-500/20">
                                        <div class="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-1">90%</div>
                                        <div class="text-xs text-white/60">Progress</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
