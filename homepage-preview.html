<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Akasha<PERSON><PERSON> - 全新官网预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'spin-slow': 'spin 3s linear infinite',
                        'float': 'float 3s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'pulse-slow': 'pulse 3s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(168, 85, 247, 0.4)' },
                            '100%': { boxShadow: '0 0 40px rgba(168, 85, 247, 0.8)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-text {
            background: linear-gradient(45deg, #8b5cf6, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-x-hidden">
    <!-- 导航栏 -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold text-lg">A</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-white">AkashaDao</h1>
                        <span class="text-xs text-purple-300">Community Pass</span>
                    </div>
                </div>
                
                <!-- 导航菜单 -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-slate-300 hover:text-white transition-colors">特性</a>
                    <a href="#benefits" class="text-slate-300 hover:text-white transition-colors">权益</a>
                    <a href="#roadmap" class="text-slate-300 hover:text-white transition-colors">路线图</a>
                    <a href="#community" class="text-slate-300 hover:text-white transition-colors">社区</a>
                </div>

                <div class="flex items-center space-x-4">
                    <button class="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-medium transition-all transform hover:scale-105 shadow-lg">
                        铸造 NFT
                    </button>
                    <button class="px-4 py-2 border border-slate-600 hover:border-purple-500 text-white rounded-lg transition-colors">
                        连接钱包
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center pt-20">
        <!-- AI生成的背景图片 -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40"
                 style="background-image: url('public/images/hero-background.webp'); background-blend-mode: overlay;"></div>
            <!-- 保留一些动态效果作为补充 -->
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
            <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
            <!-- 几何装饰元素 -->
            <div class="absolute top-10 right-10 w-32 h-32 opacity-20"
                 style="background-image: url('public/images/geometric-decoration.webp'); background-size: contain; background-repeat: no-repeat;"></div>
            <div class="absolute bottom-20 left-10 w-24 h-24 opacity-15 animate-float"
                 style="background-image: url('public/images/geometric-decoration.webp'); background-size: contain; background-repeat: no-repeat; transform: rotate(45deg);"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- 左侧内容 -->
                <div class="space-y-8">
                    <div class="space-y-4">
                        <div class="inline-flex items-center px-4 py-2 bg-purple-500/20 border border-purple-500/30 rounded-full text-purple-300 text-sm font-medium">
                            🚀 Web3社区的未来
                        </div>
                        <h1 class="text-5xl lg:text-7xl font-bold text-white leading-tight">
                            AkashaDao
                            <span class="block gradient-text">
                                Community Pass
                            </span>
                        </h1>
                        <p class="text-xl text-slate-300 max-w-2xl leading-relaxed">
                            获得专属NFT通行证，解锁Web3世界的无限可能。
                            享受无限制内容访问、社区治理权、专属活动和顶级VC网络连接。
                        </p>
                    </div>

                    <!-- CTA按钮 -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-bold text-lg transition-all transform hover:scale-105 shadow-2xl">
                            立即铸造 NFT
                            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                        </button>
                        <button class="inline-flex items-center justify-center px-8 py-4 border-2 border-slate-600 hover:border-purple-500 text-white rounded-xl font-bold text-lg transition-all hover:bg-purple-500/10">
                            了解更多
                        </button>
                    </div>

                    <!-- 统计数据 -->
                    <div class="grid grid-cols-3 gap-8 pt-8">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white">3000+</div>
                            <div class="text-slate-400 text-sm">社区成员</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white">500+</div>
                            <div class="text-slate-400 text-sm">NFT持有者</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white">50+</div>
                            <div class="text-slate-400 text-sm">合作伙伴</div>
                        </div>
                    </div>
                </div>

                <!-- 右侧NFT展示 -->
                <div class="relative">
                    <div class="relative w-full max-w-md mx-auto">
                        <div class="aspect-square bg-gradient-to-br from-orange-400 to-yellow-500 rounded-3xl shadow-2xl overflow-hidden relative">
                            <!-- NFT内容 -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center text-white">
                                    <div class="w-32 h-32 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                                        <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-spin-slow"></div>
                                    </div>
                                    <div class="bg-black/30 backdrop-blur-sm rounded-lg p-4 mx-4">
                                        <div class="text-lg font-bold">AkashaDao Pass</div>
                                        <div class="text-sm opacity-80">Premium Access</div>
                                    </div>
                                </div>
                            </div>
                            <!-- WHITELIST标签 -->
                            <div class="absolute top-4 right-4 bg-amber-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                                WHITELIST
                            </div>
                        </div>
                        
                        <!-- 浮动元素 */
                        <div class="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full opacity-20 animate-bounce"></div>
                        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full opacity-20 animate-bounce" style="animation-delay: 1s;"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 特性展示预览 -->
    <section class="py-24 relative overflow-hidden">
        <!-- AI生成的特性背景 -->
        <div class="absolute inset-0">
            <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
                 style="background-image: url('public/images/features-background.webp'); background-blend-mode: multiply;"></div>
            <div class="absolute inset-0 bg-gradient-to-b from-slate-900/50 to-slate-900/80"></div>
        </div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-white mb-4">
                    为什么选择 AkashaDao Pass？
                </h2>
                <p class="text-xl text-slate-300 max-w-3xl mx-auto">
                    不仅仅是NFT，更是通往Web3未来的钥匙
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 特性卡片示例 -->
                <div class="group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">无限制内容访问</h3>
                    <p class="text-slate-300">
                        解锁所有付费内容、研究报告和独家分析，随时随地获取最新的Web3资讯和深度见解。
                    </p>
                </div>

                <div class="group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">社区治理权</h3>
                    <p class="text-slate-300">
                        参与社区重要决策投票，影响AkashaDao的发展方向，真正成为社区的主人。
                    </p>
                </div>

                <div class="group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105">
                    <div class="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-4">VC网络连接</h3>
                    <p class="text-slate-300">
                        直接接触顶级投资人和创业者，获得投资机会和商业合作的第一手信息。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚预览 -->
    <footer class="bg-slate-900 border-t border-slate-800 mt-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-4 mb-4">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold text-lg">A</span>
                    </div>
                    <div>
                        <h3 class="text-2xl font-bold text-white">AkashaDao</h3>
                        <span class="text-sm text-purple-300">Community Pass</span>
                    </div>
                </div>
                <p class="text-slate-400 max-w-md mx-auto mb-8">
                    AkashaDao是一个专注于Web3教育和社区建设的去中心化自治组织
                </p>
                <p class="text-slate-400 text-sm">
                    &copy; 2025 AkashaDao. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <script>
        // 简单的滚动动画
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.absolute.inset-0');
            if (parallax) {
                parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>
