<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AkashaDao - AI生成背景图片展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 1s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'float': 'float 3s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(30px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold">A</span>
                    </div>
                    <h1 class="text-xl font-bold text-white">AkashaDao - AI背景图片展示</h1>
                </div>
                <a href="homepage-preview.html" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                    查看完整官网
                </a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- 标题 -->
        <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-4xl font-bold text-white mb-4">
                🎨 AI生成的高质量背景图片
            </h2>
            <p class="text-xl text-slate-300 max-w-3xl mx-auto">
                使用最新的Flux-Schnell模型生成的专业级背景图片，为AkashaDao官网提供高级感的视觉体验
            </p>
        </div>

        <!-- 图片展示网格 -->
        <div class="space-y-16">
            <!-- Hero背景 -->
            <div class="animate-slide-up">
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-3xl p-8 border border-slate-700">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-white mb-2">Hero区域背景</h3>
                        <p class="text-slate-300">深色渐变配合几何图案，营造专业的Web3氛围</p>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <span class="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">16:9比例</span>
                            <span class="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">WebP格式</span>
                            <span class="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">高质量95%</span>
                        </div>
                    </div>
                    <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                        <img src="public/images/hero-background.webp" alt="Hero背景" class="w-full h-64 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-sm font-medium">hero-background.webp</div>
                            <div class="text-xs opacity-80">用于首页Hero区域</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 几何装饰 -->
            <div class="animate-slide-up" style="animation-delay: 0.2s;">
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-3xl p-8 border border-slate-700">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-white mb-2">几何装饰元素</h3>
                        <p class="text-slate-300">优雅的几何图形，用作页面装饰和浮动元素</p>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <span class="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">1:1比例</span>
                            <span class="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">透明背景</span>
                            <span class="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">装饰用途</span>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-slate-900">
                            <img src="public/images/geometric-decoration.webp" alt="几何装饰" class="w-full h-48 object-contain p-4">
                            <div class="absolute bottom-4 left-4 text-white">
                                <div class="text-sm font-medium">geometric-decoration.webp</div>
                                <div class="text-xs opacity-80">浮动装饰元素</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-center bg-slate-900 rounded-2xl p-8">
                            <div class="text-center text-slate-400">
                                <div class="w-16 h-16 mx-auto mb-4 animate-float">
                                    <img src="public/images/geometric-decoration.webp" alt="装饰示例" class="w-full h-full object-contain opacity-60">
                                </div>
                                <p class="text-sm">动画效果预览</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特性背景 -->
            <div class="animate-slide-up" style="animation-delay: 0.4s;">
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-3xl p-8 border border-slate-700">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-white mb-2">特性展示背景</h3>
                        <p class="text-slate-300">科技感电路板风格，适合技术特性展示区域</p>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <span class="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">16:9比例</span>
                            <span class="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">科技风格</span>
                            <span class="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">特性区域</span>
                        </div>
                    </div>
                    <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                        <img src="public/images/features-background.webp" alt="特性背景" class="w-full h-64 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-sm font-medium">features-background.webp</div>
                            <div class="text-xs opacity-80">用于特性展示区域</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 社区背景 -->
            <div class="animate-slide-up" style="animation-delay: 0.6s;">
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-3xl p-8 border border-slate-700">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-white mb-2">社区展示背景</h3>
                        <p class="text-slate-300">网络连接图案，体现社区互联的概念</p>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <span class="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">16:9比例</span>
                            <span class="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">网络风格</span>
                            <span class="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">社区区域</span>
                        </div>
                    </div>
                    <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                        <img src="public/images/community-background.webp" alt="社区背景" class="w-full h-64 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-sm font-medium">community-background.webp</div>
                            <div class="text-xs opacity-80">用于社区统计区域</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 路线图背景 -->
            <div class="animate-slide-up" style="animation-delay: 0.8s;">
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-3xl p-8 border border-slate-700">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-white mb-2">路线图背景</h3>
                        <p class="text-slate-300">流动线条设计，适合时间线和路线图展示</p>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <span class="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">16:9比例</span>
                            <span class="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">流线风格</span>
                            <span class="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">路线图区域</span>
                        </div>
                    </div>
                    <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                        <img src="public/images/roadmap-background.webp" alt="路线图背景" class="w-full h-64 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-sm font-medium">roadmap-background.webp</div>
                            <div class="text-xs opacity-80">用于发展路线图区域</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装饰图标 -->
            <div class="animate-slide-up" style="animation-delay: 1s;">
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-3xl p-8 border border-slate-700">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-white mb-2">装饰图标元素</h3>
                        <p class="text-slate-300">Web3主题图标集合，用于页面装饰和功能展示</p>
                        <div class="flex flex-wrap gap-2 mt-3">
                            <span class="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">1:1比例</span>
                            <span class="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">图标风格</span>
                            <span class="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">装饰用途</span>
                        </div>
                    </div>
                    <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-slate-900">
                        <img src="public/images/decorative-icons.webp" alt="装饰图标" class="w-full h-64 object-contain p-8">
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-sm font-medium">decorative-icons.webp</div>
                            <div class="text-xs opacity-80">Web3主题图标</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术说明 -->
        <div class="mt-16 bg-slate-800/30 backdrop-blur-sm rounded-3xl p-8 border border-slate-700/50">
            <h3 class="text-2xl font-bold text-white mb-6 text-center">🛠 技术规格</h3>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-purple-500/20 rounded-2xl flex items-center justify-center">
                        <span class="text-2xl">🤖</span>
                    </div>
                    <h4 class="font-bold text-white mb-2">AI模型</h4>
                    <p class="text-sm text-slate-300">Flux-Schnell<br>最新生成模型</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-blue-500/20 rounded-2xl flex items-center justify-center">
                        <span class="text-2xl">📐</span>
                    </div>
                    <h4 class="font-bold text-white mb-2">分辨率</h4>
                    <p class="text-sm text-slate-300">1024x1024<br>高清质量</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-green-500/20 rounded-2xl flex items-center justify-center">
                        <span class="text-2xl">🎨</span>
                    </div>
                    <h4 class="font-bold text-white mb-2">格式</h4>
                    <p class="text-sm text-slate-300">WebP<br>优化压缩</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-orange-500/20 rounded-2xl flex items-center justify-center">
                        <span class="text-2xl">⚡</span>
                    </div>
                    <h4 class="font-bold text-white mb-2">质量</h4>
                    <p class="text-sm text-slate-300">95%<br>专业级别</p>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="mt-12 text-center">
            <h3 class="text-xl font-bold text-white mb-4">🚀 如何使用</h3>
            <p class="text-slate-300 mb-6">这些背景图片已经集成到AkashaDao官网中，提供专业的视觉体验</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="homepage-preview.html" class="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-medium transition-all transform hover:scale-105">
                    查看完整官网效果
                </a>
                <a href="../src/app/page.tsx" class="px-6 py-3 border-2 border-slate-600 hover:border-purple-500 text-white rounded-xl font-medium transition-all hover:bg-purple-500/10">
                    查看源代码
                </a>
            </div>
        </div>
    </main>

    <script>
        // 图片懒加载
        const images = document.querySelectorAll('img');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.classList.add('animate-fade-in');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));

        // 滚动动画
        const animateElements = document.querySelectorAll('.animate-slide-up');
        const scrollObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        });

        animateElements.forEach(el => {
            el.style.animationPlayState = 'paused';
            scrollObserver.observe(el);
        });
    </script>
</body>
</html>
