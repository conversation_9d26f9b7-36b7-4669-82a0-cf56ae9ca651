<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AkashaDao NFT Mint Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes spin-slow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes glow {
            0% { box-shadow: 0 0 5px rgba(139, 92, 246, 0.5); }
            100% { box-shadow: 0 0 20px rgba(139, 92, 246, 0.8); }
        }
        .animate-spin-slow { animation: spin-slow 3s linear infinite; }
        .animate-float { animation: float 3s ease-in-out infinite; }
        .animate-glow { animation: glow 2s ease-in-out infinite alternate; }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen">
    <!-- Header -->
    <header class="flex justify-between items-center p-6 bg-slate-800/50 backdrop-blur-sm border-b border-slate-700">
        <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg"></div>
            <h1 class="text-xl font-bold text-white">AkashaDao</h1>
        </div>
        <button class="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors">
            CONNECT WALLET
        </button>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto px-6 py-12">
        <div class="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <!-- Left Side - NFT Preview -->
            <div class="space-y-6">
                <!-- Whitelist NFT Preview -->
                <div class="relative aspect-square rounded-2xl overflow-hidden">
                    <!-- Generated NFT Image -->
                    <img src="public/images/akasha-nft-whitelist.png" alt="AkashaDao Whitelist NFT" class="absolute inset-0 w-full h-full object-cover">

                    <!-- Overlay Effects -->
                    <div class="absolute inset-0 bg-gradient-to-br from-yellow-400/20 via-orange-500/20 to-red-500/20">
                        <!-- Subtle Animation Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent opacity-50 animate-pulse"></div>

                        <!-- Interactive Glow Effect -->
                        <div class="absolute inset-0 rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/50"></div>
                    </div>
                </div>

                <!-- Benefits -->
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
                    <h3 class="text-lg font-bold text-white mb-4">Whitelist Holder Benefits</h3>
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-slate-300">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            投票权和治理参与
                        </div>
                        <div class="flex items-center text-sm text-slate-300">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            无限制内容访问权限
                        </div>
                        <div class="flex items-center text-sm text-slate-300">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            优先客户支持
                        </div>
                        <div class="flex items-center text-sm text-slate-300">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            独家活动和空投
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Mint Interface -->
            <div class="space-y-6">
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
                    <h2 class="text-2xl font-bold text-white mb-6">AKASHA DAO PASS</h2>
                    
                    <!-- Tier Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-slate-300 mb-3">Status:</label>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r from-yellow-500 to-orange-500 text-black">
                                Whitelist
                            </button>
                            <button class="px-4 py-2 rounded-lg text-sm font-medium bg-slate-700 text-slate-300 hover:bg-slate-600">
                                Student
                            </button>
                        </div>
                    </div>

                    <!-- Supply and Amount -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Supply:</label>
                            <div class="text-lg font-bold text-white">0 / 3000</div>
                            <div class="text-xs text-slate-400">[Minted]</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Amount:</label>
                            <div class="flex items-center space-x-2">
                                <button class="w-8 h-8 bg-slate-700 hover:bg-slate-600 rounded-lg flex items-center justify-center text-white">-</button>
                                <span class="text-lg font-bold text-white w-8 text-center">1</span>
                                <button class="w-8 h-8 bg-slate-700 hover:bg-slate-600 rounded-lg flex items-center justify-center text-white">+</button>
                            </div>
                        </div>
                    </div>

                    <!-- Total Price -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-slate-300 mb-1">Total:</label>
                        <div class="text-2xl font-bold text-white">0.05 ETH</div>
                    </div>

                    <!-- Eligibility Status -->
                    <div class="p-4 rounded-lg border border-slate-600 bg-slate-700/50 mb-6">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-slate-300">Whitelist Status:</span>
                            <span class="text-sm text-green-400 font-medium">✅ 已验证</span>
                        </div>
                    </div>

                    <!-- Mint Buttons -->
                    <div class="space-y-3">
                        <button class="w-full py-4 rounded-xl font-bold text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all">
                            MINT & STAKE
                        </button>
                        <button class="w-full py-4 rounded-xl font-bold bg-slate-700 hover:bg-slate-600 text-white transition-all">
                            MINT
                        </button>
                    </div>
                </div>

                <!-- Phase Information -->
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
                    <h3 class="text-lg font-bold text-white mb-4">铸造阶段</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-300">Phase 1 (Whitelist):</span>
                            <span class="text-sm text-green-400">进行中</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-300">Phase 2 (Student):</span>
                            <span class="text-sm text-yellow-400">即将开始</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-300">Phase 3 (Public):</span>
                            <span class="text-sm text-slate-500">待定</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
