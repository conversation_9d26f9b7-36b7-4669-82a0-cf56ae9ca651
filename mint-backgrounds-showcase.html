<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AkashaDao - Mint页面背景图片展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .image-card {
            transition: all 0.3s ease;
        }
        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
        }
        .bg-preview {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
    <div class="container mx-auto px-6 py-12">
        <!-- 标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-white mb-4">🎨 Mint页面AI背景图片展示</h1>
            <p class="text-xl text-slate-300 mb-2">为AkashaDao NFT铸造页面生成的高级感背景图片</p>
            <p class="text-sm text-purple-300">使用Flux-Schnell AI模型生成，WebP格式，95%质量</p>
        </div>

        <!-- 背景图片网格 -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <!-- Mint主背景 -->
            <div class="image-card bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700">
                <div class="aspect-video bg-preview rounded-xl mb-4" 
                     style="background-image: url('public/images/mint-background.webp')"></div>
                <h3 class="text-xl font-bold text-white mb-2">🌟 Mint主背景</h3>
                <div class="space-y-2 text-sm text-slate-300">
                    <p><strong>用途:</strong> NFT铸造页面主背景</p>
                    <p><strong>风格:</strong> 深色奢华渐变配合区块链六边形图案</p>
                    <p><strong>规格:</strong> 16:9比例，1024px，WebP 95%</p>
                    <p><strong>特点:</strong> 深紫到午夜黑渐变，微妙六边形纹理</p>
                </div>
                <div class="mt-4 flex flex-wrap gap-2">
                    <span class="px-2 py-1 bg-purple-600/20 text-purple-300 rounded-full text-xs">主背景</span>
                    <span class="px-2 py-1 bg-blue-600/20 text-blue-300 rounded-full text-xs">高级感</span>
                    <span class="px-2 py-1 bg-green-600/20 text-green-300 rounded-full text-xs">区块链</span>
                </div>
            </div>

            <!-- NFT卡片装饰 -->
            <div class="image-card bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700">
                <div class="aspect-square bg-preview rounded-xl mb-4" 
                     style="background-image: url('public/images/nft-card-decoration.webp')"></div>
                <h3 class="text-xl font-bold text-white mb-2">💎 NFT卡片装饰</h3>
                <div class="space-y-2 text-sm text-slate-300">
                    <p><strong>用途:</strong> NFT预览卡片装饰边框</p>
                    <p><strong>风格:</strong> 奢华金属边框配合紫金渐变</p>
                    <p><strong>规格:</strong> 1:1比例，适合卡片装饰</p>
                    <p><strong>特点:</strong> 珠宝级细节，玻璃拟态效果</p>
                </div>
                <div class="mt-4 flex flex-wrap gap-2">
                    <span class="px-2 py-1 bg-yellow-600/20 text-yellow-300 rounded-full text-xs">装饰</span>
                    <span class="px-2 py-1 bg-purple-600/20 text-purple-300 rounded-full text-xs">奢华</span>
                    <span class="px-2 py-1 bg-pink-600/20 text-pink-300 rounded-full text-xs">金属质感</span>
                </div>
            </div>

            <!-- 侧边装饰 -->
            <div class="image-card bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700">
                <div class="aspect-[4/3] bg-preview rounded-xl mb-4" 
                     style="background-image: url('public/images/mint-sidebar-decoration.webp')"></div>
                <h3 class="text-xl font-bold text-white mb-2">✨ 侧边装饰</h3>
                <div class="space-y-2 text-sm text-slate-300">
                    <p><strong>用途:</strong> 页面侧边装饰元素</p>
                    <p><strong>风格:</strong> 流动数字粒子和能量流</p>
                    <p><strong>规格:</strong> 4:3比例，适合侧边栏</p>
                    <p><strong>特点:</strong> 紫蓝能量流，玻璃效果</p>
                </div>
                <div class="mt-4 flex flex-wrap gap-2">
                    <span class="px-2 py-1 bg-blue-600/20 text-blue-300 rounded-full text-xs">装饰</span>
                    <span class="px-2 py-1 bg-purple-600/20 text-purple-300 rounded-full text-xs">科技感</span>
                    <span class="px-2 py-1 bg-cyan-600/20 text-cyan-300 rounded-full text-xs">能量流</span>
                </div>
            </div>
        </div>

        <!-- 集成效果预览 -->
        <div class="bg-slate-800/30 backdrop-blur-md rounded-2xl p-8 border border-slate-700/50 mb-12">
            <h2 class="text-2xl font-bold text-white mb-6 text-center">🎯 集成效果预览</h2>
            
            <!-- 模拟mint页面布局 -->
            <div class="relative rounded-2xl overflow-hidden" style="height: 400px;">
                <!-- 背景层 -->
                <div class="absolute inset-0 bg-cover bg-center opacity-40" 
                     style="background-image: url('public/images/mint-background.webp'); background-blend-mode: overlay;"></div>
                <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
                
                <!-- 装饰元素 -->
                <div class="absolute top-4 right-4 w-16 h-16 opacity-20" 
                     style="background-image: url('public/images/nft-card-decoration.webp'); background-size: contain; background-repeat: no-repeat;"></div>
                <div class="absolute left-0 top-1/2 transform -translate-y-1/2 w-24 h-32 opacity-10" 
                     style="background-image: url('public/images/mint-sidebar-decoration.webp'); background-size: contain; background-repeat: no-repeat;"></div>
                
                <!-- 内容层 -->
                <div class="relative z-10 h-full flex items-center justify-center">
                    <div class="text-center text-white">
                        <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                            <span class="text-2xl font-bold">A</span>
                        </div>
                        <h3 class="text-2xl font-bold mb-2">AkashaDao NFT Mint</h3>
                        <p class="text-slate-300">高级感背景集成效果展示</p>
                        <div class="mt-4 inline-flex items-center px-4 py-2 bg-slate-800/50 backdrop-blur-sm rounded-lg border border-slate-600">
                            <span class="text-sm">完美融合，专业质感</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术规格 -->
        <div class="grid md:grid-cols-2 gap-8 mb-12">
            <div class="bg-slate-800/30 backdrop-blur-md rounded-2xl p-6 border border-slate-700/50">
                <h3 class="text-xl font-bold text-white mb-4">🛠️ 技术规格</h3>
                <div class="space-y-3 text-sm text-slate-300">
                    <div class="flex justify-between">
                        <span>AI模型:</span>
                        <span class="text-purple-300 font-medium">Flux-Schnell</span>
                    </div>
                    <div class="flex justify-between">
                        <span>分辨率:</span>
                        <span class="text-blue-300 font-medium">1024x1024px</span>
                    </div>
                    <div class="flex justify-between">
                        <span>格式:</span>
                        <span class="text-green-300 font-medium">WebP</span>
                    </div>
                    <div class="flex justify-between">
                        <span>质量:</span>
                        <span class="text-yellow-300 font-medium">95%</span>
                    </div>
                    <div class="flex justify-between">
                        <span>推理步数:</span>
                        <span class="text-pink-300 font-medium">4步</span>
                    </div>
                </div>
            </div>

            <div class="bg-slate-800/30 backdrop-blur-md rounded-2xl p-6 border border-slate-700/50">
                <h3 class="text-xl font-bold text-white mb-4">🎨 设计特点</h3>
                <div class="space-y-3 text-sm text-slate-300">
                    <div class="flex items-center">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                        完全不像AI生成
                    </div>
                    <div class="flex items-center">
                        <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                        高级感奢华质感
                    </div>
                    <div class="flex items-center">
                        <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                        统一紫蓝色调
                    </div>
                    <div class="flex items-center">
                        <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                        专业企业级设计
                    </div>
                    <div class="flex items-center">
                        <span class="w-2 h-2 bg-pink-400 rounded-full mr-3"></span>
                        完美融入现有设计
                    </div>
                </div>
            </div>
        </div>

        <!-- 导航按钮 -->
        <div class="flex justify-center space-x-4">
            <a href="mint-preview.html" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-medium transition-all transform hover:scale-105">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                查看Mint页面
            </a>
            <a href="homepage-preview.html" class="inline-flex items-center px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-xl font-medium transition-all">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                返回首页
            </a>
        </div>

        <!-- 版权信息 -->
        <div class="text-center mt-12 text-sm text-slate-500">
            <p>🎨 AI背景图片由Flux-Schnell模型生成 | AkashaDao © 2024</p>
        </div>
    </div>
</body>
</html>
