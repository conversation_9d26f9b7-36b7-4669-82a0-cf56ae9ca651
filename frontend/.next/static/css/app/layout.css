/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!../node_modules/@rainbow-me/rainbowkit/dist/index.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/* vanilla-extract-css-ns:src/css/reset.css.ts.vanilla.css?source=Lmlla2JjYzAgewogIGJvcmRlcjogMDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGZvbnQtc2l6ZTogMTAwJTsKICBsaW5lLWhlaWdodDogbm9ybWFsOwogIG1hcmdpbjogMDsKICBwYWRkaW5nOiAwOwogIHRleHQtYWxpZ246IGxlZnQ7CiAgdmVydGljYWwtYWxpZ246IGJhc2VsaW5lOwogIC13ZWJraXQtdGFwLWhpZ2hsaWdodC1jb2xvcjogdHJhbnNwYXJlbnQ7Cn0KLmlla2JjYzEgewogIGxpc3Qtc3R5bGU6IG5vbmU7Cn0KLmlla2JjYzIgewogIHF1b3Rlczogbm9uZTsKfQouaWVrYmNjMjpiZWZvcmUsIC5pZWtiY2MyOmFmdGVyIHsKICBjb250ZW50OiAnJzsKfQouaWVrYmNjMyB7CiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsKICBib3JkZXItc3BhY2luZzogMDsKfQouaWVrYmNjNCB7CiAgYXBwZWFyYW5jZTogbm9uZTsKfQouaWVrYmNjNSB7CiAgb3V0bGluZTogbm9uZTsKfQouaWVrYmNjNTo6cGxhY2Vob2xkZXIgewogIG9wYWNpdHk6IDE7Cn0KLmlla2JjYzYgewogIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50OwogIGNvbG9yOiBpbmhlcml0Owp9Ci5pZWtiY2M3OmRpc2FibGVkIHsKICBvcGFjaXR5OiAxOwp9Ci5pZWtiY2M3OjotbXMtZXhwYW5kIHsKICBkaXNwbGF5OiBub25lOwp9Ci5pZWtiY2M4OjotbXMtY2xlYXIgewogIGRpc3BsYXk6IG5vbmU7Cn0KLmlla2JjYzg6Oi13ZWJraXQtc2VhcmNoLWNhbmNlbC1idXR0b24gewogIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTsKfQouaWVrYmNjOSB7CiAgYmFja2dyb3VuZDogbm9uZTsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdGV4dC1hbGlnbjogbGVmdDsKfQouaWVrYmNjYSB7CiAgY29sb3I6IGluaGVyaXQ7CiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwp9 */
[data-rk] .iekbcc0 {
  border: 0;
  box-sizing: border-box;
  font-size: 100%;
  line-height: normal;
  margin: 0;
  padding: 0;
  text-align: left;
  vertical-align: baseline;
  -webkit-tap-highlight-color: transparent;
}
[data-rk] .iekbcc1 {
  list-style: none;
}
[data-rk] .iekbcc2 {
  quotes: none;
}
[data-rk] .iekbcc2:before,
[data-rk] .iekbcc2:after {
  content: "";
}
[data-rk] .iekbcc3 {
  border-collapse: collapse;
  border-spacing: 0;
}
[data-rk] .iekbcc4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
[data-rk] .iekbcc5 {
  outline: none;
}
[data-rk] .iekbcc5::-moz-placeholder {
  opacity: 1;
}
[data-rk] .iekbcc5::placeholder {
  opacity: 1;
}
[data-rk] .iekbcc6 {
  background-color: transparent;
  color: inherit;
}
[data-rk] .iekbcc7:disabled {
  opacity: 1;
}
[data-rk] .iekbcc7::-ms-expand {
  display: none;
}
[data-rk] .iekbcc8::-ms-clear {
  display: none;
}
[data-rk] .iekbcc8::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
[data-rk] .iekbcc9 {
  background: none;
  cursor: pointer;
  text-align: left;
}
[data-rk] .iekbcca {
  color: inherit;
  text-decoration: none;
}

/* vanilla-extract-css-ns:src/css/sprinkles.css.ts.vanilla.css?source=#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 */
[data-rk] .ju367v0 {
  align-items: flex-start;
}
[data-rk] .ju367v2 {
  align-items: flex-end;
}
[data-rk] .ju367v4 {
  align-items: center;
}
[data-rk] .ju367v6 {
  display: none;
}
[data-rk] .ju367v8 {
  display: block;
}
[data-rk] .ju367va {
  display: flex;
}
[data-rk] .ju367vc {
  display: inline;
}
[data-rk] .ju367ve {
  align-self: flex-start;
}
[data-rk] .ju367vf {
  align-self: flex-end;
}
[data-rk] .ju367vg {
  align-self: center;
}
[data-rk] .ju367vh {
  background-size: cover;
}
[data-rk] .ju367vi {
  border-radius: 1px;
}
[data-rk] .ju367vj {
  border-radius: 6px;
}
[data-rk] .ju367vk {
  border-radius: 10px;
}
[data-rk] .ju367vl {
  border-radius: 13px;
}
[data-rk] .ju367vm {
  border-radius: var(--rk-radii-actionButton);
}
[data-rk] .ju367vn {
  border-radius: var(--rk-radii-connectButton);
}
[data-rk] .ju367vo {
  border-radius: var(--rk-radii-menuButton);
}
[data-rk] .ju367vp {
  border-radius: var(--rk-radii-modal);
}
[data-rk] .ju367vq {
  border-radius: var(--rk-radii-modalMobile);
}
[data-rk] .ju367vr {
  border-radius: 25%;
}
[data-rk] .ju367vs {
  border-radius: 9999px;
}
[data-rk] .ju367vt {
  border-style: solid;
}
[data-rk] .ju367vu {
  border-width: 0px;
}
[data-rk] .ju367vv {
  border-width: 1px;
}
[data-rk] .ju367vw {
  border-width: 2px;
}
[data-rk] .ju367vx {
  border-width: 4px;
}
[data-rk] .ju367vy {
  cursor: pointer;
}
[data-rk] .ju367vz {
  cursor: none;
}
[data-rk] .ju367v10 {
  pointer-events: none;
}
[data-rk] .ju367v11 {
  pointer-events: all;
}
[data-rk] .ju367v12 {
  min-height: 8px;
}
[data-rk] .ju367v13 {
  min-height: 44px;
}
[data-rk] .ju367v14 {
  flex-direction: row;
}
[data-rk] .ju367v15 {
  flex-direction: column;
}
[data-rk] .ju367v16 {
  font-family: var(--rk-fonts-body);
}
[data-rk] .ju367v17 {
  font-size: 12px;
  line-height: 18px;
}
[data-rk] .ju367v18 {
  font-size: 13px;
  line-height: 18px;
}
[data-rk] .ju367v19 {
  font-size: 14px;
  line-height: 18px;
}
[data-rk] .ju367v1a {
  font-size: 16px;
  line-height: 20px;
}
[data-rk] .ju367v1b {
  font-size: 18px;
  line-height: 24px;
}
[data-rk] .ju367v1c {
  font-size: 20px;
  line-height: 24px;
}
[data-rk] .ju367v1d {
  font-size: 23px;
  line-height: 29px;
}
[data-rk] .ju367v1e {
  font-weight: 400;
}
[data-rk] .ju367v1f {
  font-weight: 500;
}
[data-rk] .ju367v1g {
  font-weight: 600;
}
[data-rk] .ju367v1h {
  font-weight: 700;
}
[data-rk] .ju367v1i {
  font-weight: 800;
}
[data-rk] .ju367v1j {
  gap: 0;
}
[data-rk] .ju367v1k {
  gap: 1px;
}
[data-rk] .ju367v1l {
  gap: 2px;
}
[data-rk] .ju367v1m {
  gap: 3px;
}
[data-rk] .ju367v1n {
  gap: 4px;
}
[data-rk] .ju367v1o {
  gap: 5px;
}
[data-rk] .ju367v1p {
  gap: 6px;
}
[data-rk] .ju367v1q {
  gap: 8px;
}
[data-rk] .ju367v1r {
  gap: 10px;
}
[data-rk] .ju367v1s {
  gap: 12px;
}
[data-rk] .ju367v1t {
  gap: 14px;
}
[data-rk] .ju367v1u {
  gap: 16px;
}
[data-rk] .ju367v1v {
  gap: 18px;
}
[data-rk] .ju367v1w {
  gap: 20px;
}
[data-rk] .ju367v1x {
  gap: 24px;
}
[data-rk] .ju367v1y {
  gap: 28px;
}
[data-rk] .ju367v1z {
  gap: 32px;
}
[data-rk] .ju367v20 {
  gap: 36px;
}
[data-rk] .ju367v21 {
  gap: 44px;
}
[data-rk] .ju367v22 {
  gap: 64px;
}
[data-rk] .ju367v23 {
  gap: -1px;
}
[data-rk] .ju367v24 {
  height: 1px;
}
[data-rk] .ju367v25 {
  height: 2px;
}
[data-rk] .ju367v26 {
  height: 4px;
}
[data-rk] .ju367v27 {
  height: 8px;
}
[data-rk] .ju367v28 {
  height: 12px;
}
[data-rk] .ju367v29 {
  height: 20px;
}
[data-rk] .ju367v2a {
  height: 24px;
}
[data-rk] .ju367v2b {
  height: 28px;
}
[data-rk] .ju367v2c {
  height: 30px;
}
[data-rk] .ju367v2d {
  height: 32px;
}
[data-rk] .ju367v2e {
  height: 34px;
}
[data-rk] .ju367v2f {
  height: 36px;
}
[data-rk] .ju367v2g {
  height: 40px;
}
[data-rk] .ju367v2h {
  height: 44px;
}
[data-rk] .ju367v2i {
  height: 48px;
}
[data-rk] .ju367v2j {
  height: 54px;
}
[data-rk] .ju367v2k {
  height: 60px;
}
[data-rk] .ju367v2l {
  height: 200px;
}
[data-rk] .ju367v2m {
  height: 100%;
}
[data-rk] .ju367v2n {
  height: -moz-max-content;
  height: max-content;
}
[data-rk] .ju367v2o {
  justify-content: flex-start;
}
[data-rk] .ju367v2p {
  justify-content: flex-end;
}
[data-rk] .ju367v2q {
  justify-content: center;
}
[data-rk] .ju367v2r {
  justify-content: space-between;
}
[data-rk] .ju367v2s {
  justify-content: space-around;
}
[data-rk] .ju367v2t {
  text-align: left;
}
[data-rk] .ju367v2u {
  text-align: center;
}
[data-rk] .ju367v2v {
  text-align: inherit;
}
[data-rk] .ju367v2w {
  margin-bottom: 0;
}
[data-rk] .ju367v2x {
  margin-bottom: 1px;
}
[data-rk] .ju367v2y {
  margin-bottom: 2px;
}
[data-rk] .ju367v2z {
  margin-bottom: 3px;
}
[data-rk] .ju367v30 {
  margin-bottom: 4px;
}
[data-rk] .ju367v31 {
  margin-bottom: 5px;
}
[data-rk] .ju367v32 {
  margin-bottom: 6px;
}
[data-rk] .ju367v33 {
  margin-bottom: 8px;
}
[data-rk] .ju367v34 {
  margin-bottom: 10px;
}
[data-rk] .ju367v35 {
  margin-bottom: 12px;
}
[data-rk] .ju367v36 {
  margin-bottom: 14px;
}
[data-rk] .ju367v37 {
  margin-bottom: 16px;
}
[data-rk] .ju367v38 {
  margin-bottom: 18px;
}
[data-rk] .ju367v39 {
  margin-bottom: 20px;
}
[data-rk] .ju367v3a {
  margin-bottom: 24px;
}
[data-rk] .ju367v3b {
  margin-bottom: 28px;
}
[data-rk] .ju367v3c {
  margin-bottom: 32px;
}
[data-rk] .ju367v3d {
  margin-bottom: 36px;
}
[data-rk] .ju367v3e {
  margin-bottom: 44px;
}
[data-rk] .ju367v3f {
  margin-bottom: 64px;
}
[data-rk] .ju367v3g {
  margin-bottom: -1px;
}
[data-rk] .ju367v3h {
  margin-left: 0;
}
[data-rk] .ju367v3i {
  margin-left: 1px;
}
[data-rk] .ju367v3j {
  margin-left: 2px;
}
[data-rk] .ju367v3k {
  margin-left: 3px;
}
[data-rk] .ju367v3l {
  margin-left: 4px;
}
[data-rk] .ju367v3m {
  margin-left: 5px;
}
[data-rk] .ju367v3n {
  margin-left: 6px;
}
[data-rk] .ju367v3o {
  margin-left: 8px;
}
[data-rk] .ju367v3p {
  margin-left: 10px;
}
[data-rk] .ju367v3q {
  margin-left: 12px;
}
[data-rk] .ju367v3r {
  margin-left: 14px;
}
[data-rk] .ju367v3s {
  margin-left: 16px;
}
[data-rk] .ju367v3t {
  margin-left: 18px;
}
[data-rk] .ju367v3u {
  margin-left: 20px;
}
[data-rk] .ju367v3v {
  margin-left: 24px;
}
[data-rk] .ju367v3w {
  margin-left: 28px;
}
[data-rk] .ju367v3x {
  margin-left: 32px;
}
[data-rk] .ju367v3y {
  margin-left: 36px;
}
[data-rk] .ju367v3z {
  margin-left: 44px;
}
[data-rk] .ju367v40 {
  margin-left: 64px;
}
[data-rk] .ju367v41 {
  margin-left: -1px;
}
[data-rk] .ju367v42 {
  margin-right: 0;
}
[data-rk] .ju367v43 {
  margin-right: 1px;
}
[data-rk] .ju367v44 {
  margin-right: 2px;
}
[data-rk] .ju367v45 {
  margin-right: 3px;
}
[data-rk] .ju367v46 {
  margin-right: 4px;
}
[data-rk] .ju367v47 {
  margin-right: 5px;
}
[data-rk] .ju367v48 {
  margin-right: 6px;
}
[data-rk] .ju367v49 {
  margin-right: 8px;
}
[data-rk] .ju367v4a {
  margin-right: 10px;
}
[data-rk] .ju367v4b {
  margin-right: 12px;
}
[data-rk] .ju367v4c {
  margin-right: 14px;
}
[data-rk] .ju367v4d {
  margin-right: 16px;
}
[data-rk] .ju367v4e {
  margin-right: 18px;
}
[data-rk] .ju367v4f {
  margin-right: 20px;
}
[data-rk] .ju367v4g {
  margin-right: 24px;
}
[data-rk] .ju367v4h {
  margin-right: 28px;
}
[data-rk] .ju367v4i {
  margin-right: 32px;
}
[data-rk] .ju367v4j {
  margin-right: 36px;
}
[data-rk] .ju367v4k {
  margin-right: 44px;
}
[data-rk] .ju367v4l {
  margin-right: 64px;
}
[data-rk] .ju367v4m {
  margin-right: -1px;
}
[data-rk] .ju367v4n {
  margin-top: 0;
}
[data-rk] .ju367v4o {
  margin-top: 1px;
}
[data-rk] .ju367v4p {
  margin-top: 2px;
}
[data-rk] .ju367v4q {
  margin-top: 3px;
}
[data-rk] .ju367v4r {
  margin-top: 4px;
}
[data-rk] .ju367v4s {
  margin-top: 5px;
}
[data-rk] .ju367v4t {
  margin-top: 6px;
}
[data-rk] .ju367v4u {
  margin-top: 8px;
}
[data-rk] .ju367v4v {
  margin-top: 10px;
}
[data-rk] .ju367v4w {
  margin-top: 12px;
}
[data-rk] .ju367v4x {
  margin-top: 14px;
}
[data-rk] .ju367v4y {
  margin-top: 16px;
}
[data-rk] .ju367v4z {
  margin-top: 18px;
}
[data-rk] .ju367v50 {
  margin-top: 20px;
}
[data-rk] .ju367v51 {
  margin-top: 24px;
}
[data-rk] .ju367v52 {
  margin-top: 28px;
}
[data-rk] .ju367v53 {
  margin-top: 32px;
}
[data-rk] .ju367v54 {
  margin-top: 36px;
}
[data-rk] .ju367v55 {
  margin-top: 44px;
}
[data-rk] .ju367v56 {
  margin-top: 64px;
}
[data-rk] .ju367v57 {
  margin-top: -1px;
}
[data-rk] .ju367v58 {
  max-width: 1px;
}
[data-rk] .ju367v59 {
  max-width: 2px;
}
[data-rk] .ju367v5a {
  max-width: 4px;
}
[data-rk] .ju367v5b {
  max-width: 8px;
}
[data-rk] .ju367v5c {
  max-width: 12px;
}
[data-rk] .ju367v5d {
  max-width: 20px;
}
[data-rk] .ju367v5e {
  max-width: 24px;
}
[data-rk] .ju367v5f {
  max-width: 28px;
}
[data-rk] .ju367v5g {
  max-width: 30px;
}
[data-rk] .ju367v5h {
  max-width: 32px;
}
[data-rk] .ju367v5i {
  max-width: 34px;
}
[data-rk] .ju367v5j {
  max-width: 36px;
}
[data-rk] .ju367v5k {
  max-width: 40px;
}
[data-rk] .ju367v5l {
  max-width: 44px;
}
[data-rk] .ju367v5m {
  max-width: 48px;
}
[data-rk] .ju367v5n {
  max-width: 54px;
}
[data-rk] .ju367v5o {
  max-width: 60px;
}
[data-rk] .ju367v5p {
  max-width: 200px;
}
[data-rk] .ju367v5q {
  max-width: 100%;
}
[data-rk] .ju367v5r {
  max-width: -moz-max-content;
  max-width: max-content;
}
[data-rk] .ju367v5s {
  min-width: 1px;
}
[data-rk] .ju367v5t {
  min-width: 2px;
}
[data-rk] .ju367v5u {
  min-width: 4px;
}
[data-rk] .ju367v5v {
  min-width: 8px;
}
[data-rk] .ju367v5w {
  min-width: 12px;
}
[data-rk] .ju367v5x {
  min-width: 20px;
}
[data-rk] .ju367v5y {
  min-width: 24px;
}
[data-rk] .ju367v5z {
  min-width: 28px;
}
[data-rk] .ju367v60 {
  min-width: 30px;
}
[data-rk] .ju367v61 {
  min-width: 32px;
}
[data-rk] .ju367v62 {
  min-width: 34px;
}
[data-rk] .ju367v63 {
  min-width: 36px;
}
[data-rk] .ju367v64 {
  min-width: 40px;
}
[data-rk] .ju367v65 {
  min-width: 44px;
}
[data-rk] .ju367v66 {
  min-width: 48px;
}
[data-rk] .ju367v67 {
  min-width: 54px;
}
[data-rk] .ju367v68 {
  min-width: 60px;
}
[data-rk] .ju367v69 {
  min-width: 200px;
}
[data-rk] .ju367v6a {
  min-width: 100%;
}
[data-rk] .ju367v6b {
  min-width: -moz-max-content;
  min-width: max-content;
}
[data-rk] .ju367v6c {
  overflow: hidden;
}
[data-rk] .ju367v6d {
  padding-bottom: 0;
}
[data-rk] .ju367v6e {
  padding-bottom: 1px;
}
[data-rk] .ju367v6f {
  padding-bottom: 2px;
}
[data-rk] .ju367v6g {
  padding-bottom: 3px;
}
[data-rk] .ju367v6h {
  padding-bottom: 4px;
}
[data-rk] .ju367v6i {
  padding-bottom: 5px;
}
[data-rk] .ju367v6j {
  padding-bottom: 6px;
}
[data-rk] .ju367v6k {
  padding-bottom: 8px;
}
[data-rk] .ju367v6l {
  padding-bottom: 10px;
}
[data-rk] .ju367v6m {
  padding-bottom: 12px;
}
[data-rk] .ju367v6n {
  padding-bottom: 14px;
}
[data-rk] .ju367v6o {
  padding-bottom: 16px;
}
[data-rk] .ju367v6p {
  padding-bottom: 18px;
}
[data-rk] .ju367v6q {
  padding-bottom: 20px;
}
[data-rk] .ju367v6r {
  padding-bottom: 24px;
}
[data-rk] .ju367v6s {
  padding-bottom: 28px;
}
[data-rk] .ju367v6t {
  padding-bottom: 32px;
}
[data-rk] .ju367v6u {
  padding-bottom: 36px;
}
[data-rk] .ju367v6v {
  padding-bottom: 44px;
}
[data-rk] .ju367v6w {
  padding-bottom: 64px;
}
[data-rk] .ju367v6x {
  padding-bottom: -1px;
}
[data-rk] .ju367v6y {
  padding-left: 0;
}
[data-rk] .ju367v6z {
  padding-left: 1px;
}
[data-rk] .ju367v70 {
  padding-left: 2px;
}
[data-rk] .ju367v71 {
  padding-left: 3px;
}
[data-rk] .ju367v72 {
  padding-left: 4px;
}
[data-rk] .ju367v73 {
  padding-left: 5px;
}
[data-rk] .ju367v74 {
  padding-left: 6px;
}
[data-rk] .ju367v75 {
  padding-left: 8px;
}
[data-rk] .ju367v76 {
  padding-left: 10px;
}
[data-rk] .ju367v77 {
  padding-left: 12px;
}
[data-rk] .ju367v78 {
  padding-left: 14px;
}
[data-rk] .ju367v79 {
  padding-left: 16px;
}
[data-rk] .ju367v7a {
  padding-left: 18px;
}
[data-rk] .ju367v7b {
  padding-left: 20px;
}
[data-rk] .ju367v7c {
  padding-left: 24px;
}
[data-rk] .ju367v7d {
  padding-left: 28px;
}
[data-rk] .ju367v7e {
  padding-left: 32px;
}
[data-rk] .ju367v7f {
  padding-left: 36px;
}
[data-rk] .ju367v7g {
  padding-left: 44px;
}
[data-rk] .ju367v7h {
  padding-left: 64px;
}
[data-rk] .ju367v7i {
  padding-left: -1px;
}
[data-rk] .ju367v7j {
  padding-right: 0;
}
[data-rk] .ju367v7k {
  padding-right: 1px;
}
[data-rk] .ju367v7l {
  padding-right: 2px;
}
[data-rk] .ju367v7m {
  padding-right: 3px;
}
[data-rk] .ju367v7n {
  padding-right: 4px;
}
[data-rk] .ju367v7o {
  padding-right: 5px;
}
[data-rk] .ju367v7p {
  padding-right: 6px;
}
[data-rk] .ju367v7q {
  padding-right: 8px;
}
[data-rk] .ju367v7r {
  padding-right: 10px;
}
[data-rk] .ju367v7s {
  padding-right: 12px;
}
[data-rk] .ju367v7t {
  padding-right: 14px;
}
[data-rk] .ju367v7u {
  padding-right: 16px;
}
[data-rk] .ju367v7v {
  padding-right: 18px;
}
[data-rk] .ju367v7w {
  padding-right: 20px;
}
[data-rk] .ju367v7x {
  padding-right: 24px;
}
[data-rk] .ju367v7y {
  padding-right: 28px;
}
[data-rk] .ju367v7z {
  padding-right: 32px;
}
[data-rk] .ju367v80 {
  padding-right: 36px;
}
[data-rk] .ju367v81 {
  padding-right: 44px;
}
[data-rk] .ju367v82 {
  padding-right: 64px;
}
[data-rk] .ju367v83 {
  padding-right: -1px;
}
[data-rk] .ju367v84 {
  padding-top: 0;
}
[data-rk] .ju367v85 {
  padding-top: 1px;
}
[data-rk] .ju367v86 {
  padding-top: 2px;
}
[data-rk] .ju367v87 {
  padding-top: 3px;
}
[data-rk] .ju367v88 {
  padding-top: 4px;
}
[data-rk] .ju367v89 {
  padding-top: 5px;
}
[data-rk] .ju367v8a {
  padding-top: 6px;
}
[data-rk] .ju367v8b {
  padding-top: 8px;
}
[data-rk] .ju367v8c {
  padding-top: 10px;
}
[data-rk] .ju367v8d {
  padding-top: 12px;
}
[data-rk] .ju367v8e {
  padding-top: 14px;
}
[data-rk] .ju367v8f {
  padding-top: 16px;
}
[data-rk] .ju367v8g {
  padding-top: 18px;
}
[data-rk] .ju367v8h {
  padding-top: 20px;
}
[data-rk] .ju367v8i {
  padding-top: 24px;
}
[data-rk] .ju367v8j {
  padding-top: 28px;
}
[data-rk] .ju367v8k {
  padding-top: 32px;
}
[data-rk] .ju367v8l {
  padding-top: 36px;
}
[data-rk] .ju367v8m {
  padding-top: 44px;
}
[data-rk] .ju367v8n {
  padding-top: 64px;
}
[data-rk] .ju367v8o {
  padding-top: -1px;
}
[data-rk] .ju367v8p {
  position: absolute;
}
[data-rk] .ju367v8q {
  position: fixed;
}
[data-rk] .ju367v8r {
  position: relative;
}
[data-rk] .ju367v8s {
  -webkit-user-select: none;
}
[data-rk] .ju367v8t {
  right: 0;
}
[data-rk] .ju367v8u {
  transition: 0.125s ease;
}
[data-rk] .ju367v8v {
  transition: transform 0.125s ease;
}
[data-rk] .ju367v8w {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
[data-rk] .ju367v8x {
  width: 1px;
}
[data-rk] .ju367v8y {
  width: 2px;
}
[data-rk] .ju367v8z {
  width: 4px;
}
[data-rk] .ju367v90 {
  width: 8px;
}
[data-rk] .ju367v91 {
  width: 12px;
}
[data-rk] .ju367v92 {
  width: 20px;
}
[data-rk] .ju367v93 {
  width: 24px;
}
[data-rk] .ju367v94 {
  width: 28px;
}
[data-rk] .ju367v95 {
  width: 30px;
}
[data-rk] .ju367v96 {
  width: 32px;
}
[data-rk] .ju367v97 {
  width: 34px;
}
[data-rk] .ju367v98 {
  width: 36px;
}
[data-rk] .ju367v99 {
  width: 40px;
}
[data-rk] .ju367v9a {
  width: 44px;
}
[data-rk] .ju367v9b {
  width: 48px;
}
[data-rk] .ju367v9c {
  width: 54px;
}
[data-rk] .ju367v9d {
  width: 60px;
}
[data-rk] .ju367v9e {
  width: 200px;
}
[data-rk] .ju367v9f {
  width: 100%;
}
[data-rk] .ju367v9g {
  width: -moz-max-content;
  width: max-content;
}
[data-rk] .ju367v9h {
  -webkit-backdrop-filter: var(--rk-blurs-modalOverlay);
  backdrop-filter: var(--rk-blurs-modalOverlay);
}
[data-rk] .ju367v9i {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9j:hover {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9k:active {
  background: var(--rk-colors-accentColor);
}
[data-rk] .ju367v9l {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9m:hover {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9n:active {
  background: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367v9o {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9p:hover {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9q:active {
  background: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367v9r {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9s:hover {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9t:active {
  background: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367v9u {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9v:hover {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9w:active {
  background: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367v9x {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367v9y:hover {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367v9z:active {
  background: var(--rk-colors-closeButton);
}
[data-rk] .ju367va0 {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va1:hover {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va2:active {
  background: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367va3 {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va4:hover {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va5:active {
  background: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367va6 {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va7:hover {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va8:active {
  background: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367va9 {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vaa:hover {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vab:active {
  background: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vac {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vad:hover {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vae:active {
  background: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vaf {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vag:hover {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vah:active {
  background: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vai {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vaj:hover {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vak:active {
  background: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367val {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vam:hover {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367van:active {
  background: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vao {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vap:hover {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vaq:active {
  background: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367var {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vas:hover {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vat:active {
  background: var(--rk-colors-error);
}
[data-rk] .ju367vau {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vav:hover {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vaw:active {
  background: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vax {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vay:hover {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vaz:active {
  background: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vb0 {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb1:hover {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb2:active {
  background: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vb3 {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb4:hover {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb5:active {
  background: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vb6 {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb7:hover {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb8:active {
  background: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vb9 {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vba:hover {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vbb:active {
  background: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vbc {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbd:hover {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbe:active {
  background: var(--rk-colors-modalText);
}
[data-rk] .ju367vbf {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbg:hover {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbh:active {
  background: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vbi {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbj:hover {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbk:active {
  background: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vbl {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbm:hover {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbn:active {
  background: var(--rk-colors-profileAction);
}
[data-rk] .ju367vbo {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbp:hover {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbq:active {
  background: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vbr {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbs:hover {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbt:active {
  background: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vbu {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbv:hover {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbw:active {
  background: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vbx {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vby:hover {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vbz:active {
  background: var(--rk-colors-standby);
}
[data-rk] .ju367vc0 {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc1:hover {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc2:active {
  border-color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vc3 {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc4:hover {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc5:active {
  border-color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vc6 {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc7:hover {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc8:active {
  border-color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vc9 {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vca:hover {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vcb:active {
  border-color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vcc {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vcd:hover {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vce:active {
  border-color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vcf {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vcg:hover {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vch:active {
  border-color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vci {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vcj:hover {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vck:active {
  border-color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vcl {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vcm:hover {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vcn:active {
  border-color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vco {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcp:hover {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcq:active {
  border-color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vcr {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vcs:hover {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vct:active {
  border-color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vcu {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcv:hover {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcw:active {
  border-color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vcx {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vcy:hover {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vcz:active {
  border-color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vd0 {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd1:hover {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd2:active {
  border-color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vd3 {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd4:hover {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd5:active {
  border-color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vd6 {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd7:hover {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd8:active {
  border-color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vd9 {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vda:hover {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vdb:active {
  border-color: var(--rk-colors-error);
}
[data-rk] .ju367vdc {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vdd:hover {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vde:active {
  border-color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vdf {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdg:hover {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdh:active {
  border-color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vdi {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdj:hover {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdk:active {
  border-color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vdl {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdm:hover {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdn:active {
  border-color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vdo {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdp:hover {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdq:active {
  border-color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vdr {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vds:hover {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vdt:active {
  border-color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vdu {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdv:hover {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdw:active {
  border-color: var(--rk-colors-modalText);
}
[data-rk] .ju367vdx {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vdy:hover {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vdz:active {
  border-color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367ve0 {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve1:hover {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve2:active {
  border-color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367ve3 {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve4:hover {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve5:active {
  border-color: var(--rk-colors-profileAction);
}
[data-rk] .ju367ve6 {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve7:hover {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve8:active {
  border-color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367ve9 {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vea:hover {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367veb:active {
  border-color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vec {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367ved:hover {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vee:active {
  border-color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vef {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367veg:hover {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367veh:active {
  border-color: var(--rk-colors-standby);
}
[data-rk] .ju367vei {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vej:hover {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vek:active {
  box-shadow: var(--rk-shadows-connectButton);
}
[data-rk] .ju367vel {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367vem:hover {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367ven:active {
  box-shadow: var(--rk-shadows-dialog);
}
[data-rk] .ju367veo {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367vep:hover {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367veq:active {
  box-shadow: var(--rk-shadows-profileDetailsAction);
}
[data-rk] .ju367ver {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367ves:hover {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367vet:active {
  box-shadow: var(--rk-shadows-selectedOption);
}
[data-rk] .ju367veu {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vev:hover {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vew:active {
  box-shadow: var(--rk-shadows-selectedWallet);
}
[data-rk] .ju367vex {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vey:hover {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vez:active {
  box-shadow: var(--rk-shadows-walletLogo);
}
[data-rk] .ju367vf0 {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf1:hover {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf2:active {
  color: var(--rk-colors-accentColor);
}
[data-rk] .ju367vf3 {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf4:hover {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf5:active {
  color: var(--rk-colors-accentColorForeground);
}
[data-rk] .ju367vf6 {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf7:hover {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf8:active {
  color: var(--rk-colors-actionButtonBorder);
}
[data-rk] .ju367vf9 {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfa:hover {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfb:active {
  color: var(--rk-colors-actionButtonBorderMobile);
}
[data-rk] .ju367vfc {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vfd:hover {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vfe:active {
  color: var(--rk-colors-actionButtonSecondaryBackground);
}
[data-rk] .ju367vff {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfg:hover {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfh:active {
  color: var(--rk-colors-closeButton);
}
[data-rk] .ju367vfi {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfj:hover {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfk:active {
  color: var(--rk-colors-closeButtonBackground);
}
[data-rk] .ju367vfl {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfm:hover {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfn:active {
  color: var(--rk-colors-connectButtonBackground);
}
[data-rk] .ju367vfo {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfp:hover {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfq:active {
  color: var(--rk-colors-connectButtonBackgroundError);
}
[data-rk] .ju367vfr {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vfs:hover {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vft:active {
  color: var(--rk-colors-connectButtonInnerBackground);
}
[data-rk] .ju367vfu {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfv:hover {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfw:active {
  color: var(--rk-colors-connectButtonText);
}
[data-rk] .ju367vfx {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vfy:hover {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vfz:active {
  color: var(--rk-colors-connectButtonTextError);
}
[data-rk] .ju367vg0 {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg1:hover {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg2:active {
  color: var(--rk-colors-connectionIndicator);
}
[data-rk] .ju367vg3 {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg4:hover {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg5:active {
  color: var(--rk-colors-downloadBottomCardBackground);
}
[data-rk] .ju367vg6 {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg7:hover {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg8:active {
  color: var(--rk-colors-downloadTopCardBackground);
}
[data-rk] .ju367vg9 {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vga:hover {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vgb:active {
  color: var(--rk-colors-error);
}
[data-rk] .ju367vgc {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vgd:hover {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vge:active {
  color: var(--rk-colors-generalBorder);
}
[data-rk] .ju367vgf {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgg:hover {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgh:active {
  color: var(--rk-colors-generalBorderDim);
}
[data-rk] .ju367vgi {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgj:hover {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgk:active {
  color: var(--rk-colors-menuItemBackground);
}
[data-rk] .ju367vgl {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgm:hover {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgn:active {
  color: var(--rk-colors-modalBackdrop);
}
[data-rk] .ju367vgo {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgp:hover {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgq:active {
  color: var(--rk-colors-modalBackground);
}
[data-rk] .ju367vgr {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgs:hover {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgt:active {
  color: var(--rk-colors-modalBorder);
}
[data-rk] .ju367vgu {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgv:hover {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgw:active {
  color: var(--rk-colors-modalText);
}
[data-rk] .ju367vgx {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vgy:hover {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vgz:active {
  color: var(--rk-colors-modalTextDim);
}
[data-rk] .ju367vh0 {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh1:hover {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh2:active {
  color: var(--rk-colors-modalTextSecondary);
}
[data-rk] .ju367vh3 {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh4:hover {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh5:active {
  color: var(--rk-colors-profileAction);
}
[data-rk] .ju367vh6 {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh7:hover {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh8:active {
  color: var(--rk-colors-profileActionHover);
}
[data-rk] .ju367vh9 {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vha:hover {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vhb:active {
  color: var(--rk-colors-profileForeground);
}
[data-rk] .ju367vhc {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhd:hover {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhe:active {
  color: var(--rk-colors-selectedOptionBorder);
}
[data-rk] .ju367vhf {
  color: var(--rk-colors-standby);
}
[data-rk] .ju367vhg:hover {
  color: var(--rk-colors-standby);
}
[data-rk] .ju367vhh:active {
  color: var(--rk-colors-standby);
}
@media screen and (min-width: 768px) {
  [data-rk] .ju367v1 {
    align-items: flex-start;
  }
  [data-rk] .ju367v3 {
    align-items: flex-end;
  }
  [data-rk] .ju367v5 {
    align-items: center;
  }
  [data-rk] .ju367v7 {
    display: none;
  }
  [data-rk] .ju367v9 {
    display: block;
  }
  [data-rk] .ju367vb {
    display: flex;
  }
  [data-rk] .ju367vd {
    display: inline;
  }
}

/* vanilla-extract-css-ns:src/css/touchableStyles.css.ts.vanilla.css?source=Ll8xMmNibzhpMywuXzEyY2JvOGkzOjphZnRlciB7CiAgLS1fMTJjYm84aTA6IDE7CiAgLS1fMTJjYm84aTE6IDE7Cn0KLl8xMmNibzhpMzpob3ZlciB7CiAgdHJhbnNmb3JtOiBzY2FsZSh2YXIoLS1fMTJjYm84aTApKTsKfQouXzEyY2JvOGkzOmFjdGl2ZSB7CiAgdHJhbnNmb3JtOiBzY2FsZSh2YXIoLS1fMTJjYm84aTEpKTsKfQouXzEyY2JvOGkzOmFjdGl2ZTo6YWZ0ZXIgewogIGNvbnRlbnQ6ICIiOwogIGJvdHRvbTogLTFweDsKICBkaXNwbGF5OiBibG9jazsKICBsZWZ0OiAtMXB4OwogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICByaWdodDogLTFweDsKICB0b3A6IC0xcHg7CiAgdHJhbnNmb3JtOiBzY2FsZShjYWxjKCgxIC8gdmFyKC0tXzEyY2JvOGkxKSkgKiB2YXIoLS1fMTJjYm84aTApKSk7Cn0KLl8xMmNibzhpNCwuXzEyY2JvOGk0OjphZnRlciB7CiAgLS1fMTJjYm84aTA6IDEuMDI1Owp9Ci5fMTJjYm84aTUsLl8xMmNibzhpNTo6YWZ0ZXIgewogIC0tXzEyY2JvOGkwOiAxLjE7Cn0KLl8xMmNibzhpNiwuXzEyY2JvOGk2OjphZnRlciB7CiAgLS1fMTJjYm84aTE6IDAuOTU7Cn0KLl8xMmNibzhpNywuXzEyY2JvOGk3OjphZnRlciB7CiAgLS1fMTJjYm84aTE6IDAuOTsKfQ== */
[data-rk] ._12cbo8i3,
[data-rk] ._12cbo8i3::after {
  --_12cbo8i0: 1;
  --_12cbo8i1: 1;
}
[data-rk] ._12cbo8i3:hover {
  transform: scale(var(--_12cbo8i0));
}
[data-rk] ._12cbo8i3:active {
  transform: scale(var(--_12cbo8i1));
}
[data-rk] ._12cbo8i3:active::after {
  content: "";
  bottom: -1px;
  display: block;
  left: -1px;
  position: absolute;
  right: -1px;
  top: -1px;
  transform: scale(calc((1 / var(--_12cbo8i1)) * var(--_12cbo8i0)));
}
[data-rk] ._12cbo8i4,
[data-rk] ._12cbo8i4::after {
  --_12cbo8i0: 1.025;
}
[data-rk] ._12cbo8i5,
[data-rk] ._12cbo8i5::after {
  --_12cbo8i0: 1.1;
}
[data-rk] ._12cbo8i6,
[data-rk] ._12cbo8i6::after {
  --_12cbo8i1: 0.95;
}
[data-rk] ._12cbo8i7,
[data-rk] ._12cbo8i7::after {
  --_12cbo8i1: 0.9;
}

/* vanilla-extract-css-ns:src/components/Icons/Icons.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfMWx1dWxlNDEgewogIDAlIHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOwogIH0KICAxMDAlIHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7CiAgfQp9Ci5fMWx1dWxlNDIgewogIGFuaW1hdGlvbjogXzFsdXVsZTQxIDNzIGluZmluaXRlIGxpbmVhcjsKfQouXzFsdXVsZTQzIHsKICBiYWNrZ3JvdW5kOiBjb25pYy1ncmFkaWVudChmcm9tIDE4MGRlZyBhdCA1MCUgNTAlLCByZ2JhKDcyLCAxNDYsIDI1NCwgMCkgMGRlZywgY3VycmVudENvbG9yIDI4Mi4wNGRlZywgcmdiYSg3MiwgMTQ2LCAyNTQsIDApIDMxOS44NmRlZywgcmdiYSg3MiwgMTQ2LCAyNTQsIDApIDM2MGRlZyk7CiAgaGVpZ2h0OiAyMXB4OwogIHdpZHRoOiAyMXB4Owp9 */
@keyframes _1luule41 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
[data-rk] ._1luule42 {
  animation: _1luule41 3s infinite linear;
}
[data-rk] ._1luule43 {
  background:
    conic-gradient(
      from 180deg at 50% 50%,
      rgba(72, 146, 254, 0) 0deg,
      currentColor 282.04deg,
      rgba(72, 146, 254, 0) 319.86deg,
      rgba(72, 146, 254, 0) 360deg);
  height: 21px;
  width: 21px;
}

/* vanilla-extract-css-ns:src/components/Dialog/Dialog.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfOXBtNGtpMCB7CiAgMCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDEwMCUpOwogIH0KICAxMDAlIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsKICB9Cn0KQGtleWZyYW1lcyBfOXBtNGtpMSB7CiAgMCUgewogICAgb3BhY2l0eTogMDsKICB9CiAgMTAwJSB7CiAgICBvcGFjaXR5OiAxOwogIH0KfQouXzlwbTRraTMgewogIGFuaW1hdGlvbjogXzlwbTRraTEgMTUwbXMgZWFzZTsKICBib3R0b206IC0yMDBweDsKICBsZWZ0OiAtMjAwcHg7CiAgcGFkZGluZzogMjAwcHg7CiAgcmlnaHQ6IC0yMDBweDsKICB0b3A6IC0yMDBweDsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVooMCk7CiAgei1pbmRleDogMjE0NzQ4MzY0NjsKfQouXzlwbTRraTUgewogIGFuaW1hdGlvbjogXzlwbTRraTAgMzUwbXMgY3ViaWMtYmV6aWVyKC4xNSwxLjE1LDAuNiwxLjAwKSwgXzlwbTRraTEgMTUwbXMgZWFzZTsKICBtYXgtd2lkdGg6IDEwMHZ3Owp9 */
@keyframes _9pm4ki0 {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes _9pm4ki1 {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
[data-rk] ._9pm4ki3 {
  animation: _9pm4ki1 150ms ease;
  bottom: -200px;
  left: -200px;
  padding: 200px;
  right: -200px;
  top: -200px;
  transform: translateZ(0);
  z-index: 2147483646;
}
[data-rk] ._9pm4ki5 {
  animation: _9pm4ki0 350ms cubic-bezier(.15, 1.15, 0.6, 1.00), _9pm4ki1 150ms ease;
  max-width: 100vw;
}

/* vanilla-extract-css-ns:src/components/Dialog/DialogContent.css.ts.vanilla.css?source=Ll8xY2tqcG9rMSB7CiAgYm94LXNpemluZzogY29udGVudC1ib3g7CiAgbWF4LXdpZHRoOiAxMDB2dzsKICB3aWR0aDogMzYwcHg7Cn0KLl8xY2tqcG9rMiB7CiAgd2lkdGg6IDEwMHZ3Owp9Ci5fMWNranBvazMgewogIG1pbi13aWR0aDogNzIwcHg7CiAgd2lkdGg6IDcyMHB4Owp9Ci5fMWNranBvazQgewogIG1pbi13aWR0aDogMzY4cHg7CiAgd2lkdGg6IDM2OHB4Owp9Ci5fMWNranBvazYgewogIGJvcmRlci13aWR0aDogMHB4OwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgd2lkdGg6IDEwMHZ3Owp9CkBtZWRpYSBzY3JlZW4gYW5kIChtaW4td2lkdGg6IDc2OHB4KSB7CiAgLl8xY2tqcG9rMSB7CiAgICB3aWR0aDogMzYwcHg7CiAgfQogIC5fMWNranBvazIgewogICAgd2lkdGg6IDQ4MHB4OwogIH0KICAuXzFja2pwb2s0IHsKICAgIG1pbi13aWR0aDogMzY4cHg7CiAgICB3aWR0aDogMzY4cHg7CiAgfQp9CkBtZWRpYSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDc2N3B4KSB7CiAgLl8xY2tqcG9rNyB7CiAgICBib3JkZXItYm90dG9tLWxlZnQtcmFkaXVzOiAwOwogICAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDA7CiAgICBtYXJnaW4tdG9wOiAtMjAwcHg7CiAgICBwYWRkaW5nLWJvdHRvbTogMjAwcHg7CiAgICB0b3A6IDIwMHB4OwogIH0KfQ== */
[data-rk] ._1ckjpok1 {
  box-sizing: content-box;
  max-width: 100vw;
  width: 360px;
}
[data-rk] ._1ckjpok2 {
  width: 100vw;
}
[data-rk] ._1ckjpok3 {
  min-width: 720px;
  width: 720px;
}
[data-rk] ._1ckjpok4 {
  min-width: 368px;
  width: 368px;
}
[data-rk] ._1ckjpok6 {
  border-width: 0px;
  box-sizing: border-box;
  width: 100vw;
}
@media screen and (min-width: 768px) {
  [data-rk] ._1ckjpok1 {
    width: 360px;
  }
  [data-rk] ._1ckjpok2 {
    width: 480px;
  }
  [data-rk] ._1ckjpok4 {
    min-width: 368px;
    width: 368px;
  }
}
@media screen and (max-width: 767px) {
  [data-rk] ._1ckjpok7 {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    margin-top: -200px;
    padding-bottom: 200px;
    top: 200px;
  }
}

/* vanilla-extract-css-ns:src/components/MenuButton/MenuButton.css.ts.vanilla.css?source=LnY5aG9yYjA6aG92ZXIgewogIGJhY2tncm91bmQ6IHVuc2V0Owp9 */
[data-rk] .v9horb0:hover {
  background: unset;
}

/* vanilla-extract-css-ns:src/components/ChainModal/ChainModal.css.ts.vanilla.css?source=Ll8xOGRxdzl4MCB7CiAgbWF4LWhlaWdodDogNDU2cHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBvdmVyZmxvdy14OiBoaWRkZW47Cn0KLl8xOGRxdzl4MSB7CiAgbWF4LWhlaWdodDogNDU2cHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBvdmVyZmxvdy14OiBoaWRkZW47CiAgc2Nyb2xsYmFyLXdpZHRoOiBub25lOwp9Ci5fMThkcXc5eDE6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICBkaXNwbGF5OiBub25lOwp9 */
[data-rk] ._18dqw9x0 {
  max-height: 456px;
  overflow-y: auto;
  overflow-x: hidden;
}
[data-rk] ._18dqw9x1 {
  max-height: 456px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}
[data-rk] ._18dqw9x1::-webkit-scrollbar {
  display: none;
}

/* vanilla-extract-css-ns:src/components/ModalSelection/ModalSelection.css.ts.vanilla.css?source=Lmc1a2wwbDAgewogIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7Cn0= */
[data-rk] .g5kl0l0 {
  border-color: transparent;
}

/* vanilla-extract-css-ns:src/components/ConnectOptions/DesktopOptions.css.ts.vanilla.css?source=Ll8xdnd0MGNnMCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgY29sb3I6IGJsYWNrOwp9Ci5fMXZ3dDBjZzIgewogIG1heC1oZWlnaHQ6IDQ1NHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KLl8xdnd0MGNnMyB7CiAgbWluLXdpZHRoOiAyODdweDsKfQouXzF2d3QwY2c0IHsKICBtaW4td2lkdGg6IDEwMCU7Cn0= */
[data-rk] ._1vwt0cg0 {
  background: white;
  color: black;
}
[data-rk] ._1vwt0cg2 {
  max-height: 454px;
  overflow-y: auto;
}
[data-rk] ._1vwt0cg3 {
  min-width: 287px;
}
[data-rk] ._1vwt0cg4 {
  min-width: 100%;
}

/* vanilla-extract-css-ns:src/components/ConnectOptions/MobileOptions.css.ts.vanilla.css?source=QGtleWZyYW1lcyBfMWFtMTQ0MTEgewogIDAlIHsKICAgIHN0cm9rZS1kYXNob2Zmc2V0OiAwOwogIH0KICAxMDAlIHsKICAgIHN0cm9rZS1kYXNob2Zmc2V0OiAtMjgzOwogIH0KfQouXzFhbTE0NDEwIHsKICBvdmVyZmxvdzogYXV0bzsKICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVaKDApOwp9Ci5fMWFtMTQ0MTA6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICBkaXNwbGF5OiBub25lOwp9Ci5fMWFtMTQ0MTIgewogIGFuaW1hdGlvbjogXzFhbTE0NDExIDFzIGxpbmVhciBpbmZpbml0ZTsKICBzdHJva2UtZGFzaGFycmF5OiA5OCAxOTY7CiAgZmlsbDogbm9uZTsKICBzdHJva2UtbGluZWNhcDogcm91bmQ7CiAgc3Ryb2tlLXdpZHRoOiA0Owp9Ci5fMWFtMTQ0MTMgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKfQ== */
@keyframes _1am14411 {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -283;
  }
}
[data-rk] ._1am14410 {
  overflow: auto;
  scrollbar-width: none;
  transform: translateZ(0);
}
[data-rk] ._1am14410::-webkit-scrollbar {
  display: none;
}
[data-rk] ._1am14412 {
  animation: _1am14411 1s linear infinite;
  stroke-dasharray: 98 196;
  fill: none;
  stroke-linecap: round;
  stroke-width: 4;
}
[data-rk] ._1am14413 {
  position: absolute;
}

/* vanilla-extract-css-ns:src/components/WalletButton/WalletButton.css.ts.vanilla.css?source=Ll8xeTJsbmZpMCB7CiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxNiwgMjEsIDMxLCAwLjA2KTsKfQouXzF5MmxuZmkxIHsKICBtYXgtd2lkdGg6IGZpdC1jb250ZW50Owp9 */
[data-rk] ._1y2lnfi0 {
  border: 1px solid rgba(16, 21, 31, 0.06);
}
[data-rk] ._1y2lnfi1 {
  max-width: -moz-fit-content;
  max-width: fit-content;
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!../node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!../node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!../node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-orange-200: oklch(90.1% 0.076 70.697);
    --color-orange-300: oklch(83.7% 0.128 66.29);
    --color-orange-400: oklch(75% 0.183 55.934);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-orange-600: oklch(64.6% 0.222 41.116);
    --color-amber-50: oklch(98.7% 0.022 95.277);
    --color-amber-100: oklch(96.2% 0.059 95.617);
    --color-amber-200: oklch(92.4% 0.12 95.746);
    --color-amber-400: oklch(82.8% 0.189 84.429);
    --color-amber-500: oklch(76.9% 0.188 70.08);
    --color-amber-600: oklch(66.6% 0.179 58.318);
    --color-amber-700: oklch(55.5% 0.163 48.998);
    --color-amber-800: oklch(47.3% 0.137 46.201);
    --color-amber-900: oklch(41.4% 0.112 45.904);
    --color-yellow-50: oklch(98.7% 0.026 102.212);
    --color-yellow-100: oklch(97.3% 0.071 103.193);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-300: oklch(90.5% 0.182 98.111);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-yellow-800: oklch(47.6% 0.114 61.907);
    --color-yellow-900: oklch(42.1% 0.095 57.708);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-green-900: oklch(39.3% 0.095 152.535);
    --color-emerald-400: oklch(76.5% 0.177 163.223);
    --color-emerald-500: oklch(69.6% 0.17 162.48);
    --color-teal-500: oklch(70.4% 0.14 182.503);
    --color-cyan-200: oklch(91.7% 0.08 205.041);
    --color-cyan-300: oklch(86.5% 0.127 207.078);
    --color-cyan-400: oklch(78.9% 0.154 211.53);
    --color-cyan-500: oklch(71.5% 0.143 215.221);
    --color-cyan-600: oklch(60.9% 0.126 221.723);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-violet-500: oklch(60.6% 0.25 292.717);
    --color-purple-100: oklch(94.6% 0.033 307.174);
    --color-purple-200: oklch(90.2% 0.063 306.703);
    --color-purple-300: oklch(82.7% 0.119 306.383);
    --color-purple-400: oklch(71.4% 0.203 305.504);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-purple-600: oklch(55.8% 0.288 302.321);
    --color-purple-700: oklch(49.6% 0.265 301.924);
    --color-purple-900: oklch(38.1% 0.176 304.987);
    --color-pink-400: oklch(71.8% 0.202 349.761);
    --color-pink-500: oklch(65.6% 0.241 354.308);
    --color-rose-500: oklch(64.5% 0.246 16.439);
    --color-slate-300: oklch(86.9% 0.022 252.894);
    --color-slate-400: oklch(70.4% 0.04 256.788);
    --color-slate-500: oklch(55.4% 0.046 257.417);
    --color-slate-600: oklch(44.6% 0.043 257.281);
    --color-slate-700: oklch(37.2% 0.044 257.287);
    --color-slate-800: oklch(27.9% 0.041 260.031);
    --color-slate-900: oklch(20.8% 0.042 265.755);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-xl: 24px;
    --blur-2xl: 40px;
    --blur-3xl: 64px;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-4 {
    inset: calc(var(--spacing) * 4);
  }
  .inset-8 {
    inset: calc(var(--spacing) * 8);
  }
  .-top-4 {
    top: calc(var(--spacing) * -4);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-1\/4 {
    top: calc(1/4 * 100%);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-8 {
    top: calc(var(--spacing) * 8);
  }
  .top-10 {
    top: calc(var(--spacing) * 10);
  }
  .-right-4 {
    right: calc(var(--spacing) * -4);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1\/4 {
    right: calc(1/4 * 100%);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-8 {
    right: calc(var(--spacing) * 8);
  }
  .right-10 {
    right: calc(var(--spacing) * 10);
  }
  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-1\/4 {
    bottom: calc(1/4 * 100%);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }
  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }
  .-left-4 {
    left: calc(var(--spacing) * -4);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-1\/4 {
    left: calc(1/4 * 100%);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-8 {
    left: calc(var(--spacing) * 8);
  }
  .left-10 {
    left: calc(var(--spacing) * 10);
  }
  .z-10 {
    z-index: 10;
  }
  .z-50 {
    z-index: 50;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-96 {
    height: calc(var(--spacing) * 96);
  }
  .h-full {
    height: 100%;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\/3 {
    width: calc(2/3 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xl {
    max-width: var(--container-3xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-6xl {
    max-width: var(--container-6xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-10 {
    --tw-translate-y: calc(var(--spacing) * 10);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-bounce {
    animation: var(--animate-bounce);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }
  .grid-rows-8 {
    grid-template-rows: repeat(8, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .items-center {
    align-items: center;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-16 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-6 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-tl-full {
    border-top-left-radius: calc(infinity * 1px);
  }
  .rounded-br-full {
    border-bottom-right-radius: calc(infinity * 1px);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-blue-400 {
    border-color: var(--color-blue-400);
  }
  .border-blue-400\/50 {
    border-color: color-mix(in srgb, oklch(70.7% 0.165 254.624) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-blue-400) 50%, transparent);
    }
  }
  .border-blue-500 {
    border-color: var(--color-blue-500);
  }
  .border-cyan-200 {
    border-color: var(--color-cyan-200);
  }
  .border-cyan-300 {
    border-color: var(--color-cyan-300);
  }
  .border-cyan-300\/30 {
    border-color: color-mix(in srgb, oklch(86.5% 0.127 207.078) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-cyan-300) 30%, transparent);
    }
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-green-500 {
    border-color: var(--color-green-500);
  }
  .border-orange-200 {
    border-color: var(--color-orange-200);
  }
  .border-purple-200 {
    border-color: var(--color-purple-200);
  }
  .border-purple-300 {
    border-color: var(--color-purple-300);
  }
  .border-purple-500\/30 {
    border-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-purple-500) 30%, transparent);
    }
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-slate-600 {
    border-color: var(--color-slate-600);
  }
  .border-slate-700 {
    border-color: var(--color-slate-700);
  }
  .border-slate-700\/50 {
    border-color: color-mix(in srgb, oklch(37.2% 0.044 257.287) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-slate-700) 50%, transparent);
    }
  }
  .border-slate-800 {
    border-color: var(--color-slate-800);
  }
  .border-slate-900 {
    border-color: var(--color-slate-900);
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-white\/30 {
    border-color: color-mix(in srgb, #fff 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }
  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }
  .border-yellow-400\/50 {
    border-color: color-mix(in srgb, oklch(85.2% 0.199 91.936) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-yellow-400) 50%, transparent);
    }
  }
  .border-t-transparent {
    border-top-color: transparent;
  }
  .bg-amber-50 {
    background-color: var(--color-amber-50);
  }
  .bg-amber-100 {
    background-color: var(--color-amber-100);
  }
  .bg-amber-600 {
    background-color: var(--color-amber-600);
  }
  .bg-black\/30 {
    background-color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-300\/30 {
    background-color: color-mix(in srgb, oklch(80.9% 0.105 251.813) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-300) 30%, transparent);
    }
  }
  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-500\/10 {
    background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }
  .bg-blue-500\/20 {
    background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }
  .bg-blue-900\/50 {
    background-color: color-mix(in srgb, oklch(37.9% 0.146 265.522) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-900) 50%, transparent);
    }
  }
  .bg-blue-900\/80 {
    background-color: color-mix(in srgb, oklch(37.9% 0.146 265.522) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-900) 80%, transparent);
    }
  }
  .bg-cyan-400 {
    background-color: var(--color-cyan-400);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-400 {
    background-color: var(--color-green-400);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-green-500\/20 {
    background-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }
  .bg-green-900\/50 {
    background-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-green-900) 50%, transparent);
    }
  }
  .bg-orange-400 {
    background-color: var(--color-orange-400);
  }
  .bg-pink-400 {
    background-color: var(--color-pink-400);
  }
  .bg-pink-500 {
    background-color: var(--color-pink-500);
  }
  .bg-pink-500\/10 {
    background-color: color-mix(in srgb, oklch(65.6% 0.241 354.308) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-pink-500) 10%, transparent);
    }
  }
  .bg-pink-500\/20 {
    background-color: color-mix(in srgb, oklch(65.6% 0.241 354.308) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-pink-500) 20%, transparent);
    }
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-purple-300\/30 {
    background-color: color-mix(in srgb, oklch(82.7% 0.119 306.383) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-purple-300) 30%, transparent);
    }
  }
  .bg-purple-400 {
    background-color: var(--color-purple-400);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-purple-500\/10 {
    background-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }
  .bg-purple-500\/20 {
    background-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-300\/30 {
    background-color: color-mix(in srgb, oklch(80.8% 0.114 19.571) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-red-300) 30%, transparent);
    }
  }
  .bg-red-400 {
    background-color: var(--color-red-400);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-red-900\/50 {
    background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-red-900) 50%, transparent);
    }
  }
  .bg-slate-600 {
    background-color: var(--color-slate-600);
  }
  .bg-slate-700 {
    background-color: var(--color-slate-700);
  }
  .bg-slate-700\/50 {
    background-color: color-mix(in srgb, oklch(37.2% 0.044 257.287) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-slate-700) 50%, transparent);
    }
  }
  .bg-slate-800 {
    background-color: var(--color-slate-800);
  }
  .bg-slate-800\/30 {
    background-color: color-mix(in srgb, oklch(27.9% 0.041 260.031) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-slate-800) 30%, transparent);
    }
  }
  .bg-slate-800\/40 {
    background-color: color-mix(in srgb, oklch(27.9% 0.041 260.031) 40%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-slate-800) 40%, transparent);
    }
  }
  .bg-slate-800\/50 {
    background-color: color-mix(in srgb, oklch(27.9% 0.041 260.031) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-slate-800) 50%, transparent);
    }
  }
  .bg-slate-900 {
    background-color: var(--color-slate-900);
  }
  .bg-slate-900\/80 {
    background-color: color-mix(in srgb, oklch(20.8% 0.042 265.755) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-slate-900) 80%, transparent);
    }
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/20 {
    background-color: color-mix(in srgb, #fff 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }
  .bg-white\/50 {
    background-color: color-mix(in srgb, #fff 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }
  .bg-white\/60 {
    background-color: color-mix(in srgb, #fff 60%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }
  .bg-white\/70 {
    background-color: color-mix(in srgb, #fff 70%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }
  .bg-white\/80 {
    background-color: color-mix(in srgb, #fff 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-yellow-300\/30 {
    background-color: color-mix(in srgb, oklch(90.5% 0.182 98.111) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-yellow-300) 30%, transparent);
    }
  }
  .bg-yellow-400 {
    background-color: var(--color-yellow-400);
  }
  .bg-yellow-900\/80 {
    background-color: color-mix(in srgb, oklch(42.1% 0.095 57.708) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-yellow-900) 80%, transparent);
    }
  }
  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-tr {
    --tw-gradient-position: to top right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-amber-500 {
    --tw-gradient-from: var(--color-amber-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-amber-600 {
    --tw-gradient-from: var(--color-amber-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-400 {
    --tw-gradient-from: var(--color-blue-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-400\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(70.7% 0.165 254.624) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-400) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-500 {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-cyan-300 {
    --tw-gradient-from: var(--color-cyan-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-cyan-400 {
    --tw-gradient-from: var(--color-cyan-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-emerald-400 {
    --tw-gradient-from: var(--color-emerald-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-emerald-500 {
    --tw-gradient-from: var(--color-emerald-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-50 {
    --tw-gradient-from: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-100 {
    --tw-gradient-from: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-500 {
    --tw-gradient-from: var(--color-gray-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-green-500 {
    --tw-gradient-from: var(--color-green-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-orange-400 {
    --tw-gradient-from: var(--color-orange-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-orange-500 {
    --tw-gradient-from: var(--color-orange-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-pink-500 {
    --tw-gradient-from: var(--color-pink-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-400 {
    --tw-gradient-from: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-500 {
    --tw-gradient-from: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-600 {
    --tw-gradient-from: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-600\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(55.8% 0.288 302.321) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-purple-600) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-slate-900 {
    --tw-gradient-from: var(--color-slate-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-slate-900\/50 {
    --tw-gradient-from: color-mix(in srgb, oklch(20.8% 0.042 265.755) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-slate-900) 50%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-slate-900\/70 {
    --tw-gradient-from: color-mix(in srgb, oklch(20.8% 0.042 265.755) 70%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-slate-900) 70%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-violet-500 {
    --tw-gradient-from: var(--color-violet-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-300 {
    --tw-gradient-from: var(--color-yellow-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-400 {
    --tw-gradient-from: var(--color-yellow-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-400\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(85.2% 0.199 91.936) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-yellow-400) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-500 {
    --tw-gradient-from: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(79.5% 0.184 86.047) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-yellow-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-blue-600 {
    --tw-gradient-via: var(--color-blue-600);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-orange-500 {
    --tw-gradient-via: var(--color-orange-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-orange-500\/20 {
    --tw-gradient-via: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-via: color-mix(in oklab, var(--color-orange-500) 20%, transparent);
    }
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-purple-500 {
    --tw-gradient-via: var(--color-purple-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-purple-500\/20 {
    --tw-gradient-via: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-via: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-purple-900 {
    --tw-gradient-via: var(--color-purple-900);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-white\/5 {
    --tw-gradient-via: color-mix(in srgb, #fff 5%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-white\/10 {
    --tw-gradient-via: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .to-amber-600 {
    --tw-gradient-to: var(--color-amber-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-amber-700 {
    --tw-gradient-to: var(--color-amber-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-500 {
    --tw-gradient-to: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-600 {
    --tw-gradient-to: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-600\/20 {
    --tw-gradient-to: color-mix(in srgb, oklch(54.6% 0.245 262.881) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-600) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-cyan-500 {
    --tw-gradient-to: var(--color-cyan-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-cyan-500\/20 {
    --tw-gradient-to: color-mix(in srgb, oklch(71.5% 0.143 215.221) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-cyan-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-emerald-500 {
    --tw-gradient-to: var(--color-emerald-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-100 {
    --tw-gradient-to: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-200 {
    --tw-gradient-to: var(--color-gray-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-600 {
    --tw-gradient-to: var(--color-gray-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-300 {
    --tw-gradient-to: var(--color-orange-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-500 {
    --tw-gradient-to: var(--color-orange-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-500\/20 {
    --tw-gradient-to: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-orange-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-400 {
    --tw-gradient-to: var(--color-pink-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-500 {
    --tw-gradient-to: var(--color-pink-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-300 {
    --tw-gradient-to: var(--color-purple-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-400 {
    --tw-gradient-to: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-500 {
    --tw-gradient-to: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-500 {
    --tw-gradient-to: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-500\/20 {
    --tw-gradient-to: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-rose-500 {
    --tw-gradient-to: var(--color-rose-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-slate-900 {
    --tw-gradient-to: var(--color-slate-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-slate-900\/50 {
    --tw-gradient-to: color-mix(in srgb, oklch(20.8% 0.042 265.755) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-slate-900) 50%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-slate-900\/80 {
    --tw-gradient-to: color-mix(in srgb, oklch(20.8% 0.042 265.755) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-slate-900) 80%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-teal-500 {
    --tw-gradient-to: var(--color-teal-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-yellow-500 {
    --tw-gradient-to: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-clip-text {
    background-clip: text;
  }
  .bg-center {
    background-position: center;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pt-20 {
    padding-top: calc(var(--spacing) * 20);
  }
  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }
  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }
  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }
  .text-center {
    text-align: center;
  }
  .text-right {
    text-align: right;
  }
  .font-mono {
    font-family: var(--font-geist-mono);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .break-all {
    word-break: break-all;
  }
  .text-amber-100 {
    color: var(--color-amber-100);
  }
  .text-amber-600 {
    color: var(--color-amber-600);
  }
  .text-amber-800 {
    color: var(--color-amber-800);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-100 {
    color: var(--color-blue-100);
  }
  .text-blue-400 {
    color: var(--color-blue-400);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-400 {
    color: var(--color-green-400);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-orange-400 {
    color: var(--color-orange-400);
  }
  .text-pink-400 {
    color: var(--color-pink-400);
  }
  .text-purple-300 {
    color: var(--color-purple-300);
  }
  .text-purple-400 {
    color: var(--color-purple-400);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-slate-300 {
    color: var(--color-slate-300);
  }
  .text-slate-400 {
    color: var(--color-slate-400);
  }
  .text-slate-500 {
    color: var(--color-slate-500);
  }
  .text-transparent {
    color: transparent;
  }
  .text-white {
    color: var(--color-white);
  }
  .text-white\/80 {
    color: color-mix(in srgb, #fff 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }
  .text-yellow-100 {
    color: var(--color-yellow-100);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-15 {
    opacity: 15%;
  }
  .opacity-20 {
    opacity: 20%;
  }
  .opacity-25 {
    opacity: 25%;
  }
  .opacity-30 {
    opacity: 30%;
  }
  .opacity-40 {
    opacity: 40%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-80 {
    opacity: 80%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .blur-3xl {
    --tw-blur: blur(var(--blur-3xl));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-xl {
    --tw-blur: blur(var(--blur-xl));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .delay-300 {
    transition-delay: 300ms;
  }
  .delay-700 {
    transition-delay: 700ms;
  }
  .delay-1000 {
    transition-delay: 1000ms;
  }
  .delay-2000 {
    transition-delay: 2000ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .duration-1000 {
    --tw-duration: 1000ms;
    transition-duration: 1000ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .group-hover\:scale-110 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 110%;
        --tw-scale-y: 110%;
        --tw-scale-z: 110%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:border-blue-500\/50 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        border-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-blue-500) 50%, transparent);
        }
      }
    }
  }
  .group-hover\:border-orange-500\/50 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        border-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-orange-500) 50%, transparent);
        }
      }
    }
  }
  .group-hover\:blur-2xl {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-blur: blur(var(--blur-2xl));
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
      }
    }
  }
  .hover\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:transform {
    &:hover {
      @media (hover: hover) {
        transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
      }
    }
  }
  .hover\:border-blue-500\/50 {
    &:hover {
      @media (hover: hover) {
        border-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-blue-500) 50%, transparent);
        }
      }
    }
  }
  .hover\:border-green-500\/50 {
    &:hover {
      @media (hover: hover) {
        border-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-green-500) 50%, transparent);
        }
      }
    }
  }
  .hover\:border-purple-500 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-purple-500);
      }
    }
  }
  .hover\:border-purple-500\/50 {
    &:hover {
      @media (hover: hover) {
        border-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-purple-500) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-gray-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700);
      }
    }
  }
  .hover\:bg-purple-500\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 10%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
        }
      }
    }
  }
  .hover\:bg-purple-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-600);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-slate-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-600);
      }
    }
  }
  .hover\:from-amber-700 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-amber-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-blue-600 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-blue-600);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-purple-700 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-purple-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-yellow-600 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-yellow-600);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-amber-800 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-amber-800);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-blue-700 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-blue-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-cyan-600 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-cyan-600);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-orange-600 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-orange-600);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:text-red-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-700);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:shadow-2xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-blue-500\/50 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 50%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-orange-500\/50 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-orange-500) 50%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .md\:col-span-2 {
    @media (width >= 48rem) {
      grid-column: span 2 / span 2;
    }
  }
  .md\:mt-0 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:text-7xl {
    @media (width >= 64rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
  .dark\:border-gray-700 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-700);
    }
  }
  .dark\:border-gray-800 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-800);
    }
  }
  .dark\:border-green-800 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-green-800);
    }
  }
  .dark\:border-red-800 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-red-800);
    }
  }
  .dark\:border-yellow-800 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-yellow-800);
    }
  }
  .dark\:bg-amber-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(41.4% 0.112 45.904) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-amber-900) 20%, transparent);
      }
    }
  }
  .dark\:bg-blue-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(37.9% 0.146 265.522) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }
  }
  .dark\:bg-gray-600 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-600);
    }
  }
  .dark\:bg-gray-700 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-700);
    }
  }
  .dark\:bg-gray-800 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-gray-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-900) 20%, transparent);
      }
    }
  }
  .dark\:bg-green-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }
  .dark\:bg-purple-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(38.1% 0.176 304.987) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
      }
    }
  }
  .dark\:bg-red-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }
  }
  .dark\:bg-yellow-900\/20 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(42.1% 0.095 57.708) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
      }
    }
  }
  .dark\:from-gray-700 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-from: var(--color-gray-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:from-gray-900 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-from: var(--color-gray-900);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:to-gray-800 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-to: var(--color-gray-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:text-amber-200 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-amber-200);
    }
  }
  .dark\:text-amber-400 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-amber-400);
    }
  }
  .dark\:text-blue-200 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-blue-200);
    }
  }
  .dark\:text-blue-400 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-blue-400);
    }
  }
  .dark\:text-gray-200 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-200);
    }
  }
  .dark\:text-gray-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-300);
    }
  }
  .dark\:text-gray-400 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-400);
    }
  }
  .dark\:text-gray-500 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-500);
    }
  }
  .dark\:text-green-200 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-green-200);
    }
  }
  .dark\:text-green-400 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-green-400);
    }
  }
  .dark\:text-purple-400 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-purple-400);
    }
  }
  .dark\:text-red-200 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-red-200);
    }
  }
  .dark\:text-white {
    @media (prefers-color-scheme: dark) {
      color: var(--color-white);
    }
  }
  .dark\:text-yellow-200 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-yellow-200);
    }
  }
}
:root {
  --background: #ffffff;
  --foreground: #171717;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

