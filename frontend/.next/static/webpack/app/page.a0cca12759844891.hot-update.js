"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(app-pages-browser)/../node_modules/wagmi/dist/esm/hooks/useAccount.js\");\n/* harmony import */ var _components_wallet_WalletButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/wallet/WalletButton */ \"(app-pages-browser)/./src/components/wallet/WalletButton.tsx\");\n/* harmony import */ var _lib_web3__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/web3 */ \"(app-pages-browser)/./src/lib/web3.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const { address, isConnected } = (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.useAccount)();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setIsVisible(true);\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    setCurrentSlide({\n                        \"Home.useEffect.interval\": (prev)=>(prev + 1) % 3\n                    }[\"Home.useEffect.interval\"]);\n                }\n            }[\"Home.useEffect.interval\"], 5000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"A\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"AkashaDao\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-purple-300\",\n                                                children: \"Community Pass\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-slate-300 hover:text-white transition-colors\",\n                                        children: \"特性\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#benefits\",\n                                        className: \"text-slate-300 hover:text-white transition-colors\",\n                                        children: \"权益\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#roadmap\",\n                                        className: \"text-slate-300 hover:text-white transition-colors\",\n                                        children: \"路线图\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#community\",\n                                        className: \"text-slate-300 hover:text-white transition-colors\",\n                                        children: \"社区\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/mint\",\n                                        className: \"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-medium transition-all transform hover:scale-105 shadow-lg\",\n                                        children: \"铸造 NFT\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletButton__WEBPACK_IMPORTED_MODULE_2__.WalletButton, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                                children: \"AkashaDao 社区通行证\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                                children: \"获得专属NFT通行证，解锁AkashaDao社区的全部权益。 享受无限制阅读、投票权、专属活动和VC网络连接。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MintCard, {\n                                tier: \"whitelist\",\n                                title: \"白名单通行证\",\n                                description: \"早期支持者专属，享受最优价格和全部权益\",\n                                price: _lib_web3__WEBPACK_IMPORTED_MODULE_3__.PRICES.WHITELIST,\n                                benefits: [\n                                    '无限制内容阅读权限',\n                                    '社区治理投票权',\n                                    '专属线下活动邀请',\n                                    'VC网络连接机会',\n                                    '优先获得新项目信息',\n                                    '专属Discord频道访问'\n                                ],\n                                onMintSuccess: handleMintSuccess\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MintCard, {\n                                tier: \"student\",\n                                title: \"学生通行证\",\n                                description: \"学生专享优惠价格，助力学习成长\",\n                                price: _lib_web3__WEBPACK_IMPORTED_MODULE_3__.PRICES.STUDENT,\n                                benefits: [\n                                    '无限制内容阅读权限',\n                                    '学习资源优先访问',\n                                    '导师一对一指导机会',\n                                    '实习和工作推荐',\n                                    '学生专属活动',\n                                    '技能认证和证书'\n                                ],\n                                onMintSuccess: handleMintSuccess\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white mb-8\",\n                                children: \"我的通行证\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NFTGrid, {\n                                tokenIds: userNFTs\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center\",\n                                children: \"通行证权益详解\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-600 dark:text-blue-400\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white mb-2\",\n                                                children: \"无限阅读\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                children: \"访问所有付费内容和研究报告\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-green-600 dark:text-green-400\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white mb-2\",\n                                                children: \"投票权\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                children: \"参与社区重要决策投票\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto mb-4 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-purple-600 dark:text-purple-400\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white mb-2\",\n                                                children: \"专属活动\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                children: \"线下聚会和专属活动邀请\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto mb-4 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-amber-600 dark:text-amber-400\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-gray-900 dark:text-white mb-2\",\n                                                children: \"VC网络\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                children: \"连接顶级投资人和创业者\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 AkashaDao. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"0O8LHAbuhnH8l+Sr7l0bOnShZfA=\", false, function() {\n    return [\n        wagmi__WEBPACK_IMPORTED_MODULE_4__.useAccount\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});