"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mint/page",{

/***/ "(app-pages-browser)/./src/components/nft/NFTPreview.tsx":
/*!*******************************************!*\
  !*** ./src/components/nft/NFTPreview.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NFTPreview: () => (/* binding */ NFTPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ NFTPreview auto */ \n\nfunction NFTPreview(param) {\n    let { tier, className = '' } = param;\n    const isWhitelist = tier === 'whitelist';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative aspect-square rounded-2xl overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: isWhitelist ? '/images/akasha-nft-whitelist.png' : '/images/akasha-nft-student.png',\n                    alt: \"AkashaDao \".concat(tier, \" NFT\"),\n                    fill: true,\n                    className: \"object-cover\",\n                    priority: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 \".concat(isWhitelist ? 'bg-gradient-to-br from-yellow-400/20 via-orange-500/20 to-red-500/20' : 'bg-gradient-to-br from-blue-400/20 via-purple-500/20 to-cyan-500/20'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent opacity-50 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 rounded-2xl transition-all duration-300 hover:shadow-2xl \".concat(isWhitelist ? 'hover:shadow-orange-500/50' : 'hover:shadow-blue-500/50')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c = NFTPreview;\nvar _c;\n$RefreshReg$(_c, \"NFTPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nft/NFTPreview.tsx\n"));

/***/ })

});