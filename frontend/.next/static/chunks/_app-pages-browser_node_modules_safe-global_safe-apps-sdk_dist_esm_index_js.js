"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_safe-global_safe-apps-sdk_dist_esm_index_js"],{

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.RestrictedMethods),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messageFormatter.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./methods.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PostMessageCommunicator);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js":
/*!*********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* binding */ MessageFormatter)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\");\n\n\nclass MessageFormatter {\n}\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0,_version_js__WEBPACK_IMPORTED_MODULE_0__.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n\n//# sourceMappingURL=messageFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vY29tbXVuaWNhdGlvbi9tZXNzYWdlRm9ybWF0dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUNDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNERBQWlCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsMERBQWE7QUFDckMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQzJCO0FBQzVCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2NvbW11bmljYXRpb24vbWVzc2FnZUZvcm1hdHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRTREtWZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG5pbXBvcnQgeyBnZW5lcmF0ZVJlcXVlc3RJZCB9IGZyb20gJy4vdXRpbHMuanMnO1xuY2xhc3MgTWVzc2FnZUZvcm1hdHRlciB7XG59XG5NZXNzYWdlRm9ybWF0dGVyLm1ha2VSZXF1ZXN0ID0gKG1ldGhvZCwgcGFyYW1zKSA9PiB7XG4gICAgY29uc3QgaWQgPSBnZW5lcmF0ZVJlcXVlc3RJZCgpO1xuICAgIHJldHVybiB7XG4gICAgICAgIGlkLFxuICAgICAgICBtZXRob2QsXG4gICAgICAgIHBhcmFtcyxcbiAgICAgICAgZW52OiB7XG4gICAgICAgICAgICBzZGtWZXJzaW9uOiBnZXRTREtWZXJzaW9uKCksXG4gICAgICAgIH0sXG4gICAgfTtcbn07XG5NZXNzYWdlRm9ybWF0dGVyLm1ha2VSZXNwb25zZSA9IChpZCwgZGF0YSwgdmVyc2lvbikgPT4gKHtcbiAgICBpZCxcbiAgICBzdWNjZXNzOiB0cnVlLFxuICAgIHZlcnNpb24sXG4gICAgZGF0YSxcbn0pO1xuTWVzc2FnZUZvcm1hdHRlci5tYWtlRXJyb3JSZXNwb25zZSA9IChpZCwgZXJyb3IsIHZlcnNpb24pID0+ICh7XG4gICAgaWQsXG4gICAgc3VjY2VzczogZmFsc2UsXG4gICAgZXJyb3IsXG4gICAgdmVyc2lvbixcbn0pO1xuZXhwb3J0IHsgTWVzc2FnZUZvcm1hdHRlciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWVzc2FnZUZvcm1hdHRlci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js":
/*!************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* binding */ Methods),\n/* harmony export */   RestrictedMethods: () => (/* binding */ RestrictedMethods)\n/* harmony export */ });\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateRequestId: () => (/* binding */ generateRequestId)\n/* harmony export */ });\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vY29tbXVuaWNhdGlvbi91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDNkI7QUFDN0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vY29tbXVuaWNhdGlvbi91dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBpLmUuIDAtMjU1IC0+ICcwMCctJ2ZmJ1xuY29uc3QgZGVjMmhleCA9IChkZWMpID0+IGRlYy50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKTtcbmNvbnN0IGdlbmVyYXRlSWQgPSAobGVuKSA9PiB7XG4gICAgY29uc3QgYXJyID0gbmV3IFVpbnQ4QXJyYXkoKGxlbiB8fCA0MCkgLyAyKTtcbiAgICB3aW5kb3cuY3J5cHRvLmdldFJhbmRvbVZhbHVlcyhhcnIpO1xuICAgIHJldHVybiBBcnJheS5mcm9tKGFyciwgZGVjMmhleCkuam9pbignJyk7XG59O1xuY29uc3QgZ2VuZXJhdGVSZXF1ZXN0SWQgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHJldHVybiBnZW5lcmF0ZUlkKDEwKTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBEYXRlKCkuZ2V0VGltZSgpLnRvU3RyaW5nKDM2KTtcbn07XG5leHBvcnQgeyBnZW5lcmF0ZVJlcXVlc3RJZCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../wallet/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (requirePermission);\n//# sourceMappingURL=requirePermissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js":
/*!****************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RPC_CALLS: () => (/* binding */ RPC_CALLS)\n/* harmony export */ });\nconst RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vZXRoL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vZXRoL2NvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUlBDX0NBTExTID0ge1xuICAgIGV0aF9jYWxsOiAnZXRoX2NhbGwnLFxuICAgIGV0aF9nYXNQcmljZTogJ2V0aF9nYXNQcmljZScsXG4gICAgZXRoX2dldExvZ3M6ICdldGhfZ2V0TG9ncycsXG4gICAgZXRoX2dldEJhbGFuY2U6ICdldGhfZ2V0QmFsYW5jZScsXG4gICAgZXRoX2dldENvZGU6ICdldGhfZ2V0Q29kZScsXG4gICAgZXRoX2dldEJsb2NrQnlIYXNoOiAnZXRoX2dldEJsb2NrQnlIYXNoJyxcbiAgICBldGhfZ2V0QmxvY2tCeU51bWJlcjogJ2V0aF9nZXRCbG9ja0J5TnVtYmVyJyxcbiAgICBldGhfZ2V0U3RvcmFnZUF0OiAnZXRoX2dldFN0b3JhZ2VBdCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uQnlIYXNoOiAnZXRoX2dldFRyYW5zYWN0aW9uQnlIYXNoJyxcbiAgICBldGhfZ2V0VHJhbnNhY3Rpb25SZWNlaXB0OiAnZXRoX2dldFRyYW5zYWN0aW9uUmVjZWlwdCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uQ291bnQ6ICdldGhfZ2V0VHJhbnNhY3Rpb25Db3VudCcsXG4gICAgZXRoX2VzdGltYXRlR2FzOiAnZXRoX2VzdGltYXRlR2FzJyxcbiAgICBzYWZlX3NldFNldHRpbmdzOiAnc2FmZV9zZXRTZXR0aW5ncycsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eth: () => (/* binding */ Eth)\n/* harmony export */ });\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../eth/constants.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js":
/*!********************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* reexport safe */ _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__.MessageFormatter),\n/* harmony export */   Methods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.Methods),\n/* harmony export */   Operation: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.Operation),\n/* harmony export */   RPC_CALLS: () => (/* reexport safe */ _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__.RPC_CALLS),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.RestrictedMethods),\n/* harmony export */   TokenType: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransferDirection),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSDKVersion: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_4__.getSDKVersion),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./communication/methods.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communication/messageFormatter.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./eth/constants.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_sdk_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkI7QUFDM0IsaUVBQWUsK0NBQUcsRUFBQztBQUNNO0FBQ1E7QUFDVTtBQUNTO0FBQ1A7QUFDVjtBQUNuQyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgU0RLIGZyb20gJy4vc2RrLmpzJztcbmV4cG9ydCBkZWZhdWx0IFNESztcbmV4cG9ydCAqIGZyb20gJy4vc2RrLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMvaW5kZXguanMnO1xuZXhwb3J0ICogZnJvbSAnLi9jb21tdW5pY2F0aW9uL21ldGhvZHMuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9jb21tdW5pY2F0aW9uL21lc3NhZ2VGb3JtYXR0ZXIuanMnO1xuZXhwb3J0IHsgZ2V0U0RLVmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbi5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2V0aC9jb25zdGFudHMuanMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js":
/*!*************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Safe: () => (/* binding */ Safe)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/../node_modules/viem/_esm/utils/abi/encodeFunctionData.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/../node_modules/viem/_esm/utils/signature/hashMessage.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(app-pages-browser)/../node_modules/viem/_esm/utils/signature/hashTypedData.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../eth/constants.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../types/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../decorators/requirePermissions.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\");\nvar __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\n\n\n\n\n\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0,viem__WEBPACK_IMPORTED_MODULE_6__.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0,viem__WEBPACK_IMPORTED_MODULE_7__.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0,_types_index_js__WEBPACK_IMPORTED_MODULE_3__.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\n__decorate([\n    (0,_decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n], Safe.prototype, \"requestAddressBook\", null);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAGIC_VALUE: () => (/* binding */ MAGIC_VALUE),\n/* harmony export */   MAGIC_VALUE_BYTES: () => (/* binding */ MAGIC_VALUE_BYTES)\n/* harmony export */ });\nconst MAGIC_VALUE = '0x1626ba7e';\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\n\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vc2FmZS9zaWduYXR1cmVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUMwQztBQUMxQyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9zYWZlL3NpZ25hdHVyZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTUFHSUNfVkFMVUUgPSAnMHgxNjI2YmE3ZSc7XG5jb25zdCBNQUdJQ19WQUxVRV9CWVRFUyA9ICcweDIwYzEzYjBiJztcbmV4cG9ydCB7IE1BR0lDX1ZBTFVFLCBNQUdJQ19WQUxVRV9CWVRFUyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2lnbmF0dXJlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js":
/*!******************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _communication_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./communication/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\");\n/* harmony import */ var _txs_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./txs/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\");\n/* harmony import */ var _eth_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./eth/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\");\n/* harmony import */ var _safe_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./safe/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\");\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./wallet/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n\n\n\n\n\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new _communication_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](allowedDomains, debug);\n        this.eth = new _eth_index_js__WEBPACK_IMPORTED_MODULE_2__.Eth(this.communicator);\n        this.txs = new _txs_index_js__WEBPACK_IMPORTED_MODULE_1__.TXs(this.communicator);\n        this.safe = new _safe_index_js__WEBPACK_IMPORTED_MODULE_3__.Safe(this.communicator);\n        this.wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__.Wallet(this.communicator);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeAppsSDK);\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vc2RrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RDtBQUN4QjtBQUNBO0FBQ0U7QUFDSTtBQUMzQztBQUNBLHlCQUF5QjtBQUN6QixnQkFBZ0IsdUNBQXVDO0FBQ3ZELGdDQUFnQywrREFBcUI7QUFDckQsdUJBQXVCLDhDQUFHO0FBQzFCLHVCQUF1Qiw4Q0FBRztBQUMxQix3QkFBd0IsZ0RBQUk7QUFDNUIsMEJBQTBCLG9EQUFNO0FBQ2hDO0FBQ0E7QUFDQSxpRUFBZSxXQUFXLEVBQUM7QUFDM0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vc2RrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBJbnRlcmZhY2VDb21tdW5pY2F0b3IgZnJvbSAnLi9jb21tdW5pY2F0aW9uL2luZGV4LmpzJztcbmltcG9ydCB7IFRYcyB9IGZyb20gJy4vdHhzL2luZGV4LmpzJztcbmltcG9ydCB7IEV0aCB9IGZyb20gJy4vZXRoL2luZGV4LmpzJztcbmltcG9ydCB7IFNhZmUgfSBmcm9tICcuL3NhZmUvaW5kZXguanMnO1xuaW1wb3J0IHsgV2FsbGV0IH0gZnJvbSAnLi93YWxsZXQvaW5kZXguanMnO1xuY2xhc3MgU2FmZUFwcHNTREsge1xuICAgIGNvbnN0cnVjdG9yKG9wdHMgPSB7fSkge1xuICAgICAgICBjb25zdCB7IGFsbG93ZWREb21haW5zID0gbnVsbCwgZGVidWcgPSBmYWxzZSB9ID0gb3B0cztcbiAgICAgICAgdGhpcy5jb21tdW5pY2F0b3IgPSBuZXcgSW50ZXJmYWNlQ29tbXVuaWNhdG9yKGFsbG93ZWREb21haW5zLCBkZWJ1Zyk7XG4gICAgICAgIHRoaXMuZXRoID0gbmV3IEV0aCh0aGlzLmNvbW11bmljYXRvcik7XG4gICAgICAgIHRoaXMudHhzID0gbmV3IFRYcyh0aGlzLmNvbW11bmljYXRvcik7XG4gICAgICAgIHRoaXMuc2FmZSA9IG5ldyBTYWZlKHRoaXMuY29tbXVuaWNhdG9yKTtcbiAgICAgICAgdGhpcy53YWxsZXQgPSBuZXcgV2FsbGV0KHRoaXMuY29tbXVuaWNhdG9yKTtcbiAgICB9XG59XG5leHBvcnQgZGVmYXVsdCBTYWZlQXBwc1NESztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNkay5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TXs: () => (/* binding */ TXs)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/index.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n\n\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0,_types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js":
/*!****************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransferDirection)\n/* harmony export */ });\n/* harmony import */ var _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\n\n//# sourceMappingURL=gateway.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvZ2F0ZXdheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF1SDtBQUN2SCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9nYXRld2F5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IE9wZXJhdGlvbiwgVG9rZW5UeXBlLCBUcmFuc2FjdGlvblN0YXR1cywgVHJhbnNmZXJEaXJlY3Rpb24sIH0gZnJvbSAnQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkayc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nYXRld2F5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js":
/*!**************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransferDirection),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _sdk_js__WEBPACK_IMPORTED_MODULE_0__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\");\n/* harmony import */ var _rpc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rpc.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\");\n/* harmony import */ var _gateway_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gateway.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\");\n/* harmony import */ var _messaging_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messaging.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXlCO0FBQ0E7QUFDSTtBQUNFO0FBQy9CIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vc2RrLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vcnBjLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZ2F0ZXdheS5qcyc7XG5leHBvcnQgKiBmcm9tICcuL21lc3NhZ2luZy5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n//# sourceMappingURL=messaging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvbWVzc2FnaW5nLmpzIiwibWFwcGluZ3MiOiI7O0FBQXNEO0FBQ3REIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL21lc3NhZ2luZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNZXRob2RzIH0gZnJvbSAnLi4vY29tbXVuaWNhdGlvbi9tZXRob2RzLmpzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1lc3NhZ2luZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js":
/*!********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS_REQUEST_REJECTED: () => (/* binding */ PERMISSIONS_REQUEST_REJECTED),\n/* harmony export */   PermissionsError: () => (/* binding */ PermissionsError)\n/* harmony export */ });\nconst PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\n//# sourceMappingURL=permissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvcGVybWlzc2lvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvcGVybWlzc2lvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFBFUk1JU1NJT05TX1JFUVVFU1RfUkVKRUNURUQgPSA0MDAxO1xuZXhwb3J0IGNsYXNzIFBlcm1pc3Npb25zRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSwgY29kZSwgZGF0YSkge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgdGhpcy5jb2RlID0gY29kZTtcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YTtcbiAgICAgICAgLy8gU2hvdWxkIGFkanVzdCBwcm90b3R5cGUgbWFudWFsbHkgYmVjYXVzZSBob3cgVFMgaGFuZGxlcyB0aGUgdHlwZSBleHRlbnNpb24gY29tcGlsYXRpb25cbiAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL01pY3Jvc29mdC9UeXBlU2NyaXB0L3dpa2kvQnJlYWtpbmctQ2hhbmdlcyNleHRlbmRpbmctYnVpbHQtaW5zLWxpa2UtZXJyb3ItYXJyYXktYW5kLW1hcC1tYXktbm8tbG9uZ2VyLXdvcmtcbiAgICAgICAgT2JqZWN0LnNldFByb3RvdHlwZU9mKHRoaXMsIFBlcm1pc3Npb25zRXJyb3IucHJvdG90eXBlKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wZXJtaXNzaW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvcnBjLmpzIiwibWFwcGluZ3MiOiI7QUFBVTtBQUNWIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL3JwYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ycGMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectEIP712TypedData: () => (/* binding */ isObjectEIP712TypedData)\n/* harmony export */ });\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvc2RrLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9zZGsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGlzT2JqZWN0RUlQNzEyVHlwZWREYXRhID0gKG9iaikgPT4ge1xuICAgIHJldHVybiB0eXBlb2Ygb2JqID09PSAnb2JqZWN0JyAmJiBvYmogIT0gbnVsbCAmJiAnZG9tYWluJyBpbiBvYmogJiYgJ3R5cGVzJyBpbiBvYmogJiYgJ21lc3NhZ2UnIGluIG9iajtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZGsuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js":
/*!**********************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSDKVersion: () => (/* binding */ getSDKVersion)\n/* harmony export */ });\nconst getSDKVersion = () => '9.1.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS92ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRTREtWZXJzaW9uID0gKCkgPT4gJzkuMS4wJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js":
/*!***************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Wallet: () => (/* binding */ Wallet)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions request is invalid', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_BASE_URL = void 0;\nexports.DEFAULT_BASE_URL = 'https://safe-client.safe.global';\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx3QkFBd0I7QUFDeEIsd0JBQXdCO0FBQ3hCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC9jb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkRFRkFVTFRfQkFTRV9VUkwgPSB2b2lkIDA7XG5leHBvcnRzLkRFRkFVTFRfQkFTRV9VUkwgPSAnaHR0cHM6Ly9zYWZlLWNsaWVudC5zYWZlLmdsb2JhbCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25maWcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.postEndpoint = postEndpoint;\nexports.putEndpoint = putEndpoint;\nexports.deleteEndpoint = deleteEndpoint;\nexports.getEndpoint = getEndpoint;\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\");\nfunction makeUrl(baseUrl, path, pathParams, query) {\n    const pathname = (0, utils_1.insertParams)(path, pathParams);\n    const search = (0, utils_1.stringifyQuery)(query);\n    return `${baseUrl}${pathname}${search}`;\n}\nfunction postEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'POST', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction putEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'PUT', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction deleteEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'DELETE', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction getEndpoint(baseUrl, path, params, rawUrl) {\n    if (rawUrl) {\n        return (0, utils_1.getData)(rawUrl, undefined, params === null || params === void 0 ? void 0 : params.credentials);\n    }\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.getData)(url, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\n//# sourceMappingURL=endpoint.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setBaseUrl = void 0;\nexports.relayTransaction = relayTransaction;\nexports.getRelayCount = getRelayCount;\nexports.getSafeInfo = getSafeInfo;\nexports.getIncomingTransfers = getIncomingTransfers;\nexports.getModuleTransactions = getModuleTransactions;\nexports.getMultisigTransactions = getMultisigTransactions;\nexports.getBalances = getBalances;\nexports.getFiatCurrencies = getFiatCurrencies;\nexports.getOwnedSafes = getOwnedSafes;\nexports.getAllOwnedSafes = getAllOwnedSafes;\nexports.getCollectibles = getCollectibles;\nexports.getCollectiblesPage = getCollectiblesPage;\nexports.getTransactionHistory = getTransactionHistory;\nexports.getTransactionQueue = getTransactionQueue;\nexports.getTransactionDetails = getTransactionDetails;\nexports.deleteTransaction = deleteTransaction;\nexports.postSafeGasEstimation = postSafeGasEstimation;\nexports.getNonces = getNonces;\nexports.proposeTransaction = proposeTransaction;\nexports.getConfirmationView = getConfirmationView;\nexports.getTxPreview = getTxPreview;\nexports.getChainsConfig = getChainsConfig;\nexports.getChainConfig = getChainConfig;\nexports.getSafeApps = getSafeApps;\nexports.getMasterCopies = getMasterCopies;\nexports.getDecodedData = getDecodedData;\nexports.getSafeMessages = getSafeMessages;\nexports.getSafeMessage = getSafeMessage;\nexports.proposeSafeMessage = proposeSafeMessage;\nexports.confirmSafeMessage = confirmSafeMessage;\nexports.getDelegates = getDelegates;\nexports.registerDevice = registerDevice;\nexports.unregisterSafe = unregisterSafe;\nexports.unregisterDevice = unregisterDevice;\nexports.registerEmail = registerEmail;\nexports.changeEmail = changeEmail;\nexports.resendEmailVerificationCode = resendEmailVerificationCode;\nexports.verifyEmail = verifyEmail;\nexports.getRegisteredEmail = getRegisteredEmail;\nexports.deleteRegisteredEmail = deleteRegisteredEmail;\nexports.registerRecoveryModule = registerRecoveryModule;\nexports.unsubscribeSingle = unsubscribeSingle;\nexports.unsubscribeAll = unsubscribeAll;\nexports.getSafeOverviews = getSafeOverviews;\nexports.getContract = getContract;\nexports.getAuthNonce = getAuthNonce;\nexports.verifyAuth = verifyAuth;\nexports.createAccount = createAccount;\nexports.getAccount = getAccount;\nexports.deleteAccount = deleteAccount;\nexports.getAccountDataTypes = getAccountDataTypes;\nexports.getAccountDataSettings = getAccountDataSettings;\nexports.putAccountDataSettings = putAccountDataSettings;\nexports.getIndexingStatus = getIndexingStatus;\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\");\nconst config_1 = __webpack_require__(/*! ./config */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\");\n__exportStar(__webpack_require__(/*! ./types/safe-info */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-apps */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/transactions */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/chains */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/common */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/master-copies */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/decoded-data */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-messages */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/notifications */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/relay */ \"(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\"), exports);\n// Can be set externally to a different CGW host\nlet baseUrl = config_1.DEFAULT_BASE_URL;\n/**\n * Set the base CGW URL\n */\nconst setBaseUrl = (url) => {\n    baseUrl = url;\n};\nexports.setBaseUrl = setBaseUrl;\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/**\n * Relay a transaction from a Safe\n */\nfunction relayTransaction(chainId, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/relay', { path: { chainId }, body });\n}\n/**\n * Get the relay limit and number of remaining relays remaining\n */\nfunction getRelayCount(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/relay/{address}', { path: { chainId, address } });\n}\n/**\n * Get basic information about a Safe. E.g. owners, modules, version etc\n */\nfunction getSafeInfo(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}', { path: { chainId, address } });\n}\n/**\n * Get filterable list of incoming transactions\n */\nfunction getIncomingTransfers(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/incoming-transfers/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of module transactions\n */\nfunction getModuleTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/module-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of multisig transactions\n */\nfunction getMultisigTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/multisig-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get the total balance and all assets stored in a Safe\n */\nfunction getBalances(chainId, address, currency = 'usd', query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/balances/{currency}', {\n        path: { chainId, address, currency },\n        query,\n    });\n}\n/**\n * Get a list of supported fiat currencies (e.g. USD, EUR etc)\n */\nfunction getFiatCurrencies() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/balances/supported-fiat-codes');\n}\n/**\n * Get the addresses of all Safes belonging to an owner\n */\nfunction getOwnedSafes(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/owners/{address}/safes', { path: { chainId, address } });\n}\n/**\n * Get the addresses of all Safes belonging to an owner on all chains\n */\nfunction getAllOwnedSafes(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/owners/{address}/safes', { path: { address } });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectibles(chainId, address, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/collectibles', {\n        path: { chainId, address },\n        query,\n    });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectiblesPage(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{address}/collectibles', { path: { chainId, address }, query }, pageUrl);\n}\n/**\n * Get a list of past Safe transactions\n */\nfunction getTransactionHistory(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/history', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the list of pending transactions\n */\nfunction getTransactionQueue(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/queued', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the details of an individual transaction by its id\n */\nfunction getTransactionDetails(chainId, transactionId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{transactionId}', {\n        path: { chainId, transactionId },\n    });\n}\n/**\n * Delete a transaction by its safeTxHash\n */\nfunction deleteTransaction(chainId, safeTxHash, signature) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safeTxHash}', {\n        path: { chainId, safeTxHash },\n        body: { signature },\n    });\n}\n/**\n * Request a gas estimate & recommmended tx nonce for a created transaction\n */\nfunction postSafeGasEstimation(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{safe_address}/multisig-transactions/estimations', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\nfunction getNonces(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/nonces', {\n        path: { chainId, safe_address: address },\n    });\n}\n/**\n * Propose a new transaction for other owners to sign/execute\n */\nfunction proposeTransaction(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/propose', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getConfirmationView(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/views/transaction-confirmation', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Get a tx preview\n */\nfunction getTxPreview(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/preview', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Returns all defined chain configs\n */\nfunction getChainsConfig(query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains', {\n        query,\n    });\n}\n/**\n * Returns a chain config\n */\nfunction getChainConfig(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns Safe Apps List\n */\nfunction getSafeApps(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safe-apps', {\n        path: { chainId: chainId },\n        query,\n    });\n}\n/**\n * Returns list of Master Copies\n */\nfunction getMasterCopies(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/master-copies', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getDecodedData(chainId, operation, encodedData, to) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/data-decoder', {\n        path: { chainId: chainId },\n        body: { operation, data: encodedData, to },\n    });\n}\n/**\n * Returns list of `SafeMessage`s\n */\nfunction getSafeMessages(chainId, address, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', { path: { chainId, safe_address: address }, query: {} }, pageUrl);\n}\n/**\n * Returns a `SafeMessage`\n */\nfunction getSafeMessage(chainId, messageHash) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}', {\n        path: { chainId, message_hash: messageHash },\n    });\n}\n/**\n * Propose a new `SafeMessage` for other owners to sign\n */\nfunction proposeSafeMessage(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Add a confirmation to a `SafeMessage`\n */\nfunction confirmSafeMessage(chainId, messageHash, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}/signatures', {\n        path: { chainId, message_hash: messageHash },\n        body,\n    });\n}\n/**\n * Returns a list of delegates\n */\nfunction getDelegates(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/delegates', {\n        path: { chainId },\n        query,\n    });\n}\n/**\n * Registers a device/Safe for notifications\n */\nfunction registerDevice(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/register/notifications', {\n        body,\n    });\n}\n/**\n * Unregisters a Safe from notifications\n */\nfunction unregisterSafe(chainId, address, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}/safes/{safe_address}', {\n        path: { chainId, safe_address: address, uuid },\n    });\n}\n/**\n * Unregisters a device from notifications\n */\nfunction unregisterDevice(chainId, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}', {\n        path: { chainId, uuid },\n    });\n}\n/**\n * Registers a email address for a safe signer.\n *\n * The signer wallet has to sign a message of format: `email-register-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param body Signer address and email address\n * @param headers Signature and Signature timestamp\n * @returns 200 if signature matches the data\n */\nfunction registerEmail(chainId, safeAddress, body, headers) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Changes an already registered email address for a safe signer. The new email address still needs to be verified.\n *\n * The signer wallet has to sign a message of format: `email-edit-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param body New email address\n * @param headers Signature and Signature timestamp\n * @returns 202 if signature matches the data\n */\nfunction changeEmail(chainId, safeAddress, signerAddress, body, headers) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Resends an email verification code.\n */\nfunction resendEmailVerificationCode(chainId, safeAddress, signerAddress) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify-resend', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body: '',\n    });\n}\n/**\n * Verifies a pending email address registration.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address who signed the email registration\n * @param body Verification code\n */\nfunction verifyEmail(chainId, safeAddress, signerAddress, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n    });\n}\n/**\n * Gets the registered email address of the signer\n *\n * The signer wallet will have to sign a message of format: `email-retrieval-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address of the owner of the Safe\n *\n * @returns email address and verified flag\n */\nfunction getRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Delete a registered email address for the signer\n *\n * The signer wallet will have to sign a message of format: `email-delete-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param headers\n */\nfunction deleteRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Register a recovery module for receiving alerts\n * @param chainId\n * @param safeAddress\n * @param body - { moduleAddress: string }\n */\nfunction registerRecoveryModule(chainId, safeAddress, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/recovery', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n    });\n}\n/**\n * Delete email subscription for a single category\n * @param query\n */\nfunction unsubscribeSingle(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions', { query });\n}\n/**\n * Delete email subscription for all categories\n * @param query\n */\nfunction unsubscribeAll(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions/all', { query });\n}\n/**\n * Get Safe overviews per address\n */\nfunction getSafeOverviews(safes, query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/safes', {\n        query: Object.assign(Object.assign({}, query), { safes: safes.join(',') }),\n    });\n}\nfunction getContract(chainId, contractAddress) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/contracts/{contractAddress}', {\n        path: {\n            chainId: chainId,\n            contractAddress: contractAddress,\n        },\n    });\n}\nfunction getAuthNonce() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/auth/nonce', { credentials: 'include' });\n}\nfunction verifyAuth(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/auth/verify', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction createAccount(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/accounts', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction getAccount(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction deleteAccount(address) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction getAccountDataTypes() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/data-types');\n}\nfunction getAccountDataSettings(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction putAccountDataSettings(address, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        body,\n        credentials: 'include',\n    });\n}\nfunction getIndexingStatus(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/indexing', {\n        path: { chainId },\n    });\n}\n/* eslint-enable @typescript-eslint/explicit-module-boundary-types */\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FEATURES = exports.GAS_PRICE_TYPE = exports.RPC_AUTHENTICATION = void 0;\nvar RPC_AUTHENTICATION;\n(function (RPC_AUTHENTICATION) {\n    RPC_AUTHENTICATION[\"API_KEY_PATH\"] = \"API_KEY_PATH\";\n    RPC_AUTHENTICATION[\"NO_AUTHENTICATION\"] = \"NO_AUTHENTICATION\";\n    RPC_AUTHENTICATION[\"UNKNOWN\"] = \"UNKNOWN\";\n})(RPC_AUTHENTICATION || (exports.RPC_AUTHENTICATION = RPC_AUTHENTICATION = {}));\nvar GAS_PRICE_TYPE;\n(function (GAS_PRICE_TYPE) {\n    GAS_PRICE_TYPE[\"ORACLE\"] = \"ORACLE\";\n    GAS_PRICE_TYPE[\"FIXED\"] = \"FIXED\";\n    GAS_PRICE_TYPE[\"FIXED_1559\"] = \"FIXED1559\";\n    GAS_PRICE_TYPE[\"UNKNOWN\"] = \"UNKNOWN\";\n})(GAS_PRICE_TYPE || (exports.GAS_PRICE_TYPE = GAS_PRICE_TYPE = {}));\nvar FEATURES;\n(function (FEATURES) {\n    FEATURES[\"ERC721\"] = \"ERC721\";\n    FEATURES[\"SAFE_APPS\"] = \"SAFE_APPS\";\n    FEATURES[\"CONTRACT_INTERACTION\"] = \"CONTRACT_INTERACTION\";\n    FEATURES[\"DOMAIN_LOOKUP\"] = \"DOMAIN_LOOKUP\";\n    FEATURES[\"SPENDING_LIMIT\"] = \"SPENDING_LIMIT\";\n    FEATURES[\"EIP1559\"] = \"EIP1559\";\n    FEATURES[\"SAFE_TX_GAS_OPTIONAL\"] = \"SAFE_TX_GAS_OPTIONAL\";\n    FEATURES[\"TX_SIMULATION\"] = \"TX_SIMULATION\";\n    FEATURES[\"EIP1271\"] = \"EIP1271\";\n})(FEATURES || (exports.FEATURES = FEATURES = {}));\n//# sourceMappingURL=chains.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TokenType = void 0;\nvar TokenType;\n(function (TokenType) {\n    TokenType[\"ERC20\"] = \"ERC20\";\n    TokenType[\"ERC721\"] = \"ERC721\";\n    TokenType[\"NATIVE_TOKEN\"] = \"NATIVE_TOKEN\";\n    TokenType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TokenType || (exports.TokenType = TokenType = {}));\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQkFBZ0IsaUJBQWlCLGlCQUFpQjtBQUNuRCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvY29tbW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Ub2tlblR5cGUgPSB2b2lkIDA7XG52YXIgVG9rZW5UeXBlO1xuKGZ1bmN0aW9uIChUb2tlblR5cGUpIHtcbiAgICBUb2tlblR5cGVbXCJFUkMyMFwiXSA9IFwiRVJDMjBcIjtcbiAgICBUb2tlblR5cGVbXCJFUkM3MjFcIl0gPSBcIkVSQzcyMVwiO1xuICAgIFRva2VuVHlwZVtcIk5BVElWRV9UT0tFTlwiXSA9IFwiTkFUSVZFX1RPS0VOXCI7XG4gICAgVG9rZW5UeXBlW1wiVU5LTk9XTlwiXSA9IFwiVU5LTk9XTlwiO1xufSkoVG9rZW5UeXBlIHx8IChleHBvcnRzLlRva2VuVHlwZSA9IFRva2VuVHlwZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb21tb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js":
/*!*******************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.NativeStakingStatus = exports.ConfirmationViewTypes = void 0;\nvar ConfirmationViewTypes;\n(function (ConfirmationViewTypes) {\n    ConfirmationViewTypes[\"GENERIC\"] = \"GENERIC\";\n    ConfirmationViewTypes[\"COW_SWAP_ORDER\"] = \"COW_SWAP_ORDER\";\n    ConfirmationViewTypes[\"COW_SWAP_TWAP_ORDER\"] = \"COW_SWAP_TWAP_ORDER\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_DEPOSIT\"] = \"KILN_NATIVE_STAKING_DEPOSIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_VALIDATORS_EXIT\"] = \"KILN_NATIVE_STAKING_VALIDATORS_EXIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_WITHDRAW\"] = \"KILN_NATIVE_STAKING_WITHDRAW\";\n})(ConfirmationViewTypes || (exports.ConfirmationViewTypes = ConfirmationViewTypes = {}));\nvar NativeStakingStatus;\n(function (NativeStakingStatus) {\n    NativeStakingStatus[\"NOT_STAKED\"] = \"NOT_STAKED\";\n    NativeStakingStatus[\"ACTIVATING\"] = \"ACTIVATING\";\n    NativeStakingStatus[\"DEPOSIT_IN_PROGRESS\"] = \"DEPOSIT_IN_PROGRESS\";\n    NativeStakingStatus[\"ACTIVE\"] = \"ACTIVE\";\n    NativeStakingStatus[\"EXIT_REQUESTED\"] = \"EXIT_REQUESTED\";\n    NativeStakingStatus[\"EXITING\"] = \"EXITING\";\n    NativeStakingStatus[\"EXITED\"] = \"EXITED\";\n    NativeStakingStatus[\"SLASHED\"] = \"SLASHED\";\n})(NativeStakingStatus || (exports.NativeStakingStatus = NativeStakingStatus = {}));\n//# sourceMappingURL=decoded-data.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=master-copies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL21hc3Rlci1jb3BpZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL21hc3Rlci1jb3BpZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tYXN0ZXItY29waWVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeviceType = void 0;\nvar DeviceType;\n(function (DeviceType) {\n    DeviceType[\"ANDROID\"] = \"ANDROID\";\n    DeviceType[\"IOS\"] = \"IOS\";\n    DeviceType[\"WEB\"] = \"WEB\";\n})(DeviceType || (exports.DeviceType = DeviceType = {}));\n//# sourceMappingURL=notifications.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL25vdGlmaWNhdGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGlCQUFpQixrQkFBa0Isa0JBQWtCO0FBQ3REIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9ub3RpZmljYXRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5EZXZpY2VUeXBlID0gdm9pZCAwO1xudmFyIERldmljZVR5cGU7XG4oZnVuY3Rpb24gKERldmljZVR5cGUpIHtcbiAgICBEZXZpY2VUeXBlW1wiQU5EUk9JRFwiXSA9IFwiQU5EUk9JRFwiO1xuICAgIERldmljZVR5cGVbXCJJT1NcIl0gPSBcIklPU1wiO1xuICAgIERldmljZVR5cGVbXCJXRUJcIl0gPSBcIldFQlwiO1xufSkoRGV2aWNlVHlwZSB8fCAoZXhwb3J0cy5EZXZpY2VUeXBlID0gRGV2aWNlVHlwZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ub3RpZmljYXRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js":
/*!************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=relay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3JlbGF5LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9yZWxheS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlbGF5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppSocialPlatforms = exports.SafeAppFeatures = exports.SafeAppAccessPolicyTypes = void 0;\nvar SafeAppAccessPolicyTypes;\n(function (SafeAppAccessPolicyTypes) {\n    SafeAppAccessPolicyTypes[\"NoRestrictions\"] = \"NO_RESTRICTIONS\";\n    SafeAppAccessPolicyTypes[\"DomainAllowlist\"] = \"DOMAIN_ALLOWLIST\";\n})(SafeAppAccessPolicyTypes || (exports.SafeAppAccessPolicyTypes = SafeAppAccessPolicyTypes = {}));\nvar SafeAppFeatures;\n(function (SafeAppFeatures) {\n    SafeAppFeatures[\"BATCHED_TRANSACTIONS\"] = \"BATCHED_TRANSACTIONS\";\n})(SafeAppFeatures || (exports.SafeAppFeatures = SafeAppFeatures = {}));\nvar SafeAppSocialPlatforms;\n(function (SafeAppSocialPlatforms) {\n    SafeAppSocialPlatforms[\"TWITTER\"] = \"TWITTER\";\n    SafeAppSocialPlatforms[\"GITHUB\"] = \"GITHUB\";\n    SafeAppSocialPlatforms[\"DISCORD\"] = \"DISCORD\";\n    SafeAppSocialPlatforms[\"TELEGRAM\"] = \"TELEGRAM\";\n})(SafeAppSocialPlatforms || (exports.SafeAppSocialPlatforms = SafeAppSocialPlatforms = {}));\n//# sourceMappingURL=safe-apps.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ImplementationVersionState = void 0;\nvar ImplementationVersionState;\n(function (ImplementationVersionState) {\n    ImplementationVersionState[\"UP_TO_DATE\"] = \"UP_TO_DATE\";\n    ImplementationVersionState[\"OUTDATED\"] = \"OUTDATED\";\n    ImplementationVersionState[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ImplementationVersionState || (exports.ImplementationVersionState = ImplementationVersionState = {}));\n//# sourceMappingURL=safe-info.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3NhZmUtaW5mby5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsaUNBQWlDLGtDQUFrQyxrQ0FBa0M7QUFDdEciLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3NhZmUtaW5mby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGUgPSB2b2lkIDA7XG52YXIgSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGU7XG4oZnVuY3Rpb24gKEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlKSB7XG4gICAgSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGVbXCJVUF9UT19EQVRFXCJdID0gXCJVUF9UT19EQVRFXCI7XG4gICAgSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGVbXCJPVVREQVRFRFwiXSA9IFwiT1VUREFURURcIjtcbiAgICBJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZVtcIlVOS05PV05cIl0gPSBcIlVOS05PV05cIjtcbn0pKEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlIHx8IChleHBvcnRzLkltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlID0gSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGUgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2FmZS1pbmZvLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeMessageStatus = exports.SafeMessageListItemType = void 0;\nvar SafeMessageListItemType;\n(function (SafeMessageListItemType) {\n    SafeMessageListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n    SafeMessageListItemType[\"MESSAGE\"] = \"MESSAGE\";\n})(SafeMessageListItemType || (exports.SafeMessageListItemType = SafeMessageListItemType = {}));\nvar SafeMessageStatus;\n(function (SafeMessageStatus) {\n    SafeMessageStatus[\"NEEDS_CONFIRMATION\"] = \"NEEDS_CONFIRMATION\";\n    SafeMessageStatus[\"CONFIRMED\"] = \"CONFIRMED\";\n})(SafeMessageStatus || (exports.SafeMessageStatus = SafeMessageStatus = {}));\n//# sourceMappingURL=safe-messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3NhZmUtbWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QseUJBQXlCLEdBQUcsK0JBQStCO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4QkFBOEIsK0JBQStCLCtCQUErQjtBQUM3RjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsd0JBQXdCLHlCQUF5Qix5QkFBeUI7QUFDM0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL3NhZmUtbWVzc2FnZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlNhZmVNZXNzYWdlU3RhdHVzID0gZXhwb3J0cy5TYWZlTWVzc2FnZUxpc3RJdGVtVHlwZSA9IHZvaWQgMDtcbnZhciBTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZTtcbihmdW5jdGlvbiAoU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGUpIHtcbiAgICBTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZVtcIkRBVEVfTEFCRUxcIl0gPSBcIkRBVEVfTEFCRUxcIjtcbiAgICBTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZVtcIk1FU1NBR0VcIl0gPSBcIk1FU1NBR0VcIjtcbn0pKFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlIHx8IChleHBvcnRzLlNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlID0gU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGUgPSB7fSkpO1xudmFyIFNhZmVNZXNzYWdlU3RhdHVzO1xuKGZ1bmN0aW9uIChTYWZlTWVzc2FnZVN0YXR1cykge1xuICAgIFNhZmVNZXNzYWdlU3RhdHVzW1wiTkVFRFNfQ09ORklSTUFUSU9OXCJdID0gXCJORUVEU19DT05GSVJNQVRJT05cIjtcbiAgICBTYWZlTWVzc2FnZVN0YXR1c1tcIkNPTkZJUk1FRFwiXSA9IFwiQ09ORklSTUVEXCI7XG59KShTYWZlTWVzc2FnZVN0YXR1cyB8fCAoZXhwb3J0cy5TYWZlTWVzc2FnZVN0YXR1cyA9IFNhZmVNZXNzYWdlU3RhdHVzID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNhZmUtbWVzc2FnZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js":
/*!*******************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LabelValue = exports.StartTimeValue = exports.DurationType = exports.DetailedExecutionInfoType = exports.TransactionListItemType = exports.ConflictType = exports.TransactionInfoType = exports.SettingsInfoType = exports.TransactionTokenType = exports.TransferDirection = exports.TransactionStatus = exports.Operation = void 0;\nvar Operation;\n(function (Operation) {\n    Operation[Operation[\"CALL\"] = 0] = \"CALL\";\n    Operation[Operation[\"DELEGATE\"] = 1] = \"DELEGATE\";\n})(Operation || (exports.Operation = Operation = {}));\nvar TransactionStatus;\n(function (TransactionStatus) {\n    TransactionStatus[\"AWAITING_CONFIRMATIONS\"] = \"AWAITING_CONFIRMATIONS\";\n    TransactionStatus[\"AWAITING_EXECUTION\"] = \"AWAITING_EXECUTION\";\n    TransactionStatus[\"CANCELLED\"] = \"CANCELLED\";\n    TransactionStatus[\"FAILED\"] = \"FAILED\";\n    TransactionStatus[\"SUCCESS\"] = \"SUCCESS\";\n})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));\nvar TransferDirection;\n(function (TransferDirection) {\n    TransferDirection[\"INCOMING\"] = \"INCOMING\";\n    TransferDirection[\"OUTGOING\"] = \"OUTGOING\";\n    TransferDirection[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TransferDirection || (exports.TransferDirection = TransferDirection = {}));\nvar TransactionTokenType;\n(function (TransactionTokenType) {\n    TransactionTokenType[\"ERC20\"] = \"ERC20\";\n    TransactionTokenType[\"ERC721\"] = \"ERC721\";\n    TransactionTokenType[\"NATIVE_COIN\"] = \"NATIVE_COIN\";\n})(TransactionTokenType || (exports.TransactionTokenType = TransactionTokenType = {}));\nvar SettingsInfoType;\n(function (SettingsInfoType) {\n    SettingsInfoType[\"SET_FALLBACK_HANDLER\"] = \"SET_FALLBACK_HANDLER\";\n    SettingsInfoType[\"ADD_OWNER\"] = \"ADD_OWNER\";\n    SettingsInfoType[\"REMOVE_OWNER\"] = \"REMOVE_OWNER\";\n    SettingsInfoType[\"SWAP_OWNER\"] = \"SWAP_OWNER\";\n    SettingsInfoType[\"CHANGE_THRESHOLD\"] = \"CHANGE_THRESHOLD\";\n    SettingsInfoType[\"CHANGE_IMPLEMENTATION\"] = \"CHANGE_IMPLEMENTATION\";\n    SettingsInfoType[\"ENABLE_MODULE\"] = \"ENABLE_MODULE\";\n    SettingsInfoType[\"DISABLE_MODULE\"] = \"DISABLE_MODULE\";\n    SettingsInfoType[\"SET_GUARD\"] = \"SET_GUARD\";\n    SettingsInfoType[\"DELETE_GUARD\"] = \"DELETE_GUARD\";\n})(SettingsInfoType || (exports.SettingsInfoType = SettingsInfoType = {}));\nvar TransactionInfoType;\n(function (TransactionInfoType) {\n    TransactionInfoType[\"TRANSFER\"] = \"Transfer\";\n    TransactionInfoType[\"SETTINGS_CHANGE\"] = \"SettingsChange\";\n    TransactionInfoType[\"CUSTOM\"] = \"Custom\";\n    TransactionInfoType[\"CREATION\"] = \"Creation\";\n    TransactionInfoType[\"SWAP_ORDER\"] = \"SwapOrder\";\n    TransactionInfoType[\"TWAP_ORDER\"] = \"TwapOrder\";\n    TransactionInfoType[\"SWAP_TRANSFER\"] = \"SwapTransfer\";\n    TransactionInfoType[\"NATIVE_STAKING_DEPOSIT\"] = \"NativeStakingDeposit\";\n    TransactionInfoType[\"NATIVE_STAKING_VALIDATORS_EXIT\"] = \"NativeStakingValidatorsExit\";\n    TransactionInfoType[\"NATIVE_STAKING_WITHDRAW\"] = \"NativeStakingWithdraw\";\n})(TransactionInfoType || (exports.TransactionInfoType = TransactionInfoType = {}));\nvar ConflictType;\n(function (ConflictType) {\n    ConflictType[\"NONE\"] = \"None\";\n    ConflictType[\"HAS_NEXT\"] = \"HasNext\";\n    ConflictType[\"END\"] = \"End\";\n})(ConflictType || (exports.ConflictType = ConflictType = {}));\nvar TransactionListItemType;\n(function (TransactionListItemType) {\n    TransactionListItemType[\"TRANSACTION\"] = \"TRANSACTION\";\n    TransactionListItemType[\"LABEL\"] = \"LABEL\";\n    TransactionListItemType[\"CONFLICT_HEADER\"] = \"CONFLICT_HEADER\";\n    TransactionListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n})(TransactionListItemType || (exports.TransactionListItemType = TransactionListItemType = {}));\nvar DetailedExecutionInfoType;\n(function (DetailedExecutionInfoType) {\n    DetailedExecutionInfoType[\"MULTISIG\"] = \"MULTISIG\";\n    DetailedExecutionInfoType[\"MODULE\"] = \"MODULE\";\n})(DetailedExecutionInfoType || (exports.DetailedExecutionInfoType = DetailedExecutionInfoType = {}));\nvar DurationType;\n(function (DurationType) {\n    DurationType[\"AUTO\"] = \"AUTO\";\n    DurationType[\"LIMIT_DURATION\"] = \"LIMIT_DURATION\";\n})(DurationType || (exports.DurationType = DurationType = {}));\nvar StartTimeValue;\n(function (StartTimeValue) {\n    StartTimeValue[\"AT_MINING_TIME\"] = \"AT_MINING_TIME\";\n    StartTimeValue[\"AT_EPOCH\"] = \"AT_EPOCH\";\n})(StartTimeValue || (exports.StartTimeValue = StartTimeValue = {}));\nvar LabelValue;\n(function (LabelValue) {\n    LabelValue[\"Queued\"] = \"Queued\";\n    LabelValue[\"Next\"] = \"Next\";\n})(LabelValue || (exports.LabelValue = LabelValue = {}));\n//# sourceMappingURL=transactions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.insertParams = insertParams;\nexports.stringifyQuery = stringifyQuery;\nexports.fetchData = fetchData;\nexports.getData = getData;\nconst isErrorResponse = (data) => {\n    const isObject = typeof data === 'object' && data !== null;\n    return isObject && ('code' in data || 'statusCode' in data) && 'message' in data;\n};\nfunction replaceParam(str, key, value) {\n    return str.replace(new RegExp(`\\\\{${key}\\\\}`, 'g'), value);\n}\nfunction insertParams(template, params) {\n    return params\n        ? Object.keys(params).reduce((result, key) => {\n            return replaceParam(result, key, String(params[key]));\n        }, template)\n        : template;\n}\nfunction stringifyQuery(query) {\n    if (!query) {\n        return '';\n    }\n    const searchParams = new URLSearchParams();\n    Object.keys(query).forEach((key) => {\n        if (query[key] != null) {\n            searchParams.append(key, String(query[key]));\n        }\n    });\n    const searchString = searchParams.toString();\n    return searchString ? `?${searchString}` : '';\n}\nfunction parseResponse(resp) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        let json;\n        try {\n            json = yield resp.json();\n        }\n        catch (_b) {\n            json = {};\n        }\n        if (!resp.ok) {\n            const errTxt = isErrorResponse(json)\n                ? `CGW error - ${(_a = json.code) !== null && _a !== void 0 ? _a : json.statusCode}: ${json.message}`\n                : `CGW error - status ${resp.statusText}`;\n            throw new Error(errTxt);\n        }\n        return json;\n    });\n}\nfunction fetchData(url, method, body, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const requestHeaders = Object.assign({ 'Content-Type': 'application/json' }, headers);\n        const options = {\n            method: method !== null && method !== void 0 ? method : 'POST',\n            headers: requestHeaders,\n        };\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        if (body != null) {\n            options.body = typeof body === 'string' ? body : JSON.stringify(body);\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\nfunction getData(url, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const options = {\n            method: 'GET',\n        };\n        if (headers) {\n            options['headers'] = Object.assign(Object.assign({}, headers), { 'Content-Type': 'application/json' });\n        }\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\n"));

/***/ })

}]);