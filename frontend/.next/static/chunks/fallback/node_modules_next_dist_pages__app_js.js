"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_next_dist_pages__app_js"],{

/***/ "./node_modules/next/dist/pages/_app.js":
/*!**********************************************!*\
  !*** ./node_modules/next/dist/pages/_app.js ***!
  \**********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\n\nvar _classCallCheck = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/classCallCheck.js */ \"./node_modules/next/dist/compiled/@babel/runtime/helpers/classCallCheck.js\");\n\nvar _createClass = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/createClass.js */ \"./node_modules/next/dist/compiled/@babel/runtime/helpers/createClass.js\");\n\nvar _inherits = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/inherits.js */ \"./node_modules/next/dist/compiled/@babel/runtime/helpers/inherits.js\");\n\nvar _possibleConstructorReturn = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/possibleConstructorReturn.js */ \"./node_modules/next/dist/compiled/@babel/runtime/helpers/possibleConstructorReturn.js\");\n\nvar _getPrototypeOf = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/getPrototypeOf.js */ \"./node_modules/next/dist/compiled/@babel/runtime/helpers/getPrototypeOf.js\");\n\nvar _regeneratorRuntime = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/regenerator/index.js */ \"./node_modules/next/dist/compiled/@babel/runtime/regenerator/index.js\");\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"AppInitialProps\", ({\n  enumerable: true,\n  get: function get() {\n    return _utils.AppInitialProps;\n  }\n}));\nObject.defineProperty(exports, \"NextWebVitalsMetric\", ({\n  enumerable: true,\n  get: function get() {\n    return _utils.NextWebVitalsMetric;\n  }\n}));\nObject.defineProperty(exports, \"AppType\", ({\n  enumerable: true,\n  get: function get() {\n    return _utils.AppType;\n  }\n}));\nexports[\"default\"] = void 0;\n\nvar _async_to_generator = (__webpack_require__(/*! @swc/helpers/lib/_async_to_generator.js */ \"./node_modules/@swc/helpers/lib/_async_to_generator.js\")[\"default\"]);\n\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\n\nvar _react = _interop_require_default(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\n\nvar _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\n\nfunction appGetInitialProps(_) {\n  return _appGetInitialProps.apply(this, arguments);\n}\n\nfunction _appGetInitialProps() {\n  _appGetInitialProps =\n  /**\n  * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n  * This allows for keeping state between navigation, custom error handling, injecting additional data.\n  */\n  _async_to_generator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(_ref) {\n    var Component, ctx, pageProps;\n    return _regeneratorRuntime.wrap(function _callee$(_context) {\n      while (1) {\n        switch (_context.prev = _context.next) {\n          case 0:\n            Component = _ref.Component, ctx = _ref.ctx;\n            _context.next = 3;\n            return (0, _utils).loadGetInitialProps(Component, ctx);\n\n          case 3:\n            pageProps = _context.sent;\n            return _context.abrupt(\"return\", {\n              pageProps: pageProps\n            });\n\n          case 5:\n          case \"end\":\n            return _context.stop();\n        }\n      }\n    }, _callee);\n  }));\n  return _appGetInitialProps.apply(this, arguments);\n}\n\nvar _Component;\n\nvar App = /*#__PURE__*/function (_Component2) {\n  _inherits(App, _Component2);\n\n  var _super = _createSuper(App);\n\n  function App() {\n    _classCallCheck(this, App);\n\n    return _super.apply(this, arguments);\n  }\n\n  _createClass(App, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          Component = _this$props.Component,\n          pageProps = _this$props.pageProps;\n      return /*#__PURE__*/_react[\"default\"].createElement(Component, Object.assign({}, pageProps));\n    }\n  }]);\n\n  return App;\n}(_Component = _react[\"default\"].Component);\n\nApp.origGetInitialProps = appGetInitialProps;\nApp.getInitialProps = appGetInitialProps;\nexports[\"default\"] = App;\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_app.js\n"));

/***/ })

}]);