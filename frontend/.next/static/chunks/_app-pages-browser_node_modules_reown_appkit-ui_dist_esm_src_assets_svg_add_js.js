"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_add_js"],{

/***/ "(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js":
/*!***********************************************************************!*\
  !*** ../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addSvg: () => (/* binding */ addSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/../node_modules/lit/index.js\");\n\nconst addSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg\n  width=\"14\"\n  height=\"14\"\n  viewBox=\"0 0 14 14\"\n  fill=\"none\"\n  xmlns=\"http://www.w3.org/2000/svg\"\n>\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M7.0023 0.875C7.48571 0.875 7.8776 1.26675 7.8776 1.75V6.125H12.2541C12.7375 6.125 13.1294 6.51675 13.1294 7C13.1294 7.48325 12.7375 7.875 12.2541 7.875H7.8776V12.25C7.8776 12.7332 7.48571 13.125 7.0023 13.125C6.51889 13.125 6.12701 12.7332 6.12701 12.25V7.875H1.75054C1.26713 7.875 0.875244 7.48325 0.875244 7C0.875244 6.51675 1.26713 6.125 1.75054 6.125H6.12701V1.75C6.12701 1.26675 6.51889 0.875 7.0023 0.875Z\"\n    fill=\"#667dff\"\n  /></svg\n>`;\n//# sourceMappingURL=add.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9hZGQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIsZUFBZSx3Q0FBRztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9hZGQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBhZGRTdmcgPSBzdmcgYDxzdmdcbiAgd2lkdGg9XCIxNFwiXG4gIGhlaWdodD1cIjE0XCJcbiAgdmlld0JveD1cIjAgMCAxNCAxNFwiXG4gIGZpbGw9XCJub25lXCJcbiAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4+XG4gIDxwYXRoXG4gICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgZmlsbC1ydWxlPVwiZXZlbm9kZFwiXG4gICAgY2xpcC1ydWxlPVwiZXZlbm9kZFwiXG4gICAgZD1cIk03LjAwMjMgMC44NzVDNy40ODU3MSAwLjg3NSA3Ljg3NzYgMS4yNjY3NSA3Ljg3NzYgMS43NVY2LjEyNUgxMi4yNTQxQzEyLjczNzUgNi4xMjUgMTMuMTI5NCA2LjUxNjc1IDEzLjEyOTQgN0MxMy4xMjk0IDcuNDgzMjUgMTIuNzM3NSA3Ljg3NSAxMi4yNTQxIDcuODc1SDcuODc3NlYxMi4yNUM3Ljg3NzYgMTIuNzMzMiA3LjQ4NTcxIDEzLjEyNSA3LjAwMjMgMTMuMTI1QzYuNTE4ODkgMTMuMTI1IDYuMTI3MDEgMTIuNzMzMiA2LjEyNzAxIDEyLjI1VjcuODc1SDEuNzUwNTRDMS4yNjcxMyA3Ljg3NSAwLjg3NTI0NCA3LjQ4MzI1IDAuODc1MjQ0IDdDMC44NzUyNDQgNi41MTY3NSAxLjI2NzEzIDYuMTI1IDEuNzUwNTQgNi4xMjVINi4xMjcwMVYxLjc1QzYuMTI3MDEgMS4yNjY3NSA2LjUxODg5IDAuODc1IDcuMDAyMyAwLjg3NVpcIlxuICAgIGZpbGw9XCIjNjY3ZGZmXCJcbiAgLz48L3N2Z1xuPmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hZGQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js\n"));

/***/ })

}]);