"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_plus_js"],{

/***/ "(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js":
/*!************************************************************************!*\
  !*** ../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   plusSvg: () => (/* binding */ plusSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/../node_modules/lit/index.js\");\n\nconst plusSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg\n  width=\"13\"\n  height=\"12\"\n  viewBox=\"0 0 13 12\"\n  fill=\"none\"\n>\n  <path\n    fill=\"currentColor\"\n    fill-rule=\"evenodd\"\n    clip-rule=\"evenodd\"\n    d=\"M0.794373 5.99982C0.794373 5.52643 1.17812 5.14268 1.6515 5.14268H5.643V1.15109C5.643 0.677701 6.02675 0.293946 6.50012 0.293945C6.9735 0.293946 7.35725 0.677701 7.35725 1.15109V5.14268H11.3488C11.8221 5.14268 12.2059 5.52643 12.2059 5.99982C12.2059 6.47321 11.8221 6.85696 11.3488 6.85696H7.35725V10.8486C7.35725 11.3219 6.9735 11.7057 6.50012 11.7057C6.02675 11.7057 5.643 11.3219 5.643 10.8486V6.85696H1.6515C1.17812 6.85696 0.794373 6.47321 0.794373 5.99982Z\"\n  /></svg\n>`;\n//# sourceMappingURL=plus.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9wbHVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBCO0FBQ25CLGdCQUFnQix3Q0FBRztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL3BsdXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBwbHVzU3ZnID0gc3ZnIGA8c3ZnXG4gIHdpZHRoPVwiMTNcIlxuICBoZWlnaHQ9XCIxMlwiXG4gIHZpZXdCb3g9XCIwIDAgMTMgMTJcIlxuICBmaWxsPVwibm9uZVwiXG4+XG4gIDxwYXRoXG4gICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgZmlsbC1ydWxlPVwiZXZlbm9kZFwiXG4gICAgY2xpcC1ydWxlPVwiZXZlbm9kZFwiXG4gICAgZD1cIk0wLjc5NDM3MyA1Ljk5OTgyQzAuNzk0MzczIDUuNTI2NDMgMS4xNzgxMiA1LjE0MjY4IDEuNjUxNSA1LjE0MjY4SDUuNjQzVjEuMTUxMDlDNS42NDMgMC42Nzc3MDEgNi4wMjY3NSAwLjI5Mzk0NiA2LjUwMDEyIDAuMjkzOTQ1QzYuOTczNSAwLjI5Mzk0NiA3LjM1NzI1IDAuNjc3NzAxIDcuMzU3MjUgMS4xNTEwOVY1LjE0MjY4SDExLjM0ODhDMTEuODIyMSA1LjE0MjY4IDEyLjIwNTkgNS41MjY0MyAxMi4yMDU5IDUuOTk5ODJDMTIuMjA1OSA2LjQ3MzIxIDExLjgyMjEgNi44NTY5NiAxMS4zNDg4IDYuODU2OTZINy4zNTcyNVYxMC44NDg2QzcuMzU3MjUgMTEuMzIxOSA2Ljk3MzUgMTEuNzA1NyA2LjUwMDEyIDExLjcwNTdDNi4wMjY3NSAxMS43MDU3IDUuNjQzIDExLjMyMTkgNS42NDMgMTAuODQ4NlY2Ljg1Njk2SDEuNjUxNUMxLjE3ODEyIDYuODU2OTYgMC43OTQzNzMgNi40NzMyMSAwLjc5NDM3MyA1Ljk5OTgyWlwiXG4gIC8+PC9zdmdcbj5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGx1cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js\n"));

/***/ })

}]);