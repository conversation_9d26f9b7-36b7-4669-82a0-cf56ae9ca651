"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_etherscan_js"],{

/***/ "(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   etherscanSvg: () => (/* binding */ etherscanSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/../node_modules/lit/index.js\");\n\nconst etherscanSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 16 16\">\n  <path\n    fill=\"currentColor\"\n    d=\"M4.25 7a.63.63 0 0 0-.63.63v3.97c0 .28-.2.51-.47.54l-.75.07a.93.93 0 0 1-.9-.47A7.51 7.51 0 0 1 5.54.92a7.5 7.5 0 0 1 9.54 4.62c.12.35.06.72-.16 1-.74.97-1.68 1.78-2.6 2.44V4.44a.64.64 0 0 0-.63-.64h-1.06c-.35 0-.63.3-.63.64v5.5c0 .23-.12.42-.32.5l-.52.23V6.05c0-.36-.3-.64-.64-.64H7.45c-.35 0-.64.3-.64.64v4.97c0 .25-.17.46-.4.52a5.8 5.8 0 0 0-.45.11v-4c0-.36-.3-.65-.64-.65H4.25ZM14.07 12.4A7.49 7.49 0 0 1 3.6 14.08c4.09-.58 9.14-2.5 11.87-6.6v.03a7.56 7.56 0 0 1-1.41 4.91Z\"\n  />\n</svg>`;\n//# sourceMappingURL=etherscan.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9ldGhlcnNjYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIscUJBQXFCLHdDQUFHO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9AcmVvd24vYXBwa2l0LXVpL2Rpc3QvZXNtL3NyYy9hc3NldHMvc3ZnL2V0aGVyc2Nhbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdmcgfSBmcm9tICdsaXQnO1xuZXhwb3J0IGNvbnN0IGV0aGVyc2NhblN2ZyA9IHN2ZyBgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMTYgMTZcIj5cbiAgPHBhdGhcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICBkPVwiTTQuMjUgN2EuNjMuNjMgMCAwIDAtLjYzLjYzdjMuOTdjMCAuMjgtLjIuNTEtLjQ3LjU0bC0uNzUuMDdhLjkzLjkzIDAgMCAxLS45LS40N0E3LjUxIDcuNTEgMCAwIDEgNS41NC45MmE3LjUgNy41IDAgMCAxIDkuNTQgNC42MmMuMTIuMzUuMDYuNzItLjE2IDEtLjc0Ljk3LTEuNjggMS43OC0yLjYgMi40NFY0LjQ0YS42NC42NCAwIDAgMC0uNjMtLjY0aC0xLjA2Yy0uMzUgMC0uNjMuMy0uNjMuNjR2NS41YzAgLjIzLS4xMi40Mi0uMzIuNWwtLjUyLjIzVjYuMDVjMC0uMzYtLjMtLjY0LS42NC0uNjRINy40NWMtLjM1IDAtLjY0LjMtLjY0LjY0djQuOTdjMCAuMjUtLjE3LjQ2LS40LjUyYTUuOCA1LjggMCAwIDAtLjQ1LjExdi00YzAtLjM2LS4zLS42NS0uNjQtLjY1SDQuMjVaTTE0LjA3IDEyLjRBNy40OSA3LjQ5IDAgMCAxIDMuNiAxNC4wOGM0LjA5LS41OCA5LjE0LTIuNSAxMS44Ny02LjZ2LjAzYTcuNTYgNy41NiAwIDAgMS0xLjQxIDQuOTFaXCJcbiAgLz5cbjwvc3ZnPmA7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ldGhlcnNjYW4uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js\n"));

/***/ })

}]);