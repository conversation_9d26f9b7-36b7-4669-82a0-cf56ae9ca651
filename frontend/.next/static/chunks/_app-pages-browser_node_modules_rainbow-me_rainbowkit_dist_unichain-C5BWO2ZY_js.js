"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rainbow-me_rainbowkit_dist_unichain-C5BWO2ZY_js"],{

/***/ "(app-pages-browser)/../node_modules/@rainbow-me/rainbowkit/dist/unichain-C5BWO2ZY.js":
/*!************************************************************************!*\
  !*** ../node_modules/@rainbow-me/rainbowkit/dist/unichain-C5BWO2ZY.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ unichain_default)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ default auto */ // src/components/RainbowKitProvider/chainIcons/unichain.svg\nvar unichain_default = \"data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2028%2028%22%3E%3Cg%20transform%3D%22translate(0%2C0)%20scale(0.7)%22%3E%3Cg%20clip-path%3D%22url(%23a)%22%3E%3Cpath%20fill%3D%22url(%23b)%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M36%2019.696c-8.672%200-15.696-7.03-15.696-15.696h-.608v15.696H4v.608c8.673%200%2015.696%207.03%2015.696%2015.696h.608V20.304H36v-.608Z%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3CradialGradient%20id%3D%22b%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(0%2020%20-20%200%2020%2020)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23FC74FE%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23F50DB4%22%2F%3E%3C%2FradialGradient%3E%3CclipPath%20id%3D%22a%22%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3C%2FclipPath%3E%3C%2Fdefs%3E%3C%2Fg%3E%3C%2Fsvg%3E\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHJhaW5ib3ctbWUvcmFpbmJvd2tpdC9kaXN0L3VuaWNoYWluLUM1QldPMlpZLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkRBRUEsNERBQTREO0FBQzVELElBQUlBLG1CQUFtQjtBQUdyQiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9AcmFpbmJvdy1tZS9yYWluYm93a2l0L2Rpc3QvdW5pY2hhaW4tQzVCV08yWlkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbi8vIHNyYy9jb21wb25lbnRzL1JhaW5ib3dLaXRQcm92aWRlci9jaGFpbkljb25zL3VuaWNoYWluLnN2Z1xudmFyIHVuaWNoYWluX2RlZmF1bHQgPSBcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM0NzdmclMjB4bWxucyUzRCUyMmh0dHAlM0ElMkYlMkZ3d3cudzMub3JnJTJGMjAwMCUyRnN2ZyUyMiUyMGZpbGwlM0QlMjJub25lJTIyJTIwdmlld0JveCUzRCUyMjAlMjAwJTIwMjglMjAyOCUyMiUzRSUzQ2clMjB0cmFuc2Zvcm0lM0QlMjJ0cmFuc2xhdGUoMCUyQzApJTIwc2NhbGUoMC43KSUyMiUzRSUzQ2clMjBjbGlwLXBhdGglM0QlMjJ1cmwoJTIzYSklMjIlM0UlM0NwYXRoJTIwZmlsbCUzRCUyMnVybCglMjNiKSUyMiUyMGQlM0QlMjJNMCUyMDBoNDB2NDBIMHolMjIlMkYlM0UlM0NwYXRoJTIwZmlsbCUzRCUyMiUyM2ZmZiUyMiUyMGQlM0QlMjJNMzYlMjAxOS42OTZjLTguNjcyJTIwMC0xNS42OTYtNy4wMy0xNS42OTYtMTUuNjk2aC0uNjA4djE1LjY5Nkg0di42MDhjOC42NzMlMjAwJTIwMTUuNjk2JTIwNy4wMyUyMDE1LjY5NiUyMDE1LjY5NmguNjA4VjIwLjMwNEgzNnYtLjYwOFolMjIlMkYlM0UlM0MlMkZnJTNFJTNDZGVmcyUzRSUzQ3JhZGlhbEdyYWRpZW50JTIwaWQlM0QlMjJiJTIyJTIwY3glM0QlMjIwJTIyJTIwY3klM0QlMjIwJTIyJTIwciUzRCUyMjElMjIlMjBncmFkaWVudFRyYW5zZm9ybSUzRCUyMm1hdHJpeCgwJTIwMjAlMjAtMjAlMjAwJTIwMjAlMjAyMCklMjIlMjBncmFkaWVudFVuaXRzJTNEJTIydXNlclNwYWNlT25Vc2UlMjIlM0UlM0NzdG9wJTIwc3RvcC1jb2xvciUzRCUyMiUyM0ZDNzRGRSUyMiUyRiUzRSUzQ3N0b3AlMjBvZmZzZXQlM0QlMjIxJTIyJTIwc3RvcC1jb2xvciUzRCUyMiUyM0Y1MERCNCUyMiUyRiUzRSUzQyUyRnJhZGlhbEdyYWRpZW50JTNFJTNDY2xpcFBhdGglMjBpZCUzRCUyMmElMjIlM0UlM0NwYXRoJTIwZmlsbCUzRCUyMiUyM2ZmZiUyMiUyMGQlM0QlMjJNMCUyMDBoNDB2NDBIMHolMjIlMkYlM0UlM0MlMkZjbGlwUGF0aCUzRSUzQyUyRmRlZnMlM0UlM0MlMkZnJTNFJTNDJTJGc3ZnJTNFXCI7XG5leHBvcnQge1xuICB1bmljaGFpbl9kZWZhdWx0IGFzIGRlZmF1bHRcbn07XG4iXSwibmFtZXMiOlsidW5pY2hhaW5fZGVmYXVsdCIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@rainbow-me/rainbowkit/dist/unichain-C5BWO2ZY.js\n"));

/***/ })

}]);