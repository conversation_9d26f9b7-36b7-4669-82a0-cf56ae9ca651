"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_qr-code_js"],{

/***/ "(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js":
/*!***************************************************************************!*\
  !*** ../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   qrCodeIcon: () => (/* binding */ qrCodeIcon)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/../node_modules/lit/index.js\");\n\nconst qrCodeIcon = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 20 20\">\n  <path\n    fill=\"currentColor\"\n    d=\"M3 6a3 3 0 0 1 3-3h1a1 1 0 1 0 0-2H6a5 5 0 0 0-5 5v1a1 1 0 0 0 2 0V6ZM13 1a1 1 0 1 0 0 2h1a3 3 0 0 1 3 3v1a1 1 0 1 0 2 0V6a5 5 0 0 0-5-5h-1ZM3 13a1 1 0 1 0-2 0v1a5 5 0 0 0 5 5h1a1 1 0 1 0 0-2H6a3 3 0 0 1-3-3v-1ZM19 13a1 1 0 1 0-2 0v1a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1.01a5 5 0 0 0 5-5v-1ZM5.3 6.36c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05A1.5 1.5 0 0 0 9.2 8.14c.06-.2.06-.43.06-.89s0-.7-.06-.89A1.5 1.5 0 0 0 8.14 5.3c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06ZM10.8 6.36c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05a1.5 1.5 0 0 0 1.06-1.06c.06-.2.06-.43.06-.89s0-.7-.06-.89a1.5 1.5 0 0 0-1.06-1.06c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06ZM5.26 12.75c0-.46 0-.7.05-.89a1.5 1.5 0 0 1 1.06-1.06c.19-.05.42-.05.89-.05.46 0 .7 0 .88.05.52.14.93.54 1.06 1.06.06.2.06.43.06.89s0 .7-.06.89a1.5 1.5 0 0 1-1.06 1.06c-.19.05-.42.05-.88.05-.47 0-.7 0-.9-.05a1.5 1.5 0 0 1-1.05-1.06c-.05-.2-.05-.43-.05-.89ZM10.8 11.86c-.04.2-.04.43-.04.89s0 .7.05.89c.14.52.54.92 1.06 1.06.19.05.42.05.89.05.46 0 .7 0 .88-.05a1.5 1.5 0 0 0 1.06-1.06c.06-.2.06-.43.06-.89s0-.7-.06-.89a1.5 1.5 0 0 0-1.06-1.06c-.19-.05-.42-.05-.88-.05-.47 0-.7 0-.9.05a1.5 1.5 0 0 0-1.05 1.06Z\"\n  />\n</svg>`;\n//# sourceMappingURL=qr-code.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js\n"));

/***/ })

}]);