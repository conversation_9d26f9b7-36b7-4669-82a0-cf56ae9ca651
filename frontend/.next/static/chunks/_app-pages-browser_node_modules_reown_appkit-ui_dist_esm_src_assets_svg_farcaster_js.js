"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_farcaster_js"],{

/***/ "(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   farcasterSvg: () => (/* binding */ farcasterSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/../node_modules/lit/index.js\");\n\nconst farcasterSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg style=\"border-radius: 9999px; overflow: hidden;\"  fill=\"none\" viewBox=\"0 0 1000 1000\">\n  <rect width=\"1000\" height=\"1000\" rx=\"9999\" ry=\"9999\" fill=\"#855DCD\"/>\n  <path fill=\"#855DCD\" d=\"M0 0h1000v1000H0V0Z\" />\n  <path\n    fill=\"#fff\"\n    d=\"M320 248h354v504h-51.96V521.13h-.5c-5.76-63.8-59.31-113.81-124.54-113.81s-118.78 50-124.53 113.81h-.5V752H320V248Z\"\n  />\n  <path\n    fill=\"#fff\"\n    d=\"m225 320 21.16 71.46h17.9v289.09a16.29 16.29 0 0 0-16.28 16.24v19.49h-3.25a16.3 16.3 0 0 0-16.28 16.24V752h182.26v-19.48a16.22 16.22 0 0 0-16.28-16.24h-3.25v-19.5a16.22 16.22 0 0 0-16.28-16.23h-19.52V320H225Zm400.3 360.55a16.3 16.3 0 0 0-15.04 10.02 16.2 16.2 0 0 0-1.24 6.22v19.49h-3.25a16.29 16.29 0 0 0-16.27 16.24V752h182.24v-19.48a16.23 16.23 0 0 0-16.27-16.24h-3.25v-19.5a16.2 16.2 0 0 0-10.04-15 16.3 16.3 0 0 0-6.23-1.23v-289.1h17.9L775 320H644.82v360.55H625.3Z\"\n  />\n</svg>`;\n//# sourceMappingURL=farcaster.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9mYXJjYXN0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEI7QUFDbkIscUJBQXFCLHdDQUFHLHFDQUFxQyxpQkFBaUI7QUFDckY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvZmFyY2FzdGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN2ZyB9IGZyb20gJ2xpdCc7XG5leHBvcnQgY29uc3QgZmFyY2FzdGVyU3ZnID0gc3ZnIGA8c3ZnIHN0eWxlPVwiYm9yZGVyLXJhZGl1czogOTk5OXB4OyBvdmVyZmxvdzogaGlkZGVuO1wiICBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMTAwMCAxMDAwXCI+XG4gIDxyZWN0IHdpZHRoPVwiMTAwMFwiIGhlaWdodD1cIjEwMDBcIiByeD1cIjk5OTlcIiByeT1cIjk5OTlcIiBmaWxsPVwiIzg1NURDRFwiLz5cbiAgPHBhdGggZmlsbD1cIiM4NTVEQ0RcIiBkPVwiTTAgMGgxMDAwdjEwMDBIMFYwWlwiIC8+XG4gIDxwYXRoXG4gICAgZmlsbD1cIiNmZmZcIlxuICAgIGQ9XCJNMzIwIDI0OGgzNTR2NTA0aC01MS45NlY1MjEuMTNoLS41Yy01Ljc2LTYzLjgtNTkuMzEtMTEzLjgxLTEyNC41NC0xMTMuODFzLTExOC43OCA1MC0xMjQuNTMgMTEzLjgxaC0uNVY3NTJIMzIwVjI0OFpcIlxuICAvPlxuICA8cGF0aFxuICAgIGZpbGw9XCIjZmZmXCJcbiAgICBkPVwibTIyNSAzMjAgMjEuMTYgNzEuNDZoMTcuOXYyODkuMDlhMTYuMjkgMTYuMjkgMCAwIDAtMTYuMjggMTYuMjR2MTkuNDloLTMuMjVhMTYuMyAxNi4zIDAgMCAwLTE2LjI4IDE2LjI0Vjc1MmgxODIuMjZ2LTE5LjQ4YTE2LjIyIDE2LjIyIDAgMCAwLTE2LjI4LTE2LjI0aC0zLjI1di0xOS41YTE2LjIyIDE2LjIyIDAgMCAwLTE2LjI4LTE2LjIzaC0xOS41MlYzMjBIMjI1Wm00MDAuMyAzNjAuNTVhMTYuMyAxNi4zIDAgMCAwLTE1LjA0IDEwLjAyIDE2LjIgMTYuMiAwIDAgMC0xLjI0IDYuMjJ2MTkuNDloLTMuMjVhMTYuMjkgMTYuMjkgMCAwIDAtMTYuMjcgMTYuMjRWNzUyaDE4Mi4yNHYtMTkuNDhhMTYuMjMgMTYuMjMgMCAwIDAtMTYuMjctMTYuMjRoLTMuMjV2LTE5LjVhMTYuMiAxNi4yIDAgMCAwLTEwLjA0LTE1IDE2LjMgMTYuMyAwIDAgMC02LjIzLTEuMjN2LTI4OS4xaDE3LjlMNzc1IDMyMEg2NDQuODJ2MzYwLjU1SDYyNS4zWlwiXG4gIC8+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZmFyY2FzdGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js\n"));

/***/ })

}]);