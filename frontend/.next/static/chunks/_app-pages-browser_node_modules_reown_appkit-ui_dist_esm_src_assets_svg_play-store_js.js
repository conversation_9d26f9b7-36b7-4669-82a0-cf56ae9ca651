"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_play-store_js"],{

/***/ "(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   playStoreSvg: () => (/* binding */ playStoreSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/../node_modules/lit/index.js\");\n\nconst playStoreSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) ` <svg\n  width=\"36\"\n  height=\"36\"\n  fill=\"none\"\n>\n  <path\n    d=\"M0 8a8 8 0 0 1 8-8h20a8 8 0 0 1 8 8v20a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z\"\n    fill=\"#fff\"\n    fill-opacity=\".05\"\n  />\n  <path\n    d=\"m18.262 17.513-8.944 9.49v.01a2.417 2.417 0 0 0 3.56 1.452l.026-.017 10.061-5.803-4.703-5.132Z\"\n    fill=\"#EA4335\"\n  />\n  <path\n    d=\"m27.307 15.9-.008-.008-4.342-2.52-4.896 4.36 4.913 4.912 4.325-2.494a2.42 2.42 0 0 0 .008-4.25Z\"\n    fill=\"#FBBC04\"\n  />\n  <path\n    d=\"M9.318 8.997c-.05.202-.084.403-.084.622V26.39c0 .218.025.42.084.621l9.246-9.247-9.246-8.768Z\"\n    fill=\"#4285F4\"\n  />\n  <path\n    d=\"m18.33 18 4.627-4.628-10.053-5.828a2.427 2.427 0 0 0-3.586 1.444L18.329 18Z\"\n    fill=\"#34A853\"\n  />\n  <path\n    d=\"M8 .5h20A7.5 7.5 0 0 1 35.5 8v20a7.5 7.5 0 0 1-7.5 7.5H8A7.5 7.5 0 0 1 .5 28V8A7.5 7.5 0 0 1 8 .5Z\"\n    stroke=\"#fff\"\n    stroke-opacity=\".05\"\n  />\n</svg>`;\n//# sourceMappingURL=play-store.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js\n"));

/***/ })

}]);