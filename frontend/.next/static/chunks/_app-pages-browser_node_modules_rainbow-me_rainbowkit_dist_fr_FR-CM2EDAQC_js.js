"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_rainbow-me_rainbowkit_dist_fr_FR-CM2EDAQC_js"],{

/***/ "(app-pages-browser)/../node_modules/@rainbow-me/rainbowkit/dist/fr_FR-CM2EDAQC.js":
/*!*********************************************************************!*\
  !*** ../node_modules/@rainbow-me/rainbowkit/dist/fr_FR-CM2EDAQC.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ fr_FR_default)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ default auto */ // src/locales/fr_FR.json\nvar fr_FR_default = '{\\n  \"connect_wallet\": {\\n    \"label\": \"Connecter le portefeuille\",\\n    \"wrong_network\": {\\n      \"label\": \"R\\xe9seau incorrect\"\\n    }\\n  },\\n  \"intro\": {\\n    \"title\": \"Qu\\'est-ce qu\\'un portefeuille?\",\\n    \"description\": \"Un portefeuille est utilis\\xe9 pour envoyer, recevoir, stocker et afficher des actifs num\\xe9riques. C\\'est aussi une nouvelle fa\\xe7on de se connecter, sans avoir besoin de cr\\xe9er de nouveaux comptes et mots de passe sur chaque site.\",\\n    \"digital_asset\": {\\n      \"title\": \"Un foyer pour vos actifs num\\xe9riques\",\\n      \"description\": \"Les portefeuilles sont utilis\\xe9s pour envoyer, recevoir, stocker et afficher des actifs num\\xe9riques comme Ethereum et les NFTs.\"\\n    },\\n    \"login\": {\\n      \"title\": \"Une nouvelle fa\\xe7on de se connecter\",\\n      \"description\": \"Au lieu de cr\\xe9er de nouveaux comptes et mots de passe sur chaque site Web, connectez simplement votre portefeuille.\"\\n    },\\n    \"get\": {\\n      \"label\": \"Obtenir un portefeuille\"\\n    },\\n    \"learn_more\": {\\n      \"label\": \"En savoir plus\"\\n    }\\n  },\\n  \"sign_in\": {\\n    \"label\": \"V\\xe9rifiez votre compte\",\\n    \"description\": \"Pour terminer la connexion, vous devez signer un message dans votre portefeuille pour v\\xe9rifier que vous \\xeates le propri\\xe9taire de ce compte.\",\\n    \"message\": {\\n      \"send\": \"Envoyer le message\",\\n      \"preparing\": \"Pr\\xe9paration du message...\",\\n      \"cancel\": \"Annuler\",\\n      \"preparing_error\": \"Erreur lors de la pr\\xe9paration du message, veuillez r\\xe9essayer!\"\\n    },\\n    \"signature\": {\\n      \"waiting\": \"En attente de la signature...\",\\n      \"verifying\": \"V\\xe9rification de la signature...\",\\n      \"signing_error\": \"Erreur lors de la signature du message, veuillez r\\xe9essayer!\",\\n      \"verifying_error\": \"Erreur lors de la v\\xe9rification de la signature, veuillez r\\xe9essayer!\",\\n      \"oops_error\": \"Oups, quelque chose a mal tourn\\xe9!\"\\n    }\\n  },\\n  \"connect\": {\\n    \"label\": \"Connecter\",\\n    \"title\": \"Connecter un portefeuille\",\\n    \"new_to_ethereum\": {\\n      \"description\": \"Nouveau aux portefeuilles Ethereum?\",\\n      \"learn_more\": {\\n        \"label\": \"En savoir plus\"\\n      }\\n    },\\n    \"learn_more\": {\\n      \"label\": \"En savoir plus\"\\n    },\\n    \"recent\": \"R\\xe9cents\",\\n    \"status\": {\\n      \"opening\": \"Ouverture %{wallet}...\",\\n      \"connecting\": \"Connect :)ing\",\\n      \"connect_mobile\": \"Continuer dans %{wallet}\",\\n      \"not_installed\": \"%{wallet} n\\'est pas install\\xe9\",\\n      \"not_available\": \"%{wallet} n\\'est pas disponible\",\\n      \"confirm\": \"Confirmez la connexion dans l\\'extension\",\\n      \"confirm_mobile\": \"Accepter la demande de connexion dans le portefeuille\"\\n    },\\n    \"secondary_action\": {\\n      \"get\": {\\n        \"description\": \"Vous n\\'avez pas de %{wallet}?\",\\n        \"label\": \"OBTENIR\"\\n      },\\n      \"install\": {\\n        \"label\": \"INSTALLER\"\\n      },\\n      \"retry\": {\\n        \"label\": \"R\\xc9ESSAYER\"\\n      }\\n    },\\n    \"walletconnect\": {\\n      \"description\": {\\n        \"full\": \"Vous avez besoin du modal officiel de WalletConnect ?\",\\n        \"compact\": \"Besoin du modal de WalletConnect ?\"\\n      },\\n      \"open\": {\\n        \"label\": \"OUVRIR\"\\n      }\\n    }\\n  },\\n  \"connect_scan\": {\\n    \"title\": \"Scannez avec %{wallet}\",\\n    \"fallback_title\": \"Scannez avec votre t\\xe9l\\xe9phone\"\\n  },\\n  \"connector_group\": {\\n    \"installed\": \"Install\\xe9\",\\n    \"recommended\": \"Recommand\\xe9\",\\n    \"other\": \"Autre\",\\n    \"popular\": \"Populaire\",\\n    \"more\": \"Plus\",\\n    \"others\": \"Autres\"\\n  },\\n  \"get\": {\\n    \"title\": \"Obtenez un portefeuille\",\\n    \"action\": {\\n      \"label\": \"OBTENIR\"\\n    },\\n    \"mobile\": {\\n      \"description\": \"Portefeuille mobile\"\\n    },\\n    \"extension\": {\\n      \"description\": \"Extension de navigateur\"\\n    },\\n    \"mobile_and_extension\": {\\n      \"description\": \"Portefeuille mobile et extension\"\\n    },\\n    \"mobile_and_desktop\": {\\n      \"description\": \"Portefeuille mobile et de bureau\"\\n    },\\n    \"looking_for\": {\\n      \"title\": \"Ce n\\'est pas ce que vous cherchez ?\",\\n      \"mobile\": {\\n        \"description\": \"S\\xe9lectionnez un portefeuille sur l\\'\\xe9cran principal pour commencer avec un autre fournisseur de portefeuille.\"\\n      },\\n      \"desktop\": {\\n        \"compact_description\": \"S\\xe9lectionnez un portefeuille sur l\\'\\xe9cran principal pour commencer avec un autre fournisseur de portefeuille.\",\\n        \"wide_description\": \"S\\xe9lectionnez un portefeuille sur la gauche pour commencer avec un autre fournisseur de portefeuille.\"\\n      }\\n    }\\n  },\\n  \"get_options\": {\\n    \"title\": \"Commencez avec %{wallet}\",\\n    \"short_title\": \"Obtenez %{wallet}\",\\n    \"mobile\": {\\n      \"title\": \"%{wallet} pour mobile\",\\n      \"description\": \"Utilisez le portefeuille mobile pour explorer le monde d\\'Ethereum.\",\\n      \"download\": {\\n        \"label\": \"Obtenez l\\'application\"\\n      }\\n    },\\n    \"extension\": {\\n      \"title\": \"%{wallet} pour %{browser}\",\\n      \"description\": \"Acc\\xe9dez \\xe0 votre portefeuille directement depuis votre navigateur web pr\\xe9f\\xe9r\\xe9.\",\\n      \"download\": {\\n        \"label\": \"Ajouter \\xe0 %{browser}\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"title\": \"%{wallet} pour %{platform}\",\\n      \"description\": \"Acc\\xe9dez \\xe0 votre portefeuille nativement depuis votre puissant ordinateur de bureau.\",\\n      \"download\": {\\n        \"label\": \"Ajouter \\xe0 %{platform}\"\\n      }\\n    }\\n  },\\n  \"get_mobile\": {\\n    \"title\": \"Installer %{wallet}\",\\n    \"description\": \"Scannez avec votre t\\xe9l\\xe9phone pour t\\xe9l\\xe9charger sur iOS ou Android\",\\n    \"continue\": {\\n      \"label\": \"Continuer\"\\n    }\\n  },\\n  \"get_instructions\": {\\n    \"mobile\": {\\n      \"connect\": {\\n        \"label\": \"Connecter\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"En savoir plus\"\\n      }\\n    },\\n    \"extension\": {\\n      \"refresh\": {\\n        \"label\": \"Rafra\\xeechir\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"En savoir plus\"\\n      }\\n    },\\n    \"desktop\": {\\n      \"connect\": {\\n        \"label\": \"Connecter\"\\n      },\\n      \"learn_more\": {\\n        \"label\": \"En savoir plus\"\\n      }\\n    }\\n  },\\n  \"chains\": {\\n    \"title\": \"Changer de r\\xe9seau\",\\n    \"wrong_network\": \"Mauvais r\\xe9seau d\\xe9tect\\xe9, changez ou d\\xe9connectez-vous pour continuer.\",\\n    \"confirm\": \"Confirmer dans le portefeuille\",\\n    \"switching_not_supported\": \"Votre portefeuille ne supporte pas le changement de r\\xe9seau depuis %{appName}. Essayez de changer de r\\xe9seau depuis votre portefeuille.\",\\n    \"switching_not_supported_fallback\": \"Votre portefeuille ne prend pas en charge le changement de r\\xe9seau \\xe0 partir de cette application. Essayez de changer de r\\xe9seau \\xe0 partir de votre portefeuille \\xe0 la place.\",\\n    \"disconnect\": \"D\\xe9connecter\",\\n    \"connected\": \"Connect\\xe9\"\\n  },\\n  \"profile\": {\\n    \"disconnect\": {\\n      \"label\": \"D\\xe9connecter\"\\n    },\\n    \"copy_address\": {\\n      \"label\": \"Copier l\\'adresse\",\\n      \"copied\": \"Copi\\xe9 !\"\\n    },\\n    \"explorer\": {\\n      \"label\": \"Voir plus sur l\\'explorateur\"\\n    },\\n    \"transactions\": {\\n      \"description\": \"%{appName} transactions appara\\xeetront ici...\",\\n      \"description_fallback\": \"Vos transactions appara\\xeetront ici...\",\\n      \"recent\": {\\n        \"title\": \"Transactions R\\xe9centes\"\\n      },\\n      \"clear\": {\\n        \"label\": \"Tout supprimer\"\\n      }\\n    }\\n  },\\n  \"wallet_connectors\": {\\n    \"argent\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Mettez Argent sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Argent\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un portefeuille et un nom d\\'utilisateur, ou importez un portefeuille existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton Scan QR\"\\n        }\\n      }\\n    },\\n    \"berasig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension BeraSig\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler BeraSig \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"best\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Best Wallet\",\\n          \"description\": \"Ajoutez Best Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"bifrost\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de mettre le portefeuille Bifrost sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Bifrost Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez ou importez un portefeuille en utilisant votre phrase de r\\xe9cup\\xe9ration.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s votre scan, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      }\\n    },\\n    \"bitget\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de placer Bitget Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Bitget Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s le scan, une incitation de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Bitget Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension de portefeuille Bitget\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9ez ou Importez un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"bitski\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Bitski \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Bitski\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"bitverse\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Bitverse Wallet\",\\n          \"description\": \"Ajoutez Bitverse Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"bloom\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Bloom Wallet\",\\n          \"description\": \"Nous recommandons de placer Bloom Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez ou importez un portefeuille en utilisant votre phrase de r\\xe9cup\\xe9ration.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir obtenu un portefeuille, cliquez sur Connecter pour vous connecter via Bloom. Une invite de connexion appara\\xeetra dans l\\'application pour que vous confirmiez la connexion.\",\\n          \"title\": \"Cliquez sur Connecter\"\\n        }\\n      }\\n    },\\n    \"bybit\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer Bybit sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Bybit\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Cliquez en haut \\xe0 droite de votre navigateur et \\xe9pinglez le portefeuille Bybit pour un acc\\xe8s facile.\",\\n          \"title\": \"Installez l\\'extension Bybit Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 Bybit Wallet, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"binance\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de mettre Binance sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Binance\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton WalletConnect\"\\n        }\\n      }\\n    },\\n    \"coin98\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de placer Coin98 Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Coin98 Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s que vous ayez scann\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton WalletConnect\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Cliquez en haut \\xe0 droite de votre navigateur et \\xe9pinglez Coin98 Wallet pour un acc\\xe8s facile.\",\\n          \"title\": \"Installez l\\'extension Coin98 Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 Coin98 Wallet, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"coinbase\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer Coinbase Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Coinbase Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant la fonction de sauvegarde cloud.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion s\\'affichera pour que vous puissiez connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Coinbase Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Coinbase Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xfbre. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Actualisez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"compass\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Compass Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Compass Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"core\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer Core sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application Core\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton WalletConnect\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Core \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Core\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9ez ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"fox\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de mettre FoxWallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application FoxWallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invitation \\xe0 la connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      }\\n    },\\n    \"frontier\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de placer le portefeuille Frontier sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Frontier Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Frontier Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Frontier Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9ez ou importez un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"im_token\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application imToken\",\\n          \"description\": \"Placez l\\'application imToken sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou importez un portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant .\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur l\\'ic\\xf4ne du scanner dans le coin sup\\xe9rieur droit\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"iopay\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer ioPay sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application ioPay\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton WalletConnect\"\\n        }\\n      }\\n    },\\n    \"kaikas\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Kaikas \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Kaikas\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Kaikas\",\\n          \"description\": \"Placez l\\'application Kaikas sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur l\\'ic\\xf4ne du scanner dans le coin sup\\xe9rieur droit\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"kaia\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Kaia \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Kaia\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Kaia\",\\n          \"description\": \"Mettez l\\'application Kaia sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur l\\'ic\\xf4ne du scanner dans le coin sup\\xe9rieur droit\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"kraken\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Kraken Wallet\",\\n          \"description\": \"Ajoutez Kraken Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"kresus\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Kresus Wallet\",\\n          \"description\": \"Ajoutez Kresus Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"magicEden\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Magic Eden\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Magic Eden \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"metamask\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application MetaMask\",\\n          \"description\": \"Nous vous recommandons de mettre MetaMask sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Veillez \\xe0 sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l’extension de MetaMask\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler MetaMask \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"nestwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension NestWallet\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler NestWallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"okx\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application OKX Wallet\",\\n          \"description\": \"Nous recommandons de mettre OKX Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de num\\xe9risation\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension de portefeuille OKX\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler le portefeuille OKX \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"omni\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Omni\",\\n          \"description\": \"Ajoutez Omni \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Appuyez sur l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"1inch\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Placez 1inch Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application 1inch Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un portefeuille et un nom d\\'utilisateur, ou importez un portefeuille existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton Scan QR\"\\n        }\\n      }\\n    },\\n    \"token_pocket\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application TokenPocket\",\\n          \"description\": \"Nous vous recommandons de mettre TokenPocket sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille \\xe0 l\\'aide d\\'une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s votre scan, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension TokenPocket\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler TokenPocket \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"trust\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Trust Wallet\",\\n          \"description\": \"Placez Trust Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Cr\\xe9er un nouveau portefeuille ou en importer un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur WalletConnect dans les param\\xe8tres\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Trust Wallet\",\\n          \"description\": \"Cliquez en haut \\xe0 droite de votre navigateur et \\xe9pinglez Trust Wallet pour un acc\\xe8s facile.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou importer un portefeuille\",\\n          \"description\": \"Cr\\xe9er un nouveau portefeuille ou en importer un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 Trust Wallet, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"uniswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Uniswap\",\\n          \"description\": \"Ajoutez Uniswap Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou importez un portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Tapez sur l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"zerion\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Zerion\",\\n          \"description\": \"Nous vous recommandons de mettre Zerion sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Une fois que vous avez scann\\xe9, une invite de connexion appara\\xeetra pour que vous puissiez connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installer l\\'extension Zerion\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Zerion \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou Importez un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"rainbow\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvre l\\'application Rainbow\",\\n          \"description\": \"Nous vous recommandons de mettre Rainbow sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou Importez un portefeuille\",\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant notre fonction de sauvegarde sur votre t\\xe9l\\xe9phone.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir scann\\xe9, une invite de connexion appara\\xeetra pour que vous connectiez votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"enkrypt\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Enkrypt Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Enkrypt Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quelqu\\'un.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l’extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"frame\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Frame \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez Frame & l\\'extension compl\\xe9mentaire\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille \\xe0 l\\'aide d\\'une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\",\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"one_key\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension OneKey Wallet\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler OneKey Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"paraswap\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application ParaSwap\",\\n          \"description\": \"Ajoutez ParaSwap Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"phantom\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Phantom\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Phantom \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"rabby\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Rabby\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Rabby \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Actualisez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"ronin\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons de placer Ronin Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application Ronin Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Ronin Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Installez l\\'extension Ronin Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\",\\n          \"title\": \"Rafra\\xeechissez votre navigateur\"\\n        }\\n      }\\n    },\\n    \"ramper\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Ramper\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Ramper \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"safeheron\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Core\",\\n          \"description\": \"Nous recommandons d\\'\\xe9pingler Safeheron \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quelqu\\'un.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"taho\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Taho\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Taho \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9ez ou Importez un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quelqu\\'un.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"wigwam\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Wigwam\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Wigwam \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"talisman\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Talisman\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Talisman \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou importer un portefeuille Ethereum\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"xdefi\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension du portefeuille XDEFI\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler XDEFI Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec qui que ce soit.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"zeal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Zeal\",\\n          \"description\": \"Ajoutez Zeal Wallet \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Touchez l\\'ic\\xf4ne QR et scannez\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne QR sur votre \\xe9cran d\\'accueil, scannez le code et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Zeal\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Zeal \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"safepal\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension SafePal Wallet\",\\n          \"description\": \"Cliquez en haut \\xe0 droite de votre navigateur et \\xe9pinglez SafePal Wallet pour un acc\\xe8s facile.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 SafePal Wallet, cliquez ci-dessous pour rafra\\xeechir le navigateur et charger l\\'extension.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application SafePal Wallet\",\\n          \"description\": \"Mettez SafePal Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur WalletConnect dans les param\\xe8tres\",\\n          \"description\": \"Choisissez Nouvelle Connexion, puis scannez le code QR et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"desig\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Desig\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Desig \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"subwallet\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension SubWallet\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler SubWallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application SubWallet\",\\n          \"description\": \"Nous vous recommandons de mettre SubWallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"clv\": {\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension CLV Wallet\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler CLV Wallet \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application CLV Wallet\",\\n          \"description\": \"Nous vous recommandons de mettre CLV Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase secr\\xe8te avec quiconque.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"okto\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Okto\",\\n          \"description\": \"Ajoutez Okto \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s rapide\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er un portefeuille MPC\",\\n          \"description\": \"Cr\\xe9ez un compte et g\\xe9n\\xe9rez un portefeuille\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur WalletConnect dans les param\\xe8tres\",\\n          \"description\": \"Touchez l\\'ic\\xf4ne \\'Scan QR\\' en haut \\xe0 droite et confirmez l\\'invite pour vous connecter.\"\\n        }\\n      }\\n    },\\n    \"ledger\": {\\n      \"desktop\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Ledger Live\",\\n          \"description\": \"Nous vous recommandons de mettre Ledger Live sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Configurez votre Ledger\",\\n          \"description\": \"Configurez un nouveau Ledger ou connectez-vous \\xe0 un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Connecter\",\\n          \"description\": \"Une fois que vous avez scann\\xe9, une invite de connexion appara\\xeetra pour que vous puissiez connecter votre portefeuille.\"\\n        }\\n      },\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Ledger Live\",\\n          \"description\": \"Nous vous recommandons de mettre Ledger Live sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Configurez votre Ledger\",\\n          \"description\": \"Vous pouvez soit synchroniser avec l\\'application de bureau, soit connecter votre Ledger.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Scannez le code\",\\n          \"description\": \"Appuyez sur WalletConnect puis passez au Scanner. Une fois que vous avez scann\\xe9, une invite de connexion appara\\xeetra pour que vous puissiez connecter votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"valora\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Valora\",\\n          \"description\": \"Nous vous recommandons de mettre Valora sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou importer un portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      }\\n    },\\n    \"gate\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"title\": \"Ouvrez l\\'application Gate\",\\n          \"description\": \"Nous vous recommandons de mettre Gate sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Appuyez sur le bouton de scan\",\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n        }\\n      },\\n      \"extension\": {\\n        \"step1\": {\\n          \"title\": \"Installez l\\'extension Gate\",\\n          \"description\": \"Nous vous recommandons d\\'\\xe9pingler Gate \\xe0 votre barre des t\\xe2ches pour un acc\\xe8s plus facile \\xe0 votre portefeuille.\"\\n        },\\n        \"step2\": {\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n          \"description\": \"Assurez-vous de sauvegarder votre portefeuille en utilisant une m\\xe9thode s\\xe9curis\\xe9e. Ne partagez jamais votre phrase de r\\xe9cup\\xe9ration secr\\xe8te avec personne.\"\\n        },\\n        \"step3\": {\\n          \"title\": \"Rafra\\xeechissez votre navigateur\",\\n          \"description\": \"Une fois que vous avez configur\\xe9 votre portefeuille, cliquez ci-dessous pour actualiser le navigateur et charger l\\'extension.\"\\n        }\\n      }\\n    },\\n    \"xportal\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Mettez xPortal sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\",\\n          \"title\": \"Ouvrez l\\'application xPortal\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Cr\\xe9ez un portefeuille ou importez-en un existant.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton Scan QR\"\\n        }\\n      }\\n    },\\n    \"mew\": {\\n      \"qr_code\": {\\n        \"step1\": {\\n          \"description\": \"Nous vous recommandons de mettre MEW Wallet sur votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide.\",\\n          \"title\": \"Ouvrez l\\'application MEW Wallet\"\\n        },\\n        \"step2\": {\\n          \"description\": \"Vous pouvez facilement sauvegarder votre portefeuille en utilisant la fonction de sauvegarde cloud.\",\\n          \"title\": \"Cr\\xe9er ou Importer un Portefeuille\"\\n        },\\n        \"step3\": {\\n          \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\",\\n          \"title\": \"Appuyez sur le bouton de scan\"\\n        }\\n      }\\n    }\\n  },\\n  \"zilpay\": {\\n    \"qr_code\": {\\n      \"step1\": {\\n        \"title\": \"Ouvrez l\\'application ZilPay\",\\n        \"description\": \"Ajoutez ZilPay \\xe0 votre \\xe9cran d\\'accueil pour un acc\\xe8s plus rapide \\xe0 votre portefeuille.\"\\n      },\\n      \"step2\": {\\n        \"title\": \"Cr\\xe9er ou Importer un Portefeuille\",\\n        \"description\": \"Cr\\xe9ez un nouveau portefeuille ou importez-en un existant.\"\\n      },\\n      \"step3\": {\\n        \"title\": \"Appuyez sur le bouton de scan\",\\n        \"description\": \"Apr\\xe8s avoir num\\xe9ris\\xe9, une invite de connexion appara\\xeetra pour vous permettre de connecter votre portefeuille.\"\\n      }\\n    }\\n  }\\n}\\n';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@rainbow-me/rainbowkit/dist/fr_FR-CM2EDAQC.js\n"));

/***/ })

}]);