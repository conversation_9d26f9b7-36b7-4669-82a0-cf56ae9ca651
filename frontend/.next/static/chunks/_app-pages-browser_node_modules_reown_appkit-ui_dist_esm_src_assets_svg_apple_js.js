"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_reown_appkit-ui_dist_esm_src_assets_svg_apple_js"],{

/***/ "(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js":
/*!*************************************************************************!*\
  !*** ../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appleSvg: () => (/* binding */ appleSvg)\n/* harmony export */ });\n/* harmony import */ var lit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lit */ \"(app-pages-browser)/../node_modules/lit/index.js\");\n\nconst appleSvg = (0,lit__WEBPACK_IMPORTED_MODULE_0__.svg) `<svg fill=\"none\" viewBox=\"0 0 40 40\">\n  <g clip-path=\"url(#a)\">\n    <g clip-path=\"url(#b)\">\n      <circle cx=\"20\" cy=\"19.89\" r=\"20\" fill=\"#000\" />\n      <g clip-path=\"url(#c)\">\n        <path\n          fill=\"#fff\"\n          d=\"M28.77 23.3c-.69 1.99-2.75 5.52-4.87 5.56-1.4.03-1.86-.84-3.46-.84-1.61 0-2.12.81-3.45.86-2.25.1-5.72-5.1-5.72-9.62 0-4.15 2.9-6.2 5.42-6.25 1.36-.02 2.64.92 ********** 0 2.38-1.13 4.02-.97.68.03 2.6.28 3.84 2.08-3.27 2.14-2.76 6.61.75 8.25ZM24.2 7.88c-2.47.1-4.49 2.69-4.2 4.84 2.28.17 4.47-2.39 4.2-4.84Z\"\n        />\n      </g>\n    </g>\n  </g>\n  <defs>\n    <clipPath id=\"a\"><rect width=\"40\" height=\"40\" fill=\"#fff\" rx=\"20\" /></clipPath>\n    <clipPath id=\"b\"><path fill=\"#fff\" d=\"M0 0h40v40H0z\" /></clipPath>\n    <clipPath id=\"c\"><path fill=\"#fff\" d=\"M8 7.89h24v24H8z\" /></clipPath>\n  </defs>\n</svg>`;\n//# sourceMappingURL=apple.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvQHJlb3duL2FwcGtpdC11aS9kaXN0L2VzbS9zcmMvYXNzZXRzL3N2Zy9hcHBsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUNuQixpQkFBaUIsd0NBQUc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0ByZW93bi9hcHBraXQtdWkvZGlzdC9lc20vc3JjL2Fzc2V0cy9zdmcvYXBwbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3ZnIH0gZnJvbSAnbGl0JztcbmV4cG9ydCBjb25zdCBhcHBsZVN2ZyA9IHN2ZyBgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgNDAgNDBcIj5cbiAgPGcgY2xpcC1wYXRoPVwidXJsKCNhKVwiPlxuICAgIDxnIGNsaXAtcGF0aD1cInVybCgjYilcIj5cbiAgICAgIDxjaXJjbGUgY3g9XCIyMFwiIGN5PVwiMTkuODlcIiByPVwiMjBcIiBmaWxsPVwiIzAwMFwiIC8+XG4gICAgICA8ZyBjbGlwLXBhdGg9XCJ1cmwoI2MpXCI+XG4gICAgICAgIDxwYXRoXG4gICAgICAgICAgZmlsbD1cIiNmZmZcIlxuICAgICAgICAgIGQ9XCJNMjguNzcgMjMuM2MtLjY5IDEuOTktMi43NSA1LjUyLTQuODcgNS41Ni0xLjQuMDMtMS44Ni0uODQtMy40Ni0uODQtMS42MSAwLTIuMTIuODEtMy40NS44Ni0yLjI1LjEtNS43Mi01LjEtNS43Mi05LjYyIDAtNC4xNSAyLjktNi4yIDUuNDItNi4yNSAxLjM2LS4wMiAyLjY0LjkyIDMuNDcuOTIuODMgMCAyLjM4LTEuMTMgNC4wMi0uOTcuNjguMDMgMi42LjI4IDMuODQgMi4wOC0zLjI3IDIuMTQtMi43NiA2LjYxLjc1IDguMjVaTTI0LjIgNy44OGMtMi40Ny4xLTQuNDkgMi42OS00LjIgNC44NCAyLjI4LjE3IDQuNDctMi4zOSA0LjItNC44NFpcIlxuICAgICAgICAvPlxuICAgICAgPC9nPlxuICAgIDwvZz5cbiAgPC9nPlxuICA8ZGVmcz5cbiAgICA8Y2xpcFBhdGggaWQ9XCJhXCI+PHJlY3Qgd2lkdGg9XCI0MFwiIGhlaWdodD1cIjQwXCIgZmlsbD1cIiNmZmZcIiByeD1cIjIwXCIgLz48L2NsaXBQYXRoPlxuICAgIDxjbGlwUGF0aCBpZD1cImJcIj48cGF0aCBmaWxsPVwiI2ZmZlwiIGQ9XCJNMCAwaDQwdjQwSDB6XCIgLz48L2NsaXBQYXRoPlxuICAgIDxjbGlwUGF0aCBpZD1cImNcIj48cGF0aCBmaWxsPVwiI2ZmZlwiIGQ9XCJNOCA3Ljg5aDI0djI0SDh6XCIgLz48L2NsaXBQYXRoPlxuICA8L2RlZnM+XG48L3N2Zz5gO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwbGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js\n"));

/***/ })

}]);