"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/safe-stable-stringify";
exports.ids = ["vendor-chunks/safe-stable-stringify"];
exports.modules = {

/***/ "(ssr)/../node_modules/safe-stable-stringify/index.js":
/*!******************************************************!*\
  !*** ../node_modules/safe-stable-stringify/index.js ***!
  \******************************************************/
/***/ ((module, exports) => {

eval("\n\nconst { hasOwnProperty } = Object.prototype\n\nconst stringify = configure()\n\n// @ts-expect-error\nstringify.configure = configure\n// @ts-expect-error\nstringify.stringify = stringify\n\n// @ts-expect-error\nstringify.default = stringify\n\n// @ts-expect-error used for named export\nexports.stringify = stringify\n// @ts-expect-error used for named export\nexports.configure = configure\n\nmodule.exports = stringify\n\n// eslint-disable-next-line no-control-regex\nconst strEscapeSequencesRegExp = /[\\u0000-\\u001f\\u0022\\u005c\\ud800-\\udfff]/\n\n// Escape C0 control characters, double quotes, the backslash and every code\n// unit with a numeric value in the inclusive range 0xD800 to 0xDFFF.\nfunction strEscape (str) {\n  // Some magic numbers that worked out fine while benchmarking with v8 8.0\n  if (str.length < 5000 && !strEscapeSequencesRegExp.test(str)) {\n    return `\"${str}\"`\n  }\n  return JSON.stringify(str)\n}\n\nfunction sort (array, comparator) {\n  // Insertion sort is very efficient for small input sizes, but it has a bad\n  // worst case complexity. Thus, use native array sort for bigger values.\n  if (array.length > 2e2 || comparator) {\n    return array.sort(comparator)\n  }\n  for (let i = 1; i < array.length; i++) {\n    const currentValue = array[i]\n    let position = i\n    while (position !== 0 && array[position - 1] > currentValue) {\n      array[position] = array[position - 1]\n      position--\n    }\n    array[position] = currentValue\n  }\n  return array\n}\n\nconst typedArrayPrototypeGetSymbolToStringTag =\n  Object.getOwnPropertyDescriptor(\n    Object.getPrototypeOf(\n      Object.getPrototypeOf(\n        new Int8Array()\n      )\n    ),\n    Symbol.toStringTag\n  ).get\n\nfunction isTypedArrayWithEntries (value) {\n  return typedArrayPrototypeGetSymbolToStringTag.call(value) !== undefined && value.length !== 0\n}\n\nfunction stringifyTypedArray (array, separator, maximumBreadth) {\n  if (array.length < maximumBreadth) {\n    maximumBreadth = array.length\n  }\n  const whitespace = separator === ',' ? '' : ' '\n  let res = `\"0\":${whitespace}${array[0]}`\n  for (let i = 1; i < maximumBreadth; i++) {\n    res += `${separator}\"${i}\":${whitespace}${array[i]}`\n  }\n  return res\n}\n\nfunction getCircularValueOption (options) {\n  if (hasOwnProperty.call(options, 'circularValue')) {\n    const circularValue = options.circularValue\n    if (typeof circularValue === 'string') {\n      return `\"${circularValue}\"`\n    }\n    if (circularValue == null) {\n      return circularValue\n    }\n    if (circularValue === Error || circularValue === TypeError) {\n      return {\n        toString () {\n          throw new TypeError('Converting circular structure to JSON')\n        }\n      }\n    }\n    throw new TypeError('The \"circularValue\" argument must be of type string or the value null or undefined')\n  }\n  return '\"[Circular]\"'\n}\n\nfunction getDeterministicOption (options) {\n  let value\n  if (hasOwnProperty.call(options, 'deterministic')) {\n    value = options.deterministic\n    if (typeof value !== 'boolean' && typeof value !== 'function') {\n      throw new TypeError('The \"deterministic\" argument must be of type boolean or comparator function')\n    }\n  }\n  return value === undefined ? true : value\n}\n\nfunction getBooleanOption (options, key) {\n  let value\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key]\n    if (typeof value !== 'boolean') {\n      throw new TypeError(`The \"${key}\" argument must be of type boolean`)\n    }\n  }\n  return value === undefined ? true : value\n}\n\nfunction getPositiveIntegerOption (options, key) {\n  let value\n  if (hasOwnProperty.call(options, key)) {\n    value = options[key]\n    if (typeof value !== 'number') {\n      throw new TypeError(`The \"${key}\" argument must be of type number`)\n    }\n    if (!Number.isInteger(value)) {\n      throw new TypeError(`The \"${key}\" argument must be an integer`)\n    }\n    if (value < 1) {\n      throw new RangeError(`The \"${key}\" argument must be >= 1`)\n    }\n  }\n  return value === undefined ? Infinity : value\n}\n\nfunction getItemCount (number) {\n  if (number === 1) {\n    return '1 item'\n  }\n  return `${number} items`\n}\n\nfunction getUniqueReplacerSet (replacerArray) {\n  const replacerSet = new Set()\n  for (const value of replacerArray) {\n    if (typeof value === 'string' || typeof value === 'number') {\n      replacerSet.add(String(value))\n    }\n  }\n  return replacerSet\n}\n\nfunction getStrictOption (options) {\n  if (hasOwnProperty.call(options, 'strict')) {\n    const value = options.strict\n    if (typeof value !== 'boolean') {\n      throw new TypeError('The \"strict\" argument must be of type boolean')\n    }\n    if (value) {\n      return (value) => {\n        let message = `Object can not safely be stringified. Received type ${typeof value}`\n        if (typeof value !== 'function') message += ` (${value.toString()})`\n        throw new Error(message)\n      }\n    }\n  }\n}\n\nfunction configure (options) {\n  options = { ...options }\n  const fail = getStrictOption(options)\n  if (fail) {\n    if (options.bigint === undefined) {\n      options.bigint = false\n    }\n    if (!('circularValue' in options)) {\n      options.circularValue = Error\n    }\n  }\n  const circularValue = getCircularValueOption(options)\n  const bigint = getBooleanOption(options, 'bigint')\n  const deterministic = getDeterministicOption(options)\n  const comparator = typeof deterministic === 'function' ? deterministic : undefined\n  const maximumDepth = getPositiveIntegerOption(options, 'maximumDepth')\n  const maximumBreadth = getPositiveIntegerOption(options, 'maximumBreadth')\n\n  function stringifyFnReplacer (key, parent, stack, replacer, spacer, indentation) {\n    let value = parent[key]\n\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key)\n    }\n    value = replacer.call(parent, key, value)\n\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        let res = ''\n        let join = ','\n        const originalIndentation = indentation\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          if (spacer !== '') {\n            indentation += spacer\n            res += `\\n${indentation}`\n            join = `,\\n${indentation}`\n          }\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyFnReplacer(String(i), value, stack, replacer, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          if (spacer !== '') {\n            res += `\\n${originalIndentation}`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        let whitespace = ''\n        let separator = ''\n        if (spacer !== '') {\n          indentation += spacer\n          join = `,\\n${indentation}`\n          whitespace = ' '\n        }\n        const maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (deterministic && !isTypedArrayWithEntries(value)) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifyFnReplacer(key, value, stack, replacer, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${whitespace}${tmp}`\n            separator = join\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\":${whitespace}\"${getItemCount(removedKeys)} not stringified\"`\n          separator = join\n        }\n        if (spacer !== '' && separator.length > 1) {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifyArrayReplacer (key, value, stack, replacer, spacer, indentation) {\n    if (typeof value === 'object' && value !== null && typeof value.toJSON === 'function') {\n      value = value.toJSON(key)\n    }\n\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        const originalIndentation = indentation\n        let res = ''\n        let join = ','\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          if (spacer !== '') {\n            indentation += spacer\n            res += `\\n${indentation}`\n            join = `,\\n${indentation}`\n          }\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyArrayReplacer(String(i), value[i], stack, replacer, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          if (spacer !== '') {\n            res += `\\n${originalIndentation}`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n        stack.push(value)\n        let whitespace = ''\n        if (spacer !== '') {\n          indentation += spacer\n          join = `,\\n${indentation}`\n          whitespace = ' '\n        }\n        let separator = ''\n        for (const key of replacer) {\n          const tmp = stringifyArrayReplacer(key, value[key], stack, replacer, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${whitespace}${tmp}`\n            separator = join\n          }\n        }\n        if (spacer !== '' && separator.length > 1) {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifyIndent (key, value, stack, spacer, indentation) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (typeof value.toJSON === 'function') {\n          value = value.toJSON(key)\n          // Prevent calling `toJSON` again.\n          if (typeof value !== 'object') {\n            return stringifyIndent(key, value, stack, spacer, indentation)\n          }\n          if (value === null) {\n            return 'null'\n          }\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n        const originalIndentation = indentation\n\n        if (Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          indentation += spacer\n          let res = `\\n${indentation}`\n          const join = `,\\n${indentation}`\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation)\n            res += tmp !== undefined ? tmp : 'null'\n            res += join\n          }\n          const tmp = stringifyIndent(String(i), value[i], stack, spacer, indentation)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `${join}\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          res += `\\n${originalIndentation}`\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        indentation += spacer\n        const join = `,\\n${indentation}`\n        let res = ''\n        let separator = ''\n        let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (isTypedArrayWithEntries(value)) {\n          res += stringifyTypedArray(value, join, maximumBreadth)\n          keys = keys.slice(value.length)\n          maximumPropertiesToStringify -= value.length\n          separator = join\n        }\n        if (deterministic) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifyIndent(key, value[key], stack, spacer, indentation)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}: ${tmp}`\n            separator = join\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\": \"${getItemCount(removedKeys)} not stringified\"`\n          separator = join\n        }\n        if (separator !== '') {\n          res = `\\n${indentation}${res}\\n${originalIndentation}`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringifySimple (key, value, stack) {\n    switch (typeof value) {\n      case 'string':\n        return strEscape(value)\n      case 'object': {\n        if (value === null) {\n          return 'null'\n        }\n        if (typeof value.toJSON === 'function') {\n          value = value.toJSON(key)\n          // Prevent calling `toJSON` again\n          if (typeof value !== 'object') {\n            return stringifySimple(key, value, stack)\n          }\n          if (value === null) {\n            return 'null'\n          }\n        }\n        if (stack.indexOf(value) !== -1) {\n          return circularValue\n        }\n\n        let res = ''\n\n        const hasLength = value.length !== undefined\n        if (hasLength && Array.isArray(value)) {\n          if (value.length === 0) {\n            return '[]'\n          }\n          if (maximumDepth < stack.length + 1) {\n            return '\"[Array]\"'\n          }\n          stack.push(value)\n          const maximumValuesToStringify = Math.min(value.length, maximumBreadth)\n          let i = 0\n          for (; i < maximumValuesToStringify - 1; i++) {\n            const tmp = stringifySimple(String(i), value[i], stack)\n            res += tmp !== undefined ? tmp : 'null'\n            res += ','\n          }\n          const tmp = stringifySimple(String(i), value[i], stack)\n          res += tmp !== undefined ? tmp : 'null'\n          if (value.length - 1 > maximumBreadth) {\n            const removedKeys = value.length - maximumBreadth - 1\n            res += `,\"... ${getItemCount(removedKeys)} not stringified\"`\n          }\n          stack.pop()\n          return `[${res}]`\n        }\n\n        let keys = Object.keys(value)\n        const keyLength = keys.length\n        if (keyLength === 0) {\n          return '{}'\n        }\n        if (maximumDepth < stack.length + 1) {\n          return '\"[Object]\"'\n        }\n        let separator = ''\n        let maximumPropertiesToStringify = Math.min(keyLength, maximumBreadth)\n        if (hasLength && isTypedArrayWithEntries(value)) {\n          res += stringifyTypedArray(value, ',', maximumBreadth)\n          keys = keys.slice(value.length)\n          maximumPropertiesToStringify -= value.length\n          separator = ','\n        }\n        if (deterministic) {\n          keys = sort(keys, comparator)\n        }\n        stack.push(value)\n        for (let i = 0; i < maximumPropertiesToStringify; i++) {\n          const key = keys[i]\n          const tmp = stringifySimple(key, value[key], stack)\n          if (tmp !== undefined) {\n            res += `${separator}${strEscape(key)}:${tmp}`\n            separator = ','\n          }\n        }\n        if (keyLength > maximumBreadth) {\n          const removedKeys = keyLength - maximumBreadth\n          res += `${separator}\"...\":\"${getItemCount(removedKeys)} not stringified\"`\n        }\n        stack.pop()\n        return `{${res}}`\n      }\n      case 'number':\n        return isFinite(value) ? String(value) : fail ? fail(value) : 'null'\n      case 'boolean':\n        return value === true ? 'true' : 'false'\n      case 'undefined':\n        return undefined\n      case 'bigint':\n        if (bigint) {\n          return String(value)\n        }\n        // fallthrough\n      default:\n        return fail ? fail(value) : undefined\n    }\n  }\n\n  function stringify (value, replacer, space) {\n    if (arguments.length > 1) {\n      let spacer = ''\n      if (typeof space === 'number') {\n        spacer = ' '.repeat(Math.min(space, 10))\n      } else if (typeof space === 'string') {\n        spacer = space.slice(0, 10)\n      }\n      if (replacer != null) {\n        if (typeof replacer === 'function') {\n          return stringifyFnReplacer('', { '': value }, [], replacer, spacer, '')\n        }\n        if (Array.isArray(replacer)) {\n          return stringifyArrayReplacer('', value, [], getUniqueReplacerSet(replacer), spacer, '')\n        }\n      }\n      if (spacer.length !== 0) {\n        return stringifyIndent('', value, [], spacer, '')\n      }\n    }\n    return stringifySimple('', value, [])\n  }\n\n  return stringify\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/safe-stable-stringify/index.js\n");

/***/ })

};
;