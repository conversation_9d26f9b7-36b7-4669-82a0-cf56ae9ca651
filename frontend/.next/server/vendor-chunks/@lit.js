"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lit";
exports.ids = ["vendor-chunks/@lit"];
exports.modules = {

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/css-tag.js":
/*!*************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/css-tag.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSResult: () => (/* binding */ CSSResult),\n/* harmony export */   adoptStyles: () => (/* binding */ adoptStyles),\n/* harmony export */   css: () => (/* binding */ css),\n/* harmony export */   getCompatibleStyle: () => (/* binding */ getCompatibleStyle),\n/* harmony export */   supportsAdoptingStyleSheets: () => (/* binding */ supportsAdoptingStyleSheets),\n/* harmony export */   unsafeCSS: () => (/* binding */ unsafeCSS)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n// Allows minifiers to rename references to globalThis\nconst global = globalThis;\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nconst supportsAdoptingStyleSheets = global.ShadowRoot &&\n    (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) &&\n    'adoptedStyleSheets' in Document.prototype &&\n    'replace' in CSSStyleSheet.prototype;\nconst constructionToken = Symbol();\nconst cssTagCache = new WeakMap();\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nclass CSSResult {\n    constructor(cssText, strings, safeToken) {\n        // This property needs to remain unminified.\n        this['_$cssResult$'] = true;\n        if (safeToken !== constructionToken) {\n            throw new Error('CSSResult is not constructable. Use `unsafeCSS` or `css` instead.');\n        }\n        this.cssText = cssText;\n        this._strings = strings;\n    }\n    // This is a getter so that it's lazy. In practice, this means stylesheets\n    // are not created until the first element instance is made.\n    get styleSheet() {\n        // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n        // constructable.\n        let styleSheet = this._styleSheet;\n        const strings = this._strings;\n        if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n            const cacheable = strings !== undefined && strings.length === 1;\n            if (cacheable) {\n                styleSheet = cssTagCache.get(strings);\n            }\n            if (styleSheet === undefined) {\n                (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(this.cssText);\n                if (cacheable) {\n                    cssTagCache.set(strings, styleSheet);\n                }\n            }\n        }\n        return styleSheet;\n    }\n    toString() {\n        return this.cssText;\n    }\n}\nconst textFromCSSResult = (value) => {\n    // This property needs to remain unminified.\n    if (value['_$cssResult$'] === true) {\n        return value.cssText;\n    }\n    else if (typeof value === 'number') {\n        return value;\n    }\n    else {\n        throw new Error(`Value passed to 'css' function must be a 'css' function result: ` +\n            `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` +\n            `to ensure page security.`);\n    }\n};\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nconst unsafeCSS = (value) => new CSSResult(typeof value === 'string' ? value : String(value), undefined, constructionToken);\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nconst css = (strings, ...values) => {\n    const cssText = strings.length === 1\n        ? strings[0]\n        : values.reduce((acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1], strings[0]);\n    return new CSSResult(cssText, strings, constructionToken);\n};\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic the native feature](https://developer.mozilla.org/en-US/docs/Web/API/ShadowRoot/adoptedStyleSheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nconst adoptStyles = (renderRoot, styles) => {\n    if (supportsAdoptingStyleSheets) {\n        renderRoot.adoptedStyleSheets = styles.map((s) => s instanceof CSSStyleSheet ? s : s.styleSheet);\n    }\n    else {\n        for (const s of styles) {\n            const style = document.createElement('style');\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const nonce = global['litNonce'];\n            if (nonce !== undefined) {\n                style.setAttribute('nonce', nonce);\n            }\n            style.textContent = s.cssText;\n            renderRoot.appendChild(style);\n        }\n    }\n};\nconst cssResultFromStyleSheet = (sheet) => {\n    let cssText = '';\n    for (const rule of sheet.cssRules) {\n        cssText += rule.cssText;\n    }\n    return unsafeCSS(cssText);\n};\nconst getCompatibleStyle = supportsAdoptingStyleSheets ||\n    (global.CSSStyleSheet === undefined)\n    ? (s) => s\n    : (s) => s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;\n\n\n//# sourceMappingURL=css-tag.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/css-tag.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/base.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/base.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   desc: () => (/* binding */ desc)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * Wraps up a few best practices when returning a property descriptor from a\n * decorator.\n *\n * Marks the defined property as configurable, and enumerable, and handles\n * the case where we have a busted Reflect.decorate zombiefill (e.g. in Angular\n * apps).\n *\n * @internal\n */\nconst desc = (obj, name, descriptor) => {\n    // For backwards compatibility, we keep them configurable and enumerable.\n    descriptor.configurable = true;\n    descriptor.enumerable = true;\n    if (\n    // We check for Reflect.decorate each time, in case the zombiefill\n    // is applied via lazy loading some Angular code.\n    Reflect.decorate &&\n        typeof name !== 'object') {\n        // If we're called as a legacy decorator, and Reflect.decorate is present\n        // then we have no guarantees that the returned descriptor will be\n        // defined on the class, so we must apply it directly ourselves.\n        Object.defineProperty(obj, name, descriptor);\n    }\n    return descriptor;\n};\n\n\n//# sourceMappingURL=base.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/base.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/custom-element.js":
/*!*******************************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/custom-element.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customElement: () => (/* binding */ customElement)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * Class decorator factory that defines the decorated class as a custom element.\n *\n * ```js\n * @customElement('my-element')\n * class MyElement extends LitElement {\n *   render() {\n *     return html``;\n *   }\n * }\n * ```\n * @category Decorator\n * @param tagName The tag name of the custom element to define.\n */\nconst customElement = (tagName) => (classOrTarget, context) => {\n    if (context !== undefined) {\n        context.addInitializer(() => {\n            customElements.define(tagName, classOrTarget);\n        });\n    }\n    else {\n        customElements.define(tagName, classOrTarget);\n    }\n};\n\n\n//# sourceMappingURL=custom-element.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BsaXQvcmVhY3RpdmUtZWxlbWVudC9ub2RlL2RldmVsb3BtZW50L2RlY29yYXRvcnMvY3VzdG9tLWVsZW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUI7QUFDekIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQGxpdC9yZWFjdGl2ZS1lbGVtZW50L25vZGUvZGV2ZWxvcG1lbnQvZGVjb3JhdG9ycy9jdXN0b20tZWxlbWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAxNyBHb29nbGUgTExDXG4gKiBTUERYLUxpY2Vuc2UtSWRlbnRpZmllcjogQlNELTMtQ2xhdXNlXG4gKi9cbi8qKlxuICogQ2xhc3MgZGVjb3JhdG9yIGZhY3RvcnkgdGhhdCBkZWZpbmVzIHRoZSBkZWNvcmF0ZWQgY2xhc3MgYXMgYSBjdXN0b20gZWxlbWVudC5cbiAqXG4gKiBgYGBqc1xuICogQGN1c3RvbUVsZW1lbnQoJ215LWVsZW1lbnQnKVxuICogY2xhc3MgTXlFbGVtZW50IGV4dGVuZHMgTGl0RWxlbWVudCB7XG4gKiAgIHJlbmRlcigpIHtcbiAqICAgICByZXR1cm4gaHRtbGBgO1xuICogICB9XG4gKiB9XG4gKiBgYGBcbiAqIEBjYXRlZ29yeSBEZWNvcmF0b3JcbiAqIEBwYXJhbSB0YWdOYW1lIFRoZSB0YWcgbmFtZSBvZiB0aGUgY3VzdG9tIGVsZW1lbnQgdG8gZGVmaW5lLlxuICovXG5jb25zdCBjdXN0b21FbGVtZW50ID0gKHRhZ05hbWUpID0+IChjbGFzc09yVGFyZ2V0LCBjb250ZXh0KSA9PiB7XG4gICAgaWYgKGNvbnRleHQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBjb250ZXh0LmFkZEluaXRpYWxpemVyKCgpID0+IHtcbiAgICAgICAgICAgIGN1c3RvbUVsZW1lbnRzLmRlZmluZSh0YWdOYW1lLCBjbGFzc09yVGFyZ2V0KTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBjdXN0b21FbGVtZW50cy5kZWZpbmUodGFnTmFtZSwgY2xhc3NPclRhcmdldCk7XG4gICAgfVxufTtcblxuZXhwb3J0IHsgY3VzdG9tRWxlbWVudCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3VzdG9tLWVsZW1lbnQuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/custom-element.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/event-options.js":
/*!******************************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/event-options.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventOptions: () => (/* binding */ eventOptions)\n/* harmony export */ });\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * Adds event listener options to a method used as an event listener in a\n * lit-html template.\n *\n * @param options An object that specifies event listener options as accepted by\n * `EventTarget#addEventListener` and `EventTarget#removeEventListener`.\n *\n * Current browsers support the `capture`, `passive`, and `once` options. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Parameters\n *\n * ```ts\n * class MyElement {\n *   clicked = false;\n *\n *   render() {\n *     return html`\n *       <div @click=${this._onClick}>\n *         <button></button>\n *       </div>\n *     `;\n *   }\n *\n *   @eventOptions({capture: true})\n *   _onClick(e) {\n *     this.clicked = true;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction eventOptions(options) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return ((protoOrValue, nameOrContext) => {\n        const method = typeof protoOrValue === 'function'\n            ? protoOrValue\n            : protoOrValue[nameOrContext];\n        Object.assign(method, options);\n    });\n}\n\n\n//# sourceMappingURL=event-options.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/event-options.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/property.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/property.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   property: () => (/* binding */ property),\n/* harmony export */   standardProperty: () => (/* binding */ standardProperty)\n/* harmony export */ });\n/* harmony import */ var _reactive_element_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../reactive-element.js */ \"(ssr)/../node_modules/@lit/reactive-element/node/development/reactive-element.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/*\n * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nlet issueWarning;\n{\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    globalThis.litIssuedWarnings ??= new Set();\n    /**\n     * Issue a warning if we haven't already, based either on `code` or `warning`.\n     * Warnings are disabled automatically only by `warning`; disabling via `code`\n     * can be done by users.\n     */\n    issueWarning = (code, warning) => {\n        warning += ` See https://lit.dev/msg/${code} for more information.`;\n        if (!globalThis.litIssuedWarnings.has(warning) &&\n            !globalThis.litIssuedWarnings.has(code)) {\n            console.warn(warning);\n            globalThis.litIssuedWarnings.add(warning);\n        }\n    };\n}\nconst legacyProperty = (options, proto, name) => {\n    const hasOwnProperty = proto.hasOwnProperty(name);\n    proto.constructor.createProperty(name, options);\n    // For accessors (which have a descriptor on the prototype) we need to\n    // return a descriptor, otherwise TypeScript overwrites the descriptor we\n    // define in createProperty() with the original descriptor. We don't do this\n    // for fields, which don't have a descriptor, because this could overwrite\n    // descriptor defined by other decorators.\n    return hasOwnProperty\n        ? Object.getOwnPropertyDescriptor(proto, name)\n        : undefined;\n};\n// This is duplicated from a similar variable in reactive-element.ts, but\n// actually makes sense to have this default defined with the decorator, so\n// that different decorators could have different defaults.\nconst defaultPropertyDeclaration = {\n    attribute: true,\n    type: String,\n    converter: _reactive_element_js__WEBPACK_IMPORTED_MODULE_0__.defaultConverter,\n    reflect: false,\n    hasChanged: _reactive_element_js__WEBPACK_IMPORTED_MODULE_0__.notEqual,\n};\n/**\n * Wraps a class accessor or setter so that `requestUpdate()` is called with the\n * property name and old value when the accessor is set.\n */\nconst standardProperty = (options = defaultPropertyDeclaration, target, context) => {\n    const { kind, metadata } = context;\n    if (metadata == null) {\n        issueWarning('missing-class-metadata', `The class ${target} is missing decorator metadata. This ` +\n            `could mean that you're using a compiler that supports decorators ` +\n            `but doesn't support decorator metadata, such as TypeScript 5.1. ` +\n            `Please update your compiler.`);\n    }\n    // Store the property options\n    let properties = globalThis.litPropertyMetadata.get(metadata);\n    if (properties === undefined) {\n        globalThis.litPropertyMetadata.set(metadata, (properties = new Map()));\n    }\n    if (kind === 'setter') {\n        options = Object.create(options);\n        options.wrapped = true;\n    }\n    properties.set(context.name, options);\n    if (kind === 'accessor') {\n        // Standard decorators cannot dynamically modify the class, so we can't\n        // replace a field with accessors. The user must use the new `accessor`\n        // keyword instead.\n        const { name } = context;\n        return {\n            set(v) {\n                const oldValue = target.get.call(this);\n                target.set.call(this, v);\n                this.requestUpdate(name, oldValue, options);\n            },\n            init(v) {\n                if (v !== undefined) {\n                    this._$changeProperty(name, undefined, options, v);\n                }\n                return v;\n            },\n        };\n    }\n    else if (kind === 'setter') {\n        const { name } = context;\n        return function (value) {\n            const oldValue = this[name];\n            target.call(this, value);\n            this.requestUpdate(name, oldValue, options);\n        };\n    }\n    throw new Error(`Unsupported decorator location: ${kind}`);\n};\n/**\n * A class field or accessor decorator which creates a reactive property that\n * reflects a corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nfunction property(options) {\n    return (protoOrTarget, nameOrContext\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    ) => {\n        return (typeof nameOrContext === 'object'\n            ? standardProperty(options, protoOrTarget, nameOrContext)\n            : legacyProperty(options, protoOrTarget, nameOrContext));\n    };\n}\n\n\n//# sourceMappingURL=property.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/property.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query-all.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/query-all.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAll: () => (/* binding */ queryAll)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n// Shared fragment used to generate empty NodeLists when a render root is\n// undefined\nlet fragment;\n/**\n * A property decorator that converts a class property into a getter\n * that executes a querySelectorAll on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See:\n * https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelectorAll\n *\n * ```ts\n * class MyElement {\n *   @queryAll('div')\n *   divs: NodeListOf<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction queryAll(selector) {\n    return ((obj, name) => {\n        return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(obj, name, {\n            get() {\n                const container = this.renderRoot ?? (fragment ??= document.createDocumentFragment());\n                return container.querySelectorAll(selector);\n            },\n        });\n    });\n}\n\n\n//# sourceMappingURL=query-all.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query-all.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js":
/*!****************************************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAssignedElements: () => (/* binding */ queryAssignedElements)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedElements` of the given `slot`. Provides a declarative\n * way to use\n * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n *\n * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedElements({ slot: 'list' })\n *   listItems!: Array<HTMLElement>;\n *   @queryAssignedElements()\n *   unnamedSlotEls!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *       <slot></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note, the type of this property should be annotated as `Array<HTMLElement>`.\n *\n * @category Decorator\n */\nfunction queryAssignedElements(options) {\n    return ((obj, name) => {\n        const { slot, selector } = options ?? {};\n        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n        return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(obj, name, {\n            get() {\n                const slotEl = this.renderRoot?.querySelector(slotSelector);\n                const elements = slotEl?.assignedElements(options) ?? [];\n                return (selector === undefined\n                    ? elements\n                    : elements.filter((node) => node.matches(selector)));\n            },\n        });\n    });\n}\n\n\n//# sourceMappingURL=query-assigned-elements.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js":
/*!*************************************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAssignedNodes: () => (/* binding */ queryAssignedNodes)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedNodes` of the given `slot`.\n *\n * Can be passed an optional {@linkcode QueryAssignedNodesOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedNodes({slot: 'list', flatten: true})\n *   listItems!: Array<Node>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note the type of this property should be annotated as `Array<Node>`. Use the\n * queryAssignedElements decorator to list only elements, and optionally filter\n * the element list using a CSS selector.\n *\n * @category Decorator\n */\nfunction queryAssignedNodes(options) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return ((obj, name) => {\n        const { slot } = options ?? {};\n        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n        return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(obj, name, {\n            get() {\n                const slotEl = this.renderRoot?.querySelector(slotSelector);\n                return (slotEl?.assignedNodes(options) ?? []);\n            },\n        });\n    });\n}\n\n\n//# sourceMappingURL=query-assigned-nodes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query-async.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/query-async.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryAsync: () => (/* binding */ queryAsync)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n// Note, in the future, we may extend this decorator to support the use case\n// where the queried element may need to do work to become ready to interact\n// with (e.g. load some implementation code). If so, we might elect to\n// add a second argument defining a function that can be run to make the\n// queried element loaded/updated/ready.\n/**\n * A property decorator that converts a class property into a getter that\n * returns a promise that resolves to the result of a querySelector on the\n * element's renderRoot done after the element's `updateComplete` promise\n * resolves. When the queried property may change with element state, this\n * decorator can be used instead of requiring users to await the\n * `updateComplete` before accessing the property.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @queryAsync('#first')\n *   first: Promise<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n *\n * // external usage\n * async doSomethingWithFirst() {\n *  (await aMyElement.first).doSomething();\n * }\n * ```\n * @category Decorator\n */\nfunction queryAsync(selector) {\n    return ((obj, name) => {\n        return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(obj, name, {\n            async get() {\n                await this.updateComplete;\n                return this.renderRoot?.querySelector(selector) ?? null;\n            },\n        });\n    });\n}\n\n\n//# sourceMappingURL=query-async.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query-async.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/query.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/base.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nlet issueWarning;\n{\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    globalThis.litIssuedWarnings ??= new Set();\n    /**\n     * Issue a warning if we haven't already, based either on `code` or `warning`.\n     * Warnings are disabled automatically only by `warning`; disabling via `code`\n     * can be done by users.\n     */\n    issueWarning = (code, warning) => {\n        warning += code\n            ? ` See https://lit.dev/msg/${code} for more information.`\n            : '';\n        if (!globalThis.litIssuedWarnings.has(warning) &&\n            !globalThis.litIssuedWarnings.has(code)) {\n            console.warn(warning);\n            globalThis.litIssuedWarnings.add(warning);\n        }\n    };\n}\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nfunction query(selector, cache) {\n    return ((protoOrTarget, nameOrContext, descriptor) => {\n        const doQuery = (el) => {\n            const result = (el.renderRoot?.querySelector(selector) ?? null);\n            if (result === null && cache && !el.hasUpdated) {\n                const name = typeof nameOrContext === 'object'\n                    ? nameOrContext.name\n                    : nameOrContext;\n                issueWarning('', `@query'd field ${JSON.stringify(String(name))} with the 'cache' ` +\n                    `flag set for selector '${selector}' has been accessed before ` +\n                    `the first update and returned null. This is expected if the ` +\n                    `renderRoot tree has not been provided beforehand (e.g. via ` +\n                    `Declarative Shadow DOM). Therefore the value hasn't been cached.`);\n            }\n            // TODO: if we want to allow users to assert that the query will never\n            // return null, we need a new option and to throw here if the result\n            // is null.\n            return result;\n        };\n        if (cache) {\n            // Accessors to wrap from either:\n            //   1. The decorator target, in the case of standard decorators\n            //   2. The property descriptor, in the case of experimental decorators\n            //      on auto-accessors.\n            //   3. Functions that access our own cache-key property on the instance,\n            //      in the case of experimental decorators on fields.\n            const { get, set } = typeof nameOrContext === 'object'\n                ? protoOrTarget\n                : descriptor ??\n                    (() => {\n                        const key = Symbol(`${String(nameOrContext)} (@query() cache)`)\n                            ;\n                        return {\n                            get() {\n                                return this[key];\n                            },\n                            set(v) {\n                                this[key] = v;\n                            },\n                        };\n                    })();\n            return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(protoOrTarget, nameOrContext, {\n                get() {\n                    let result = get.call(this);\n                    if (result === undefined) {\n                        result = doQuery(this);\n                        if (result !== null || this.hasUpdated) {\n                            set.call(this, result);\n                        }\n                    }\n                    return result;\n                },\n            });\n        }\n        else {\n            // This object works as the return type for both standard and\n            // experimental decorators.\n            return (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.desc)(protoOrTarget, nameOrContext, {\n                get() {\n                    return doQuery(this);\n                },\n            });\n        }\n    });\n}\n\n\n//# sourceMappingURL=query.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/query.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/state.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/decorators/state.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   state: () => (/* binding */ state)\n/* harmony export */ });\n/* harmony import */ var _property_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./property.js */ \"(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/property.js\");\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n/*\n * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n/**\n * Declares a private or protected reactive property that still triggers\n * updates to the element when it changes. It does not reflect from the\n * corresponding attribute.\n *\n * Properties declared this way must not be used from HTML or HTML templating\n * systems, they're solely for properties internal to the element. These\n * properties may be renamed by optimization tools like closure compiler.\n * @category Decorator\n */\nfunction state(options) {\n    return (0,_property_js__WEBPACK_IMPORTED_MODULE_0__.property)({\n        ...options,\n        // Add both `state` and `attribute` because we found a third party\n        // controller that is keying off of PropertyOptions.state to determine\n        // whether a field is a private internal property or not.\n        state: true,\n        attribute: false,\n    });\n}\n\n\n//# sourceMappingURL=state.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/decorators/state.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@lit/reactive-element/node/development/reactive-element.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@lit/reactive-element/node/development/reactive-element.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSResult: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.CSSResult),\n/* harmony export */   ReactiveElement: () => (/* binding */ ReactiveElement),\n/* harmony export */   adoptStyles: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.adoptStyles),\n/* harmony export */   css: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.css),\n/* harmony export */   defaultConverter: () => (/* binding */ defaultConverter),\n/* harmony export */   getCompatibleStyle: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.getCompatibleStyle),\n/* harmony export */   notEqual: () => (/* binding */ notEqual),\n/* harmony export */   supportsAdoptingStyleSheets: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.supportsAdoptingStyleSheets),\n/* harmony export */   unsafeCSS: () => (/* reexport safe */ _css_tag_js__WEBPACK_IMPORTED_MODULE_1__.unsafeCSS)\n/* harmony export */ });\n/* harmony import */ var _lit_labs_ssr_dom_shim__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lit-labs/ssr-dom-shim */ \"(ssr)/../node_modules/@lit-labs/ssr-dom-shim/index.js\");\n/* harmony import */ var _css_tag_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./css-tag.js */ \"(ssr)/../node_modules/@lit/reactive-element/node/development/css-tag.js\");\n\n\n\n\n// TODO (justinfagnani): Add `hasOwn` here when we ship ES2022\nconst { is, defineProperty, getOwnPropertyDescriptor, getOwnPropertyNames, getOwnPropertySymbols, getPrototypeOf, } = Object;\n// Lets a minifier replace globalThis references with a minified name\nconst global = globalThis;\n{\n    global.customElements ??= _lit_labs_ssr_dom_shim__WEBPACK_IMPORTED_MODULE_0__.customElements;\n}\nlet issueWarning;\nconst trustedTypes = global\n    .trustedTypes;\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n    ? trustedTypes.emptyScript\n    : '';\nconst polyfillSupport = global.reactiveElementPolyfillSupportDevMode\n    ;\n{\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    global.litIssuedWarnings ??= new Set();\n    /**\n     * Issue a warning if we haven't already, based either on `code` or `warning`.\n     * Warnings are disabled automatically only by `warning`; disabling via `code`\n     * can be done by users.\n     */\n    issueWarning = (code, warning) => {\n        warning += ` See https://lit.dev/msg/${code} for more information.`;\n        if (!global.litIssuedWarnings.has(warning) &&\n            !global.litIssuedWarnings.has(code)) {\n            console.warn(warning);\n            global.litIssuedWarnings.add(warning);\n        }\n    };\n    queueMicrotask(() => {\n        issueWarning('dev-mode', `Lit is in dev mode. Not recommended for production!`);\n        // Issue polyfill support warning.\n        if (global.ShadyDOM?.inUse && polyfillSupport === undefined) {\n            issueWarning('polyfill-support-missing', `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n                `the \\`polyfill-support\\` module has not been loaded.`);\n        }\n    });\n}\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = (event) => {\n        const shouldEmit = global\n            .emitLitDebugLogEvents;\n        if (!shouldEmit) {\n            return;\n        }\n        global.dispatchEvent(new CustomEvent('lit-debug', {\n            detail: event,\n        }));\n    }\n    ;\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = (prop, _obj) => prop;\nconst defaultConverter = {\n    toAttribute(value, type) {\n        switch (type) {\n            case Boolean:\n                value = value ? emptyStringForBooleanAttribute : null;\n                break;\n            case Object:\n            case Array:\n                // if the value is `null` or `undefined` pass this through\n                // to allow removing/no change behavior.\n                value = value == null ? value : JSON.stringify(value);\n                break;\n        }\n        return value;\n    },\n    fromAttribute(value, type) {\n        let fromValue = value;\n        switch (type) {\n            case Boolean:\n                fromValue = value !== null;\n                break;\n            case Number:\n                fromValue = value === null ? null : Number(value);\n                break;\n            case Object:\n            case Array:\n                // Do *not* generate exception when invalid JSON is set as elements\n                // don't normally complain on being mis-configured.\n                // TODO(sorvell): Do generate exception in *dev mode*.\n                try {\n                    // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n                    fromValue = JSON.parse(value);\n                }\n                catch (e) {\n                    fromValue = null;\n                }\n                break;\n        }\n        return fromValue;\n    },\n};\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nconst notEqual = (value, old) => !is(value, old);\nconst defaultPropertyDeclaration = {\n    attribute: true,\n    type: String,\n    converter: defaultConverter,\n    reflect: false,\n    useDefault: false,\n    hasChanged: notEqual,\n};\n// Ensure metadata is enabled. TypeScript does not polyfill\n// Symbol.metadata, so we must ensure that it exists.\nSymbol.metadata ??= Symbol('metadata');\n// Map from a class's metadata object to property options\n// Note that we must use nullish-coalescing assignment so that we only use one\n// map even if we load multiple version of this module.\nglobal.litPropertyMetadata ??= new WeakMap();\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclasses to render updates as desired.\n * @noInheritDoc\n */\nclass ReactiveElement\n// In the Node build, this `extends` clause will be substituted with\n// `(globalThis.HTMLElement ?? HTMLElement)`.\n//\n// This way, we will first prefer any global `HTMLElement` polyfill that the\n// user has assigned, and then fall back to the `HTMLElement` shim which has\n// been imported (see note at the top of this file about how this import is\n// generated by Rollup). Note that the `HTMLElement` variable has been\n// shadowed by this import, so it no longer refers to the global.\n extends (globalThis.HTMLElement ?? _lit_labs_ssr_dom_shim__WEBPACK_IMPORTED_MODULE_0__.HTMLElement) {\n    /**\n     * Adds an initializer function to the class that is called during instance\n     * construction.\n     *\n     * This is useful for code that runs against a `ReactiveElement`\n     * subclass, such as a decorator, that needs to do work for each\n     * instance, such as setting up a `ReactiveController`.\n     *\n     * ```ts\n     * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n     *   target.addInitializer((instance: ReactiveElement) => {\n     *     // This is run during construction of the element\n     *     new MyController(instance);\n     *   });\n     * }\n     * ```\n     *\n     * Decorating a field will then cause each instance to run an initializer\n     * that adds a controller:\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   @myDecorator foo;\n     * }\n     * ```\n     *\n     * Initializers are stored per-constructor. Adding an initializer to a\n     * subclass does not add it to a superclass. Since initializers are run in\n     * constructors, initializers will run in order of the class hierarchy,\n     * starting with superclasses and progressing to the instance's class.\n     *\n     * @nocollapse\n     */\n    static addInitializer(initializer) {\n        this.__prepare();\n        (this._initializers ??= []).push(initializer);\n    }\n    /**\n     * Returns a list of attributes corresponding to the registered properties.\n     * @nocollapse\n     * @category attributes\n     */\n    static get observedAttributes() {\n        // Ensure we've created all properties\n        this.finalize();\n        // this.__attributeToPropertyMap is only undefined after finalize() in\n        // ReactiveElement itself. ReactiveElement.observedAttributes is only\n        // accessed with ReactiveElement as the receiver when a subclass or mixin\n        // calls super.observedAttributes\n        return (this.__attributeToPropertyMap && [...this.__attributeToPropertyMap.keys()]);\n    }\n    /**\n     * Creates a property accessor on the element prototype if one does not exist\n     * and stores a {@linkcode PropertyDeclaration} for the property with the\n     * given options. The property setter calls the property's `hasChanged`\n     * property option or uses a strict identity check to determine whether or not\n     * to request an update.\n     *\n     * This method may be overridden to customize properties; however,\n     * when doing so, it's important to call `super.createProperty` to ensure\n     * the property is setup correctly. This method calls\n     * `getPropertyDescriptor` internally to get a descriptor to install.\n     * To customize what properties do when they are get or set, override\n     * `getPropertyDescriptor`. To customize the options for a property,\n     * implement `createProperty` like this:\n     *\n     * ```ts\n     * static createProperty(name, options) {\n     *   options = Object.assign(options, {myOption: true});\n     *   super.createProperty(name, options);\n     * }\n     * ```\n     *\n     * @nocollapse\n     * @category properties\n     */\n    static createProperty(name, options = defaultPropertyDeclaration) {\n        // If this is a state property, force the attribute to false.\n        if (options.state) {\n            options.attribute = false;\n        }\n        this.__prepare();\n        // Whether this property is wrapping accessors.\n        // Helps control the initial value change and reflection logic.\n        if (this.prototype.hasOwnProperty(name)) {\n            options = Object.create(options);\n            options.wrapped = true;\n        }\n        this.elementProperties.set(name, options);\n        if (!options.noAccessor) {\n            const key = // Use Symbol.for in dev mode to make it easier to maintain state\n                    // when doing HMR.\n                    Symbol.for(`${String(name)} (@property() cache)`)\n                ;\n            const descriptor = this.getPropertyDescriptor(name, key, options);\n            if (descriptor !== undefined) {\n                defineProperty(this.prototype, name, descriptor);\n            }\n        }\n    }\n    /**\n     * Returns a property descriptor to be defined on the given named property.\n     * If no descriptor is returned, the property will not become an accessor.\n     * For example,\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   static getPropertyDescriptor(name, key, options) {\n     *     const defaultDescriptor =\n     *         super.getPropertyDescriptor(name, key, options);\n     *     const setter = defaultDescriptor.set;\n     *     return {\n     *       get: defaultDescriptor.get,\n     *       set(value) {\n     *         setter.call(this, value);\n     *         // custom action.\n     *       },\n     *       configurable: true,\n     *       enumerable: true\n     *     }\n     *   }\n     * }\n     * ```\n     *\n     * @nocollapse\n     * @category properties\n     */\n    static getPropertyDescriptor(name, key, options) {\n        const { get, set } = getOwnPropertyDescriptor(this.prototype, name) ?? {\n            get() {\n                return this[key];\n            },\n            set(v) {\n                this[key] = v;\n            },\n        };\n        if (get == null) {\n            if ('value' in (getOwnPropertyDescriptor(this.prototype, name) ?? {})) {\n                throw new Error(`Field ${JSON.stringify(String(name))} on ` +\n                    `${this.name} was declared as a reactive property ` +\n                    `but it's actually declared as a value on the prototype. ` +\n                    `Usually this is due to using @property or @state on a method.`);\n            }\n            issueWarning('reactive-property-without-getter', `Field ${JSON.stringify(String(name))} on ` +\n                `${this.name} was declared as a reactive property ` +\n                `but it does not have a getter. This will be an error in a ` +\n                `future version of Lit.`);\n        }\n        return {\n            get,\n            set(value) {\n                const oldValue = get?.call(this);\n                set?.call(this, value);\n                this.requestUpdate(name, oldValue, options);\n            },\n            configurable: true,\n            enumerable: true,\n        };\n    }\n    /**\n     * Returns the property options associated with the given property.\n     * These options are defined with a `PropertyDeclaration` via the `properties`\n     * object or the `@property` decorator and are registered in\n     * `createProperty(...)`.\n     *\n     * Note, this method should be considered \"final\" and not overridden. To\n     * customize the options for a given property, override\n     * {@linkcode createProperty}.\n     *\n     * @nocollapse\n     * @final\n     * @category properties\n     */\n    static getPropertyOptions(name) {\n        return this.elementProperties.get(name) ?? defaultPropertyDeclaration;\n    }\n    /**\n     * Initializes static own properties of the class used in bookkeeping\n     * for element properties, initializers, etc.\n     *\n     * Can be called multiple times by code that needs to ensure these\n     * properties exist before using them.\n     *\n     * This method ensures the superclass is finalized so that inherited\n     * property metadata can be copied down.\n     * @nocollapse\n     */\n    static __prepare() {\n        if (this.hasOwnProperty(JSCompiler_renameProperty('elementProperties'))) {\n            // Already prepared\n            return;\n        }\n        // Finalize any superclasses\n        const superCtor = getPrototypeOf(this);\n        superCtor.finalize();\n        // Create own set of initializers for this class if any exist on the\n        // superclass and copy them down. Note, for a small perf boost, avoid\n        // creating initializers unless needed.\n        if (superCtor._initializers !== undefined) {\n            this._initializers = [...superCtor._initializers];\n        }\n        // Initialize elementProperties from the superclass\n        this.elementProperties = new Map(superCtor.elementProperties);\n    }\n    /**\n     * Finishes setting up the class so that it's ready to be registered\n     * as a custom element and instantiated.\n     *\n     * This method is called by the ReactiveElement.observedAttributes getter.\n     * If you override the observedAttributes getter, you must either call\n     * super.observedAttributes to trigger finalization, or call finalize()\n     * yourself.\n     *\n     * @nocollapse\n     */\n    static finalize() {\n        if (this.hasOwnProperty(JSCompiler_renameProperty('finalized'))) {\n            return;\n        }\n        this.finalized = true;\n        this.__prepare();\n        // Create properties from the static properties block:\n        if (this.hasOwnProperty(JSCompiler_renameProperty('properties'))) {\n            const props = this.properties;\n            const propKeys = [\n                ...getOwnPropertyNames(props),\n                ...getOwnPropertySymbols(props),\n            ];\n            for (const p of propKeys) {\n                this.createProperty(p, props[p]);\n            }\n        }\n        // Create properties from standard decorator metadata:\n        const metadata = this[Symbol.metadata];\n        if (metadata !== null) {\n            const properties = litPropertyMetadata.get(metadata);\n            if (properties !== undefined) {\n                for (const [p, options] of properties) {\n                    this.elementProperties.set(p, options);\n                }\n            }\n        }\n        // Create the attribute-to-property map\n        this.__attributeToPropertyMap = new Map();\n        for (const [p, options] of this.elementProperties) {\n            const attr = this.__attributeNameForProperty(p, options);\n            if (attr !== undefined) {\n                this.__attributeToPropertyMap.set(attr, p);\n            }\n        }\n        this.elementStyles = this.finalizeStyles(this.styles);\n        {\n            if (this.hasOwnProperty('createProperty')) {\n                issueWarning('no-override-create-property', 'Overriding ReactiveElement.createProperty() is deprecated. ' +\n                    'The override will not be called with standard decorators');\n            }\n            if (this.hasOwnProperty('getPropertyDescriptor')) {\n                issueWarning('no-override-get-property-descriptor', 'Overriding ReactiveElement.getPropertyDescriptor() is deprecated. ' +\n                    'The override will not be called with standard decorators');\n            }\n        }\n    }\n    /**\n     * Takes the styles the user supplied via the `static styles` property and\n     * returns the array of styles to apply to the element.\n     * Override this method to integrate into a style management system.\n     *\n     * Styles are deduplicated preserving the _last_ instance in the list. This\n     * is a performance optimization to avoid duplicated styles that can occur\n     * especially when composing via subclassing. The last item is kept to try\n     * to preserve the cascade order with the assumption that it's most important\n     * that last added styles override previous styles.\n     *\n     * @nocollapse\n     * @category styles\n     */\n    static finalizeStyles(styles) {\n        const elementStyles = [];\n        if (Array.isArray(styles)) {\n            // Dedupe the flattened array in reverse order to preserve the last items.\n            // Casting to Array<unknown> works around TS error that\n            // appears to come from trying to flatten a type CSSResultArray.\n            const set = new Set(styles.flat(Infinity).reverse());\n            // Then preserve original order by adding the set items in reverse order.\n            for (const s of set) {\n                elementStyles.unshift((0,_css_tag_js__WEBPACK_IMPORTED_MODULE_1__.getCompatibleStyle)(s));\n            }\n        }\n        else if (styles !== undefined) {\n            elementStyles.push((0,_css_tag_js__WEBPACK_IMPORTED_MODULE_1__.getCompatibleStyle)(styles));\n        }\n        return elementStyles;\n    }\n    /**\n     * Returns the property name for the given attribute `name`.\n     * @nocollapse\n     */\n    static __attributeNameForProperty(name, options) {\n        const attribute = options.attribute;\n        return attribute === false\n            ? undefined\n            : typeof attribute === 'string'\n                ? attribute\n                : typeof name === 'string'\n                    ? name.toLowerCase()\n                    : undefined;\n    }\n    constructor() {\n        super();\n        this.__instanceProperties = undefined;\n        /**\n         * True if there is a pending update as a result of calling `requestUpdate()`.\n         * Should only be read.\n         * @category updates\n         */\n        this.isUpdatePending = false;\n        /**\n         * Is set to `true` after the first update. The element code cannot assume\n         * that `renderRoot` exists before the element `hasUpdated`.\n         * @category updates\n         */\n        this.hasUpdated = false;\n        /**\n         * Name of currently reflecting property\n         */\n        this.__reflectingProperty = null;\n        this.__initialize();\n    }\n    /**\n     * Internal only override point for customizing work done when elements\n     * are constructed.\n     */\n    __initialize() {\n        this.__updatePromise = new Promise((res) => (this.enableUpdating = res));\n        this._$changedProperties = new Map();\n        // This enqueues a microtask that must run before the first update, so it\n        // must be called before requestUpdate()\n        this.__saveInstanceProperties();\n        // ensures first update will be caught by an early access of\n        // `updateComplete`\n        this.requestUpdate();\n        this.constructor._initializers?.forEach((i) => i(this));\n    }\n    /**\n     * Registers a `ReactiveController` to participate in the element's reactive\n     * update cycle. The element automatically calls into any registered\n     * controllers during its lifecycle callbacks.\n     *\n     * If the element is connected when `addController()` is called, the\n     * controller's `hostConnected()` callback will be immediately called.\n     * @category controllers\n     */\n    addController(controller) {\n        (this.__controllers ??= new Set()).add(controller);\n        // If a controller is added after the element has been connected,\n        // call hostConnected. Note, re-using existence of `renderRoot` here\n        // (which is set in connectedCallback) to avoid the need to track a\n        // first connected state.\n        if (this.renderRoot !== undefined && this.isConnected) {\n            controller.hostConnected?.();\n        }\n    }\n    /**\n     * Removes a `ReactiveController` from the element.\n     * @category controllers\n     */\n    removeController(controller) {\n        this.__controllers?.delete(controller);\n    }\n    /**\n     * Fixes any properties set on the instance before upgrade time.\n     * Otherwise these would shadow the accessor and break these properties.\n     * The properties are stored in a Map which is played back after the\n     * constructor runs.\n     */\n    __saveInstanceProperties() {\n        const instanceProperties = new Map();\n        const elementProperties = this.constructor\n            .elementProperties;\n        for (const p of elementProperties.keys()) {\n            if (this.hasOwnProperty(p)) {\n                instanceProperties.set(p, this[p]);\n                delete this[p];\n            }\n        }\n        if (instanceProperties.size > 0) {\n            this.__instanceProperties = instanceProperties;\n        }\n    }\n    /**\n     * Returns the node into which the element should render and by default\n     * creates and returns an open shadowRoot. Implement to customize where the\n     * element's DOM is rendered. For example, to render into the element's\n     * childNodes, return `this`.\n     *\n     * @return Returns a node into which to render.\n     * @category rendering\n     */\n    createRenderRoot() {\n        const renderRoot = this.shadowRoot ??\n            this.attachShadow(this.constructor.shadowRootOptions);\n        (0,_css_tag_js__WEBPACK_IMPORTED_MODULE_1__.adoptStyles)(renderRoot, this.constructor.elementStyles);\n        return renderRoot;\n    }\n    /**\n     * On first connection, creates the element's renderRoot, sets up\n     * element styling, and enables updating.\n     * @category lifecycle\n     */\n    connectedCallback() {\n        // Create renderRoot before controllers `hostConnected`\n        this.renderRoot ??=\n            this.createRenderRoot();\n        this.enableUpdating(true);\n        this.__controllers?.forEach((c) => c.hostConnected?.());\n    }\n    /**\n     * Note, this method should be considered final and not overridden. It is\n     * overridden on the element instance with a function that triggers the first\n     * update.\n     * @category updates\n     */\n    enableUpdating(_requestedUpdate) { }\n    /**\n     * Allows for `super.disconnectedCallback()` in extensions while\n     * reserving the possibility of making non-breaking feature additions\n     * when disconnecting at some point in the future.\n     * @category lifecycle\n     */\n    disconnectedCallback() {\n        this.__controllers?.forEach((c) => c.hostDisconnected?.());\n    }\n    /**\n     * Synchronizes property values when attributes change.\n     *\n     * Specifically, when an attribute is set, the corresponding property is set.\n     * You should rarely need to implement this callback. If this method is\n     * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n     * called.\n     *\n     * See [responding to attribute changes](https://developer.mozilla.org/en-US/docs/Web/API/Web_components/Using_custom_elements#responding_to_attribute_changes)\n     * on MDN for more information about the `attributeChangedCallback`.\n     * @category attributes\n     */\n    attributeChangedCallback(name, _old, value) {\n        this._$attributeToProperty(name, value);\n    }\n    __propertyToAttribute(name, value) {\n        const elemProperties = this.constructor.elementProperties;\n        const options = elemProperties.get(name);\n        const attr = this.constructor.__attributeNameForProperty(name, options);\n        if (attr !== undefined && options.reflect === true) {\n            const converter = options.converter?.toAttribute !==\n                undefined\n                ? options.converter\n                : defaultConverter;\n            const attrValue = converter.toAttribute(value, options.type);\n            if (this.constructor.enabledWarnings.includes('migration') &&\n                attrValue === undefined) {\n                issueWarning('undefined-attribute-value', `The attribute value for the ${name} property is ` +\n                    `undefined on element ${this.localName}. The attribute will be ` +\n                    `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n                    `the attribute would not have changed.`);\n            }\n            // Track if the property is being reflected to avoid\n            // setting the property again via `attributeChangedCallback`. Note:\n            // 1. this takes advantage of the fact that the callback is synchronous.\n            // 2. will behave incorrectly if multiple attributes are in the reaction\n            // stack at time of calling. However, since we process attributes\n            // in `update` this should not be possible (or an extreme corner case\n            // that we'd like to discover).\n            // mark state reflecting\n            this.__reflectingProperty = name;\n            if (attrValue == null) {\n                this.removeAttribute(attr);\n            }\n            else {\n                this.setAttribute(attr, attrValue);\n            }\n            // mark state not reflecting\n            this.__reflectingProperty = null;\n        }\n    }\n    /** @internal */\n    _$attributeToProperty(name, value) {\n        const ctor = this.constructor;\n        // Note, hint this as an `AttributeMap` so closure clearly understands\n        // the type; it has issues with tracking types through statics\n        const propName = ctor.__attributeToPropertyMap.get(name);\n        // Use tracking info to avoid reflecting a property value to an attribute\n        // if it was just set because the attribute changed.\n        if (propName !== undefined && this.__reflectingProperty !== propName) {\n            const options = ctor.getPropertyOptions(propName);\n            const converter = typeof options.converter === 'function'\n                ? { fromAttribute: options.converter }\n                : options.converter?.fromAttribute !== undefined\n                    ? options.converter\n                    : defaultConverter;\n            // mark state reflecting\n            this.__reflectingProperty = propName;\n            this[propName] =\n                converter.fromAttribute(value, options.type) ??\n                    this.__defaultValues?.get(propName) ??\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    null;\n            // mark state not reflecting\n            this.__reflectingProperty = null;\n        }\n    }\n    /**\n     * Requests an update which is processed asynchronously. This should be called\n     * when an element should update based on some state not triggered by setting\n     * a reactive property. In this case, pass no arguments. It should also be\n     * called when manually implementing a property setter. In this case, pass the\n     * property `name` and `oldValue` to ensure that any configured property\n     * options are honored.\n     *\n     * @param name name of requesting property\n     * @param oldValue old value of requesting property\n     * @param options property options to use instead of the previously\n     *     configured options\n     * @category updates\n     */\n    requestUpdate(name, oldValue, options) {\n        // If we have a property key, perform property update steps.\n        if (name !== undefined) {\n            if (name instanceof Event) {\n                issueWarning(``, `The requestUpdate() method was called with an Event as the property name. This is probably a mistake caused by binding this.requestUpdate as an event listener. Instead bind a function that will call it with no arguments: () => this.requestUpdate()`);\n            }\n            const ctor = this.constructor;\n            const newValue = this[name];\n            options ??= ctor.getPropertyOptions(name);\n            const changed = (options.hasChanged ?? notEqual)(newValue, oldValue) ||\n                // When there is no change, check a corner case that can occur when\n                // 1. there's a initial value which was not reflected\n                // 2. the property is subsequently set to this value.\n                // For example, `prop: {useDefault: true, reflect: true}`\n                // and el.prop = 'foo'. This should be considered a change if the\n                // attribute is not set because we will now reflect the property to the attribute.\n                (options.useDefault &&\n                    options.reflect &&\n                    newValue === this.__defaultValues?.get(name) &&\n                    !this.hasAttribute(ctor.__attributeNameForProperty(name, options)));\n            if (changed) {\n                this._$changeProperty(name, oldValue, options);\n            }\n            else {\n                // Abort the request if the property should not be considered changed.\n                return;\n            }\n        }\n        if (this.isUpdatePending === false) {\n            this.__updatePromise = this.__enqueueUpdate();\n        }\n    }\n    /**\n     * @internal\n     */\n    _$changeProperty(name, oldValue, { useDefault, reflect, wrapped }, initializeValue) {\n        // Record default value when useDefault is used. This allows us to\n        // restore this value when the attribute is removed.\n        if (useDefault && !(this.__defaultValues ??= new Map()).has(name)) {\n            this.__defaultValues.set(name, initializeValue ?? oldValue ?? this[name]);\n            // if this is not wrapping an accessor, it must be an initial setting\n            // and in this case we do not want to record the change or reflect.\n            if (wrapped !== true || initializeValue !== undefined) {\n                return;\n            }\n        }\n        // TODO (justinfagnani): Create a benchmark of Map.has() + Map.set(\n        // vs just Map.set()\n        if (!this._$changedProperties.has(name)) {\n            // On the initial change, the old value should be `undefined`, except\n            // with `useDefault`\n            if (!this.hasUpdated && !useDefault) {\n                oldValue = undefined;\n            }\n            this._$changedProperties.set(name, oldValue);\n        }\n        // Add to reflecting properties set.\n        // Note, it's important that every change has a chance to add the\n        // property to `__reflectingProperties`. This ensures setting\n        // attribute + property reflects correctly.\n        if (reflect === true && this.__reflectingProperty !== name) {\n            (this.__reflectingProperties ??= new Set()).add(name);\n        }\n    }\n    /**\n     * Sets up the element to asynchronously update.\n     */\n    async __enqueueUpdate() {\n        this.isUpdatePending = true;\n        try {\n            // Ensure any previous update has resolved before updating.\n            // This `await` also ensures that property changes are batched.\n            await this.__updatePromise;\n        }\n        catch (e) {\n            // Refire any previous errors async so they do not disrupt the update\n            // cycle. Errors are refired so developers have a chance to observe\n            // them, and this can be done by implementing\n            // `window.onunhandledrejection`.\n            Promise.reject(e);\n        }\n        const result = this.scheduleUpdate();\n        // If `scheduleUpdate` returns a Promise, we await it. This is done to\n        // enable coordinating updates with a scheduler. Note, the result is\n        // checked to avoid delaying an additional microtask unless we need to.\n        if (result != null) {\n            await result;\n        }\n        return !this.isUpdatePending;\n    }\n    /**\n     * Schedules an element update. You can override this method to change the\n     * timing of updates by returning a Promise. The update will await the\n     * returned Promise, and you should resolve the Promise to allow the update\n     * to proceed. If this method is overridden, `super.scheduleUpdate()`\n     * must be called.\n     *\n     * For instance, to schedule updates to occur just before the next frame:\n     *\n     * ```ts\n     * override protected async scheduleUpdate(): Promise<unknown> {\n     *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n     *   super.scheduleUpdate();\n     * }\n     * ```\n     * @category updates\n     */\n    scheduleUpdate() {\n        const result = this.performUpdate();\n        if (this.constructor.enabledWarnings.includes('async-perform-update') &&\n            typeof result?.then ===\n                'function') {\n            issueWarning('async-perform-update', `Element ${this.localName} returned a Promise from performUpdate(). ` +\n                `This behavior is deprecated and will be removed in a future ` +\n                `version of ReactiveElement.`);\n        }\n        return result;\n    }\n    /**\n     * Performs an element update. Note, if an exception is thrown during the\n     * update, `firstUpdated` and `updated` will not be called.\n     *\n     * Call `performUpdate()` to immediately process a pending update. This should\n     * generally not be needed, but it can be done in rare cases when you need to\n     * update synchronously.\n     *\n     * @category updates\n     */\n    performUpdate() {\n        // Abort any update if one is not pending when this is called.\n        // This can happen if `performUpdate` is called early to \"flush\"\n        // the update.\n        if (!this.isUpdatePending) {\n            return;\n        }\n        debugLogEvent?.({ kind: 'update' });\n        if (!this.hasUpdated) {\n            // Create renderRoot before first update. This occurs in `connectedCallback`\n            // but is done here to support out of tree calls to `enableUpdating`/`performUpdate`.\n            this.renderRoot ??=\n                this.createRenderRoot();\n            {\n                // Produce warning if any reactive properties on the prototype are\n                // shadowed by class fields. Instance fields set before upgrade are\n                // deleted by this point, so any own property is caused by class field\n                // initialization in the constructor.\n                const ctor = this.constructor;\n                const shadowedProperties = [...ctor.elementProperties.keys()].filter((p) => this.hasOwnProperty(p) && p in getPrototypeOf(this));\n                if (shadowedProperties.length) {\n                    throw new Error(`The following properties on element ${this.localName} will not ` +\n                        `trigger updates as expected because they are set using class ` +\n                        `fields: ${shadowedProperties.join(', ')}. ` +\n                        `Native class fields and some compiled output will overwrite ` +\n                        `accessors used for detecting changes. See ` +\n                        `https://lit.dev/msg/class-field-shadowing ` +\n                        `for more information.`);\n                }\n            }\n            // Mixin instance properties once, if they exist.\n            if (this.__instanceProperties) {\n                // TODO (justinfagnani): should we use the stored value? Could a new value\n                // have been set since we stored the own property value?\n                for (const [p, value] of this.__instanceProperties) {\n                    this[p] = value;\n                }\n                this.__instanceProperties = undefined;\n            }\n            // Trigger initial value reflection and populate the initial\n            // `changedProperties` map, but only for the case of properties created\n            // via `createProperty` on accessors, which will not have already\n            // populated the `changedProperties` map since they are not set.\n            // We can't know if these accessors had initializers, so we just set\n            // them anyway - a difference from experimental decorators on fields and\n            // standard decorators on auto-accessors.\n            // For context see:\n            // https://github.com/lit/lit/pull/4183#issuecomment-1711959635\n            const elementProperties = this.constructor\n                .elementProperties;\n            if (elementProperties.size > 0) {\n                for (const [p, options] of elementProperties) {\n                    const { wrapped } = options;\n                    const value = this[p];\n                    if (wrapped === true &&\n                        !this._$changedProperties.has(p) &&\n                        value !== undefined) {\n                        this._$changeProperty(p, undefined, options, value);\n                    }\n                }\n            }\n        }\n        let shouldUpdate = false;\n        const changedProperties = this._$changedProperties;\n        try {\n            shouldUpdate = this.shouldUpdate(changedProperties);\n            if (shouldUpdate) {\n                this.willUpdate(changedProperties);\n                this.__controllers?.forEach((c) => c.hostUpdate?.());\n                this.update(changedProperties);\n            }\n            else {\n                this.__markUpdated();\n            }\n        }\n        catch (e) {\n            // Prevent `firstUpdated` and `updated` from running when there's an\n            // update exception.\n            shouldUpdate = false;\n            // Ensure element can accept additional updates after an exception.\n            this.__markUpdated();\n            throw e;\n        }\n        // The update is no longer considered pending and further updates are now allowed.\n        if (shouldUpdate) {\n            this._$didUpdate(changedProperties);\n        }\n    }\n    /**\n     * Invoked before `update()` to compute values needed during the update.\n     *\n     * Implement `willUpdate` to compute property values that depend on other\n     * properties and are used in the rest of the update process.\n     *\n     * ```ts\n     * willUpdate(changedProperties) {\n     *   // only need to check changed properties for an expensive computation.\n     *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n     *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n     *   }\n     * }\n     *\n     * render() {\n     *   return html`SHA: ${this.sha}`;\n     * }\n     * ```\n     *\n     * @category updates\n     */\n    willUpdate(_changedProperties) { }\n    // Note, this is an override point for polyfill-support.\n    // @internal\n    _$didUpdate(changedProperties) {\n        this.__controllers?.forEach((c) => c.hostUpdated?.());\n        if (!this.hasUpdated) {\n            this.hasUpdated = true;\n            this.firstUpdated(changedProperties);\n        }\n        this.updated(changedProperties);\n        if (this.isUpdatePending &&\n            this.constructor.enabledWarnings.includes('change-in-update')) {\n            issueWarning('change-in-update', `Element ${this.localName} scheduled an update ` +\n                `(generally because a property was set) ` +\n                `after an update completed, causing a new update to be scheduled. ` +\n                `This is inefficient and should be avoided unless the next update ` +\n                `can only be scheduled as a side effect of the previous update.`);\n        }\n    }\n    __markUpdated() {\n        this._$changedProperties = new Map();\n        this.isUpdatePending = false;\n    }\n    /**\n     * Returns a Promise that resolves when the element has completed updating.\n     * The Promise value is a boolean that is `true` if the element completed the\n     * update without triggering another update. The Promise result is `false` if\n     * a property was set inside `updated()`. If the Promise is rejected, an\n     * exception was thrown during the update.\n     *\n     * To await additional asynchronous work, override the `getUpdateComplete`\n     * method. For example, it is sometimes useful to await a rendered element\n     * before fulfilling this Promise. To do this, first await\n     * `super.getUpdateComplete()`, then any subsequent state.\n     *\n     * @return A promise of a boolean that resolves to true if the update completed\n     *     without triggering another update.\n     * @category updates\n     */\n    get updateComplete() {\n        return this.getUpdateComplete();\n    }\n    /**\n     * Override point for the `updateComplete` promise.\n     *\n     * It is not safe to override the `updateComplete` getter directly due to a\n     * limitation in TypeScript which means it is not possible to call a\n     * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n     * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n     * This method should be overridden instead. For example:\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   override async getUpdateComplete() {\n     *     const result = await super.getUpdateComplete();\n     *     await this._myChild.updateComplete;\n     *     return result;\n     *   }\n     * }\n     * ```\n     *\n     * @return A promise of a boolean that resolves to true if the update completed\n     *     without triggering another update.\n     * @category updates\n     */\n    getUpdateComplete() {\n        return this.__updatePromise;\n    }\n    /**\n     * Controls whether or not `update()` should be called when the element requests\n     * an update. By default, this method always returns `true`, but this can be\n     * customized to control when to update.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    shouldUpdate(_changedProperties) {\n        return true;\n    }\n    /**\n     * Updates the element. This method reflects property values to attributes.\n     * It can be overridden to render and keep updated element DOM.\n     * Setting properties inside this method will *not* trigger\n     * another update.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    update(_changedProperties) {\n        // The forEach() expression will only run when __reflectingProperties is\n        // defined, and it returns undefined, setting __reflectingProperties to\n        // undefined\n        this.__reflectingProperties &&= this.__reflectingProperties.forEach((p) => this.__propertyToAttribute(p, this[p]));\n        this.__markUpdated();\n    }\n    /**\n     * Invoked whenever the element is updated. Implement to perform\n     * post-updating tasks via DOM APIs, for example, focusing an element.\n     *\n     * Setting properties inside this method will trigger the element to update\n     * again after this update cycle completes.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    updated(_changedProperties) { }\n    /**\n     * Invoked when the element is first updated. Implement to perform one time\n     * work on the element after update.\n     *\n     * ```ts\n     * firstUpdated() {\n     *   this.renderRoot.getElementById('my-text-area').focus();\n     * }\n     * ```\n     *\n     * Setting properties inside this method will trigger the element to update\n     * again after this update cycle completes.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    firstUpdated(_changedProperties) { }\n}\n/**\n * Memoized list of all element styles.\n * Created lazily on user subclasses when finalizing the class.\n * @nocollapse\n * @category styles\n */\nReactiveElement.elementStyles = [];\n/**\n * Options used when calling `attachShadow`. Set this property to customize\n * the options for the shadowRoot; for example, to create a closed\n * shadowRoot: `{mode: 'closed'}`.\n *\n * Note, these options are used in `createRenderRoot`. If this method\n * is customized, options should be respected if possible.\n * @nocollapse\n * @category rendering\n */\nReactiveElement.shadowRootOptions = { mode: 'open' };\n// Assigned here to work around a jscompiler bug with static fields\n// when compiling to ES5.\n// https://github.com/google/closure-compiler/issues/3177\nReactiveElement[JSCompiler_renameProperty('elementProperties')] = new Map();\nReactiveElement[JSCompiler_renameProperty('finalized')] = new Map();\n// Apply polyfills if available\npolyfillSupport?.({ ReactiveElement });\n// Dev mode warnings...\n{\n    // Default warning set.\n    ReactiveElement.enabledWarnings = [\n        'change-in-update',\n        'async-perform-update',\n    ];\n    const ensureOwnWarnings = function (ctor) {\n        if (!ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings'))) {\n            ctor.enabledWarnings = ctor.enabledWarnings.slice();\n        }\n    };\n    ReactiveElement.enableWarning = function (warning) {\n        ensureOwnWarnings(this);\n        if (!this.enabledWarnings.includes(warning)) {\n            this.enabledWarnings.push(warning);\n        }\n    };\n    ReactiveElement.disableWarning = function (warning) {\n        ensureOwnWarnings(this);\n        const i = this.enabledWarnings.indexOf(warning);\n        if (i >= 0) {\n            this.enabledWarnings.splice(i, 1);\n        }\n    };\n}\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n(global.reactiveElementVersions ??= []).push('2.1.0');\nif (global.reactiveElementVersions.length > 1) {\n    queueMicrotask(() => {\n        issueWarning('multiple-versions', `Multiple versions of Lit loaded. Loading multiple versions ` +\n            `is not recommended.`);\n    });\n}\n\n\n//# sourceMappingURL=reactive-element.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@lit/reactive-element/node/development/reactive-element.js\n");

/***/ })

};
;