"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@safe-global";
exports.ids = ["vendor-chunks/@safe-global"];
exports.modules = {

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/index.js":
/*!*********************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-provider/dist/index.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppProvider = void 0;\nvar provider_1 = __webpack_require__(/*! ./provider */ \"(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/provider.js\");\nObject.defineProperty(exports, \"SafeAppProvider\", ({ enumerable: true, get: function () { return provider_1.SafeAppProvider; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtcHJvdmlkZXIvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx1QkFBdUI7QUFDdkIsaUJBQWlCLG1CQUFPLENBQUMsMEZBQVk7QUFDckMsbURBQWtELEVBQUUscUNBQXFDLHNDQUFzQyxFQUFDO0FBQ2hJIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtcHJvdmlkZXIvZGlzdC9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuU2FmZUFwcFByb3ZpZGVyID0gdm9pZCAwO1xudmFyIHByb3ZpZGVyXzEgPSByZXF1aXJlKFwiLi9wcm92aWRlclwiKTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlNhZmVBcHBQcm92aWRlclwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gcHJvdmlkZXJfMS5TYWZlQXBwUHJvdmlkZXI7IH0gfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/provider.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-provider/dist/provider.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppProvider = void 0;\nconst safe_apps_sdk_1 = __webpack_require__(/*! @safe-global/safe-apps-sdk */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js\");\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/utils.js\");\n// The API is based on Ethereum JavaScript API Provider Standard. Link: https://eips.ethereum.org/EIPS/eip-1193\nclass SafeAppProvider extends events_1.EventEmitter {\n    constructor(safe, sdk) {\n        super();\n        this.submittedTxs = new Map();\n        this.safe = safe;\n        this.sdk = sdk;\n    }\n    async connect() {\n        this.emit('connect', { chainId: this.chainId });\n        return;\n    }\n    async disconnect() {\n        return;\n    }\n    get chainId() {\n        return this.safe.chainId;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async request(request) {\n        const { method, params = [] } = request;\n        switch (method) {\n            case 'eth_accounts':\n                return [this.safe.safeAddress];\n            case 'net_version':\n            case 'eth_chainId':\n                return (0, utils_1.numberToHex)(this.chainId);\n            case 'personal_sign': {\n                const [message, address] = params;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase()) {\n                    throw new Error('The address or message hash is invalid');\n                }\n                const response = await this.sdk.txs.signMessage(message);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_sign': {\n                const [address, messageHash] = params;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase() || !messageHash.startsWith('0x')) {\n                    throw new Error('The address or message hash is invalid');\n                }\n                const response = await this.sdk.txs.signMessage(messageHash);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_signTypedData':\n            case 'eth_signTypedData_v4': {\n                const [address, typedData] = params;\n                const parsedTypedData = typeof typedData === 'string' ? JSON.parse(typedData) : typedData;\n                if (this.safe.safeAddress.toLowerCase() !== address.toLowerCase()) {\n                    throw new Error('The address is invalid');\n                }\n                const response = await this.sdk.txs.signTypedMessage(parsedTypedData);\n                const signature = 'signature' in response ? response.signature : undefined;\n                return signature || '0x';\n            }\n            case 'eth_sendTransaction':\n                // `value` or `data` can be explicitly set as `undefined` for example in Viem. The spread will overwrite the fallback value.\n                const tx = {\n                    ...params[0],\n                    value: params[0].value || '0',\n                    data: params[0].data || '0x',\n                };\n                // Some ethereum libraries might pass the gas as a hex-encoded string\n                // We need to convert it to a number because the SDK expects a number and our backend only supports\n                // Decimal numbers\n                if (typeof tx.gas === 'string' && tx.gas.startsWith('0x')) {\n                    tx.gas = parseInt(tx.gas, 16);\n                }\n                const resp = await this.sdk.txs.send({\n                    txs: [tx],\n                    params: { safeTxGas: tx.gas },\n                });\n                // Store fake transaction\n                this.submittedTxs.set(resp.safeTxHash, {\n                    from: this.safe.safeAddress,\n                    hash: resp.safeTxHash,\n                    gas: 0,\n                    gasPrice: '0x00',\n                    nonce: 0,\n                    input: tx.data,\n                    value: tx.value,\n                    to: tx.to,\n                    blockHash: null,\n                    blockNumber: null,\n                    transactionIndex: null,\n                });\n                return resp.safeTxHash;\n            case 'eth_blockNumber':\n                const block = await this.sdk.eth.getBlockByNumber(['latest']);\n                return block.number;\n            case 'eth_getBalance':\n                return this.sdk.eth.getBalance([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getCode':\n                return this.sdk.eth.getCode([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getTransactionCount':\n                return this.sdk.eth.getTransactionCount([(0, utils_1.getLowerCase)(params[0]), params[1]]);\n            case 'eth_getStorageAt':\n                return this.sdk.eth.getStorageAt([(0, utils_1.getLowerCase)(params[0]), params[1], params[2]]);\n            case 'eth_getBlockByNumber':\n                return this.sdk.eth.getBlockByNumber([params[0], params[1]]);\n            case 'eth_getBlockByHash':\n                return this.sdk.eth.getBlockByHash([params[0], params[1]]);\n            case 'eth_getTransactionByHash':\n                let txHash = params[0];\n                try {\n                    const resp = await this.sdk.txs.getBySafeTxHash(txHash);\n                    txHash = resp.txHash || txHash;\n                }\n                catch (e) { }\n                // Use fake transaction if we don't have a real tx hash\n                if (this.submittedTxs.has(txHash)) {\n                    return this.submittedTxs.get(txHash);\n                }\n                return this.sdk.eth.getTransactionByHash([txHash]).then((tx) => {\n                    // We set the tx hash to the one requested, as some provider assert this\n                    if (tx) {\n                        tx.hash = params[0];\n                    }\n                    return tx;\n                });\n            case 'eth_getTransactionReceipt': {\n                let txHash = params[0];\n                try {\n                    const resp = await this.sdk.txs.getBySafeTxHash(txHash);\n                    txHash = resp.txHash || txHash;\n                }\n                catch (e) { }\n                return this.sdk.eth.getTransactionReceipt([txHash]).then((tx) => {\n                    // We set the tx hash to the one requested, as some provider assert this\n                    if (tx) {\n                        tx.transactionHash = params[0];\n                    }\n                    return tx;\n                });\n            }\n            case 'eth_estimateGas': {\n                return this.sdk.eth.getEstimateGas(params[0]);\n            }\n            case 'eth_call': {\n                return this.sdk.eth.call([params[0], params[1]]);\n            }\n            case 'eth_getLogs':\n                return this.sdk.eth.getPastLogs([params[0]]);\n            case 'eth_gasPrice':\n                return this.sdk.eth.getGasPrice();\n            case 'wallet_getPermissions':\n                return this.sdk.wallet.getPermissions();\n            case 'wallet_requestPermissions':\n                return this.sdk.wallet.requestPermissions(params[0]);\n            case 'safe_setSettings':\n                return this.sdk.eth.setSafeSettings([params[0]]);\n            case 'wallet_sendCalls': {\n                const { from, calls, chainId } = params[0];\n                if (chainId !== (0, utils_1.numberToHex)(this.chainId)) {\n                    throw new Error(`Safe is not on chain ${chainId}`);\n                }\n                if (from !== this.safe.safeAddress) {\n                    throw Error('Invalid from address');\n                }\n                const txs = calls.map((call, i) => {\n                    if (!call.to) {\n                        throw new Error(`Invalid call #${i}: missing \"to\" field`);\n                    }\n                    return {\n                        to: call.to,\n                        data: call.data ?? '0x',\n                        value: call.value ?? (0, utils_1.numberToHex)(0),\n                    };\n                });\n                const { safeTxHash } = await this.sdk.txs.send({ txs });\n                const result = {\n                    id: safeTxHash,\n                };\n                return result;\n            }\n            case 'wallet_getCallsStatus': {\n                const safeTxHash = params[0];\n                const CallStatus = {\n                    [safe_apps_sdk_1.TransactionStatus.AWAITING_CONFIRMATIONS]: 100,\n                    [safe_apps_sdk_1.TransactionStatus.AWAITING_EXECUTION]: 100,\n                    [safe_apps_sdk_1.TransactionStatus.SUCCESS]: 200,\n                    [safe_apps_sdk_1.TransactionStatus.CANCELLED]: 400,\n                    [safe_apps_sdk_1.TransactionStatus.FAILED]: 500,\n                };\n                const tx = await this.sdk.txs.getBySafeTxHash(safeTxHash);\n                const result = {\n                    version: '1.0',\n                    id: safeTxHash,\n                    chainId: (0, utils_1.numberToHex)(this.chainId),\n                    status: CallStatus[tx.txStatus],\n                };\n                // Transaction is queued\n                if (!tx.txHash) {\n                    return result;\n                }\n                // If transaction is executing, receipt is null\n                const receipt = await this.sdk.eth.getTransactionReceipt([tx.txHash]);\n                if (!receipt) {\n                    return result;\n                }\n                const calls = tx.txData?.dataDecoded?.method !== 'multiSend'\n                    ? 1\n                    : // Number of batched transactions\n                        tx.txData.dataDecoded.parameters?.[0].valueDecoded?.length ?? 1;\n                // Typed as number; is hex\n                const blockNumber = Number(receipt.blockNumber);\n                const gasUsed = Number(receipt.gasUsed);\n                result.receipts = Array(calls).fill({\n                    logs: receipt.logs,\n                    status: (0, utils_1.numberToHex)(tx.txStatus === safe_apps_sdk_1.TransactionStatus.SUCCESS ? 1 : 0),\n                    blockHash: receipt.blockHash,\n                    blockNumber: (0, utils_1.numberToHex)(blockNumber),\n                    gasUsed: (0, utils_1.numberToHex)(gasUsed),\n                    transactionHash: tx.txHash,\n                });\n                return result;\n            }\n            case 'wallet_showCallsStatus': {\n                // Cannot open transaction details page via SDK\n                throw new Error(`\"${request.method}\" not supported`);\n            }\n            case 'wallet_getCapabilities': {\n                return {\n                    [(0, utils_1.numberToHex)(this.chainId)]: {\n                        atomicBatch: {\n                            supported: true,\n                        },\n                    },\n                };\n            }\n            default:\n                throw Error(`\"${request.method}\" not implemented`);\n        }\n    }\n    // this method is needed for ethers v4\n    // https://github.com/ethers-io/ethers.js/blob/427e16826eb15d52d25c4f01027f8db22b74b76c/src.ts/providers/web3-provider.ts#L41-L55\n    send(request, callback) {\n        if (!request)\n            callback('Undefined request');\n        this.request(request)\n            .then((result) => callback(null, { jsonrpc: '2.0', id: request.id, result }))\n            .catch((error) => callback(error, null));\n    }\n}\nexports.SafeAppProvider = SafeAppProvider;\n//# sourceMappingURL=provider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/provider.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/utils.js":
/*!*********************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-provider/dist/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.numberToHex = exports.getLowerCase = void 0;\nfunction getLowerCase(value) {\n    if (value) {\n        return value.toLowerCase();\n    }\n    return value;\n}\nexports.getLowerCase = getLowerCase;\nfunction numberToHex(value) {\n    return `0x${value.toString(16)}`;\n}\nexports.numberToHex = numberToHex;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtcHJvdmlkZXIvZGlzdC91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxtQkFBbUIsR0FBRyxvQkFBb0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0EsZ0JBQWdCLG1CQUFtQjtBQUNuQztBQUNBLG1CQUFtQjtBQUNuQiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXByb3ZpZGVyL2Rpc3QvdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLm51bWJlclRvSGV4ID0gZXhwb3J0cy5nZXRMb3dlckNhc2UgPSB2b2lkIDA7XG5mdW5jdGlvbiBnZXRMb3dlckNhc2UodmFsdWUpIHtcbiAgICBpZiAodmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlLnRvTG93ZXJDYXNlKCk7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn1cbmV4cG9ydHMuZ2V0TG93ZXJDYXNlID0gZ2V0TG93ZXJDYXNlO1xuZnVuY3Rpb24gbnVtYmVyVG9IZXgodmFsdWUpIHtcbiAgICByZXR1cm4gYDB4JHt2YWx1ZS50b1N0cmluZygxNil9YDtcbn1cbmV4cG9ydHMubnVtYmVyVG9IZXggPSBudW1iZXJUb0hleDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst messageFormatter_js_1 = __webpack_require__(/*! ./messageFormatter.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\");\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = messageFormatter_js_1.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\nexports[\"default\"] = PostMessageCommunicator;\n__exportStar(__webpack_require__(/*! ./methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js":
/*!*********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MessageFormatter = void 0;\nconst version_js_1 = __webpack_require__(/*! ../version.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js\");\nclass MessageFormatter {\n}\nexports.MessageFormatter = MessageFormatter;\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0, utils_js_1.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0, version_js_1.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n//# sourceMappingURL=messageFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL2NvbW11bmljYXRpb24vbWVzc2FnZUZvcm1hdHRlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx3QkFBd0I7QUFDeEIscUJBQXFCLG1CQUFPLENBQUMsMkZBQWU7QUFDNUMsbUJBQW1CLG1CQUFPLENBQUMsb0dBQVk7QUFDdkM7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvY29tbXVuaWNhdGlvbi9tZXNzYWdlRm9ybWF0dGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5NZXNzYWdlRm9ybWF0dGVyID0gdm9pZCAwO1xuY29uc3QgdmVyc2lvbl9qc18xID0gcmVxdWlyZShcIi4uL3ZlcnNpb24uanNcIik7XG5jb25zdCB1dGlsc19qc18xID0gcmVxdWlyZShcIi4vdXRpbHMuanNcIik7XG5jbGFzcyBNZXNzYWdlRm9ybWF0dGVyIHtcbn1cbmV4cG9ydHMuTWVzc2FnZUZvcm1hdHRlciA9IE1lc3NhZ2VGb3JtYXR0ZXI7XG5NZXNzYWdlRm9ybWF0dGVyLm1ha2VSZXF1ZXN0ID0gKG1ldGhvZCwgcGFyYW1zKSA9PiB7XG4gICAgY29uc3QgaWQgPSAoMCwgdXRpbHNfanNfMS5nZW5lcmF0ZVJlcXVlc3RJZCkoKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBpZCxcbiAgICAgICAgbWV0aG9kLFxuICAgICAgICBwYXJhbXMsXG4gICAgICAgIGVudjoge1xuICAgICAgICAgICAgc2RrVmVyc2lvbjogKDAsIHZlcnNpb25fanNfMS5nZXRTREtWZXJzaW9uKSgpLFxuICAgICAgICB9LFxuICAgIH07XG59O1xuTWVzc2FnZUZvcm1hdHRlci5tYWtlUmVzcG9uc2UgPSAoaWQsIGRhdGEsIHZlcnNpb24pID0+ICh7XG4gICAgaWQsXG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICB2ZXJzaW9uLFxuICAgIGRhdGEsXG59KTtcbk1lc3NhZ2VGb3JtYXR0ZXIubWFrZUVycm9yUmVzcG9uc2UgPSAoaWQsIGVycm9yLCB2ZXJzaW9uKSA9PiAoe1xuICAgIGlkLFxuICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgIGVycm9yLFxuICAgIHZlcnNpb24sXG59KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1lc3NhZ2VGb3JtYXR0ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js":
/*!************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RestrictedMethods = exports.Methods = void 0;\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (exports.Methods = Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (exports.RestrictedMethods = RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRequestId = void 0;\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\nexports.generateRequestId = generateRequestId;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL2NvbW11bmljYXRpb24vdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL2NvbW11bmljYXRpb24vdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmdlbmVyYXRlUmVxdWVzdElkID0gdm9pZCAwO1xuLy8gaS5lLiAwLTI1NSAtPiAnMDAnLSdmZidcbmNvbnN0IGRlYzJoZXggPSAoZGVjKSA9PiBkZWMudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyk7XG5jb25zdCBnZW5lcmF0ZUlkID0gKGxlbikgPT4ge1xuICAgIGNvbnN0IGFyciA9IG5ldyBVaW50OEFycmF5KChsZW4gfHwgNDApIC8gMik7XG4gICAgd2luZG93LmNyeXB0by5nZXRSYW5kb21WYWx1ZXMoYXJyKTtcbiAgICByZXR1cm4gQXJyYXkuZnJvbShhcnIsIGRlYzJoZXgpLmpvaW4oJycpO1xufTtcbmNvbnN0IGdlbmVyYXRlUmVxdWVzdElkID0gKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICByZXR1cm4gZ2VuZXJhdGVJZCgxMCk7XG4gICAgfVxuICAgIHJldHVybiBuZXcgRGF0ZSgpLmdldFRpbWUoKS50b1N0cmluZygzNik7XG59O1xuZXhwb3J0cy5nZW5lcmF0ZVJlcXVlc3RJZCA9IGdlbmVyYXRlUmVxdWVzdElkO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst index_js_1 = __webpack_require__(/*! ../wallet/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\");\nconst permissions_js_1 = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\");\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new index_js_1.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new permissions_js_1.PermissionsError('Permissions rejected', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\nexports[\"default\"] = requirePermission;\n//# sourceMappingURL=requirePermissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js":
/*!****************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RPC_CALLS = void 0;\nexports.RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL2V0aC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCO0FBQ2pCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvZXRoL2NvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuUlBDX0NBTExTID0gdm9pZCAwO1xuZXhwb3J0cy5SUENfQ0FMTFMgPSB7XG4gICAgZXRoX2NhbGw6ICdldGhfY2FsbCcsXG4gICAgZXRoX2dhc1ByaWNlOiAnZXRoX2dhc1ByaWNlJyxcbiAgICBldGhfZ2V0TG9nczogJ2V0aF9nZXRMb2dzJyxcbiAgICBldGhfZ2V0QmFsYW5jZTogJ2V0aF9nZXRCYWxhbmNlJyxcbiAgICBldGhfZ2V0Q29kZTogJ2V0aF9nZXRDb2RlJyxcbiAgICBldGhfZ2V0QmxvY2tCeUhhc2g6ICdldGhfZ2V0QmxvY2tCeUhhc2gnLFxuICAgIGV0aF9nZXRCbG9ja0J5TnVtYmVyOiAnZXRoX2dldEJsb2NrQnlOdW1iZXInLFxuICAgIGV0aF9nZXRTdG9yYWdlQXQ6ICdldGhfZ2V0U3RvcmFnZUF0JyxcbiAgICBldGhfZ2V0VHJhbnNhY3Rpb25CeUhhc2g6ICdldGhfZ2V0VHJhbnNhY3Rpb25CeUhhc2gnLFxuICAgIGV0aF9nZXRUcmFuc2FjdGlvblJlY2VpcHQ6ICdldGhfZ2V0VHJhbnNhY3Rpb25SZWNlaXB0JyxcbiAgICBldGhfZ2V0VHJhbnNhY3Rpb25Db3VudDogJ2V0aF9nZXRUcmFuc2FjdGlvbkNvdW50JyxcbiAgICBldGhfZXN0aW1hdGVHYXM6ICdldGhfZXN0aW1hdGVHYXMnLFxuICAgIHNhZmVfc2V0U2V0dGluZ3M6ICdzYWZlX3NldFNldHRpbmdzJyxcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Eth = void 0;\nconst constants_js_1 = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\");\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: constants_js_1.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: constants_js_1.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\nexports.Eth = Eth;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js":
/*!********************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getSDKVersion = void 0;\nconst sdk_js_1 = __importDefault(__webpack_require__(/*! ./sdk.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\"));\nexports[\"default\"] = sdk_js_1.default;\n__exportStar(__webpack_require__(/*! ./sdk.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\"), exports);\n__exportStar(__webpack_require__(/*! ./communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\"), exports);\n__exportStar(__webpack_require__(/*! ./communication/messageFormatter.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/messageFormatter.js\"), exports);\nvar version_js_1 = __webpack_require__(/*! ./version.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\");\nObject.defineProperty(exports, \"getSDKVersion\", ({ enumerable: true, get: function () { return version_js_1.getSDKVersion; } }));\n__exportStar(__webpack_require__(/*! ./eth/constants.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js":
/*!*************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Safe = void 0;\nconst viem_1 = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_cjs/index.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./signatures.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js\");\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst constants_js_1 = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/constants.js\");\nconst index_js_1 = __webpack_require__(/*! ../types/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\");\nconst requirePermissions_js_1 = __importDefault(__webpack_require__(/*! ../decorators/requirePermissions.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/decorators/requirePermissions.js\"));\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(methods_js_1.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0, viem_1.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: constants_js_1.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === signatures_js_1.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0, viem_1.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: constants_js_1.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === signatures_js_1.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0, viem_1.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0, viem_1.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(methods_js_1.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0, index_js_1.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(methods_js_1.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(methods_js_1.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\nexports.Safe = Safe;\n__decorate([\n    (0, requirePermissions_js_1.default)()\n], Safe.prototype, \"requestAddressBook\", null);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MAGIC_VALUE_BYTES = exports.MAGIC_VALUE = void 0;\nconst MAGIC_VALUE = '0x1626ba7e';\nexports.MAGIC_VALUE = MAGIC_VALUE;\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\nexports.MAGIC_VALUE_BYTES = MAGIC_VALUE_BYTES;\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3NhZmUvc2lnbmF0dXJlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx5QkFBeUIsR0FBRyxtQkFBbUI7QUFDL0M7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQSx5QkFBeUI7QUFDekIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvc2FmZS9zaWduYXR1cmVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5NQUdJQ19WQUxVRV9CWVRFUyA9IGV4cG9ydHMuTUFHSUNfVkFMVUUgPSB2b2lkIDA7XG5jb25zdCBNQUdJQ19WQUxVRSA9ICcweDE2MjZiYTdlJztcbmV4cG9ydHMuTUFHSUNfVkFMVUUgPSBNQUdJQ19WQUxVRTtcbmNvbnN0IE1BR0lDX1ZBTFVFX0JZVEVTID0gJzB4MjBjMTNiMGInO1xuZXhwb3J0cy5NQUdJQ19WQUxVRV9CWVRFUyA9IE1BR0lDX1ZBTFVFX0JZVEVTO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2lnbmF0dXJlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/signatures.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js":
/*!******************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst index_js_1 = __importDefault(__webpack_require__(/*! ./communication/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/index.js\"));\nconst index_js_2 = __webpack_require__(/*! ./txs/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js\");\nconst index_js_3 = __webpack_require__(/*! ./eth/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/eth/index.js\");\nconst index_js_4 = __webpack_require__(/*! ./safe/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/safe/index.js\");\nconst index_js_5 = __webpack_require__(/*! ./wallet/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\");\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new index_js_1.default(allowedDomains, debug);\n        this.eth = new index_js_3.Eth(this.communicator);\n        this.txs = new index_js_2.TXs(this.communicator);\n        this.safe = new index_js_4.Safe(this.communicator);\n        this.wallet = new index_js_5.Wallet(this.communicator);\n    }\n}\nexports[\"default\"] = SafeAppsSDK;\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/sdk.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TXs = void 0;\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst index_js_1 = __webpack_require__(/*! ../types/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\");\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(methods_js_1.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(methods_js_1.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0, index_js_1.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(methods_js_1.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(methods_js_1.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\nexports.TXs = TXs;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/txs/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js":
/*!****************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TransferDirection = exports.TransactionStatus = exports.TokenType = exports.Operation = void 0;\nvar safe_gateway_typescript_sdk_1 = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\nObject.defineProperty(exports, \"Operation\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.Operation; } }));\nObject.defineProperty(exports, \"TokenType\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TokenType; } }));\nObject.defineProperty(exports, \"TransactionStatus\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TransactionStatus; } }));\nObject.defineProperty(exports, \"TransferDirection\", ({ enumerable: true, get: function () { return safe_gateway_typescript_sdk_1.TransferDirection; } }));\n//# sourceMappingURL=gateway.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3R5cGVzL2dhdGV3YXkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QseUJBQXlCLEdBQUcseUJBQXlCLEdBQUcsaUJBQWlCLEdBQUcsaUJBQWlCO0FBQzdGLG9DQUFvQyxtQkFBTyxDQUFDLDhIQUEwQztBQUN0Riw2Q0FBNEMsRUFBRSxxQ0FBcUMsbURBQW1ELEVBQUM7QUFDdkksNkNBQTRDLEVBQUUscUNBQXFDLG1EQUFtRCxFQUFDO0FBQ3ZJLHFEQUFvRCxFQUFFLHFDQUFxQywyREFBMkQsRUFBQztBQUN2SixxREFBb0QsRUFBRSxxQ0FBcUMsMkRBQTJELEVBQUM7QUFDdkoiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvZ2F0ZXdheS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuVHJhbnNmZXJEaXJlY3Rpb24gPSBleHBvcnRzLlRyYW5zYWN0aW9uU3RhdHVzID0gZXhwb3J0cy5Ub2tlblR5cGUgPSBleHBvcnRzLk9wZXJhdGlvbiA9IHZvaWQgMDtcbnZhciBzYWZlX2dhdGV3YXlfdHlwZXNjcmlwdF9zZGtfMSA9IHJlcXVpcmUoXCJAc2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiT3BlcmF0aW9uXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBzYWZlX2dhdGV3YXlfdHlwZXNjcmlwdF9zZGtfMS5PcGVyYXRpb247IH0gfSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJUb2tlblR5cGVcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHNhZmVfZ2F0ZXdheV90eXBlc2NyaXB0X3Nka18xLlRva2VuVHlwZTsgfSB9KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlRyYW5zYWN0aW9uU3RhdHVzXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBzYWZlX2dhdGV3YXlfdHlwZXNjcmlwdF9zZGtfMS5UcmFuc2FjdGlvblN0YXR1czsgfSB9KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIlRyYW5zZmVyRGlyZWN0aW9uXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBzYWZlX2dhdGV3YXlfdHlwZXNjcmlwdF9zZGtfMS5UcmFuc2ZlckRpcmVjdGlvbjsgfSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdhdGV3YXkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js":
/*!**************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./sdk.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js\"), exports);\n__exportStar(__webpack_require__(/*! ./rpc.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js\"), exports);\n__exportStar(__webpack_require__(/*! ./gateway.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/gateway.js\"), exports);\n__exportStar(__webpack_require__(/*! ./messaging.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\n//# sourceMappingURL=messaging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3R5cGVzL21lc3NhZ2luZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUIsbUJBQU8sQ0FBQyx1SEFBNkI7QUFDMUQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvbWVzc2FnaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgbWV0aG9kc19qc18xID0gcmVxdWlyZShcIi4uL2NvbW11bmljYXRpb24vbWV0aG9kcy5qc1wiKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1lc3NhZ2luZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/messaging.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js":
/*!********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PermissionsError = exports.PERMISSIONS_REQUEST_REJECTED = void 0;\nexports.PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\nexports.PermissionsError = PermissionsError;\n//# sourceMappingURL=permissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3R5cGVzL3Blcm1pc3Npb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHdCQUF3QixHQUFHLG9DQUFvQztBQUMvRCxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9janMvdHlwZXMvcGVybWlzc2lvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlBlcm1pc3Npb25zRXJyb3IgPSBleHBvcnRzLlBFUk1JU1NJT05TX1JFUVVFU1RfUkVKRUNURUQgPSB2b2lkIDA7XG5leHBvcnRzLlBFUk1JU1NJT05TX1JFUVVFU1RfUkVKRUNURUQgPSA0MDAxO1xuY2xhc3MgUGVybWlzc2lvbnNFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBjb2RlLCBkYXRhKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLmNvZGUgPSBjb2RlO1xuICAgICAgICB0aGlzLmRhdGEgPSBkYXRhO1xuICAgICAgICAvLyBTaG91bGQgYWRqdXN0IHByb3RvdHlwZSBtYW51YWxseSBiZWNhdXNlIGhvdyBUUyBoYW5kbGVzIHRoZSB0eXBlIGV4dGVuc2lvbiBjb21waWxhdGlvblxuICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vTWljcm9zb2Z0L1R5cGVTY3JpcHQvd2lraS9CcmVha2luZy1DaGFuZ2VzI2V4dGVuZGluZy1idWlsdC1pbnMtbGlrZS1lcnJvci1hcnJheS1hbmQtbWFwLW1heS1uby1sb25nZXItd29ya1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcywgUGVybWlzc2lvbnNFcnJvci5wcm90b3R5cGUpO1xuICAgIH1cbn1cbmV4cG9ydHMuUGVybWlzc2lvbnNFcnJvciA9IFBlcm1pc3Npb25zRXJyb3I7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wZXJtaXNzaW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3R5cGVzL3JwYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2Nqcy90eXBlcy9ycGMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ycGMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/rpc.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectEIP712TypedData = void 0;\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\nexports.isObjectEIP712TypedData = isObjectEIP712TypedData;\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3R5cGVzL3Nkay5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3R5cGVzL3Nkay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuaXNPYmplY3RFSVA3MTJUeXBlZERhdGEgPSB2b2lkIDA7XG5jb25zdCBpc09iamVjdEVJUDcxMlR5cGVkRGF0YSA9IChvYmopID0+IHtcbiAgICByZXR1cm4gdHlwZW9mIG9iaiA9PT0gJ29iamVjdCcgJiYgb2JqICE9IG51bGwgJiYgJ2RvbWFpbicgaW4gb2JqICYmICd0eXBlcycgaW4gb2JqICYmICdtZXNzYWdlJyBpbiBvYmo7XG59O1xuZXhwb3J0cy5pc09iamVjdEVJUDcxMlR5cGVkRGF0YSA9IGlzT2JqZWN0RUlQNzEyVHlwZWREYXRhO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2RrLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/sdk.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js":
/*!**********************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getSDKVersion = void 0;\nconst getSDKVersion = () => '9.1.0';\nexports.getSDKVersion = getSDKVersion;\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QscUJBQXFCO0FBQ3JCO0FBQ0EscUJBQXFCO0FBQ3JCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvY2pzL3ZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmdldFNES1ZlcnNpb24gPSB2b2lkIDA7XG5jb25zdCBnZXRTREtWZXJzaW9uID0gKCkgPT4gJzkuMS4wJztcbmV4cG9ydHMuZ2V0U0RLVmVyc2lvbiA9IGdldFNES1ZlcnNpb247XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/version.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js":
/*!***************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Wallet = void 0;\nconst methods_js_1 = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/communication/methods.js\");\nconst permissions_js_1 = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/types/permissions.js\");\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(methods_js_1.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new permissions_js_1.PermissionsError('Permissions request is invalid', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(methods_js_1.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new permissions_js_1.PermissionsError('Permissions rejected', permissions_js_1.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(methods_js_1.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\nexports.Wallet = Wallet;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/cjs/wallet/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _methods_js__WEBPACK_IMPORTED_MODULE_1__.RestrictedMethods),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messageFormatter.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\nclass PostMessageCommunicator {\n    constructor(allowedOrigins = null, debugMode = false) {\n        this.allowedOrigins = null;\n        this.callbacks = new Map();\n        this.debugMode = false;\n        this.isServer = typeof window === 'undefined';\n        this.isValidMessage = ({ origin, data, source }) => {\n            const emptyOrMalformed = !data;\n            const sentFromParentEl = !this.isServer && source === window.parent;\n            const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n            const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n            let validOrigin = true;\n            if (Array.isArray(this.allowedOrigins)) {\n                validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n            }\n            return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n        };\n        this.logIncomingMessage = (msg) => {\n            console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n        };\n        this.onParentMessage = (msg) => {\n            if (this.isValidMessage(msg)) {\n                this.debugMode && this.logIncomingMessage(msg);\n                this.handleIncomingMessage(msg.data);\n            }\n        };\n        this.handleIncomingMessage = (payload) => {\n            const { id } = payload;\n            const cb = this.callbacks.get(id);\n            if (cb) {\n                cb(payload);\n                this.callbacks.delete(id);\n            }\n        };\n        this.send = (method, params) => {\n            const request = _messageFormatter_js__WEBPACK_IMPORTED_MODULE_0__.MessageFormatter.makeRequest(method, params);\n            if (this.isServer) {\n                throw new Error(\"Window doesn't exist\");\n            }\n            window.parent.postMessage(request, '*');\n            return new Promise((resolve, reject) => {\n                this.callbacks.set(request.id, (response) => {\n                    if (!response.success) {\n                        reject(new Error(response.error));\n                        return;\n                    }\n                    resolve(response);\n                });\n            });\n        };\n        this.allowedOrigins = allowedOrigins;\n        this.debugMode = debugMode;\n        if (!this.isServer) {\n            window.addEventListener('message', this.onParentMessage);\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PostMessageCommunicator);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js":
/*!*********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* binding */ MessageFormatter)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\");\n\n\nclass MessageFormatter {\n}\nMessageFormatter.makeRequest = (method, params) => {\n    const id = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.generateRequestId)();\n    return {\n        id,\n        method,\n        params,\n        env: {\n            sdkVersion: (0,_version_js__WEBPACK_IMPORTED_MODULE_0__.getSDKVersion)(),\n        },\n    };\n};\nMessageFormatter.makeResponse = (id, data, version) => ({\n    id,\n    success: true,\n    version,\n    data,\n});\nMessageFormatter.makeErrorResponse = (id, error, version) => ({\n    id,\n    success: false,\n    error,\n    version,\n});\n\n//# sourceMappingURL=messageFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2NvbW11bmljYXRpb24vbWVzc2FnZUZvcm1hdHRlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFDQztBQUMvQztBQUNBO0FBQ0E7QUFDQSxlQUFlLDREQUFpQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDBEQUFhO0FBQ3JDLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUMyQjtBQUM1QiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS9jb21tdW5pY2F0aW9uL21lc3NhZ2VGb3JtYXR0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U0RLVmVyc2lvbiB9IGZyb20gJy4uL3ZlcnNpb24uanMnO1xuaW1wb3J0IHsgZ2VuZXJhdGVSZXF1ZXN0SWQgfSBmcm9tICcuL3V0aWxzLmpzJztcbmNsYXNzIE1lc3NhZ2VGb3JtYXR0ZXIge1xufVxuTWVzc2FnZUZvcm1hdHRlci5tYWtlUmVxdWVzdCA9IChtZXRob2QsIHBhcmFtcykgPT4ge1xuICAgIGNvbnN0IGlkID0gZ2VuZXJhdGVSZXF1ZXN0SWQoKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBpZCxcbiAgICAgICAgbWV0aG9kLFxuICAgICAgICBwYXJhbXMsXG4gICAgICAgIGVudjoge1xuICAgICAgICAgICAgc2RrVmVyc2lvbjogZ2V0U0RLVmVyc2lvbigpLFxuICAgICAgICB9LFxuICAgIH07XG59O1xuTWVzc2FnZUZvcm1hdHRlci5tYWtlUmVzcG9uc2UgPSAoaWQsIGRhdGEsIHZlcnNpb24pID0+ICh7XG4gICAgaWQsXG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICB2ZXJzaW9uLFxuICAgIGRhdGEsXG59KTtcbk1lc3NhZ2VGb3JtYXR0ZXIubWFrZUVycm9yUmVzcG9uc2UgPSAoaWQsIGVycm9yLCB2ZXJzaW9uKSA9PiAoe1xuICAgIGlkLFxuICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgIGVycm9yLFxuICAgIHZlcnNpb24sXG59KTtcbmV4cG9ydCB7IE1lc3NhZ2VGb3JtYXR0ZXIgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1lc3NhZ2VGb3JtYXR0ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js":
/*!************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Methods: () => (/* binding */ Methods),\n/* harmony export */   RestrictedMethods: () => (/* binding */ RestrictedMethods)\n/* harmony export */ });\nvar Methods;\n(function (Methods) {\n    Methods[\"sendTransactions\"] = \"sendTransactions\";\n    Methods[\"rpcCall\"] = \"rpcCall\";\n    Methods[\"getChainInfo\"] = \"getChainInfo\";\n    Methods[\"getSafeInfo\"] = \"getSafeInfo\";\n    Methods[\"getTxBySafeTxHash\"] = \"getTxBySafeTxHash\";\n    Methods[\"getSafeBalances\"] = \"getSafeBalances\";\n    Methods[\"signMessage\"] = \"signMessage\";\n    Methods[\"signTypedMessage\"] = \"signTypedMessage\";\n    Methods[\"getEnvironmentInfo\"] = \"getEnvironmentInfo\";\n    Methods[\"getOffChainSignature\"] = \"getOffChainSignature\";\n    Methods[\"requestAddressBook\"] = \"requestAddressBook\";\n    Methods[\"wallet_getPermissions\"] = \"wallet_getPermissions\";\n    Methods[\"wallet_requestPermissions\"] = \"wallet_requestPermissions\";\n})(Methods || (Methods = {}));\nvar RestrictedMethods;\n(function (RestrictedMethods) {\n    RestrictedMethods[\"requestAddressBook\"] = \"requestAddressBook\";\n})(RestrictedMethods || (RestrictedMethods = {}));\n//# sourceMappingURL=methods.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2NvbW11bmljYXRpb24vbWV0aG9kcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsMEJBQTBCO0FBQ3BCO0FBQ1A7QUFDQTtBQUNBLENBQUMsOENBQThDO0FBQy9DIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2NvbW11bmljYXRpb24vbWV0aG9kcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIE1ldGhvZHM7XG4oZnVuY3Rpb24gKE1ldGhvZHMpIHtcbiAgICBNZXRob2RzW1wic2VuZFRyYW5zYWN0aW9uc1wiXSA9IFwic2VuZFRyYW5zYWN0aW9uc1wiO1xuICAgIE1ldGhvZHNbXCJycGNDYWxsXCJdID0gXCJycGNDYWxsXCI7XG4gICAgTWV0aG9kc1tcImdldENoYWluSW5mb1wiXSA9IFwiZ2V0Q2hhaW5JbmZvXCI7XG4gICAgTWV0aG9kc1tcImdldFNhZmVJbmZvXCJdID0gXCJnZXRTYWZlSW5mb1wiO1xuICAgIE1ldGhvZHNbXCJnZXRUeEJ5U2FmZVR4SGFzaFwiXSA9IFwiZ2V0VHhCeVNhZmVUeEhhc2hcIjtcbiAgICBNZXRob2RzW1wiZ2V0U2FmZUJhbGFuY2VzXCJdID0gXCJnZXRTYWZlQmFsYW5jZXNcIjtcbiAgICBNZXRob2RzW1wic2lnbk1lc3NhZ2VcIl0gPSBcInNpZ25NZXNzYWdlXCI7XG4gICAgTWV0aG9kc1tcInNpZ25UeXBlZE1lc3NhZ2VcIl0gPSBcInNpZ25UeXBlZE1lc3NhZ2VcIjtcbiAgICBNZXRob2RzW1wiZ2V0RW52aXJvbm1lbnRJbmZvXCJdID0gXCJnZXRFbnZpcm9ubWVudEluZm9cIjtcbiAgICBNZXRob2RzW1wiZ2V0T2ZmQ2hhaW5TaWduYXR1cmVcIl0gPSBcImdldE9mZkNoYWluU2lnbmF0dXJlXCI7XG4gICAgTWV0aG9kc1tcInJlcXVlc3RBZGRyZXNzQm9va1wiXSA9IFwicmVxdWVzdEFkZHJlc3NCb29rXCI7XG4gICAgTWV0aG9kc1tcIndhbGxldF9nZXRQZXJtaXNzaW9uc1wiXSA9IFwid2FsbGV0X2dldFBlcm1pc3Npb25zXCI7XG4gICAgTWV0aG9kc1tcIndhbGxldF9yZXF1ZXN0UGVybWlzc2lvbnNcIl0gPSBcIndhbGxldF9yZXF1ZXN0UGVybWlzc2lvbnNcIjtcbn0pKE1ldGhvZHMgfHwgKE1ldGhvZHMgPSB7fSkpO1xuZXhwb3J0IHZhciBSZXN0cmljdGVkTWV0aG9kcztcbihmdW5jdGlvbiAoUmVzdHJpY3RlZE1ldGhvZHMpIHtcbiAgICBSZXN0cmljdGVkTWV0aG9kc1tcInJlcXVlc3RBZGRyZXNzQm9va1wiXSA9IFwicmVxdWVzdEFkZHJlc3NCb29rXCI7XG59KShSZXN0cmljdGVkTWV0aG9kcyB8fCAoUmVzdHJpY3RlZE1ldGhvZHMgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWV0aG9kcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateRequestId: () => (/* binding */ generateRequestId)\n/* harmony export */ });\n// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec) => dec.toString(16).padStart(2, '0');\nconst generateId = (len) => {\n    const arr = new Uint8Array((len || 40) / 2);\n    window.crypto.getRandomValues(arr);\n    return Array.from(arr, dec2hex).join('');\n};\nconst generateRequestId = () => {\n    if (typeof window !== 'undefined') {\n        return generateId(10);\n    }\n    return new Date().getTime().toString(36);\n};\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2NvbW11bmljYXRpb24vdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzZCO0FBQzdCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2NvbW11bmljYXRpb24vdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gaS5lLiAwLTI1NSAtPiAnMDAnLSdmZidcbmNvbnN0IGRlYzJoZXggPSAoZGVjKSA9PiBkZWMudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyk7XG5jb25zdCBnZW5lcmF0ZUlkID0gKGxlbikgPT4ge1xuICAgIGNvbnN0IGFyciA9IG5ldyBVaW50OEFycmF5KChsZW4gfHwgNDApIC8gMik7XG4gICAgd2luZG93LmNyeXB0by5nZXRSYW5kb21WYWx1ZXMoYXJyKTtcbiAgICByZXR1cm4gQXJyYXkuZnJvbShhcnIsIGRlYzJoZXgpLmpvaW4oJycpO1xufTtcbmNvbnN0IGdlbmVyYXRlUmVxdWVzdElkID0gKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICByZXR1cm4gZ2VuZXJhdGVJZCgxMCk7XG4gICAgfVxuICAgIHJldHVybiBuZXcgRGF0ZSgpLmdldFRpbWUoKS50b1N0cmluZygzNik7XG59O1xuZXhwb3J0IHsgZ2VuZXJhdGVSZXF1ZXN0SWQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../wallet/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nconst hasPermission = (required, permissions) => permissions.some((permission) => permission.parentCapability === required);\nconst requirePermission = () => (_, propertyKey, descriptor) => {\n    const originalMethod = descriptor.value;\n    descriptor.value = async function () {\n        // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n        const wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_0__.Wallet(this.communicator);\n        let currentPermissions = await wallet.getPermissions();\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            currentPermissions = await wallet.requestPermissions([{ [propertyKey]: {} }]);\n        }\n        if (!hasPermission(propertyKey, currentPermissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        return originalMethod.apply(this);\n    };\n    return descriptor;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (requirePermission);\n//# sourceMappingURL=requirePermissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js":
/*!****************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RPC_CALLS: () => (/* binding */ RPC_CALLS)\n/* harmony export */ });\nconst RPC_CALLS = {\n    eth_call: 'eth_call',\n    eth_gasPrice: 'eth_gasPrice',\n    eth_getLogs: 'eth_getLogs',\n    eth_getBalance: 'eth_getBalance',\n    eth_getCode: 'eth_getCode',\n    eth_getBlockByHash: 'eth_getBlockByHash',\n    eth_getBlockByNumber: 'eth_getBlockByNumber',\n    eth_getStorageAt: 'eth_getStorageAt',\n    eth_getTransactionByHash: 'eth_getTransactionByHash',\n    eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n    eth_getTransactionCount: 'eth_getTransactionCount',\n    eth_estimateGas: 'eth_estimateGas',\n    safe_setSettings: 'safe_setSettings',\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2V0aC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2V0aC9jb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFJQQ19DQUxMUyA9IHtcbiAgICBldGhfY2FsbDogJ2V0aF9jYWxsJyxcbiAgICBldGhfZ2FzUHJpY2U6ICdldGhfZ2FzUHJpY2UnLFxuICAgIGV0aF9nZXRMb2dzOiAnZXRoX2dldExvZ3MnLFxuICAgIGV0aF9nZXRCYWxhbmNlOiAnZXRoX2dldEJhbGFuY2UnLFxuICAgIGV0aF9nZXRDb2RlOiAnZXRoX2dldENvZGUnLFxuICAgIGV0aF9nZXRCbG9ja0J5SGFzaDogJ2V0aF9nZXRCbG9ja0J5SGFzaCcsXG4gICAgZXRoX2dldEJsb2NrQnlOdW1iZXI6ICdldGhfZ2V0QmxvY2tCeU51bWJlcicsXG4gICAgZXRoX2dldFN0b3JhZ2VBdDogJ2V0aF9nZXRTdG9yYWdlQXQnLFxuICAgIGV0aF9nZXRUcmFuc2FjdGlvbkJ5SGFzaDogJ2V0aF9nZXRUcmFuc2FjdGlvbkJ5SGFzaCcsXG4gICAgZXRoX2dldFRyYW5zYWN0aW9uUmVjZWlwdDogJ2V0aF9nZXRUcmFuc2FjdGlvblJlY2VpcHQnLFxuICAgIGV0aF9nZXRUcmFuc2FjdGlvbkNvdW50OiAnZXRoX2dldFRyYW5zYWN0aW9uQ291bnQnLFxuICAgIGV0aF9lc3RpbWF0ZUdhczogJ2V0aF9lc3RpbWF0ZUdhcycsXG4gICAgc2FmZV9zZXRTZXR0aW5nczogJ3NhZmVfc2V0U2V0dGluZ3MnLFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eth: () => (/* binding */ Eth)\n/* harmony export */ });\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n\nconst inputFormatters = {\n    defaultBlockParam: (arg = 'latest') => arg,\n    returnFullTxObjectParam: (arg = false) => arg,\n    blockNumberToHex: (arg) => Number.isInteger(arg) ? `0x${arg.toString(16)}` : arg,\n};\nclass Eth {\n    constructor(communicator) {\n        this.communicator = communicator;\n        this.call = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_call,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getBalance = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBalance,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getCode = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getCode,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getStorageAt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getStorageAt,\n            formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n        });\n        this.getPastLogs = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getLogs,\n        });\n        this.getBlockByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByHash,\n            formatters: [null, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getBlockByNumber = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getBlockByNumber,\n            formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n        });\n        this.getTransactionByHash = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionByHash,\n        });\n        this.getTransactionReceipt = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionReceipt,\n        });\n        this.getTransactionCount = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_getTransactionCount,\n            formatters: [null, inputFormatters.defaultBlockParam],\n        });\n        this.getGasPrice = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_gasPrice,\n        });\n        this.getEstimateGas = (transaction) => this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.eth_estimateGas,\n        })([transaction]);\n        this.setSafeSettings = this.buildRequest({\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_0__.RPC_CALLS.safe_setSettings,\n        });\n    }\n    buildRequest(args) {\n        const { call, formatters } = args;\n        return async (params) => {\n            if (formatters && Array.isArray(params)) {\n                formatters.forEach((formatter, i) => {\n                    if (formatter) {\n                        params[i] = formatter(params[i]);\n                    }\n                });\n            }\n            const payload = {\n                call,\n                params: params || [],\n            };\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data;\n        };\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js":
/*!********************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageFormatter: () => (/* reexport safe */ _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__.MessageFormatter),\n/* harmony export */   Methods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.Methods),\n/* harmony export */   Operation: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.Operation),\n/* harmony export */   RPC_CALLS: () => (/* reexport safe */ _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__.RPC_CALLS),\n/* harmony export */   RestrictedMethods: () => (/* reexport safe */ _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__.RestrictedMethods),\n/* harmony export */   TokenType: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.TransferDirection),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSDKVersion: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_4__.getSDKVersion),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _communication_messageFormatter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./communication/messageFormatter.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/messageFormatter.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./eth/constants.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_sdk_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTJCO0FBQzNCLGlFQUFlLCtDQUFHLEVBQUM7QUFDTTtBQUNRO0FBQ1U7QUFDUztBQUNQO0FBQ1Y7QUFDbkMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNESyBmcm9tICcuL3Nkay5qcyc7XG5leHBvcnQgZGVmYXVsdCBTREs7XG5leHBvcnQgKiBmcm9tICcuL3Nkay5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzL2luZGV4LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vY29tbXVuaWNhdGlvbi9tZXRob2RzLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vY29tbXVuaWNhdGlvbi9tZXNzYWdlRm9ybWF0dGVyLmpzJztcbmV4cG9ydCB7IGdldFNES1ZlcnNpb24gfSBmcm9tICcuL3ZlcnNpb24uanMnO1xuZXhwb3J0ICogZnJvbSAnLi9ldGgvY29uc3RhbnRzLmpzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js":
/*!*************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Safe: () => (/* binding */ Safe)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/abi/encodeFunctionData.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/signature/hashMessage.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/signature/hashTypedData.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\");\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../eth/constants.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/constants.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../types/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n/* harmony import */ var _decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../decorators/requirePermissions.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/decorators/requirePermissions.js\");\nvar __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\n\n\n\n\n\nclass Safe {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getChainInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getChainInfo, undefined);\n        return response.data;\n    }\n    async getInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeInfo, undefined);\n        return response.data;\n    }\n    // There is a possibility that this method will change because we may add pagination to the endpoint\n    async experimental_getBalances({ currency = 'usd' } = {}) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getSafeBalances, {\n            currency,\n        });\n        return response.data;\n    }\n    async check1271Signature(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_dataHash',\n                            type: 'bytes32',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    async check1271SignatureBytes(messageHash, signature = '0x') {\n        const safeInfo = await this.getInfo();\n        const encodedIsValidSignatureCall = (0,viem__WEBPACK_IMPORTED_MODULE_5__.encodeFunctionData)({\n            abi: [\n                {\n                    constant: false,\n                    inputs: [\n                        {\n                            name: '_data',\n                            type: 'bytes',\n                        },\n                        {\n                            name: '_signature',\n                            type: 'bytes',\n                        },\n                    ],\n                    name: 'isValidSignature',\n                    outputs: [\n                        {\n                            name: '',\n                            type: 'bytes4',\n                        },\n                    ],\n                    payable: false,\n                    stateMutability: 'nonpayable',\n                    type: 'function',\n                },\n            ],\n            functionName: 'isValidSignature',\n            args: [messageHash, signature],\n        });\n        const payload = {\n            call: _eth_constants_js__WEBPACK_IMPORTED_MODULE_2__.RPC_CALLS.eth_call,\n            params: [\n                {\n                    to: safeInfo.safeAddress,\n                    data: encodedIsValidSignatureCall,\n                },\n                'latest',\n            ],\n        };\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.rpcCall, payload);\n            return response.data.slice(0, 10).toLowerCase() === _signatures_js__WEBPACK_IMPORTED_MODULE_0__.MAGIC_VALUE_BYTES;\n        }\n        catch (err) {\n            return false;\n        }\n    }\n    calculateMessageHash(message) {\n        return (0,viem__WEBPACK_IMPORTED_MODULE_6__.hashMessage)(message);\n    }\n    calculateTypedMessageHash(typedMessage) {\n        const chainId = typeof typedMessage.domain.chainId === 'object'\n            ? typedMessage.domain.chainId.toNumber()\n            : Number(typedMessage.domain.chainId);\n        let primaryType = typedMessage.primaryType;\n        if (!primaryType) {\n            const fields = Object.values(typedMessage.types);\n            // We try to infer primaryType (simplified ether's version)\n            const primaryTypes = Object.keys(typedMessage.types).filter((typeName) => fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)));\n            if (primaryTypes.length === 0 || primaryTypes.length > 1)\n                throw new Error('Please specify primaryType');\n            primaryType = primaryTypes[0];\n        }\n        return (0,viem__WEBPACK_IMPORTED_MODULE_7__.hashTypedData)({\n            message: typedMessage.message,\n            domain: {\n                ...typedMessage.domain,\n                chainId,\n                verifyingContract: typedMessage.domain.verifyingContract,\n                salt: typedMessage.domain.salt,\n            },\n            types: typedMessage.types,\n            primaryType,\n        });\n    }\n    async getOffChainSignature(messageHash) {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getOffChainSignature, messageHash);\n        return response.data;\n    }\n    async isMessageSigned(message, signature = '0x') {\n        let check;\n        if (typeof message === 'string') {\n            check = async () => {\n                const messageHash = this.calculateMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if ((0,_types_index_js__WEBPACK_IMPORTED_MODULE_3__.isObjectEIP712TypedData)(message)) {\n            check = async () => {\n                const messageHash = this.calculateTypedMessageHash(message);\n                const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n                return messageHashSigned;\n            };\n        }\n        if (check) {\n            const isValid = await check();\n            return isValid;\n        }\n        throw new Error('Invalid message type');\n    }\n    async isMessageHashSigned(messageHash, signature = '0x') {\n        const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n        for (const check of checks) {\n            const isValid = await check(messageHash, signature);\n            if (isValid) {\n                return true;\n            }\n        }\n        return false;\n    }\n    async getEnvironmentInfo() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.getEnvironmentInfo, undefined);\n        return response.data;\n    }\n    async requestAddressBook() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_1__.Methods.requestAddressBook, undefined);\n        return response.data;\n    }\n}\n__decorate([\n    (0,_decorators_requirePermissions_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n], Safe.prototype, \"requestAddressBook\", null);\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAGIC_VALUE: () => (/* binding */ MAGIC_VALUE),\n/* harmony export */   MAGIC_VALUE_BYTES: () => (/* binding */ MAGIC_VALUE_BYTES)\n/* harmony export */ });\nconst MAGIC_VALUE = '0x1626ba7e';\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\n\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3NhZmUvc2lnbmF0dXJlcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDMEM7QUFDMUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vc2FmZS9zaWduYXR1cmVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IE1BR0lDX1ZBTFVFID0gJzB4MTYyNmJhN2UnO1xuY29uc3QgTUFHSUNfVkFMVUVfQllURVMgPSAnMHgyMGMxM2IwYic7XG5leHBvcnQgeyBNQUdJQ19WQUxVRSwgTUFHSUNfVkFMVUVfQllURVMgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNpZ25hdHVyZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/signatures.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js":
/*!******************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _communication_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./communication/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/index.js\");\n/* harmony import */ var _txs_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./txs/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\");\n/* harmony import */ var _eth_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./eth/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/eth/index.js\");\n/* harmony import */ var _safe_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./safe/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/safe/index.js\");\n/* harmony import */ var _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./wallet/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\");\n\n\n\n\n\nclass SafeAppsSDK {\n    constructor(opts = {}) {\n        const { allowedDomains = null, debug = false } = opts;\n        this.communicator = new _communication_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](allowedDomains, debug);\n        this.eth = new _eth_index_js__WEBPACK_IMPORTED_MODULE_2__.Eth(this.communicator);\n        this.txs = new _txs_index_js__WEBPACK_IMPORTED_MODULE_1__.TXs(this.communicator);\n        this.safe = new _safe_index_js__WEBPACK_IMPORTED_MODULE_3__.Safe(this.communicator);\n        this.wallet = new _wallet_index_js__WEBPACK_IMPORTED_MODULE_4__.Wallet(this.communicator);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeAppsSDK);\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3Nkay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFDeEI7QUFDQTtBQUNFO0FBQ0k7QUFDM0M7QUFDQSx5QkFBeUI7QUFDekIsZ0JBQWdCLHVDQUF1QztBQUN2RCxnQ0FBZ0MsK0RBQXFCO0FBQ3JELHVCQUF1Qiw4Q0FBRztBQUMxQix1QkFBdUIsOENBQUc7QUFDMUIsd0JBQXdCLGdEQUFJO0FBQzVCLDBCQUEwQixvREFBTTtBQUNoQztBQUNBO0FBQ0EsaUVBQWUsV0FBVyxFQUFDO0FBQzNCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3Nkay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSW50ZXJmYWNlQ29tbXVuaWNhdG9yIGZyb20gJy4vY29tbXVuaWNhdGlvbi9pbmRleC5qcyc7XG5pbXBvcnQgeyBUWHMgfSBmcm9tICcuL3R4cy9pbmRleC5qcyc7XG5pbXBvcnQgeyBFdGggfSBmcm9tICcuL2V0aC9pbmRleC5qcyc7XG5pbXBvcnQgeyBTYWZlIH0gZnJvbSAnLi9zYWZlL2luZGV4LmpzJztcbmltcG9ydCB7IFdhbGxldCB9IGZyb20gJy4vd2FsbGV0L2luZGV4LmpzJztcbmNsYXNzIFNhZmVBcHBzU0RLIHtcbiAgICBjb25zdHJ1Y3RvcihvcHRzID0ge30pIHtcbiAgICAgICAgY29uc3QgeyBhbGxvd2VkRG9tYWlucyA9IG51bGwsIGRlYnVnID0gZmFsc2UgfSA9IG9wdHM7XG4gICAgICAgIHRoaXMuY29tbXVuaWNhdG9yID0gbmV3IEludGVyZmFjZUNvbW11bmljYXRvcihhbGxvd2VkRG9tYWlucywgZGVidWcpO1xuICAgICAgICB0aGlzLmV0aCA9IG5ldyBFdGgodGhpcy5jb21tdW5pY2F0b3IpO1xuICAgICAgICB0aGlzLnR4cyA9IG5ldyBUWHModGhpcy5jb21tdW5pY2F0b3IpO1xuICAgICAgICB0aGlzLnNhZmUgPSBuZXcgU2FmZSh0aGlzLmNvbW11bmljYXRvcik7XG4gICAgICAgIHRoaXMud2FsbGV0ID0gbmV3IFdhbGxldCh0aGlzLmNvbW11bmljYXRvcik7XG4gICAgfVxufVxuZXhwb3J0IGRlZmF1bHQgU2FmZUFwcHNTREs7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZGsuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/sdk.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TXs: () => (/* binding */ TXs)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/index.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\");\n\n\nclass TXs {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getBySafeTxHash(safeTxHash) {\n        if (!safeTxHash) {\n            throw new Error('Invalid safeTxHash');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.getTxBySafeTxHash, { safeTxHash });\n        return response.data;\n    }\n    async signMessage(message) {\n        const messagePayload = {\n            message,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signMessage, messagePayload);\n        return response.data;\n    }\n    async signTypedMessage(typedData) {\n        if (!(0,_types_index_js__WEBPACK_IMPORTED_MODULE_1__.isObjectEIP712TypedData)(typedData)) {\n            throw new Error('Invalid typed data');\n        }\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.signTypedMessage, { typedData });\n        return response.data;\n    }\n    async send({ txs, params }) {\n        if (!txs || !txs.length) {\n            throw new Error('No transactions were passed');\n        }\n        const messagePayload = {\n            txs,\n            params,\n        };\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.sendTransactions, messagePayload);\n        return response.data;\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/txs/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js":
/*!****************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TransferDirection)\n/* harmony export */ });\n/* harmony import */ var _safe_global_safe_gateway_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @safe-global/safe-gateway-typescript-sdk */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\");\n\n//# sourceMappingURL=gateway.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL2dhdGV3YXkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBdUg7QUFDdkgiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvZ2F0ZXdheS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBPcGVyYXRpb24sIFRva2VuVHlwZSwgVHJhbnNhY3Rpb25TdGF0dXMsIFRyYW5zZmVyRGlyZWN0aW9uLCB9IGZyb20gJ0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2F0ZXdheS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js":
/*!**************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Operation: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.Operation),\n/* harmony export */   TokenType: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TokenType),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransactionStatus),\n/* harmony export */   TransferDirection: () => (/* reexport safe */ _gateway_js__WEBPACK_IMPORTED_MODULE_2__.TransferDirection),\n/* harmony export */   isObjectEIP712TypedData: () => (/* reexport safe */ _sdk_js__WEBPACK_IMPORTED_MODULE_0__.isObjectEIP712TypedData)\n/* harmony export */ });\n/* harmony import */ var _sdk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdk.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\");\n/* harmony import */ var _rpc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rpc.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\");\n/* harmony import */ var _gateway_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gateway.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/gateway.js\");\n/* harmony import */ var _messaging_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messaging.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUF5QjtBQUNBO0FBQ0k7QUFDRTtBQUMvQiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL3Nkay5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3JwYy5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2dhdGV3YXkuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9tZXNzYWdpbmcuanMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n\n//# sourceMappingURL=messaging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL21lc3NhZ2luZy5qcyIsIm1hcHBpbmdzIjoiOztBQUFzRDtBQUN0RCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9tZXNzYWdpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTWV0aG9kcyB9IGZyb20gJy4uL2NvbW11bmljYXRpb24vbWV0aG9kcy5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXNzYWdpbmcuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/messaging.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js":
/*!********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS_REQUEST_REJECTED: () => (/* binding */ PERMISSIONS_REQUEST_REJECTED),\n/* harmony export */   PermissionsError: () => (/* binding */ PermissionsError)\n/* harmony export */ });\nconst PERMISSIONS_REQUEST_REJECTED = 4001;\nclass PermissionsError extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.code = code;\n        this.data = data;\n        // Should adjust prototype manually because how TS handles the type extension compilation\n        // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        Object.setPrototypeOf(this, PermissionsError.prototype);\n    }\n}\n//# sourceMappingURL=permissions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL3Blcm1pc3Npb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL3Blcm1pc3Npb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBQRVJNSVNTSU9OU19SRVFVRVNUX1JFSkVDVEVEID0gNDAwMTtcbmV4cG9ydCBjbGFzcyBQZXJtaXNzaW9uc0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIGNvZGUsIGRhdGEpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMuY29kZSA9IGNvZGU7XG4gICAgICAgIHRoaXMuZGF0YSA9IGRhdGE7XG4gICAgICAgIC8vIFNob3VsZCBhZGp1c3QgcHJvdG90eXBlIG1hbnVhbGx5IGJlY2F1c2UgaG93IFRTIGhhbmRsZXMgdGhlIHR5cGUgZXh0ZW5zaW9uIGNvbXBpbGF0aW9uXG4gICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9NaWNyb3NvZnQvVHlwZVNjcmlwdC93aWtpL0JyZWFraW5nLUNoYW5nZXMjZXh0ZW5kaW5nLWJ1aWx0LWlucy1saWtlLWVycm9yLWFycmF5LWFuZC1tYXAtbWF5LW5vLWxvbmdlci13b3JrXG4gICAgICAgIE9iamVjdC5zZXRQcm90b3R5cGVPZih0aGlzLCBQZXJtaXNzaW9uc0Vycm9yLnByb3RvdHlwZSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGVybWlzc2lvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=rpc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL3JwYy5qcyIsIm1hcHBpbmdzIjoiO0FBQVU7QUFDViIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1hcHBzLXNkay9kaXN0L2VzbS90eXBlcy9ycGMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cnBjLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/rpc.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js":
/*!************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectEIP712TypedData: () => (/* binding */ isObjectEIP712TypedData)\n/* harmony export */ });\nconst isObjectEIP712TypedData = (obj) => {\n    return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\n//# sourceMappingURL=sdk.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3R5cGVzL3Nkay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdHlwZXMvc2RrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpc09iamVjdEVJUDcxMlR5cGVkRGF0YSA9IChvYmopID0+IHtcbiAgICByZXR1cm4gdHlwZW9mIG9iaiA9PT0gJ29iamVjdCcgJiYgb2JqICE9IG51bGwgJiYgJ2RvbWFpbicgaW4gb2JqICYmICd0eXBlcycgaW4gb2JqICYmICdtZXNzYWdlJyBpbiBvYmo7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2RrLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/sdk.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js":
/*!**********************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSDKVersion: () => (/* binding */ getSDKVersion)\n/* harmony export */ });\nconst getSDKVersion = () => '9.1.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWFwcHMtc2RrL2Rpc3QvZXNtL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtYXBwcy1zZGsvZGlzdC9lc20vdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZ2V0U0RLVmVyc2lvbiA9ICgpID0+ICc5LjEuMCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/version.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js":
/*!***************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Wallet: () => (/* binding */ Wallet)\n/* harmony export */ });\n/* harmony import */ var _communication_methods_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../communication/methods.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/communication/methods.js\");\n/* harmony import */ var _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types/permissions.js */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/types/permissions.js\");\n\n\nclass Wallet {\n    constructor(communicator) {\n        this.communicator = communicator;\n    }\n    async getPermissions() {\n        const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_getPermissions, undefined);\n        return response.data;\n    }\n    async requestPermissions(permissions) {\n        if (!this.isPermissionRequestValid(permissions)) {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions request is invalid', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n        try {\n            const response = await this.communicator.send(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.Methods.wallet_requestPermissions, permissions);\n            return response.data;\n        }\n        catch {\n            throw new _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PermissionsError('Permissions rejected', _types_permissions_js__WEBPACK_IMPORTED_MODULE_1__.PERMISSIONS_REQUEST_REJECTED);\n        }\n    }\n    isPermissionRequestValid(permissions) {\n        return permissions.every((pr) => {\n            if (typeof pr === 'object') {\n                return Object.keys(pr).every((method) => {\n                    if (Object.values(_communication_methods_js__WEBPACK_IMPORTED_MODULE_0__.RestrictedMethods).includes(method)) {\n                        return true;\n                    }\n                    return false;\n                });\n            }\n            return false;\n        });\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/wallet/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_BASE_URL = void 0;\nexports.DEFAULT_BASE_URL = 'https://safe-client.safe.global';\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC9jb25maWcuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsd0JBQXdCO0FBQ3hCLHdCQUF3QjtBQUN4QiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5ERUZBVUxUX0JBU0VfVVJMID0gdm9pZCAwO1xuZXhwb3J0cy5ERUZBVUxUX0JBU0VfVVJMID0gJ2h0dHBzOi8vc2FmZS1jbGllbnQuc2FmZS5nbG9iYWwnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uZmlnLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.postEndpoint = postEndpoint;\nexports.putEndpoint = putEndpoint;\nexports.deleteEndpoint = deleteEndpoint;\nexports.getEndpoint = getEndpoint;\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\");\nfunction makeUrl(baseUrl, path, pathParams, query) {\n    const pathname = (0, utils_1.insertParams)(path, pathParams);\n    const search = (0, utils_1.stringifyQuery)(query);\n    return `${baseUrl}${pathname}${search}`;\n}\nfunction postEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'POST', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction putEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'PUT', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction deleteEndpoint(baseUrl, path, params) {\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.fetchData)(url, 'DELETE', params === null || params === void 0 ? void 0 : params.body, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\nfunction getEndpoint(baseUrl, path, params, rawUrl) {\n    if (rawUrl) {\n        return (0, utils_1.getData)(rawUrl, undefined, params === null || params === void 0 ? void 0 : params.credentials);\n    }\n    const url = makeUrl(baseUrl, path, params === null || params === void 0 ? void 0 : params.path, params === null || params === void 0 ? void 0 : params.query);\n    return (0, utils_1.getData)(url, params === null || params === void 0 ? void 0 : params.headers, params === null || params === void 0 ? void 0 : params.credentials);\n}\n//# sourceMappingURL=endpoint.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setBaseUrl = void 0;\nexports.relayTransaction = relayTransaction;\nexports.getRelayCount = getRelayCount;\nexports.getSafeInfo = getSafeInfo;\nexports.getIncomingTransfers = getIncomingTransfers;\nexports.getModuleTransactions = getModuleTransactions;\nexports.getMultisigTransactions = getMultisigTransactions;\nexports.getBalances = getBalances;\nexports.getFiatCurrencies = getFiatCurrencies;\nexports.getOwnedSafes = getOwnedSafes;\nexports.getAllOwnedSafes = getAllOwnedSafes;\nexports.getCollectibles = getCollectibles;\nexports.getCollectiblesPage = getCollectiblesPage;\nexports.getTransactionHistory = getTransactionHistory;\nexports.getTransactionQueue = getTransactionQueue;\nexports.getTransactionDetails = getTransactionDetails;\nexports.deleteTransaction = deleteTransaction;\nexports.postSafeGasEstimation = postSafeGasEstimation;\nexports.getNonces = getNonces;\nexports.proposeTransaction = proposeTransaction;\nexports.getConfirmationView = getConfirmationView;\nexports.getTxPreview = getTxPreview;\nexports.getChainsConfig = getChainsConfig;\nexports.getChainConfig = getChainConfig;\nexports.getSafeApps = getSafeApps;\nexports.getMasterCopies = getMasterCopies;\nexports.getDecodedData = getDecodedData;\nexports.getSafeMessages = getSafeMessages;\nexports.getSafeMessage = getSafeMessage;\nexports.proposeSafeMessage = proposeSafeMessage;\nexports.confirmSafeMessage = confirmSafeMessage;\nexports.getDelegates = getDelegates;\nexports.registerDevice = registerDevice;\nexports.unregisterSafe = unregisterSafe;\nexports.unregisterDevice = unregisterDevice;\nexports.registerEmail = registerEmail;\nexports.changeEmail = changeEmail;\nexports.resendEmailVerificationCode = resendEmailVerificationCode;\nexports.verifyEmail = verifyEmail;\nexports.getRegisteredEmail = getRegisteredEmail;\nexports.deleteRegisteredEmail = deleteRegisteredEmail;\nexports.registerRecoveryModule = registerRecoveryModule;\nexports.unsubscribeSingle = unsubscribeSingle;\nexports.unsubscribeAll = unsubscribeAll;\nexports.getSafeOverviews = getSafeOverviews;\nexports.getContract = getContract;\nexports.getAuthNonce = getAuthNonce;\nexports.verifyAuth = verifyAuth;\nexports.createAccount = createAccount;\nexports.getAccount = getAccount;\nexports.deleteAccount = deleteAccount;\nexports.getAccountDataTypes = getAccountDataTypes;\nexports.getAccountDataSettings = getAccountDataSettings;\nexports.putAccountDataSettings = putAccountDataSettings;\nexports.getIndexingStatus = getIndexingStatus;\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/endpoint.js\");\nconst config_1 = __webpack_require__(/*! ./config */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/config.js\");\n__exportStar(__webpack_require__(/*! ./types/safe-info */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-apps */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/transactions */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/chains */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/common */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/master-copies */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/decoded-data */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/safe-messages */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/notifications */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types/relay */ \"(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\"), exports);\n// Can be set externally to a different CGW host\nlet baseUrl = config_1.DEFAULT_BASE_URL;\n/**\n * Set the base CGW URL\n */\nconst setBaseUrl = (url) => {\n    baseUrl = url;\n};\nexports.setBaseUrl = setBaseUrl;\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/**\n * Relay a transaction from a Safe\n */\nfunction relayTransaction(chainId, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/relay', { path: { chainId }, body });\n}\n/**\n * Get the relay limit and number of remaining relays remaining\n */\nfunction getRelayCount(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/relay/{address}', { path: { chainId, address } });\n}\n/**\n * Get basic information about a Safe. E.g. owners, modules, version etc\n */\nfunction getSafeInfo(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}', { path: { chainId, address } });\n}\n/**\n * Get filterable list of incoming transactions\n */\nfunction getIncomingTransfers(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/incoming-transfers/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of module transactions\n */\nfunction getModuleTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/module-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get filterable list of multisig transactions\n */\nfunction getMultisigTransactions(chainId, address, query, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/multisig-transactions/', {\n        path: { chainId, address },\n        query,\n    }, pageUrl);\n}\n/**\n * Get the total balance and all assets stored in a Safe\n */\nfunction getBalances(chainId, address, currency = 'usd', query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/balances/{currency}', {\n        path: { chainId, address, currency },\n        query,\n    });\n}\n/**\n * Get a list of supported fiat currencies (e.g. USD, EUR etc)\n */\nfunction getFiatCurrencies() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/balances/supported-fiat-codes');\n}\n/**\n * Get the addresses of all Safes belonging to an owner\n */\nfunction getOwnedSafes(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/owners/{address}/safes', { path: { chainId, address } });\n}\n/**\n * Get the addresses of all Safes belonging to an owner on all chains\n */\nfunction getAllOwnedSafes(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/owners/{address}/safes', { path: { address } });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectibles(chainId, address, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{address}/collectibles', {\n        path: { chainId, address },\n        query,\n    });\n}\n/**\n * Get NFTs stored in a Safe\n */\nfunction getCollectiblesPage(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{address}/collectibles', { path: { chainId, address }, query }, pageUrl);\n}\n/**\n * Get a list of past Safe transactions\n */\nfunction getTransactionHistory(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/history', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the list of pending transactions\n */\nfunction getTransactionQueue(chainId, address, query = {}, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/transactions/queued', { path: { chainId, safe_address: address }, query }, pageUrl);\n}\n/**\n * Get the details of an individual transaction by its id\n */\nfunction getTransactionDetails(chainId, transactionId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{transactionId}', {\n        path: { chainId, transactionId },\n    });\n}\n/**\n * Delete a transaction by its safeTxHash\n */\nfunction deleteTransaction(chainId, safeTxHash, signature) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safeTxHash}', {\n        path: { chainId, safeTxHash },\n        body: { signature },\n    });\n}\n/**\n * Request a gas estimate & recommmended tx nonce for a created transaction\n */\nfunction postSafeGasEstimation(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v2/chains/{chainId}/safes/{safe_address}/multisig-transactions/estimations', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\nfunction getNonces(chainId, address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/nonces', {\n        path: { chainId, safe_address: address },\n    });\n}\n/**\n * Propose a new transaction for other owners to sign/execute\n */\nfunction proposeTransaction(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/propose', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getConfirmationView(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/views/transaction-confirmation', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Get a tx preview\n */\nfunction getTxPreview(chainId, safeAddress, operation, data, to, value) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/transactions/{safe_address}/preview', {\n        path: { chainId, safe_address: safeAddress },\n        body: { operation, data, to, value },\n    });\n}\n/**\n * Returns all defined chain configs\n */\nfunction getChainsConfig(query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains', {\n        query,\n    });\n}\n/**\n * Returns a chain config\n */\nfunction getChainConfig(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns Safe Apps List\n */\nfunction getSafeApps(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safe-apps', {\n        path: { chainId: chainId },\n        query,\n    });\n}\n/**\n * Returns list of Master Copies\n */\nfunction getMasterCopies(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/master-copies', {\n        path: { chainId: chainId },\n    });\n}\n/**\n * Returns decoded data\n */\nfunction getDecodedData(chainId, operation, encodedData, to) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/data-decoder', {\n        path: { chainId: chainId },\n        body: { operation, data: encodedData, to },\n    });\n}\n/**\n * Returns list of `SafeMessage`s\n */\nfunction getSafeMessages(chainId, address, pageUrl) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', { path: { chainId, safe_address: address }, query: {} }, pageUrl);\n}\n/**\n * Returns a `SafeMessage`\n */\nfunction getSafeMessage(chainId, messageHash) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}', {\n        path: { chainId, message_hash: messageHash },\n    });\n}\n/**\n * Propose a new `SafeMessage` for other owners to sign\n */\nfunction proposeSafeMessage(chainId, address, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/messages', {\n        path: { chainId, safe_address: address },\n        body,\n    });\n}\n/**\n * Add a confirmation to a `SafeMessage`\n */\nfunction confirmSafeMessage(chainId, messageHash, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/messages/{message_hash}/signatures', {\n        path: { chainId, message_hash: messageHash },\n        body,\n    });\n}\n/**\n * Returns a list of delegates\n */\nfunction getDelegates(chainId, query = {}) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v2/chains/{chainId}/delegates', {\n        path: { chainId },\n        query,\n    });\n}\n/**\n * Registers a device/Safe for notifications\n */\nfunction registerDevice(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/register/notifications', {\n        body,\n    });\n}\n/**\n * Unregisters a Safe from notifications\n */\nfunction unregisterSafe(chainId, address, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}/safes/{safe_address}', {\n        path: { chainId, safe_address: address, uuid },\n    });\n}\n/**\n * Unregisters a device from notifications\n */\nfunction unregisterDevice(chainId, uuid) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/notifications/devices/{uuid}', {\n        path: { chainId, uuid },\n    });\n}\n/**\n * Registers a email address for a safe signer.\n *\n * The signer wallet has to sign a message of format: `email-register-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param body Signer address and email address\n * @param headers Signature and Signature timestamp\n * @returns 200 if signature matches the data\n */\nfunction registerEmail(chainId, safeAddress, body, headers) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Changes an already registered email address for a safe signer. The new email address still needs to be verified.\n *\n * The signer wallet has to sign a message of format: `email-edit-{chainId}-{safeAddress}-{emailAddress}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param body New email address\n * @param headers Signature and Signature timestamp\n * @returns 202 if signature matches the data\n */\nfunction changeEmail(chainId, safeAddress, signerAddress, body, headers) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n        headers,\n    });\n}\n/**\n * Resends an email verification code.\n */\nfunction resendEmailVerificationCode(chainId, safeAddress, signerAddress) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify-resend', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body: '',\n    });\n}\n/**\n * Verifies a pending email address registration.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address who signed the email registration\n * @param body Verification code\n */\nfunction verifyEmail(chainId, safeAddress, signerAddress, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}/verify', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        body,\n    });\n}\n/**\n * Gets the registered email address of the signer\n *\n * The signer wallet will have to sign a message of format: `email-retrieval-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress address of the owner of the Safe\n *\n * @returns email address and verified flag\n */\nfunction getRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Delete a registered email address for the signer\n *\n * The signer wallet will have to sign a message of format: `email-delete-{chainId}-{safe}-{signer}-{timestamp}`\n * The signature is valid for 5 minutes.\n *\n * @param chainId\n * @param safeAddress\n * @param signerAddress\n * @param headers\n */\nfunction deleteRegisteredEmail(chainId, safeAddress, signerAddress, headers) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/emails/{signer}', {\n        path: { chainId, safe_address: safeAddress, signer: signerAddress },\n        headers,\n    });\n}\n/**\n * Register a recovery module for receiving alerts\n * @param chainId\n * @param safeAddress\n * @param body - { moduleAddress: string }\n */\nfunction registerRecoveryModule(chainId, safeAddress, body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/chains/{chainId}/safes/{safe_address}/recovery', {\n        path: { chainId, safe_address: safeAddress },\n        body,\n    });\n}\n/**\n * Delete email subscription for a single category\n * @param query\n */\nfunction unsubscribeSingle(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions', { query });\n}\n/**\n * Delete email subscription for all categories\n * @param query\n */\nfunction unsubscribeAll(query) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/subscriptions/all', { query });\n}\n/**\n * Get Safe overviews per address\n */\nfunction getSafeOverviews(safes, query) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/safes', {\n        query: Object.assign(Object.assign({}, query), { safes: safes.join(',') }),\n    });\n}\nfunction getContract(chainId, contractAddress) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/contracts/{contractAddress}', {\n        path: {\n            chainId: chainId,\n            contractAddress: contractAddress,\n        },\n    });\n}\nfunction getAuthNonce() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/auth/nonce', { credentials: 'include' });\n}\nfunction verifyAuth(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/auth/verify', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction createAccount(body) {\n    return (0, endpoint_1.postEndpoint)(baseUrl, '/v1/accounts', {\n        body,\n        credentials: 'include',\n    });\n}\nfunction getAccount(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction deleteAccount(address) {\n    return (0, endpoint_1.deleteEndpoint)(baseUrl, '/v1/accounts/{address}', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction getAccountDataTypes() {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/data-types');\n}\nfunction getAccountDataSettings(address) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        credentials: 'include',\n    });\n}\nfunction putAccountDataSettings(address, body) {\n    return (0, endpoint_1.putEndpoint)(baseUrl, '/v1/accounts/{address}/data-settings', {\n        path: { address },\n        body,\n        credentials: 'include',\n    });\n}\nfunction getIndexingStatus(chainId) {\n    return (0, endpoint_1.getEndpoint)(baseUrl, '/v1/chains/{chainId}/about/indexing', {\n        path: { chainId },\n    });\n}\n/* eslint-enable @typescript-eslint/explicit-module-boundary-types */\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FEATURES = exports.GAS_PRICE_TYPE = exports.RPC_AUTHENTICATION = void 0;\nvar RPC_AUTHENTICATION;\n(function (RPC_AUTHENTICATION) {\n    RPC_AUTHENTICATION[\"API_KEY_PATH\"] = \"API_KEY_PATH\";\n    RPC_AUTHENTICATION[\"NO_AUTHENTICATION\"] = \"NO_AUTHENTICATION\";\n    RPC_AUTHENTICATION[\"UNKNOWN\"] = \"UNKNOWN\";\n})(RPC_AUTHENTICATION || (exports.RPC_AUTHENTICATION = RPC_AUTHENTICATION = {}));\nvar GAS_PRICE_TYPE;\n(function (GAS_PRICE_TYPE) {\n    GAS_PRICE_TYPE[\"ORACLE\"] = \"ORACLE\";\n    GAS_PRICE_TYPE[\"FIXED\"] = \"FIXED\";\n    GAS_PRICE_TYPE[\"FIXED_1559\"] = \"FIXED1559\";\n    GAS_PRICE_TYPE[\"UNKNOWN\"] = \"UNKNOWN\";\n})(GAS_PRICE_TYPE || (exports.GAS_PRICE_TYPE = GAS_PRICE_TYPE = {}));\nvar FEATURES;\n(function (FEATURES) {\n    FEATURES[\"ERC721\"] = \"ERC721\";\n    FEATURES[\"SAFE_APPS\"] = \"SAFE_APPS\";\n    FEATURES[\"CONTRACT_INTERACTION\"] = \"CONTRACT_INTERACTION\";\n    FEATURES[\"DOMAIN_LOOKUP\"] = \"DOMAIN_LOOKUP\";\n    FEATURES[\"SPENDING_LIMIT\"] = \"SPENDING_LIMIT\";\n    FEATURES[\"EIP1559\"] = \"EIP1559\";\n    FEATURES[\"SAFE_TX_GAS_OPTIONAL\"] = \"SAFE_TX_GAS_OPTIONAL\";\n    FEATURES[\"TX_SIMULATION\"] = \"TX_SIMULATION\";\n    FEATURES[\"EIP1271\"] = \"EIP1271\";\n})(FEATURES || (exports.FEATURES = FEATURES = {}));\n//# sourceMappingURL=chains.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/chains.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TokenType = void 0;\nvar TokenType;\n(function (TokenType) {\n    TokenType[\"ERC20\"] = \"ERC20\";\n    TokenType[\"ERC721\"] = \"ERC721\";\n    TokenType[\"NATIVE_TOKEN\"] = \"NATIVE_TOKEN\";\n    TokenType[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TokenType || (exports.TokenType = TokenType = {}));\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9jb21tb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0JBQWdCLGlCQUFpQixpQkFBaUI7QUFDbkQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHNhZmUtZ2xvYmFsL3NhZmUtZ2F0ZXdheS10eXBlc2NyaXB0LXNkay9kaXN0L3R5cGVzL2NvbW1vbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuVG9rZW5UeXBlID0gdm9pZCAwO1xudmFyIFRva2VuVHlwZTtcbihmdW5jdGlvbiAoVG9rZW5UeXBlKSB7XG4gICAgVG9rZW5UeXBlW1wiRVJDMjBcIl0gPSBcIkVSQzIwXCI7XG4gICAgVG9rZW5UeXBlW1wiRVJDNzIxXCJdID0gXCJFUkM3MjFcIjtcbiAgICBUb2tlblR5cGVbXCJOQVRJVkVfVE9LRU5cIl0gPSBcIk5BVElWRV9UT0tFTlwiO1xuICAgIFRva2VuVHlwZVtcIlVOS05PV05cIl0gPSBcIlVOS05PV05cIjtcbn0pKFRva2VuVHlwZSB8fCAoZXhwb3J0cy5Ub2tlblR5cGUgPSBUb2tlblR5cGUgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29tbW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/common.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js":
/*!*******************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.NativeStakingStatus = exports.ConfirmationViewTypes = void 0;\nvar ConfirmationViewTypes;\n(function (ConfirmationViewTypes) {\n    ConfirmationViewTypes[\"GENERIC\"] = \"GENERIC\";\n    ConfirmationViewTypes[\"COW_SWAP_ORDER\"] = \"COW_SWAP_ORDER\";\n    ConfirmationViewTypes[\"COW_SWAP_TWAP_ORDER\"] = \"COW_SWAP_TWAP_ORDER\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_DEPOSIT\"] = \"KILN_NATIVE_STAKING_DEPOSIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_VALIDATORS_EXIT\"] = \"KILN_NATIVE_STAKING_VALIDATORS_EXIT\";\n    ConfirmationViewTypes[\"KILN_NATIVE_STAKING_WITHDRAW\"] = \"KILN_NATIVE_STAKING_WITHDRAW\";\n})(ConfirmationViewTypes || (exports.ConfirmationViewTypes = ConfirmationViewTypes = {}));\nvar NativeStakingStatus;\n(function (NativeStakingStatus) {\n    NativeStakingStatus[\"NOT_STAKED\"] = \"NOT_STAKED\";\n    NativeStakingStatus[\"ACTIVATING\"] = \"ACTIVATING\";\n    NativeStakingStatus[\"DEPOSIT_IN_PROGRESS\"] = \"DEPOSIT_IN_PROGRESS\";\n    NativeStakingStatus[\"ACTIVE\"] = \"ACTIVE\";\n    NativeStakingStatus[\"EXIT_REQUESTED\"] = \"EXIT_REQUESTED\";\n    NativeStakingStatus[\"EXITING\"] = \"EXITING\";\n    NativeStakingStatus[\"EXITED\"] = \"EXITED\";\n    NativeStakingStatus[\"SLASHED\"] = \"SLASHED\";\n})(NativeStakingStatus || (exports.NativeStakingStatus = NativeStakingStatus = {}));\n//# sourceMappingURL=decoded-data.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/decoded-data.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=master-copies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9tYXN0ZXItY29waWVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9tYXN0ZXItY29waWVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWFzdGVyLWNvcGllcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/master-copies.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeviceType = void 0;\nvar DeviceType;\n(function (DeviceType) {\n    DeviceType[\"ANDROID\"] = \"ANDROID\";\n    DeviceType[\"IOS\"] = \"IOS\";\n    DeviceType[\"WEB\"] = \"WEB\";\n})(DeviceType || (exports.DeviceType = DeviceType = {}));\n//# sourceMappingURL=notifications.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9ub3RpZmljYXRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxpQkFBaUIsa0JBQWtCLGtCQUFrQjtBQUN0RCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvbm90aWZpY2F0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuRGV2aWNlVHlwZSA9IHZvaWQgMDtcbnZhciBEZXZpY2VUeXBlO1xuKGZ1bmN0aW9uIChEZXZpY2VUeXBlKSB7XG4gICAgRGV2aWNlVHlwZVtcIkFORFJPSURcIl0gPSBcIkFORFJPSURcIjtcbiAgICBEZXZpY2VUeXBlW1wiSU9TXCJdID0gXCJJT1NcIjtcbiAgICBEZXZpY2VUeXBlW1wiV0VCXCJdID0gXCJXRUJcIjtcbn0pKERldmljZVR5cGUgfHwgKGV4cG9ydHMuRGV2aWNlVHlwZSA9IERldmljZVR5cGUgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bm90aWZpY2F0aW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/notifications.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js":
/*!************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=relay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9yZWxheS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ac2FmZS1nbG9iYWwvc2FmZS1nYXRld2F5LXR5cGVzY3JpcHQtc2RrL2Rpc3QvdHlwZXMvcmVsYXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWxheS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/relay.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeAppSocialPlatforms = exports.SafeAppFeatures = exports.SafeAppAccessPolicyTypes = void 0;\nvar SafeAppAccessPolicyTypes;\n(function (SafeAppAccessPolicyTypes) {\n    SafeAppAccessPolicyTypes[\"NoRestrictions\"] = \"NO_RESTRICTIONS\";\n    SafeAppAccessPolicyTypes[\"DomainAllowlist\"] = \"DOMAIN_ALLOWLIST\";\n})(SafeAppAccessPolicyTypes || (exports.SafeAppAccessPolicyTypes = SafeAppAccessPolicyTypes = {}));\nvar SafeAppFeatures;\n(function (SafeAppFeatures) {\n    SafeAppFeatures[\"BATCHED_TRANSACTIONS\"] = \"BATCHED_TRANSACTIONS\";\n})(SafeAppFeatures || (exports.SafeAppFeatures = SafeAppFeatures = {}));\nvar SafeAppSocialPlatforms;\n(function (SafeAppSocialPlatforms) {\n    SafeAppSocialPlatforms[\"TWITTER\"] = \"TWITTER\";\n    SafeAppSocialPlatforms[\"GITHUB\"] = \"GITHUB\";\n    SafeAppSocialPlatforms[\"DISCORD\"] = \"DISCORD\";\n    SafeAppSocialPlatforms[\"TELEGRAM\"] = \"TELEGRAM\";\n})(SafeAppSocialPlatforms || (exports.SafeAppSocialPlatforms = SafeAppSocialPlatforms = {}));\n//# sourceMappingURL=safe-apps.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-apps.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ImplementationVersionState = void 0;\nvar ImplementationVersionState;\n(function (ImplementationVersionState) {\n    ImplementationVersionState[\"UP_TO_DATE\"] = \"UP_TO_DATE\";\n    ImplementationVersionState[\"OUTDATED\"] = \"OUTDATED\";\n    ImplementationVersionState[\"UNKNOWN\"] = \"UNKNOWN\";\n})(ImplementationVersionState || (exports.ImplementationVersionState = ImplementationVersionState = {}));\n//# sourceMappingURL=safe-info.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9zYWZlLWluZm8uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLGlDQUFpQyxrQ0FBa0Msa0NBQWtDO0FBQ3RHIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9zYWZlLWluZm8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlID0gdm9pZCAwO1xudmFyIEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlO1xuKGZ1bmN0aW9uIChJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZSkge1xuICAgIEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlW1wiVVBfVE9fREFURVwiXSA9IFwiVVBfVE9fREFURVwiO1xuICAgIEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlW1wiT1VUREFURURcIl0gPSBcIk9VVERBVEVEXCI7XG4gICAgSW1wbGVtZW50YXRpb25WZXJzaW9uU3RhdGVbXCJVTktOT1dOXCJdID0gXCJVTktOT1dOXCI7XG59KShJbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZSB8fCAoZXhwb3J0cy5JbXBsZW1lbnRhdGlvblZlcnNpb25TdGF0ZSA9IEltcGxlbWVudGF0aW9uVmVyc2lvblN0YXRlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNhZmUtaW5mby5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-info.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SafeMessageStatus = exports.SafeMessageListItemType = void 0;\nvar SafeMessageListItemType;\n(function (SafeMessageListItemType) {\n    SafeMessageListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n    SafeMessageListItemType[\"MESSAGE\"] = \"MESSAGE\";\n})(SafeMessageListItemType || (exports.SafeMessageListItemType = SafeMessageListItemType = {}));\nvar SafeMessageStatus;\n(function (SafeMessageStatus) {\n    SafeMessageStatus[\"NEEDS_CONFIRMATION\"] = \"NEEDS_CONFIRMATION\";\n    SafeMessageStatus[\"CONFIRMED\"] = \"CONFIRMED\";\n})(SafeMessageStatus || (exports.SafeMessageStatus = SafeMessageStatus = {}));\n//# sourceMappingURL=safe-messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9zYWZlLW1lc3NhZ2VzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHlCQUF5QixHQUFHLCtCQUErQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsOEJBQThCLCtCQUErQiwrQkFBK0I7QUFDN0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHdCQUF3Qix5QkFBeUIseUJBQXlCO0FBQzNFIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0BzYWZlLWdsb2JhbC9zYWZlLWdhdGV3YXktdHlwZXNjcmlwdC1zZGsvZGlzdC90eXBlcy9zYWZlLW1lc3NhZ2VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5TYWZlTWVzc2FnZVN0YXR1cyA9IGV4cG9ydHMuU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGUgPSB2b2lkIDA7XG52YXIgU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGU7XG4oZnVuY3Rpb24gKFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlKSB7XG4gICAgU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGVbXCJEQVRFX0xBQkVMXCJdID0gXCJEQVRFX0xBQkVMXCI7XG4gICAgU2FmZU1lc3NhZ2VMaXN0SXRlbVR5cGVbXCJNRVNTQUdFXCJdID0gXCJNRVNTQUdFXCI7XG59KShTYWZlTWVzc2FnZUxpc3RJdGVtVHlwZSB8fCAoZXhwb3J0cy5TYWZlTWVzc2FnZUxpc3RJdGVtVHlwZSA9IFNhZmVNZXNzYWdlTGlzdEl0ZW1UeXBlID0ge30pKTtcbnZhciBTYWZlTWVzc2FnZVN0YXR1cztcbihmdW5jdGlvbiAoU2FmZU1lc3NhZ2VTdGF0dXMpIHtcbiAgICBTYWZlTWVzc2FnZVN0YXR1c1tcIk5FRURTX0NPTkZJUk1BVElPTlwiXSA9IFwiTkVFRFNfQ09ORklSTUFUSU9OXCI7XG4gICAgU2FmZU1lc3NhZ2VTdGF0dXNbXCJDT05GSVJNRURcIl0gPSBcIkNPTkZJUk1FRFwiO1xufSkoU2FmZU1lc3NhZ2VTdGF0dXMgfHwgKGV4cG9ydHMuU2FmZU1lc3NhZ2VTdGF0dXMgPSBTYWZlTWVzc2FnZVN0YXR1cyA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zYWZlLW1lc3NhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/safe-messages.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js":
/*!*******************************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LabelValue = exports.StartTimeValue = exports.DurationType = exports.DetailedExecutionInfoType = exports.TransactionListItemType = exports.ConflictType = exports.TransactionInfoType = exports.SettingsInfoType = exports.TransactionTokenType = exports.TransferDirection = exports.TransactionStatus = exports.Operation = void 0;\nvar Operation;\n(function (Operation) {\n    Operation[Operation[\"CALL\"] = 0] = \"CALL\";\n    Operation[Operation[\"DELEGATE\"] = 1] = \"DELEGATE\";\n})(Operation || (exports.Operation = Operation = {}));\nvar TransactionStatus;\n(function (TransactionStatus) {\n    TransactionStatus[\"AWAITING_CONFIRMATIONS\"] = \"AWAITING_CONFIRMATIONS\";\n    TransactionStatus[\"AWAITING_EXECUTION\"] = \"AWAITING_EXECUTION\";\n    TransactionStatus[\"CANCELLED\"] = \"CANCELLED\";\n    TransactionStatus[\"FAILED\"] = \"FAILED\";\n    TransactionStatus[\"SUCCESS\"] = \"SUCCESS\";\n})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));\nvar TransferDirection;\n(function (TransferDirection) {\n    TransferDirection[\"INCOMING\"] = \"INCOMING\";\n    TransferDirection[\"OUTGOING\"] = \"OUTGOING\";\n    TransferDirection[\"UNKNOWN\"] = \"UNKNOWN\";\n})(TransferDirection || (exports.TransferDirection = TransferDirection = {}));\nvar TransactionTokenType;\n(function (TransactionTokenType) {\n    TransactionTokenType[\"ERC20\"] = \"ERC20\";\n    TransactionTokenType[\"ERC721\"] = \"ERC721\";\n    TransactionTokenType[\"NATIVE_COIN\"] = \"NATIVE_COIN\";\n})(TransactionTokenType || (exports.TransactionTokenType = TransactionTokenType = {}));\nvar SettingsInfoType;\n(function (SettingsInfoType) {\n    SettingsInfoType[\"SET_FALLBACK_HANDLER\"] = \"SET_FALLBACK_HANDLER\";\n    SettingsInfoType[\"ADD_OWNER\"] = \"ADD_OWNER\";\n    SettingsInfoType[\"REMOVE_OWNER\"] = \"REMOVE_OWNER\";\n    SettingsInfoType[\"SWAP_OWNER\"] = \"SWAP_OWNER\";\n    SettingsInfoType[\"CHANGE_THRESHOLD\"] = \"CHANGE_THRESHOLD\";\n    SettingsInfoType[\"CHANGE_IMPLEMENTATION\"] = \"CHANGE_IMPLEMENTATION\";\n    SettingsInfoType[\"ENABLE_MODULE\"] = \"ENABLE_MODULE\";\n    SettingsInfoType[\"DISABLE_MODULE\"] = \"DISABLE_MODULE\";\n    SettingsInfoType[\"SET_GUARD\"] = \"SET_GUARD\";\n    SettingsInfoType[\"DELETE_GUARD\"] = \"DELETE_GUARD\";\n})(SettingsInfoType || (exports.SettingsInfoType = SettingsInfoType = {}));\nvar TransactionInfoType;\n(function (TransactionInfoType) {\n    TransactionInfoType[\"TRANSFER\"] = \"Transfer\";\n    TransactionInfoType[\"SETTINGS_CHANGE\"] = \"SettingsChange\";\n    TransactionInfoType[\"CUSTOM\"] = \"Custom\";\n    TransactionInfoType[\"CREATION\"] = \"Creation\";\n    TransactionInfoType[\"SWAP_ORDER\"] = \"SwapOrder\";\n    TransactionInfoType[\"TWAP_ORDER\"] = \"TwapOrder\";\n    TransactionInfoType[\"SWAP_TRANSFER\"] = \"SwapTransfer\";\n    TransactionInfoType[\"NATIVE_STAKING_DEPOSIT\"] = \"NativeStakingDeposit\";\n    TransactionInfoType[\"NATIVE_STAKING_VALIDATORS_EXIT\"] = \"NativeStakingValidatorsExit\";\n    TransactionInfoType[\"NATIVE_STAKING_WITHDRAW\"] = \"NativeStakingWithdraw\";\n})(TransactionInfoType || (exports.TransactionInfoType = TransactionInfoType = {}));\nvar ConflictType;\n(function (ConflictType) {\n    ConflictType[\"NONE\"] = \"None\";\n    ConflictType[\"HAS_NEXT\"] = \"HasNext\";\n    ConflictType[\"END\"] = \"End\";\n})(ConflictType || (exports.ConflictType = ConflictType = {}));\nvar TransactionListItemType;\n(function (TransactionListItemType) {\n    TransactionListItemType[\"TRANSACTION\"] = \"TRANSACTION\";\n    TransactionListItemType[\"LABEL\"] = \"LABEL\";\n    TransactionListItemType[\"CONFLICT_HEADER\"] = \"CONFLICT_HEADER\";\n    TransactionListItemType[\"DATE_LABEL\"] = \"DATE_LABEL\";\n})(TransactionListItemType || (exports.TransactionListItemType = TransactionListItemType = {}));\nvar DetailedExecutionInfoType;\n(function (DetailedExecutionInfoType) {\n    DetailedExecutionInfoType[\"MULTISIG\"] = \"MULTISIG\";\n    DetailedExecutionInfoType[\"MODULE\"] = \"MODULE\";\n})(DetailedExecutionInfoType || (exports.DetailedExecutionInfoType = DetailedExecutionInfoType = {}));\nvar DurationType;\n(function (DurationType) {\n    DurationType[\"AUTO\"] = \"AUTO\";\n    DurationType[\"LIMIT_DURATION\"] = \"LIMIT_DURATION\";\n})(DurationType || (exports.DurationType = DurationType = {}));\nvar StartTimeValue;\n(function (StartTimeValue) {\n    StartTimeValue[\"AT_MINING_TIME\"] = \"AT_MINING_TIME\";\n    StartTimeValue[\"AT_EPOCH\"] = \"AT_EPOCH\";\n})(StartTimeValue || (exports.StartTimeValue = StartTimeValue = {}));\nvar LabelValue;\n(function (LabelValue) {\n    LabelValue[\"Queued\"] = \"Queued\";\n    LabelValue[\"Next\"] = \"Next\";\n})(LabelValue || (exports.LabelValue = LabelValue = {}));\n//# sourceMappingURL=transactions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/types/transactions.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js":
/*!******************************************************************************!*\
  !*** ../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.insertParams = insertParams;\nexports.stringifyQuery = stringifyQuery;\nexports.fetchData = fetchData;\nexports.getData = getData;\nconst isErrorResponse = (data) => {\n    const isObject = typeof data === 'object' && data !== null;\n    return isObject && ('code' in data || 'statusCode' in data) && 'message' in data;\n};\nfunction replaceParam(str, key, value) {\n    return str.replace(new RegExp(`\\\\{${key}\\\\}`, 'g'), value);\n}\nfunction insertParams(template, params) {\n    return params\n        ? Object.keys(params).reduce((result, key) => {\n            return replaceParam(result, key, String(params[key]));\n        }, template)\n        : template;\n}\nfunction stringifyQuery(query) {\n    if (!query) {\n        return '';\n    }\n    const searchParams = new URLSearchParams();\n    Object.keys(query).forEach((key) => {\n        if (query[key] != null) {\n            searchParams.append(key, String(query[key]));\n        }\n    });\n    const searchString = searchParams.toString();\n    return searchString ? `?${searchString}` : '';\n}\nfunction parseResponse(resp) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        let json;\n        try {\n            json = yield resp.json();\n        }\n        catch (_b) {\n            json = {};\n        }\n        if (!resp.ok) {\n            const errTxt = isErrorResponse(json)\n                ? `CGW error - ${(_a = json.code) !== null && _a !== void 0 ? _a : json.statusCode}: ${json.message}`\n                : `CGW error - status ${resp.statusText}`;\n            throw new Error(errTxt);\n        }\n        return json;\n    });\n}\nfunction fetchData(url, method, body, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const requestHeaders = Object.assign({ 'Content-Type': 'application/json' }, headers);\n        const options = {\n            method: method !== null && method !== void 0 ? method : 'POST',\n            headers: requestHeaders,\n        };\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        if (body != null) {\n            options.body = typeof body === 'string' ? body : JSON.stringify(body);\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\nfunction getData(url, headers, credentials) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const options = {\n            method: 'GET',\n        };\n        if (headers) {\n            options['headers'] = Object.assign(Object.assign({}, headers), { 'Content-Type': 'application/json' });\n        }\n        if (credentials) {\n            options['credentials'] = credentials;\n        }\n        const resp = yield fetch(url, options);\n        return parseResponse(resp);\n    });\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@safe-global/safe-gateway-typescript-sdk/dist/utils.js\n");

/***/ })

};
;