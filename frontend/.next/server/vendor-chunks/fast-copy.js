"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-copy";
exports.ids = ["vendor-chunks/fast-copy"];
exports.modules = {

/***/ "(ssr)/../../../node_modules/fast-copy/dist/cjs/index.cjs":
/*!**********************************************************!*\
  !*** ../../../node_modules/fast-copy/dist/cjs/index.cjs ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar toStringFunction = Function.prototype.toString;\nvar create = Object.create;\nvar toStringObject = Object.prototype.toString;\n/**\n * @classdesc Fallback cache for when WeakMap is not natively supported\n */\nvar LegacyCache = /** @class */ (function () {\n    function LegacyCache() {\n        this._keys = [];\n        this._values = [];\n    }\n    LegacyCache.prototype.has = function (key) {\n        return !!~this._keys.indexOf(key);\n    };\n    LegacyCache.prototype.get = function (key) {\n        return this._values[this._keys.indexOf(key)];\n    };\n    LegacyCache.prototype.set = function (key, value) {\n        this._keys.push(key);\n        this._values.push(value);\n    };\n    return LegacyCache;\n}());\nfunction createCacheLegacy() {\n    return new LegacyCache();\n}\nfunction createCacheModern() {\n    return new WeakMap();\n}\n/**\n * Get a new cache object to prevent circular references.\n */\nvar createCache = typeof WeakMap !== 'undefined' ? createCacheModern : createCacheLegacy;\n/**\n * Get an empty version of the object with the same prototype it has.\n */\nfunction getCleanClone(prototype) {\n    if (!prototype) {\n        return create(null);\n    }\n    var Constructor = prototype.constructor;\n    if (Constructor === Object) {\n        return prototype === Object.prototype ? {} : create(prototype);\n    }\n    if (Constructor &&\n        ~toStringFunction.call(Constructor).indexOf('[native code]')) {\n        try {\n            return new Constructor();\n        }\n        catch (_a) { }\n    }\n    return create(prototype);\n}\nfunction getRegExpFlagsLegacy(regExp) {\n    var flags = '';\n    if (regExp.global) {\n        flags += 'g';\n    }\n    if (regExp.ignoreCase) {\n        flags += 'i';\n    }\n    if (regExp.multiline) {\n        flags += 'm';\n    }\n    if (regExp.unicode) {\n        flags += 'u';\n    }\n    if (regExp.sticky) {\n        flags += 'y';\n    }\n    return flags;\n}\nfunction getRegExpFlagsModern(regExp) {\n    return regExp.flags;\n}\n/**\n * Get the flags to apply to the copied regexp.\n */\nvar getRegExpFlags = /test/g.flags === 'g' ? getRegExpFlagsModern : getRegExpFlagsLegacy;\nfunction getTagLegacy(value) {\n    var type = toStringObject.call(value);\n    return type.substring(8, type.length - 1);\n}\nfunction getTagModern(value) {\n    return value[Symbol.toStringTag] || getTagLegacy(value);\n}\n/**\n * Get the tag of the value passed, so that the correct copier can be used.\n */\nvar getTag = typeof Symbol !== 'undefined' ? getTagModern : getTagLegacy;\n\nvar defineProperty = Object.defineProperty, getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, getOwnPropertyNames = Object.getOwnPropertyNames, getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar _a = Object.prototype, hasOwnProperty = _a.hasOwnProperty, propertyIsEnumerable = _a.propertyIsEnumerable;\nvar SUPPORTS_SYMBOL = typeof getOwnPropertySymbols === 'function';\nfunction getStrictPropertiesModern(object) {\n    return getOwnPropertyNames(object).concat(getOwnPropertySymbols(object));\n}\n/**\n * Get the properites used when copying objects strictly. This includes both keys and symbols.\n */\nvar getStrictProperties = SUPPORTS_SYMBOL\n    ? getStrictPropertiesModern\n    : getOwnPropertyNames;\n/**\n * Striclty copy all properties contained on the object.\n */\nfunction copyOwnPropertiesStrict(value, clone, state) {\n    var properties = getStrictProperties(value);\n    for (var index = 0, length_1 = properties.length, property = void 0, descriptor = void 0; index < length_1; ++index) {\n        property = properties[index];\n        if (property === 'callee' || property === 'caller') {\n            continue;\n        }\n        descriptor = getOwnPropertyDescriptor(value, property);\n        if (!descriptor) {\n            // In extra edge cases where the property descriptor cannot be retrived, fall back to\n            // the loose assignment.\n            clone[property] = state.copier(value[property], state);\n            continue;\n        }\n        // Only clone the value if actually a value, not a getter / setter.\n        if (!descriptor.get && !descriptor.set) {\n            descriptor.value = state.copier(descriptor.value, state);\n        }\n        try {\n            defineProperty(clone, property, descriptor);\n        }\n        catch (error) {\n            // Tee above can fail on node in edge cases, so fall back to the loose assignment.\n            clone[property] = descriptor.value;\n        }\n    }\n    return clone;\n}\n/**\n * Deeply copy the indexed values in the array.\n */\nfunction copyArrayLoose(array, state) {\n    var clone = new state.Constructor();\n    // set in the cache immediately to be able to reuse the object recursively\n    state.cache.set(array, clone);\n    for (var index = 0, length_2 = array.length; index < length_2; ++index) {\n        clone[index] = state.copier(array[index], state);\n    }\n    return clone;\n}\n/**\n * Deeply copy the indexed values in the array, as well as any custom properties.\n */\nfunction copyArrayStrict(array, state) {\n    var clone = new state.Constructor();\n    // set in the cache immediately to be able to reuse the object recursively\n    state.cache.set(array, clone);\n    return copyOwnPropertiesStrict(array, clone, state);\n}\n/**\n * Copy the contents of the ArrayBuffer.\n */\nfunction copyArrayBuffer(arrayBuffer, _state) {\n    return arrayBuffer.slice(0);\n}\n/**\n * Create a new Blob with the contents of the original.\n */\nfunction copyBlob(blob, _state) {\n    return blob.slice(0, blob.size, blob.type);\n}\n/**\n * Create a new DataView with the contents of the original.\n */\nfunction copyDataView(dataView, state) {\n    return new state.Constructor(copyArrayBuffer(dataView.buffer));\n}\n/**\n * Create a new Date based on the time of the original.\n */\nfunction copyDate(date, state) {\n    return new state.Constructor(date.getTime());\n}\n/**\n * Deeply copy the keys and values of the original.\n */\nfunction copyMapLoose(map, state) {\n    var clone = new state.Constructor();\n    // set in the cache immediately to be able to reuse the object recursively\n    state.cache.set(map, clone);\n    map.forEach(function (value, key) {\n        clone.set(key, state.copier(value, state));\n    });\n    return clone;\n}\n/**\n * Deeply copy the keys and values of the original, as well as any custom properties.\n */\nfunction copyMapStrict(map, state) {\n    return copyOwnPropertiesStrict(map, copyMapLoose(map, state), state);\n}\nfunction copyObjectLooseLegacy(object, state) {\n    var clone = getCleanClone(state.prototype);\n    // set in the cache immediately to be able to reuse the object recursively\n    state.cache.set(object, clone);\n    for (var key in object) {\n        if (hasOwnProperty.call(object, key)) {\n            clone[key] = state.copier(object[key], state);\n        }\n    }\n    return clone;\n}\nfunction copyObjectLooseModern(object, state) {\n    var clone = getCleanClone(state.prototype);\n    // set in the cache immediately to be able to reuse the object recursively\n    state.cache.set(object, clone);\n    for (var key in object) {\n        if (hasOwnProperty.call(object, key)) {\n            clone[key] = state.copier(object[key], state);\n        }\n    }\n    var symbols = getOwnPropertySymbols(object);\n    for (var index = 0, length_3 = symbols.length, symbol = void 0; index < length_3; ++index) {\n        symbol = symbols[index];\n        if (propertyIsEnumerable.call(object, symbol)) {\n            clone[symbol] = state.copier(object[symbol], state);\n        }\n    }\n    return clone;\n}\n/**\n * Deeply copy the properties (keys and symbols) and values of the original.\n */\nvar copyObjectLoose = SUPPORTS_SYMBOL\n    ? copyObjectLooseModern\n    : copyObjectLooseLegacy;\n/**\n * Deeply copy the properties (keys and symbols) and values of the original, as well\n * as any hidden or non-enumerable properties.\n */\nfunction copyObjectStrict(object, state) {\n    var clone = getCleanClone(state.prototype);\n    // set in the cache immediately to be able to reuse the object recursively\n    state.cache.set(object, clone);\n    return copyOwnPropertiesStrict(object, clone, state);\n}\n/**\n * Create a new primitive wrapper from the value of the original.\n */\nfunction copyPrimitiveWrapper(primitiveObject, state) {\n    return new state.Constructor(primitiveObject.valueOf());\n}\n/**\n * Create a new RegExp based on the value and flags of the original.\n */\nfunction copyRegExp(regExp, state) {\n    var clone = new state.Constructor(regExp.source, getRegExpFlags(regExp));\n    clone.lastIndex = regExp.lastIndex;\n    return clone;\n}\n/**\n * Return the original value (an identity function).\n *\n * @note\n * THis is used for objects that cannot be copied, such as WeakMap.\n */\nfunction copySelf(value, _state) {\n    return value;\n}\n/**\n * Deeply copy the values of the original.\n */\nfunction copySetLoose(set, state) {\n    var clone = new state.Constructor();\n    // set in the cache immediately to be able to reuse the object recursively\n    state.cache.set(set, clone);\n    set.forEach(function (value) {\n        clone.add(state.copier(value, state));\n    });\n    return clone;\n}\n/**\n * Deeply copy the values of the original, as well as any custom properties.\n */\nfunction copySetStrict(set, state) {\n    return copyOwnPropertiesStrict(set, copySetLoose(set, state), state);\n}\n\nvar isArray = Array.isArray;\nvar assign = Object.assign;\nvar getPrototypeOf = Object.getPrototypeOf || (function (obj) { return obj.__proto__; });\nvar DEFAULT_LOOSE_OPTIONS = {\n    array: copyArrayLoose,\n    arrayBuffer: copyArrayBuffer,\n    blob: copyBlob,\n    dataView: copyDataView,\n    date: copyDate,\n    error: copySelf,\n    map: copyMapLoose,\n    object: copyObjectLoose,\n    regExp: copyRegExp,\n    set: copySetLoose,\n};\nvar DEFAULT_STRICT_OPTIONS = assign({}, DEFAULT_LOOSE_OPTIONS, {\n    array: copyArrayStrict,\n    map: copyMapStrict,\n    object: copyObjectStrict,\n    set: copySetStrict,\n});\n/**\n * Get the copiers used for each specific object tag.\n */\nfunction getTagSpecificCopiers(options) {\n    return {\n        Arguments: options.object,\n        Array: options.array,\n        ArrayBuffer: options.arrayBuffer,\n        Blob: options.blob,\n        Boolean: copyPrimitiveWrapper,\n        DataView: options.dataView,\n        Date: options.date,\n        Error: options.error,\n        Float32Array: options.arrayBuffer,\n        Float64Array: options.arrayBuffer,\n        Int8Array: options.arrayBuffer,\n        Int16Array: options.arrayBuffer,\n        Int32Array: options.arrayBuffer,\n        Map: options.map,\n        Number: copyPrimitiveWrapper,\n        Object: options.object,\n        Promise: copySelf,\n        RegExp: options.regExp,\n        Set: options.set,\n        String: copyPrimitiveWrapper,\n        WeakMap: copySelf,\n        WeakSet: copySelf,\n        Uint8Array: options.arrayBuffer,\n        Uint8ClampedArray: options.arrayBuffer,\n        Uint16Array: options.arrayBuffer,\n        Uint32Array: options.arrayBuffer,\n        Uint64Array: options.arrayBuffer,\n    };\n}\n/**\n * Create a custom copier based on the object-specific copy methods passed.\n */\nfunction createCopier(options) {\n    var normalizedOptions = assign({}, DEFAULT_LOOSE_OPTIONS, options);\n    var tagSpecificCopiers = getTagSpecificCopiers(normalizedOptions);\n    var array = tagSpecificCopiers.Array, object = tagSpecificCopiers.Object;\n    function copier(value, state) {\n        state.prototype = state.Constructor = undefined;\n        if (!value || typeof value !== 'object') {\n            return value;\n        }\n        if (state.cache.has(value)) {\n            return state.cache.get(value);\n        }\n        state.prototype = getPrototypeOf(value);\n        state.Constructor = state.prototype && state.prototype.constructor;\n        // plain objects\n        if (!state.Constructor || state.Constructor === Object) {\n            return object(value, state);\n        }\n        // arrays\n        if (isArray(value)) {\n            return array(value, state);\n        }\n        var tagSpecificCopier = tagSpecificCopiers[getTag(value)];\n        if (tagSpecificCopier) {\n            return tagSpecificCopier(value, state);\n        }\n        return typeof value.then === 'function' ? value : object(value, state);\n    }\n    return function copy(value) {\n        return copier(value, {\n            Constructor: undefined,\n            cache: createCache(),\n            copier: copier,\n            prototype: undefined,\n        });\n    };\n}\n/**\n * Create a custom copier based on the object-specific copy methods passed, defaulting to the\n * same internals as `copyStrict`.\n */\nfunction createStrictCopier(options) {\n    return createCopier(assign({}, DEFAULT_STRICT_OPTIONS, options));\n}\n/**\n * Copy an value deeply as much as possible, where strict recreation of object properties\n * are maintained. All properties (including non-enumerable ones) are copied with their\n * original property descriptors on both objects and arrays.\n */\nvar copyStrict = createStrictCopier({});\n/**\n * Copy an value deeply as much as possible.\n */\nvar index = createCopier({});\n\nexports.copyStrict = copyStrict;\nexports.createCopier = createCopier;\nexports.createStrictCopier = createStrictCopier;\nexports[\"default\"] = index;\n//# sourceMappingURL=index.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../../node_modules/fast-copy/dist/cjs/index.cjs\n");

/***/ })

};
;