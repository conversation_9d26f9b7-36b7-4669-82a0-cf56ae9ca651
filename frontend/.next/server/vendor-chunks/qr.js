"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/qr";
exports.ids = ["vendor-chunks/qr"];
exports.modules = {

/***/ "(ssr)/../node_modules/qr/index.js":
/*!***********************************!*\
  !*** ../node_modules/qr/index.js ***!
  \***********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bitmap: () => (/* binding */ Bitmap),\n/* harmony export */   ECMode: () => (/* binding */ ECMode),\n/* harmony export */   Encoding: () => (/* binding */ Encoding),\n/* harmony export */   _tests: () => (/* binding */ _tests),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   encodeQR: () => (/* binding */ encodeQR),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   utils: () => (/* binding */ utils)\n/* harmony export */ });\n/*!\nCopyright (c) 2023 Paul Miller (paulmillr.com)\nThe library paulmillr-qr is dual-licensed under the Apache 2.0 OR MIT license.\nYou can select a license of your choice.\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at\n\n    http://www.apache.org/licenses/LICENSE-2.0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.\n*/\n/**\n * Methods for encoding (generating) QR code patterns.\n * Check out decode.ts for decoding (reading).\n * @module\n * @example\n```js\nimport encodeQR from 'qr';\nconst txt = 'Hello world';\nconst ascii = encodeQR(txt, 'ascii'); // Not all fonts are supported\nconst terminalFriendly = encodeQR(txt, 'term'); // 2x larger, all fonts are OK\nconst gifBytes = encodeQR(txt, 'gif'); // Uncompressed GIF\nconst svgElement = encodeQR(txt, 'svg'); // SVG vector image element\nconst array = encodeQR(txt, 'raw'); // 2d array for canvas or other libs\n// import decodeQR from 'qr/decode.js';\n```\n */\n// We do not use newline escape code directly in strings because it's not parser-friendly\nconst chCodes = { newline: 10, reset: 27 };\nfunction assertNumber(n) {\n    if (!Number.isSafeInteger(n))\n        throw new Error(`integer expected: ${n}`);\n}\nfunction validateVersion(ver) {\n    if (!Number.isSafeInteger(ver) || ver < 1 || ver > 40)\n        throw new Error(`Invalid version=${ver}. Expected number [1..40]`);\n}\nfunction bin(dec, pad) {\n    return dec.toString(2).padStart(pad, '0');\n}\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= 0 ? result : b + result;\n}\nfunction fillArr(length, val) {\n    return new Array(length).fill(val);\n}\n/**\n * Interleaves byte blocks.\n * @param blocks [[1, 2, 3], [4, 5, 6]]\n * @returns [1, 4, 2, 5, 3, 6]\n */\nfunction interleaveBytes(...blocks) {\n    let len = 0;\n    for (const b of blocks)\n        len = Math.max(len, b.length);\n    const res = [];\n    for (let i = 0; i < len; i++) {\n        for (const b of blocks) {\n            if (i >= b.length)\n                continue; // outside of block, skip\n            res.push(b[i]);\n        }\n    }\n    return new Uint8Array(res);\n}\nfunction includesAt(lst, pattern, index) {\n    if (index < 0 || index + pattern.length > lst.length)\n        return false;\n    for (let i = 0; i < pattern.length; i++)\n        if (pattern[i] !== lst[index + i])\n            return false;\n    return true;\n}\n// Optimize for minimal score/penalty\nfunction best() {\n    let best;\n    let bestScore = Infinity;\n    return {\n        add(score, value) {\n            if (score >= bestScore)\n                return;\n            best = value;\n            bestScore = score;\n        },\n        get: () => best,\n        score: () => bestScore,\n    };\n}\n// Based on https://github.com/paulmillr/scure-base/blob/main/index.ts\nfunction alphabet(alphabet) {\n    return {\n        has: (char) => alphabet.includes(char),\n        decode: (input) => {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('alphabet.decode input should be array of strings');\n            return input.map((letter) => {\n                if (typeof letter !== 'string')\n                    throw new Error(`alphabet.decode: not string element=${letter}`);\n                const index = alphabet.indexOf(letter);\n                if (index === -1)\n                    throw new Error(`Unknown letter: \"${letter}\". Allowed: ${alphabet}`);\n                return index;\n            });\n        },\n        encode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('alphabet.encode input should be an array of numbers');\n            return digits.map((i) => {\n                assertNumber(i);\n                if (i < 0 || i >= alphabet.length)\n                    throw new Error(`Digit index outside alphabet: ${i} (alphabet: ${alphabet.length})`);\n                return alphabet[i];\n            });\n        },\n    };\n}\nclass Bitmap {\n    static size(size, limit) {\n        if (typeof size === 'number')\n            size = { height: size, width: size };\n        if (!Number.isSafeInteger(size.height) && size.height !== Infinity)\n            throw new Error(`Bitmap: invalid height=${size.height} (${typeof size.height})`);\n        if (!Number.isSafeInteger(size.width) && size.width !== Infinity)\n            throw new Error(`Bitmap: invalid width=${size.width} (${typeof size.width})`);\n        if (limit !== undefined) {\n            // Clamp length, so it won't overflow, also allows to use Infinity, so we draw until end\n            size = {\n                width: Math.min(size.width, limit.width),\n                height: Math.min(size.height, limit.height),\n            };\n        }\n        return size;\n    }\n    static fromString(s) {\n        // Remove linebreaks on start and end, so we draw in `` section\n        s = s.replace(/^\\n+/g, '').replace(/\\n+$/g, '');\n        const lines = s.split(String.fromCharCode(chCodes.newline));\n        const height = lines.length;\n        const data = new Array(height);\n        let width;\n        for (const line of lines) {\n            const row = line.split('').map((i) => {\n                if (i === 'X')\n                    return true;\n                if (i === ' ')\n                    return false;\n                if (i === '?')\n                    return undefined;\n                throw new Error(`Bitmap.fromString: unknown symbol=${i}`);\n            });\n            if (width && row.length !== width)\n                throw new Error(`Bitmap.fromString different row sizes: width=${width} cur=${row.length}`);\n            width = row.length;\n            data.push(row);\n        }\n        if (!width)\n            width = 0;\n        return new Bitmap({ height, width }, data);\n    }\n    data;\n    height;\n    width;\n    constructor(size, data) {\n        const { height, width } = Bitmap.size(size);\n        this.data = data || Array.from({ length: height }, () => fillArr(width, undefined));\n        this.height = height;\n        this.width = width;\n    }\n    point(p) {\n        return this.data[p.y][p.x];\n    }\n    isInside(p) {\n        return 0 <= p.x && p.x < this.width && 0 <= p.y && p.y < this.height;\n    }\n    size(offset) {\n        if (!offset)\n            return { height: this.height, width: this.width };\n        const { x, y } = this.xy(offset);\n        return { height: this.height - y, width: this.width - x };\n    }\n    xy(c) {\n        if (typeof c === 'number')\n            c = { x: c, y: c };\n        if (!Number.isSafeInteger(c.x))\n            throw new Error(`Bitmap: invalid x=${c.x}`);\n        if (!Number.isSafeInteger(c.y))\n            throw new Error(`Bitmap: invalid y=${c.y}`);\n        // Do modulo, so we can use negative positions\n        c.x = mod(c.x, this.width);\n        c.y = mod(c.y, this.height);\n        return c;\n    }\n    // Basically every operation can be represented as rect\n    rect(c, size, value) {\n        const { x, y } = this.xy(c);\n        const { height, width } = Bitmap.size(size, this.size({ x, y }));\n        for (let yPos = 0; yPos < height; yPos++) {\n            for (let xPos = 0; xPos < width; xPos++) {\n                // NOTE: we use give function relative coordinates inside box\n                this.data[y + yPos][x + xPos] =\n                    typeof value === 'function'\n                        ? value({ x: xPos, y: yPos }, this.data[y + yPos][x + xPos])\n                        : value;\n            }\n        }\n        return this;\n    }\n    // returns rectangular part of bitmap\n    rectRead(c, size, fn) {\n        return this.rect(c, size, (c, cur) => {\n            fn(c, cur);\n            return cur;\n        });\n    }\n    // Horizontal & vertical lines\n    hLine(c, len, value) {\n        return this.rect(c, { width: len, height: 1 }, value);\n    }\n    vLine(c, len, value) {\n        return this.rect(c, { width: 1, height: len }, value);\n    }\n    // add border\n    border(border = 2, value) {\n        const height = this.height + 2 * border;\n        const width = this.width + 2 * border;\n        const v = fillArr(border, value);\n        const h = Array.from({ length: border }, () => fillArr(width, value));\n        return new Bitmap({ height, width }, [...h, ...this.data.map((i) => [...v, ...i, ...v]), ...h]);\n    }\n    // Embed another bitmap on coordinates\n    embed(c, bm) {\n        return this.rect(c, bm.size(), ({ x, y }) => bm.data[y][x]);\n    }\n    // returns rectangular part of bitmap\n    rectSlice(c, size = this.size()) {\n        const rect = new Bitmap(Bitmap.size(size, this.size(this.xy(c))));\n        this.rect(c, size, ({ x, y }, cur) => (rect.data[y][x] = cur));\n        return rect;\n    }\n    // Change shape, replace rows with columns (data[y][x] -> data[x][y])\n    inverse() {\n        const { height, width } = this;\n        const res = new Bitmap({ height: width, width: height });\n        return res.rect({ x: 0, y: 0 }, Infinity, ({ x, y }) => this.data[x][y]);\n    }\n    // Each pixel size is multiplied by factor\n    scale(factor) {\n        if (!Number.isSafeInteger(factor) || factor > 1024)\n            throw new Error(`invalid scale factor: ${factor}`);\n        const { height, width } = this;\n        const res = new Bitmap({ height: factor * height, width: factor * width });\n        return res.rect({ x: 0, y: 0 }, Infinity, ({ x, y }) => this.data[Math.floor(y / factor)][Math.floor(x / factor)]);\n    }\n    clone() {\n        const res = new Bitmap(this.size());\n        return res.rect({ x: 0, y: 0 }, this.size(), ({ x, y }) => this.data[y][x]);\n    }\n    // Ensure that there is no undefined values left\n    assertDrawn() {\n        this.rectRead(0, Infinity, (_, cur) => {\n            if (typeof cur !== 'boolean')\n                throw new Error(`Invalid color type=${typeof cur}`);\n        });\n    }\n    // Simple string representation for debugging\n    toString() {\n        return this.data\n            .map((i) => i.map((j) => (j === undefined ? '?' : j ? 'X' : ' ')).join(''))\n            .join(String.fromCharCode(chCodes.newline));\n    }\n    toASCII() {\n        const { height, width, data } = this;\n        let out = '';\n        // Terminal character height is x2 of character width, so we process two rows of bitmap\n        // to produce one row of ASCII\n        for (let y = 0; y < height; y += 2) {\n            for (let x = 0; x < width; x++) {\n                const first = data[y][x];\n                const second = y + 1 >= height ? true : data[y + 1][x]; // if last row outside bitmap, make it black\n                if (!first && !second)\n                    out += '█'; // both rows white (empty)\n                else if (!first && second)\n                    out += '▀'; // top row white\n                else if (first && !second)\n                    out += '▄'; // down row white\n                else if (first && second)\n                    out += ' '; // both rows black\n            }\n            out += String.fromCharCode(chCodes.newline);\n        }\n        return out;\n    }\n    toTerm() {\n        const cc = String.fromCharCode(chCodes.reset);\n        const reset = cc + '[0m';\n        const whiteBG = cc + '[1;47m  ' + reset;\n        const darkBG = cc + `[40m  ` + reset;\n        return this.data\n            .map((i) => i.map((j) => (j ? darkBG : whiteBG)).join(''))\n            .join(String.fromCharCode(chCodes.newline));\n    }\n    toSVG(optimize = true) {\n        let out = `<svg viewBox=\"0 0 ${this.width} ${this.height}\" xmlns=\"http://www.w3.org/2000/svg\">`;\n        // Construct optimized SVG path data.\n        let pathData = '';\n        let prevPoint;\n        this.rectRead(0, Infinity, (point, val) => {\n            if (!val)\n                return;\n            const { x, y } = point;\n            if (!optimize) {\n                out += `<rect x=\"${x}\" y=\"${y}\" width=\"1\" height=\"1\" />`;\n                return;\n            }\n            // https://developer.mozilla.org/en-US/docs/Web/SVG/Reference/Attribute/d#path_commands\n            // Determine the shortest way to represent the initial cursor movement.\n            // M - Move cursor (without drawing) to absolute coordinate pair.\n            let m = `M${x} ${y}`;\n            // Only allow using the relative cursor move command if previous points\n            // were drawn.\n            if (prevPoint) {\n                // m - Move cursor (without drawing) to relative coordinate pair.\n                const relM = `m${x - prevPoint.x} ${y - prevPoint.y}`;\n                if (relM.length <= m.length)\n                    m = relM;\n            }\n            // Determine the shortest way to represent the cell's bottom line draw.\n            // H - Draw line from cursor position to absolute x coordinate.\n            // h - Draw line from cursor position to relative x coordinate.\n            const bH = x < 10 ? `H${x}` : 'h-1';\n            // v - Draw line from cursor position to relative y coordinate.\n            // Z - Close path (draws line from cursor position to M coordinate).\n            pathData += `${m}h1v1${bH}Z`;\n            prevPoint = point;\n        });\n        if (optimize)\n            out += `<path d=\"${pathData}\"/>`;\n        out += `</svg>`;\n        return out;\n    }\n    toGIF() {\n        // NOTE: Small, but inefficient implementation.\n        // Uses 1 byte per pixel.\n        const u16le = (i) => [i & 0xff, (i >>> 8) & 0xff];\n        const dims = [...u16le(this.width), ...u16le(this.height)];\n        const data = [];\n        this.rectRead(0, Infinity, (_, cur) => data.push(+(cur === true)));\n        const N = 126; // Block size\n        // prettier-ignore\n        const bytes = [\n            0x47, 0x49, 0x46, 0x38, 0x37, 0x61, ...dims, 0xf6, 0x00, 0x00, 0xff, 0xff, 0xff,\n            ...fillArr(3 * 127, 0x00), 0x2c, 0x00, 0x00, 0x00, 0x00, ...dims, 0x00, 0x07\n        ];\n        const fullChunks = Math.floor(data.length / N);\n        // Full blocks\n        for (let i = 0; i < fullChunks; i++)\n            bytes.push(N + 1, 0x80, ...data.slice(N * i, N * (i + 1)).map((i) => +i));\n        // Remaining bytes\n        bytes.push((data.length % N) + 1, 0x80, ...data.slice(fullChunks * N).map((i) => +i));\n        bytes.push(0x01, 0x81, 0x00, 0x3b);\n        return new Uint8Array(bytes);\n    }\n    toImage(isRGB = false) {\n        const { height, width } = this.size();\n        const data = new Uint8Array(height * width * (isRGB ? 3 : 4));\n        let i = 0;\n        for (let y = 0; y < height; y++) {\n            for (let x = 0; x < width; x++) {\n                const value = !!this.data[y][x] ? 0 : 255;\n                data[i++] = value;\n                data[i++] = value;\n                data[i++] = value;\n                if (!isRGB)\n                    data[i++] = 255; // alpha channel\n            }\n        }\n        return { height, width, data };\n    }\n}\n// End of utils\n// Runtime type-checking\n/** Error correction mode. low: 7%, medium: 15%, quartile: 25%, high: 30% */\nconst ECMode = ['low', 'medium', 'quartile', 'high'];\n/** QR Code encoding */\nconst Encoding = ['numeric', 'alphanumeric', 'byte', 'kanji', 'eci'];\n// Various constants & tables\n// prettier-ignore\nconst BYTES = [\n    // 1,  2,  3,   4,   5,   6,   7,   8,   9,  10,  11,  12,  13,  14,  15,  16,  17,  18,  19,   20,\n    26, 44, 70, 100, 134, 172, 196, 242, 292, 346, 404, 466, 532, 581, 655, 733, 815, 901, 991, 1085,\n    //  21,   22,   23,   24,   25,   26,   27,   28,   29,   30,   31,   32,   33,   34,   35,   36,   37,   38,   39,   40\n    1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185, 2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706,\n];\n// prettier-ignore\nconst WORDS_PER_BLOCK = {\n    // Version 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40\n    low: [7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    medium: [10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    quartile: [13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    high: [17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n};\n// prettier-ignore\nconst ECC_BLOCKS = {\n    // Version   1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40\n    low: [1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    medium: [1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    quartile: [1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    high: [1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81],\n};\nconst info = {\n    size: {\n        encode: (ver) => 21 + 4 * (ver - 1), // ver1 = 21, ver40=177 blocks\n        decode: (size) => (size - 17) / 4,\n    },\n    sizeType: (ver) => Math.floor((ver + 7) / 17),\n    // Based on https://codereview.stackexchange.com/questions/74925/algorithm-to-generate-this-alignment-pattern-locations-table-for-qr-codes\n    alignmentPatterns(ver) {\n        if (ver === 1)\n            return [];\n        const first = 6;\n        const last = info.size.encode(ver) - first - 1;\n        const distance = last - first;\n        const count = Math.ceil(distance / 28);\n        let interval = Math.floor(distance / count);\n        if (interval % 2)\n            interval += 1;\n        else if ((distance % count) * 2 >= count)\n            interval += 2;\n        const res = [first];\n        for (let m = 1; m < count; m++)\n            res.push(last - (count - m) * interval);\n        res.push(last);\n        return res;\n    },\n    ECCode: {\n        low: 0b01,\n        medium: 0b00,\n        quartile: 0b11,\n        high: 0b10,\n    },\n    formatMask: 0b101010000010010,\n    formatBits(ecc, maskIdx) {\n        const data = (info.ECCode[ecc] << 3) | maskIdx;\n        let d = data;\n        for (let i = 0; i < 10; i++)\n            d = (d << 1) ^ ((d >> 9) * 0b10100110111);\n        return ((data << 10) | d) ^ info.formatMask;\n    },\n    versionBits(ver) {\n        let d = ver;\n        for (let i = 0; i < 12; i++)\n            d = (d << 1) ^ ((d >> 11) * 0b1111100100101);\n        return (ver << 12) | d;\n    },\n    alphabet: {\n        numeric: alphabet('0123456789'),\n        alphanumerc: alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:'),\n    }, // as Record<EncodingType, ReturnType<typeof alphabet>>,\n    lengthBits(ver, type) {\n        const table = {\n            numeric: [10, 12, 14],\n            alphanumeric: [9, 11, 13],\n            byte: [8, 16, 16],\n            kanji: [8, 10, 12],\n            eci: [0, 0, 0],\n        };\n        return table[type][info.sizeType(ver)];\n    },\n    modeBits: {\n        numeric: '0001',\n        alphanumeric: '0010',\n        byte: '0100',\n        kanji: '1000',\n        eci: '0111',\n    },\n    capacity(ver, ecc) {\n        const bytes = BYTES[ver - 1];\n        const words = WORDS_PER_BLOCK[ecc][ver - 1];\n        const numBlocks = ECC_BLOCKS[ecc][ver - 1];\n        const blockLen = Math.floor(bytes / numBlocks) - words;\n        const shortBlocks = numBlocks - (bytes % numBlocks);\n        return {\n            words,\n            numBlocks,\n            shortBlocks,\n            blockLen,\n            capacity: (bytes - words * numBlocks) * 8,\n            total: (words + blockLen) * numBlocks + numBlocks - shortBlocks,\n        };\n    },\n};\nconst PATTERNS = [\n    (x, y) => (x + y) % 2 == 0,\n    (_x, y) => y % 2 == 0,\n    (x, _y) => x % 3 == 0,\n    (x, y) => (x + y) % 3 == 0,\n    (x, y) => (Math.floor(y / 2) + Math.floor(x / 3)) % 2 == 0,\n    (x, y) => ((x * y) % 2) + ((x * y) % 3) == 0,\n    (x, y) => (((x * y) % 2) + ((x * y) % 3)) % 2 == 0,\n    (x, y) => (((x + y) % 2) + ((x * y) % 3)) % 2 == 0,\n];\n// Galois field && reed-solomon encoding\nconst GF = {\n    tables: ((p_poly) => {\n        const exp = fillArr(256, 0);\n        const log = fillArr(256, 0);\n        for (let i = 0, x = 1; i < 256; i++) {\n            exp[i] = x;\n            log[x] = i;\n            x <<= 1;\n            if (x & 0x100)\n                x ^= p_poly;\n        }\n        return { exp, log };\n    })(0x11d),\n    exp: (x) => GF.tables.exp[x],\n    log(x) {\n        if (x === 0)\n            throw new Error(`GF.log: invalid arg=${x}`);\n        return GF.tables.log[x] % 255;\n    },\n    mul(x, y) {\n        if (x === 0 || y === 0)\n            return 0;\n        return GF.tables.exp[(GF.tables.log[x] + GF.tables.log[y]) % 255];\n    },\n    add: (x, y) => x ^ y,\n    pow: (x, e) => GF.tables.exp[(GF.tables.log[x] * e) % 255],\n    inv(x) {\n        if (x === 0)\n            throw new Error(`GF.inverse: invalid arg=${x}`);\n        return GF.tables.exp[255 - GF.tables.log[x]];\n    },\n    polynomial(poly) {\n        if (poly.length == 0)\n            throw new Error('GF.polymomial: invalid length');\n        if (poly[0] !== 0)\n            return poly;\n        // Strip leading zeros\n        let i = 0;\n        for (; i < poly.length - 1 && poly[i] == 0; i++)\n            ;\n        return poly.slice(i);\n    },\n    monomial(degree, coefficient) {\n        if (degree < 0)\n            throw new Error(`GF.monomial: invalid degree=${degree}`);\n        if (coefficient == 0)\n            return [0];\n        let coefficients = fillArr(degree + 1, 0);\n        coefficients[0] = coefficient;\n        return GF.polynomial(coefficients);\n    },\n    degree: (a) => a.length - 1,\n    coefficient: (a, degree) => a[GF.degree(a) - degree],\n    mulPoly(a, b) {\n        if (a[0] === 0 || b[0] === 0)\n            return [0];\n        const res = fillArr(a.length + b.length - 1, 0);\n        for (let i = 0; i < a.length; i++) {\n            for (let j = 0; j < b.length; j++) {\n                res[i + j] = GF.add(res[i + j], GF.mul(a[i], b[j]));\n            }\n        }\n        return GF.polynomial(res);\n    },\n    mulPolyScalar(a, scalar) {\n        if (scalar == 0)\n            return [0];\n        if (scalar == 1)\n            return a;\n        const res = fillArr(a.length, 0);\n        for (let i = 0; i < a.length; i++)\n            res[i] = GF.mul(a[i], scalar);\n        return GF.polynomial(res);\n    },\n    mulPolyMonomial(a, degree, coefficient) {\n        if (degree < 0)\n            throw new Error('GF.mulPolyMonomial: invalid degree');\n        if (coefficient == 0)\n            return [0];\n        const res = fillArr(a.length + degree, 0);\n        for (let i = 0; i < a.length; i++)\n            res[i] = GF.mul(a[i], coefficient);\n        return GF.polynomial(res);\n    },\n    addPoly(a, b) {\n        if (a[0] === 0)\n            return b;\n        if (b[0] === 0)\n            return a;\n        let smaller = a;\n        let larger = b;\n        if (smaller.length > larger.length)\n            [smaller, larger] = [larger, smaller];\n        let sumDiff = fillArr(larger.length, 0);\n        let lengthDiff = larger.length - smaller.length;\n        let s = larger.slice(0, lengthDiff);\n        for (let i = 0; i < s.length; i++)\n            sumDiff[i] = s[i];\n        for (let i = lengthDiff; i < larger.length; i++)\n            sumDiff[i] = GF.add(smaller[i - lengthDiff], larger[i]);\n        return GF.polynomial(sumDiff);\n    },\n    remainderPoly(data, divisor) {\n        const out = Array.from(data);\n        for (let i = 0; i < data.length - divisor.length + 1; i++) {\n            const elm = out[i];\n            if (elm === 0)\n                continue;\n            for (let j = 1; j < divisor.length; j++) {\n                if (divisor[j] !== 0)\n                    out[i + j] = GF.add(out[i + j], GF.mul(divisor[j], elm));\n            }\n        }\n        return out.slice(data.length - divisor.length + 1, out.length);\n    },\n    divisorPoly(degree) {\n        let g = [1];\n        for (let i = 0; i < degree; i++)\n            g = GF.mulPoly(g, [1, GF.pow(2, i)]);\n        return g;\n    },\n    evalPoly(poly, a) {\n        if (a == 0)\n            return GF.coefficient(poly, 0); // Just return the x^0 coefficient\n        let res = poly[0];\n        for (let i = 1; i < poly.length; i++)\n            res = GF.add(GF.mul(a, res), poly[i]);\n        return res;\n    },\n    // TODO: cleanup\n    euclidian(a, b, R) {\n        // Force degree(a) >= degree(b)\n        if (GF.degree(a) < GF.degree(b))\n            [a, b] = [b, a];\n        let rLast = a;\n        let r = b;\n        let tLast = [0];\n        let t = [1];\n        // while degree of Ri ≥ t/2\n        while (2 * GF.degree(r) >= R) {\n            let rLastLast = rLast;\n            let tLastLast = tLast;\n            rLast = r;\n            tLast = t;\n            if (rLast[0] === 0)\n                throw new Error('rLast[0] === 0');\n            r = rLastLast;\n            let q = [0];\n            const dltInverse = GF.inv(rLast[0]);\n            while (GF.degree(r) >= GF.degree(rLast) && r[0] !== 0) {\n                const degreeDiff = GF.degree(r) - GF.degree(rLast);\n                const scale = GF.mul(r[0], dltInverse);\n                q = GF.addPoly(q, GF.monomial(degreeDiff, scale));\n                r = GF.addPoly(r, GF.mulPolyMonomial(rLast, degreeDiff, scale));\n            }\n            q = GF.mulPoly(q, tLast);\n            t = GF.addPoly(q, tLastLast);\n            if (GF.degree(r) >= GF.degree(rLast))\n                throw new Error(`Division failed r: ${r}, rLast: ${rLast}`);\n        }\n        const sigmaTildeAtZero = GF.coefficient(t, 0);\n        if (sigmaTildeAtZero == 0)\n            throw new Error('sigmaTilde(0) was zero');\n        const inverse = GF.inv(sigmaTildeAtZero);\n        return [GF.mulPolyScalar(t, inverse), GF.mulPolyScalar(r, inverse)];\n    },\n};\nfunction RS(eccWords) {\n    return {\n        encode(from) {\n            const d = GF.divisorPoly(eccWords);\n            const pol = Array.from(from);\n            pol.push(...d.slice(0, -1).fill(0));\n            return Uint8Array.from(GF.remainderPoly(pol, d));\n        },\n        decode(to) {\n            const res = to.slice();\n            const poly = GF.polynomial(Array.from(to));\n            // Find errors\n            let syndrome = fillArr(eccWords, 0);\n            let hasError = false;\n            for (let i = 0; i < eccWords; i++) {\n                const evl = GF.evalPoly(poly, GF.exp(i));\n                syndrome[syndrome.length - 1 - i] = evl;\n                if (evl !== 0)\n                    hasError = true;\n            }\n            if (!hasError)\n                return res;\n            syndrome = GF.polynomial(syndrome);\n            const monomial = GF.monomial(eccWords, 1);\n            const [errorLocator, errorEvaluator] = GF.euclidian(monomial, syndrome, eccWords);\n            // Error locations\n            const locations = fillArr(GF.degree(errorLocator), 0);\n            let e = 0;\n            for (let i = 1; i < 256 && e < locations.length; i++) {\n                if (GF.evalPoly(errorLocator, i) === 0)\n                    locations[e++] = GF.inv(i);\n            }\n            if (e !== locations.length)\n                throw new Error('RS.decode: invalid errors number');\n            for (let i = 0; i < locations.length; i++) {\n                const pos = res.length - 1 - GF.log(locations[i]);\n                if (pos < 0)\n                    throw new Error('RS.decode: invalid error location');\n                const xiInverse = GF.inv(locations[i]);\n                let denominator = 1;\n                for (let j = 0; j < locations.length; j++) {\n                    if (i === j)\n                        continue;\n                    denominator = GF.mul(denominator, GF.add(1, GF.mul(locations[j], xiInverse)));\n                }\n                res[pos] = GF.add(res[pos], GF.mul(GF.evalPoly(errorEvaluator, xiInverse), GF.inv(denominator)));\n            }\n            return res;\n        },\n    };\n}\n// Interleaves blocks\nfunction interleave(ver, ecc) {\n    const { words, shortBlocks, numBlocks, blockLen, total } = info.capacity(ver, ecc);\n    const rs = RS(words);\n    return {\n        encode(bytes) {\n            // Add error correction to bytes\n            const blocks = [];\n            const eccBlocks = [];\n            for (let i = 0; i < numBlocks; i++) {\n                const isShort = i < shortBlocks;\n                const len = blockLen + (isShort ? 0 : 1);\n                blocks.push(bytes.subarray(0, len));\n                eccBlocks.push(rs.encode(bytes.subarray(0, len)));\n                bytes = bytes.subarray(len);\n            }\n            const resBlocks = interleaveBytes(...blocks);\n            const resECC = interleaveBytes(...eccBlocks);\n            const res = new Uint8Array(resBlocks.length + resECC.length);\n            res.set(resBlocks);\n            res.set(resECC, resBlocks.length);\n            return res;\n        },\n        decode(data) {\n            if (data.length !== total)\n                throw new Error(`interleave.decode: len(data)=${data.length}, total=${total}`);\n            const blocks = [];\n            for (let i = 0; i < numBlocks; i++) {\n                const isShort = i < shortBlocks;\n                blocks.push(new Uint8Array(words + blockLen + (isShort ? 0 : 1)));\n            }\n            // Short blocks\n            let pos = 0;\n            for (let i = 0; i < blockLen; i++) {\n                for (let j = 0; j < numBlocks; j++)\n                    blocks[j][i] = data[pos++];\n            }\n            // Long blocks\n            for (let j = shortBlocks; j < numBlocks; j++)\n                blocks[j][blockLen] = data[pos++];\n            // ECC\n            for (let i = blockLen; i < blockLen + words; i++) {\n                for (let j = 0; j < numBlocks; j++) {\n                    const isShort = j < shortBlocks;\n                    blocks[j][i + (isShort ? 0 : 1)] = data[pos++];\n                }\n            }\n            // Decode\n            // Error-correct and copy data blocks together into a stream of bytes\n            const res = [];\n            for (const block of blocks)\n                res.push(...Array.from(rs.decode(block)).slice(0, -words));\n            return Uint8Array.from(res);\n        },\n    };\n}\n// Draw\n// Generic template per version+ecc+mask. Can be cached, to speedup calculations.\nfunction drawTemplate(ver, ecc, maskIdx, test = false) {\n    const size = info.size.encode(ver);\n    let b = new Bitmap(size + 2);\n    // Finder patterns\n    // We draw full pattern and later slice, since before addition of borders finder is truncated by one pixel on sides\n    const finder = new Bitmap(3).rect(0, 3, true).border(1, false).border(1, true).border(1, false);\n    b = b\n        .embed(0, finder) // top left\n        .embed({ x: -finder.width, y: 0 }, finder) // top right\n        .embed({ x: 0, y: -finder.height }, finder); // bottom left\n    b = b.rectSlice(1, size);\n    // Alignment patterns\n    const align = new Bitmap(1).rect(0, 1, true).border(1, false).border(1, true);\n    const alignPos = info.alignmentPatterns(ver);\n    for (const y of alignPos) {\n        for (const x of alignPos) {\n            if (b.data[y][x] !== undefined)\n                continue;\n            b.embed({ x: x - 2, y: y - 2 }, align); // center of pattern should be at position\n        }\n    }\n    // Timing patterns\n    b = b\n        .hLine({ x: 0, y: 6 }, Infinity, ({ x }, cur) => (cur === undefined ? x % 2 == 0 : cur))\n        .vLine({ x: 6, y: 0 }, Infinity, ({ y }, cur) => (cur === undefined ? y % 2 == 0 : cur));\n    // Format information\n    {\n        const bits = info.formatBits(ecc, maskIdx);\n        const getBit = (i) => !test && ((bits >> i) & 1) == 1;\n        // vertical\n        for (let i = 0; i < 6; i++)\n            b.data[i][8] = getBit(i); // right of top-left finder\n        // TODO: re-write as lines, like:\n        // b.vLine({ x: 8, y: 0 }, 6, ({ x, y }) => getBit(y));\n        for (let i = 6; i < 8; i++)\n            b.data[i + 1][8] = getBit(i); // after timing pattern\n        for (let i = 8; i < 15; i++)\n            b.data[size - 15 + i][8] = getBit(i); // right of bottom-left finder\n        // horizontal\n        for (let i = 0; i < 8; i++)\n            b.data[8][size - i - 1] = getBit(i); // under top-right finder\n        for (let i = 8; i < 9; i++)\n            b.data[8][15 - i - 1 + 1] = getBit(i); // VVV, after timing\n        for (let i = 9; i < 15; i++)\n            b.data[8][15 - i - 1] = getBit(i); // under top-left finder\n        b.data[size - 8][8] = !test; // bottom-left finder, right\n    }\n    // Version information\n    if (ver >= 7) {\n        const bits = info.versionBits(ver);\n        for (let i = 0; i < 18; i += 1) {\n            const bit = !test && ((bits >> i) & 1) == 1;\n            const x = Math.floor(i / 3);\n            const y = (i % 3) + size - 8 - 3;\n            // two copies\n            b.data[x][y] = bit;\n            b.data[y][x] = bit;\n        }\n    }\n    return b;\n}\n// zigzag: bottom->top && top->bottom\nfunction zigzag(tpl, maskIdx, fn) {\n    const size = tpl.height;\n    const pattern = PATTERNS[maskIdx];\n    // zig-zag pattern\n    let dir = -1;\n    let y = size - 1;\n    // two columns at time\n    for (let xOffset = size - 1; xOffset > 0; xOffset -= 2) {\n        if (xOffset == 6)\n            xOffset = 5; // skip vertical timing pattern\n        for (;; y += dir) {\n            for (let j = 0; j < 2; j += 1) {\n                const x = xOffset - j;\n                if (tpl.data[y][x] !== undefined)\n                    continue; // skip already written elements\n                fn(x, y, pattern(x, y));\n            }\n            if (y + dir < 0 || y + dir >= size)\n                break;\n        }\n        dir = -dir; // change direction\n    }\n}\n// NOTE: byte encoding is just representation, QR works with strings only. Most decoders will fail on raw byte array,\n// since they expect unicode or other text encoding inside bytes\nfunction detectType(str) {\n    let type = 'numeric';\n    for (let x of str) {\n        if (info.alphabet.numeric.has(x))\n            continue;\n        type = 'alphanumeric';\n        if (!info.alphabet.alphanumerc.has(x))\n            return 'byte';\n    }\n    return type;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\nfunction encode(ver, ecc, data, type) {\n    let encoded = '';\n    let dataLen = data.length;\n    if (type === 'numeric') {\n        const t = info.alphabet.numeric.decode(data.split(''));\n        const n = t.length;\n        for (let i = 0; i < n - 2; i += 3)\n            encoded += bin(t[i] * 100 + t[i + 1] * 10 + t[i + 2], 10);\n        if (n % 3 === 1) {\n            encoded += bin(t[n - 1], 4);\n        }\n        else if (n % 3 === 2) {\n            encoded += bin(t[n - 2] * 10 + t[n - 1], 7);\n        }\n    }\n    else if (type === 'alphanumeric') {\n        const t = info.alphabet.alphanumerc.decode(data.split(''));\n        const n = t.length;\n        for (let i = 0; i < n - 1; i += 2)\n            encoded += bin(t[i] * 45 + t[i + 1], 11);\n        if (n % 2 == 1)\n            encoded += bin(t[n - 1], 6); // pad if odd number of chars\n    }\n    else if (type === 'byte') {\n        const utf8 = utf8ToBytes(data);\n        dataLen = utf8.length;\n        encoded = Array.from(utf8)\n            .map((i) => bin(i, 8))\n            .join('');\n    }\n    else {\n        throw new Error('encode: unsupported type');\n    }\n    const { capacity } = info.capacity(ver, ecc);\n    const len = bin(dataLen, info.lengthBits(ver, type));\n    let bits = info.modeBits[type] + len + encoded;\n    if (bits.length > capacity)\n        throw new Error('Capacity overflow');\n    // Terminator\n    bits += '0'.repeat(Math.min(4, Math.max(0, capacity - bits.length)));\n    // Pad bits string untill full byte\n    if (bits.length % 8)\n        bits += '0'.repeat(8 - (bits.length % 8));\n    // Add padding until capacity is full\n    const padding = '1110110000010001';\n    for (let idx = 0; bits.length !== capacity; idx++)\n        bits += padding[idx % padding.length];\n    // Convert a bitstring to array of bytes\n    const bytes = Uint8Array.from(bits.match(/(.{8})/g).map((i) => Number(`0b${i}`)));\n    return interleave(ver, ecc).encode(bytes);\n}\n// DRAW\nfunction drawQR(ver, ecc, data, maskIdx, test = false) {\n    const b = drawTemplate(ver, ecc, maskIdx, test);\n    let i = 0;\n    const need = 8 * data.length;\n    zigzag(b, maskIdx, (x, y, mask) => {\n        let value = false;\n        if (i < need) {\n            value = ((data[i >>> 3] >> ((7 - i) & 7)) & 1) !== 0;\n            i++;\n        }\n        b.data[y][x] = value !== mask; // !== as xor\n    });\n    if (i !== need)\n        throw new Error('QR: bytes left after draw');\n    return b;\n}\nfunction penalty(bm) {\n    const inverse = bm.inverse();\n    // Adjacent modules in row/column in same | No. of modules = (5 + i) color\n    const sameColor = (row) => {\n        let res = 0;\n        for (let i = 0, same = 1, last = undefined; i < row.length; i++) {\n            if (last === row[i]) {\n                same++;\n                if (i !== row.length - 1)\n                    continue; // handle last element\n            }\n            if (same >= 5)\n                res += 3 + (same - 5);\n            last = row[i];\n            same = 1;\n        }\n        return res;\n    };\n    let adjacent = 0;\n    bm.data.forEach((row) => (adjacent += sameColor(row)));\n    inverse.data.forEach((column) => (adjacent += sameColor(column)));\n    // Block of modules in same color (Block size = 2x2)\n    let box = 0;\n    let b = bm.data;\n    const lastW = bm.width - 1;\n    const lastH = bm.height - 1;\n    for (let x = 0; x < lastW; x++) {\n        for (let y = 0; y < lastH; y++) {\n            const x1 = x + 1;\n            const y1 = y + 1;\n            if (b[x][y] === b[x1][y] && b[x1][y] === b[x][y1] && b[x1][y] === b[x1][y1]) {\n                box += 3;\n            }\n        }\n    }\n    // 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column, preceded or followed by light area 4 modules wide\n    const finderPattern = (row) => {\n        const finderPattern = [true, false, true, true, true, false, true]; // dark:light:dark:light:dark\n        const lightPattern = [false, false, false, false]; // light area 4 modules wide\n        const p1 = [...finderPattern, ...lightPattern];\n        const p2 = [...lightPattern, ...finderPattern];\n        let res = 0;\n        for (let i = 0; i < row.length; i++) {\n            if (includesAt(row, p1, i))\n                res += 40;\n            if (includesAt(row, p2, i))\n                res += 40;\n        }\n        return res;\n    };\n    let finder = 0;\n    for (const row of bm.data)\n        finder += finderPattern(row);\n    for (const column of inverse.data)\n        finder += finderPattern(column);\n    // Proportion of dark modules in entire symbol\n    // Add 10 points to a deviation of 5% increment or decrement in the proportion\n    // ratio of dark module from the referential 50%\n    let darkPixels = 0;\n    bm.rectRead(0, Infinity, (_c, val) => (darkPixels += val ? 1 : 0));\n    const darkPercent = (darkPixels / (bm.height * bm.width)) * 100;\n    const dark = 10 * Math.floor(Math.abs(darkPercent - 50) / 5);\n    return adjacent + box + finder + dark;\n}\n// Selects best mask according to penalty, if no mask is provided\nfunction drawQRBest(ver, ecc, data, maskIdx) {\n    if (maskIdx === undefined) {\n        const bestMask = best();\n        for (let mask = 0; mask < PATTERNS.length; mask++)\n            bestMask.add(penalty(drawQR(ver, ecc, data, mask, true)), mask);\n        maskIdx = bestMask.get();\n    }\n    if (maskIdx === undefined)\n        throw new Error('Cannot find mask'); // Should never happen\n    return drawQR(ver, ecc, data, maskIdx);\n}\nfunction validateECC(ec) {\n    if (!ECMode.includes(ec))\n        throw new Error(`Invalid error correction mode=${ec}. Expected: ${ECMode}`);\n}\nfunction validateEncoding(enc) {\n    if (!Encoding.includes(enc))\n        throw new Error(`Encoding: invalid mode=${enc}. Expected: ${Encoding}`);\n    if (enc === 'kanji' || enc === 'eci')\n        throw new Error(`Encoding: ${enc} is not supported (yet?).`);\n}\nfunction validateMask(mask) {\n    if (![0, 1, 2, 3, 4, 5, 6, 7].includes(mask) || !PATTERNS[mask])\n        throw new Error(`Invalid mask=${mask}. Expected number [0..7]`);\n}\nfunction encodeQR(text, output = 'raw', opts = {}) {\n    const ecc = opts.ecc !== undefined ? opts.ecc : 'medium';\n    validateECC(ecc);\n    const encoding = opts.encoding !== undefined ? opts.encoding : detectType(text);\n    validateEncoding(encoding);\n    if (opts.mask !== undefined)\n        validateMask(opts.mask);\n    let ver = opts.version;\n    let data, err = new Error('Unknown error');\n    if (ver !== undefined) {\n        validateVersion(ver);\n        data = encode(ver, ecc, text, encoding);\n    }\n    else {\n        // If no version is provided, try to find smallest one which fits\n        // Currently just scans all version, can be significantly speedup if needed\n        for (let i = 1; i <= 40; i++) {\n            try {\n                data = encode(i, ecc, text, encoding);\n                ver = i;\n                break;\n            }\n            catch (e) {\n                err = e;\n            }\n        }\n    }\n    if (!ver || !data)\n        throw err;\n    let res = drawQRBest(ver, ecc, data, opts.mask);\n    res.assertDrawn();\n    const border = opts.border === undefined ? 2 : opts.border;\n    if (!Number.isSafeInteger(border))\n        throw new Error(`invalid border type=${typeof border}`);\n    res = res.border(border, false); // Add border\n    if (opts.scale !== undefined)\n        res = res.scale(opts.scale); // Scale image\n    if (output === 'raw')\n        return res.data;\n    else if (output === 'ascii')\n        return res.toASCII();\n    else if (output === 'svg')\n        return res.toSVG(opts.optimize);\n    else if (output === 'gif')\n        return res.toGIF();\n    else if (output === 'term')\n        return res.toTerm();\n    else\n        throw new Error(`Unknown output: ${output}`);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encodeQR);\nconst utils = {\n    best,\n    bin,\n    drawTemplate,\n    fillArr,\n    info,\n    interleave,\n    validateVersion,\n    zigzag,\n};\n// Unsafe API utils, exported only for tests\nconst _tests = {\n    Bitmap,\n    info,\n    detectType,\n    encode,\n    drawQR,\n    penalty,\n    PATTERNS,\n};\n// Type tests\n// const o1 = qr('test', 'ascii');\n// const o2 = qr('test', 'raw');\n// const o3 = qr('test', 'gif');\n// const o4 = qr('test', 'svg');\n// const o5 = qr('test', 'term');\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/qr/index.js\n");

/***/ })

};
;