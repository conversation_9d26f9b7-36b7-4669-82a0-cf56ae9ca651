"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/valtio";
exports.ids = ["vendor-chunks/valtio"];
exports.modules = {

/***/ "(ssr)/../node_modules/valtio/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ../node_modules/valtio/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion),\n/* harmony export */   proxy: () => (/* binding */ proxy),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   snapshot: () => (/* binding */ snapshot),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   unstable_buildProxyFunction: () => (/* binding */ unstable_buildProxyFunction)\n/* harmony export */ });\n/* harmony import */ var proxy_compare__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! proxy-compare */ \"(ssr)/../node_modules/proxy-compare/dist/index.modern.js\");\n\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nconst proxyStateMap = /* @__PURE__ */ new WeakMap();\nconst refSet = /* @__PURE__ */ new WeakSet();\nconst buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), defaultHandlePromise = (promise) => {\n  switch (promise.status) {\n    case \"fulfilled\":\n      return promise.value;\n    case \"rejected\":\n      throw promise.reason;\n    default:\n      throw promise;\n  }\n}, snapCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (target, version, handlePromise = defaultHandlePromise) => {\n  const cache = snapCache.get(target);\n  if ((cache == null ? void 0 : cache[0]) === version) {\n    return cache[1];\n  }\n  const snap = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));\n  (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(snap, true);\n  snapCache.set(target, [version, snap]);\n  Reflect.ownKeys(target).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(snap, key)) {\n      return;\n    }\n    const value = Reflect.get(target, key);\n    const { enumerable } = Reflect.getOwnPropertyDescriptor(\n      target,\n      key\n    );\n    const desc = {\n      value,\n      enumerable,\n      // This is intentional to avoid copying with proxy-compare.\n      // It's still non-writable, so it avoids assigning a value.\n      configurable: true\n    };\n    if (refSet.has(value)) {\n      (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.markToTrack)(value, false);\n    } else if (value instanceof Promise) {\n      delete desc.value;\n      desc.get = () => handlePromise(value);\n    } else if (proxyStateMap.has(value)) {\n      const [target2, ensureVersion] = proxyStateMap.get(\n        value\n      );\n      desc.value = createSnapshot(\n        target2,\n        ensureVersion(),\n        handlePromise\n      );\n    }\n    Object.defineProperty(snap, key, desc);\n  });\n  return Object.preventExtensions(snap);\n}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1, 1], proxyFunction = (initialObject) => {\n  if (!isObject(initialObject)) {\n    throw new Error(\"object required\");\n  }\n  const found = proxyCache.get(initialObject);\n  if (found) {\n    return found;\n  }\n  let version = versionHolder[0];\n  const listeners = /* @__PURE__ */ new Set();\n  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {\n    if (version !== nextVersion) {\n      version = nextVersion;\n      listeners.forEach((listener) => listener(op, nextVersion));\n    }\n  };\n  let checkVersion = versionHolder[1];\n  const ensureVersion = (nextCheckVersion = ++versionHolder[1]) => {\n    if (checkVersion !== nextCheckVersion && !listeners.size) {\n      checkVersion = nextCheckVersion;\n      propProxyStates.forEach(([propProxyState]) => {\n        const propVersion = propProxyState[1](nextCheckVersion);\n        if (propVersion > version) {\n          version = propVersion;\n        }\n      });\n    }\n    return version;\n  };\n  const createPropListener = (prop) => (op, nextVersion) => {\n    const newOp = [...op];\n    newOp[1] = [prop, ...newOp[1]];\n    notifyUpdate(newOp, nextVersion);\n  };\n  const propProxyStates = /* @__PURE__ */ new Map();\n  const addPropListener = (prop, propProxyState) => {\n    if (( false ? 0 : void 0) !== \"production\" && propProxyStates.has(prop)) {\n      throw new Error(\"prop listener already exists\");\n    }\n    if (listeners.size) {\n      const remove = propProxyState[3](createPropListener(prop));\n      propProxyStates.set(prop, [propProxyState, remove]);\n    } else {\n      propProxyStates.set(prop, [propProxyState]);\n    }\n  };\n  const removePropListener = (prop) => {\n    var _a;\n    const entry = propProxyStates.get(prop);\n    if (entry) {\n      propProxyStates.delete(prop);\n      (_a = entry[1]) == null ? void 0 : _a.call(entry);\n    }\n  };\n  const addListener = (listener) => {\n    listeners.add(listener);\n    if (listeners.size === 1) {\n      propProxyStates.forEach(([propProxyState, prevRemove], prop) => {\n        if (( false ? 0 : void 0) !== \"production\" && prevRemove) {\n          throw new Error(\"remove already exists\");\n        }\n        const remove = propProxyState[3](createPropListener(prop));\n        propProxyStates.set(prop, [propProxyState, remove]);\n      });\n    }\n    const removeListener = () => {\n      listeners.delete(listener);\n      if (listeners.size === 0) {\n        propProxyStates.forEach(([propProxyState, remove], prop) => {\n          if (remove) {\n            remove();\n            propProxyStates.set(prop, [propProxyState]);\n          }\n        });\n      }\n    };\n    return removeListener;\n  };\n  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));\n  const handler = {\n    deleteProperty(target, prop) {\n      const prevValue = Reflect.get(target, prop);\n      removePropListener(prop);\n      const deleted = Reflect.deleteProperty(target, prop);\n      if (deleted) {\n        notifyUpdate([\"delete\", [prop], prevValue]);\n      }\n      return deleted;\n    },\n    set(target, prop, value, receiver) {\n      const hasPrevValue = Reflect.has(target, prop);\n      const prevValue = Reflect.get(target, prop, receiver);\n      if (hasPrevValue && (objectIs(prevValue, value) || proxyCache.has(value) && objectIs(prevValue, proxyCache.get(value)))) {\n        return true;\n      }\n      removePropListener(prop);\n      if (isObject(value)) {\n        value = (0,proxy_compare__WEBPACK_IMPORTED_MODULE_0__.getUntracked)(value) || value;\n      }\n      let nextValue = value;\n      if (value instanceof Promise) {\n        value.then((v) => {\n          value.status = \"fulfilled\";\n          value.value = v;\n          notifyUpdate([\"resolve\", [prop], v]);\n        }).catch((e) => {\n          value.status = \"rejected\";\n          value.reason = e;\n          notifyUpdate([\"reject\", [prop], e]);\n        });\n      } else {\n        if (!proxyStateMap.has(value) && canProxy(value)) {\n          nextValue = proxyFunction(value);\n        }\n        const childProxyState = !refSet.has(nextValue) && proxyStateMap.get(nextValue);\n        if (childProxyState) {\n          addPropListener(prop, childProxyState);\n        }\n      }\n      Reflect.set(target, prop, nextValue, receiver);\n      notifyUpdate([\"set\", [prop], value, prevValue]);\n      return true;\n    }\n  };\n  const proxyObject = newProxy(baseObject, handler);\n  proxyCache.set(initialObject, proxyObject);\n  const proxyState = [\n    baseObject,\n    ensureVersion,\n    createSnapshot,\n    addListener\n  ];\n  proxyStateMap.set(proxyObject, proxyState);\n  Reflect.ownKeys(initialObject).forEach((key) => {\n    const desc = Object.getOwnPropertyDescriptor(\n      initialObject,\n      key\n    );\n    if (\"value\" in desc) {\n      proxyObject[key] = initialObject[key];\n      delete desc.value;\n      delete desc.writable;\n    }\n    Object.defineProperty(baseObject, key, desc);\n  });\n  return proxyObject;\n}) => [\n  // public functions\n  proxyFunction,\n  // shared state\n  proxyStateMap,\n  refSet,\n  // internal things\n  objectIs,\n  newProxy,\n  canProxy,\n  defaultHandlePromise,\n  snapCache,\n  createSnapshot,\n  proxyCache,\n  versionHolder\n];\nconst [defaultProxyFunction] = buildProxyFunction();\nfunction proxy(initialObject = {}) {\n  return defaultProxyFunction(initialObject);\n}\nfunction getVersion(proxyObject) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  return proxyState == null ? void 0 : proxyState[1]();\n}\nfunction subscribe(proxyObject, callback, notifyInSync) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  let promise;\n  const ops = [];\n  const addListener = proxyState[3];\n  let isListenerActive = false;\n  const listener = (op) => {\n    ops.push(op);\n    if (notifyInSync) {\n      callback(ops.splice(0));\n      return;\n    }\n    if (!promise) {\n      promise = Promise.resolve().then(() => {\n        promise = void 0;\n        if (isListenerActive) {\n          callback(ops.splice(0));\n        }\n      });\n    }\n  };\n  const removeListener = addListener(listener);\n  isListenerActive = true;\n  return () => {\n    isListenerActive = false;\n    removeListener();\n  };\n}\nfunction snapshot(proxyObject, handlePromise) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if (( false ? 0 : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  const [target, ensureVersion, createSnapshot] = proxyState;\n  return createSnapshot(target, ensureVersion(), handlePromise);\n}\nfunction ref(obj) {\n  refSet.add(obj);\n  return obj;\n}\nconst unstable_buildProxyFunction = buildProxyFunction;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/valtio/esm/vanilla.mjs\n");

/***/ }),

/***/ "(ssr)/../node_modules/valtio/esm/vanilla/utils.mjs":
/*!****************************************************!*\
  !*** ../node_modules/valtio/esm/vanilla/utils.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addComputed: () => (/* binding */ addComputed_DEPRECATED),\n/* harmony export */   derive: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.derive),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   proxyMap: () => (/* binding */ proxyMap),\n/* harmony export */   proxySet: () => (/* binding */ proxySet),\n/* harmony export */   proxyWithComputed: () => (/* binding */ proxyWithComputed_DEPRECATED),\n/* harmony export */   proxyWithHistory: () => (/* binding */ proxyWithHistory_DEPRECATED),\n/* harmony export */   subscribeKey: () => (/* binding */ subscribeKey),\n/* harmony export */   underive: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.underive),\n/* harmony export */   unstable_deriveSubscriptions: () => (/* reexport safe */ derive_valtio__WEBPACK_IMPORTED_MODULE_0__.unstable_deriveSubscriptions),\n/* harmony export */   watch: () => (/* binding */ watch)\n/* harmony export */ });\n/* harmony import */ var valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! valtio/vanilla */ \"(ssr)/../node_modules/valtio/esm/vanilla.mjs\");\n/* harmony import */ var derive_valtio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! derive-valtio */ \"(ssr)/../node_modules/derive-valtio/dist/index.modern.js\");\n\n\n\n\nfunction subscribeKey(proxyObject, key, callback, notifyInSync) {\n  let prevValue = proxyObject[key];\n  return (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(\n    proxyObject,\n    () => {\n      const nextValue = proxyObject[key];\n      if (!Object.is(prevValue, nextValue)) {\n        callback(prevValue = nextValue);\n      }\n    },\n    notifyInSync\n  );\n}\n\nlet currentCleanups;\nfunction watch(callback, options) {\n  let alive = true;\n  const cleanups = /* @__PURE__ */ new Set();\n  const subscriptions = /* @__PURE__ */ new Map();\n  const cleanup = () => {\n    if (alive) {\n      alive = false;\n      cleanups.forEach((clean) => clean());\n      cleanups.clear();\n      subscriptions.forEach((unsubscribe) => unsubscribe());\n      subscriptions.clear();\n    }\n  };\n  const revalidate = async () => {\n    if (!alive) {\n      return;\n    }\n    cleanups.forEach((clean) => clean());\n    cleanups.clear();\n    const proxiesToSubscribe = /* @__PURE__ */ new Set();\n    const parent = currentCleanups;\n    currentCleanups = cleanups;\n    try {\n      const promiseOrPossibleCleanup = callback((proxyObject) => {\n        proxiesToSubscribe.add(proxyObject);\n        if (alive && !subscriptions.has(proxyObject)) {\n          const unsubscribe = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, revalidate, options == null ? void 0 : options.sync);\n          subscriptions.set(proxyObject, unsubscribe);\n        }\n        return proxyObject;\n      });\n      const couldBeCleanup = promiseOrPossibleCleanup && promiseOrPossibleCleanup instanceof Promise ? await promiseOrPossibleCleanup : promiseOrPossibleCleanup;\n      if (couldBeCleanup) {\n        if (alive) {\n          cleanups.add(couldBeCleanup);\n        } else {\n          cleanup();\n        }\n      }\n    } finally {\n      currentCleanups = parent;\n    }\n    subscriptions.forEach((unsubscribe, proxyObject) => {\n      if (!proxiesToSubscribe.has(proxyObject)) {\n        subscriptions.delete(proxyObject);\n        unsubscribe();\n      }\n    });\n  };\n  if (currentCleanups) {\n    currentCleanups.add(cleanup);\n  }\n  revalidate();\n  return cleanup;\n}\n\nconst DEVTOOLS = Symbol();\nfunction devtools(proxyObject, options) {\n  if (typeof options === \"string\") {\n    console.warn(\n      \"string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400\"\n    );\n    options = { name: options };\n  }\n  const { enabled, name = \"\", ...rest } = options || {};\n  let extension;\n  try {\n    extension = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extension) {\n    if (( false ? 0 : void 0) !== \"production\" && enabled) {\n      console.warn(\"[Warning] Please install/enable Redux devtools extension\");\n    }\n    return;\n  }\n  let isTimeTraveling = false;\n  const devtools2 = extension.connect({ name, ...rest });\n  const unsub1 = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, (ops) => {\n    const action = ops.filter(([_, path]) => path[0] !== DEVTOOLS).map(([op, path]) => `${op}:${path.map(String).join(\".\")}`).join(\", \");\n    if (!action) {\n      return;\n    }\n    if (isTimeTraveling) {\n      isTimeTraveling = false;\n    } else {\n      const snapWithoutDevtools = Object.assign({}, (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n      delete snapWithoutDevtools[DEVTOOLS];\n      devtools2.send(\n        {\n          type: action,\n          updatedAt: (/* @__PURE__ */ new Date()).toLocaleString()\n        },\n        snapWithoutDevtools\n      );\n    }\n  });\n  const unsub2 = devtools2.subscribe((message) => {\n    var _a, _b, _c, _d, _e, _f;\n    if (message.type === \"ACTION\" && message.payload) {\n      try {\n        Object.assign(proxyObject, JSON.parse(message.payload));\n      } catch (e) {\n        console.error(\n          \"please dispatch a serializable value that JSON.parse() and proxy() support\\n\",\n          e\n        );\n      }\n    }\n    if (message.type === \"DISPATCH\" && message.state) {\n      if (((_a = message.payload) == null ? void 0 : _a.type) === \"JUMP_TO_ACTION\" || ((_b = message.payload) == null ? void 0 : _b.type) === \"JUMP_TO_STATE\") {\n        isTimeTraveling = true;\n        const state = JSON.parse(message.state);\n        Object.assign(proxyObject, state);\n      }\n      proxyObject[DEVTOOLS] = message;\n    } else if (message.type === \"DISPATCH\" && ((_c = message.payload) == null ? void 0 : _c.type) === \"COMMIT\") {\n      devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n    } else if (message.type === \"DISPATCH\" && ((_d = message.payload) == null ? void 0 : _d.type) === \"IMPORT_STATE\") {\n      const actions = (_e = message.payload.nextLiftedState) == null ? void 0 : _e.actionsById;\n      const computedStates = ((_f = message.payload.nextLiftedState) == null ? void 0 : _f.computedStates) || [];\n      isTimeTraveling = true;\n      computedStates.forEach(({ state }, index) => {\n        const action = actions[index] || \"No action found\";\n        Object.assign(proxyObject, state);\n        if (index === 0) {\n          devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n        } else {\n          devtools2.send(action, (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n        }\n      });\n    }\n  });\n  devtools2.init((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n  return () => {\n    unsub1();\n    unsub2 == null ? void 0 : unsub2();\n  };\n}\n\nfunction addComputed_DEPRECATED(proxyObject, computedFns_FAKE, targetObject = proxyObject) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"addComputed is deprecated. Please consider using `derive`. Falling back to emulation with derive. https://github.com/pmndrs/valtio/pull/201\"\n    );\n  }\n  const derivedFns = {};\n  Object.keys(computedFns_FAKE).forEach((key) => {\n    derivedFns[key] = (get) => computedFns_FAKE[key](get(proxyObject));\n  });\n  return (0,derive_valtio__WEBPACK_IMPORTED_MODULE_0__.derive)(derivedFns, { proxy: targetObject });\n}\n\nfunction proxyWithComputed_DEPRECATED(initialObject, computedFns) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithComputed is deprecated. Please follow \"Computed Properties\" guide in docs.'\n    );\n  }\n  Object.keys(computedFns).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(initialObject, key)) {\n      throw new Error(\"object property already defined\");\n    }\n    const computedFn = computedFns[key];\n    const { get, set } = typeof computedFn === \"function\" ? { get: computedFn } : computedFn;\n    const desc = {};\n    desc.get = () => get((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject));\n    if (set) {\n      desc.set = (newValue) => set(proxyObject, newValue);\n    }\n    Object.defineProperty(initialObject, key, desc);\n  });\n  const proxyObject = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)(initialObject);\n  return proxyObject;\n}\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nlet refSet;\nconst deepClone = (obj) => {\n  if (!refSet) {\n    refSet = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.unstable_buildProxyFunction)()[2];\n  }\n  if (!isObject(obj) || refSet.has(obj)) {\n    return obj;\n  }\n  const baseObject = Array.isArray(obj) ? [] : Object.create(Object.getPrototypeOf(obj));\n  Reflect.ownKeys(obj).forEach((key) => {\n    baseObject[key] = deepClone(obj[key]);\n  });\n  return baseObject;\n};\nfunction proxyWithHistory_DEPRECATED(initialValue, skipSubscribe = false) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      'proxyWithHistory is deprecated. Please use the \"valtio-history\" package; refer to the docs'\n    );\n  }\n  const proxyObject = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    value: initialValue,\n    history: (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.ref)({\n      wip: void 0,\n      // to avoid infinite loop\n      snapshots: [],\n      index: -1\n    }),\n    clone: deepClone,\n    canUndo: () => proxyObject.history.index > 0,\n    undo: () => {\n      if (proxyObject.canUndo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[--proxyObject.history.index]\n        );\n      }\n    },\n    canRedo: () => proxyObject.history.index < proxyObject.history.snapshots.length - 1,\n    redo: () => {\n      if (proxyObject.canRedo()) {\n        proxyObject.value = proxyObject.history.wip = proxyObject.clone(\n          proxyObject.history.snapshots[++proxyObject.history.index]\n        );\n      }\n    },\n    saveHistory: () => {\n      proxyObject.history.snapshots.splice(proxyObject.history.index + 1);\n      proxyObject.history.snapshots.push((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.snapshot)(proxyObject).value);\n      ++proxyObject.history.index;\n    },\n    subscribe: () => (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.subscribe)(proxyObject, (ops) => {\n      if (ops.every(\n        (op) => op[1][0] === \"value\" && (op[0] !== \"set\" || op[2] !== proxyObject.history.wip)\n      )) {\n        proxyObject.saveHistory();\n      }\n    })\n  });\n  proxyObject.saveHistory();\n  if (!skipSubscribe) {\n    proxyObject.subscribe();\n  }\n  return proxyObject;\n}\n\nfunction proxySet(initialValues) {\n  const set = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    data: Array.from(new Set(initialValues)),\n    has(value) {\n      return this.data.indexOf(value) !== -1;\n    },\n    add(value) {\n      let hasProxy = false;\n      if (typeof value === \"object\" && value !== null) {\n        hasProxy = this.data.indexOf((0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)(value)) !== -1;\n      }\n      if (this.data.indexOf(value) === -1 && !hasProxy) {\n        this.data.push(value);\n      }\n      return this;\n    },\n    delete(value) {\n      const index = this.data.indexOf(value);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    forEach(cb) {\n      this.data.forEach((value) => {\n        cb(value, value, this);\n      });\n    },\n    get [Symbol.toStringTag]() {\n      return \"Set\";\n    },\n    toJSON() {\n      return new Set(this.data);\n    },\n    [Symbol.iterator]() {\n      return this.data[Symbol.iterator]();\n    },\n    values() {\n      return this.data.values();\n    },\n    keys() {\n      return this.data.values();\n    },\n    entries() {\n      return new Set(this.data).entries();\n    }\n  });\n  Object.defineProperties(set, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(set);\n  return set;\n}\n\nfunction proxyMap(entries) {\n  const map = (0,valtio_vanilla__WEBPACK_IMPORTED_MODULE_1__.proxy)({\n    data: Array.from(entries || []),\n    has(key) {\n      return this.data.some((p) => p[0] === key);\n    },\n    set(key, value) {\n      const record = this.data.find((p) => p[0] === key);\n      if (record) {\n        record[1] = value;\n      } else {\n        this.data.push([key, value]);\n      }\n      return this;\n    },\n    get(key) {\n      var _a;\n      return (_a = this.data.find((p) => p[0] === key)) == null ? void 0 : _a[1];\n    },\n    delete(key) {\n      const index = this.data.findIndex((p) => p[0] === key);\n      if (index === -1) {\n        return false;\n      }\n      this.data.splice(index, 1);\n      return true;\n    },\n    clear() {\n      this.data.splice(0);\n    },\n    get size() {\n      return this.data.length;\n    },\n    toJSON() {\n      return new Map(this.data);\n    },\n    forEach(cb) {\n      this.data.forEach((p) => {\n        cb(p[1], p[0], this);\n      });\n    },\n    keys() {\n      return this.data.map((p) => p[0]).values();\n    },\n    values() {\n      return this.data.map((p) => p[1]).values();\n    },\n    entries() {\n      return new Map(this.data).entries();\n    },\n    get [Symbol.toStringTag]() {\n      return \"Map\";\n    },\n    [Symbol.iterator]() {\n      return this.entries();\n    }\n  });\n  Object.defineProperties(map, {\n    data: {\n      enumerable: false\n    },\n    size: {\n      enumerable: false\n    },\n    toJSON: {\n      enumerable: false\n    }\n  });\n  Object.seal(map);\n  return map;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/valtio/esm/vanilla/utils.mjs\n");

/***/ })

};
;