"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unstorage";
exports.ids = ["vendor-chunks/unstorage"];
exports.modules = {

/***/ "(ssr)/../node_modules/unstorage/dist/index.mjs":
/*!************************************************!*\
  !*** ../node_modules/unstorage/dist/index.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   builtinDrivers: () => (/* binding */ builtinDrivers),\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   defineDriver: () => (/* binding */ defineDriver),\n/* harmony export */   filterKeyByBase: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   filterKeyByDepth: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   joinKeys: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   normalizeBaseKey: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   normalizeKey: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   prefixStorage: () => (/* reexport safe */ _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   restoreSnapshot: () => (/* binding */ restoreSnapshot),\n/* harmony export */   snapshot: () => (/* binding */ snapshot)\n/* harmony export */ });\n/* harmony import */ var destr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! destr */ \"(ssr)/../node_modules/destr/dist/index.mjs\");\n/* harmony import */ var _shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./shared/unstorage.CoCt7NXC.mjs */ \"(ssr)/../node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs\");\n\n\n\n\nfunction defineDriver(factory) {\n  return factory;\n}\n\nconst DRIVER_NAME = \"memory\";\nconst memory = defineDriver(() => {\n  const data = /* @__PURE__ */ new Map();\n  return {\n    name: DRIVER_NAME,\n    getInstance: () => data,\n    hasItem(key) {\n      return data.has(key);\n    },\n    getItem(key) {\n      return data.get(key) ?? null;\n    },\n    getItemRaw(key) {\n      return data.get(key) ?? null;\n    },\n    setItem(key, value) {\n      data.set(key, value);\n    },\n    setItemRaw(key, value) {\n      data.set(key, value);\n    },\n    removeItem(key) {\n      data.delete(key);\n    },\n    getKeys() {\n      return [...data.keys()];\n    },\n    clear() {\n      data.clear();\n    },\n    dispose() {\n      data.clear();\n    }\n  };\n});\n\nfunction createStorage(options = {}) {\n  const context = {\n    mounts: { \"\": options.driver || memory() },\n    mountpoints: [\"\"],\n    watching: false,\n    watchListeners: [],\n    unwatch: {}\n  };\n  const getMount = (key) => {\n    for (const base of context.mountpoints) {\n      if (key.startsWith(base)) {\n        return {\n          base,\n          relativeKey: key.slice(base.length),\n          driver: context.mounts[base]\n        };\n      }\n    }\n    return {\n      base: \"\",\n      relativeKey: key,\n      driver: context.mounts[\"\"]\n    };\n  };\n  const getMounts = (base, includeParent) => {\n    return context.mountpoints.filter(\n      (mountpoint) => mountpoint.startsWith(base) || includeParent && base.startsWith(mountpoint)\n    ).map((mountpoint) => ({\n      relativeBase: base.length > mountpoint.length ? base.slice(mountpoint.length) : void 0,\n      mountpoint,\n      driver: context.mounts[mountpoint]\n    }));\n  };\n  const onChange = (event, key) => {\n    if (!context.watching) {\n      return;\n    }\n    key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n    for (const listener of context.watchListeners) {\n      listener(event, key);\n    }\n  };\n  const startWatch = async () => {\n    if (context.watching) {\n      return;\n    }\n    context.watching = true;\n    for (const mountpoint in context.mounts) {\n      context.unwatch[mountpoint] = await watch(\n        context.mounts[mountpoint],\n        onChange,\n        mountpoint\n      );\n    }\n  };\n  const stopWatch = async () => {\n    if (!context.watching) {\n      return;\n    }\n    for (const mountpoint in context.unwatch) {\n      await context.unwatch[mountpoint]();\n    }\n    context.unwatch = {};\n    context.watching = false;\n  };\n  const runBatch = (items, commonOptions, cb) => {\n    const batches = /* @__PURE__ */ new Map();\n    const getBatch = (mount) => {\n      let batch = batches.get(mount.base);\n      if (!batch) {\n        batch = {\n          driver: mount.driver,\n          base: mount.base,\n          items: []\n        };\n        batches.set(mount.base, batch);\n      }\n      return batch;\n    };\n    for (const item of items) {\n      const isStringItem = typeof item === \"string\";\n      const key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(isStringItem ? item : item.key);\n      const value = isStringItem ? void 0 : item.value;\n      const options2 = isStringItem || !item.options ? commonOptions : { ...commonOptions, ...item.options };\n      const mount = getMount(key);\n      getBatch(mount).items.push({\n        key,\n        value,\n        relativeKey: mount.relativeKey,\n        options: options2\n      });\n    }\n    return Promise.all([...batches.values()].map((batch) => cb(batch))).then(\n      (r) => r.flat()\n    );\n  };\n  const storage = {\n    // Item\n    hasItem(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.hasItem, relativeKey, opts);\n    },\n    getItem(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItem, relativeKey, opts).then(\n        (value) => (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value)\n      );\n    },\n    getItems(items, commonOptions = {}) {\n      return runBatch(items, commonOptions, (batch) => {\n        if (batch.driver.getItems) {\n          return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n            batch.driver.getItems,\n            batch.items.map((item) => ({\n              key: item.relativeKey,\n              options: item.options\n            })),\n            commonOptions\n          ).then(\n            (r) => r.map((item) => ({\n              key: (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.j)(batch.base, item.key),\n              value: (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(item.value)\n            }))\n          );\n        }\n        return Promise.all(\n          batch.items.map((item) => {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n              batch.driver.getItem,\n              item.relativeKey,\n              item.options\n            ).then((value) => ({\n              key: item.key,\n              value: (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value)\n            }));\n          })\n        );\n      });\n    },\n    getItemRaw(key, opts = {}) {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (driver.getItemRaw) {\n        return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItemRaw, relativeKey, opts);\n      }\n      return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getItem, relativeKey, opts).then(\n        (value) => (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(value)\n      );\n    },\n    async setItem(key, value, opts = {}) {\n      if (value === void 0) {\n        return storage.removeItem(key);\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (!driver.setItem) {\n        return;\n      }\n      await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItem, relativeKey, (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(value), opts);\n      if (!driver.watch) {\n        onChange(\"update\", key);\n      }\n    },\n    async setItems(items, commonOptions) {\n      await runBatch(items, commonOptions, async (batch) => {\n        if (batch.driver.setItems) {\n          return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n            batch.driver.setItems,\n            batch.items.map((item) => ({\n              key: item.relativeKey,\n              value: (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(item.value),\n              options: item.options\n            })),\n            commonOptions\n          );\n        }\n        if (!batch.driver.setItem) {\n          return;\n        }\n        await Promise.all(\n          batch.items.map((item) => {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n              batch.driver.setItem,\n              item.relativeKey,\n              (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(item.value),\n              item.options\n            );\n          })\n        );\n      });\n    },\n    async setItemRaw(key, value, opts = {}) {\n      if (value === void 0) {\n        return storage.removeItem(key, opts);\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (driver.setItemRaw) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItemRaw, relativeKey, value, opts);\n      } else if (driver.setItem) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.setItem, relativeKey, (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(value), opts);\n      } else {\n        return;\n      }\n      if (!driver.watch) {\n        onChange(\"update\", key);\n      }\n    },\n    async removeItem(key, opts = {}) {\n      if (typeof opts === \"boolean\") {\n        opts = { removeMeta: opts };\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      if (!driver.removeItem) {\n        return;\n      }\n      await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.removeItem, relativeKey, opts);\n      if (opts.removeMeta || opts.removeMata) {\n        await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.removeItem, relativeKey + \"$\", opts);\n      }\n      if (!driver.watch) {\n        onChange(\"remove\", key);\n      }\n    },\n    // Meta\n    async getMeta(key, opts = {}) {\n      if (typeof opts === \"boolean\") {\n        opts = { nativeOnly: opts };\n      }\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n      const { relativeKey, driver } = getMount(key);\n      const meta = /* @__PURE__ */ Object.create(null);\n      if (driver.getMeta) {\n        Object.assign(meta, await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.getMeta, relativeKey, opts));\n      }\n      if (!opts.nativeOnly) {\n        const value = await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n          driver.getItem,\n          relativeKey + \"$\",\n          opts\n        ).then((value_) => (0,destr__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(value_));\n        if (value && typeof value === \"object\") {\n          if (typeof value.atime === \"string\") {\n            value.atime = new Date(value.atime);\n          }\n          if (typeof value.mtime === \"string\") {\n            value.mtime = new Date(value.mtime);\n          }\n          Object.assign(meta, value);\n        }\n      }\n      return meta;\n    },\n    setMeta(key, value, opts = {}) {\n      return this.setItem(key + \"$\", value, opts);\n    },\n    removeMeta(key, opts = {}) {\n      return this.removeItem(key + \"$\", opts);\n    },\n    // Keys\n    async getKeys(base, opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      const mounts = getMounts(base, true);\n      let maskedMounts = [];\n      const allKeys = [];\n      let allMountsSupportMaxDepth = true;\n      for (const mount of mounts) {\n        if (!mount.driver.flags?.maxDepth) {\n          allMountsSupportMaxDepth = false;\n        }\n        const rawKeys = await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(\n          mount.driver.getKeys,\n          mount.relativeBase,\n          opts\n        );\n        for (const key of rawKeys) {\n          const fullKey = mount.mountpoint + (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key);\n          if (!maskedMounts.some((p) => fullKey.startsWith(p))) {\n            allKeys.push(fullKey);\n          }\n        }\n        maskedMounts = [\n          mount.mountpoint,\n          ...maskedMounts.filter((p) => !p.startsWith(mount.mountpoint))\n        ];\n      }\n      const shouldFilterByDepth = opts.maxDepth !== void 0 && !allMountsSupportMaxDepth;\n      return allKeys.filter(\n        (key) => (!shouldFilterByDepth || (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(key, opts.maxDepth)) && (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.c)(key, base)\n      );\n    },\n    // Utils\n    async clear(base, opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      await Promise.all(\n        getMounts(base, false).map(async (m) => {\n          if (m.driver.clear) {\n            return (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(m.driver.clear, m.relativeBase, opts);\n          }\n          if (m.driver.removeItem) {\n            const keys = await m.driver.getKeys(m.relativeBase || \"\", opts);\n            return Promise.all(\n              keys.map((key) => m.driver.removeItem(key, opts))\n            );\n          }\n        })\n      );\n    },\n    async dispose() {\n      await Promise.all(\n        Object.values(context.mounts).map((driver) => dispose(driver))\n      );\n    },\n    async watch(callback) {\n      await startWatch();\n      context.watchListeners.push(callback);\n      return async () => {\n        context.watchListeners = context.watchListeners.filter(\n          (listener) => listener !== callback\n        );\n        if (context.watchListeners.length === 0) {\n          await stopWatch();\n        }\n      };\n    },\n    async unwatch() {\n      context.watchListeners = [];\n      await stopWatch();\n    },\n    // Mount\n    mount(base, driver) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      if (base && context.mounts[base]) {\n        throw new Error(`already mounted at ${base}`);\n      }\n      if (base) {\n        context.mountpoints.push(base);\n        context.mountpoints.sort((a, b) => b.length - a.length);\n      }\n      context.mounts[base] = driver;\n      if (context.watching) {\n        Promise.resolve(watch(driver, onChange, base)).then((unwatcher) => {\n          context.unwatch[base] = unwatcher;\n        }).catch(console.error);\n      }\n      return storage;\n    },\n    async unmount(base, _dispose = true) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n      if (!base || !context.mounts[base]) {\n        return;\n      }\n      if (context.watching && base in context.unwatch) {\n        context.unwatch[base]?.();\n        delete context.unwatch[base];\n      }\n      if (_dispose) {\n        await dispose(context.mounts[base]);\n      }\n      context.mountpoints = context.mountpoints.filter((key) => key !== base);\n      delete context.mounts[base];\n    },\n    getMount(key = \"\") {\n      key = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(key) + \":\";\n      const m = getMount(key);\n      return {\n        driver: m.driver,\n        base: m.base\n      };\n    },\n    getMounts(base = \"\", opts = {}) {\n      base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(base);\n      const mounts = getMounts(base, opts.parents);\n      return mounts.map((m) => ({\n        driver: m.driver,\n        base: m.mountpoint\n      }));\n    },\n    // Aliases\n    keys: (base, opts = {}) => storage.getKeys(base, opts),\n    get: (key, opts = {}) => storage.getItem(key, opts),\n    set: (key, value, opts = {}) => storage.setItem(key, value, opts),\n    has: (key, opts = {}) => storage.hasItem(key, opts),\n    del: (key, opts = {}) => storage.removeItem(key, opts),\n    remove: (key, opts = {}) => storage.removeItem(key, opts)\n  };\n  return storage;\n}\nasync function snapshot(storage, base) {\n  base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n  const keys = await storage.getKeys(base);\n  const snapshot2 = {};\n  await Promise.all(\n    keys.map(async (key) => {\n      snapshot2[key.slice(base.length)] = await storage.getItem(key);\n    })\n  );\n  return snapshot2;\n}\nasync function restoreSnapshot(driver, snapshot2, base = \"\") {\n  base = (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.n)(base);\n  await Promise.all(\n    Object.entries(snapshot2).map((e) => driver.setItem(base + e[0], e[1]))\n  );\n}\nfunction watch(driver, onChange, base) {\n  return driver.watch ? driver.watch((event, key) => onChange(event, base + key)) : () => {\n  };\n}\nasync function dispose(driver) {\n  if (typeof driver.dispose === \"function\") {\n    await (0,_shared_unstorage_CoCt7NXC_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(driver.dispose);\n  }\n}\n\nconst builtinDrivers = {\n  \"azure-app-configuration\": \"unstorage/drivers/azure-app-configuration\",\n  \"azureAppConfiguration\": \"unstorage/drivers/azure-app-configuration\",\n  \"azure-cosmos\": \"unstorage/drivers/azure-cosmos\",\n  \"azureCosmos\": \"unstorage/drivers/azure-cosmos\",\n  \"azure-key-vault\": \"unstorage/drivers/azure-key-vault\",\n  \"azureKeyVault\": \"unstorage/drivers/azure-key-vault\",\n  \"azure-storage-blob\": \"unstorage/drivers/azure-storage-blob\",\n  \"azureStorageBlob\": \"unstorage/drivers/azure-storage-blob\",\n  \"azure-storage-table\": \"unstorage/drivers/azure-storage-table\",\n  \"azureStorageTable\": \"unstorage/drivers/azure-storage-table\",\n  \"capacitor-preferences\": \"unstorage/drivers/capacitor-preferences\",\n  \"capacitorPreferences\": \"unstorage/drivers/capacitor-preferences\",\n  \"cloudflare-kv-binding\": \"unstorage/drivers/cloudflare-kv-binding\",\n  \"cloudflareKVBinding\": \"unstorage/drivers/cloudflare-kv-binding\",\n  \"cloudflare-kv-http\": \"unstorage/drivers/cloudflare-kv-http\",\n  \"cloudflareKVHttp\": \"unstorage/drivers/cloudflare-kv-http\",\n  \"cloudflare-r2-binding\": \"unstorage/drivers/cloudflare-r2-binding\",\n  \"cloudflareR2Binding\": \"unstorage/drivers/cloudflare-r2-binding\",\n  \"db0\": \"unstorage/drivers/db0\",\n  \"deno-kv-node\": \"unstorage/drivers/deno-kv-node\",\n  \"denoKVNode\": \"unstorage/drivers/deno-kv-node\",\n  \"deno-kv\": \"unstorage/drivers/deno-kv\",\n  \"denoKV\": \"unstorage/drivers/deno-kv\",\n  \"fs-lite\": \"unstorage/drivers/fs-lite\",\n  \"fsLite\": \"unstorage/drivers/fs-lite\",\n  \"fs\": \"unstorage/drivers/fs\",\n  \"github\": \"unstorage/drivers/github\",\n  \"http\": \"unstorage/drivers/http\",\n  \"indexedb\": \"unstorage/drivers/indexedb\",\n  \"localstorage\": \"unstorage/drivers/localstorage\",\n  \"lru-cache\": \"unstorage/drivers/lru-cache\",\n  \"lruCache\": \"unstorage/drivers/lru-cache\",\n  \"memory\": \"unstorage/drivers/memory\",\n  \"mongodb\": \"unstorage/drivers/mongodb\",\n  \"netlify-blobs\": \"unstorage/drivers/netlify-blobs\",\n  \"netlifyBlobs\": \"unstorage/drivers/netlify-blobs\",\n  \"null\": \"unstorage/drivers/null\",\n  \"overlay\": \"unstorage/drivers/overlay\",\n  \"planetscale\": \"unstorage/drivers/planetscale\",\n  \"redis\": \"unstorage/drivers/redis\",\n  \"s3\": \"unstorage/drivers/s3\",\n  \"session-storage\": \"unstorage/drivers/session-storage\",\n  \"sessionStorage\": \"unstorage/drivers/session-storage\",\n  \"uploadthing\": \"unstorage/drivers/uploadthing\",\n  \"upstash\": \"unstorage/drivers/upstash\",\n  \"vercel-blob\": \"unstorage/drivers/vercel-blob\",\n  \"vercelBlob\": \"unstorage/drivers/vercel-blob\",\n  \"vercel-kv\": \"unstorage/drivers/vercel-kv\",\n  \"vercelKV\": \"unstorage/drivers/vercel-kv\"\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/unstorage/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs":
/*!********************************************************************!*\
  !*** ../node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ normalizeKey),\n/* harmony export */   b: () => (/* binding */ asyncCall),\n/* harmony export */   c: () => (/* binding */ filterKeyByBase),\n/* harmony export */   d: () => (/* binding */ stringify),\n/* harmony export */   e: () => (/* binding */ deserializeRaw),\n/* harmony export */   f: () => (/* binding */ filterKeyByDepth),\n/* harmony export */   j: () => (/* binding */ joinKeys),\n/* harmony export */   n: () => (/* binding */ normalizeBaseKey),\n/* harmony export */   p: () => (/* binding */ prefixStorage),\n/* harmony export */   s: () => (/* binding */ serializeRaw)\n/* harmony export */ });\nfunction wrapToPromise(value) {\n  if (!value || typeof value.then !== \"function\") {\n    return Promise.resolve(value);\n  }\n  return value;\n}\nfunction asyncCall(function_, ...arguments_) {\n  try {\n    return wrapToPromise(function_(...arguments_));\n  } catch (error) {\n    return Promise.reject(error);\n  }\n}\nfunction isPrimitive(value) {\n  const type = typeof value;\n  return value === null || type !== \"object\" && type !== \"function\";\n}\nfunction isPureObject(value) {\n  const proto = Object.getPrototypeOf(value);\n  return !proto || proto.isPrototypeOf(Object);\n}\nfunction stringify(value) {\n  if (isPrimitive(value)) {\n    return String(value);\n  }\n  if (isPureObject(value) || Array.isArray(value)) {\n    return JSON.stringify(value);\n  }\n  if (typeof value.toJSON === \"function\") {\n    return stringify(value.toJSON());\n  }\n  throw new Error(\"[unstorage] Cannot stringify value!\");\n}\nconst BASE64_PREFIX = \"base64:\";\nfunction serializeRaw(value) {\n  if (typeof value === \"string\") {\n    return value;\n  }\n  return BASE64_PREFIX + base64Encode(value);\n}\nfunction deserializeRaw(value) {\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  if (!value.startsWith(BASE64_PREFIX)) {\n    return value;\n  }\n  return base64Decode(value.slice(BASE64_PREFIX.length));\n}\nfunction base64Decode(input) {\n  if (globalThis.Buffer) {\n    return Buffer.from(input, \"base64\");\n  }\n  return Uint8Array.from(\n    globalThis.atob(input),\n    (c) => c.codePointAt(0)\n  );\n}\nfunction base64Encode(input) {\n  if (globalThis.Buffer) {\n    return Buffer.from(input).toString(\"base64\");\n  }\n  return globalThis.btoa(String.fromCodePoint(...input));\n}\n\nconst storageKeyProperties = [\n  \"has\",\n  \"hasItem\",\n  \"get\",\n  \"getItem\",\n  \"getItemRaw\",\n  \"set\",\n  \"setItem\",\n  \"setItemRaw\",\n  \"del\",\n  \"remove\",\n  \"removeItem\",\n  \"getMeta\",\n  \"setMeta\",\n  \"removeMeta\",\n  \"getKeys\",\n  \"clear\",\n  \"mount\",\n  \"unmount\"\n];\nfunction prefixStorage(storage, base) {\n  base = normalizeBaseKey(base);\n  if (!base) {\n    return storage;\n  }\n  const nsStorage = { ...storage };\n  for (const property of storageKeyProperties) {\n    nsStorage[property] = (key = \"\", ...args) => (\n      // @ts-ignore\n      storage[property](base + key, ...args)\n    );\n  }\n  nsStorage.getKeys = (key = \"\", ...arguments_) => storage.getKeys(base + key, ...arguments_).then((keys) => keys.map((key2) => key2.slice(base.length)));\n  nsStorage.getItems = async (items, commonOptions) => {\n    const prefixedItems = items.map(\n      (item) => typeof item === \"string\" ? base + item : { ...item, key: base + item.key }\n    );\n    const results = await storage.getItems(prefixedItems, commonOptions);\n    return results.map((entry) => ({\n      key: entry.key.slice(base.length),\n      value: entry.value\n    }));\n  };\n  nsStorage.setItems = async (items, commonOptions) => {\n    const prefixedItems = items.map((item) => ({\n      key: base + item.key,\n      value: item.value,\n      options: item.options\n    }));\n    return storage.setItems(prefixedItems, commonOptions);\n  };\n  return nsStorage;\n}\nfunction normalizeKey(key) {\n  if (!key) {\n    return \"\";\n  }\n  return key.split(\"?\")[0]?.replace(/[/\\\\]/g, \":\").replace(/:+/g, \":\").replace(/^:|:$/g, \"\") || \"\";\n}\nfunction joinKeys(...keys) {\n  return normalizeKey(keys.join(\":\"));\n}\nfunction normalizeBaseKey(base) {\n  base = normalizeKey(base);\n  return base ? base + \":\" : \"\";\n}\nfunction filterKeyByDepth(key, depth) {\n  if (depth === void 0) {\n    return true;\n  }\n  let substrCount = 0;\n  let index = key.indexOf(\":\");\n  while (index > -1) {\n    substrCount++;\n    index = key.indexOf(\":\", index + 1);\n  }\n  return substrCount <= depth;\n}\nfunction filterKeyByBase(key, base) {\n  if (base) {\n    return key.startsWith(base) && key[key.length - 1] !== \"$\";\n  }\n  return key[key.length - 1] !== \"$\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/unstorage/dist/shared/unstorage.CoCt7NXC.mjs\n");

/***/ })

};
;