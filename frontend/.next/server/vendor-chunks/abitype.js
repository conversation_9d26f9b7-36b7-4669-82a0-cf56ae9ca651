"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/abitype";
exports.ids = ["vendor-chunks/abitype"];
exports.modules = {

/***/ "(ssr)/../node_modules/abitype/dist/cjs/errors.js":
/*!**************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/errors.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BaseError = void 0;\nconst version_js_1 = __webpack_require__(/*! ./version.js */ \"(ssr)/../node_modules/abitype/dist/cjs/version.js\");\nclass BaseError extends Error {\n    constructor(shortMessage, args = {}) {\n        const details = args.cause instanceof BaseError\n            ? args.cause.details\n            : args.cause?.message\n                ? args.cause.message\n                : args.details;\n        const docsPath = args.cause instanceof BaseError\n            ? args.cause.docsPath || args.docsPath\n            : args.docsPath;\n        const message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n            ...(docsPath ? [`Docs: https://abitype.dev${docsPath}`] : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: abitype@${version_js_1.version}`,\n        ].join('\\n');\n        super(message);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiTypeError'\n        });\n        if (args.cause)\n            this.cause = args.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = args.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n}\nexports.BaseError = BaseError;\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/errors.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/exports/index.js":
/*!*********************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/exports/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CircularReferenceError = exports.InvalidParenthesisError = exports.UnknownSignatureError = exports.InvalidSignatureError = exports.InvalidStructSignatureError = exports.InvalidAbiParameterError = exports.InvalidAbiParametersError = exports.InvalidParameterError = exports.SolidityProtectedKeywordError = exports.InvalidModifierError = exports.InvalidFunctionModifierError = exports.InvalidAbiTypeParameterError = exports.UnknownSolidityTypeError = exports.InvalidAbiItemError = exports.UnknownTypeError = exports.parseAbiParameters = exports.parseAbiParameter = exports.parseAbiItem = exports.parseAbi = exports.formatAbiParameters = exports.formatAbiParameter = exports.formatAbiItem = exports.formatAbi = exports.narrow = exports.BaseError = void 0;\nvar errors_js_1 = __webpack_require__(/*! ../errors.js */ \"(ssr)/../node_modules/abitype/dist/cjs/errors.js\");\nObject.defineProperty(exports, \"BaseError\", ({ enumerable: true, get: function () { return errors_js_1.BaseError; } }));\nvar narrow_js_1 = __webpack_require__(/*! ../narrow.js */ \"(ssr)/../node_modules/abitype/dist/cjs/narrow.js\");\nObject.defineProperty(exports, \"narrow\", ({ enumerable: true, get: function () { return narrow_js_1.narrow; } }));\nvar formatAbi_js_1 = __webpack_require__(/*! ../human-readable/formatAbi.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbi.js\");\nObject.defineProperty(exports, \"formatAbi\", ({ enumerable: true, get: function () { return formatAbi_js_1.formatAbi; } }));\nvar formatAbiItem_js_1 = __webpack_require__(/*! ../human-readable/formatAbiItem.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js\");\nObject.defineProperty(exports, \"formatAbiItem\", ({ enumerable: true, get: function () { return formatAbiItem_js_1.formatAbiItem; } }));\nvar formatAbiParameter_js_1 = __webpack_require__(/*! ../human-readable/formatAbiParameter.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js\");\nObject.defineProperty(exports, \"formatAbiParameter\", ({ enumerable: true, get: function () { return formatAbiParameter_js_1.formatAbiParameter; } }));\nvar formatAbiParameters_js_1 = __webpack_require__(/*! ../human-readable/formatAbiParameters.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js\");\nObject.defineProperty(exports, \"formatAbiParameters\", ({ enumerable: true, get: function () { return formatAbiParameters_js_1.formatAbiParameters; } }));\nvar parseAbi_js_1 = __webpack_require__(/*! ../human-readable/parseAbi.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbi.js\");\nObject.defineProperty(exports, \"parseAbi\", ({ enumerable: true, get: function () { return parseAbi_js_1.parseAbi; } }));\nvar parseAbiItem_js_1 = __webpack_require__(/*! ../human-readable/parseAbiItem.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiItem.js\");\nObject.defineProperty(exports, \"parseAbiItem\", ({ enumerable: true, get: function () { return parseAbiItem_js_1.parseAbiItem; } }));\nvar parseAbiParameter_js_1 = __webpack_require__(/*! ../human-readable/parseAbiParameter.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiParameter.js\");\nObject.defineProperty(exports, \"parseAbiParameter\", ({ enumerable: true, get: function () { return parseAbiParameter_js_1.parseAbiParameter; } }));\nvar parseAbiParameters_js_1 = __webpack_require__(/*! ../human-readable/parseAbiParameters.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiParameters.js\");\nObject.defineProperty(exports, \"parseAbiParameters\", ({ enumerable: true, get: function () { return parseAbiParameters_js_1.parseAbiParameters; } }));\nvar abiItem_js_1 = __webpack_require__(/*! ../human-readable/errors/abiItem.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\");\nObject.defineProperty(exports, \"UnknownTypeError\", ({ enumerable: true, get: function () { return abiItem_js_1.UnknownTypeError; } }));\nObject.defineProperty(exports, \"InvalidAbiItemError\", ({ enumerable: true, get: function () { return abiItem_js_1.InvalidAbiItemError; } }));\nObject.defineProperty(exports, \"UnknownSolidityTypeError\", ({ enumerable: true, get: function () { return abiItem_js_1.UnknownSolidityTypeError; } }));\nvar abiParameter_js_1 = __webpack_require__(/*! ../human-readable/errors/abiParameter.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nObject.defineProperty(exports, \"InvalidAbiTypeParameterError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidAbiTypeParameterError; } }));\nObject.defineProperty(exports, \"InvalidFunctionModifierError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidFunctionModifierError; } }));\nObject.defineProperty(exports, \"InvalidModifierError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidModifierError; } }));\nObject.defineProperty(exports, \"SolidityProtectedKeywordError\", ({ enumerable: true, get: function () { return abiParameter_js_1.SolidityProtectedKeywordError; } }));\nObject.defineProperty(exports, \"InvalidParameterError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidParameterError; } }));\nObject.defineProperty(exports, \"InvalidAbiParametersError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidAbiParametersError; } }));\nObject.defineProperty(exports, \"InvalidAbiParameterError\", ({ enumerable: true, get: function () { return abiParameter_js_1.InvalidAbiParameterError; } }));\nvar signature_js_1 = __webpack_require__(/*! ../human-readable/errors/signature.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/signature.js\");\nObject.defineProperty(exports, \"InvalidStructSignatureError\", ({ enumerable: true, get: function () { return signature_js_1.InvalidStructSignatureError; } }));\nObject.defineProperty(exports, \"InvalidSignatureError\", ({ enumerable: true, get: function () { return signature_js_1.InvalidSignatureError; } }));\nObject.defineProperty(exports, \"UnknownSignatureError\", ({ enumerable: true, get: function () { return signature_js_1.UnknownSignatureError; } }));\nvar splitParameters_js_1 = __webpack_require__(/*! ../human-readable/errors/splitParameters.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js\");\nObject.defineProperty(exports, \"InvalidParenthesisError\", ({ enumerable: true, get: function () { return splitParameters_js_1.InvalidParenthesisError; } }));\nvar struct_js_1 = __webpack_require__(/*! ../human-readable/errors/struct.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/struct.js\");\nObject.defineProperty(exports, \"CircularReferenceError\", ({ enumerable: true, get: function () { return struct_js_1.CircularReferenceError; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/exports/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js":
/*!*************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UnknownSolidityTypeError = exports.UnknownTypeError = exports.InvalidAbiItemError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/cjs/errors.js\");\nclass InvalidAbiItemError extends errors_js_1.BaseError {\n    constructor({ signature }) {\n        super('Failed to parse ABI item.', {\n            details: `parseAbiItem(${JSON.stringify(signature, null, 2)})`,\n            docsPath: '/api/human#parseabiitem-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiItemError'\n        });\n    }\n}\nexports.InvalidAbiItemError = InvalidAbiItemError;\nclass UnknownTypeError extends errors_js_1.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [\n                `Type \"${type}\" is not a valid ABI type. Perhaps you forgot to include a struct signature?`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownTypeError'\n        });\n    }\n}\nexports.UnknownTypeError = UnknownTypeError;\nclass UnknownSolidityTypeError extends errors_js_1.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [`Type \"${type}\" is not a valid ABI type.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSolidityTypeError'\n        });\n    }\n}\nexports.UnknownSolidityTypeError = UnknownSolidityTypeError;\n//# sourceMappingURL=abiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js":
/*!******************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidAbiTypeParameterError = exports.InvalidFunctionModifierError = exports.InvalidModifierError = exports.SolidityProtectedKeywordError = exports.InvalidParameterError = exports.InvalidAbiParametersError = exports.InvalidAbiParameterError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/cjs/errors.js\");\nclass InvalidAbiParameterError extends errors_js_1.BaseError {\n    constructor({ param }) {\n        super('Failed to parse ABI parameter.', {\n            details: `parseAbiParameter(${JSON.stringify(param, null, 2)})`,\n            docsPath: '/api/human#parseabiparameter-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParameterError'\n        });\n    }\n}\nexports.InvalidAbiParameterError = InvalidAbiParameterError;\nclass InvalidAbiParametersError extends errors_js_1.BaseError {\n    constructor({ params }) {\n        super('Failed to parse ABI parameters.', {\n            details: `parseAbiParameters(${JSON.stringify(params, null, 2)})`,\n            docsPath: '/api/human#parseabiparameters-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParametersError'\n        });\n    }\n}\nexports.InvalidAbiParametersError = InvalidAbiParametersError;\nclass InvalidParameterError extends errors_js_1.BaseError {\n    constructor({ param }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParameterError'\n        });\n    }\n}\nexports.InvalidParameterError = InvalidParameterError;\nclass SolidityProtectedKeywordError extends errors_js_1.BaseError {\n    constructor({ param, name }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `\"${name}\" is a protected Solidity keyword. More info: https://docs.soliditylang.org/en/latest/cheatsheet.html`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SolidityProtectedKeywordError'\n        });\n    }\n}\nexports.SolidityProtectedKeywordError = SolidityProtectedKeywordError;\nclass InvalidModifierError extends errors_js_1.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidModifierError'\n        });\n    }\n}\nexports.InvalidModifierError = InvalidModifierError;\nclass InvalidFunctionModifierError extends errors_js_1.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n                `Data location can only be specified for array, struct, or mapping types, but \"${modifier}\" was given.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidFunctionModifierError'\n        });\n    }\n}\nexports.InvalidFunctionModifierError = InvalidFunctionModifierError;\nclass InvalidAbiTypeParameterError extends errors_js_1.BaseError {\n    constructor({ abiParameter, }) {\n        super('Invalid ABI parameter.', {\n            details: JSON.stringify(abiParameter, null, 2),\n            metaMessages: ['ABI parameter type is invalid.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiTypeParameterError'\n        });\n    }\n}\nexports.InvalidAbiTypeParameterError = InvalidAbiTypeParameterError;\n//# sourceMappingURL=abiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/signature.js":
/*!***************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/errors/signature.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidStructSignatureError = exports.UnknownSignatureError = exports.InvalidSignatureError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/cjs/errors.js\");\nclass InvalidSignatureError extends errors_js_1.BaseError {\n    constructor({ signature, type, }) {\n        super(`Invalid ${type} signature.`, {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidSignatureError'\n        });\n    }\n}\nexports.InvalidSignatureError = InvalidSignatureError;\nclass UnknownSignatureError extends errors_js_1.BaseError {\n    constructor({ signature }) {\n        super('Unknown signature.', {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSignatureError'\n        });\n    }\n}\nexports.UnknownSignatureError = UnknownSignatureError;\nclass InvalidStructSignatureError extends errors_js_1.BaseError {\n    constructor({ signature }) {\n        super('Invalid struct signature.', {\n            details: signature,\n            metaMessages: ['No properties exist.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidStructSignatureError'\n        });\n    }\n}\nexports.InvalidStructSignatureError = InvalidStructSignatureError;\n//# sourceMappingURL=signature.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/signature.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InvalidParenthesisError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/cjs/errors.js\");\nclass InvalidParenthesisError extends errors_js_1.BaseError {\n    constructor({ current, depth }) {\n        super('Unbalanced parentheses.', {\n            metaMessages: [\n                `\"${current.trim()}\" has too many ${depth > 0 ? 'opening' : 'closing'} parentheses.`,\n            ],\n            details: `Depth \"${depth}\"`,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParenthesisError'\n        });\n    }\n}\nexports.InvalidParenthesisError = InvalidParenthesisError;\n//# sourceMappingURL=splitParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvaHVtYW4tcmVhZGFibGUvZXJyb3JzL3NwbGl0UGFyYW1ldGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCwrQkFBK0I7QUFDL0Isb0JBQW9CLG1CQUFPLENBQUMseUVBQWlCO0FBQzdDO0FBQ0Esa0JBQWtCLGdCQUFnQjtBQUNsQztBQUNBO0FBQ0Esb0JBQW9CLGVBQWUsaUJBQWlCLG1DQUFtQztBQUN2RjtBQUNBLCtCQUErQixNQUFNO0FBQ3JDLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3BsaXRQYXJhbWV0ZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5JbnZhbGlkUGFyZW50aGVzaXNFcnJvciA9IHZvaWQgMDtcbmNvbnN0IGVycm9yc19qc18xID0gcmVxdWlyZShcIi4uLy4uL2Vycm9ycy5qc1wiKTtcbmNsYXNzIEludmFsaWRQYXJlbnRoZXNpc0Vycm9yIGV4dGVuZHMgZXJyb3JzX2pzXzEuQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IGN1cnJlbnQsIGRlcHRoIH0pIHtcbiAgICAgICAgc3VwZXIoJ1VuYmFsYW5jZWQgcGFyZW50aGVzZXMuJywge1xuICAgICAgICAgICAgbWV0YU1lc3NhZ2VzOiBbXG4gICAgICAgICAgICAgICAgYFwiJHtjdXJyZW50LnRyaW0oKX1cIiBoYXMgdG9vIG1hbnkgJHtkZXB0aCA+IDAgPyAnb3BlbmluZycgOiAnY2xvc2luZyd9IHBhcmVudGhlc2VzLmAsXG4gICAgICAgICAgICBdLFxuICAgICAgICAgICAgZGV0YWlsczogYERlcHRoIFwiJHtkZXB0aH1cImAsXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnSW52YWxpZFBhcmVudGhlc2lzRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydHMuSW52YWxpZFBhcmVudGhlc2lzRXJyb3IgPSBJbnZhbGlkUGFyZW50aGVzaXNFcnJvcjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNwbGl0UGFyYW1ldGVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/struct.js":
/*!************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/errors/struct.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CircularReferenceError = void 0;\nconst errors_js_1 = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/cjs/errors.js\");\nclass CircularReferenceError extends errors_js_1.BaseError {\n    constructor({ type }) {\n        super('Circular reference detected.', {\n            metaMessages: [`Struct \"${type}\" is a circular reference.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'CircularReferenceError'\n        });\n    }\n}\nexports.CircularReferenceError = CircularReferenceError;\n//# sourceMappingURL=struct.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvaHVtYW4tcmVhZGFibGUvZXJyb3JzL3N0cnVjdC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw4QkFBOEI7QUFDOUIsb0JBQW9CLG1CQUFPLENBQUMseUVBQWlCO0FBQzdDO0FBQ0Esa0JBQWtCLE1BQU07QUFDeEI7QUFDQSxzQ0FBc0MsS0FBSztBQUMzQyxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvaHVtYW4tcmVhZGFibGUvZXJyb3JzL3N0cnVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQ2lyY3VsYXJSZWZlcmVuY2VFcnJvciA9IHZvaWQgMDtcbmNvbnN0IGVycm9yc19qc18xID0gcmVxdWlyZShcIi4uLy4uL2Vycm9ycy5qc1wiKTtcbmNsYXNzIENpcmN1bGFyUmVmZXJlbmNlRXJyb3IgZXh0ZW5kcyBlcnJvcnNfanNfMS5CYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHsgdHlwZSB9KSB7XG4gICAgICAgIHN1cGVyKCdDaXJjdWxhciByZWZlcmVuY2UgZGV0ZWN0ZWQuJywge1xuICAgICAgICAgICAgbWV0YU1lc3NhZ2VzOiBbYFN0cnVjdCBcIiR7dHlwZX1cIiBpcyBhIGNpcmN1bGFyIHJlZmVyZW5jZS5gXSxcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdDaXJjdWxhclJlZmVyZW5jZUVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnRzLkNpcmN1bGFyUmVmZXJlbmNlRXJyb3IgPSBDaXJjdWxhclJlZmVyZW5jZUVycm9yO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RydWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/struct.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbi.js":
/*!********************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/formatAbi.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatAbi = formatAbi;\nconst formatAbiItem_js_1 = __webpack_require__(/*! ./formatAbiItem.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js\");\nfunction formatAbi(abi) {\n    const signatures = [];\n    const length = abi.length;\n    for (let i = 0; i < length; i++) {\n        const abiItem = abi[i];\n        const signature = (0, formatAbiItem_js_1.formatAbiItem)(abiItem);\n        signatures.push(signature);\n    }\n    return signatures;\n}\n//# sourceMappingURL=formatAbi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvaHVtYW4tcmVhZGFibGUvZm9ybWF0QWJpLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQjtBQUNqQiwyQkFBMkIsbUJBQU8sQ0FBQyxrR0FBb0I7QUFDdkQ7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFlBQVk7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9mb3JtYXRBYmkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmZvcm1hdEFiaSA9IGZvcm1hdEFiaTtcbmNvbnN0IGZvcm1hdEFiaUl0ZW1fanNfMSA9IHJlcXVpcmUoXCIuL2Zvcm1hdEFiaUl0ZW0uanNcIik7XG5mdW5jdGlvbiBmb3JtYXRBYmkoYWJpKSB7XG4gICAgY29uc3Qgc2lnbmF0dXJlcyA9IFtdO1xuICAgIGNvbnN0IGxlbmd0aCA9IGFiaS5sZW5ndGg7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBhYmlJdGVtID0gYWJpW2ldO1xuICAgICAgICBjb25zdCBzaWduYXR1cmUgPSAoMCwgZm9ybWF0QWJpSXRlbV9qc18xLmZvcm1hdEFiaUl0ZW0pKGFiaUl0ZW0pO1xuICAgICAgICBzaWduYXR1cmVzLnB1c2goc2lnbmF0dXJlKTtcbiAgICB9XG4gICAgcmV0dXJuIHNpZ25hdHVyZXM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1mb3JtYXRBYmkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbi.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js":
/*!************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatAbiItem = formatAbiItem;\nconst formatAbiParameters_js_1 = __webpack_require__(/*! ./formatAbiParameters.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js\");\nfunction formatAbiItem(abiItem) {\n    if (abiItem.type === 'function')\n        return `function ${abiItem.name}(${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability && abiItem.stateMutability !== 'nonpayable'\n            ? ` ${abiItem.stateMutability}`\n            : ''}${abiItem.outputs?.length\n            ? ` returns (${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.outputs)})`\n            : ''}`;\n    if (abiItem.type === 'event')\n        return `event ${abiItem.name}(${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'error')\n        return `error ${abiItem.name}(${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'constructor')\n        return `constructor(${(0, formatAbiParameters_js_1.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    if (abiItem.type === 'fallback')\n        return `fallback() external${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    return 'receive() external payable';\n}\n//# sourceMappingURL=formatAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiItem.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatAbiParameter = formatAbiParameter;\nconst regex_js_1 = __webpack_require__(/*! ../regex.js */ \"(ssr)/../node_modules/abitype/dist/cjs/regex.js\");\nconst tupleRegex = /^tuple(?<array>(\\[(\\d*)\\])*)$/;\nfunction formatAbiParameter(abiParameter) {\n    let type = abiParameter.type;\n    if (tupleRegex.test(abiParameter.type) && 'components' in abiParameter) {\n        type = '(';\n        const length = abiParameter.components.length;\n        for (let i = 0; i < length; i++) {\n            const component = abiParameter.components[i];\n            type += formatAbiParameter(component);\n            if (i < length - 1)\n                type += ', ';\n        }\n        const result = (0, regex_js_1.execTyped)(tupleRegex, abiParameter.type);\n        type += `)${result?.array ?? ''}`;\n        return formatAbiParameter({\n            ...abiParameter,\n            type,\n        });\n    }\n    if ('indexed' in abiParameter && abiParameter.indexed)\n        type = `${type} indexed`;\n    if (abiParameter.name)\n        return `${type} ${abiParameter.name}`;\n    return type;\n}\n//# sourceMappingURL=formatAbiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js":
/*!******************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.formatAbiParameters = formatAbiParameters;\nconst formatAbiParameter_js_1 = __webpack_require__(/*! ./formatAbiParameter.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiParameter.js\");\nfunction formatAbiParameters(abiParameters) {\n    let params = '';\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        params += (0, formatAbiParameter_js_1.formatAbiParameter)(abiParameter);\n        if (i !== length - 1)\n            params += ', ';\n    }\n    return params;\n}\n//# sourceMappingURL=formatAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvaHVtYW4tcmVhZGFibGUvZm9ybWF0QWJpUGFyYW1ldGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCwyQkFBMkI7QUFDM0IsZ0NBQWdDLG1CQUFPLENBQUMsNEdBQXlCO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixZQUFZO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2Nqcy9odW1hbi1yZWFkYWJsZS9mb3JtYXRBYmlQYXJhbWV0ZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5mb3JtYXRBYmlQYXJhbWV0ZXJzID0gZm9ybWF0QWJpUGFyYW1ldGVycztcbmNvbnN0IGZvcm1hdEFiaVBhcmFtZXRlcl9qc18xID0gcmVxdWlyZShcIi4vZm9ybWF0QWJpUGFyYW1ldGVyLmpzXCIpO1xuZnVuY3Rpb24gZm9ybWF0QWJpUGFyYW1ldGVycyhhYmlQYXJhbWV0ZXJzKSB7XG4gICAgbGV0IHBhcmFtcyA9ICcnO1xuICAgIGNvbnN0IGxlbmd0aCA9IGFiaVBhcmFtZXRlcnMubGVuZ3RoO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgYWJpUGFyYW1ldGVyID0gYWJpUGFyYW1ldGVyc1tpXTtcbiAgICAgICAgcGFyYW1zICs9ICgwLCBmb3JtYXRBYmlQYXJhbWV0ZXJfanNfMS5mb3JtYXRBYmlQYXJhbWV0ZXIpKGFiaVBhcmFtZXRlcik7XG4gICAgICAgIGlmIChpICE9PSBsZW5ndGggLSAxKVxuICAgICAgICAgICAgcGFyYW1zICs9ICcsICc7XG4gICAgfVxuICAgIHJldHVybiBwYXJhbXM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1mb3JtYXRBYmlQYXJhbWV0ZXJzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/formatAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbi.js":
/*!*******************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/parseAbi.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAbi = parseAbi;\nconst signatures_js_1 = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst structs_js_1 = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\");\nconst utils_js_1 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseAbi(signatures) {\n    const structs = (0, structs_js_1.parseStructs)(signatures);\n    const abi = [];\n    const length = signatures.length;\n    for (let i = 0; i < length; i++) {\n        const signature = signatures[i];\n        if ((0, signatures_js_1.isStructSignature)(signature))\n            continue;\n        abi.push((0, utils_js_1.parseSignature)(signature, structs));\n    }\n    return abi;\n}\n//# sourceMappingURL=parseAbi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvaHVtYW4tcmVhZGFibGUvcGFyc2VBYmkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0JBQWdCO0FBQ2hCLHdCQUF3QixtQkFBTyxDQUFDLDRHQUF5QjtBQUN6RCxxQkFBcUIsbUJBQU8sQ0FBQyxzR0FBc0I7QUFDbkQsbUJBQW1CLG1CQUFPLENBQUMsa0dBQW9CO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFlBQVk7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvY2pzL2h1bWFuLXJlYWRhYmxlL3BhcnNlQWJpLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5wYXJzZUFiaSA9IHBhcnNlQWJpO1xuY29uc3Qgc2lnbmF0dXJlc19qc18xID0gcmVxdWlyZShcIi4vcnVudGltZS9zaWduYXR1cmVzLmpzXCIpO1xuY29uc3Qgc3RydWN0c19qc18xID0gcmVxdWlyZShcIi4vcnVudGltZS9zdHJ1Y3RzLmpzXCIpO1xuY29uc3QgdXRpbHNfanNfMSA9IHJlcXVpcmUoXCIuL3J1bnRpbWUvdXRpbHMuanNcIik7XG5mdW5jdGlvbiBwYXJzZUFiaShzaWduYXR1cmVzKSB7XG4gICAgY29uc3Qgc3RydWN0cyA9ICgwLCBzdHJ1Y3RzX2pzXzEucGFyc2VTdHJ1Y3RzKShzaWduYXR1cmVzKTtcbiAgICBjb25zdCBhYmkgPSBbXTtcbiAgICBjb25zdCBsZW5ndGggPSBzaWduYXR1cmVzLmxlbmd0aDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZSA9IHNpZ25hdHVyZXNbaV07XG4gICAgICAgIGlmICgoMCwgc2lnbmF0dXJlc19qc18xLmlzU3RydWN0U2lnbmF0dXJlKShzaWduYXR1cmUpKVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIGFiaS5wdXNoKCgwLCB1dGlsc19qc18xLnBhcnNlU2lnbmF0dXJlKShzaWduYXR1cmUsIHN0cnVjdHMpKTtcbiAgICB9XG4gICAgcmV0dXJuIGFiaTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhcnNlQWJpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbi.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiItem.js":
/*!***********************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/parseAbiItem.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAbiItem = parseAbiItem;\nconst abiItem_js_1 = __webpack_require__(/*! ./errors/abiItem.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst structs_js_1 = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\");\nconst utils_js_1 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseAbiItem(signature) {\n    let abiItem;\n    if (typeof signature === 'string')\n        abiItem = (0, utils_js_1.parseSignature)(signature);\n    else {\n        const structs = (0, structs_js_1.parseStructs)(signature);\n        const length = signature.length;\n        for (let i = 0; i < length; i++) {\n            const signature_ = signature[i];\n            if ((0, signatures_js_1.isStructSignature)(signature_))\n                continue;\n            abiItem = (0, utils_js_1.parseSignature)(signature_, structs);\n            break;\n        }\n    }\n    if (!abiItem)\n        throw new abiItem_js_1.InvalidAbiItemError({ signature });\n    return abiItem;\n}\n//# sourceMappingURL=parseAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiItem.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiParameter.js":
/*!****************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/parseAbiParameter.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAbiParameter = parseAbiParameter;\nconst abiParameter_js_1 = __webpack_require__(/*! ./errors/abiParameter.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst structs_js_1 = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\");\nconst utils_js_1 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseAbiParameter(param) {\n    let abiParameter;\n    if (typeof param === 'string')\n        abiParameter = (0, utils_js_1.parseAbiParameter)(param, {\n            modifiers: signatures_js_1.modifiers,\n        });\n    else {\n        const structs = (0, structs_js_1.parseStructs)(param);\n        const length = param.length;\n        for (let i = 0; i < length; i++) {\n            const signature = param[i];\n            if ((0, signatures_js_1.isStructSignature)(signature))\n                continue;\n            abiParameter = (0, utils_js_1.parseAbiParameter)(signature, { modifiers: signatures_js_1.modifiers, structs });\n            break;\n        }\n    }\n    if (!abiParameter)\n        throw new abiParameter_js_1.InvalidAbiParameterError({ param });\n    return abiParameter;\n}\n//# sourceMappingURL=parseAbiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiParameter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiParameters.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/parseAbiParameters.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAbiParameters = parseAbiParameters;\nconst abiParameter_js_1 = __webpack_require__(/*! ./errors/abiParameter.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst structs_js_1 = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\");\nconst utils_js_1 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nconst utils_js_2 = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseAbiParameters(params) {\n    const abiParameters = [];\n    if (typeof params === 'string') {\n        const parameters = (0, utils_js_1.splitParameters)(params);\n        const length = parameters.length;\n        for (let i = 0; i < length; i++) {\n            abiParameters.push((0, utils_js_2.parseAbiParameter)(parameters[i], { modifiers: signatures_js_1.modifiers }));\n        }\n    }\n    else {\n        const structs = (0, structs_js_1.parseStructs)(params);\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            const signature = params[i];\n            if ((0, signatures_js_1.isStructSignature)(signature))\n                continue;\n            const parameters = (0, utils_js_1.splitParameters)(signature);\n            const length = parameters.length;\n            for (let k = 0; k < length; k++) {\n                abiParameters.push((0, utils_js_2.parseAbiParameter)(parameters[k], { modifiers: signatures_js_1.modifiers, structs }));\n            }\n        }\n    }\n    if (abiParameters.length === 0)\n        throw new abiParameter_js_1.InvalidAbiParametersError({ params });\n    return abiParameters;\n}\n//# sourceMappingURL=parseAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/parseAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/cache.js":
/*!************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/runtime/cache.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parameterCache = void 0;\nexports.getParameterCacheKey = getParameterCacheKey;\nfunction getParameterCacheKey(param, type, structs) {\n    let structKey = '';\n    if (structs)\n        for (const struct of Object.entries(structs)) {\n            if (!struct)\n                continue;\n            let propertyKey = '';\n            for (const property of struct[1]) {\n                propertyKey += `[${property.type}${property.name ? `:${property.name}` : ''}]`;\n            }\n            structKey += `(${struct[0]}{${propertyKey}})`;\n        }\n    if (type)\n        return `${type}:${param}${structKey}`;\n    return param;\n}\nexports.parameterCache = new Map([\n    ['address', { type: 'address' }],\n    ['bool', { type: 'bool' }],\n    ['bytes', { type: 'bytes' }],\n    ['bytes32', { type: 'bytes32' }],\n    ['int', { type: 'int256' }],\n    ['int256', { type: 'int256' }],\n    ['string', { type: 'string' }],\n    ['uint', { type: 'uint256' }],\n    ['uint8', { type: 'uint8' }],\n    ['uint16', { type: 'uint16' }],\n    ['uint24', { type: 'uint24' }],\n    ['uint32', { type: 'uint32' }],\n    ['uint64', { type: 'uint64' }],\n    ['uint96', { type: 'uint96' }],\n    ['uint112', { type: 'uint112' }],\n    ['uint160', { type: 'uint160' }],\n    ['uint192', { type: 'uint192' }],\n    ['uint256', { type: 'uint256' }],\n    ['address owner', { type: 'address', name: 'owner' }],\n    ['address to', { type: 'address', name: 'to' }],\n    ['bool approved', { type: 'bool', name: 'approved' }],\n    ['bytes _data', { type: 'bytes', name: '_data' }],\n    ['bytes data', { type: 'bytes', name: 'data' }],\n    ['bytes signature', { type: 'bytes', name: 'signature' }],\n    ['bytes32 hash', { type: 'bytes32', name: 'hash' }],\n    ['bytes32 r', { type: 'bytes32', name: 'r' }],\n    ['bytes32 root', { type: 'bytes32', name: 'root' }],\n    ['bytes32 s', { type: 'bytes32', name: 's' }],\n    ['string name', { type: 'string', name: 'name' }],\n    ['string symbol', { type: 'string', name: 'symbol' }],\n    ['string tokenURI', { type: 'string', name: 'tokenURI' }],\n    ['uint tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint8 v', { type: 'uint8', name: 'v' }],\n    ['uint256 balance', { type: 'uint256', name: 'balance' }],\n    ['uint256 tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint256 value', { type: 'uint256', name: 'value' }],\n    [\n        'event:address indexed from',\n        { type: 'address', name: 'from', indexed: true },\n    ],\n    ['event:address indexed to', { type: 'address', name: 'to', indexed: true }],\n    [\n        'event:uint indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n    [\n        'event:uint256 indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n]);\n//# sourceMappingURL=cache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/cache.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.functionModifiers = exports.eventModifiers = exports.modifiers = void 0;\nexports.isErrorSignature = isErrorSignature;\nexports.execErrorSignature = execErrorSignature;\nexports.isEventSignature = isEventSignature;\nexports.execEventSignature = execEventSignature;\nexports.isFunctionSignature = isFunctionSignature;\nexports.execFunctionSignature = execFunctionSignature;\nexports.isStructSignature = isStructSignature;\nexports.execStructSignature = execStructSignature;\nexports.isConstructorSignature = isConstructorSignature;\nexports.execConstructorSignature = execConstructorSignature;\nexports.isFallbackSignature = isFallbackSignature;\nexports.execFallbackSignature = execFallbackSignature;\nexports.isReceiveSignature = isReceiveSignature;\nconst regex_js_1 = __webpack_require__(/*! ../../regex.js */ \"(ssr)/../node_modules/abitype/dist/cjs/regex.js\");\nconst errorSignatureRegex = /^error (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isErrorSignature(signature) {\n    return errorSignatureRegex.test(signature);\n}\nfunction execErrorSignature(signature) {\n    return (0, regex_js_1.execTyped)(errorSignatureRegex, signature);\n}\nconst eventSignatureRegex = /^event (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isEventSignature(signature) {\n    return eventSignatureRegex.test(signature);\n}\nfunction execEventSignature(signature) {\n    return (0, regex_js_1.execTyped)(eventSignatureRegex, signature);\n}\nconst functionSignatureRegex = /^function (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)(?: (?<scope>external|public{1}))?(?: (?<stateMutability>pure|view|nonpayable|payable{1}))?(?: returns\\s?\\((?<returns>.*?)\\))?$/;\nfunction isFunctionSignature(signature) {\n    return functionSignatureRegex.test(signature);\n}\nfunction execFunctionSignature(signature) {\n    return (0, regex_js_1.execTyped)(functionSignatureRegex, signature);\n}\nconst structSignatureRegex = /^struct (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*) \\{(?<properties>.*?)\\}$/;\nfunction isStructSignature(signature) {\n    return structSignatureRegex.test(signature);\n}\nfunction execStructSignature(signature) {\n    return (0, regex_js_1.execTyped)(structSignatureRegex, signature);\n}\nconst constructorSignatureRegex = /^constructor\\((?<parameters>.*?)\\)(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isConstructorSignature(signature) {\n    return constructorSignatureRegex.test(signature);\n}\nfunction execConstructorSignature(signature) {\n    return (0, regex_js_1.execTyped)(constructorSignatureRegex, signature);\n}\nconst fallbackSignatureRegex = /^fallback\\(\\) external(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isFallbackSignature(signature) {\n    return fallbackSignatureRegex.test(signature);\n}\nfunction execFallbackSignature(signature) {\n    return (0, regex_js_1.execTyped)(fallbackSignatureRegex, signature);\n}\nconst receiveSignatureRegex = /^receive\\(\\) external payable$/;\nfunction isReceiveSignature(signature) {\n    return receiveSignatureRegex.test(signature);\n}\nexports.modifiers = new Set([\n    'memory',\n    'indexed',\n    'storage',\n    'calldata',\n]);\nexports.eventModifiers = new Set(['indexed']);\nexports.functionModifiers = new Set([\n    'calldata',\n    'memory',\n    'storage',\n]);\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/structs.js":
/*!**************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/runtime/structs.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseStructs = parseStructs;\nconst regex_js_1 = __webpack_require__(/*! ../../regex.js */ \"(ssr)/../node_modules/abitype/dist/cjs/regex.js\");\nconst abiItem_js_1 = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\");\nconst abiParameter_js_1 = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nconst signature_js_1 = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/signature.js\");\nconst struct_js_1 = __webpack_require__(/*! ../errors/struct.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/struct.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./signatures.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\");\nfunction parseStructs(signatures) {\n    const shallowStructs = {};\n    const signaturesLength = signatures.length;\n    for (let i = 0; i < signaturesLength; i++) {\n        const signature = signatures[i];\n        if (!(0, signatures_js_1.isStructSignature)(signature))\n            continue;\n        const match = (0, signatures_js_1.execStructSignature)(signature);\n        if (!match)\n            throw new signature_js_1.InvalidSignatureError({ signature, type: 'struct' });\n        const properties = match.properties.split(';');\n        const components = [];\n        const propertiesLength = properties.length;\n        for (let k = 0; k < propertiesLength; k++) {\n            const property = properties[k];\n            const trimmed = property.trim();\n            if (!trimmed)\n                continue;\n            const abiParameter = (0, utils_js_1.parseAbiParameter)(trimmed, {\n                type: 'struct',\n            });\n            components.push(abiParameter);\n        }\n        if (!components.length)\n            throw new signature_js_1.InvalidStructSignatureError({ signature });\n        shallowStructs[match.name] = components;\n    }\n    const resolvedStructs = {};\n    const entries = Object.entries(shallowStructs);\n    const entriesLength = entries.length;\n    for (let i = 0; i < entriesLength; i++) {\n        const [name, parameters] = entries[i];\n        resolvedStructs[name] = resolveStructs(parameters, shallowStructs);\n    }\n    return resolvedStructs;\n}\nconst typeWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?$/;\nfunction resolveStructs(abiParameters, structs, ancestors = new Set()) {\n    const components = [];\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        const isTuple = regex_js_1.isTupleRegex.test(abiParameter.type);\n        if (isTuple)\n            components.push(abiParameter);\n        else {\n            const match = (0, regex_js_1.execTyped)(typeWithoutTupleRegex, abiParameter.type);\n            if (!match?.type)\n                throw new abiParameter_js_1.InvalidAbiTypeParameterError({ abiParameter });\n            const { array, type } = match;\n            if (type in structs) {\n                if (ancestors.has(type))\n                    throw new struct_js_1.CircularReferenceError({ type });\n                components.push({\n                    ...abiParameter,\n                    type: `tuple${array ?? ''}`,\n                    components: resolveStructs(structs[type] ?? [], structs, new Set([...ancestors, type])),\n                });\n            }\n            else {\n                if ((0, utils_js_1.isSolidityType)(type))\n                    components.push(abiParameter);\n                else\n                    throw new abiItem_js_1.UnknownTypeError({ type });\n            }\n        }\n    }\n    return components;\n}\n//# sourceMappingURL=structs.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/structs.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js":
/*!************************************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseSignature = parseSignature;\nexports.parseFunctionSignature = parseFunctionSignature;\nexports.parseEventSignature = parseEventSignature;\nexports.parseErrorSignature = parseErrorSignature;\nexports.parseConstructorSignature = parseConstructorSignature;\nexports.parseFallbackSignature = parseFallbackSignature;\nexports.parseAbiParameter = parseAbiParameter;\nexports.splitParameters = splitParameters;\nexports.isSolidityType = isSolidityType;\nexports.isSolidityKeyword = isSolidityKeyword;\nexports.isValidDataLocation = isValidDataLocation;\nconst regex_js_1 = __webpack_require__(/*! ../../regex.js */ \"(ssr)/../node_modules/abitype/dist/cjs/regex.js\");\nconst abiItem_js_1 = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiItem.js\");\nconst abiParameter_js_1 = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/abiParameter.js\");\nconst signature_js_1 = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/signature.js\");\nconst splitParameters_js_1 = __webpack_require__(/*! ../errors/splitParameters.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/errors/splitParameters.js\");\nconst cache_js_1 = __webpack_require__(/*! ./cache.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/cache.js\");\nconst signatures_js_1 = __webpack_require__(/*! ./signatures.js */ \"(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/signatures.js\");\nfunction parseSignature(signature, structs = {}) {\n    if ((0, signatures_js_1.isFunctionSignature)(signature))\n        return parseFunctionSignature(signature, structs);\n    if ((0, signatures_js_1.isEventSignature)(signature))\n        return parseEventSignature(signature, structs);\n    if ((0, signatures_js_1.isErrorSignature)(signature))\n        return parseErrorSignature(signature, structs);\n    if ((0, signatures_js_1.isConstructorSignature)(signature))\n        return parseConstructorSignature(signature, structs);\n    if ((0, signatures_js_1.isFallbackSignature)(signature))\n        return parseFallbackSignature(signature);\n    if ((0, signatures_js_1.isReceiveSignature)(signature))\n        return {\n            type: 'receive',\n            stateMutability: 'payable',\n        };\n    throw new signature_js_1.UnknownSignatureError({ signature });\n}\nfunction parseFunctionSignature(signature, structs = {}) {\n    const match = (0, signatures_js_1.execFunctionSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'function' });\n    const inputParams = splitParameters(match.parameters);\n    const inputs = [];\n    const inputLength = inputParams.length;\n    for (let i = 0; i < inputLength; i++) {\n        inputs.push(parseAbiParameter(inputParams[i], {\n            modifiers: signatures_js_1.functionModifiers,\n            structs,\n            type: 'function',\n        }));\n    }\n    const outputs = [];\n    if (match.returns) {\n        const outputParams = splitParameters(match.returns);\n        const outputLength = outputParams.length;\n        for (let i = 0; i < outputLength; i++) {\n            outputs.push(parseAbiParameter(outputParams[i], {\n                modifiers: signatures_js_1.functionModifiers,\n                structs,\n                type: 'function',\n            }));\n        }\n    }\n    return {\n        name: match.name,\n        type: 'function',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs,\n        outputs,\n    };\n}\nfunction parseEventSignature(signature, structs = {}) {\n    const match = (0, signatures_js_1.execEventSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'event' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], {\n            modifiers: signatures_js_1.eventModifiers,\n            structs,\n            type: 'event',\n        }));\n    return { name: match.name, type: 'event', inputs: abiParameters };\n}\nfunction parseErrorSignature(signature, structs = {}) {\n    const match = (0, signatures_js_1.execErrorSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'error' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'error' }));\n    return { name: match.name, type: 'error', inputs: abiParameters };\n}\nfunction parseConstructorSignature(signature, structs = {}) {\n    const match = (0, signatures_js_1.execConstructorSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'constructor' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'constructor' }));\n    return {\n        type: 'constructor',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs: abiParameters,\n    };\n}\nfunction parseFallbackSignature(signature) {\n    const match = (0, signatures_js_1.execFallbackSignature)(signature);\n    if (!match)\n        throw new signature_js_1.InvalidSignatureError({ signature, type: 'fallback' });\n    return {\n        type: 'fallback',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n    };\n}\nconst abiParameterWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst abiParameterWithTupleRegex = /^\\((?<type>.+?)\\)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst dynamicIntegerRegex = /^u?int$/;\nfunction parseAbiParameter(param, options) {\n    const parameterCacheKey = (0, cache_js_1.getParameterCacheKey)(param, options?.type, options?.structs);\n    if (cache_js_1.parameterCache.has(parameterCacheKey))\n        return cache_js_1.parameterCache.get(parameterCacheKey);\n    const isTuple = regex_js_1.isTupleRegex.test(param);\n    const match = (0, regex_js_1.execTyped)(isTuple ? abiParameterWithTupleRegex : abiParameterWithoutTupleRegex, param);\n    if (!match)\n        throw new abiParameter_js_1.InvalidParameterError({ param });\n    if (match.name && isSolidityKeyword(match.name))\n        throw new abiParameter_js_1.SolidityProtectedKeywordError({ param, name: match.name });\n    const name = match.name ? { name: match.name } : {};\n    const indexed = match.modifier === 'indexed' ? { indexed: true } : {};\n    const structs = options?.structs ?? {};\n    let type;\n    let components = {};\n    if (isTuple) {\n        type = 'tuple';\n        const params = splitParameters(match.type);\n        const components_ = [];\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            components_.push(parseAbiParameter(params[i], { structs }));\n        }\n        components = { components: components_ };\n    }\n    else if (match.type in structs) {\n        type = 'tuple';\n        components = { components: structs[match.type] };\n    }\n    else if (dynamicIntegerRegex.test(match.type)) {\n        type = `${match.type}256`;\n    }\n    else {\n        type = match.type;\n        if (!(options?.type === 'struct') && !isSolidityType(type))\n            throw new abiItem_js_1.UnknownSolidityTypeError({ type });\n    }\n    if (match.modifier) {\n        if (!options?.modifiers?.has?.(match.modifier))\n            throw new abiParameter_js_1.InvalidModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n        if (signatures_js_1.functionModifiers.has(match.modifier) &&\n            !isValidDataLocation(type, !!match.array))\n            throw new abiParameter_js_1.InvalidFunctionModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n    }\n    const abiParameter = {\n        type: `${type}${match.array ?? ''}`,\n        ...name,\n        ...indexed,\n        ...components,\n    };\n    cache_js_1.parameterCache.set(parameterCacheKey, abiParameter);\n    return abiParameter;\n}\nfunction splitParameters(params, result = [], current = '', depth = 0) {\n    const length = params.trim().length;\n    for (let i = 0; i < length; i++) {\n        const char = params[i];\n        const tail = params.slice(i + 1);\n        switch (char) {\n            case ',':\n                return depth === 0\n                    ? splitParameters(tail, [...result, current.trim()])\n                    : splitParameters(tail, result, `${current}${char}`, depth);\n            case '(':\n                return splitParameters(tail, result, `${current}${char}`, depth + 1);\n            case ')':\n                return splitParameters(tail, result, `${current}${char}`, depth - 1);\n            default:\n                return splitParameters(tail, result, `${current}${char}`, depth);\n        }\n    }\n    if (current === '')\n        return result;\n    if (depth !== 0)\n        throw new splitParameters_js_1.InvalidParenthesisError({ current, depth });\n    result.push(current.trim());\n    return result;\n}\nfunction isSolidityType(type) {\n    return (type === 'address' ||\n        type === 'bool' ||\n        type === 'function' ||\n        type === 'string' ||\n        regex_js_1.bytesRegex.test(type) ||\n        regex_js_1.integerRegex.test(type));\n}\nconst protectedKeywordsRegex = /^(?:after|alias|anonymous|apply|auto|byte|calldata|case|catch|constant|copyof|default|defined|error|event|external|false|final|function|immutable|implements|in|indexed|inline|internal|let|mapping|match|memory|mutable|null|of|override|partial|private|promise|public|pure|reference|relocatable|return|returns|sizeof|static|storage|struct|super|supports|switch|this|true|try|typedef|typeof|var|view|virtual)$/;\nfunction isSolidityKeyword(name) {\n    return (name === 'address' ||\n        name === 'bool' ||\n        name === 'function' ||\n        name === 'string' ||\n        name === 'tuple' ||\n        regex_js_1.bytesRegex.test(name) ||\n        regex_js_1.integerRegex.test(name) ||\n        protectedKeywordsRegex.test(name));\n}\nfunction isValidDataLocation(type, isArray) {\n    return isArray || type === 'bytes' || type === 'string' || type === 'tuple';\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/human-readable/runtime/utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/narrow.js":
/*!**************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/narrow.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.narrow = narrow;\nfunction narrow(value) {\n    return value;\n}\n//# sourceMappingURL=narrow.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvbmFycm93LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvY2pzL25hcnJvdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMubmFycm93ID0gbmFycm93O1xuZnVuY3Rpb24gbmFycm93KHZhbHVlKSB7XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmFycm93LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/narrow.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/regex.js":
/*!*************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/regex.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isTupleRegex = exports.integerRegex = exports.bytesRegex = void 0;\nexports.execTyped = execTyped;\nfunction execTyped(regex, string) {\n    const match = regex.exec(string);\n    return match?.groups;\n}\nexports.bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;\nexports.integerRegex = /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;\nexports.isTupleRegex = /^\\(.+?\\).*?$/;\n//# sourceMappingURL=regex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvcmVnZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CLEdBQUcsb0JBQW9CLEdBQUcsa0JBQWtCO0FBQ2hFLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQixvQkFBb0I7QUFDcEIsb0JBQW9CO0FBQ3BCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvcmVnZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmlzVHVwbGVSZWdleCA9IGV4cG9ydHMuaW50ZWdlclJlZ2V4ID0gZXhwb3J0cy5ieXRlc1JlZ2V4ID0gdm9pZCAwO1xuZXhwb3J0cy5leGVjVHlwZWQgPSBleGVjVHlwZWQ7XG5mdW5jdGlvbiBleGVjVHlwZWQocmVnZXgsIHN0cmluZykge1xuICAgIGNvbnN0IG1hdGNoID0gcmVnZXguZXhlYyhzdHJpbmcpO1xuICAgIHJldHVybiBtYXRjaD8uZ3JvdXBzO1xufVxuZXhwb3J0cy5ieXRlc1JlZ2V4ID0gL15ieXRlcyhbMS05XXwxWzAtOV18MlswLTldfDNbMC0yXSk/JC87XG5leHBvcnRzLmludGVnZXJSZWdleCA9IC9edT9pbnQoOHwxNnwyNHwzMnw0MHw0OHw1Nnw2NHw3Mnw4MHw4OHw5NnwxMDR8MTEyfDEyMHwxMjh8MTM2fDE0NHwxNTJ8MTYwfDE2OHwxNzZ8MTg0fDE5MnwyMDB8MjA4fDIxNnwyMjR8MjMyfDI0MHwyNDh8MjU2KT8kLztcbmV4cG9ydHMuaXNUdXBsZVJlZ2V4ID0gL15cXCguKz9cXCkuKj8kLztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZ2V4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/regex.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/cjs/version.js":
/*!***************************************************!*\
  !*** ../node_modules/abitype/dist/cjs/version.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.version = void 0;\nexports.version = '1.0.8';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxlQUFlO0FBQ2YsZUFBZTtBQUNmIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9janMvdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudmVyc2lvbiA9IHZvaWQgMDtcbmV4cG9ydHMudmVyc2lvbiA9ICcxLjAuOCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/cjs/version.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/errors.js":
/*!**************************************************!*\
  !*** ../node_modules/abitype/dist/esm/errors.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version.js */ \"(ssr)/../node_modules/abitype/dist/esm/version.js\");\n\nclass BaseError extends Error {\n    constructor(shortMessage, args = {}) {\n        const details = args.cause instanceof BaseError\n            ? args.cause.details\n            : args.cause?.message\n                ? args.cause.message\n                : args.details;\n        const docsPath = args.cause instanceof BaseError\n            ? args.cause.docsPath || args.docsPath\n            : args.docsPath;\n        const message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n            ...(docsPath ? [`Docs: https://abitype.dev${docsPath}`] : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: abitype@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`,\n        ].join('\\n');\n        super(message);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiTypeError'\n        });\n        if (args.cause)\n            this.cause = args.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = args.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/errors.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiItem.js":
/*!*************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/errors/abiItem.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidAbiItemError: () => (/* binding */ InvalidAbiItemError),\n/* harmony export */   UnknownSolidityTypeError: () => (/* binding */ UnknownSolidityTypeError),\n/* harmony export */   UnknownTypeError: () => (/* binding */ UnknownTypeError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidAbiItemError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Failed to parse ABI item.', {\n            details: `parseAbiItem(${JSON.stringify(signature, null, 2)})`,\n            docsPath: '/api/human#parseabiitem-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiItemError'\n        });\n    }\n}\nclass UnknownTypeError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [\n                `Type \"${type}\" is not a valid ABI type. Perhaps you forgot to include a struct signature?`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownTypeError'\n        });\n    }\n}\nclass UnknownSolidityTypeError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [`Type \"${type}\" is not a valid ABI type.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSolidityTypeError'\n        });\n    }\n}\n//# sourceMappingURL=abiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvZXJyb3JzL2FiaUl0ZW0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0QztBQUNyQyxrQ0FBa0MsaURBQVM7QUFDbEQsa0JBQWtCLFdBQVc7QUFDN0I7QUFDQSxxQ0FBcUMsbUNBQW1DO0FBQ3hFO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPLCtCQUErQixpREFBUztBQUMvQyxrQkFBa0IsTUFBTTtBQUN4QjtBQUNBO0FBQ0EseUJBQXlCLEtBQUs7QUFDOUI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ08sdUNBQXVDLGlEQUFTO0FBQ3ZELGtCQUFrQixNQUFNO0FBQ3hCO0FBQ0Esb0NBQW9DLEtBQUs7QUFDekMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvZXJyb3JzL2FiaUl0ZW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi4vLi4vZXJyb3JzLmpzJztcbmV4cG9ydCBjbGFzcyBJbnZhbGlkQWJpSXRlbUVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IHNpZ25hdHVyZSB9KSB7XG4gICAgICAgIHN1cGVyKCdGYWlsZWQgdG8gcGFyc2UgQUJJIGl0ZW0uJywge1xuICAgICAgICAgICAgZGV0YWlsczogYHBhcnNlQWJpSXRlbSgke0pTT04uc3RyaW5naWZ5KHNpZ25hdHVyZSwgbnVsbCwgMil9KWAsXG4gICAgICAgICAgICBkb2NzUGF0aDogJy9hcGkvaHVtYW4jcGFyc2VhYmlpdGVtLTEnLFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0ludmFsaWRBYmlJdGVtRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBVbmtub3duVHlwZUVycm9yIGV4dGVuZHMgQmFzZUVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcih7IHR5cGUgfSkge1xuICAgICAgICBzdXBlcignVW5rbm93biB0eXBlLicsIHtcbiAgICAgICAgICAgIG1ldGFNZXNzYWdlczogW1xuICAgICAgICAgICAgICAgIGBUeXBlIFwiJHt0eXBlfVwiIGlzIG5vdCBhIHZhbGlkIEFCSSB0eXBlLiBQZXJoYXBzIHlvdSBmb3Jnb3QgdG8gaW5jbHVkZSBhIHN0cnVjdCBzaWduYXR1cmU/YCxcbiAgICAgICAgICAgIF0sXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnVW5rbm93blR5cGVFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIFVua25vd25Tb2xpZGl0eVR5cGVFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyB0eXBlIH0pIHtcbiAgICAgICAgc3VwZXIoJ1Vua25vd24gdHlwZS4nLCB7XG4gICAgICAgICAgICBtZXRhTWVzc2FnZXM6IFtgVHlwZSBcIiR7dHlwZX1cIiBpcyBub3QgYSB2YWxpZCBBQkkgdHlwZS5gXSxcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5hbWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6ICdVbmtub3duU29saWRpdHlUeXBlRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFiaUl0ZW0uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js":
/*!******************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidAbiParameterError: () => (/* binding */ InvalidAbiParameterError),\n/* harmony export */   InvalidAbiParametersError: () => (/* binding */ InvalidAbiParametersError),\n/* harmony export */   InvalidAbiTypeParameterError: () => (/* binding */ InvalidAbiTypeParameterError),\n/* harmony export */   InvalidFunctionModifierError: () => (/* binding */ InvalidFunctionModifierError),\n/* harmony export */   InvalidModifierError: () => (/* binding */ InvalidModifierError),\n/* harmony export */   InvalidParameterError: () => (/* binding */ InvalidParameterError),\n/* harmony export */   SolidityProtectedKeywordError: () => (/* binding */ SolidityProtectedKeywordError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidAbiParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param }) {\n        super('Failed to parse ABI parameter.', {\n            details: `parseAbiParameter(${JSON.stringify(param, null, 2)})`,\n            docsPath: '/api/human#parseabiparameter-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParameterError'\n        });\n    }\n}\nclass InvalidAbiParametersError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ params }) {\n        super('Failed to parse ABI parameters.', {\n            details: `parseAbiParameters(${JSON.stringify(params, null, 2)})`,\n            docsPath: '/api/human#parseabiparameters-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParametersError'\n        });\n    }\n}\nclass InvalidParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParameterError'\n        });\n    }\n}\nclass SolidityProtectedKeywordError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, name }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `\"${name}\" is a protected Solidity keyword. More info: https://docs.soliditylang.org/en/latest/cheatsheet.html`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SolidityProtectedKeywordError'\n        });\n    }\n}\nclass InvalidModifierError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidModifierError'\n        });\n    }\n}\nclass InvalidFunctionModifierError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n                `Data location can only be specified for array, struct, or mapping types, but \"${modifier}\" was given.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidFunctionModifierError'\n        });\n    }\n}\nclass InvalidAbiTypeParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ abiParameter, }) {\n        super('Invalid ABI parameter.', {\n            details: JSON.stringify(abiParameter, null, 2),\n            metaMessages: ['ABI parameter type is invalid.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiTypeParameterError'\n        });\n    }\n}\n//# sourceMappingURL=abiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/signature.js":
/*!***************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/errors/signature.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidSignatureError: () => (/* binding */ InvalidSignatureError),\n/* harmony export */   InvalidStructSignatureError: () => (/* binding */ InvalidStructSignatureError),\n/* harmony export */   UnknownSignatureError: () => (/* binding */ UnknownSignatureError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature, type, }) {\n        super(`Invalid ${type} signature.`, {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidSignatureError'\n        });\n    }\n}\nclass UnknownSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Unknown signature.', {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSignatureError'\n        });\n    }\n}\nclass InvalidStructSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Invalid struct signature.', {\n            details: signature,\n            metaMessages: ['No properties exist.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidStructSignatureError'\n        });\n    }\n}\n//# sourceMappingURL=signature.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/signature.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidParenthesisError: () => (/* binding */ InvalidParenthesisError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidParenthesisError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ current, depth }) {\n        super('Unbalanced parentheses.', {\n            metaMessages: [\n                `\"${current.trim()}\" has too many ${depth > 0 ? 'opening' : 'closing'} parentheses.`,\n            ],\n            details: `Depth \"${depth}\"`,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParenthesisError'\n        });\n    }\n}\n//# sourceMappingURL=splitParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvZXJyb3JzL3NwbGl0UGFyYW1ldGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUNyQyxzQ0FBc0MsaURBQVM7QUFDdEQsa0JBQWtCLGdCQUFnQjtBQUNsQztBQUNBO0FBQ0Esb0JBQW9CLGVBQWUsaUJBQWlCLG1DQUFtQztBQUN2RjtBQUNBLCtCQUErQixNQUFNO0FBQ3JDLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvZXNtL2h1bWFuLXJlYWRhYmxlL2Vycm9ycy9zcGxpdFBhcmFtZXRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi4vLi4vZXJyb3JzLmpzJztcbmV4cG9ydCBjbGFzcyBJbnZhbGlkUGFyZW50aGVzaXNFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyBjdXJyZW50LCBkZXB0aCB9KSB7XG4gICAgICAgIHN1cGVyKCdVbmJhbGFuY2VkIHBhcmVudGhlc2VzLicsIHtcbiAgICAgICAgICAgIG1ldGFNZXNzYWdlczogW1xuICAgICAgICAgICAgICAgIGBcIiR7Y3VycmVudC50cmltKCl9XCIgaGFzIHRvbyBtYW55ICR7ZGVwdGggPiAwID8gJ29wZW5pbmcnIDogJ2Nsb3NpbmcnfSBwYXJlbnRoZXNlcy5gLFxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIGRldGFpbHM6IGBEZXB0aCBcIiR7ZGVwdGh9XCJgLFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0ludmFsaWRQYXJlbnRoZXNpc0Vycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zcGxpdFBhcmFtZXRlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/struct.js":
/*!************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/errors/struct.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CircularReferenceError: () => (/* binding */ CircularReferenceError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/../node_modules/abitype/dist/esm/errors.js\");\n\nclass CircularReferenceError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Circular reference detected.', {\n            metaMessages: [`Struct \"${type}\" is a circular reference.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'CircularReferenceError'\n        });\n    }\n}\n//# sourceMappingURL=struct.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvZXJyb3JzL3N0cnVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUNyQyxxQ0FBcUMsaURBQVM7QUFDckQsa0JBQWtCLE1BQU07QUFDeEI7QUFDQSxzQ0FBc0MsS0FBSztBQUMzQyxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3RydWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhc2VFcnJvciB9IGZyb20gJy4uLy4uL2Vycm9ycy5qcyc7XG5leHBvcnQgY2xhc3MgQ2lyY3VsYXJSZWZlcmVuY2VFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyB0eXBlIH0pIHtcbiAgICAgICAgc3VwZXIoJ0NpcmN1bGFyIHJlZmVyZW5jZSBkZXRlY3RlZC4nLCB7XG4gICAgICAgICAgICBtZXRhTWVzc2FnZXM6IFtgU3RydWN0IFwiJHt0eXBlfVwiIGlzIGEgY2lyY3VsYXIgcmVmZXJlbmNlLmBdLFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0NpcmN1bGFyUmVmZXJlbmNlRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN0cnVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/struct.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/formatAbiItem.js":
/*!************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/formatAbiItem.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiItem: () => (/* binding */ formatAbiItem)\n/* harmony export */ });\n/* harmony import */ var _formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatAbiParameters.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js\");\n\n/**\n * Formats ABI item (e.g. error, event, function) into human-readable ABI item\n *\n * @param abiItem - ABI item\n * @returns Human-readable ABI item\n */\nfunction formatAbiItem(abiItem) {\n    if (abiItem.type === 'function')\n        return `function ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability && abiItem.stateMutability !== 'nonpayable'\n            ? ` ${abiItem.stateMutability}`\n            : ''}${abiItem.outputs?.length\n            ? ` returns (${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.outputs)})`\n            : ''}`;\n    if (abiItem.type === 'event')\n        return `event ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'error')\n        return `error ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'constructor')\n        return `constructor(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    if (abiItem.type === 'fallback')\n        return `fallback() external${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    return 'receive() external payable';\n}\n//# sourceMappingURL=formatAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvZm9ybWF0QWJpSXRlbS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRTtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsMkJBQTJCLGFBQWEsR0FBRyw0RUFBbUIsaUJBQWlCLEdBQUc7QUFDbEYsa0JBQWtCLHdCQUF3QjtBQUMxQyxpQkFBaUIsRUFBRTtBQUNuQiwyQkFBMkIsNEVBQW1CLGtCQUFrQjtBQUNoRSxpQkFBaUI7QUFDakI7QUFDQSx3QkFBd0IsYUFBYSxHQUFHLDRFQUFtQixpQkFBaUI7QUFDNUU7QUFDQSx3QkFBd0IsYUFBYSxHQUFHLDRFQUFtQixpQkFBaUI7QUFDNUU7QUFDQSw4QkFBOEIsNEVBQW1CLGlCQUFpQixHQUFHLHdEQUF3RDtBQUM3SDtBQUNBLHFDQUFxQyx3REFBd0Q7QUFDN0Y7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvZm9ybWF0QWJpSXRlbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmb3JtYXRBYmlQYXJhbWV0ZXJzLCB9IGZyb20gJy4vZm9ybWF0QWJpUGFyYW1ldGVycy5qcyc7XG4vKipcbiAqIEZvcm1hdHMgQUJJIGl0ZW0gKGUuZy4gZXJyb3IsIGV2ZW50LCBmdW5jdGlvbikgaW50byBodW1hbi1yZWFkYWJsZSBBQkkgaXRlbVxuICpcbiAqIEBwYXJhbSBhYmlJdGVtIC0gQUJJIGl0ZW1cbiAqIEByZXR1cm5zIEh1bWFuLXJlYWRhYmxlIEFCSSBpdGVtXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRBYmlJdGVtKGFiaUl0ZW0pIHtcbiAgICBpZiAoYWJpSXRlbS50eXBlID09PSAnZnVuY3Rpb24nKVxuICAgICAgICByZXR1cm4gYGZ1bmN0aW9uICR7YWJpSXRlbS5uYW1lfSgke2Zvcm1hdEFiaVBhcmFtZXRlcnMoYWJpSXRlbS5pbnB1dHMpfSkke2FiaUl0ZW0uc3RhdGVNdXRhYmlsaXR5ICYmIGFiaUl0ZW0uc3RhdGVNdXRhYmlsaXR5ICE9PSAnbm9ucGF5YWJsZSdcbiAgICAgICAgICAgID8gYCAke2FiaUl0ZW0uc3RhdGVNdXRhYmlsaXR5fWBcbiAgICAgICAgICAgIDogJyd9JHthYmlJdGVtLm91dHB1dHM/Lmxlbmd0aFxuICAgICAgICAgICAgPyBgIHJldHVybnMgKCR7Zm9ybWF0QWJpUGFyYW1ldGVycyhhYmlJdGVtLm91dHB1dHMpfSlgXG4gICAgICAgICAgICA6ICcnfWA7XG4gICAgaWYgKGFiaUl0ZW0udHlwZSA9PT0gJ2V2ZW50JylcbiAgICAgICAgcmV0dXJuIGBldmVudCAke2FiaUl0ZW0ubmFtZX0oJHtmb3JtYXRBYmlQYXJhbWV0ZXJzKGFiaUl0ZW0uaW5wdXRzKX0pYDtcbiAgICBpZiAoYWJpSXRlbS50eXBlID09PSAnZXJyb3InKVxuICAgICAgICByZXR1cm4gYGVycm9yICR7YWJpSXRlbS5uYW1lfSgke2Zvcm1hdEFiaVBhcmFtZXRlcnMoYWJpSXRlbS5pbnB1dHMpfSlgO1xuICAgIGlmIChhYmlJdGVtLnR5cGUgPT09ICdjb25zdHJ1Y3RvcicpXG4gICAgICAgIHJldHVybiBgY29uc3RydWN0b3IoJHtmb3JtYXRBYmlQYXJhbWV0ZXJzKGFiaUl0ZW0uaW5wdXRzKX0pJHthYmlJdGVtLnN0YXRlTXV0YWJpbGl0eSA9PT0gJ3BheWFibGUnID8gJyBwYXlhYmxlJyA6ICcnfWA7XG4gICAgaWYgKGFiaUl0ZW0udHlwZSA9PT0gJ2ZhbGxiYWNrJylcbiAgICAgICAgcmV0dXJuIGBmYWxsYmFjaygpIGV4dGVybmFsJHthYmlJdGVtLnN0YXRlTXV0YWJpbGl0eSA9PT0gJ3BheWFibGUnID8gJyBwYXlhYmxlJyA6ICcnfWA7XG4gICAgcmV0dXJuICdyZWNlaXZlKCkgZXh0ZXJuYWwgcGF5YWJsZSc7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1mb3JtYXRBYmlJdGVtLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/formatAbiItem.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiParameter: () => (/* binding */ formatAbiParameter)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../regex.js */ \"(ssr)/../node_modules/abitype/dist/esm/regex.js\");\n\n// https://regexr.com/7f7rv\nconst tupleRegex = /^tuple(?<array>(\\[(\\d*)\\])*)$/;\n/**\n * Formats {@link AbiParameter} to human-readable ABI parameter.\n *\n * @param abiParameter - ABI parameter\n * @returns Human-readable ABI parameter\n *\n * @example\n * const result = formatAbiParameter({ type: 'address', name: 'from' })\n * //    ^? const result: 'address from'\n */\nfunction formatAbiParameter(abiParameter) {\n    let type = abiParameter.type;\n    if (tupleRegex.test(abiParameter.type) && 'components' in abiParameter) {\n        type = '(';\n        const length = abiParameter.components.length;\n        for (let i = 0; i < length; i++) {\n            const component = abiParameter.components[i];\n            type += formatAbiParameter(component);\n            if (i < length - 1)\n                type += ', ';\n        }\n        const result = (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(tupleRegex, abiParameter.type);\n        type += `)${result?.array ?? ''}`;\n        return formatAbiParameter({\n            ...abiParameter,\n            type,\n        });\n    }\n    // Add `indexed` to type if in `abiParameter`\n    if ('indexed' in abiParameter && abiParameter.indexed)\n        type = `${type} indexed`;\n    // Return human-readable ABI parameter\n    if (abiParameter.name)\n        return `${type} ${abiParameter.name}`;\n    return type;\n}\n//# sourceMappingURL=formatAbiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js":
/*!******************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiParameters: () => (/* binding */ formatAbiParameters)\n/* harmony export */ });\n/* harmony import */ var _formatAbiParameter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatAbiParameter.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js\");\n\n/**\n * Formats {@link AbiParameter}s to human-readable ABI parameters.\n *\n * @param abiParameters - ABI parameters\n * @returns Human-readable ABI parameters\n *\n * @example\n * const result = formatAbiParameters([\n *   //  ^? const result: 'address from, uint256 tokenId'\n *   { type: 'address', name: 'from' },\n *   { type: 'uint256', name: 'tokenId' },\n * ])\n */\nfunction formatAbiParameters(abiParameters) {\n    let params = '';\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        params += (0,_formatAbiParameter_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameter)(abiParameter);\n        if (i !== length - 1)\n            params += ', ';\n    }\n    return params;\n}\n//# sourceMappingURL=formatAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvZm9ybWF0QWJpUGFyYW1ldGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4RDtBQUM5RDtBQUNBLFlBQVksbUJBQW1CO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTywrQkFBK0I7QUFDdEMsT0FBTyxrQ0FBa0M7QUFDekM7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLG9CQUFvQixZQUFZO0FBQ2hDO0FBQ0Esa0JBQWtCLDBFQUFrQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9mb3JtYXRBYmlQYXJhbWV0ZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZvcm1hdEFiaVBhcmFtZXRlciwgfSBmcm9tICcuL2Zvcm1hdEFiaVBhcmFtZXRlci5qcyc7XG4vKipcbiAqIEZvcm1hdHMge0BsaW5rIEFiaVBhcmFtZXRlcn1zIHRvIGh1bWFuLXJlYWRhYmxlIEFCSSBwYXJhbWV0ZXJzLlxuICpcbiAqIEBwYXJhbSBhYmlQYXJhbWV0ZXJzIC0gQUJJIHBhcmFtZXRlcnNcbiAqIEByZXR1cm5zIEh1bWFuLXJlYWRhYmxlIEFCSSBwYXJhbWV0ZXJzXG4gKlxuICogQGV4YW1wbGVcbiAqIGNvbnN0IHJlc3VsdCA9IGZvcm1hdEFiaVBhcmFtZXRlcnMoW1xuICogICAvLyAgXj8gY29uc3QgcmVzdWx0OiAnYWRkcmVzcyBmcm9tLCB1aW50MjU2IHRva2VuSWQnXG4gKiAgIHsgdHlwZTogJ2FkZHJlc3MnLCBuYW1lOiAnZnJvbScgfSxcbiAqICAgeyB0eXBlOiAndWludDI1NicsIG5hbWU6ICd0b2tlbklkJyB9LFxuICogXSlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEFiaVBhcmFtZXRlcnMoYWJpUGFyYW1ldGVycykge1xuICAgIGxldCBwYXJhbXMgPSAnJztcbiAgICBjb25zdCBsZW5ndGggPSBhYmlQYXJhbWV0ZXJzLmxlbmd0aDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGFiaVBhcmFtZXRlciA9IGFiaVBhcmFtZXRlcnNbaV07XG4gICAgICAgIHBhcmFtcyArPSBmb3JtYXRBYmlQYXJhbWV0ZXIoYWJpUGFyYW1ldGVyKTtcbiAgICAgICAgaWYgKGkgIT09IGxlbmd0aCAtIDEpXG4gICAgICAgICAgICBwYXJhbXMgKz0gJywgJztcbiAgICB9XG4gICAgcmV0dXJuIHBhcmFtcztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZvcm1hdEFiaVBhcmFtZXRlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/parseAbi.js":
/*!*******************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/parseAbi.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAbi: () => (/* binding */ parseAbi)\n/* harmony export */ });\n/* harmony import */ var _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _runtime_structs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/structs.js\");\n/* harmony import */ var _runtime_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n/**\n * Parses human-readable ABI into JSON {@link Abi}\n *\n * @param signatures - Human-Readable ABI\n * @returns Parsed {@link Abi}\n *\n * @example\n * const abi = parseAbi([\n *   //  ^? const abi: readonly [{ name: \"balanceOf\"; type: \"function\"; stateMutability:...\n *   'function balanceOf(address owner) view returns (uint256)',\n *   'event Transfer(address indexed from, address indexed to, uint256 amount)',\n * ])\n */\nfunction parseAbi(signatures) {\n    const structs = (0,_runtime_structs_js__WEBPACK_IMPORTED_MODULE_0__.parseStructs)(signatures);\n    const abi = [];\n    const length = signatures.length;\n    for (let i = 0; i < length; i++) {\n        const signature = signatures[i];\n        if ((0,_runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.isStructSignature)(signature))\n            continue;\n        abi.push((0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_2__.parseSignature)(signature, structs));\n    }\n    return abi;\n}\n//# sourceMappingURL=parseAbi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvcGFyc2VBYmkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0RDtBQUNSO0FBQ0E7QUFDcEQ7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsbUJBQW1CLGtCQUFrQjtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1Asb0JBQW9CLGlFQUFZO0FBQ2hDO0FBQ0E7QUFDQSxvQkFBb0IsWUFBWTtBQUNoQztBQUNBLFlBQVkseUVBQWlCO0FBQzdCO0FBQ0EsaUJBQWlCLGlFQUFjO0FBQy9CO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vaHVtYW4tcmVhZGFibGUvcGFyc2VBYmkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNTdHJ1Y3RTaWduYXR1cmUgfSBmcm9tICcuL3J1bnRpbWUvc2lnbmF0dXJlcy5qcyc7XG5pbXBvcnQgeyBwYXJzZVN0cnVjdHMgfSBmcm9tICcuL3J1bnRpbWUvc3RydWN0cy5qcyc7XG5pbXBvcnQgeyBwYXJzZVNpZ25hdHVyZSB9IGZyb20gJy4vcnVudGltZS91dGlscy5qcyc7XG4vKipcbiAqIFBhcnNlcyBodW1hbi1yZWFkYWJsZSBBQkkgaW50byBKU09OIHtAbGluayBBYml9XG4gKlxuICogQHBhcmFtIHNpZ25hdHVyZXMgLSBIdW1hbi1SZWFkYWJsZSBBQklcbiAqIEByZXR1cm5zIFBhcnNlZCB7QGxpbmsgQWJpfVxuICpcbiAqIEBleGFtcGxlXG4gKiBjb25zdCBhYmkgPSBwYXJzZUFiaShbXG4gKiAgIC8vICBePyBjb25zdCBhYmk6IHJlYWRvbmx5IFt7IG5hbWU6IFwiYmFsYW5jZU9mXCI7IHR5cGU6IFwiZnVuY3Rpb25cIjsgc3RhdGVNdXRhYmlsaXR5Oi4uLlxuICogICAnZnVuY3Rpb24gYmFsYW5jZU9mKGFkZHJlc3Mgb3duZXIpIHZpZXcgcmV0dXJucyAodWludDI1NiknLFxuICogICAnZXZlbnQgVHJhbnNmZXIoYWRkcmVzcyBpbmRleGVkIGZyb20sIGFkZHJlc3MgaW5kZXhlZCB0bywgdWludDI1NiBhbW91bnQpJyxcbiAqIF0pXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZUFiaShzaWduYXR1cmVzKSB7XG4gICAgY29uc3Qgc3RydWN0cyA9IHBhcnNlU3RydWN0cyhzaWduYXR1cmVzKTtcbiAgICBjb25zdCBhYmkgPSBbXTtcbiAgICBjb25zdCBsZW5ndGggPSBzaWduYXR1cmVzLmxlbmd0aDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZSA9IHNpZ25hdHVyZXNbaV07XG4gICAgICAgIGlmIChpc1N0cnVjdFNpZ25hdHVyZShzaWduYXR1cmUpKVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIGFiaS5wdXNoKHBhcnNlU2lnbmF0dXJlKHNpZ25hdHVyZSwgc3RydWN0cykpO1xuICAgIH1cbiAgICByZXR1cm4gYWJpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFyc2VBYmkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/parseAbi.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/parseAbiItem.js":
/*!***********************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/parseAbiItem.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAbiItem: () => (/* binding */ parseAbiItem)\n/* harmony export */ });\n/* harmony import */ var _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors/abiItem.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\");\n/* harmony import */ var _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _runtime_structs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/structs.js\");\n/* harmony import */ var _runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n\n/**\n * Parses human-readable ABI item (e.g. error, event, function) into {@link Abi} item\n *\n * @param signature - Human-readable ABI item\n * @returns Parsed {@link Abi} item\n *\n * @example\n * const abiItem = parseAbiItem('function balanceOf(address owner) view returns (uint256)')\n * //    ^? const abiItem: { name: \"balanceOf\"; type: \"function\"; stateMutability: \"view\";...\n *\n * @example\n * const abiItem = parseAbiItem([\n *   //  ^? const abiItem: { name: \"foo\"; type: \"function\"; stateMutability: \"view\"; inputs:...\n *   'function foo(Baz bar) view returns (string)',\n *   'struct Baz { string name; }',\n * ])\n */\nfunction parseAbiItem(signature) {\n    let abiItem;\n    if (typeof signature === 'string')\n        abiItem = (0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.parseSignature)(signature);\n    else {\n        const structs = (0,_runtime_structs_js__WEBPACK_IMPORTED_MODULE_1__.parseStructs)(signature);\n        const length = signature.length;\n        for (let i = 0; i < length; i++) {\n            const signature_ = signature[i];\n            if ((0,_runtime_signatures_js__WEBPACK_IMPORTED_MODULE_2__.isStructSignature)(signature_))\n                continue;\n            abiItem = (0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.parseSignature)(signature_, structs);\n            break;\n        }\n    }\n    if (!abiItem)\n        throw new _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_3__.InvalidAbiItemError({ signature });\n    return abiItem;\n}\n//# sourceMappingURL=parseAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/parseAbiItem.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/parseAbiParameters.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/parseAbiParameters.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAbiParameters: () => (/* binding */ parseAbiParameters)\n/* harmony export */ });\n/* harmony import */ var _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors/abiParameter.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\");\n/* harmony import */ var _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _runtime_structs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/structs.js\");\n/* harmony import */ var _runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n\n\n/**\n * Parses human-readable ABI parameters into {@link AbiParameter}s\n *\n * @param params - Human-readable ABI parameters\n * @returns Parsed {@link AbiParameter}s\n *\n * @example\n * const abiParameters = parseAbiParameters('address from, address to, uint256 amount')\n * //    ^? const abiParameters: [{ type: \"address\"; name: \"from\"; }, { type: \"address\";...\n *\n * @example\n * const abiParameters = parseAbiParameters([\n *   //  ^? const abiParameters: [{ type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   'Baz bar',\n *   'struct Baz { string name; }',\n * ])\n */\nfunction parseAbiParameters(params) {\n    const abiParameters = [];\n    if (typeof params === 'string') {\n        const parameters = (0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.splitParameters)(params);\n        const length = parameters.length;\n        for (let i = 0; i < length; i++) {\n            abiParameters.push((0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.parseAbiParameter)(parameters[i], { modifiers: _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.modifiers }));\n        }\n    }\n    else {\n        const structs = (0,_runtime_structs_js__WEBPACK_IMPORTED_MODULE_2__.parseStructs)(params);\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            const signature = params[i];\n            if ((0,_runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.isStructSignature)(signature))\n                continue;\n            const parameters = (0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.splitParameters)(signature);\n            const length = parameters.length;\n            for (let k = 0; k < length; k++) {\n                abiParameters.push((0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_0__.parseAbiParameter)(parameters[k], { modifiers: _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.modifiers, structs }));\n            }\n        }\n    }\n    if (abiParameters.length === 0)\n        throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_3__.InvalidAbiParametersError({ params });\n    return abiParameters;\n}\n//# sourceMappingURL=parseAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/parseAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/cache.js":
/*!************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/runtime/cache.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getParameterCacheKey: () => (/* binding */ getParameterCacheKey),\n/* harmony export */   parameterCache: () => (/* binding */ parameterCache)\n/* harmony export */ });\n/**\n * Gets {@link parameterCache} cache key namespaced by {@link type}. This prevents parameters from being accessible to types that don't allow them (e.g. `string indexed foo` not allowed outside of `type: 'event'`).\n * @param param ABI parameter string\n * @param type ABI parameter type\n * @returns Cache key for {@link parameterCache}\n */\nfunction getParameterCacheKey(param, type, structs) {\n    let structKey = '';\n    if (structs)\n        for (const struct of Object.entries(structs)) {\n            if (!struct)\n                continue;\n            let propertyKey = '';\n            for (const property of struct[1]) {\n                propertyKey += `[${property.type}${property.name ? `:${property.name}` : ''}]`;\n            }\n            structKey += `(${struct[0]}{${propertyKey}})`;\n        }\n    if (type)\n        return `${type}:${param}${structKey}`;\n    return param;\n}\n/**\n * Basic cache seeded with common ABI parameter strings.\n *\n * **Note: When seeding more parameters, make sure you benchmark performance. The current number is the ideal balance between performance and having an already existing cache.**\n */\nconst parameterCache = new Map([\n    // Unnamed\n    ['address', { type: 'address' }],\n    ['bool', { type: 'bool' }],\n    ['bytes', { type: 'bytes' }],\n    ['bytes32', { type: 'bytes32' }],\n    ['int', { type: 'int256' }],\n    ['int256', { type: 'int256' }],\n    ['string', { type: 'string' }],\n    ['uint', { type: 'uint256' }],\n    ['uint8', { type: 'uint8' }],\n    ['uint16', { type: 'uint16' }],\n    ['uint24', { type: 'uint24' }],\n    ['uint32', { type: 'uint32' }],\n    ['uint64', { type: 'uint64' }],\n    ['uint96', { type: 'uint96' }],\n    ['uint112', { type: 'uint112' }],\n    ['uint160', { type: 'uint160' }],\n    ['uint192', { type: 'uint192' }],\n    ['uint256', { type: 'uint256' }],\n    // Named\n    ['address owner', { type: 'address', name: 'owner' }],\n    ['address to', { type: 'address', name: 'to' }],\n    ['bool approved', { type: 'bool', name: 'approved' }],\n    ['bytes _data', { type: 'bytes', name: '_data' }],\n    ['bytes data', { type: 'bytes', name: 'data' }],\n    ['bytes signature', { type: 'bytes', name: 'signature' }],\n    ['bytes32 hash', { type: 'bytes32', name: 'hash' }],\n    ['bytes32 r', { type: 'bytes32', name: 'r' }],\n    ['bytes32 root', { type: 'bytes32', name: 'root' }],\n    ['bytes32 s', { type: 'bytes32', name: 's' }],\n    ['string name', { type: 'string', name: 'name' }],\n    ['string symbol', { type: 'string', name: 'symbol' }],\n    ['string tokenURI', { type: 'string', name: 'tokenURI' }],\n    ['uint tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint8 v', { type: 'uint8', name: 'v' }],\n    ['uint256 balance', { type: 'uint256', name: 'balance' }],\n    ['uint256 tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint256 value', { type: 'uint256', name: 'value' }],\n    // Indexed\n    [\n        'event:address indexed from',\n        { type: 'address', name: 'from', indexed: true },\n    ],\n    ['event:address indexed to', { type: 'address', name: 'to', indexed: true }],\n    [\n        'event:uint indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n    [\n        'event:uint256 indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n]);\n//# sourceMappingURL=cache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/cache.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/signatures.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/runtime/signatures.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventModifiers: () => (/* binding */ eventModifiers),\n/* harmony export */   execConstructorSignature: () => (/* binding */ execConstructorSignature),\n/* harmony export */   execErrorSignature: () => (/* binding */ execErrorSignature),\n/* harmony export */   execEventSignature: () => (/* binding */ execEventSignature),\n/* harmony export */   execFallbackSignature: () => (/* binding */ execFallbackSignature),\n/* harmony export */   execFunctionSignature: () => (/* binding */ execFunctionSignature),\n/* harmony export */   execStructSignature: () => (/* binding */ execStructSignature),\n/* harmony export */   functionModifiers: () => (/* binding */ functionModifiers),\n/* harmony export */   isConstructorSignature: () => (/* binding */ isConstructorSignature),\n/* harmony export */   isErrorSignature: () => (/* binding */ isErrorSignature),\n/* harmony export */   isEventSignature: () => (/* binding */ isEventSignature),\n/* harmony export */   isFallbackSignature: () => (/* binding */ isFallbackSignature),\n/* harmony export */   isFunctionSignature: () => (/* binding */ isFunctionSignature),\n/* harmony export */   isReceiveSignature: () => (/* binding */ isReceiveSignature),\n/* harmony export */   isStructSignature: () => (/* binding */ isStructSignature),\n/* harmony export */   modifiers: () => (/* binding */ modifiers)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/../node_modules/abitype/dist/esm/regex.js\");\n\n// https://regexr.com/7gmok\nconst errorSignatureRegex = /^error (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isErrorSignature(signature) {\n    return errorSignatureRegex.test(signature);\n}\nfunction execErrorSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(errorSignatureRegex, signature);\n}\n// https://regexr.com/7gmoq\nconst eventSignatureRegex = /^event (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isEventSignature(signature) {\n    return eventSignatureRegex.test(signature);\n}\nfunction execEventSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(eventSignatureRegex, signature);\n}\n// https://regexr.com/7gmot\nconst functionSignatureRegex = /^function (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)(?: (?<scope>external|public{1}))?(?: (?<stateMutability>pure|view|nonpayable|payable{1}))?(?: returns\\s?\\((?<returns>.*?)\\))?$/;\nfunction isFunctionSignature(signature) {\n    return functionSignatureRegex.test(signature);\n}\nfunction execFunctionSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(functionSignatureRegex, signature);\n}\n// https://regexr.com/7gmp3\nconst structSignatureRegex = /^struct (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*) \\{(?<properties>.*?)\\}$/;\nfunction isStructSignature(signature) {\n    return structSignatureRegex.test(signature);\n}\nfunction execStructSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(structSignatureRegex, signature);\n}\n// https://regexr.com/78u01\nconst constructorSignatureRegex = /^constructor\\((?<parameters>.*?)\\)(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isConstructorSignature(signature) {\n    return constructorSignatureRegex.test(signature);\n}\nfunction execConstructorSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(constructorSignatureRegex, signature);\n}\n// https://regexr.com/7srtn\nconst fallbackSignatureRegex = /^fallback\\(\\) external(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isFallbackSignature(signature) {\n    return fallbackSignatureRegex.test(signature);\n}\nfunction execFallbackSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(fallbackSignatureRegex, signature);\n}\n// https://regexr.com/78u1k\nconst receiveSignatureRegex = /^receive\\(\\) external payable$/;\nfunction isReceiveSignature(signature) {\n    return receiveSignatureRegex.test(signature);\n}\nconst modifiers = new Set([\n    'memory',\n    'indexed',\n    'storage',\n    'calldata',\n]);\nconst eventModifiers = new Set(['indexed']);\nconst functionModifiers = new Set([\n    'calldata',\n    'memory',\n    'storage',\n]);\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/structs.js":
/*!**************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/runtime/structs.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseStructs: () => (/* binding */ parseStructs)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/../node_modules/abitype/dist/esm/regex.js\");\n/* harmony import */ var _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\");\n/* harmony import */ var _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\");\n/* harmony import */ var _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/signature.js\");\n/* harmony import */ var _errors_struct_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../errors/struct.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/struct.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n\n\n\n\nfunction parseStructs(signatures) {\n    // Create \"shallow\" version of each struct (and filter out non-structs or invalid structs)\n    const shallowStructs = {};\n    const signaturesLength = signatures.length;\n    for (let i = 0; i < signaturesLength; i++) {\n        const signature = signatures[i];\n        if (!(0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isStructSignature)(signature))\n            continue;\n        const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execStructSignature)(signature);\n        if (!match)\n            throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'struct' });\n        const properties = match.properties.split(';');\n        const components = [];\n        const propertiesLength = properties.length;\n        for (let k = 0; k < propertiesLength; k++) {\n            const property = properties[k];\n            const trimmed = property.trim();\n            if (!trimmed)\n                continue;\n            const abiParameter = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.parseAbiParameter)(trimmed, {\n                type: 'struct',\n            });\n            components.push(abiParameter);\n        }\n        if (!components.length)\n            throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidStructSignatureError({ signature });\n        shallowStructs[match.name] = components;\n    }\n    // Resolve nested structs inside each parameter\n    const resolvedStructs = {};\n    const entries = Object.entries(shallowStructs);\n    const entriesLength = entries.length;\n    for (let i = 0; i < entriesLength; i++) {\n        const [name, parameters] = entries[i];\n        resolvedStructs[name] = resolveStructs(parameters, shallowStructs);\n    }\n    return resolvedStructs;\n}\nconst typeWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?$/;\nfunction resolveStructs(abiParameters, structs, ancestors = new Set()) {\n    const components = [];\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        const isTuple = _regex_js__WEBPACK_IMPORTED_MODULE_3__.isTupleRegex.test(abiParameter.type);\n        if (isTuple)\n            components.push(abiParameter);\n        else {\n            const match = (0,_regex_js__WEBPACK_IMPORTED_MODULE_3__.execTyped)(typeWithoutTupleRegex, abiParameter.type);\n            if (!match?.type)\n                throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidAbiTypeParameterError({ abiParameter });\n            const { array, type } = match;\n            if (type in structs) {\n                if (ancestors.has(type))\n                    throw new _errors_struct_js__WEBPACK_IMPORTED_MODULE_5__.CircularReferenceError({ type });\n                components.push({\n                    ...abiParameter,\n                    type: `tuple${array ?? ''}`,\n                    components: resolveStructs(structs[type] ?? [], structs, new Set([...ancestors, type])),\n                });\n            }\n            else {\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isSolidityType)(type))\n                    components.push(abiParameter);\n                else\n                    throw new _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_6__.UnknownTypeError({ type });\n            }\n        }\n    }\n    return components;\n}\n//# sourceMappingURL=structs.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/structs.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/utils.js":
/*!************************************************************************!*\
  !*** ../node_modules/abitype/dist/esm/human-readable/runtime/utils.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSolidityKeyword: () => (/* binding */ isSolidityKeyword),\n/* harmony export */   isSolidityType: () => (/* binding */ isSolidityType),\n/* harmony export */   isValidDataLocation: () => (/* binding */ isValidDataLocation),\n/* harmony export */   parseAbiParameter: () => (/* binding */ parseAbiParameter),\n/* harmony export */   parseConstructorSignature: () => (/* binding */ parseConstructorSignature),\n/* harmony export */   parseErrorSignature: () => (/* binding */ parseErrorSignature),\n/* harmony export */   parseEventSignature: () => (/* binding */ parseEventSignature),\n/* harmony export */   parseFallbackSignature: () => (/* binding */ parseFallbackSignature),\n/* harmony export */   parseFunctionSignature: () => (/* binding */ parseFunctionSignature),\n/* harmony export */   parseSignature: () => (/* binding */ parseSignature),\n/* harmony export */   splitParameters: () => (/* binding */ splitParameters)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/../node_modules/abitype/dist/esm/regex.js\");\n/* harmony import */ var _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\");\n/* harmony import */ var _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\");\n/* harmony import */ var _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/signature.js\");\n/* harmony import */ var _errors_splitParameters_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/splitParameters.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js\");\n/* harmony import */ var _cache_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cache.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/cache.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n\n\n\n\n\n\n\nfunction parseSignature(signature, structs = {}) {\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isFunctionSignature)(signature))\n        return parseFunctionSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isEventSignature)(signature))\n        return parseEventSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isErrorSignature)(signature))\n        return parseErrorSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isConstructorSignature)(signature))\n        return parseConstructorSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isFallbackSignature)(signature))\n        return parseFallbackSignature(signature);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isReceiveSignature)(signature))\n        return {\n            type: 'receive',\n            stateMutability: 'payable',\n        };\n    throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.UnknownSignatureError({ signature });\n}\nfunction parseFunctionSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execFunctionSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'function' });\n    const inputParams = splitParameters(match.parameters);\n    const inputs = [];\n    const inputLength = inputParams.length;\n    for (let i = 0; i < inputLength; i++) {\n        inputs.push(parseAbiParameter(inputParams[i], {\n            modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers,\n            structs,\n            type: 'function',\n        }));\n    }\n    const outputs = [];\n    if (match.returns) {\n        const outputParams = splitParameters(match.returns);\n        const outputLength = outputParams.length;\n        for (let i = 0; i < outputLength; i++) {\n            outputs.push(parseAbiParameter(outputParams[i], {\n                modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers,\n                structs,\n                type: 'function',\n            }));\n        }\n    }\n    return {\n        name: match.name,\n        type: 'function',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs,\n        outputs,\n    };\n}\nfunction parseEventSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execEventSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'event' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], {\n            modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.eventModifiers,\n            structs,\n            type: 'event',\n        }));\n    return { name: match.name, type: 'event', inputs: abiParameters };\n}\nfunction parseErrorSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execErrorSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'error' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'error' }));\n    return { name: match.name, type: 'error', inputs: abiParameters };\n}\nfunction parseConstructorSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execConstructorSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'constructor' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'constructor' }));\n    return {\n        type: 'constructor',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs: abiParameters,\n    };\n}\nfunction parseFallbackSignature(signature) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execFallbackSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'fallback' });\n    return {\n        type: 'fallback',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n    };\n}\nconst abiParameterWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst abiParameterWithTupleRegex = /^\\((?<type>.+?)\\)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst dynamicIntegerRegex = /^u?int$/;\nfunction parseAbiParameter(param, options) {\n    // optional namespace cache by `type`\n    const parameterCacheKey = (0,_cache_js__WEBPACK_IMPORTED_MODULE_2__.getParameterCacheKey)(param, options?.type, options?.structs);\n    if (_cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.has(parameterCacheKey))\n        return _cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.get(parameterCacheKey);\n    const isTuple = _regex_js__WEBPACK_IMPORTED_MODULE_3__.isTupleRegex.test(param);\n    const match = (0,_regex_js__WEBPACK_IMPORTED_MODULE_3__.execTyped)(isTuple ? abiParameterWithTupleRegex : abiParameterWithoutTupleRegex, param);\n    if (!match)\n        throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidParameterError({ param });\n    if (match.name && isSolidityKeyword(match.name))\n        throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.SolidityProtectedKeywordError({ param, name: match.name });\n    const name = match.name ? { name: match.name } : {};\n    const indexed = match.modifier === 'indexed' ? { indexed: true } : {};\n    const structs = options?.structs ?? {};\n    let type;\n    let components = {};\n    if (isTuple) {\n        type = 'tuple';\n        const params = splitParameters(match.type);\n        const components_ = [];\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            // remove `modifiers` from `options` to prevent from being added to tuple components\n            components_.push(parseAbiParameter(params[i], { structs }));\n        }\n        components = { components: components_ };\n    }\n    else if (match.type in structs) {\n        type = 'tuple';\n        components = { components: structs[match.type] };\n    }\n    else if (dynamicIntegerRegex.test(match.type)) {\n        type = `${match.type}256`;\n    }\n    else {\n        type = match.type;\n        if (!(options?.type === 'struct') && !isSolidityType(type))\n            throw new _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_5__.UnknownSolidityTypeError({ type });\n    }\n    if (match.modifier) {\n        // Check if modifier exists, but is not allowed (e.g. `indexed` in `functionModifiers`)\n        if (!options?.modifiers?.has?.(match.modifier))\n            throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n        // Check if resolved `type` is valid if there is a function modifier\n        if (_signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers.has(match.modifier) &&\n            !isValidDataLocation(type, !!match.array))\n            throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidFunctionModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n    }\n    const abiParameter = {\n        type: `${type}${match.array ?? ''}`,\n        ...name,\n        ...indexed,\n        ...components,\n    };\n    _cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.set(parameterCacheKey, abiParameter);\n    return abiParameter;\n}\n// s/o latika for this\nfunction splitParameters(params, result = [], current = '', depth = 0) {\n    const length = params.trim().length;\n    // biome-ignore lint/correctness/noUnreachable: recursive\n    for (let i = 0; i < length; i++) {\n        const char = params[i];\n        const tail = params.slice(i + 1);\n        switch (char) {\n            case ',':\n                return depth === 0\n                    ? splitParameters(tail, [...result, current.trim()])\n                    : splitParameters(tail, result, `${current}${char}`, depth);\n            case '(':\n                return splitParameters(tail, result, `${current}${char}`, depth + 1);\n            case ')':\n                return splitParameters(tail, result, `${current}${char}`, depth - 1);\n            default:\n                return splitParameters(tail, result, `${current}${char}`, depth);\n        }\n    }\n    if (current === '')\n        return result;\n    if (depth !== 0)\n        throw new _errors_splitParameters_js__WEBPACK_IMPORTED_MODULE_6__.InvalidParenthesisError({ current, depth });\n    result.push(current.trim());\n    return result;\n}\nfunction isSolidityType(type) {\n    return (type === 'address' ||\n        type === 'bool' ||\n        type === 'function' ||\n        type === 'string' ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.bytesRegex.test(type) ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.integerRegex.test(type));\n}\nconst protectedKeywordsRegex = /^(?:after|alias|anonymous|apply|auto|byte|calldata|case|catch|constant|copyof|default|defined|error|event|external|false|final|function|immutable|implements|in|indexed|inline|internal|let|mapping|match|memory|mutable|null|of|override|partial|private|promise|public|pure|reference|relocatable|return|returns|sizeof|static|storage|struct|super|supports|switch|this|true|try|typedef|typeof|var|view|virtual)$/;\n/** @internal */\nfunction isSolidityKeyword(name) {\n    return (name === 'address' ||\n        name === 'bool' ||\n        name === 'function' ||\n        name === 'string' ||\n        name === 'tuple' ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.bytesRegex.test(name) ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.integerRegex.test(name) ||\n        protectedKeywordsRegex.test(name));\n}\n/** @internal */\nfunction isValidDataLocation(type, isArray) {\n    return isArray || type === 'bytes' || type === 'string' || type === 'tuple';\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/human-readable/runtime/utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/regex.js":
/*!*************************************************!*\
  !*** ../node_modules/abitype/dist/esm/regex.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bytesRegex: () => (/* binding */ bytesRegex),\n/* harmony export */   execTyped: () => (/* binding */ execTyped),\n/* harmony export */   integerRegex: () => (/* binding */ integerRegex),\n/* harmony export */   isTupleRegex: () => (/* binding */ isTupleRegex)\n/* harmony export */ });\n// TODO: This looks cool. Need to check the performance of `new RegExp` versus defined inline though.\n// https://twitter.com/GabrielVergnaud/status/1622906834343366657\nfunction execTyped(regex, string) {\n    const match = regex.exec(string);\n    return match?.groups;\n}\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nconst bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nconst integerRegex = /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;\nconst isTupleRegex = /^\\(.+?\\).*?$/;\n//# sourceMappingURL=regex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vcmVnZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNBO0FBQ1AiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9yZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUT0RPOiBUaGlzIGxvb2tzIGNvb2wuIE5lZWQgdG8gY2hlY2sgdGhlIHBlcmZvcm1hbmNlIG9mIGBuZXcgUmVnRXhwYCB2ZXJzdXMgZGVmaW5lZCBpbmxpbmUgdGhvdWdoLlxuLy8gaHR0cHM6Ly90d2l0dGVyLmNvbS9HYWJyaWVsVmVyZ25hdWQvc3RhdHVzLzE2MjI5MDY4MzQzNDMzNjY2NTdcbmV4cG9ydCBmdW5jdGlvbiBleGVjVHlwZWQocmVnZXgsIHN0cmluZykge1xuICAgIGNvbnN0IG1hdGNoID0gcmVnZXguZXhlYyhzdHJpbmcpO1xuICAgIHJldHVybiBtYXRjaD8uZ3JvdXBzO1xufVxuLy8gYGJ5dGVzPE0+YDogYmluYXJ5IHR5cGUgb2YgYE1gIGJ5dGVzLCBgMCA8IE0gPD0gMzJgXG4vLyBodHRwczovL3JlZ2V4ci5jb20vNnZhNTVcbmV4cG9ydCBjb25zdCBieXRlc1JlZ2V4ID0gL15ieXRlcyhbMS05XXwxWzAtOV18MlswLTldfDNbMC0yXSk/JC87XG4vLyBgKHUpaW50PE0+YDogKHVuKXNpZ25lZCBpbnRlZ2VyIHR5cGUgb2YgYE1gIGJpdHMsIGAwIDwgTSA8PSAyNTZgLCBgTSAlIDggPT0gMGBcbi8vIGh0dHBzOi8vcmVnZXhyLmNvbS82djhocFxuZXhwb3J0IGNvbnN0IGludGVnZXJSZWdleCA9IC9edT9pbnQoOHwxNnwyNHwzMnw0MHw0OHw1Nnw2NHw3Mnw4MHw4OHw5NnwxMDR8MTEyfDEyMHwxMjh8MTM2fDE0NHwxNTJ8MTYwfDE2OHwxNzZ8MTg0fDE5MnwyMDB8MjA4fDIxNnwyMjR8MjMyfDI0MHwyNDh8MjU2KT8kLztcbmV4cG9ydCBjb25zdCBpc1R1cGxlUmVnZXggPSAvXlxcKC4rP1xcKS4qPyQvO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVnZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/regex.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/abitype/dist/esm/version.js":
/*!***************************************************!*\
  !*** ../node_modules/abitype/dist/esm/version.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '1.0.8';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2FiaXR5cGUvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvZXNtL3ZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMS4wLjgnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/abitype/dist/esm/version.js\n");

/***/ })

};
;