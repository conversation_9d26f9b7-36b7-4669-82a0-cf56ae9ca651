/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pump";
exports.ids = ["vendor-chunks/pump"];
exports.modules = {

/***/ "(ssr)/../../../node_modules/pump/index.js":
/*!*******************************************!*\
  !*** ../../../node_modules/pump/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var once = __webpack_require__(/*! once */ \"(ssr)/../../../node_modules/once/once.js\")\nvar eos = __webpack_require__(/*! end-of-stream */ \"(ssr)/../../../node_modules/end-of-stream/index.js\")\nvar fs = __webpack_require__(/*! fs */ \"fs\") // we only need fs to get the ReadStream and WriteStream prototypes\n\nvar noop = function () {}\nvar ancient = /^v?\\.0/.test(process.version)\n\nvar isFn = function (fn) {\n  return typeof fn === 'function'\n}\n\nvar isFS = function (stream) {\n  if (!ancient) return false // newer node version do not need to care about fs is a special way\n  if (!fs) return false // browser\n  return (stream instanceof (fs.ReadStream || noop) || stream instanceof (fs.WriteStream || noop)) && isFn(stream.close)\n}\n\nvar isRequest = function (stream) {\n  return stream.setHeader && isFn(stream.abort)\n}\n\nvar destroyer = function (stream, reading, writing, callback) {\n  callback = once(callback)\n\n  var closed = false\n  stream.on('close', function () {\n    closed = true\n  })\n\n  eos(stream, {readable: reading, writable: writing}, function (err) {\n    if (err) return callback(err)\n    closed = true\n    callback()\n  })\n\n  var destroyed = false\n  return function (err) {\n    if (closed) return\n    if (destroyed) return\n    destroyed = true\n\n    if (isFS(stream)) return stream.close(noop) // use close for fs streams to avoid fd leaks\n    if (isRequest(stream)) return stream.abort() // request.destroy just do .end - .abort is what we want\n\n    if (isFn(stream.destroy)) return stream.destroy()\n\n    callback(err || new Error('stream was destroyed'))\n  }\n}\n\nvar call = function (fn) {\n  fn()\n}\n\nvar pipe = function (from, to) {\n  return from.pipe(to)\n}\n\nvar pump = function () {\n  var streams = Array.prototype.slice.call(arguments)\n  var callback = isFn(streams[streams.length - 1] || noop) && streams.pop() || noop\n\n  if (Array.isArray(streams[0])) streams = streams[0]\n  if (streams.length < 2) throw new Error('pump requires two streams per minimum')\n\n  var error\n  var destroys = streams.map(function (stream, i) {\n    var reading = i < streams.length - 1\n    var writing = i > 0\n    return destroyer(stream, reading, writing, function (err) {\n      if (!error) error = err\n      if (err) destroys.forEach(call)\n      if (reading) return\n      destroys.forEach(call)\n      callback(error)\n    })\n  })\n\n  return streams.reduce(pipe)\n}\n\nmodule.exports = pump\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../../node_modules/pump/index.js\n");

/***/ })

};
;