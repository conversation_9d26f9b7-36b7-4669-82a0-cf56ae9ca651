"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/thread-stream";
exports.ids = ["vendor-chunks/thread-stream"];
exports.modules = {

/***/ "(ssr)/../node_modules/thread-stream/index.js":
/*!**********************************************!*\
  !*** ../node_modules/thread-stream/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst { Worker } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst { join } = __webpack_require__(/*! path */ \"path\")\nconst { pathToFileURL } = __webpack_require__(/*! url */ \"url\")\nconst { wait } = __webpack_require__(/*! ./lib/wait */ \"(ssr)/../node_modules/thread-stream/lib/wait.js\")\nconst {\n  WRITE_INDEX,\n  READ_INDEX\n} = __webpack_require__(/*! ./lib/indexes */ \"(ssr)/../node_modules/thread-stream/lib/indexes.js\")\nconst buffer = __webpack_require__(/*! buffer */ \"buffer\")\nconst assert = __webpack_require__(/*! assert */ \"assert\")\n\nconst kImpl = Symbol('kImpl')\n\n// V8 limit for string size\nconst MAX_STRING = buffer.constants.MAX_STRING_LENGTH\n\nclass FakeWeakRef {\n  constructor (value) {\n    this._value = value\n  }\n\n  deref () {\n    return this._value\n  }\n}\n\nconst FinalizationRegistry = global.FinalizationRegistry || class FakeFinalizationRegistry {\n  register () {}\n  unregister () {}\n}\n\nconst WeakRef = global.WeakRef || FakeWeakRef\n\nconst registry = new FinalizationRegistry((worker) => {\n  if (worker.exited) {\n    return\n  }\n  worker.terminate()\n})\n\nfunction createWorker (stream, opts) {\n  const { filename, workerData } = opts\n\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n  const toExecute = bundlerOverrides['thread-stream-worker'] || join(__dirname, 'lib', 'worker.js')\n\n  const worker = new Worker(toExecute, {\n    ...opts.workerOpts,\n    workerData: {\n      filename: filename.indexOf('file://') === 0\n        ? filename\n        : pathToFileURL(filename).href,\n      dataBuf: stream[kImpl].dataBuf,\n      stateBuf: stream[kImpl].stateBuf,\n      workerData\n    }\n  })\n\n  // We keep a strong reference for now,\n  // we need to start writing first\n  worker.stream = new FakeWeakRef(stream)\n\n  worker.on('message', onWorkerMessage)\n  worker.on('exit', onWorkerExit)\n  registry.register(stream, worker)\n\n  return worker\n}\n\nfunction drain (stream) {\n  assert(!stream[kImpl].sync)\n  if (stream[kImpl].needDrain) {\n    stream[kImpl].needDrain = false\n    stream.emit('drain')\n  }\n}\n\nfunction nextFlush (stream) {\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  let leftover = stream[kImpl].data.length - writeIndex\n\n  if (leftover > 0) {\n    if (stream[kImpl].buf.length === 0) {\n      stream[kImpl].flushing = false\n\n      if (stream[kImpl].ending) {\n        end(stream)\n      } else if (stream[kImpl].needDrain) {\n        process.nextTick(drain, stream)\n      }\n\n      return\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, nextFlush.bind(null, stream))\n    } else {\n      // multi-byte utf-8\n      stream.flush(() => {\n        // err is already handled in flush()\n        if (stream.destroyed) {\n          return\n        }\n\n        Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n        Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n        // Find a toWrite length that fits the buffer\n        // it must exists as the buffer is at least 4 bytes length\n        // and the max utf-8 length for a char is 4 bytes.\n        while (toWriteBytes > stream[kImpl].data.length) {\n          leftover = leftover / 2\n          toWrite = stream[kImpl].buf.slice(0, leftover)\n          toWriteBytes = Buffer.byteLength(toWrite)\n        }\n        stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n        write(stream, toWrite, nextFlush.bind(null, stream))\n      })\n    }\n  } else if (leftover === 0) {\n    if (writeIndex === 0 && stream[kImpl].buf.length === 0) {\n      // we had a flushSync in the meanwhile\n      return\n    }\n    stream.flush(() => {\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      nextFlush(stream)\n    })\n  } else {\n    // This should never happen\n    throw new Error('overwritten')\n  }\n}\n\nfunction onWorkerMessage (msg) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    this.exited = true\n    // Terminate the worker.\n    this.terminate()\n    return\n  }\n\n  switch (msg.code) {\n    case 'READY':\n      // Replace the FakeWeakRef with a\n      // proper one.\n      this.stream = new WeakRef(stream)\n\n      stream.flush(() => {\n        stream[kImpl].ready = true\n        stream.emit('ready')\n      })\n      break\n    case 'ERROR':\n      destroy(stream, msg.err)\n      break\n    default:\n      throw new Error('this should not happen: ' + msg.code)\n  }\n}\n\nfunction onWorkerExit (code) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    // Nothing to do, the worker already exit\n    return\n  }\n  registry.unregister(stream)\n  stream.worker.exited = true\n  stream.worker.off('exit', onWorkerExit)\n  destroy(stream, code !== 0 ? new Error('The worker thread exited') : null)\n}\n\nclass ThreadStream extends EventEmitter {\n  constructor (opts = {}) {\n    super()\n\n    if (opts.bufferSize < 4) {\n      throw new Error('bufferSize must at least fit a 4-byte utf-8 char')\n    }\n\n    this[kImpl] = {}\n    this[kImpl].stateBuf = new SharedArrayBuffer(128)\n    this[kImpl].state = new Int32Array(this[kImpl].stateBuf)\n    this[kImpl].dataBuf = new SharedArrayBuffer(opts.bufferSize || 4 * 1024 * 1024)\n    this[kImpl].data = Buffer.from(this[kImpl].dataBuf)\n    this[kImpl].sync = opts.sync || false\n    this[kImpl].ending = false\n    this[kImpl].ended = false\n    this[kImpl].needDrain = false\n    this[kImpl].destroyed = false\n    this[kImpl].flushing = false\n    this[kImpl].ready = false\n    this[kImpl].finished = false\n    this[kImpl].errored = null\n    this[kImpl].closed = false\n    this[kImpl].buf = ''\n\n    // TODO (fix): Make private?\n    this.worker = createWorker(this, opts) // TODO (fix): make private\n  }\n\n  write (data) {\n    if (this[kImpl].destroyed) {\n      throw new Error('the worker has exited')\n    }\n\n    if (this[kImpl].ending) {\n      throw new Error('the worker is ending')\n    }\n\n    if (this[kImpl].flushing && this[kImpl].buf.length + data.length >= MAX_STRING) {\n      try {\n        writeSync(this)\n        this[kImpl].flushing = true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    this[kImpl].buf += data\n\n    if (this[kImpl].sync) {\n      try {\n        writeSync(this)\n        return true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    if (!this[kImpl].flushing) {\n      this[kImpl].flushing = true\n      setImmediate(nextFlush, this)\n    }\n\n    this[kImpl].needDrain = this[kImpl].data.length - this[kImpl].buf.length - Atomics.load(this[kImpl].state, WRITE_INDEX) <= 0\n    return !this[kImpl].needDrain\n  }\n\n  end () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    this[kImpl].ending = true\n    end(this)\n  }\n\n  flush (cb) {\n    if (this[kImpl].destroyed) {\n      if (typeof cb === 'function') {\n        process.nextTick(cb, new Error('the worker has exited'))\n      }\n      return\n    }\n\n    // TODO write all .buf\n    const writeIndex = Atomics.load(this[kImpl].state, WRITE_INDEX)\n    // process._rawDebug(`(flush) readIndex (${Atomics.load(this.state, READ_INDEX)}) writeIndex (${Atomics.load(this.state, WRITE_INDEX)})`)\n    wait(this[kImpl].state, READ_INDEX, writeIndex, Infinity, (err, res) => {\n      if (err) {\n        destroy(this, err)\n        process.nextTick(cb, err)\n        return\n      }\n      if (res === 'not-equal') {\n        // TODO handle deadlock\n        this.flush(cb)\n        return\n      }\n      process.nextTick(cb)\n    })\n  }\n\n  flushSync () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    writeSync(this)\n    flushSync(this)\n  }\n\n  unref () {\n    this.worker.unref()\n  }\n\n  ref () {\n    this.worker.ref()\n  }\n\n  get ready () {\n    return this[kImpl].ready\n  }\n\n  get destroyed () {\n    return this[kImpl].destroyed\n  }\n\n  get closed () {\n    return this[kImpl].closed\n  }\n\n  get writable () {\n    return !this[kImpl].destroyed && !this[kImpl].ending\n  }\n\n  get writableEnded () {\n    return this[kImpl].ending\n  }\n\n  get writableFinished () {\n    return this[kImpl].finished\n  }\n\n  get writableNeedDrain () {\n    return this[kImpl].needDrain\n  }\n\n  get writableObjectMode () {\n    return false\n  }\n\n  get writableErrored () {\n    return this[kImpl].errored\n  }\n}\n\nfunction destroy (stream, err) {\n  if (stream[kImpl].destroyed) {\n    return\n  }\n  stream[kImpl].destroyed = true\n\n  if (err) {\n    stream[kImpl].errored = err\n    stream.emit('error', err)\n  }\n\n  if (!stream.worker.exited) {\n    stream.worker.terminate()\n      .catch(() => {})\n      .then(() => {\n        stream[kImpl].closed = true\n        stream.emit('close')\n      })\n  } else {\n    setImmediate(() => {\n      stream[kImpl].closed = true\n      stream.emit('close')\n    })\n  }\n}\n\nfunction write (stream, data, cb) {\n  // data is smaller than the shared buffer length\n  const current = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  const length = Buffer.byteLength(data)\n  stream[kImpl].data.write(data, current)\n  Atomics.store(stream[kImpl].state, WRITE_INDEX, current + length)\n  Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n  cb()\n  return true\n}\n\nfunction end (stream) {\n  if (stream[kImpl].ended || !stream[kImpl].ending || stream[kImpl].flushing) {\n    return\n  }\n  stream[kImpl].ended = true\n\n  try {\n    stream.flushSync()\n\n    let readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    // process._rawDebug('writing index')\n    Atomics.store(stream[kImpl].state, WRITE_INDEX, -1)\n    // process._rawDebug(`(end) readIndex (${Atomics.load(stream.state, READ_INDEX)}) writeIndex (${Atomics.load(stream.state, WRITE_INDEX)})`)\n    Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n\n    // Wait for the process to complete\n    let spins = 0\n    while (readIndex !== -1) {\n      // process._rawDebug(`read = ${read}`)\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n      readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n      if (readIndex === -2) {\n        throw new Error('end() failed')\n      }\n\n      if (++spins === 10) {\n        throw new Error('end() took too long (10s)')\n      }\n    }\n\n    process.nextTick(() => {\n      stream[kImpl].finished = true\n      stream.emit('finish')\n    })\n  } catch (err) {\n    destroy(stream, err)\n  }\n  // process._rawDebug('end finished...')\n}\n\nfunction writeSync (stream) {\n  const cb = () => {\n    if (stream[kImpl].ending) {\n      end(stream)\n    } else if (stream[kImpl].needDrain) {\n      process.nextTick(drain, stream)\n    }\n  }\n  stream[kImpl].flushing = false\n\n  while (stream[kImpl].buf.length !== 0) {\n    const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n    let leftover = stream[kImpl].data.length - writeIndex\n    if (leftover === 0) {\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      continue\n    } else if (leftover < 0) {\n      // stream should never happen\n      throw new Error('overwritten')\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, cb)\n    } else {\n      // multi-byte utf-8\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n      // Find a toWrite length that fits the buffer\n      // it must exists as the buffer is at least 4 bytes length\n      // and the max utf-8 length for a char is 4 bytes.\n      while (toWriteBytes > stream[kImpl].buf.length) {\n        leftover = leftover / 2\n        toWrite = stream[kImpl].buf.slice(0, leftover)\n        toWriteBytes = Buffer.byteLength(toWrite)\n      }\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      write(stream, toWrite, cb)\n    }\n  }\n}\n\nfunction flushSync (stream) {\n  if (stream[kImpl].flushing) {\n    throw new Error('unable to flush while flushing')\n  }\n\n  // process._rawDebug('flushSync started')\n\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n\n  let spins = 0\n\n  // TODO handle deadlock\n  while (true) {\n    const readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    if (readIndex === -2) {\n      throw new Error('_flushSync failed')\n    }\n\n    // process._rawDebug(`(flushSync) readIndex (${readIndex}) writeIndex (${writeIndex})`)\n    if (readIndex !== writeIndex) {\n      // TODO stream timeouts for some reason.\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n    } else {\n      break\n    }\n\n    if (++spins === 10) {\n      throw new Error('_flushSync took too long (10s)')\n    }\n  }\n  // process._rawDebug('flushSync finished')\n}\n\nmodule.exports = ThreadStream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/thread-stream/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/thread-stream/lib/indexes.js":
/*!****************************************************!*\
  !*** ../node_modules/thread-stream/lib/indexes.js ***!
  \****************************************************/
/***/ ((module) => {

eval("\n\nconst WRITE_INDEX = 4\nconst READ_INDEX = 8\n\nmodule.exports = {\n  WRITE_INDEX,\n  READ_INDEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3RocmVhZC1zdHJlYW0vbGliL2luZGV4ZXMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy90aHJlYWQtc3RyZWFtL2xpYi9pbmRleGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBXUklURV9JTkRFWCA9IDRcbmNvbnN0IFJFQURfSU5ERVggPSA4XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBXUklURV9JTkRFWCxcbiAgUkVBRF9JTkRFWFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/thread-stream/lib/indexes.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/thread-stream/lib/wait.js":
/*!*************************************************!*\
  !*** ../node_modules/thread-stream/lib/wait.js ***!
  \*************************************************/
/***/ ((module) => {

eval("\n\nconst MAX_TIMEOUT = 1000\n\nfunction wait (state, index, expected, timeout, done) {\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current === expected) {\n    done(null, 'ok')\n    return\n  }\n  let prior = current\n  const check = (backoff) => {\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        prior = current\n        current = Atomics.load(state, index)\n        if (current === prior) {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        } else {\n          if (current === expected) done(null, 'ok')\n          else done(null, 'not-equal')\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\n// let waitDiffCount = 0\nfunction waitDiff (state, index, expected, timeout, done) {\n  // const id = waitDiffCount++\n  // process._rawDebug(`>>> waitDiff ${id}`)\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current !== expected) {\n    done(null, 'ok')\n    return\n  }\n  const check = (backoff) => {\n    // process._rawDebug(`${id} ${index} current ${current} expected ${expected}`)\n    // process._rawDebug('' + backoff)\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        current = Atomics.load(state, index)\n        if (current !== expected) {\n          done(null, 'ok')\n        } else {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\nmodule.exports = { wait, waitDiff }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/thread-stream/lib/wait.js\n");

/***/ })

};
;