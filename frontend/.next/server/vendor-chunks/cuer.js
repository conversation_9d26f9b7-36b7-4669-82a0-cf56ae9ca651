"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cuer";
exports.ids = ["vendor-chunks/cuer"];
exports.modules = {

/***/ "(ssr)/../node_modules/cuer/_dist/Cuer.js":
/*!******************************************!*\
  !*** ../node_modules/cuer/_dist/Cuer.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cuer: () => (/* binding */ Cuer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _QrCode_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QrCode.js */ \"(ssr)/../node_modules/cuer/_dist/QrCode.js\");\n\n\n\n/**\n * Renders a QR code with a finder pattern, cells, and an `arena` (if provided).\n *\n * @params {@link Cuer.Props}\n * @returns A {@link React.ReactNode}\n */\nfunction Cuer(props) {\n    const { arena, ...rest } = props;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Cuer.Root, { ...rest, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Finder, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Cells, {}), arena && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Arena, { children: typeof arena === 'string' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", { alt: \"Arena\", src: arena, style: {\n                        borderRadius: 1,\n                        height: '100%',\n                        objectFit: 'cover',\n                        width: '100%',\n                    } })) : (arena) }))] }));\n}\n(function (Cuer) {\n    Cuer.Context = react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);\n    /**\n     * Root component for the QR code.\n     *\n     * @params {@link Root.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Root(props) {\n        const { children, size = '100%', value, version, ...rest } = props;\n        // Check if the children contain an `Arena` component.\n        const hasArena = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (react__WEBPACK_IMPORTED_MODULE_1__.Children.map(children, (child) => {\n            if (!react__WEBPACK_IMPORTED_MODULE_1__.isValidElement(child))\n                return null;\n            if (typeof child.type === 'string')\n                return null;\n            if ('displayName' in child.type &&\n                child.type.displayName === 'Arena')\n                return true;\n            return null;\n        }) ?? []).some(Boolean), [children]);\n        // Create the QR code.\n        const qrcode = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n            let errorCorrection = props.errorCorrection;\n            // If the QR code has an arena, use a higher error correction level.\n            if (hasArena && errorCorrection === 'low')\n                errorCorrection = 'medium';\n            return _QrCode_js__WEBPACK_IMPORTED_MODULE_2__.create(value, {\n                errorCorrection,\n                version,\n            });\n        }, [value, hasArena, props.errorCorrection, version]);\n        const cellSize = 1;\n        const edgeSize = qrcode.edgeLength * cellSize;\n        const finderSize = (qrcode.finderLength * cellSize) / 2;\n        const arenaSize = hasArena ? Math.floor(edgeSize / 4) : 0;\n        const context = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => ({ arenaSize, cellSize, edgeSize, qrcode, finderSize }), [arenaSize, edgeSize, qrcode, finderSize]);\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cuer.Context.Provider, { value: context, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"svg\", { ...rest, width: size, height: size, viewBox: `0 0 ${edgeSize} ${edgeSize}`, xmlns: \"http://www.w3.org/2000/svg\", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"title\", { children: \"QR Code\" }), children] }) }));\n    }\n    Cuer.Root = Root;\n    (function (Root) {\n        Root.displayName = 'Root';\n    })(Root = Cuer.Root || (Cuer.Root = {}));\n    /**\n     * Finder component for the QR code. The finder pattern is the squares\n     * on the top left, top right, and bottom left of the QR code.\n     *\n     * @params {@link Finder.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Finder(props) {\n        const { className, fill, innerClassName, radius = 0.25 } = props;\n        const { cellSize, edgeSize, finderSize } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        function Inner({ position }) {\n            let outerX = finderSize - (finderSize - cellSize) - cellSize / 2;\n            if (position === 'top-right')\n                outerX = edgeSize - finderSize - (finderSize - cellSize) - cellSize / 2;\n            let outerY = finderSize - (finderSize - cellSize) - cellSize / 2;\n            if (position === 'bottom-left')\n                outerY = edgeSize - finderSize - (finderSize - cellSize) - cellSize / 2;\n            let innerX = finderSize - cellSize * 1.5;\n            if (position === 'top-right')\n                innerX = edgeSize - finderSize - cellSize * 1.5;\n            let innerY = finderSize - cellSize * 1.5;\n            if (position === 'bottom-left')\n                innerY = edgeSize - finderSize - cellSize * 1.5;\n            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"rect\", { className: className, stroke: fill ?? 'currentColor', fill: \"transparent\", x: outerX, y: outerY, width: cellSize + (finderSize - cellSize) * 2, height: cellSize + (finderSize - cellSize) * 2, rx: 2 * radius * (finderSize - cellSize), ry: 2 * radius * (finderSize - cellSize), strokeWidth: cellSize }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"rect\", { className: innerClassName, fill: fill ?? 'currentColor', x: innerX, y: innerY, width: cellSize * 3, height: cellSize * 3, rx: 2 * radius * cellSize, ry: 2 * radius * cellSize })] }));\n        }\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"top-left\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"top-right\" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Inner, { position: \"bottom-left\" })] }));\n    }\n    Cuer.Finder = Finder;\n    (function (Finder) {\n        Finder.displayName = 'Finder';\n    })(Finder = Cuer.Finder || (Cuer.Finder = {}));\n    /**\n     * Cells for the QR code.\n     *\n     * @params {@link Cells.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Cells(props) {\n        const { className, fill = 'currentColor', inset: inset_ = true, radius = 1, } = props;\n        const { arenaSize, cellSize, qrcode } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        const { edgeLength, finderLength } = qrcode;\n        const path = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n            let path = '';\n            for (let i = 0; i < qrcode.grid.length; i++) {\n                const row = qrcode.grid[i];\n                if (!row)\n                    continue;\n                for (let j = 0; j < row.length; j++) {\n                    const cell = row[j];\n                    if (!cell)\n                        continue;\n                    // Skip rendering dots in arena area.\n                    const start = edgeLength / 2 - arenaSize / 2;\n                    const end = start + arenaSize;\n                    if (i >= start && i <= end && j >= start && j <= end)\n                        continue;\n                    // Skip rendering dots in the finder pattern areas\n                    const topLeftFinder = i < finderLength && j < finderLength;\n                    const topRightFinder = i < finderLength && j >= edgeLength - finderLength;\n                    const bottomLeftFinder = i >= edgeLength - finderLength && j < finderLength;\n                    if (topLeftFinder || topRightFinder || bottomLeftFinder)\n                        continue;\n                    // Add inset for padding\n                    const inset = inset_ ? cellSize * 0.1 : 0;\n                    const innerSize = (cellSize - inset * 2) / 2;\n                    // Calculate center positions\n                    const cx = j * cellSize + cellSize / 2;\n                    const cy = i * cellSize + cellSize / 2;\n                    // Calculate edge positions\n                    const left = cx - innerSize;\n                    const right = cx + innerSize;\n                    const top = cy - innerSize;\n                    const bottom = cy + innerSize;\n                    // Apply corner radius (clamped to maximum of innerSize)\n                    const r = radius * innerSize;\n                    path += [\n                        `M ${left + r},${top}`,\n                        `L ${right - r},${top}`,\n                        `A ${r},${r} 0 0,1 ${right},${top + r}`,\n                        `L ${right},${bottom - r}`,\n                        `A ${r},${r} 0 0,1 ${right - r},${bottom}`,\n                        `L ${left + r},${bottom}`,\n                        `A ${r},${r} 0 0,1 ${left},${bottom - r}`,\n                        `L ${left},${top + r}`,\n                        `A ${r},${r} 0 0,1 ${left + r},${top}`,\n                        'z',\n                    ].join(' ');\n                }\n            }\n            return path;\n        }, [\n            arenaSize,\n            cellSize,\n            edgeLength,\n            finderLength,\n            qrcode.grid,\n            inset_,\n            radius,\n        ]);\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { className: className, d: path, fill: fill });\n    }\n    Cuer.Cells = Cells;\n    (function (Cells) {\n        Cells.displayName = 'Cells';\n    })(Cells = Cuer.Cells || (Cuer.Cells = {}));\n    /**\n     * Arena component for the QR code. The arena is the area in the center\n     * of the QR code that is not part of the finder pattern.\n     *\n     * @params {@link Arena.Props}\n     * @returns A {@link React.ReactNode}\n     */\n    function Arena(props) {\n        const { children } = props;\n        const { arenaSize, cellSize, edgeSize } = react__WEBPACK_IMPORTED_MODULE_1__.useContext(Cuer.Context);\n        const start = Math.ceil(edgeSize / 2 - arenaSize / 2);\n        const size = arenaSize + (arenaSize % 2);\n        const padding = cellSize / 2;\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"foreignObject\", { x: start, y: start, width: size, height: size, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: {\n                    alignItems: 'center',\n                    display: 'flex',\n                    fontSize: 1,\n                    justifyContent: 'center',\n                    height: '100%',\n                    overflow: 'hidden',\n                    width: '100%',\n                    padding,\n                    boxSizing: 'border-box',\n                }, children: children }) }));\n    }\n    Cuer.Arena = Arena;\n    (function (Arena) {\n        Arena.displayName = 'Arena';\n    })(Arena = Cuer.Arena || (Cuer.Arena = {}));\n})(Cuer || (Cuer = {}));\n//# sourceMappingURL=Cuer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/cuer/_dist/Cuer.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/cuer/_dist/QrCode.js":
/*!********************************************!*\
  !*** ../node_modules/cuer/_dist/QrCode.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var qr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! qr */ \"(ssr)/../node_modules/qr/index.js\");\n\nfunction create(value, options = {}) {\n    const { errorCorrection, version } = options;\n    const grid = (0,qr__WEBPACK_IMPORTED_MODULE_0__.encodeQR)(value, 'raw', {\n        border: 0,\n        ecc: errorCorrection,\n        scale: 1,\n        version: version,\n    });\n    const finderLength = 7;\n    const edgeLength = grid.length;\n    return {\n        edgeLength,\n        finderLength,\n        grid,\n        value,\n    };\n}\n//# sourceMappingURL=QrCode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2N1ZXIvX2Rpc3QvUXJDb2RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThCO0FBQ3ZCLG1DQUFtQztBQUMxQyxZQUFZLDJCQUEyQjtBQUN2QyxpQkFBaUIsNENBQVE7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvY3Vlci9fZGlzdC9RckNvZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZW5jb2RlUVIgfSBmcm9tICdxcic7XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlKHZhbHVlLCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCB7IGVycm9yQ29ycmVjdGlvbiwgdmVyc2lvbiB9ID0gb3B0aW9ucztcbiAgICBjb25zdCBncmlkID0gZW5jb2RlUVIodmFsdWUsICdyYXcnLCB7XG4gICAgICAgIGJvcmRlcjogMCxcbiAgICAgICAgZWNjOiBlcnJvckNvcnJlY3Rpb24sXG4gICAgICAgIHNjYWxlOiAxLFxuICAgICAgICB2ZXJzaW9uOiB2ZXJzaW9uLFxuICAgIH0pO1xuICAgIGNvbnN0IGZpbmRlckxlbmd0aCA9IDc7XG4gICAgY29uc3QgZWRnZUxlbmd0aCA9IGdyaWQubGVuZ3RoO1xuICAgIHJldHVybiB7XG4gICAgICAgIGVkZ2VMZW5ndGgsXG4gICAgICAgIGZpbmRlckxlbmd0aCxcbiAgICAgICAgZ3JpZCxcbiAgICAgICAgdmFsdWUsXG4gICAgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVFyQ29kZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/cuer/_dist/QrCode.js\n");

/***/ })

};
;