"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry";
exports.ids = ["vendor-chunks/@opentelemetry"];
exports.modules = {

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/context.js":
/*!*************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/api/context.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextAPI: () => (/* binding */ ContextAPI)\n/* harmony export */ });\n/* harmony import */ var _context_NoopContextManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context/NoopContextManager */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js\");\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/* harmony import */ var _diag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./diag */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\n\n\nvar API_NAME = 'context';\nvar NOOP_CONTEXT_MANAGER = new _context_NoopContextManager__WEBPACK_IMPORTED_MODULE_0__.NoopContextManager();\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Context API\n */\nvar ContextAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function ContextAPI() {\n    }\n    /** Get the singleton instance of the Context API */\n    ContextAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new ContextAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current context manager.\n     *\n     * @returns true if the context manager was successfully registered, else false\n     */\n    ContextAPI.prototype.setGlobalContextManager = function (contextManager) {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_1__.registerGlobal)(API_NAME, contextManager, _diag__WEBPACK_IMPORTED_MODULE_2__.DiagAPI.instance());\n    };\n    /**\n     * Get the currently active context\n     */\n    ContextAPI.prototype.active = function () {\n        return this._getContextManager().active();\n    };\n    /**\n     * Execute a function with an active context\n     *\n     * @param context context to be active during function execution\n     * @param fn function to execute in a context\n     * @param thisArg optional receiver to be used for calling fn\n     * @param args optional arguments forwarded to fn\n     */\n    ContextAPI.prototype.with = function (context, fn, thisArg) {\n        var _a;\n        var args = [];\n        for (var _i = 3; _i < arguments.length; _i++) {\n            args[_i - 3] = arguments[_i];\n        }\n        return (_a = this._getContextManager()).with.apply(_a, __spreadArray([context, fn, thisArg], __read(args), false));\n    };\n    /**\n     * Bind a context to a target function or event emitter\n     *\n     * @param context context to bind to the event emitter or function. Defaults to the currently active context\n     * @param target function or event emitter to bind\n     */\n    ContextAPI.prototype.bind = function (context, target) {\n        return this._getContextManager().bind(context, target);\n    };\n    ContextAPI.prototype._getContextManager = function () {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_1__.getGlobal)(API_NAME) || NOOP_CONTEXT_MANAGER;\n    };\n    /** Disable and remove the global context manager */\n    ContextAPI.prototype.disable = function () {\n        this._getContextManager().disable();\n        (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_1__.unregisterGlobal)(API_NAME, _diag__WEBPACK_IMPORTED_MODULE_2__.DiagAPI.instance());\n    };\n    return ContextAPI;\n}());\n\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/context.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/diag.js":
/*!**********************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/api/diag.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagAPI: () => (/* binding */ DiagAPI)\n/* harmony export */ });\n/* harmony import */ var _diag_ComponentLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../diag/ComponentLogger */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js\");\n/* harmony import */ var _diag_internal_logLevelLogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../diag/internal/logLevelLogger */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js\");\n/* harmony import */ var _diag_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../diag/types */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/types.js\");\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\n\n\n\nvar API_NAME = 'diag';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry internal\n * diagnostic API\n */\nvar DiagAPI = /** @class */ (function () {\n    /**\n     * Private internal constructor\n     * @private\n     */\n    function DiagAPI() {\n        function _logProxy(funcName) {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                var logger = (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.getGlobal)('diag');\n                // shortcut if logger not set\n                if (!logger)\n                    return;\n                return logger[funcName].apply(logger, __spreadArray([], __read(args), false));\n            };\n        }\n        // Using self local variable for minification purposes as 'this' cannot be minified\n        var self = this;\n        // DiagAPI specific functions\n        var setLogger = function (logger, optionsOrLogLevel) {\n            var _a, _b, _c;\n            if (optionsOrLogLevel === void 0) { optionsOrLogLevel = { logLevel: _diag_types__WEBPACK_IMPORTED_MODULE_1__.DiagLogLevel.INFO }; }\n            if (logger === self) {\n                // There isn't much we can do here.\n                // Logging to the console might break the user application.\n                // Try to log to self. If a logger was previously registered it will receive the log.\n                var err = new Error('Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation');\n                self.error((_a = err.stack) !== null && _a !== void 0 ? _a : err.message);\n                return false;\n            }\n            if (typeof optionsOrLogLevel === 'number') {\n                optionsOrLogLevel = {\n                    logLevel: optionsOrLogLevel,\n                };\n            }\n            var oldLogger = (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.getGlobal)('diag');\n            var newLogger = (0,_diag_internal_logLevelLogger__WEBPACK_IMPORTED_MODULE_2__.createLogLevelDiagLogger)((_b = optionsOrLogLevel.logLevel) !== null && _b !== void 0 ? _b : _diag_types__WEBPACK_IMPORTED_MODULE_1__.DiagLogLevel.INFO, logger);\n            // There already is an logger registered. We'll let it know before overwriting it.\n            if (oldLogger && !optionsOrLogLevel.suppressOverrideMessage) {\n                var stack = (_c = new Error().stack) !== null && _c !== void 0 ? _c : '<failed to generate stacktrace>';\n                oldLogger.warn(\"Current logger will be overwritten from \" + stack);\n                newLogger.warn(\"Current logger will overwrite one already registered from \" + stack);\n            }\n            return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.registerGlobal)('diag', newLogger, self, true);\n        };\n        self.setLogger = setLogger;\n        self.disable = function () {\n            (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.unregisterGlobal)(API_NAME, self);\n        };\n        self.createComponentLogger = function (options) {\n            return new _diag_ComponentLogger__WEBPACK_IMPORTED_MODULE_3__.DiagComponentLogger(options);\n        };\n        self.verbose = _logProxy('verbose');\n        self.debug = _logProxy('debug');\n        self.info = _logProxy('info');\n        self.warn = _logProxy('warn');\n        self.error = _logProxy('error');\n    }\n    /** Get the singleton instance of the DiagAPI API */\n    DiagAPI.instance = function () {\n        if (!this._instance) {\n            this._instance = new DiagAPI();\n        }\n        return this._instance;\n    };\n    return DiagAPI;\n}());\n\n//# sourceMappingURL=diag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/diag.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/metrics.js":
/*!*************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/api/metrics.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MetricsAPI: () => (/* binding */ MetricsAPI)\n/* harmony export */ });\n/* harmony import */ var _metrics_NoopMeterProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../metrics/NoopMeterProvider */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js\");\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/* harmony import */ var _diag__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./diag */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\nvar API_NAME = 'metrics';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Metrics API\n */\nvar MetricsAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function MetricsAPI() {\n    }\n    /** Get the singleton instance of the Metrics API */\n    MetricsAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new MetricsAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current global meter provider.\n     * Returns true if the meter provider was successfully registered, else false.\n     */\n    MetricsAPI.prototype.setGlobalMeterProvider = function (provider) {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.registerGlobal)(API_NAME, provider, _diag__WEBPACK_IMPORTED_MODULE_1__.DiagAPI.instance());\n    };\n    /**\n     * Returns the global meter provider.\n     */\n    MetricsAPI.prototype.getMeterProvider = function () {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.getGlobal)(API_NAME) || _metrics_NoopMeterProvider__WEBPACK_IMPORTED_MODULE_2__.NOOP_METER_PROVIDER;\n    };\n    /**\n     * Returns a meter from the global meter provider.\n     */\n    MetricsAPI.prototype.getMeter = function (name, version, options) {\n        return this.getMeterProvider().getMeter(name, version, options);\n    };\n    /** Remove the global meter provider */\n    MetricsAPI.prototype.disable = function () {\n        (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.unregisterGlobal)(API_NAME, _diag__WEBPACK_IMPORTED_MODULE_1__.DiagAPI.instance());\n    };\n    return MetricsAPI;\n}());\n\n//# sourceMappingURL=metrics.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/metrics.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/propagation.js":
/*!*****************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/api/propagation.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropagationAPI: () => (/* binding */ PropagationAPI)\n/* harmony export */ });\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/* harmony import */ var _propagation_NoopTextMapPropagator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../propagation/NoopTextMapPropagator */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js\");\n/* harmony import */ var _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../propagation/TextMapPropagator */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js\");\n/* harmony import */ var _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../baggage/context-helpers */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js\");\n/* harmony import */ var _baggage_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../baggage/utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/utils.js\");\n/* harmony import */ var _diag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./diag */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\n\n\nvar API_NAME = 'propagation';\nvar NOOP_TEXT_MAP_PROPAGATOR = new _propagation_NoopTextMapPropagator__WEBPACK_IMPORTED_MODULE_0__.NoopTextMapPropagator();\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Propagation API\n */\nvar PropagationAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function PropagationAPI() {\n        this.createBaggage = _baggage_utils__WEBPACK_IMPORTED_MODULE_1__.createBaggage;\n        this.getBaggage = _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__.getBaggage;\n        this.getActiveBaggage = _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__.getActiveBaggage;\n        this.setBaggage = _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__.setBaggage;\n        this.deleteBaggage = _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__.deleteBaggage;\n    }\n    /** Get the singleton instance of the Propagator API */\n    PropagationAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new PropagationAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current propagator.\n     *\n     * @returns true if the propagator was successfully registered, else false\n     */\n    PropagationAPI.prototype.setGlobalPropagator = function (propagator) {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.registerGlobal)(API_NAME, propagator, _diag__WEBPACK_IMPORTED_MODULE_4__.DiagAPI.instance());\n    };\n    /**\n     * Inject context into a carrier to be propagated inter-process\n     *\n     * @param context Context carrying tracing data to inject\n     * @param carrier carrier to inject context into\n     * @param setter Function used to set values on the carrier\n     */\n    PropagationAPI.prototype.inject = function (context, carrier, setter) {\n        if (setter === void 0) { setter = _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_5__.defaultTextMapSetter; }\n        return this._getGlobalPropagator().inject(context, carrier, setter);\n    };\n    /**\n     * Extract context from a carrier\n     *\n     * @param context Context which the newly created context will inherit from\n     * @param carrier Carrier to extract context from\n     * @param getter Function used to extract keys from a carrier\n     */\n    PropagationAPI.prototype.extract = function (context, carrier, getter) {\n        if (getter === void 0) { getter = _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_5__.defaultTextMapGetter; }\n        return this._getGlobalPropagator().extract(context, carrier, getter);\n    };\n    /**\n     * Return a list of all fields which may be used by the propagator.\n     */\n    PropagationAPI.prototype.fields = function () {\n        return this._getGlobalPropagator().fields();\n    };\n    /** Remove the global propagator */\n    PropagationAPI.prototype.disable = function () {\n        (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.unregisterGlobal)(API_NAME, _diag__WEBPACK_IMPORTED_MODULE_4__.DiagAPI.instance());\n    };\n    PropagationAPI.prototype._getGlobalPropagator = function () {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.getGlobal)(API_NAME) || NOOP_TEXT_MAP_PROPAGATOR;\n    };\n    return PropagationAPI;\n}());\n\n//# sourceMappingURL=propagation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/propagation.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/trace.js":
/*!***********************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/api/trace.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TraceAPI: () => (/* binding */ TraceAPI)\n/* harmony export */ });\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/* harmony import */ var _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../trace/ProxyTracerProvider */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js\");\n/* harmony import */ var _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../trace/spancontext-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js\");\n/* harmony import */ var _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../trace/context-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/context-utils.js\");\n/* harmony import */ var _diag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./diag */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\n\nvar API_NAME = 'trace';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Tracing API\n */\nvar TraceAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function TraceAPI() {\n        this._proxyTracerProvider = new _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_0__.ProxyTracerProvider();\n        this.wrapSpanContext = _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_1__.wrapSpanContext;\n        this.isSpanContextValid = _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_1__.isSpanContextValid;\n        this.deleteSpan = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.deleteSpan;\n        this.getSpan = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.getSpan;\n        this.getActiveSpan = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveSpan;\n        this.getSpanContext = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.getSpanContext;\n        this.setSpan = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.setSpan;\n        this.setSpanContext = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.setSpanContext;\n    }\n    /** Get the singleton instance of the Trace API */\n    TraceAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new TraceAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current global tracer.\n     *\n     * @returns true if the tracer provider was successfully registered, else false\n     */\n    TraceAPI.prototype.setGlobalTracerProvider = function (provider) {\n        var success = (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.registerGlobal)(API_NAME, this._proxyTracerProvider, _diag__WEBPACK_IMPORTED_MODULE_4__.DiagAPI.instance());\n        if (success) {\n            this._proxyTracerProvider.setDelegate(provider);\n        }\n        return success;\n    };\n    /**\n     * Returns the global tracer provider.\n     */\n    TraceAPI.prototype.getTracerProvider = function () {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.getGlobal)(API_NAME) || this._proxyTracerProvider;\n    };\n    /**\n     * Returns a tracer from the global tracer provider.\n     */\n    TraceAPI.prototype.getTracer = function (name, version) {\n        return this.getTracerProvider().getTracer(name, version);\n    };\n    /** Remove the global tracer provider */\n    TraceAPI.prototype.disable = function () {\n        (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.unregisterGlobal)(API_NAME, _diag__WEBPACK_IMPORTED_MODULE_4__.DiagAPI.instance());\n        this._proxyTracerProvider = new _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_0__.ProxyTracerProvider();\n    };\n    return TraceAPI;\n}());\n\n//# sourceMappingURL=trace.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/trace.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js":
/*!*************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteBaggage: () => (/* binding */ deleteBaggage),\n/* harmony export */   getActiveBaggage: () => (/* binding */ getActiveBaggage),\n/* harmony export */   getBaggage: () => (/* binding */ getBaggage),\n/* harmony export */   setBaggage: () => (/* binding */ setBaggage)\n/* harmony export */ });\n/* harmony import */ var _api_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../api/context */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/context.js\");\n/* harmony import */ var _context_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context/context */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/context.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n/**\n * Baggage key\n */\nvar BAGGAGE_KEY = (0,_context_context__WEBPACK_IMPORTED_MODULE_0__.createContextKey)('OpenTelemetry Baggage Key');\n/**\n * Retrieve the current baggage from the given context\n *\n * @param {Context} Context that manage all context values\n * @returns {Baggage} Extracted baggage from the context\n */\nfunction getBaggage(context) {\n    return context.getValue(BAGGAGE_KEY) || undefined;\n}\n/**\n * Retrieve the current baggage from the active/current context\n *\n * @returns {Baggage} Extracted baggage from the context\n */\nfunction getActiveBaggage() {\n    return getBaggage(_api_context__WEBPACK_IMPORTED_MODULE_1__.ContextAPI.getInstance().active());\n}\n/**\n * Store a baggage in the given context\n *\n * @param {Context} Context that manage all context values\n * @param {Baggage} baggage that will be set in the actual context\n */\nfunction setBaggage(context, baggage) {\n    return context.setValue(BAGGAGE_KEY, baggage);\n}\n/**\n * Delete the baggage stored in the given context\n *\n * @param {Context} Context that manage all context values\n */\nfunction deleteBaggage(context) {\n    return context.deleteValue(BAGGAGE_KEY);\n}\n//# sourceMappingURL=context-helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js":
/*!*******************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaggageImpl: () => (/* binding */ BaggageImpl)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar BaggageImpl = /** @class */ (function () {\n    function BaggageImpl(entries) {\n        this._entries = entries ? new Map(entries) : new Map();\n    }\n    BaggageImpl.prototype.getEntry = function (key) {\n        var entry = this._entries.get(key);\n        if (!entry) {\n            return undefined;\n        }\n        return Object.assign({}, entry);\n    };\n    BaggageImpl.prototype.getAllEntries = function () {\n        return Array.from(this._entries.entries()).map(function (_a) {\n            var _b = __read(_a, 2), k = _b[0], v = _b[1];\n            return [k, v];\n        });\n    };\n    BaggageImpl.prototype.setEntry = function (key, entry) {\n        var newBaggage = new BaggageImpl(this._entries);\n        newBaggage._entries.set(key, entry);\n        return newBaggage;\n    };\n    BaggageImpl.prototype.removeEntry = function (key) {\n        var newBaggage = new BaggageImpl(this._entries);\n        newBaggage._entries.delete(key);\n        return newBaggage;\n    };\n    BaggageImpl.prototype.removeEntries = function () {\n        var e_1, _a;\n        var keys = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            keys[_i] = arguments[_i];\n        }\n        var newBaggage = new BaggageImpl(this._entries);\n        try {\n            for (var keys_1 = __values(keys), keys_1_1 = keys_1.next(); !keys_1_1.done; keys_1_1 = keys_1.next()) {\n                var key = keys_1_1.value;\n                newBaggage._entries.delete(key);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (keys_1_1 && !keys_1_1.done && (_a = keys_1.return)) _a.call(keys_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return newBaggage;\n    };\n    BaggageImpl.prototype.clear = function () {\n        return new BaggageImpl();\n    };\n    return BaggageImpl;\n}());\n\n//# sourceMappingURL=baggage-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js":
/*!*************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baggageEntryMetadataSymbol: () => (/* binding */ baggageEntryMetadataSymbol)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Symbol used to make BaggageEntryMetadata an opaque type\n */\nvar baggageEntryMetadataSymbol = Symbol('BaggageEntryMetadata');\n//# sourceMappingURL=symbol.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vYmFnZ2FnZS9pbnRlcm5hbC9zeW1ib2wuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vYmFnZ2FnZS9pbnRlcm5hbC9zeW1ib2wuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKlxuICogU3ltYm9sIHVzZWQgdG8gbWFrZSBCYWdnYWdlRW50cnlNZXRhZGF0YSBhbiBvcGFxdWUgdHlwZVxuICovXG5leHBvcnQgdmFyIGJhZ2dhZ2VFbnRyeU1ldGFkYXRhU3ltYm9sID0gU3ltYm9sKCdCYWdnYWdlRW50cnlNZXRhZGF0YScpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3ltYm9sLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/utils.js":
/*!***************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/baggage/utils.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baggageEntryMetadataFromString: () => (/* binding */ baggageEntryMetadataFromString),\n/* harmony export */   createBaggage: () => (/* binding */ createBaggage)\n/* harmony export */ });\n/* harmony import */ var _api_diag__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api/diag */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/* harmony import */ var _internal_baggage_impl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internal/baggage-impl */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js\");\n/* harmony import */ var _internal_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./internal/symbol */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\nvar diag = _api_diag__WEBPACK_IMPORTED_MODULE_0__.DiagAPI.instance();\n/**\n * Create a new Baggage with optional entries\n *\n * @param entries An array of baggage entries the new baggage should contain\n */\nfunction createBaggage(entries) {\n    if (entries === void 0) { entries = {}; }\n    return new _internal_baggage_impl__WEBPACK_IMPORTED_MODULE_1__.BaggageImpl(new Map(Object.entries(entries)));\n}\n/**\n * Create a serializable BaggageEntryMetadata object from a string.\n *\n * @param str string metadata. Format is currently not defined by the spec and has no special meaning.\n *\n */\nfunction baggageEntryMetadataFromString(str) {\n    if (typeof str !== 'string') {\n        diag.error(\"Cannot create baggage metadata from unknown type: \" + typeof str);\n        str = '';\n    }\n    return {\n        __TYPE__: _internal_symbol__WEBPACK_IMPORTED_MODULE_2__.baggageEntryMetadataSymbol,\n        toString: function () {\n            return str;\n        },\n    };\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/utils.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context-api.js":
/*!*************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/context-api.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   context: () => (/* binding */ context)\n/* harmony export */ });\n/* harmony import */ var _api_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/context */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/context.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/** Entrypoint for context API */\nvar context = _api_context__WEBPACK_IMPORTED_MODULE_0__.ContextAPI.getInstance();\n//# sourceMappingURL=context-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vY29udGV4dC1hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzJDO0FBQzNDO0FBQ08sY0FBYyxvREFBVTtBQUMvQiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9jb250ZXh0LWFwaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLy8gU3BsaXQgbW9kdWxlLWxldmVsIHZhcmlhYmxlIGRlZmluaXRpb24gaW50byBzZXBhcmF0ZSBmaWxlcyB0byBhbGxvd1xuLy8gdHJlZS1zaGFraW5nIG9uIGVhY2ggYXBpIGluc3RhbmNlLlxuaW1wb3J0IHsgQ29udGV4dEFQSSB9IGZyb20gJy4vYXBpL2NvbnRleHQnO1xuLyoqIEVudHJ5cG9pbnQgZm9yIGNvbnRleHQgQVBJICovXG5leHBvcnQgdmFyIGNvbnRleHQgPSBDb250ZXh0QVBJLmdldEluc3RhbmNlKCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb250ZXh0LWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context-api.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js":
/*!****************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoopContextManager: () => (/* binding */ NoopContextManager)\n/* harmony export */ });\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/context.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nvar NoopContextManager = /** @class */ (function () {\n    function NoopContextManager() {\n    }\n    NoopContextManager.prototype.active = function () {\n        return _context__WEBPACK_IMPORTED_MODULE_0__.ROOT_CONTEXT;\n    };\n    NoopContextManager.prototype.with = function (_context, fn, thisArg) {\n        var args = [];\n        for (var _i = 3; _i < arguments.length; _i++) {\n            args[_i - 3] = arguments[_i];\n        }\n        return fn.call.apply(fn, __spreadArray([thisArg], __read(args), false));\n    };\n    NoopContextManager.prototype.bind = function (_context, target) {\n        return target;\n    };\n    NoopContextManager.prototype.enable = function () {\n        return this;\n    };\n    NoopContextManager.prototype.disable = function () {\n        return this;\n    };\n    return NoopContextManager;\n}());\n\n//# sourceMappingURL=NoopContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vY29udGV4dC9Ob29wQ29udGV4dE1hbmFnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLFNBQUksSUFBSSxTQUFJO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixNQUFNO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixTQUFJLElBQUksU0FBSTtBQUNqQyw2RUFBNkUsT0FBTztBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUN5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsa0RBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHVCQUF1QjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUM2QjtBQUM5QiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9jb250ZXh0L05vb3BDb250ZXh0TWFuYWdlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xudmFyIF9fcmVhZCA9ICh0aGlzICYmIHRoaXMuX19yZWFkKSB8fCBmdW5jdGlvbiAobywgbikge1xuICAgIHZhciBtID0gdHlwZW9mIFN5bWJvbCA9PT0gXCJmdW5jdGlvblwiICYmIG9bU3ltYm9sLml0ZXJhdG9yXTtcbiAgICBpZiAoIW0pIHJldHVybiBvO1xuICAgIHZhciBpID0gbS5jYWxsKG8pLCByLCBhciA9IFtdLCBlO1xuICAgIHRyeSB7XG4gICAgICAgIHdoaWxlICgobiA9PT0gdm9pZCAwIHx8IG4tLSA+IDApICYmICEociA9IGkubmV4dCgpKS5kb25lKSBhci5wdXNoKHIudmFsdWUpO1xuICAgIH1cbiAgICBjYXRjaCAoZXJyb3IpIHsgZSA9IHsgZXJyb3I6IGVycm9yIH07IH1cbiAgICBmaW5hbGx5IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGlmIChyICYmICFyLmRvbmUgJiYgKG0gPSBpW1wicmV0dXJuXCJdKSkgbS5jYWxsKGkpO1xuICAgICAgICB9XG4gICAgICAgIGZpbmFsbHkgeyBpZiAoZSkgdGhyb3cgZS5lcnJvcjsgfVxuICAgIH1cbiAgICByZXR1cm4gYXI7XG59O1xudmFyIF9fc3ByZWFkQXJyYXkgPSAodGhpcyAmJiB0aGlzLl9fc3ByZWFkQXJyYXkpIHx8IGZ1bmN0aW9uICh0bywgZnJvbSwgcGFjaykge1xuICAgIGlmIChwYWNrIHx8IGFyZ3VtZW50cy5sZW5ndGggPT09IDIpIGZvciAodmFyIGkgPSAwLCBsID0gZnJvbS5sZW5ndGgsIGFyOyBpIDwgbDsgaSsrKSB7XG4gICAgICAgIGlmIChhciB8fCAhKGkgaW4gZnJvbSkpIHtcbiAgICAgICAgICAgIGlmICghYXIpIGFyID0gQXJyYXkucHJvdG90eXBlLnNsaWNlLmNhbGwoZnJvbSwgMCwgaSk7XG4gICAgICAgICAgICBhcltpXSA9IGZyb21baV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRvLmNvbmNhdChhciB8fCBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChmcm9tKSk7XG59O1xuaW1wb3J0IHsgUk9PVF9DT05URVhUIH0gZnJvbSAnLi9jb250ZXh0JztcbnZhciBOb29wQ29udGV4dE1hbmFnZXIgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gTm9vcENvbnRleHRNYW5hZ2VyKCkge1xuICAgIH1cbiAgICBOb29wQ29udGV4dE1hbmFnZXIucHJvdG90eXBlLmFjdGl2ZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIFJPT1RfQ09OVEVYVDtcbiAgICB9O1xuICAgIE5vb3BDb250ZXh0TWFuYWdlci5wcm90b3R5cGUud2l0aCA9IGZ1bmN0aW9uIChfY29udGV4dCwgZm4sIHRoaXNBcmcpIHtcbiAgICAgICAgdmFyIGFyZ3MgPSBbXTtcbiAgICAgICAgZm9yICh2YXIgX2kgPSAzOyBfaSA8IGFyZ3VtZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgICAgIGFyZ3NbX2kgLSAzXSA9IGFyZ3VtZW50c1tfaV07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZuLmNhbGwuYXBwbHkoZm4sIF9fc3ByZWFkQXJyYXkoW3RoaXNBcmddLCBfX3JlYWQoYXJncyksIGZhbHNlKSk7XG4gICAgfTtcbiAgICBOb29wQ29udGV4dE1hbmFnZXIucHJvdG90eXBlLmJpbmQgPSBmdW5jdGlvbiAoX2NvbnRleHQsIHRhcmdldCkge1xuICAgICAgICByZXR1cm4gdGFyZ2V0O1xuICAgIH07XG4gICAgTm9vcENvbnRleHRNYW5hZ2VyLnByb3RvdHlwZS5lbmFibGUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG4gICAgTm9vcENvbnRleHRNYW5hZ2VyLnByb3RvdHlwZS5kaXNhYmxlID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuICAgIHJldHVybiBOb29wQ29udGV4dE1hbmFnZXI7XG59KCkpO1xuZXhwb3J0IHsgTm9vcENvbnRleHRNYW5hZ2VyIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Ob29wQ29udGV4dE1hbmFnZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/context.js":
/*!*****************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/context/context.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ROOT_CONTEXT: () => (/* binding */ ROOT_CONTEXT),\n/* harmony export */   createContextKey: () => (/* binding */ createContextKey)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Get a key to uniquely identify a context value */\nfunction createContextKey(description) {\n    // The specification states that for the same input, multiple calls should\n    // return different keys. Due to the nature of the JS dependency management\n    // system, this creates problems where multiple versions of some package\n    // could hold different keys for the same property.\n    //\n    // Therefore, we use Symbol.for which returns the same key for the same input.\n    return Symbol.for(description);\n}\nvar BaseContext = /** @class */ (function () {\n    /**\n     * Construct a new context which inherits values from an optional parent context.\n     *\n     * @param parentContext a context from which to inherit values\n     */\n    function BaseContext(parentContext) {\n        // for minification\n        var self = this;\n        self._currentContext = parentContext ? new Map(parentContext) : new Map();\n        self.getValue = function (key) { return self._currentContext.get(key); };\n        self.setValue = function (key, value) {\n            var context = new BaseContext(self._currentContext);\n            context._currentContext.set(key, value);\n            return context;\n        };\n        self.deleteValue = function (key) {\n            var context = new BaseContext(self._currentContext);\n            context._currentContext.delete(key);\n            return context;\n        };\n    }\n    return BaseContext;\n}());\n/** The root context is used as the default parent context when there is no active context */\nvar ROOT_CONTEXT = new BaseContext();\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/context.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag-api.js":
/*!**********************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/diag-api.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diag: () => (/* binding */ diag)\n/* harmony export */ });\n/* harmony import */ var _api_diag__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/diag */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/**\n * Entrypoint for Diag API.\n * Defines Diagnostic handler used for internal diagnostic logging operations.\n * The default provides a Noop DiagLogger implementation which may be changed via the\n * diag.setLogger(logger: DiagLogger) function.\n */\nvar diag = _api_diag__WEBPACK_IMPORTED_MODULE_0__.DiagAPI.instance();\n//# sourceMappingURL=diag-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vZGlhZy1hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ3FDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLFdBQVcsOENBQU87QUFDekIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vZGlhZy1hcGkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8vIFNwbGl0IG1vZHVsZS1sZXZlbCB2YXJpYWJsZSBkZWZpbml0aW9uIGludG8gc2VwYXJhdGUgZmlsZXMgdG8gYWxsb3dcbi8vIHRyZWUtc2hha2luZyBvbiBlYWNoIGFwaSBpbnN0YW5jZS5cbmltcG9ydCB7IERpYWdBUEkgfSBmcm9tICcuL2FwaS9kaWFnJztcbi8qKlxuICogRW50cnlwb2ludCBmb3IgRGlhZyBBUEkuXG4gKiBEZWZpbmVzIERpYWdub3N0aWMgaGFuZGxlciB1c2VkIGZvciBpbnRlcm5hbCBkaWFnbm9zdGljIGxvZ2dpbmcgb3BlcmF0aW9ucy5cbiAqIFRoZSBkZWZhdWx0IHByb3ZpZGVzIGEgTm9vcCBEaWFnTG9nZ2VyIGltcGxlbWVudGF0aW9uIHdoaWNoIG1heSBiZSBjaGFuZ2VkIHZpYSB0aGVcbiAqIGRpYWcuc2V0TG9nZ2VyKGxvZ2dlcjogRGlhZ0xvZ2dlcikgZnVuY3Rpb24uXG4gKi9cbmV4cG9ydCB2YXIgZGlhZyA9IERpYWdBUEkuaW5zdGFuY2UoKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRpYWctYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag-api.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js":
/*!**********************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagComponentLogger: () => (/* binding */ DiagComponentLogger)\n/* harmony export */ });\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\n/**\n * Component Logger which is meant to be used as part of any component which\n * will add automatically additional namespace in front of the log message.\n * It will then forward all message to global diag logger\n * @example\n * const cLogger = diag.createComponentLogger({ namespace: '@opentelemetry/instrumentation-http' });\n * cLogger.debug('test');\n * // @opentelemetry/instrumentation-http test\n */\nvar DiagComponentLogger = /** @class */ (function () {\n    function DiagComponentLogger(props) {\n        this._namespace = props.namespace || 'DiagComponentLogger';\n    }\n    DiagComponentLogger.prototype.debug = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('debug', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.error = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('error', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.info = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('info', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.warn = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('warn', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.verbose = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('verbose', this._namespace, args);\n    };\n    return DiagComponentLogger;\n}());\n\nfunction logProxy(funcName, namespace, args) {\n    var logger = (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.getGlobal)('diag');\n    // shortcut if logger not set\n    if (!logger) {\n        return;\n    }\n    args.unshift(namespace);\n    return logger[funcName].apply(logger, __spreadArray([], __read(args), false));\n}\n//# sourceMappingURL=ComponentLogger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js":
/*!********************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagConsoleLogger: () => (/* binding */ DiagConsoleLogger)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar consoleMap = [\n    { n: 'error', c: 'error' },\n    { n: 'warn', c: 'warn' },\n    { n: 'info', c: 'info' },\n    { n: 'debug', c: 'debug' },\n    { n: 'verbose', c: 'trace' },\n];\n/**\n * A simple Immutable Console based diagnostic logger which will output any messages to the Console.\n * If you want to limit the amount of logging to a specific level or lower use the\n * {@link createLogLevelDiagLogger}\n */\nvar DiagConsoleLogger = /** @class */ (function () {\n    function DiagConsoleLogger() {\n        function _consoleFunc(funcName) {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                if (console) {\n                    // Some environments only expose the console when the F12 developer console is open\n                    // eslint-disable-next-line no-console\n                    var theFunc = console[funcName];\n                    if (typeof theFunc !== 'function') {\n                        // Not all environments support all functions\n                        // eslint-disable-next-line no-console\n                        theFunc = console.log;\n                    }\n                    // One last final check\n                    if (typeof theFunc === 'function') {\n                        return theFunc.apply(console, args);\n                    }\n                }\n            };\n        }\n        for (var i = 0; i < consoleMap.length; i++) {\n            this[consoleMap[i].n] = _consoleFunc(consoleMap[i].c);\n        }\n    }\n    return DiagConsoleLogger;\n}());\n\n//# sourceMappingURL=consoleLogger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vZGlhZy9jb25zb2xlTG9nZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sd0JBQXdCO0FBQzlCLE1BQU0sc0JBQXNCO0FBQzVCLE1BQU0sc0JBQXNCO0FBQzVCLE1BQU0sd0JBQXdCO0FBQzlCLE1BQU0sMEJBQTBCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyx1QkFBdUI7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHVCQUF1QjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDNEI7QUFDN0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vZGlhZy9jb25zb2xlTG9nZ2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG52YXIgY29uc29sZU1hcCA9IFtcbiAgICB7IG46ICdlcnJvcicsIGM6ICdlcnJvcicgfSxcbiAgICB7IG46ICd3YXJuJywgYzogJ3dhcm4nIH0sXG4gICAgeyBuOiAnaW5mbycsIGM6ICdpbmZvJyB9LFxuICAgIHsgbjogJ2RlYnVnJywgYzogJ2RlYnVnJyB9LFxuICAgIHsgbjogJ3ZlcmJvc2UnLCBjOiAndHJhY2UnIH0sXG5dO1xuLyoqXG4gKiBBIHNpbXBsZSBJbW11dGFibGUgQ29uc29sZSBiYXNlZCBkaWFnbm9zdGljIGxvZ2dlciB3aGljaCB3aWxsIG91dHB1dCBhbnkgbWVzc2FnZXMgdG8gdGhlIENvbnNvbGUuXG4gKiBJZiB5b3Ugd2FudCB0byBsaW1pdCB0aGUgYW1vdW50IG9mIGxvZ2dpbmcgdG8gYSBzcGVjaWZpYyBsZXZlbCBvciBsb3dlciB1c2UgdGhlXG4gKiB7QGxpbmsgY3JlYXRlTG9nTGV2ZWxEaWFnTG9nZ2VyfVxuICovXG52YXIgRGlhZ0NvbnNvbGVMb2dnZXIgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gRGlhZ0NvbnNvbGVMb2dnZXIoKSB7XG4gICAgICAgIGZ1bmN0aW9uIF9jb25zb2xlRnVuYyhmdW5jTmFtZSkge1xuICAgICAgICAgICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICB2YXIgYXJncyA9IFtdO1xuICAgICAgICAgICAgICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgICAgICAgICAgICAgIGFyZ3NbX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGNvbnNvbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gU29tZSBlbnZpcm9ubWVudHMgb25seSBleHBvc2UgdGhlIGNvbnNvbGUgd2hlbiB0aGUgRjEyIGRldmVsb3BlciBjb25zb2xlIGlzIG9wZW5cbiAgICAgICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnNvbGVcbiAgICAgICAgICAgICAgICAgICAgdmFyIHRoZUZ1bmMgPSBjb25zb2xlW2Z1bmNOYW1lXTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB0aGVGdW5jICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBOb3QgYWxsIGVudmlyb25tZW50cyBzdXBwb3J0IGFsbCBmdW5jdGlvbnNcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb25zb2xlXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGVGdW5jID0gY29uc29sZS5sb2c7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLy8gT25lIGxhc3QgZmluYWwgY2hlY2tcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB0aGVGdW5jID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGhlRnVuYy5hcHBseShjb25zb2xlLCBhcmdzKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBjb25zb2xlTWFwLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICB0aGlzW2NvbnNvbGVNYXBbaV0ubl0gPSBfY29uc29sZUZ1bmMoY29uc29sZU1hcFtpXS5jKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gRGlhZ0NvbnNvbGVMb2dnZXI7XG59KCkpO1xuZXhwb3J0IHsgRGlhZ0NvbnNvbGVMb2dnZXIgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnNvbGVMb2dnZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js":
/*!******************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLogLevelDiagLogger: () => (/* binding */ createLogLevelDiagLogger)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/types.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nfunction createLogLevelDiagLogger(maxLevel, logger) {\n    if (maxLevel < _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.NONE) {\n        maxLevel = _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.NONE;\n    }\n    else if (maxLevel > _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.ALL) {\n        maxLevel = _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.ALL;\n    }\n    // In case the logger is null or undefined\n    logger = logger || {};\n    function _filterFunc(funcName, theLevel) {\n        var theFunc = logger[funcName];\n        if (typeof theFunc === 'function' && maxLevel >= theLevel) {\n            return theFunc.bind(logger);\n        }\n        return function () { };\n    }\n    return {\n        error: _filterFunc('error', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.ERROR),\n        warn: _filterFunc('warn', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.WARN),\n        info: _filterFunc('info', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.INFO),\n        debug: _filterFunc('debug', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.DEBUG),\n        verbose: _filterFunc('verbose', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.VERBOSE),\n    };\n}\n//# sourceMappingURL=logLevelLogger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/types.js":
/*!************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/diag/types.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagLogLevel: () => (/* binding */ DiagLogLevel)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Defines the available internal logging levels for the diagnostic logger, the numeric values\n * of the levels are defined to match the original values from the initial LogLevel to avoid\n * compatibility/migration issues for any implementation that assume the numeric ordering.\n */\nvar DiagLogLevel;\n(function (DiagLogLevel) {\n    /** Diagnostic Logging level setting to disable all logging (except and forced logs) */\n    DiagLogLevel[DiagLogLevel[\"NONE\"] = 0] = \"NONE\";\n    /** Identifies an error scenario */\n    DiagLogLevel[DiagLogLevel[\"ERROR\"] = 30] = \"ERROR\";\n    /** Identifies a warning scenario */\n    DiagLogLevel[DiagLogLevel[\"WARN\"] = 50] = \"WARN\";\n    /** General informational log message */\n    DiagLogLevel[DiagLogLevel[\"INFO\"] = 60] = \"INFO\";\n    /** General debug log message */\n    DiagLogLevel[DiagLogLevel[\"DEBUG\"] = 70] = \"DEBUG\";\n    /**\n     * Detailed trace level logging should only be used for development, should only be set\n     * in a development environment.\n     */\n    DiagLogLevel[DiagLogLevel[\"VERBOSE\"] = 80] = \"VERBOSE\";\n    /** Used to set the logging level to include all logging */\n    DiagLogLevel[DiagLogLevel[\"ALL\"] = 9999] = \"ALL\";\n})(DiagLogLevel || (DiagLogLevel = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/types.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/index.js":
/*!*******************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagConsoleLogger: () => (/* reexport safe */ _diag_consoleLogger__WEBPACK_IMPORTED_MODULE_2__.DiagConsoleLogger),\n/* harmony export */   DiagLogLevel: () => (/* reexport safe */ _diag_types__WEBPACK_IMPORTED_MODULE_3__.DiagLogLevel),\n/* harmony export */   INVALID_SPANID: () => (/* reexport safe */ _trace_invalid_span_constants__WEBPACK_IMPORTED_MODULE_15__.INVALID_SPANID),\n/* harmony export */   INVALID_SPAN_CONTEXT: () => (/* reexport safe */ _trace_invalid_span_constants__WEBPACK_IMPORTED_MODULE_15__.INVALID_SPAN_CONTEXT),\n/* harmony export */   INVALID_TRACEID: () => (/* reexport safe */ _trace_invalid_span_constants__WEBPACK_IMPORTED_MODULE_15__.INVALID_TRACEID),\n/* harmony export */   ProxyTracer: () => (/* reexport safe */ _trace_ProxyTracer__WEBPACK_IMPORTED_MODULE_7__.ProxyTracer),\n/* harmony export */   ProxyTracerProvider: () => (/* reexport safe */ _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_8__.ProxyTracerProvider),\n/* harmony export */   ROOT_CONTEXT: () => (/* reexport safe */ _context_context__WEBPACK_IMPORTED_MODULE_1__.ROOT_CONTEXT),\n/* harmony export */   SamplingDecision: () => (/* reexport safe */ _trace_SamplingResult__WEBPACK_IMPORTED_MODULE_9__.SamplingDecision),\n/* harmony export */   SpanKind: () => (/* reexport safe */ _trace_span_kind__WEBPACK_IMPORTED_MODULE_10__.SpanKind),\n/* harmony export */   SpanStatusCode: () => (/* reexport safe */ _trace_status__WEBPACK_IMPORTED_MODULE_11__.SpanStatusCode),\n/* harmony export */   TraceFlags: () => (/* reexport safe */ _trace_trace_flags__WEBPACK_IMPORTED_MODULE_12__.TraceFlags),\n/* harmony export */   ValueType: () => (/* reexport safe */ _metrics_Metric__WEBPACK_IMPORTED_MODULE_5__.ValueType),\n/* harmony export */   baggageEntryMetadataFromString: () => (/* reexport safe */ _baggage_utils__WEBPACK_IMPORTED_MODULE_0__.baggageEntryMetadataFromString),\n/* harmony export */   context: () => (/* reexport safe */ _context_api__WEBPACK_IMPORTED_MODULE_16__.context),\n/* harmony export */   createContextKey: () => (/* reexport safe */ _context_context__WEBPACK_IMPORTED_MODULE_1__.createContextKey),\n/* harmony export */   createNoopMeter: () => (/* reexport safe */ _metrics_NoopMeter__WEBPACK_IMPORTED_MODULE_4__.createNoopMeter),\n/* harmony export */   createTraceState: () => (/* reexport safe */ _trace_internal_utils__WEBPACK_IMPORTED_MODULE_13__.createTraceState),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultTextMapGetter: () => (/* reexport safe */ _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_6__.defaultTextMapGetter),\n/* harmony export */   defaultTextMapSetter: () => (/* reexport safe */ _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_6__.defaultTextMapSetter),\n/* harmony export */   diag: () => (/* reexport safe */ _diag_api__WEBPACK_IMPORTED_MODULE_17__.diag),\n/* harmony export */   isSpanContextValid: () => (/* reexport safe */ _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_14__.isSpanContextValid),\n/* harmony export */   isValidSpanId: () => (/* reexport safe */ _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_14__.isValidSpanId),\n/* harmony export */   isValidTraceId: () => (/* reexport safe */ _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_14__.isValidTraceId),\n/* harmony export */   metrics: () => (/* reexport safe */ _metrics_api__WEBPACK_IMPORTED_MODULE_18__.metrics),\n/* harmony export */   propagation: () => (/* reexport safe */ _propagation_api__WEBPACK_IMPORTED_MODULE_19__.propagation),\n/* harmony export */   trace: () => (/* reexport safe */ _trace_api__WEBPACK_IMPORTED_MODULE_20__.trace)\n/* harmony export */ });\n/* harmony import */ var _baggage_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./baggage/utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/baggage/utils.js\");\n/* harmony import */ var _context_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context/context */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/context.js\");\n/* harmony import */ var _diag_consoleLogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./diag/consoleLogger */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js\");\n/* harmony import */ var _diag_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./diag/types */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag/types.js\");\n/* harmony import */ var _metrics_NoopMeter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./metrics/NoopMeter */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js\");\n/* harmony import */ var _metrics_Metric__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./metrics/Metric */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/Metric.js\");\n/* harmony import */ var _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./propagation/TextMapPropagator */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js\");\n/* harmony import */ var _trace_ProxyTracer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./trace/ProxyTracer */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js\");\n/* harmony import */ var _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./trace/ProxyTracerProvider */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js\");\n/* harmony import */ var _trace_SamplingResult__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./trace/SamplingResult */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js\");\n/* harmony import */ var _trace_span_kind__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./trace/span_kind */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/span_kind.js\");\n/* harmony import */ var _trace_status__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./trace/status */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/status.js\");\n/* harmony import */ var _trace_trace_flags__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./trace/trace_flags */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js\");\n/* harmony import */ var _trace_internal_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./trace/internal/utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js\");\n/* harmony import */ var _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./trace/spancontext-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js\");\n/* harmony import */ var _trace_invalid_span_constants__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./trace/invalid-span-constants */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\");\n/* harmony import */ var _context_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./context-api */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context-api.js\");\n/* harmony import */ var _diag_api__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./diag-api */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/diag-api.js\");\n/* harmony import */ var _metrics_api__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./metrics-api */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics-api.js\");\n/* harmony import */ var _propagation_api__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./propagation-api */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation-api.js\");\n/* harmony import */ var _trace_api__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./trace-api */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Context APIs\n\n// Diag APIs\n\n\n// Metrics APIs\n\n\n// Propagation APIs\n\n\n\n\n\n\n\n\n\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n\n\n\n\n// Named export.\n\n// Default export.\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    context: _context_api__WEBPACK_IMPORTED_MODULE_16__.context,\n    diag: _diag_api__WEBPACK_IMPORTED_MODULE_17__.diag,\n    metrics: _metrics_api__WEBPACK_IMPORTED_MODULE_18__.metrics,\n    propagation: _propagation_api__WEBPACK_IMPORTED_MODULE_19__.propagation,\n    trace: _trace_api__WEBPACK_IMPORTED_MODULE_20__.trace,\n});\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/index.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js":
/*!***********************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getGlobal: () => (/* binding */ getGlobal),\n/* harmony export */   registerGlobal: () => (/* binding */ registerGlobal),\n/* harmony export */   unregisterGlobal: () => (/* binding */ unregisterGlobal)\n/* harmony export */ });\n/* harmony import */ var _platform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../platform */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js\");\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/version.js\");\n/* harmony import */ var _semver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./semver */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/semver.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\nvar major = _version__WEBPACK_IMPORTED_MODULE_0__.VERSION.split('.')[0];\nvar GLOBAL_OPENTELEMETRY_API_KEY = Symbol.for(\"opentelemetry.js.api.\" + major);\nvar _global = _platform__WEBPACK_IMPORTED_MODULE_1__._globalThis;\nfunction registerGlobal(type, instance, diag, allowOverride) {\n    var _a;\n    if (allowOverride === void 0) { allowOverride = false; }\n    var api = (_global[GLOBAL_OPENTELEMETRY_API_KEY] = (_a = _global[GLOBAL_OPENTELEMETRY_API_KEY]) !== null && _a !== void 0 ? _a : {\n        version: _version__WEBPACK_IMPORTED_MODULE_0__.VERSION,\n    });\n    if (!allowOverride && api[type]) {\n        // already registered an API of this type\n        var err = new Error(\"@opentelemetry/api: Attempted duplicate registration of API: \" + type);\n        diag.error(err.stack || err.message);\n        return false;\n    }\n    if (api.version !== _version__WEBPACK_IMPORTED_MODULE_0__.VERSION) {\n        // All registered APIs must be of the same version exactly\n        var err = new Error(\"@opentelemetry/api: Registration of version v\" + api.version + \" for \" + type + \" does not match previously registered API v\" + _version__WEBPACK_IMPORTED_MODULE_0__.VERSION);\n        diag.error(err.stack || err.message);\n        return false;\n    }\n    api[type] = instance;\n    diag.debug(\"@opentelemetry/api: Registered a global for \" + type + \" v\" + _version__WEBPACK_IMPORTED_MODULE_0__.VERSION + \".\");\n    return true;\n}\nfunction getGlobal(type) {\n    var _a, _b;\n    var globalVersion = (_a = _global[GLOBAL_OPENTELEMETRY_API_KEY]) === null || _a === void 0 ? void 0 : _a.version;\n    if (!globalVersion || !(0,_semver__WEBPACK_IMPORTED_MODULE_2__.isCompatible)(globalVersion)) {\n        return;\n    }\n    return (_b = _global[GLOBAL_OPENTELEMETRY_API_KEY]) === null || _b === void 0 ? void 0 : _b[type];\n}\nfunction unregisterGlobal(type, diag) {\n    diag.debug(\"@opentelemetry/api: Unregistering a global for \" + type + \" v\" + _version__WEBPACK_IMPORTED_MODULE_0__.VERSION + \".\");\n    var api = _global[GLOBAL_OPENTELEMETRY_API_KEY];\n    if (api) {\n        delete api[type];\n    }\n}\n//# sourceMappingURL=global-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/semver.js":
/*!*****************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/internal/semver.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _makeCompatibilityCheck: () => (/* binding */ _makeCompatibilityCheck),\n/* harmony export */   isCompatible: () => (/* binding */ isCompatible)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/version.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar re = /^(\\d+)\\.(\\d+)\\.(\\d+)(-(.+))?$/;\n/**\n * Create a function to test an API version to see if it is compatible with the provided ownVersion.\n *\n * The returned function has the following semantics:\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param ownVersion version which should be checked against\n */\nfunction _makeCompatibilityCheck(ownVersion) {\n    var acceptedVersions = new Set([ownVersion]);\n    var rejectedVersions = new Set();\n    var myVersionMatch = ownVersion.match(re);\n    if (!myVersionMatch) {\n        // we cannot guarantee compatibility so we always return noop\n        return function () { return false; };\n    }\n    var ownVersionParsed = {\n        major: +myVersionMatch[1],\n        minor: +myVersionMatch[2],\n        patch: +myVersionMatch[3],\n        prerelease: myVersionMatch[4],\n    };\n    // if ownVersion has a prerelease tag, versions must match exactly\n    if (ownVersionParsed.prerelease != null) {\n        return function isExactmatch(globalVersion) {\n            return globalVersion === ownVersion;\n        };\n    }\n    function _reject(v) {\n        rejectedVersions.add(v);\n        return false;\n    }\n    function _accept(v) {\n        acceptedVersions.add(v);\n        return true;\n    }\n    return function isCompatible(globalVersion) {\n        if (acceptedVersions.has(globalVersion)) {\n            return true;\n        }\n        if (rejectedVersions.has(globalVersion)) {\n            return false;\n        }\n        var globalVersionMatch = globalVersion.match(re);\n        if (!globalVersionMatch) {\n            // cannot parse other version\n            // we cannot guarantee compatibility so we always noop\n            return _reject(globalVersion);\n        }\n        var globalVersionParsed = {\n            major: +globalVersionMatch[1],\n            minor: +globalVersionMatch[2],\n            patch: +globalVersionMatch[3],\n            prerelease: globalVersionMatch[4],\n        };\n        // if globalVersion has a prerelease tag, versions must match exactly\n        if (globalVersionParsed.prerelease != null) {\n            return _reject(globalVersion);\n        }\n        // major versions must match\n        if (ownVersionParsed.major !== globalVersionParsed.major) {\n            return _reject(globalVersion);\n        }\n        if (ownVersionParsed.major === 0) {\n            if (ownVersionParsed.minor === globalVersionParsed.minor &&\n                ownVersionParsed.patch <= globalVersionParsed.patch) {\n                return _accept(globalVersion);\n            }\n            return _reject(globalVersion);\n        }\n        if (ownVersionParsed.minor <= globalVersionParsed.minor) {\n            return _accept(globalVersion);\n        }\n        return _reject(globalVersion);\n    };\n}\n/**\n * Test an API version to see if it is compatible with this API.\n *\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param version version of the API requesting an instance of the global API\n */\nvar isCompatible = _makeCompatibilityCheck(_version__WEBPACK_IMPORTED_MODULE_0__.VERSION);\n//# sourceMappingURL=semver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/internal/semver.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics-api.js":
/*!*************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/metrics-api.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metrics: () => (/* binding */ metrics)\n/* harmony export */ });\n/* harmony import */ var _api_metrics__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/metrics */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/metrics.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/** Entrypoint for metrics API */\nvar metrics = _api_metrics__WEBPACK_IMPORTED_MODULE_0__.MetricsAPI.getInstance();\n//# sourceMappingURL=metrics-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vbWV0cmljcy1hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzJDO0FBQzNDO0FBQ08sY0FBYyxvREFBVTtBQUMvQiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9tZXRyaWNzLWFwaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLy8gU3BsaXQgbW9kdWxlLWxldmVsIHZhcmlhYmxlIGRlZmluaXRpb24gaW50byBzZXBhcmF0ZSBmaWxlcyB0byBhbGxvd1xuLy8gdHJlZS1zaGFraW5nIG9uIGVhY2ggYXBpIGluc3RhbmNlLlxuaW1wb3J0IHsgTWV0cmljc0FQSSB9IGZyb20gJy4vYXBpL21ldHJpY3MnO1xuLyoqIEVudHJ5cG9pbnQgZm9yIG1ldHJpY3MgQVBJICovXG5leHBvcnQgdmFyIG1ldHJpY3MgPSBNZXRyaWNzQVBJLmdldEluc3RhbmNlKCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXRyaWNzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics-api.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/Metric.js":
/*!****************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/metrics/Metric.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValueType: () => (/* binding */ ValueType)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** The Type of value. It describes how the data is reported. */\nvar ValueType;\n(function (ValueType) {\n    ValueType[ValueType[\"INT\"] = 0] = \"INT\";\n    ValueType[ValueType[\"DOUBLE\"] = 1] = \"DOUBLE\";\n})(ValueType || (ValueType = {}));\n//# sourceMappingURL=Metric.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vbWV0cmljcy9NZXRyaWMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxDQUFDLDhCQUE4QjtBQUMvQiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9tZXRyaWNzL01ldHJpYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLyoqIFRoZSBUeXBlIG9mIHZhbHVlLiBJdCBkZXNjcmliZXMgaG93IHRoZSBkYXRhIGlzIHJlcG9ydGVkLiAqL1xuZXhwb3J0IHZhciBWYWx1ZVR5cGU7XG4oZnVuY3Rpb24gKFZhbHVlVHlwZSkge1xuICAgIFZhbHVlVHlwZVtWYWx1ZVR5cGVbXCJJTlRcIl0gPSAwXSA9IFwiSU5UXCI7XG4gICAgVmFsdWVUeXBlW1ZhbHVlVHlwZVtcIkRPVUJMRVwiXSA9IDFdID0gXCJET1VCTEVcIjtcbn0pKFZhbHVlVHlwZSB8fCAoVmFsdWVUeXBlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPU1ldHJpYy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/Metric.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js":
/*!*******************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NOOP_COUNTER_METRIC: () => (/* binding */ NOOP_COUNTER_METRIC),\n/* harmony export */   NOOP_GAUGE_METRIC: () => (/* binding */ NOOP_GAUGE_METRIC),\n/* harmony export */   NOOP_HISTOGRAM_METRIC: () => (/* binding */ NOOP_HISTOGRAM_METRIC),\n/* harmony export */   NOOP_METER: () => (/* binding */ NOOP_METER),\n/* harmony export */   NOOP_OBSERVABLE_COUNTER_METRIC: () => (/* binding */ NOOP_OBSERVABLE_COUNTER_METRIC),\n/* harmony export */   NOOP_OBSERVABLE_GAUGE_METRIC: () => (/* binding */ NOOP_OBSERVABLE_GAUGE_METRIC),\n/* harmony export */   NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC: () => (/* binding */ NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC),\n/* harmony export */   NOOP_UP_DOWN_COUNTER_METRIC: () => (/* binding */ NOOP_UP_DOWN_COUNTER_METRIC),\n/* harmony export */   NoopCounterMetric: () => (/* binding */ NoopCounterMetric),\n/* harmony export */   NoopGaugeMetric: () => (/* binding */ NoopGaugeMetric),\n/* harmony export */   NoopHistogramMetric: () => (/* binding */ NoopHistogramMetric),\n/* harmony export */   NoopMeter: () => (/* binding */ NoopMeter),\n/* harmony export */   NoopMetric: () => (/* binding */ NoopMetric),\n/* harmony export */   NoopObservableCounterMetric: () => (/* binding */ NoopObservableCounterMetric),\n/* harmony export */   NoopObservableGaugeMetric: () => (/* binding */ NoopObservableGaugeMetric),\n/* harmony export */   NoopObservableMetric: () => (/* binding */ NoopObservableMetric),\n/* harmony export */   NoopObservableUpDownCounterMetric: () => (/* binding */ NoopObservableUpDownCounterMetric),\n/* harmony export */   NoopUpDownCounterMetric: () => (/* binding */ NoopUpDownCounterMetric),\n/* harmony export */   createNoopMeter: () => (/* binding */ createNoopMeter)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/**\n * NoopMeter is a noop implementation of the {@link Meter} interface. It reuses\n * constant NoopMetrics for all of its methods.\n */\nvar NoopMeter = /** @class */ (function () {\n    function NoopMeter() {\n    }\n    /**\n     * @see {@link Meter.createGauge}\n     */\n    NoopMeter.prototype.createGauge = function (_name, _options) {\n        return NOOP_GAUGE_METRIC;\n    };\n    /**\n     * @see {@link Meter.createHistogram}\n     */\n    NoopMeter.prototype.createHistogram = function (_name, _options) {\n        return NOOP_HISTOGRAM_METRIC;\n    };\n    /**\n     * @see {@link Meter.createCounter}\n     */\n    NoopMeter.prototype.createCounter = function (_name, _options) {\n        return NOOP_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createUpDownCounter}\n     */\n    NoopMeter.prototype.createUpDownCounter = function (_name, _options) {\n        return NOOP_UP_DOWN_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableGauge}\n     */\n    NoopMeter.prototype.createObservableGauge = function (_name, _options) {\n        return NOOP_OBSERVABLE_GAUGE_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableCounter}\n     */\n    NoopMeter.prototype.createObservableCounter = function (_name, _options) {\n        return NOOP_OBSERVABLE_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableUpDownCounter}\n     */\n    NoopMeter.prototype.createObservableUpDownCounter = function (_name, _options) {\n        return NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.addBatchObservableCallback}\n     */\n    NoopMeter.prototype.addBatchObservableCallback = function (_callback, _observables) { };\n    /**\n     * @see {@link Meter.removeBatchObservableCallback}\n     */\n    NoopMeter.prototype.removeBatchObservableCallback = function (_callback) { };\n    return NoopMeter;\n}());\n\nvar NoopMetric = /** @class */ (function () {\n    function NoopMetric() {\n    }\n    return NoopMetric;\n}());\n\nvar NoopCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopCounterMetric, _super);\n    function NoopCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopCounterMetric.prototype.add = function (_value, _attributes) { };\n    return NoopCounterMetric;\n}(NoopMetric));\n\nvar NoopUpDownCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopUpDownCounterMetric, _super);\n    function NoopUpDownCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopUpDownCounterMetric.prototype.add = function (_value, _attributes) { };\n    return NoopUpDownCounterMetric;\n}(NoopMetric));\n\nvar NoopGaugeMetric = /** @class */ (function (_super) {\n    __extends(NoopGaugeMetric, _super);\n    function NoopGaugeMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopGaugeMetric.prototype.record = function (_value, _attributes) { };\n    return NoopGaugeMetric;\n}(NoopMetric));\n\nvar NoopHistogramMetric = /** @class */ (function (_super) {\n    __extends(NoopHistogramMetric, _super);\n    function NoopHistogramMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopHistogramMetric.prototype.record = function (_value, _attributes) { };\n    return NoopHistogramMetric;\n}(NoopMetric));\n\nvar NoopObservableMetric = /** @class */ (function () {\n    function NoopObservableMetric() {\n    }\n    NoopObservableMetric.prototype.addCallback = function (_callback) { };\n    NoopObservableMetric.prototype.removeCallback = function (_callback) { };\n    return NoopObservableMetric;\n}());\n\nvar NoopObservableCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableCounterMetric, _super);\n    function NoopObservableCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableCounterMetric;\n}(NoopObservableMetric));\n\nvar NoopObservableGaugeMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableGaugeMetric, _super);\n    function NoopObservableGaugeMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableGaugeMetric;\n}(NoopObservableMetric));\n\nvar NoopObservableUpDownCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableUpDownCounterMetric, _super);\n    function NoopObservableUpDownCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableUpDownCounterMetric;\n}(NoopObservableMetric));\n\nvar NOOP_METER = new NoopMeter();\n// Synchronous instruments\nvar NOOP_COUNTER_METRIC = new NoopCounterMetric();\nvar NOOP_GAUGE_METRIC = new NoopGaugeMetric();\nvar NOOP_HISTOGRAM_METRIC = new NoopHistogramMetric();\nvar NOOP_UP_DOWN_COUNTER_METRIC = new NoopUpDownCounterMetric();\n// Asynchronous instruments\nvar NOOP_OBSERVABLE_COUNTER_METRIC = new NoopObservableCounterMetric();\nvar NOOP_OBSERVABLE_GAUGE_METRIC = new NoopObservableGaugeMetric();\nvar NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC = new NoopObservableUpDownCounterMetric();\n/**\n * Create a no-op Meter\n */\nfunction createNoopMeter() {\n    return NOOP_METER;\n}\n//# sourceMappingURL=NoopMeter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js":
/*!***************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NOOP_METER_PROVIDER: () => (/* binding */ NOOP_METER_PROVIDER),\n/* harmony export */   NoopMeterProvider: () => (/* binding */ NoopMeterProvider)\n/* harmony export */ });\n/* harmony import */ var _NoopMeter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NoopMeter */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * An implementation of the {@link MeterProvider} which returns an impotent Meter\n * for all calls to `getMeter`\n */\nvar NoopMeterProvider = /** @class */ (function () {\n    function NoopMeterProvider() {\n    }\n    NoopMeterProvider.prototype.getMeter = function (_name, _version, _options) {\n        return _NoopMeter__WEBPACK_IMPORTED_MODULE_0__.NOOP_METER;\n    };\n    return NoopMeterProvider;\n}());\n\nvar NOOP_METER_PROVIDER = new NoopMeterProvider();\n//# sourceMappingURL=NoopMeterProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js":
/*!**************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _globalThis: () => (/* binding */ _globalThis)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nvar _globalThis = typeof globalThis === 'object' ? globalThis : global;\n//# sourceMappingURL=globalThis.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vcGxhdGZvcm0vbm9kZS9nbG9iYWxUaGlzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9wbGF0Zm9ybS9ub2RlL2dsb2JhbFRoaXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKiBvbmx5IGdsb2JhbHMgdGhhdCBjb21tb24gdG8gbm9kZSBhbmQgYnJvd3NlcnMgYXJlIGFsbG93ZWQgKi9cbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBub2RlL25vLXVuc3VwcG9ydGVkLWZlYXR1cmVzL2VzLWJ1aWx0aW5zXG5leHBvcnQgdmFyIF9nbG9iYWxUaGlzID0gdHlwZW9mIGdsb2JhbFRoaXMgPT09ICdvYmplY3QnID8gZ2xvYmFsVGhpcyA6IGdsb2JhbDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdsb2JhbFRoaXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation-api.js":
/*!*****************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/propagation-api.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   propagation: () => (/* binding */ propagation)\n/* harmony export */ });\n/* harmony import */ var _api_propagation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/propagation */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/propagation.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/** Entrypoint for propagation API */\nvar propagation = _api_propagation__WEBPACK_IMPORTED_MODULE_0__.PropagationAPI.getInstance();\n//# sourceMappingURL=propagation-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vcHJvcGFnYXRpb24tYXBpLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNtRDtBQUNuRDtBQUNPLGtCQUFrQiw0REFBYztBQUN2QyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9wcm9wYWdhdGlvbi1hcGkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8vIFNwbGl0IG1vZHVsZS1sZXZlbCB2YXJpYWJsZSBkZWZpbml0aW9uIGludG8gc2VwYXJhdGUgZmlsZXMgdG8gYWxsb3dcbi8vIHRyZWUtc2hha2luZyBvbiBlYWNoIGFwaSBpbnN0YW5jZS5cbmltcG9ydCB7IFByb3BhZ2F0aW9uQVBJIH0gZnJvbSAnLi9hcGkvcHJvcGFnYXRpb24nO1xuLyoqIEVudHJ5cG9pbnQgZm9yIHByb3BhZ2F0aW9uIEFQSSAqL1xuZXhwb3J0IHZhciBwcm9wYWdhdGlvbiA9IFByb3BhZ2F0aW9uQVBJLmdldEluc3RhbmNlKCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wcm9wYWdhdGlvbi1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation-api.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js":
/*!***********************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoopTextMapPropagator: () => (/* binding */ NoopTextMapPropagator)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * No-op implementations of {@link TextMapPropagator}.\n */\nvar NoopTextMapPropagator = /** @class */ (function () {\n    function NoopTextMapPropagator() {\n    }\n    /** Noop inject function does nothing */\n    NoopTextMapPropagator.prototype.inject = function (_context, _carrier) { };\n    /** Noop extract function does nothing and returns the input context */\n    NoopTextMapPropagator.prototype.extract = function (context, _carrier) {\n        return context;\n    };\n    NoopTextMapPropagator.prototype.fields = function () {\n        return [];\n    };\n    return NoopTextMapPropagator;\n}());\n\n//# sourceMappingURL=NoopTextMapPropagator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js":
/*!*******************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultTextMapGetter: () => (/* binding */ defaultTextMapGetter),\n/* harmony export */   defaultTextMapSetter: () => (/* binding */ defaultTextMapSetter)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar defaultTextMapGetter = {\n    get: function (carrier, key) {\n        if (carrier == null) {\n            return undefined;\n        }\n        return carrier[key];\n    },\n    keys: function (carrier) {\n        if (carrier == null) {\n            return [];\n        }\n        return Object.keys(carrier);\n    },\n};\nvar defaultTextMapSetter = {\n    set: function (carrier, key, value) {\n        if (carrier == null) {\n            return;\n        }\n        carrier[key] = value;\n    },\n};\n//# sourceMappingURL=TextMapPropagator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace-api.js":
/*!***********************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace-api.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trace: () => (/* binding */ trace)\n/* harmony export */ });\n/* harmony import */ var _api_trace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/trace */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/trace.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/** Entrypoint for trace API */\nvar trace = _api_trace__WEBPACK_IMPORTED_MODULE_0__.TraceAPI.getInstance();\n//# sourceMappingURL=trace-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UtYXBpLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUN1QztBQUN2QztBQUNPLFlBQVksZ0RBQVE7QUFDM0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UtYXBpLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vLyBTcGxpdCBtb2R1bGUtbGV2ZWwgdmFyaWFibGUgZGVmaW5pdGlvbiBpbnRvIHNlcGFyYXRlIGZpbGVzIHRvIGFsbG93XG4vLyB0cmVlLXNoYWtpbmcgb24gZWFjaCBhcGkgaW5zdGFuY2UuXG5pbXBvcnQgeyBUcmFjZUFQSSB9IGZyb20gJy4vYXBpL3RyYWNlJztcbi8qKiBFbnRyeXBvaW50IGZvciB0cmFjZSBBUEkgKi9cbmV4cG9ydCB2YXIgdHJhY2UgPSBUcmFjZUFQSS5nZXRJbnN0YW5jZSgpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJhY2UtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace-api.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js":
/*!************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NonRecordingSpan: () => (/* binding */ NonRecordingSpan)\n/* harmony export */ });\n/* harmony import */ var _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./invalid-span-constants */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The NonRecordingSpan is the default {@link Span} that is used when no Span\n * implementation is available. All operations are no-op including context\n * propagation.\n */\nvar NonRecordingSpan = /** @class */ (function () {\n    function NonRecordingSpan(_spanContext) {\n        if (_spanContext === void 0) { _spanContext = _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__.INVALID_SPAN_CONTEXT; }\n        this._spanContext = _spanContext;\n    }\n    // Returns a SpanContext.\n    NonRecordingSpan.prototype.spanContext = function () {\n        return this._spanContext;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setAttribute = function (_key, _value) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setAttributes = function (_attributes) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.addEvent = function (_name, _attributes) {\n        return this;\n    };\n    NonRecordingSpan.prototype.addLink = function (_link) {\n        return this;\n    };\n    NonRecordingSpan.prototype.addLinks = function (_links) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setStatus = function (_status) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.updateName = function (_name) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.end = function (_endTime) { };\n    // isRecording always returns false for NonRecordingSpan.\n    NonRecordingSpan.prototype.isRecording = function () {\n        return false;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.recordException = function (_exception, _time) { };\n    return NonRecordingSpan;\n}());\n\n//# sourceMappingURL=NonRecordingSpan.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UvTm9uUmVjb3JkaW5nU3Bhbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNnRTtBQUNoRTtBQUNBLHdDQUF3QyxZQUFZO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsZUFBZSx5RUFBb0I7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDMkI7QUFDNUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UvTm9uUmVjb3JkaW5nU3Bhbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuaW1wb3J0IHsgSU5WQUxJRF9TUEFOX0NPTlRFWFQgfSBmcm9tICcuL2ludmFsaWQtc3Bhbi1jb25zdGFudHMnO1xuLyoqXG4gKiBUaGUgTm9uUmVjb3JkaW5nU3BhbiBpcyB0aGUgZGVmYXVsdCB7QGxpbmsgU3Bhbn0gdGhhdCBpcyB1c2VkIHdoZW4gbm8gU3BhblxuICogaW1wbGVtZW50YXRpb24gaXMgYXZhaWxhYmxlLiBBbGwgb3BlcmF0aW9ucyBhcmUgbm8tb3AgaW5jbHVkaW5nIGNvbnRleHRcbiAqIHByb3BhZ2F0aW9uLlxuICovXG52YXIgTm9uUmVjb3JkaW5nU3BhbiA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcbiAgICBmdW5jdGlvbiBOb25SZWNvcmRpbmdTcGFuKF9zcGFuQ29udGV4dCkge1xuICAgICAgICBpZiAoX3NwYW5Db250ZXh0ID09PSB2b2lkIDApIHsgX3NwYW5Db250ZXh0ID0gSU5WQUxJRF9TUEFOX0NPTlRFWFQ7IH1cbiAgICAgICAgdGhpcy5fc3BhbkNvbnRleHQgPSBfc3BhbkNvbnRleHQ7XG4gICAgfVxuICAgIC8vIFJldHVybnMgYSBTcGFuQ29udGV4dC5cbiAgICBOb25SZWNvcmRpbmdTcGFuLnByb3RvdHlwZS5zcGFuQ29udGV4dCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3NwYW5Db250ZXh0O1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCBkb2VzIG5vdGhpbmdcbiAgICBOb25SZWNvcmRpbmdTcGFuLnByb3RvdHlwZS5zZXRBdHRyaWJ1dGUgPSBmdW5jdGlvbiAoX2tleSwgX3ZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCBkb2VzIG5vdGhpbmdcbiAgICBOb25SZWNvcmRpbmdTcGFuLnByb3RvdHlwZS5zZXRBdHRyaWJ1dGVzID0gZnVuY3Rpb24gKF9hdHRyaWJ1dGVzKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCBkb2VzIG5vdGhpbmdcbiAgICBOb25SZWNvcmRpbmdTcGFuLnByb3RvdHlwZS5hZGRFdmVudCA9IGZ1bmN0aW9uIChfbmFtZSwgX2F0dHJpYnV0ZXMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcbiAgICBOb25SZWNvcmRpbmdTcGFuLnByb3RvdHlwZS5hZGRMaW5rID0gZnVuY3Rpb24gKF9saW5rKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG4gICAgTm9uUmVjb3JkaW5nU3Bhbi5wcm90b3R5cGUuYWRkTGlua3MgPSBmdW5jdGlvbiAoX2xpbmtzKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCBkb2VzIG5vdGhpbmdcbiAgICBOb25SZWNvcmRpbmdTcGFuLnByb3RvdHlwZS5zZXRTdGF0dXMgPSBmdW5jdGlvbiAoX3N0YXR1cykge1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuICAgIC8vIEJ5IGRlZmF1bHQgZG9lcyBub3RoaW5nXG4gICAgTm9uUmVjb3JkaW5nU3Bhbi5wcm90b3R5cGUudXBkYXRlTmFtZSA9IGZ1bmN0aW9uIChfbmFtZSkge1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuICAgIC8vIEJ5IGRlZmF1bHQgZG9lcyBub3RoaW5nXG4gICAgTm9uUmVjb3JkaW5nU3Bhbi5wcm90b3R5cGUuZW5kID0gZnVuY3Rpb24gKF9lbmRUaW1lKSB7IH07XG4gICAgLy8gaXNSZWNvcmRpbmcgYWx3YXlzIHJldHVybnMgZmFsc2UgZm9yIE5vblJlY29yZGluZ1NwYW4uXG4gICAgTm9uUmVjb3JkaW5nU3Bhbi5wcm90b3R5cGUuaXNSZWNvcmRpbmcgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9O1xuICAgIC8vIEJ5IGRlZmF1bHQgZG9lcyBub3RoaW5nXG4gICAgTm9uUmVjb3JkaW5nU3Bhbi5wcm90b3R5cGUucmVjb3JkRXhjZXB0aW9uID0gZnVuY3Rpb24gKF9leGNlcHRpb24sIF90aW1lKSB7IH07XG4gICAgcmV0dXJuIE5vblJlY29yZGluZ1NwYW47XG59KCkpO1xuZXhwb3J0IHsgTm9uUmVjb3JkaW5nU3BhbiB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Tm9uUmVjb3JkaW5nU3Bhbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js":
/*!******************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoopTracer: () => (/* binding */ NoopTracer)\n/* harmony export */ });\n/* harmony import */ var _api_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api/context */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/context.js\");\n/* harmony import */ var _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../trace/context-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/context-utils.js\");\n/* harmony import */ var _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NonRecordingSpan */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js\");\n/* harmony import */ var _spancontext_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./spancontext-utils */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\nvar contextApi = _api_context__WEBPACK_IMPORTED_MODULE_0__.ContextAPI.getInstance();\n/**\n * No-op implementations of {@link Tracer}.\n */\nvar NoopTracer = /** @class */ (function () {\n    function NoopTracer() {\n    }\n    // startSpan starts a noop span.\n    NoopTracer.prototype.startSpan = function (name, options, context) {\n        if (context === void 0) { context = contextApi.active(); }\n        var root = Boolean(options === null || options === void 0 ? void 0 : options.root);\n        if (root) {\n            return new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__.NonRecordingSpan();\n        }\n        var parentFromContext = context && (0,_trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.getSpanContext)(context);\n        if (isSpanContext(parentFromContext) &&\n            (0,_spancontext_utils__WEBPACK_IMPORTED_MODULE_3__.isSpanContextValid)(parentFromContext)) {\n            return new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__.NonRecordingSpan(parentFromContext);\n        }\n        else {\n            return new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__.NonRecordingSpan();\n        }\n    };\n    NoopTracer.prototype.startActiveSpan = function (name, arg2, arg3, arg4) {\n        var opts;\n        var ctx;\n        var fn;\n        if (arguments.length < 2) {\n            return;\n        }\n        else if (arguments.length === 2) {\n            fn = arg2;\n        }\n        else if (arguments.length === 3) {\n            opts = arg2;\n            fn = arg3;\n        }\n        else {\n            opts = arg2;\n            ctx = arg3;\n            fn = arg4;\n        }\n        var parentContext = ctx !== null && ctx !== void 0 ? ctx : contextApi.active();\n        var span = this.startSpan(name, opts, parentContext);\n        var contextWithSpanSet = (0,_trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.setSpan)(parentContext, span);\n        return contextApi.with(contextWithSpanSet, fn, undefined, span);\n    };\n    return NoopTracer;\n}());\n\nfunction isSpanContext(spanContext) {\n    return (typeof spanContext === 'object' &&\n        typeof spanContext['spanId'] === 'string' &&\n        typeof spanContext['traceId'] === 'string' &&\n        typeof spanContext['traceFlags'] === 'number');\n}\n//# sourceMappingURL=NoopTracer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js":
/*!**************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoopTracerProvider: () => (/* binding */ NoopTracerProvider)\n/* harmony export */ });\n/* harmony import */ var _NoopTracer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NoopTracer */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * An implementation of the {@link TracerProvider} which returns an impotent\n * Tracer for all calls to `getTracer`.\n *\n * All operations are no-op.\n */\nvar NoopTracerProvider = /** @class */ (function () {\n    function NoopTracerProvider() {\n    }\n    NoopTracerProvider.prototype.getTracer = function (_name, _version, _options) {\n        return new _NoopTracer__WEBPACK_IMPORTED_MODULE_0__.NoopTracer();\n    };\n    return NoopTracerProvider;\n}());\n\n//# sourceMappingURL=NoopTracerProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js":
/*!*******************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProxyTracer: () => (/* binding */ ProxyTracer)\n/* harmony export */ });\n/* harmony import */ var _NoopTracer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NoopTracer */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar NOOP_TRACER = new _NoopTracer__WEBPACK_IMPORTED_MODULE_0__.NoopTracer();\n/**\n * Proxy tracer provided by the proxy tracer provider\n */\nvar ProxyTracer = /** @class */ (function () {\n    function ProxyTracer(_provider, name, version, options) {\n        this._provider = _provider;\n        this.name = name;\n        this.version = version;\n        this.options = options;\n    }\n    ProxyTracer.prototype.startSpan = function (name, options, context) {\n        return this._getTracer().startSpan(name, options, context);\n    };\n    ProxyTracer.prototype.startActiveSpan = function (_name, _options, _context, _fn) {\n        var tracer = this._getTracer();\n        return Reflect.apply(tracer.startActiveSpan, tracer, arguments);\n    };\n    /**\n     * Try to get a tracer from the proxy tracer provider.\n     * If the proxy tracer provider has no delegate, return a noop tracer.\n     */\n    ProxyTracer.prototype._getTracer = function () {\n        if (this._delegate) {\n            return this._delegate;\n        }\n        var tracer = this._provider.getDelegateTracer(this.name, this.version, this.options);\n        if (!tracer) {\n            return NOOP_TRACER;\n        }\n        this._delegate = tracer;\n        return this._delegate;\n    };\n    return ProxyTracer;\n}());\n\n//# sourceMappingURL=ProxyTracer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js":
/*!***************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProxyTracerProvider: () => (/* binding */ ProxyTracerProvider)\n/* harmony export */ });\n/* harmony import */ var _ProxyTracer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ProxyTracer */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js\");\n/* harmony import */ var _NoopTracerProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NoopTracerProvider */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\nvar NOOP_TRACER_PROVIDER = new _NoopTracerProvider__WEBPACK_IMPORTED_MODULE_0__.NoopTracerProvider();\n/**\n * Tracer provider which provides {@link ProxyTracer}s.\n *\n * Before a delegate is set, tracers provided are NoOp.\n *   When a delegate is set, traces are provided from the delegate.\n *   When a delegate is set after tracers have already been provided,\n *   all tracers already provided will use the provided delegate implementation.\n */\nvar ProxyTracerProvider = /** @class */ (function () {\n    function ProxyTracerProvider() {\n    }\n    /**\n     * Get a {@link ProxyTracer}\n     */\n    ProxyTracerProvider.prototype.getTracer = function (name, version, options) {\n        var _a;\n        return ((_a = this.getDelegateTracer(name, version, options)) !== null && _a !== void 0 ? _a : new _ProxyTracer__WEBPACK_IMPORTED_MODULE_1__.ProxyTracer(this, name, version, options));\n    };\n    ProxyTracerProvider.prototype.getDelegate = function () {\n        var _a;\n        return (_a = this._delegate) !== null && _a !== void 0 ? _a : NOOP_TRACER_PROVIDER;\n    };\n    /**\n     * Set the delegate tracer provider\n     */\n    ProxyTracerProvider.prototype.setDelegate = function (delegate) {\n        this._delegate = delegate;\n    };\n    ProxyTracerProvider.prototype.getDelegateTracer = function (name, version, options) {\n        var _a;\n        return (_a = this._delegate) === null || _a === void 0 ? void 0 : _a.getTracer(name, version, options);\n    };\n    return ProxyTracerProvider;\n}());\n\n//# sourceMappingURL=ProxyTracerProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js":
/*!**********************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SamplingDecision: () => (/* binding */ SamplingDecision)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @deprecated use the one declared in @opentelemetry/sdk-trace-base instead.\n * A sampling decision that determines how a {@link Span} will be recorded\n * and collected.\n */\nvar SamplingDecision;\n(function (SamplingDecision) {\n    /**\n     * `Span.isRecording() === false`, span will not be recorded and all events\n     * and attributes will be dropped.\n     */\n    SamplingDecision[SamplingDecision[\"NOT_RECORD\"] = 0] = \"NOT_RECORD\";\n    /**\n     * `Span.isRecording() === true`, but `Sampled` flag in {@link TraceFlags}\n     * MUST NOT be set.\n     */\n    SamplingDecision[SamplingDecision[\"RECORD\"] = 1] = \"RECORD\";\n    /**\n     * `Span.isRecording() === true` AND `Sampled` flag in {@link TraceFlags}\n     * MUST be set.\n     */\n    SamplingDecision[SamplingDecision[\"RECORD_AND_SAMPLED\"] = 2] = \"RECORD_AND_SAMPLED\";\n})(SamplingDecision || (SamplingDecision = {}));\n//# sourceMappingURL=SamplingResult.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/context-utils.js":
/*!*********************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/context-utils.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteSpan: () => (/* binding */ deleteSpan),\n/* harmony export */   getActiveSpan: () => (/* binding */ getActiveSpan),\n/* harmony export */   getSpan: () => (/* binding */ getSpan),\n/* harmony export */   getSpanContext: () => (/* binding */ getSpanContext),\n/* harmony export */   setSpan: () => (/* binding */ setSpan),\n/* harmony export */   setSpanContext: () => (/* binding */ setSpanContext)\n/* harmony export */ });\n/* harmony import */ var _context_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context/context */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/context/context.js\");\n/* harmony import */ var _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NonRecordingSpan */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js\");\n/* harmony import */ var _api_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../api/context */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/api/context.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n/**\n * span key\n */\nvar SPAN_KEY = (0,_context_context__WEBPACK_IMPORTED_MODULE_0__.createContextKey)('OpenTelemetry Context Key SPAN');\n/**\n * Return the span if one exists\n *\n * @param context context to get span from\n */\nfunction getSpan(context) {\n    return context.getValue(SPAN_KEY) || undefined;\n}\n/**\n * Gets the span from the current context, if one exists.\n */\nfunction getActiveSpan() {\n    return getSpan(_api_context__WEBPACK_IMPORTED_MODULE_1__.ContextAPI.getInstance().active());\n}\n/**\n * Set the span on a context\n *\n * @param context context to use as parent\n * @param span span to set active\n */\nfunction setSpan(context, span) {\n    return context.setValue(SPAN_KEY, span);\n}\n/**\n * Remove current span stored in the context\n *\n * @param context context to delete span from\n */\nfunction deleteSpan(context) {\n    return context.deleteValue(SPAN_KEY);\n}\n/**\n * Wrap span context in a NoopSpan and set as span in a new\n * context\n *\n * @param context context to set active span on\n * @param spanContext span context to be wrapped\n */\nfunction setSpanContext(context, spanContext) {\n    return setSpan(context, new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_2__.NonRecordingSpan(spanContext));\n}\n/**\n * Get the span context of the span if it exists.\n *\n * @param context context to get values from\n */\nfunction getSpanContext(context) {\n    var _a;\n    return (_a = getSpan(context)) === null || _a === void 0 ? void 0 : _a.spanContext();\n}\n//# sourceMappingURL=context-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/context-utils.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js":
/*!********************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TraceStateImpl: () => (/* binding */ TraceStateImpl)\n/* harmony export */ });\n/* harmony import */ var _tracestate_validators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tracestate-validators */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar MAX_TRACE_STATE_ITEMS = 32;\nvar MAX_TRACE_STATE_LEN = 512;\nvar LIST_MEMBERS_SEPARATOR = ',';\nvar LIST_MEMBER_KEY_VALUE_SPLITTER = '=';\n/**\n * TraceState must be a class and not a simple object type because of the spec\n * requirement (https://www.w3.org/TR/trace-context/#tracestate-field).\n *\n * Here is the list of allowed mutations:\n * - New key-value pair should be added into the beginning of the list\n * - The value of any key can be updated. Modified keys MUST be moved to the\n * beginning of the list.\n */\nvar TraceStateImpl = /** @class */ (function () {\n    function TraceStateImpl(rawTraceState) {\n        this._internalState = new Map();\n        if (rawTraceState)\n            this._parse(rawTraceState);\n    }\n    TraceStateImpl.prototype.set = function (key, value) {\n        // TODO: Benchmark the different approaches(map vs list) and\n        // use the faster one.\n        var traceState = this._clone();\n        if (traceState._internalState.has(key)) {\n            traceState._internalState.delete(key);\n        }\n        traceState._internalState.set(key, value);\n        return traceState;\n    };\n    TraceStateImpl.prototype.unset = function (key) {\n        var traceState = this._clone();\n        traceState._internalState.delete(key);\n        return traceState;\n    };\n    TraceStateImpl.prototype.get = function (key) {\n        return this._internalState.get(key);\n    };\n    TraceStateImpl.prototype.serialize = function () {\n        var _this = this;\n        return this._keys()\n            .reduce(function (agg, key) {\n            agg.push(key + LIST_MEMBER_KEY_VALUE_SPLITTER + _this.get(key));\n            return agg;\n        }, [])\n            .join(LIST_MEMBERS_SEPARATOR);\n    };\n    TraceStateImpl.prototype._parse = function (rawTraceState) {\n        if (rawTraceState.length > MAX_TRACE_STATE_LEN)\n            return;\n        this._internalState = rawTraceState\n            .split(LIST_MEMBERS_SEPARATOR)\n            .reverse() // Store in reverse so new keys (.set(...)) will be placed at the beginning\n            .reduce(function (agg, part) {\n            var listMember = part.trim(); // Optional Whitespace (OWS) handling\n            var i = listMember.indexOf(LIST_MEMBER_KEY_VALUE_SPLITTER);\n            if (i !== -1) {\n                var key = listMember.slice(0, i);\n                var value = listMember.slice(i + 1, part.length);\n                if ((0,_tracestate_validators__WEBPACK_IMPORTED_MODULE_0__.validateKey)(key) && (0,_tracestate_validators__WEBPACK_IMPORTED_MODULE_0__.validateValue)(value)) {\n                    agg.set(key, value);\n                }\n                else {\n                    // TODO: Consider to add warning log\n                }\n            }\n            return agg;\n        }, new Map());\n        // Because of the reverse() requirement, trunc must be done after map is created\n        if (this._internalState.size > MAX_TRACE_STATE_ITEMS) {\n            this._internalState = new Map(Array.from(this._internalState.entries())\n                .reverse() // Use reverse same as original tracestate parse chain\n                .slice(0, MAX_TRACE_STATE_ITEMS));\n        }\n    };\n    TraceStateImpl.prototype._keys = function () {\n        return Array.from(this._internalState.keys()).reverse();\n    };\n    TraceStateImpl.prototype._clone = function () {\n        var traceState = new TraceStateImpl();\n        traceState._internalState = new Map(this._internalState);\n        return traceState;\n    };\n    return TraceStateImpl;\n}());\n\n//# sourceMappingURL=tracestate-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UvaW50ZXJuYWwvdHJhY2VzdGF0ZS1pbXBsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ3FFO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtRUFBVyxTQUFTLHFFQUFhO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUN5QjtBQUMxQiIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS9pbnRlcm5hbC90cmFjZXN0YXRlLWltcGwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbmltcG9ydCB7IHZhbGlkYXRlS2V5LCB2YWxpZGF0ZVZhbHVlIH0gZnJvbSAnLi90cmFjZXN0YXRlLXZhbGlkYXRvcnMnO1xudmFyIE1BWF9UUkFDRV9TVEFURV9JVEVNUyA9IDMyO1xudmFyIE1BWF9UUkFDRV9TVEFURV9MRU4gPSA1MTI7XG52YXIgTElTVF9NRU1CRVJTX1NFUEFSQVRPUiA9ICcsJztcbnZhciBMSVNUX01FTUJFUl9LRVlfVkFMVUVfU1BMSVRURVIgPSAnPSc7XG4vKipcbiAqIFRyYWNlU3RhdGUgbXVzdCBiZSBhIGNsYXNzIGFuZCBub3QgYSBzaW1wbGUgb2JqZWN0IHR5cGUgYmVjYXVzZSBvZiB0aGUgc3BlY1xuICogcmVxdWlyZW1lbnQgKGh0dHBzOi8vd3d3LnczLm9yZy9UUi90cmFjZS1jb250ZXh0LyN0cmFjZXN0YXRlLWZpZWxkKS5cbiAqXG4gKiBIZXJlIGlzIHRoZSBsaXN0IG9mIGFsbG93ZWQgbXV0YXRpb25zOlxuICogLSBOZXcga2V5LXZhbHVlIHBhaXIgc2hvdWxkIGJlIGFkZGVkIGludG8gdGhlIGJlZ2lubmluZyBvZiB0aGUgbGlzdFxuICogLSBUaGUgdmFsdWUgb2YgYW55IGtleSBjYW4gYmUgdXBkYXRlZC4gTW9kaWZpZWQga2V5cyBNVVNUIGJlIG1vdmVkIHRvIHRoZVxuICogYmVnaW5uaW5nIG9mIHRoZSBsaXN0LlxuICovXG52YXIgVHJhY2VTdGF0ZUltcGwgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gVHJhY2VTdGF0ZUltcGwocmF3VHJhY2VTdGF0ZSkge1xuICAgICAgICB0aGlzLl9pbnRlcm5hbFN0YXRlID0gbmV3IE1hcCgpO1xuICAgICAgICBpZiAocmF3VHJhY2VTdGF0ZSlcbiAgICAgICAgICAgIHRoaXMuX3BhcnNlKHJhd1RyYWNlU3RhdGUpO1xuICAgIH1cbiAgICBUcmFjZVN0YXRlSW1wbC5wcm90b3R5cGUuc2V0ID0gZnVuY3Rpb24gKGtleSwgdmFsdWUpIHtcbiAgICAgICAgLy8gVE9ETzogQmVuY2htYXJrIHRoZSBkaWZmZXJlbnQgYXBwcm9hY2hlcyhtYXAgdnMgbGlzdCkgYW5kXG4gICAgICAgIC8vIHVzZSB0aGUgZmFzdGVyIG9uZS5cbiAgICAgICAgdmFyIHRyYWNlU3RhdGUgPSB0aGlzLl9jbG9uZSgpO1xuICAgICAgICBpZiAodHJhY2VTdGF0ZS5faW50ZXJuYWxTdGF0ZS5oYXMoa2V5KSkge1xuICAgICAgICAgICAgdHJhY2VTdGF0ZS5faW50ZXJuYWxTdGF0ZS5kZWxldGUoa2V5KTtcbiAgICAgICAgfVxuICAgICAgICB0cmFjZVN0YXRlLl9pbnRlcm5hbFN0YXRlLnNldChrZXksIHZhbHVlKTtcbiAgICAgICAgcmV0dXJuIHRyYWNlU3RhdGU7XG4gICAgfTtcbiAgICBUcmFjZVN0YXRlSW1wbC5wcm90b3R5cGUudW5zZXQgPSBmdW5jdGlvbiAoa2V5KSB7XG4gICAgICAgIHZhciB0cmFjZVN0YXRlID0gdGhpcy5fY2xvbmUoKTtcbiAgICAgICAgdHJhY2VTdGF0ZS5faW50ZXJuYWxTdGF0ZS5kZWxldGUoa2V5KTtcbiAgICAgICAgcmV0dXJuIHRyYWNlU3RhdGU7XG4gICAgfTtcbiAgICBUcmFjZVN0YXRlSW1wbC5wcm90b3R5cGUuZ2V0ID0gZnVuY3Rpb24gKGtleSkge1xuICAgICAgICByZXR1cm4gdGhpcy5faW50ZXJuYWxTdGF0ZS5nZXQoa2V5KTtcbiAgICB9O1xuICAgIFRyYWNlU3RhdGVJbXBsLnByb3RvdHlwZS5zZXJpYWxpemUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBfdGhpcyA9IHRoaXM7XG4gICAgICAgIHJldHVybiB0aGlzLl9rZXlzKClcbiAgICAgICAgICAgIC5yZWR1Y2UoZnVuY3Rpb24gKGFnZywga2V5KSB7XG4gICAgICAgICAgICBhZ2cucHVzaChrZXkgKyBMSVNUX01FTUJFUl9LRVlfVkFMVUVfU1BMSVRURVIgKyBfdGhpcy5nZXQoa2V5KSk7XG4gICAgICAgICAgICByZXR1cm4gYWdnO1xuICAgICAgICB9LCBbXSlcbiAgICAgICAgICAgIC5qb2luKExJU1RfTUVNQkVSU19TRVBBUkFUT1IpO1xuICAgIH07XG4gICAgVHJhY2VTdGF0ZUltcGwucHJvdG90eXBlLl9wYXJzZSA9IGZ1bmN0aW9uIChyYXdUcmFjZVN0YXRlKSB7XG4gICAgICAgIGlmIChyYXdUcmFjZVN0YXRlLmxlbmd0aCA+IE1BWF9UUkFDRV9TVEFURV9MRU4pXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIHRoaXMuX2ludGVybmFsU3RhdGUgPSByYXdUcmFjZVN0YXRlXG4gICAgICAgICAgICAuc3BsaXQoTElTVF9NRU1CRVJTX1NFUEFSQVRPUilcbiAgICAgICAgICAgIC5yZXZlcnNlKCkgLy8gU3RvcmUgaW4gcmV2ZXJzZSBzbyBuZXcga2V5cyAoLnNldCguLi4pKSB3aWxsIGJlIHBsYWNlZCBhdCB0aGUgYmVnaW5uaW5nXG4gICAgICAgICAgICAucmVkdWNlKGZ1bmN0aW9uIChhZ2csIHBhcnQpIHtcbiAgICAgICAgICAgIHZhciBsaXN0TWVtYmVyID0gcGFydC50cmltKCk7IC8vIE9wdGlvbmFsIFdoaXRlc3BhY2UgKE9XUykgaGFuZGxpbmdcbiAgICAgICAgICAgIHZhciBpID0gbGlzdE1lbWJlci5pbmRleE9mKExJU1RfTUVNQkVSX0tFWV9WQUxVRV9TUExJVFRFUik7XG4gICAgICAgICAgICBpZiAoaSAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgICB2YXIga2V5ID0gbGlzdE1lbWJlci5zbGljZSgwLCBpKTtcbiAgICAgICAgICAgICAgICB2YXIgdmFsdWUgPSBsaXN0TWVtYmVyLnNsaWNlKGkgKyAxLCBwYXJ0Lmxlbmd0aCk7XG4gICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRlS2V5KGtleSkgJiYgdmFsaWRhdGVWYWx1ZSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgYWdnLnNldChrZXksIHZhbHVlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE86IENvbnNpZGVyIHRvIGFkZCB3YXJuaW5nIGxvZ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBhZ2c7XG4gICAgICAgIH0sIG5ldyBNYXAoKSk7XG4gICAgICAgIC8vIEJlY2F1c2Ugb2YgdGhlIHJldmVyc2UoKSByZXF1aXJlbWVudCwgdHJ1bmMgbXVzdCBiZSBkb25lIGFmdGVyIG1hcCBpcyBjcmVhdGVkXG4gICAgICAgIGlmICh0aGlzLl9pbnRlcm5hbFN0YXRlLnNpemUgPiBNQVhfVFJBQ0VfU1RBVEVfSVRFTVMpIHtcbiAgICAgICAgICAgIHRoaXMuX2ludGVybmFsU3RhdGUgPSBuZXcgTWFwKEFycmF5LmZyb20odGhpcy5faW50ZXJuYWxTdGF0ZS5lbnRyaWVzKCkpXG4gICAgICAgICAgICAgICAgLnJldmVyc2UoKSAvLyBVc2UgcmV2ZXJzZSBzYW1lIGFzIG9yaWdpbmFsIHRyYWNlc3RhdGUgcGFyc2UgY2hhaW5cbiAgICAgICAgICAgICAgICAuc2xpY2UoMCwgTUFYX1RSQUNFX1NUQVRFX0lURU1TKSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIFRyYWNlU3RhdGVJbXBsLnByb3RvdHlwZS5fa2V5cyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIEFycmF5LmZyb20odGhpcy5faW50ZXJuYWxTdGF0ZS5rZXlzKCkpLnJldmVyc2UoKTtcbiAgICB9O1xuICAgIFRyYWNlU3RhdGVJbXBsLnByb3RvdHlwZS5fY2xvbmUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciB0cmFjZVN0YXRlID0gbmV3IFRyYWNlU3RhdGVJbXBsKCk7XG4gICAgICAgIHRyYWNlU3RhdGUuX2ludGVybmFsU3RhdGUgPSBuZXcgTWFwKHRoaXMuX2ludGVybmFsU3RhdGUpO1xuICAgICAgICByZXR1cm4gdHJhY2VTdGF0ZTtcbiAgICB9O1xuICAgIHJldHVybiBUcmFjZVN0YXRlSW1wbDtcbn0oKSk7XG5leHBvcnQgeyBUcmFjZVN0YXRlSW1wbCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJhY2VzdGF0ZS1pbXBsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js":
/*!**************************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateKey: () => (/* binding */ validateKey),\n/* harmony export */   validateValue: () => (/* binding */ validateValue)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar VALID_KEY_CHAR_RANGE = '[_0-9a-z-*/]';\nvar VALID_KEY = \"[a-z]\" + VALID_KEY_CHAR_RANGE + \"{0,255}\";\nvar VALID_VENDOR_KEY = \"[a-z0-9]\" + VALID_KEY_CHAR_RANGE + \"{0,240}@[a-z]\" + VALID_KEY_CHAR_RANGE + \"{0,13}\";\nvar VALID_KEY_REGEX = new RegExp(\"^(?:\" + VALID_KEY + \"|\" + VALID_VENDOR_KEY + \")$\");\nvar VALID_VALUE_BASE_REGEX = /^[ -~]{0,255}[!-~]$/;\nvar INVALID_VALUE_COMMA_EQUAL_REGEX = /,|=/;\n/**\n * Key is opaque string up to 256 characters printable. It MUST begin with a\n * lowercase letter, and can only contain lowercase letters a-z, digits 0-9,\n * underscores _, dashes -, asterisks *, and forward slashes /.\n * For multi-tenant vendor scenarios, an at sign (@) can be used to prefix the\n * vendor name. Vendors SHOULD set the tenant ID at the beginning of the key.\n * see https://www.w3.org/TR/trace-context/#key\n */\nfunction validateKey(key) {\n    return VALID_KEY_REGEX.test(key);\n}\n/**\n * Value is opaque string up to 256 characters printable ASCII RFC0020\n * characters (i.e., the range 0x20 to 0x7E) except comma , and =.\n */\nfunction validateValue(value) {\n    return (VALID_VALUE_BASE_REGEX.test(value) &&\n        !INVALID_VALUE_COMMA_EQUAL_REGEX.test(value));\n}\n//# sourceMappingURL=tracestate-validators.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js":
/*!**********************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTraceState: () => (/* binding */ createTraceState)\n/* harmony export */ });\n/* harmony import */ var _tracestate_impl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tracestate-impl */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nfunction createTraceState(rawTraceState) {\n    return new _tracestate_impl__WEBPACK_IMPORTED_MODULE_0__.TraceStateImpl(rawTraceState);\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UvaW50ZXJuYWwvdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDbUQ7QUFDNUM7QUFDUCxlQUFlLDREQUFjO0FBQzdCO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UvaW50ZXJuYWwvdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbmltcG9ydCB7IFRyYWNlU3RhdGVJbXBsIH0gZnJvbSAnLi90cmFjZXN0YXRlLWltcGwnO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVRyYWNlU3RhdGUocmF3VHJhY2VTdGF0ZSkge1xuICAgIHJldHVybiBuZXcgVHJhY2VTdGF0ZUltcGwocmF3VHJhY2VTdGF0ZSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js":
/*!******************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INVALID_SPANID: () => (/* binding */ INVALID_SPANID),\n/* harmony export */   INVALID_SPAN_CONTEXT: () => (/* binding */ INVALID_SPAN_CONTEXT),\n/* harmony export */   INVALID_TRACEID: () => (/* binding */ INVALID_TRACEID)\n/* harmony export */ });\n/* harmony import */ var _trace_flags__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./trace_flags */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar INVALID_SPANID = '0000000000000000';\nvar INVALID_TRACEID = '00000000000000000000000000000000';\nvar INVALID_SPAN_CONTEXT = {\n    traceId: INVALID_TRACEID,\n    spanId: INVALID_SPANID,\n    traceFlags: _trace_flags__WEBPACK_IMPORTED_MODULE_0__.TraceFlags.NONE,\n};\n//# sourceMappingURL=invalid-span-constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UvaW52YWxpZC1zcGFuLWNvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzJDO0FBQ3BDO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQSxnQkFBZ0Isb0RBQVU7QUFDMUI7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS9pbnZhbGlkLXNwYW4tY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5pbXBvcnQgeyBUcmFjZUZsYWdzIH0gZnJvbSAnLi90cmFjZV9mbGFncyc7XG5leHBvcnQgdmFyIElOVkFMSURfU1BBTklEID0gJzAwMDAwMDAwMDAwMDAwMDAnO1xuZXhwb3J0IHZhciBJTlZBTElEX1RSQUNFSUQgPSAnMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAnO1xuZXhwb3J0IHZhciBJTlZBTElEX1NQQU5fQ09OVEVYVCA9IHtcbiAgICB0cmFjZUlkOiBJTlZBTElEX1RSQUNFSUQsXG4gICAgc3BhbklkOiBJTlZBTElEX1NQQU5JRCxcbiAgICB0cmFjZUZsYWdzOiBUcmFjZUZsYWdzLk5PTkUsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW52YWxpZC1zcGFuLWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/span_kind.js":
/*!*****************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/span_kind.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpanKind: () => (/* binding */ SpanKind)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar SpanKind;\n(function (SpanKind) {\n    /** Default value. Indicates that the span is used internally. */\n    SpanKind[SpanKind[\"INTERNAL\"] = 0] = \"INTERNAL\";\n    /**\n     * Indicates that the span covers server-side handling of an RPC or other\n     * remote request.\n     */\n    SpanKind[SpanKind[\"SERVER\"] = 1] = \"SERVER\";\n    /**\n     * Indicates that the span covers the client-side wrapper around an RPC or\n     * other remote request.\n     */\n    SpanKind[SpanKind[\"CLIENT\"] = 2] = \"CLIENT\";\n    /**\n     * Indicates that the span describes producer sending a message to a\n     * broker. Unlike client and server, there is no direct critical path latency\n     * relationship between producer and consumer spans.\n     */\n    SpanKind[SpanKind[\"PRODUCER\"] = 3] = \"PRODUCER\";\n    /**\n     * Indicates that the span describes consumer receiving a message from a\n     * broker. Unlike client and server, there is no direct critical path latency\n     * relationship between producer and consumer spans.\n     */\n    SpanKind[SpanKind[\"CONSUMER\"] = 4] = \"CONSUMER\";\n})(SpanKind || (SpanKind = {}));\n//# sourceMappingURL=span_kind.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/span_kind.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js":
/*!*************************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSpanContextValid: () => (/* binding */ isSpanContextValid),\n/* harmony export */   isValidSpanId: () => (/* binding */ isValidSpanId),\n/* harmony export */   isValidTraceId: () => (/* binding */ isValidTraceId),\n/* harmony export */   wrapSpanContext: () => (/* binding */ wrapSpanContext)\n/* harmony export */ });\n/* harmony import */ var _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./invalid-span-constants */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\");\n/* harmony import */ var _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NonRecordingSpan */ \"(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\nvar VALID_TRACEID_REGEX = /^([0-9a-f]{32})$/i;\nvar VALID_SPANID_REGEX = /^[0-9a-f]{16}$/i;\nfunction isValidTraceId(traceId) {\n    return VALID_TRACEID_REGEX.test(traceId) && traceId !== _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__.INVALID_TRACEID;\n}\nfunction isValidSpanId(spanId) {\n    return VALID_SPANID_REGEX.test(spanId) && spanId !== _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__.INVALID_SPANID;\n}\n/**\n * Returns true if this {@link SpanContext} is valid.\n * @return true if this {@link SpanContext} is valid.\n */\nfunction isSpanContextValid(spanContext) {\n    return (isValidTraceId(spanContext.traceId) && isValidSpanId(spanContext.spanId));\n}\n/**\n * Wrap the given {@link SpanContext} in a new non-recording {@link Span}\n *\n * @param spanContext span context to be wrapped\n * @returns a new non-recording {@link Span} with the provided context\n */\nfunction wrapSpanContext(spanContext) {\n    return new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__.NonRecordingSpan(spanContext);\n}\n//# sourceMappingURL=spancontext-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/status.js":
/*!**************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/status.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpanStatusCode: () => (/* binding */ SpanStatusCode)\n/* harmony export */ });\n/**\n * An enumeration of status codes.\n */\nvar SpanStatusCode;\n(function (SpanStatusCode) {\n    /**\n     * The default status.\n     */\n    SpanStatusCode[SpanStatusCode[\"UNSET\"] = 0] = \"UNSET\";\n    /**\n     * The operation has been validated by an Application developer or\n     * Operator to have completed successfully.\n     */\n    SpanStatusCode[SpanStatusCode[\"OK\"] = 1] = \"OK\";\n    /**\n     * The operation contains an error.\n     */\n    SpanStatusCode[SpanStatusCode[\"ERROR\"] = 2] = \"ERROR\";\n})(SpanStatusCode || (SpanStatusCode = {}));\n//# sourceMappingURL=status.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2Uvc3RhdHVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHdDQUF3QztBQUN6QyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS9zdGF0dXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBbiBlbnVtZXJhdGlvbiBvZiBzdGF0dXMgY29kZXMuXG4gKi9cbmV4cG9ydCB2YXIgU3BhblN0YXR1c0NvZGU7XG4oZnVuY3Rpb24gKFNwYW5TdGF0dXNDb2RlKSB7XG4gICAgLyoqXG4gICAgICogVGhlIGRlZmF1bHQgc3RhdHVzLlxuICAgICAqL1xuICAgIFNwYW5TdGF0dXNDb2RlW1NwYW5TdGF0dXNDb2RlW1wiVU5TRVRcIl0gPSAwXSA9IFwiVU5TRVRcIjtcbiAgICAvKipcbiAgICAgKiBUaGUgb3BlcmF0aW9uIGhhcyBiZWVuIHZhbGlkYXRlZCBieSBhbiBBcHBsaWNhdGlvbiBkZXZlbG9wZXIgb3JcbiAgICAgKiBPcGVyYXRvciB0byBoYXZlIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkuXG4gICAgICovXG4gICAgU3BhblN0YXR1c0NvZGVbU3BhblN0YXR1c0NvZGVbXCJPS1wiXSA9IDFdID0gXCJPS1wiO1xuICAgIC8qKlxuICAgICAqIFRoZSBvcGVyYXRpb24gY29udGFpbnMgYW4gZXJyb3IuXG4gICAgICovXG4gICAgU3BhblN0YXR1c0NvZGVbU3BhblN0YXR1c0NvZGVbXCJFUlJPUlwiXSA9IDJdID0gXCJFUlJPUlwiO1xufSkoU3BhblN0YXR1c0NvZGUgfHwgKFNwYW5TdGF0dXNDb2RlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN0YXR1cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/status.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js":
/*!*******************************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TraceFlags: () => (/* binding */ TraceFlags)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar TraceFlags;\n(function (TraceFlags) {\n    /** Represents no flag set. */\n    TraceFlags[TraceFlags[\"NONE\"] = 0] = \"NONE\";\n    /** Bit to represent whether trace is sampled in trace flags. */\n    TraceFlags[TraceFlags[\"SAMPLED\"] = 1] = \"SAMPLED\";\n})(TraceFlags || (TraceFlags = {}));\n//# sourceMappingURL=trace_flags.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UvdHJhY2VfZmxhZ3MuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0NBQWdDO0FBQ2pDIiwic291cmNlcyI6WyIvVXNlcnMvam9lL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL3RyYWNlL3RyYWNlX2ZsYWdzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5leHBvcnQgdmFyIFRyYWNlRmxhZ3M7XG4oZnVuY3Rpb24gKFRyYWNlRmxhZ3MpIHtcbiAgICAvKiogUmVwcmVzZW50cyBubyBmbGFnIHNldC4gKi9cbiAgICBUcmFjZUZsYWdzW1RyYWNlRmxhZ3NbXCJOT05FXCJdID0gMF0gPSBcIk5PTkVcIjtcbiAgICAvKiogQml0IHRvIHJlcHJlc2VudCB3aGV0aGVyIHRyYWNlIGlzIHNhbXBsZWQgaW4gdHJhY2UgZmxhZ3MuICovXG4gICAgVHJhY2VGbGFnc1tUcmFjZUZsYWdzW1wiU0FNUExFRFwiXSA9IDFdID0gXCJTQU1QTEVEXCI7XG59KShUcmFjZUZsYWdzIHx8IChUcmFjZUZsYWdzID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYWNlX2ZsYWdzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js\n");

/***/ }),

/***/ "(rsc)/../../../node_modules/@opentelemetry/api/build/esm/version.js":
/*!*********************************************************************!*\
  !*** ../../../node_modules/@opentelemetry/api/build/esm/version.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// this is autogenerated file, see scripts/version-update.js\nvar VERSION = '1.9.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQIiwic291cmNlcyI6WyIvVXNlcnMvam9lL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL3ZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8vIHRoaXMgaXMgYXV0b2dlbmVyYXRlZCBmaWxlLCBzZWUgc2NyaXB0cy92ZXJzaW9uLXVwZGF0ZS5qc1xuZXhwb3J0IHZhciBWRVJTSU9OID0gJzEuOS4wJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../../node_modules/@opentelemetry/api/build/esm/version.js\n");

/***/ })

};
;