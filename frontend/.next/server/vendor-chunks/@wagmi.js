"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wagmi";
exports.ids = ["vendor-chunks/@wagmi"];
exports.modules = {

/***/ "(ssr)/../node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js":
/*!********************************************************************!*\
  !*** ../node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinbaseWallet: () => (/* binding */ coinbaseWallet)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\ncoinbaseWallet.type = 'coinbaseWallet';\nfunction coinbaseWallet(parameters = {}) {\n    if (parameters.version === '3' || parameters.headlessMode)\n        return version3(parameters);\n    return version4(parameters);\n}\nfunction version4(parameters) {\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                    params: 'instantOnboarding' in rest && rest.instantOnboarding\n                        ? [{ onboarding: 'instant' }]\n                        : [],\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close?.();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = (await provider.request({\n                method: 'eth_chainId',\n            }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                const preference = (() => {\n                    if (typeof parameters.preference === 'string')\n                        return { options: parameters.preference };\n                    return {\n                        ...parameters.preference,\n                        options: parameters.preference?.options ?? 'all',\n                    };\n                })();\n                const { createCoinbaseWalletSDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/preact\"), __webpack_require__.e(\"vendor-chunks/@coinbase\")]).then(__webpack_require__.bind(__webpack_require__, /*! @coinbase/wallet-sdk */ \"(ssr)/../node_modules/@coinbase/wallet-sdk/dist/index.js\"));\n                const sdk = createCoinbaseWalletSDK({\n                    ...parameters,\n                    appChainIds: config.chains.map((x) => x.id),\n                    preference,\n                });\n                walletProvider = sdk.getProvider();\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\nfunction version3(parameters) {\n    const reloadOnDisconnect = false;\n    let sdk;\n    let walletProvider;\n    let accountsChanged;\n    let chainChanged;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'coinbaseWalletSDK',\n        name: 'Coinbase Wallet',\n        rdns: 'com.coinbase.wallet',\n        type: coinbaseWallet.type,\n        async connect({ chainId } = {}) {\n            try {\n                const provider = await this.getProvider();\n                const accounts = (await provider.request({\n                    method: 'eth_requestAccounts',\n                })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user closed modal|accounts received is empty|user denied account)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            provider.disconnect();\n            provider.close();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return (await provider.request({\n                method: 'eth_accounts',\n            })).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = await provider.request({\n                method: 'eth_chainId',\n            });\n            return Number(chainId);\n        },\n        async getProvider() {\n            if (!walletProvider) {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const CoinbaseWalletSDK = await (async () => {\n                    const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@metamask\"), __webpack_require__.e(\"vendor-chunks/preact\"), __webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/fast-safe-stringify\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/supports-color\"), __webpack_require__.e(\"vendor-chunks/has-flag\"), __webpack_require__.e(\"vendor-chunks/eth-block-tracker\"), __webpack_require__.e(\"vendor-chunks/cbw-sdk\"), __webpack_require__.e(\"vendor-chunks/readable-stream\"), __webpack_require__.e(\"vendor-chunks/eth-json-rpc-filters\"), __webpack_require__.e(\"vendor-chunks/sha.js\"), __webpack_require__.e(\"vendor-chunks/json-rpc-engine\"), __webpack_require__.e(\"vendor-chunks/keccak\"), __webpack_require__.e(\"vendor-chunks/eth-rpc-errors\"), __webpack_require__.e(\"vendor-chunks/async-mutex\"), __webpack_require__.e(\"vendor-chunks/inherits\"), __webpack_require__.e(\"vendor-chunks/superstruct\"), __webpack_require__.e(\"vendor-chunks/xtend\"), __webpack_require__.e(\"vendor-chunks/util-deprecate\"), __webpack_require__.e(\"vendor-chunks/string_decoder\"), __webpack_require__.e(\"vendor-chunks/safe-buffer\"), __webpack_require__.e(\"vendor-chunks/json-rpc-random-id\"), __webpack_require__.e(\"vendor-chunks/eth-query\"), __webpack_require__.e(\"vendor-chunks/bn.js\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! cbw-sdk */ \"(ssr)/../node_modules/cbw-sdk/dist/index.js\", 19));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                sdk = new CoinbaseWalletSDK({ ...parameters, reloadOnDisconnect });\n                // Force types to retrieve private `walletExtension` method from the Coinbase Wallet SDK.\n                const walletExtensionChainId = sdk.walletExtension?.getChainId();\n                const chain = config.chains.find((chain) => parameters.chainId\n                    ? chain.id === parameters.chainId\n                    : chain.id === walletExtensionChainId) || config.chains[0];\n                const chainId = parameters.chainId || chain?.id;\n                const jsonRpcUrl = parameters.jsonRpcUrl || chain?.rpcUrls.default.http[0];\n                walletProvider = sdk.makeWeb3Provider(jsonRpcUrl, chainId);\n            }\n            return walletProvider;\n        },\n        async isAuthorized() {\n            try {\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const chain = config.chains.find((chain) => chain.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_3__.ChainNotConfiguredError());\n            const provider = await this.getProvider();\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chain.id) }],\n                });\n                return chain;\n            }\n            catch (error) {\n                // Indicates chain is not added to provider\n                if (error.code === 4902) {\n                    try {\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else\n                            blockExplorerUrls = chain.blockExplorers?.default.url\n                                ? [chain.blockExplorers?.default.url]\n                                : [];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_4__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [addEthereumChain],\n                        });\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onDisconnect(_error) {\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n        },\n    }));\n}\n//# sourceMappingURL=coinbaseWallet.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb25uZWN0b3JzL2Rpc3QvZXNtL2NvaW5iYXNlV2FsbGV0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF3RTtBQUNvQjtBQUM1RjtBQUNPLHVDQUF1QztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLDREQUFlO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLG1CQUFtQixJQUFJO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsdUJBQXVCO0FBQ3BEO0FBQ0EsaUJBQWlCLGNBQWMsZ0RBQVU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELFNBQVM7QUFDcEUsMkNBQTJDLDBEQUF3QjtBQUNuRTtBQUNBLGlDQUFpQztBQUNqQyxxQkFBcUI7QUFDckI7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsMERBQXdCO0FBQ3REO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGNBQWMsZ0RBQVU7QUFDckMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLHdCQUF3QiwwQkFBMEIsUUFBUSx5UUFBOEI7QUFDeEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsNEJBQTRCLG9DQUFvQztBQUNoRTtBQUNBO0FBQ0EsMEJBQTBCLGtEQUFnQixLQUFLLGdFQUF1QjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixTQUFTLGlEQUFXLFlBQVk7QUFDL0QsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsaURBQVc7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQywwREFBd0I7QUFDMUQ7QUFDQTtBQUNBLDBCQUEwQixrREFBZ0I7QUFDMUM7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxnREFBVTtBQUM1RCxpQkFBaUI7QUFDakIsU0FBUztBQUNUO0FBQ0E7QUFDQSw0Q0FBNEMsU0FBUztBQUNyRCxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw0REFBZTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixVQUFVLElBQUk7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsY0FBYyxnREFBVTtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsU0FBUztBQUNwRSwyQ0FBMkMsMERBQXdCO0FBQ25FO0FBQ0EsaUNBQWlDO0FBQ2pDLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwwREFBd0I7QUFDdEQ7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsY0FBYyxnREFBVTtBQUNyQyxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixlQUFlLFFBQVEsazZDQUFpQjtBQUNwRTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsOENBQThDLG1DQUFtQztBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDRCQUE0QixvQ0FBb0M7QUFDaEU7QUFDQTtBQUNBLDBCQUEwQixrREFBZ0IsS0FBSyxnRUFBdUI7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsU0FBUyxpREFBVyxZQUFZO0FBQy9ELGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGlEQUFXO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsMERBQXdCO0FBQzFEO0FBQ0E7QUFDQSwwQkFBMEIsa0RBQWdCO0FBQzFDO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsZ0RBQVU7QUFDNUQsaUJBQWlCO0FBQ2pCLFNBQVM7QUFDVDtBQUNBO0FBQ0EsNENBQTRDLFNBQVM7QUFDckQsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29ubmVjdG9ycy9kaXN0L2VzbS9jb2luYmFzZVdhbGxldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDaGFpbk5vdENvbmZpZ3VyZWRFcnJvciwgY3JlYXRlQ29ubmVjdG9yLCB9IGZyb20gJ0B3YWdtaS9jb3JlJztcbmltcG9ydCB7IFN3aXRjaENoYWluRXJyb3IsIFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvciwgZ2V0QWRkcmVzcywgbnVtYmVyVG9IZXgsIH0gZnJvbSAndmllbSc7XG5jb2luYmFzZVdhbGxldC50eXBlID0gJ2NvaW5iYXNlV2FsbGV0JztcbmV4cG9ydCBmdW5jdGlvbiBjb2luYmFzZVdhbGxldChwYXJhbWV0ZXJzID0ge30pIHtcbiAgICBpZiAocGFyYW1ldGVycy52ZXJzaW9uID09PSAnMycgfHwgcGFyYW1ldGVycy5oZWFkbGVzc01vZGUpXG4gICAgICAgIHJldHVybiB2ZXJzaW9uMyhwYXJhbWV0ZXJzKTtcbiAgICByZXR1cm4gdmVyc2lvbjQocGFyYW1ldGVycyk7XG59XG5mdW5jdGlvbiB2ZXJzaW9uNChwYXJhbWV0ZXJzKSB7XG4gICAgbGV0IHdhbGxldFByb3ZpZGVyO1xuICAgIGxldCBhY2NvdW50c0NoYW5nZWQ7XG4gICAgbGV0IGNoYWluQ2hhbmdlZDtcbiAgICBsZXQgZGlzY29ubmVjdDtcbiAgICByZXR1cm4gY3JlYXRlQ29ubmVjdG9yKChjb25maWcpID0+ICh7XG4gICAgICAgIGlkOiAnY29pbmJhc2VXYWxsZXRTREsnLFxuICAgICAgICBuYW1lOiAnQ29pbmJhc2UgV2FsbGV0JyxcbiAgICAgICAgcmRuczogJ2NvbS5jb2luYmFzZS53YWxsZXQnLFxuICAgICAgICB0eXBlOiBjb2luYmFzZVdhbGxldC50eXBlLFxuICAgICAgICBhc3luYyBjb25uZWN0KHsgY2hhaW5JZCwgLi4ucmVzdCB9ID0ge30pIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBhd2FpdCB0aGlzLmdldFByb3ZpZGVyKCk7XG4gICAgICAgICAgICAgICAgY29uc3QgYWNjb3VudHMgPSAoYXdhaXQgcHJvdmlkZXIucmVxdWVzdCh7XG4gICAgICAgICAgICAgICAgICAgIG1ldGhvZDogJ2V0aF9yZXF1ZXN0QWNjb3VudHMnLFxuICAgICAgICAgICAgICAgICAgICBwYXJhbXM6ICdpbnN0YW50T25ib2FyZGluZycgaW4gcmVzdCAmJiByZXN0Lmluc3RhbnRPbmJvYXJkaW5nXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFt7IG9uYm9hcmRpbmc6ICdpbnN0YW50JyB9XVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBbXSxcbiAgICAgICAgICAgICAgICB9KSkubWFwKCh4KSA9PiBnZXRBZGRyZXNzKHgpKTtcbiAgICAgICAgICAgICAgICBpZiAoIWFjY291bnRzQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgICAgICBhY2NvdW50c0NoYW5nZWQgPSB0aGlzLm9uQWNjb3VudHNDaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWNoYWluQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB0aGlzLm9uQ2hhaW5DaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjaGFpbkNoYW5nZWQnLCBjaGFpbkNoYW5nZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWRpc2Nvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHRoaXMub25EaXNjb25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFN3aXRjaCB0byBjaGFpbiBpZiBwcm92aWRlZFxuICAgICAgICAgICAgICAgIGxldCBjdXJyZW50Q2hhaW5JZCA9IGF3YWl0IHRoaXMuZ2V0Q2hhaW5JZCgpO1xuICAgICAgICAgICAgICAgIGlmIChjaGFpbklkICYmIGN1cnJlbnRDaGFpbklkICE9PSBjaGFpbklkKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNoYWluID0gYXdhaXQgdGhpcy5zd2l0Y2hDaGFpbih7IGNoYWluSWQgfSkuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZXJyb3IuY29kZSA9PT0gVXNlclJlamVjdGVkUmVxdWVzdEVycm9yLmNvZGUpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4geyBpZDogY3VycmVudENoYWluSWQgfTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRDaGFpbklkID0gY2hhaW4/LmlkID8/IGN1cnJlbnRDaGFpbklkO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4geyBhY2NvdW50cywgY2hhaW5JZDogY3VycmVudENoYWluSWQgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGlmICgvKHVzZXIgY2xvc2VkIG1vZGFsfGFjY291bnRzIHJlY2VpdmVkIGlzIGVtcHR5fHVzZXIgZGVuaWVkIGFjY291bnR8cmVxdWVzdCByZWplY3RlZCkvaS50ZXN0KGVycm9yLm1lc3NhZ2UpKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgVXNlclJlamVjdGVkUmVxdWVzdEVycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZGlzY29ubmVjdCgpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgaWYgKGFjY291bnRzQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgICAgIGFjY291bnRzQ2hhbmdlZCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY2hhaW5DaGFuZ2VkJywgY2hhaW5DaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZGlzY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHByb3ZpZGVyLmRpc2Nvbm5lY3QoKTtcbiAgICAgICAgICAgIHByb3ZpZGVyLmNsb3NlPy4oKTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0QWNjb3VudHMoKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIHJldHVybiAoYXdhaXQgcHJvdmlkZXIucmVxdWVzdCh7XG4gICAgICAgICAgICAgICAgbWV0aG9kOiAnZXRoX2FjY291bnRzJyxcbiAgICAgICAgICAgIH0pKS5tYXAoKHgpID0+IGdldEFkZHJlc3MoeCkpO1xuICAgICAgICB9LFxuICAgICAgICBhc3luYyBnZXRDaGFpbklkKCkge1xuICAgICAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBhd2FpdCB0aGlzLmdldFByb3ZpZGVyKCk7XG4gICAgICAgICAgICBjb25zdCBjaGFpbklkID0gKGF3YWl0IHByb3ZpZGVyLnJlcXVlc3Qoe1xuICAgICAgICAgICAgICAgIG1ldGhvZDogJ2V0aF9jaGFpbklkJyxcbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgIHJldHVybiBOdW1iZXIoY2hhaW5JZCk7XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGdldFByb3ZpZGVyKCkge1xuICAgICAgICAgICAgaWYgKCF3YWxsZXRQcm92aWRlcikge1xuICAgICAgICAgICAgICAgIGNvbnN0IHByZWZlcmVuY2UgPSAoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHBhcmFtZXRlcnMucHJlZmVyZW5jZSA9PT0gJ3N0cmluZycpXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4geyBvcHRpb25zOiBwYXJhbWV0ZXJzLnByZWZlcmVuY2UgfTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnBhcmFtZXRlcnMucHJlZmVyZW5jZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM6IHBhcmFtZXRlcnMucHJlZmVyZW5jZT8ub3B0aW9ucyA/PyAnYWxsJyxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9KSgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgY3JlYXRlQ29pbmJhc2VXYWxsZXRTREsgfSA9IGF3YWl0IGltcG9ydCgnQGNvaW5iYXNlL3dhbGxldC1zZGsnKTtcbiAgICAgICAgICAgICAgICBjb25zdCBzZGsgPSBjcmVhdGVDb2luYmFzZVdhbGxldFNESyh7XG4gICAgICAgICAgICAgICAgICAgIC4uLnBhcmFtZXRlcnMsXG4gICAgICAgICAgICAgICAgICAgIGFwcENoYWluSWRzOiBjb25maWcuY2hhaW5zLm1hcCgoeCkgPT4geC5pZCksXG4gICAgICAgICAgICAgICAgICAgIHByZWZlcmVuY2UsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgd2FsbGV0UHJvdmlkZXIgPSBzZGsuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB3YWxsZXRQcm92aWRlcjtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgaXNBdXRob3JpemVkKCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IHRoaXMuZ2V0QWNjb3VudHMoKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gISFhY2NvdW50cy5sZW5ndGg7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBzd2l0Y2hDaGFpbih7IGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIsIGNoYWluSWQgfSkge1xuICAgICAgICAgICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKGNoYWluKSA9PiBjaGFpbi5pZCA9PT0gY2hhaW5JZCk7XG4gICAgICAgICAgICBpZiAoIWNoYWluKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbkVycm9yKG5ldyBDaGFpbk5vdENvbmZpZ3VyZWRFcnJvcigpKTtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHtcbiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAnd2FsbGV0X3N3aXRjaEV0aGVyZXVtQ2hhaW4nLFxuICAgICAgICAgICAgICAgICAgICBwYXJhbXM6IFt7IGNoYWluSWQ6IG51bWJlclRvSGV4KGNoYWluLmlkKSB9XSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2hhaW47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAvLyBJbmRpY2F0ZXMgY2hhaW4gaXMgbm90IGFkZGVkIHRvIHByb3ZpZGVyXG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IDQ5MDIpIHtcbiAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBibG9ja0V4cGxvcmVyVXJscztcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyPy5ibG9ja0V4cGxvcmVyVXJscylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBibG9ja0V4cGxvcmVyVXJscyA9IGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIuYmxvY2tFeHBsb3JlclVybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmxvY2tFeHBsb3JlclVybHMgPSBjaGFpbi5ibG9ja0V4cGxvcmVycz8uZGVmYXVsdC51cmxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBbY2hhaW4uYmxvY2tFeHBsb3JlcnM/LmRlZmF1bHQudXJsXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IHJwY1VybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8ucnBjVXJscz8ubGVuZ3RoKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJwY1VybHMgPSBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLnJwY1VybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcnBjVXJscyA9IFtjaGFpbi5ycGNVcmxzLmRlZmF1bHQ/Lmh0dHBbMF0gPz8gJyddO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYWRkRXRoZXJldW1DaGFpbiA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBibG9ja0V4cGxvcmVyVXJscyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiBudW1iZXJUb0hleChjaGFpbklkKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbk5hbWU6IGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXI/LmNoYWluTmFtZSA/PyBjaGFpbi5uYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb25VcmxzOiBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyPy5pY29uVXJscyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYXRpdmVDdXJyZW5jeTogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8ubmF0aXZlQ3VycmVuY3kgPz9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hhaW4ubmF0aXZlQ3VycmVuY3ksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcnBjVXJscyxcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICd3YWxsZXRfYWRkRXRoZXJldW1DaGFpbicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zOiBbYWRkRXRoZXJldW1DaGFpbl0sXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjaGFpbjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbkVycm9yKGVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgb25BY2NvdW50c0NoYW5nZWQoYWNjb3VudHMpIHtcbiAgICAgICAgICAgIGlmIChhY2NvdW50cy5sZW5ndGggPT09IDApXG4gICAgICAgICAgICAgICAgdGhpcy5vbkRpc2Nvbm5lY3QoKTtcbiAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICBjb25maWcuZW1pdHRlci5lbWl0KCdjaGFuZ2UnLCB7XG4gICAgICAgICAgICAgICAgICAgIGFjY291bnRzOiBhY2NvdW50cy5tYXAoKHgpID0+IGdldEFkZHJlc3MoeCkpLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgICAgICBvbkNoYWluQ2hhbmdlZChjaGFpbikge1xuICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IE51bWJlcihjaGFpbik7XG4gICAgICAgICAgICBjb25maWcuZW1pdHRlci5lbWl0KCdjaGFuZ2UnLCB7IGNoYWluSWQgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIG9uRGlzY29ubmVjdChfZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2Rpc2Nvbm5lY3QnKTtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgaWYgKGFjY291bnRzQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgICAgIGFjY291bnRzQ2hhbmdlZCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY2hhaW5DaGFuZ2VkJywgY2hhaW5DaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZGlzY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9KSk7XG59XG5mdW5jdGlvbiB2ZXJzaW9uMyhwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgcmVsb2FkT25EaXNjb25uZWN0ID0gZmFsc2U7XG4gICAgbGV0IHNkaztcbiAgICBsZXQgd2FsbGV0UHJvdmlkZXI7XG4gICAgbGV0IGFjY291bnRzQ2hhbmdlZDtcbiAgICBsZXQgY2hhaW5DaGFuZ2VkO1xuICAgIGxldCBkaXNjb25uZWN0O1xuICAgIHJldHVybiBjcmVhdGVDb25uZWN0b3IoKGNvbmZpZykgPT4gKHtcbiAgICAgICAgaWQ6ICdjb2luYmFzZVdhbGxldFNESycsXG4gICAgICAgIG5hbWU6ICdDb2luYmFzZSBXYWxsZXQnLFxuICAgICAgICByZG5zOiAnY29tLmNvaW5iYXNlLndhbGxldCcsXG4gICAgICAgIHR5cGU6IGNvaW5iYXNlV2FsbGV0LnR5cGUsXG4gICAgICAgIGFzeW5jIGNvbm5lY3QoeyBjaGFpbklkIH0gPSB7fSkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IChhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHtcbiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAnZXRoX3JlcXVlc3RBY2NvdW50cycsXG4gICAgICAgICAgICAgICAgfSkpLm1hcCgoeCkgPT4gZ2V0QWRkcmVzcyh4KSk7XG4gICAgICAgICAgICAgICAgaWYgKCFhY2NvdW50c0NoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgYWNjb3VudHNDaGFuZ2VkID0gdGhpcy5vbkFjY291bnRzQ2hhbmdlZC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgICAgICBwcm92aWRlci5vbignYWNjb3VudHNDaGFuZ2VkJywgYWNjb3VudHNDaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKCFjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgY2hhaW5DaGFuZ2VkID0gdGhpcy5vbkNoYWluQ2hhbmdlZC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgICAgICBwcm92aWRlci5vbignY2hhaW5DaGFuZ2VkJywgY2hhaW5DaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKCFkaXNjb25uZWN0KSB7XG4gICAgICAgICAgICAgICAgICAgIGRpc2Nvbm5lY3QgPSB0aGlzLm9uRGlzY29ubmVjdC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgICAgICBwcm92aWRlci5vbignZGlzY29ubmVjdCcsIGRpc2Nvbm5lY3QpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBTd2l0Y2ggdG8gY2hhaW4gaWYgcHJvdmlkZWRcbiAgICAgICAgICAgICAgICBsZXQgY3VycmVudENoYWluSWQgPSBhd2FpdCB0aGlzLmdldENoYWluSWQoKTtcbiAgICAgICAgICAgICAgICBpZiAoY2hhaW5JZCAmJiBjdXJyZW50Q2hhaW5JZCAhPT0gY2hhaW5JZCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjaGFpbiA9IGF3YWl0IHRoaXMuc3dpdGNoQ2hhaW4oeyBjaGFpbklkIH0pLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgaWQ6IGN1cnJlbnRDaGFpbklkIH07XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q2hhaW5JZCA9IGNoYWluPy5pZCA/PyBjdXJyZW50Q2hhaW5JZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIHsgYWNjb3VudHMsIGNoYWluSWQ6IGN1cnJlbnRDaGFpbklkIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBpZiAoLyh1c2VyIGNsb3NlZCBtb2RhbHxhY2NvdW50cyByZWNlaXZlZCBpcyBlbXB0eXx1c2VyIGRlbmllZCBhY2NvdW50KS9pLnRlc3QoZXJyb3IubWVzc2FnZSkpXG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBkaXNjb25uZWN0KCkge1xuICAgICAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBhd2FpdCB0aGlzLmdldFByb3ZpZGVyKCk7XG4gICAgICAgICAgICBpZiAoYWNjb3VudHNDaGFuZ2VkKSB7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIucmVtb3ZlTGlzdGVuZXIoJ2FjY291bnRzQ2hhbmdlZCcsIGFjY291bnRzQ2hhbmdlZCk7XG4gICAgICAgICAgICAgICAgYWNjb3VudHNDaGFuZ2VkID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGNoYWluQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdjaGFpbkNoYW5nZWQnLCBjaGFpbkNoYW5nZWQpO1xuICAgICAgICAgICAgICAgIGNoYWluQ2hhbmdlZCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChkaXNjb25uZWN0KSB7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIucmVtb3ZlTGlzdGVuZXIoJ2Rpc2Nvbm5lY3QnLCBkaXNjb25uZWN0KTtcbiAgICAgICAgICAgICAgICBkaXNjb25uZWN0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcHJvdmlkZXIuZGlzY29ubmVjdCgpO1xuICAgICAgICAgICAgcHJvdmlkZXIuY2xvc2UoKTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0QWNjb3VudHMoKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIHJldHVybiAoYXdhaXQgcHJvdmlkZXIucmVxdWVzdCh7XG4gICAgICAgICAgICAgICAgbWV0aG9kOiAnZXRoX2FjY291bnRzJyxcbiAgICAgICAgICAgIH0pKS5tYXAoKHgpID0+IGdldEFkZHJlc3MoeCkpO1xuICAgICAgICB9LFxuICAgICAgICBhc3luYyBnZXRDaGFpbklkKCkge1xuICAgICAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBhd2FpdCB0aGlzLmdldFByb3ZpZGVyKCk7XG4gICAgICAgICAgICBjb25zdCBjaGFpbklkID0gYXdhaXQgcHJvdmlkZXIucmVxdWVzdCh7XG4gICAgICAgICAgICAgICAgbWV0aG9kOiAnZXRoX2NoYWluSWQnLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gTnVtYmVyKGNoYWluSWQpO1xuICAgICAgICB9LFxuICAgICAgICBhc3luYyBnZXRQcm92aWRlcigpIHtcbiAgICAgICAgICAgIGlmICghd2FsbGV0UHJvdmlkZXIpIHtcbiAgICAgICAgICAgICAgICAvLyBVbndyYXBwaW5nIGltcG9ydCBmb3IgVml0ZSBjb21wYXRpYmlsaXR5LlxuICAgICAgICAgICAgICAgIC8vIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL3ZpdGVqcy92aXRlL2lzc3Vlcy85NzAzXG4gICAgICAgICAgICAgICAgY29uc3QgQ29pbmJhc2VXYWxsZXRTREsgPSBhd2FpdCAoYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGRlZmF1bHQ6IFNESyB9ID0gYXdhaXQgaW1wb3J0KCdjYnctc2RrJyk7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgU0RLICE9PSAnZnVuY3Rpb24nICYmIHR5cGVvZiBTREsuZGVmYXVsdCA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBTREsuZGVmYXVsdDtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFNESztcbiAgICAgICAgICAgICAgICB9KSgpO1xuICAgICAgICAgICAgICAgIHNkayA9IG5ldyBDb2luYmFzZVdhbGxldFNESyh7IC4uLnBhcmFtZXRlcnMsIHJlbG9hZE9uRGlzY29ubmVjdCB9KTtcbiAgICAgICAgICAgICAgICAvLyBGb3JjZSB0eXBlcyB0byByZXRyaWV2ZSBwcml2YXRlIGB3YWxsZXRFeHRlbnNpb25gIG1ldGhvZCBmcm9tIHRoZSBDb2luYmFzZSBXYWxsZXQgU0RLLlxuICAgICAgICAgICAgICAgIGNvbnN0IHdhbGxldEV4dGVuc2lvbkNoYWluSWQgPSBzZGsud2FsbGV0RXh0ZW5zaW9uPy5nZXRDaGFpbklkKCk7XG4gICAgICAgICAgICAgICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKGNoYWluKSA9PiBwYXJhbWV0ZXJzLmNoYWluSWRcbiAgICAgICAgICAgICAgICAgICAgPyBjaGFpbi5pZCA9PT0gcGFyYW1ldGVycy5jaGFpbklkXG4gICAgICAgICAgICAgICAgICAgIDogY2hhaW4uaWQgPT09IHdhbGxldEV4dGVuc2lvbkNoYWluSWQpIHx8IGNvbmZpZy5jaGFpbnNbMF07XG4gICAgICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IHBhcmFtZXRlcnMuY2hhaW5JZCB8fCBjaGFpbj8uaWQ7XG4gICAgICAgICAgICAgICAgY29uc3QganNvblJwY1VybCA9IHBhcmFtZXRlcnMuanNvblJwY1VybCB8fCBjaGFpbj8ucnBjVXJscy5kZWZhdWx0Lmh0dHBbMF07XG4gICAgICAgICAgICAgICAgd2FsbGV0UHJvdmlkZXIgPSBzZGsubWFrZVdlYjNQcm92aWRlcihqc29uUnBjVXJsLCBjaGFpbklkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB3YWxsZXRQcm92aWRlcjtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgaXNBdXRob3JpemVkKCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IHRoaXMuZ2V0QWNjb3VudHMoKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gISFhY2NvdW50cy5sZW5ndGg7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBzd2l0Y2hDaGFpbih7IGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIsIGNoYWluSWQgfSkge1xuICAgICAgICAgICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKGNoYWluKSA9PiBjaGFpbi5pZCA9PT0gY2hhaW5JZCk7XG4gICAgICAgICAgICBpZiAoIWNoYWluKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbkVycm9yKG5ldyBDaGFpbk5vdENvbmZpZ3VyZWRFcnJvcigpKTtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHtcbiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAnd2FsbGV0X3N3aXRjaEV0aGVyZXVtQ2hhaW4nLFxuICAgICAgICAgICAgICAgICAgICBwYXJhbXM6IFt7IGNoYWluSWQ6IG51bWJlclRvSGV4KGNoYWluLmlkKSB9XSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2hhaW47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAvLyBJbmRpY2F0ZXMgY2hhaW4gaXMgbm90IGFkZGVkIHRvIHByb3ZpZGVyXG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IDQ5MDIpIHtcbiAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBibG9ja0V4cGxvcmVyVXJscztcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyPy5ibG9ja0V4cGxvcmVyVXJscylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBibG9ja0V4cGxvcmVyVXJscyA9IGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIuYmxvY2tFeHBsb3JlclVybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmxvY2tFeHBsb3JlclVybHMgPSBjaGFpbi5ibG9ja0V4cGxvcmVycz8uZGVmYXVsdC51cmxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBbY2hhaW4uYmxvY2tFeHBsb3JlcnM/LmRlZmF1bHQudXJsXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IHJwY1VybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8ucnBjVXJscz8ubGVuZ3RoKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJwY1VybHMgPSBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLnJwY1VybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcnBjVXJscyA9IFtjaGFpbi5ycGNVcmxzLmRlZmF1bHQ/Lmh0dHBbMF0gPz8gJyddO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYWRkRXRoZXJldW1DaGFpbiA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBibG9ja0V4cGxvcmVyVXJscyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiBudW1iZXJUb0hleChjaGFpbklkKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbk5hbWU6IGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXI/LmNoYWluTmFtZSA/PyBjaGFpbi5uYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb25VcmxzOiBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyPy5pY29uVXJscyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYXRpdmVDdXJyZW5jeTogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8ubmF0aXZlQ3VycmVuY3kgPz9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hhaW4ubmF0aXZlQ3VycmVuY3ksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcnBjVXJscyxcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICd3YWxsZXRfYWRkRXRoZXJldW1DaGFpbicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zOiBbYWRkRXRoZXJldW1DaGFpbl0sXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjaGFpbjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbkVycm9yKGVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgb25BY2NvdW50c0NoYW5nZWQoYWNjb3VudHMpIHtcbiAgICAgICAgICAgIGlmIChhY2NvdW50cy5sZW5ndGggPT09IDApXG4gICAgICAgICAgICAgICAgdGhpcy5vbkRpc2Nvbm5lY3QoKTtcbiAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICBjb25maWcuZW1pdHRlci5lbWl0KCdjaGFuZ2UnLCB7XG4gICAgICAgICAgICAgICAgICAgIGFjY291bnRzOiBhY2NvdW50cy5tYXAoKHgpID0+IGdldEFkZHJlc3MoeCkpLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgICAgICBvbkNoYWluQ2hhbmdlZChjaGFpbikge1xuICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IE51bWJlcihjaGFpbik7XG4gICAgICAgICAgICBjb25maWcuZW1pdHRlci5lbWl0KCdjaGFuZ2UnLCB7IGNoYWluSWQgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIG9uRGlzY29ubmVjdChfZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2Rpc2Nvbm5lY3QnKTtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgaWYgKGFjY291bnRzQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgICAgIGFjY291bnRzQ2hhbmdlZCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY2hhaW5DaGFuZ2VkJywgY2hhaW5DaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZGlzY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9KSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb2luYmFzZVdhbGxldC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/connectors/dist/esm/coinbaseWallet.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/connectors/dist/esm/metaMask.js":
/*!**************************************************************!*\
  !*** ../node_modules/@wagmi/connectors/dist/esm/metaMask.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metaMask: () => (/* binding */ metaMask)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/encoding/fromHex.js\");\n\n\nmetaMask.type = 'metaMask';\nfunction metaMask(parameters = {}) {\n    let sdk;\n    let provider;\n    let providerPromise;\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'metaMaskSDK',\n        name: 'MetaMask',\n        rdns: ['io.metamask', 'io.metamask.mobile'],\n        type: metaMask.type,\n        async setup() {\n            const provider = await this.getProvider();\n            if (provider?.on) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!displayUri) {\n                displayUri = this.onDisplayUri;\n                provider.on('display_uri', displayUri);\n            }\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            try {\n                let signResponse;\n                let connectWithResponse;\n                if (!accounts?.length) {\n                    if (parameters.connectAndSign || parameters.connectWith) {\n                        if (parameters.connectAndSign)\n                            signResponse = await sdk.connectAndSign({\n                                msg: parameters.connectAndSign,\n                            });\n                        else if (parameters.connectWith)\n                            connectWithResponse = await sdk.connectWith({\n                                method: parameters.connectWith.method,\n                                params: parameters.connectWith.params,\n                            });\n                        accounts = await this.getAccounts();\n                    }\n                    else {\n                        const requestedAccounts = (await sdk.connect());\n                        accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n                    }\n                }\n                // Switch to chain if provided\n                let currentChainId = (await this.getChainId());\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (signResponse)\n                    provider.emit('connectAndSign', {\n                        accounts,\n                        chainId: currentChainId,\n                        signResponse,\n                    });\n                else if (connectWithResponse)\n                    provider.emit('connectWith', {\n                        accounts,\n                        chainId: currentChainId,\n                        connectWithResponse,\n                    });\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            await sdk.terminate();\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            const accounts = (await provider.request({\n                method: 'eth_accounts',\n            }));\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            const chainId = provider.getChainId() ||\n                (await provider?.request({ method: 'eth_chainId' }));\n            return Number(chainId);\n        },\n        async getProvider() {\n            async function initProvider() {\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const MetaMaskSDK = await (async () => {\n                    const { default: SDK } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/node-gyp-build\"), __webpack_require__.e(\"vendor-chunks/utf-8-validate\"), __webpack_require__.e(\"vendor-chunks/bufferutil\"), __webpack_require__.e(\"vendor-chunks/@metamask\"), __webpack_require__.e(\"vendor-chunks/tr46\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/node-fetch\"), __webpack_require__.e(\"vendor-chunks/whatwg-url\"), __webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/webidl-conversions\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/supports-color\"), __webpack_require__.e(\"vendor-chunks/has-flag\"), __webpack_require__.e(\"vendor-chunks/engine.io-client\"), __webpack_require__.e(\"vendor-chunks/socket.io-client\"), __webpack_require__.e(\"vendor-chunks/socket.io-parser\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/engine.io-parser\"), __webpack_require__.e(\"vendor-chunks/@socket.io\"), __webpack_require__.e(\"vendor-chunks/xmlhttprequest-ssl\"), __webpack_require__.e(\"vendor-chunks/eventemitter2\"), __webpack_require__.e(\"vendor-chunks/cross-fetch\")]).then(__webpack_require__.bind(__webpack_require__, /*! @metamask/sdk */ \"(ssr)/../node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js\"));\n                    if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n                        return SDK.default;\n                    return SDK;\n                })();\n                const readonlyRPCMap = {};\n                for (const chain of config.chains)\n                    readonlyRPCMap[(0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chain.id)] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                        chain,\n                        transports: config.transports,\n                    })?.[0];\n                sdk = new MetaMaskSDK({\n                    _source: 'wagmi',\n                    forceDeleteProvider: false,\n                    forceInjectProvider: false,\n                    injectProvider: false,\n                    // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`\n                    ...parameters,\n                    readonlyRPCMap,\n                    dappMetadata: {\n                        ...parameters.dappMetadata,\n                        // Test if name and url are set AND not empty\n                        name: parameters.dappMetadata?.name\n                            ? parameters.dappMetadata?.name\n                            : 'wagmi',\n                        url: parameters.dappMetadata?.url\n                            ? parameters.dappMetadata?.url\n                            : typeof window !== 'undefined'\n                                ? window.location.origin\n                                : 'https://wagmi.sh',\n                    },\n                    useDeeplink: parameters.useDeeplink ?? true,\n                });\n                const result = await sdk.init();\n                // On initial load, sometimes `sdk.getProvider` does not return provider.\n                // https://github.com/wevm/wagmi/issues/4367\n                // Use result of `init` call if available.\n                const provider = (() => {\n                    if (result?.activeProvider)\n                        return result.activeProvider;\n                    return sdk.getProvider();\n                })();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ProviderNotFoundError();\n                return provider;\n            }\n            if (!provider) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider = await providerPromise;\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                // MetaMask mobile provider sometimes fails to immediately resolve\n                // JSON-RPC requests on page load\n                const timeout = 200;\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(() => (0,viem__WEBPACK_IMPORTED_MODULE_7__.withTimeout)(() => this.getAccounts(), { timeout }), {\n                    delay: timeout + 1,\n                    retryCount: 3,\n                });\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_8__.ChainNotConfiguredError());\n            try {\n                await provider.request({\n                    method: 'wallet_switchEthereumChain',\n                    params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId) }],\n                });\n                // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                // this callback or an externally emitted `'chainChanged'` event.\n                // https://github.com/MetaMask/metamask-extension/issues/24247\n                await waitForChainIdToSync();\n                await sendAndWaitForChangeEvent(chainId);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        await provider.request({\n                            method: 'wallet_addEthereumChain',\n                            params: [\n                                {\n                                    blockExplorerUrls: (() => {\n                                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                                        if (addEthereumChainParameter?.blockExplorerUrls)\n                                            return addEthereumChainParameter.blockExplorerUrls;\n                                        if (blockExplorer)\n                                            return [\n                                                blockExplorer.url,\n                                                ...Object.values(blockExplorers).map((x) => x.url),\n                                            ];\n                                        return;\n                                    })(),\n                                    chainId: (0,viem__WEBPACK_IMPORTED_MODULE_3__.numberToHex)(chainId),\n                                    chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                                    iconUrls: addEthereumChainParameter?.iconUrls,\n                                    nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                        chain.nativeCurrency,\n                                    rpcUrls: (() => {\n                                        if (addEthereumChainParameter?.rpcUrls?.length)\n                                            return addEthereumChainParameter.rpcUrls;\n                                        return [chain.rpcUrls.default?.http[0] ?? ''];\n                                    })(),\n                                },\n                            ],\n                        });\n                        await waitForChainIdToSync();\n                        await sendAndWaitForChangeEvent(chainId);\n                        return chain;\n                    }\n                    catch (err) {\n                        const error = err;\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError.code)\n                            throw new viem__WEBPACK_IMPORTED_MODULE_2__.UserRejectedRequestError(error);\n                        throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n                    }\n                }\n                throw new viem__WEBPACK_IMPORTED_MODULE_2__.SwitchChainError(error);\n            }\n            async function waitForChainIdToSync() {\n                // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.\n                // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.\n                await (0,viem__WEBPACK_IMPORTED_MODULE_6__.withRetry)(async () => {\n                    const value = (0,viem__WEBPACK_IMPORTED_MODULE_9__.hexToNumber)(\n                    // `'eth_chainId'` is cached by the MetaMask SDK side to avoid unnecessary deeplinks\n                    (await provider.request({ method: 'eth_chainId' })));\n                    // `value` doesn't match expected `chainId`, throw to trigger retry\n                    if (value !== chainId)\n                        throw new Error('User rejected switch after adding network.');\n                    return value;\n                }, {\n                    delay: 50,\n                    retryCount: 20, // android device encryption is slower\n                });\n            }\n            async function sendAndWaitForChangeEvent(chainId) {\n                await new Promise((resolve) => {\n                    const listener = ((data) => {\n                        if ('chainId' in data && data.chainId === chainId) {\n                            config.emitter.off('change', listener);\n                            resolve();\n                        }\n                    });\n                    config.emitter.on('change', listener);\n                    config.emitter.emit('change', { chainId });\n                });\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0) {\n                // ... and using browser extension\n                if (sdk.isExtensionActive())\n                    this.onDisconnect();\n                // FIXME(upstream): Mobile app sometimes emits invalid `accountsChanged` event with empty accounts array\n                else\n                    return;\n            }\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_1__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            const provider = await this.getProvider();\n            if (connect) {\n                provider.removeListener('connect', connect);\n                connect = undefined;\n            }\n            if (!accountsChanged) {\n                accountsChanged = this.onAccountsChanged.bind(this);\n                provider.on('accountsChanged', accountsChanged);\n            }\n            if (!chainChanged) {\n                chainChanged = this.onChainChanged.bind(this);\n                provider.on('chainChanged', chainChanged);\n            }\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n    }));\n}\n//# sourceMappingURL=metaMask.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb25uZWN0b3JzL2Rpc3QvZXNtL21ldGFNYXNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStHO0FBQytDO0FBQzlKO0FBQ08saUNBQWlDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLDREQUFlO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCx3QkFBd0IsMEJBQTBCLElBQUk7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRUFBZ0UsZ0RBQVU7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRCxTQUFTO0FBQ3BFLDJDQUEyQywwREFBd0I7QUFDbkU7QUFDQSxpQ0FBaUM7QUFDakMscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLDBEQUF3QjtBQUMzRCw4QkFBOEIsMERBQXdCO0FBQ3RELG1DQUFtQyw2REFBMkI7QUFDOUQsOEJBQThCLDZEQUEyQjtBQUN6RDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLHVDQUF1QyxnREFBVTtBQUNqRCxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLHVCQUF1QjtBQUNsRTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGVBQWUsUUFBUSxreUNBQXVCO0FBQzFFO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EsbUNBQW1DLGlEQUFXLGNBQWMsMkRBQWM7QUFDMUU7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsOEJBQThCLDhEQUFxQjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsK0NBQVMsT0FBTyxpREFBVyw2QkFBNkIsU0FBUztBQUN4RztBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsNEJBQTRCLG9DQUFvQztBQUNoRTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsa0RBQWdCLEtBQUssZ0VBQXVCO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixTQUFTLGlEQUFXLFdBQVc7QUFDOUQsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsMERBQXdCO0FBQzNELDhCQUE4QiwwREFBd0I7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELDRDQUE0QztBQUM1RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDLDZDQUE2QyxpREFBVztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDLGlDQUFpQztBQUNqQztBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsMERBQXdCO0FBQ25FLHNDQUFzQywwREFBd0I7QUFDOUQsa0NBQWtDLGtEQUFnQjtBQUNsRDtBQUNBO0FBQ0EsMEJBQTBCLGtEQUFnQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwrQ0FBUztBQUMvQixrQ0FBa0MsaURBQVc7QUFDN0M7QUFDQSw4Q0FBOEMsdUJBQXVCO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLG9EQUFvRCxTQUFTO0FBQzdELGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxTQUFTO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELGdEQUFVO0FBQzVELGlCQUFpQjtBQUNqQixTQUFTO0FBQ1Q7QUFDQTtBQUNBLDRDQUE0QyxTQUFTO0FBQ3JELFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLG1CQUFtQjtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsNkNBQTZDLGdDQUFnQztBQUM3RSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2Nvbm5lY3RvcnMvZGlzdC9lc20vbWV0YU1hc2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IsIFByb3ZpZGVyTm90Rm91bmRFcnJvciwgY3JlYXRlQ29ubmVjdG9yLCBleHRyYWN0UnBjVXJscywgfSBmcm9tICdAd2FnbWkvY29yZSc7XG5pbXBvcnQgeyBSZXNvdXJjZVVuYXZhaWxhYmxlUnBjRXJyb3IsIFN3aXRjaENoYWluRXJyb3IsIFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvciwgZ2V0QWRkcmVzcywgaGV4VG9OdW1iZXIsIG51bWJlclRvSGV4LCB3aXRoUmV0cnksIHdpdGhUaW1lb3V0LCB9IGZyb20gJ3ZpZW0nO1xubWV0YU1hc2sudHlwZSA9ICdtZXRhTWFzayc7XG5leHBvcnQgZnVuY3Rpb24gbWV0YU1hc2socGFyYW1ldGVycyA9IHt9KSB7XG4gICAgbGV0IHNkaztcbiAgICBsZXQgcHJvdmlkZXI7XG4gICAgbGV0IHByb3ZpZGVyUHJvbWlzZTtcbiAgICBsZXQgYWNjb3VudHNDaGFuZ2VkO1xuICAgIGxldCBjaGFpbkNoYW5nZWQ7XG4gICAgbGV0IGNvbm5lY3Q7XG4gICAgbGV0IGRpc3BsYXlVcmk7XG4gICAgbGV0IGRpc2Nvbm5lY3Q7XG4gICAgcmV0dXJuIGNyZWF0ZUNvbm5lY3RvcigoY29uZmlnKSA9PiAoe1xuICAgICAgICBpZDogJ21ldGFNYXNrU0RLJyxcbiAgICAgICAgbmFtZTogJ01ldGFNYXNrJyxcbiAgICAgICAgcmRuczogWydpby5tZXRhbWFzaycsICdpby5tZXRhbWFzay5tb2JpbGUnXSxcbiAgICAgICAgdHlwZTogbWV0YU1hc2sudHlwZSxcbiAgICAgICAgYXN5bmMgc2V0dXAoKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGlmIChwcm92aWRlcj8ub24pIHtcbiAgICAgICAgICAgICAgICBpZiAoIWNvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICAgICAgY29ubmVjdCA9IHRoaXMub25Db25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFdlIHNob3VsZG4ndCBuZWVkIHRvIGxpc3RlbiBmb3IgYCdhY2NvdW50c0NoYW5nZWQnYCBoZXJlIHNpbmNlIHRoZSBgJ2Nvbm5lY3QnYCBldmVudCBzaG91bGQgc3VmZmljZSAoYW5kIHdhbGxldCBzaG91bGRuJ3QgYmUgY29ubmVjdGVkIHlldCkuXG4gICAgICAgICAgICAgICAgLy8gU29tZSB3YWxsZXRzLCBsaWtlIE1ldGFNYXNrLCBkbyBub3QgaW1wbGVtZW50IHRoZSBgJ2Nvbm5lY3QnYCBldmVudCBhbmQgb3ZlcmxvYWQgYCdhY2NvdW50c0NoYW5nZWQnYCBpbnN0ZWFkLlxuICAgICAgICAgICAgICAgIGlmICghYWNjb3VudHNDaGFuZ2VkKSB7XG4gICAgICAgICAgICAgICAgICAgIGFjY291bnRzQ2hhbmdlZCA9IHRoaXMub25BY2NvdW50c0NoYW5nZWQuYmluZCh0aGlzKTtcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2FjY291bnRzQ2hhbmdlZCcsIGFjY291bnRzQ2hhbmdlZCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBjb25uZWN0KHsgY2hhaW5JZCwgaXNSZWNvbm5lY3RpbmcgfSA9IHt9KSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGlmICghZGlzcGxheVVyaSkge1xuICAgICAgICAgICAgICAgIGRpc3BsYXlVcmkgPSB0aGlzLm9uRGlzcGxheVVyaTtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5vbignZGlzcGxheV91cmknLCBkaXNwbGF5VXJpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBhY2NvdW50cyA9IFtdO1xuICAgICAgICAgICAgaWYgKGlzUmVjb25uZWN0aW5nKVxuICAgICAgICAgICAgICAgIGFjY291bnRzID0gYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpLmNhdGNoKCgpID0+IFtdKTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgbGV0IHNpZ25SZXNwb25zZTtcbiAgICAgICAgICAgICAgICBsZXQgY29ubmVjdFdpdGhSZXNwb25zZTtcbiAgICAgICAgICAgICAgICBpZiAoIWFjY291bnRzPy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmFtZXRlcnMuY29ubmVjdEFuZFNpZ24gfHwgcGFyYW1ldGVycy5jb25uZWN0V2l0aCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmFtZXRlcnMuY29ubmVjdEFuZFNpZ24pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2lnblJlc3BvbnNlID0gYXdhaXQgc2RrLmNvbm5lY3RBbmRTaWduKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbXNnOiBwYXJhbWV0ZXJzLmNvbm5lY3RBbmRTaWduLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAocGFyYW1ldGVycy5jb25uZWN0V2l0aClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25uZWN0V2l0aFJlc3BvbnNlID0gYXdhaXQgc2RrLmNvbm5lY3RXaXRoKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiBwYXJhbWV0ZXJzLmNvbm5lY3RXaXRoLm1ldGhvZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zOiBwYXJhbWV0ZXJzLmNvbm5lY3RXaXRoLnBhcmFtcyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY291bnRzID0gYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVxdWVzdGVkQWNjb3VudHMgPSAoYXdhaXQgc2RrLmNvbm5lY3QoKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NvdW50cyA9IHJlcXVlc3RlZEFjY291bnRzLm1hcCgoeCkgPT4gZ2V0QWRkcmVzcyh4KSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gU3dpdGNoIHRvIGNoYWluIGlmIHByb3ZpZGVkXG4gICAgICAgICAgICAgICAgbGV0IGN1cnJlbnRDaGFpbklkID0gKGF3YWl0IHRoaXMuZ2V0Q2hhaW5JZCgpKTtcbiAgICAgICAgICAgICAgICBpZiAoY2hhaW5JZCAmJiBjdXJyZW50Q2hhaW5JZCAhPT0gY2hhaW5JZCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjaGFpbiA9IGF3YWl0IHRoaXMuc3dpdGNoQ2hhaW4oeyBjaGFpbklkIH0pLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgaWQ6IGN1cnJlbnRDaGFpbklkIH07XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q2hhaW5JZCA9IGNoYWluPy5pZCA/PyBjdXJyZW50Q2hhaW5JZDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGRpc3BsYXlVcmkpIHtcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIucmVtb3ZlTGlzdGVuZXIoJ2Rpc3BsYXlfdXJpJywgZGlzcGxheVVyaSk7XG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXlVcmkgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChzaWduUmVzcG9uc2UpXG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLmVtaXQoJ2Nvbm5lY3RBbmRTaWduJywge1xuICAgICAgICAgICAgICAgICAgICAgICAgYWNjb3VudHMsXG4gICAgICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiBjdXJyZW50Q2hhaW5JZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpZ25SZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoY29ubmVjdFdpdGhSZXNwb25zZSlcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXIuZW1pdCgnY29ubmVjdFdpdGgnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NvdW50cyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluSWQ6IGN1cnJlbnRDaGFpbklkLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29ubmVjdFdpdGhSZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgLy8gTWFuYWdlIEVJUC0xMTkzIGV2ZW50IGxpc3RlbmVyc1xuICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vZWlwcy5ldGhlcmV1bS5vcmcvRUlQUy9laXAtMTE5MyNldmVudHNcbiAgICAgICAgICAgICAgICBpZiAoY29ubmVjdCkge1xuICAgICAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY29ubmVjdCcsIGNvbm5lY3QpO1xuICAgICAgICAgICAgICAgICAgICBjb25uZWN0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWFjY291bnRzQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgICAgICBhY2NvdW50c0NoYW5nZWQgPSB0aGlzLm9uQWNjb3VudHNDaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWNoYWluQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB0aGlzLm9uQ2hhaW5DaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjaGFpbkNoYW5nZWQnLCBjaGFpbkNoYW5nZWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWRpc2Nvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHRoaXMub25EaXNjb25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiB7IGFjY291bnRzLCBjaGFpbklkOiBjdXJyZW50Q2hhaW5JZCB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZXJyO1xuICAgICAgICAgICAgICAgIGlmIChlcnJvci5jb2RlID09PSBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IuY29kZSlcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvcihlcnJvcik7XG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFJlc291cmNlVW5hdmFpbGFibGVScGNFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgUmVzb3VyY2VVbmF2YWlsYWJsZVJwY0Vycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZGlzY29ubmVjdCgpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgLy8gTWFuYWdlIEVJUC0xMTkzIGV2ZW50IGxpc3RlbmVyc1xuICAgICAgICAgICAgaWYgKGNoYWluQ2hhbmdlZCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdjaGFpbkNoYW5nZWQnLCBjaGFpbkNoYW5nZWQpO1xuICAgICAgICAgICAgICAgIGNoYWluQ2hhbmdlZCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChkaXNjb25uZWN0KSB7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIucmVtb3ZlTGlzdGVuZXIoJ2Rpc2Nvbm5lY3QnLCBkaXNjb25uZWN0KTtcbiAgICAgICAgICAgICAgICBkaXNjb25uZWN0ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjb25uZWN0KSB7XG4gICAgICAgICAgICAgICAgY29ubmVjdCA9IHRoaXMub25Db25uZWN0LmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2Nvbm5lY3QnLCBjb25uZWN0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGF3YWl0IHNkay50ZXJtaW5hdGUoKTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0QWNjb3VudHMoKSB7XG4gICAgICAgICAgICBjb25zdCBwcm92aWRlciA9IGF3YWl0IHRoaXMuZ2V0UHJvdmlkZXIoKTtcbiAgICAgICAgICAgIGNvbnN0IGFjY291bnRzID0gKGF3YWl0IHByb3ZpZGVyLnJlcXVlc3Qoe1xuICAgICAgICAgICAgICAgIG1ldGhvZDogJ2V0aF9hY2NvdW50cycsXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICByZXR1cm4gYWNjb3VudHMubWFwKCh4KSA9PiBnZXRBZGRyZXNzKHgpKTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgZ2V0Q2hhaW5JZCgpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IHByb3ZpZGVyLmdldENoYWluSWQoKSB8fFxuICAgICAgICAgICAgICAgIChhd2FpdCBwcm92aWRlcj8ucmVxdWVzdCh7IG1ldGhvZDogJ2V0aF9jaGFpbklkJyB9KSk7XG4gICAgICAgICAgICByZXR1cm4gTnVtYmVyKGNoYWluSWQpO1xuICAgICAgICB9LFxuICAgICAgICBhc3luYyBnZXRQcm92aWRlcigpIHtcbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIGluaXRQcm92aWRlcigpIHtcbiAgICAgICAgICAgICAgICAvLyBVbndyYXBwaW5nIGltcG9ydCBmb3IgVml0ZSBjb21wYXRpYmlsaXR5LlxuICAgICAgICAgICAgICAgIC8vIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL3ZpdGVqcy92aXRlL2lzc3Vlcy85NzAzXG4gICAgICAgICAgICAgICAgY29uc3QgTWV0YU1hc2tTREsgPSBhd2FpdCAoYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGRlZmF1bHQ6IFNESyB9ID0gYXdhaXQgaW1wb3J0KCdAbWV0YW1hc2svc2RrJyk7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgU0RLICE9PSAnZnVuY3Rpb24nICYmIHR5cGVvZiBTREsuZGVmYXVsdCA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBTREsuZGVmYXVsdDtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFNESztcbiAgICAgICAgICAgICAgICB9KSgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlYWRvbmx5UlBDTWFwID0ge307XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBjaGFpbiBvZiBjb25maWcuY2hhaW5zKVxuICAgICAgICAgICAgICAgICAgICByZWFkb25seVJQQ01hcFtudW1iZXJUb0hleChjaGFpbi5pZCldID0gZXh0cmFjdFJwY1VybHMoe1xuICAgICAgICAgICAgICAgICAgICAgICAgY2hhaW4sXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc3BvcnRzOiBjb25maWcudHJhbnNwb3J0cyxcbiAgICAgICAgICAgICAgICAgICAgfSk/LlswXTtcbiAgICAgICAgICAgICAgICBzZGsgPSBuZXcgTWV0YU1hc2tTREsoe1xuICAgICAgICAgICAgICAgICAgICBfc291cmNlOiAnd2FnbWknLFxuICAgICAgICAgICAgICAgICAgICBmb3JjZURlbGV0ZVByb3ZpZGVyOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgZm9yY2VJbmplY3RQcm92aWRlcjogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGluamVjdFByb3ZpZGVyOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgLy8gV29ya2Fyb3VuZCBjYXN0IHNpbmNlIE1ldGFNYXNrIFNESyBkb2VzIG5vdCBzdXBwb3J0IGAnZXhhY3RPcHRpb25hbFByb3BlcnR5VHlwZXMnYFxuICAgICAgICAgICAgICAgICAgICAuLi5wYXJhbWV0ZXJzLFxuICAgICAgICAgICAgICAgICAgICByZWFkb25seVJQQ01hcCxcbiAgICAgICAgICAgICAgICAgICAgZGFwcE1ldGFkYXRhOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YSxcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRlc3QgaWYgbmFtZSBhbmQgdXJsIGFyZSBzZXQgQU5EIG5vdCBlbXB0eVxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogcGFyYW1ldGVycy5kYXBwTWV0YWRhdGE/Lm5hbWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHBhcmFtZXRlcnMuZGFwcE1ldGFkYXRhPy5uYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnd2FnbWknLFxuICAgICAgICAgICAgICAgICAgICAgICAgdXJsOiBwYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YT8udXJsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBwYXJhbWV0ZXJzLmRhcHBNZXRhZGF0YT8udXJsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnaHR0cHM6Ly93YWdtaS5zaCcsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHVzZURlZXBsaW5rOiBwYXJhbWV0ZXJzLnVzZURlZXBsaW5rID8/IHRydWUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2RrLmluaXQoKTtcbiAgICAgICAgICAgICAgICAvLyBPbiBpbml0aWFsIGxvYWQsIHNvbWV0aW1lcyBgc2RrLmdldFByb3ZpZGVyYCBkb2VzIG5vdCByZXR1cm4gcHJvdmlkZXIuXG4gICAgICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3dldm0vd2FnbWkvaXNzdWVzLzQzNjdcbiAgICAgICAgICAgICAgICAvLyBVc2UgcmVzdWx0IG9mIGBpbml0YCBjYWxsIGlmIGF2YWlsYWJsZS5cbiAgICAgICAgICAgICAgICBjb25zdCBwcm92aWRlciA9ICgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChyZXN1bHQ/LmFjdGl2ZVByb3ZpZGVyKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdC5hY3RpdmVQcm92aWRlcjtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNkay5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgICAgIH0pKCk7XG4gICAgICAgICAgICAgICAgaWYgKCFwcm92aWRlcilcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFByb3ZpZGVyTm90Rm91bmRFcnJvcigpO1xuICAgICAgICAgICAgICAgIHJldHVybiBwcm92aWRlcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghcHJvdmlkZXIpIHtcbiAgICAgICAgICAgICAgICBpZiAoIXByb3ZpZGVyUHJvbWlzZSlcbiAgICAgICAgICAgICAgICAgICAgcHJvdmlkZXJQcm9taXNlID0gaW5pdFByb3ZpZGVyKCk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIgPSBhd2FpdCBwcm92aWRlclByb21pc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcHJvdmlkZXI7XG4gICAgICAgIH0sXG4gICAgICAgIGFzeW5jIGlzQXV0aG9yaXplZCgpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgLy8gTWV0YU1hc2sgbW9iaWxlIHByb3ZpZGVyIHNvbWV0aW1lcyBmYWlscyB0byBpbW1lZGlhdGVseSByZXNvbHZlXG4gICAgICAgICAgICAgICAgLy8gSlNPTi1SUEMgcmVxdWVzdHMgb24gcGFnZSBsb2FkXG4gICAgICAgICAgICAgICAgY29uc3QgdGltZW91dCA9IDIwMDtcbiAgICAgICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IHdpdGhSZXRyeSgoKSA9PiB3aXRoVGltZW91dCgoKSA9PiB0aGlzLmdldEFjY291bnRzKCksIHsgdGltZW91dCB9KSwge1xuICAgICAgICAgICAgICAgICAgICBkZWxheTogdGltZW91dCArIDEsXG4gICAgICAgICAgICAgICAgICAgIHJldHJ5Q291bnQ6IDMsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuICEhYWNjb3VudHMubGVuZ3RoO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2gge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgc3dpdGNoQ2hhaW4oeyBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLCBjaGFpbklkIH0pIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKHgpID0+IHguaWQgPT09IGNoYWluSWQpO1xuICAgICAgICAgICAgaWYgKCFjaGFpbilcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgU3dpdGNoQ2hhaW5FcnJvcihuZXcgQ2hhaW5Ob3RDb25maWd1cmVkRXJyb3IoKSk7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGF3YWl0IHByb3ZpZGVyLnJlcXVlc3Qoe1xuICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICd3YWxsZXRfc3dpdGNoRXRoZXJldW1DaGFpbicsXG4gICAgICAgICAgICAgICAgICAgIHBhcmFtczogW3sgY2hhaW5JZDogbnVtYmVyVG9IZXgoY2hhaW5JZCkgfV0sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgLy8gRHVyaW5nIGAnd2FsbGV0X3N3aXRjaEV0aGVyZXVtQ2hhaW4nYCwgTWV0YU1hc2sgbWFrZXMgYSBgJ25ldF92ZXJzaW9uJ2AgUlBDIGNhbGwgdG8gdGhlIHRhcmdldCBjaGFpbi5cbiAgICAgICAgICAgICAgICAvLyBJZiB0aGlzIHJlcXVlc3QgZmFpbHMsIE1ldGFNYXNrIGRvZXMgbm90IGVtaXQgdGhlIGAnY2hhaW5DaGFuZ2VkJ2AgZXZlbnQsIGJ1dCB3aWxsIHN0aWxsIHN3aXRjaCB0aGUgY2hhaW4uXG4gICAgICAgICAgICAgICAgLy8gVG8gY291bnRlciB0aGlzIGJlaGF2aW9yLCB3ZSByZXF1ZXN0IGFuZCBlbWl0IHRoZSBjdXJyZW50IGNoYWluIElEIHRvIGNvbmZpcm0gdGhlIGNoYWluIHN3aXRjaCBlaXRoZXIgdmlhXG4gICAgICAgICAgICAgICAgLy8gdGhpcyBjYWxsYmFjayBvciBhbiBleHRlcm5hbGx5IGVtaXR0ZWQgYCdjaGFpbkNoYW5nZWQnYCBldmVudC5cbiAgICAgICAgICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vTWV0YU1hc2svbWV0YW1hc2stZXh0ZW5zaW9uL2lzc3Vlcy8yNDI0N1xuICAgICAgICAgICAgICAgIGF3YWl0IHdhaXRGb3JDaGFpbklkVG9TeW5jKCk7XG4gICAgICAgICAgICAgICAgYXdhaXQgc2VuZEFuZFdhaXRGb3JDaGFuZ2VFdmVudChjaGFpbklkKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2hhaW47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3IgPSBlcnI7XG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgVXNlclJlamVjdGVkUmVxdWVzdEVycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICAvLyBJbmRpY2F0ZXMgY2hhaW4gaXMgbm90IGFkZGVkIHRvIHByb3ZpZGVyXG4gICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IDQ5MDIgfHxcbiAgICAgICAgICAgICAgICAgICAgLy8gVW53cmFwcGluZyBmb3IgTWV0YU1hc2sgTW9iaWxlXG4gICAgICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9NZXRhTWFzay9tZXRhbWFzay1tb2JpbGUvaXNzdWVzLzI5NDQjaXNzdWVjb21tZW50LTk3Njk4ODcxOVxuICAgICAgICAgICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgPy5kYXRhPy5vcmlnaW5hbEVycm9yPy5jb2RlID09PSA0OTAyKSB7XG4gICAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICd3YWxsZXRfYWRkRXRoZXJldW1DaGFpbicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyYW1zOiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJsb2NrRXhwbG9yZXJVcmxzOiAoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgZGVmYXVsdDogYmxvY2tFeHBsb3JlciwgLi4uYmxvY2tFeHBsb3JlcnMgfSA9IGNoYWluLmJsb2NrRXhwbG9yZXJzID8/IHt9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyPy5ibG9ja0V4cGxvcmVyVXJscylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIuYmxvY2tFeHBsb3JlclVybHM7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGJsb2NrRXhwbG9yZXIpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBibG9ja0V4cGxvcmVyLnVybCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLk9iamVjdC52YWx1ZXMoYmxvY2tFeHBsb3JlcnMpLm1hcCgoeCkgPT4geC51cmwpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKCksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbklkOiBudW1iZXJUb0hleChjaGFpbklkKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoYWluTmFtZTogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8uY2hhaW5OYW1lID8/IGNoYWluLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uVXJsczogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8uaWNvblVybHMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYXRpdmVDdXJyZW5jeTogYWRkRXRoZXJldW1DaGFpblBhcmFtZXRlcj8ubmF0aXZlQ3VycmVuY3kgPz9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGFpbi5uYXRpdmVDdXJyZW5jeSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJwY1VybHM6ICgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXI/LnJwY1VybHM/Lmxlbmd0aClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFkZEV0aGVyZXVtQ2hhaW5QYXJhbWV0ZXIucnBjVXJscztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gW2NoYWluLnJwY1VybHMuZGVmYXVsdD8uaHR0cFswXSA/PyAnJ107XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IHdhaXRGb3JDaGFpbklkVG9TeW5jKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBzZW5kQW5kV2FpdEZvckNoYW5nZUV2ZW50KGNoYWluSWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNoYWluO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZXJyO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVycm9yLmNvZGUgPT09IFVzZXJSZWplY3RlZFJlcXVlc3RFcnJvci5jb2RlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBVc2VyUmVqZWN0ZWRSZXF1ZXN0RXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFN3aXRjaENoYWluRXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbkVycm9yKGVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIHdhaXRGb3JDaGFpbklkVG9TeW5jKCkge1xuICAgICAgICAgICAgICAgIC8vIE9uIG1vYmlsZSwgdGhlcmUgaXMgYSByYWNlIGNvbmRpdGlvbiBiZXR3ZWVuIHRoZSByZXN1bHQgb2YgYCd3YWxsZXRfYWRkRXRoZXJldW1DaGFpbidgIGFuZCBgJ2V0aF9jaGFpbklkJ2AuXG4gICAgICAgICAgICAgICAgLy8gVG8gYXZvaWQgdGhpcywgd2Ugd2FpdCBmb3IgYCdldGhfY2hhaW5JZCdgIHRvIHJldHVybiB0aGUgZXhwZWN0ZWQgY2hhaW4gSUQgd2l0aCBhIHJldHJ5IGxvb3AuXG4gICAgICAgICAgICAgICAgYXdhaXQgd2l0aFJldHJ5KGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBoZXhUb051bWJlcihcbiAgICAgICAgICAgICAgICAgICAgLy8gYCdldGhfY2hhaW5JZCdgIGlzIGNhY2hlZCBieSB0aGUgTWV0YU1hc2sgU0RLIHNpZGUgdG8gYXZvaWQgdW5uZWNlc3NhcnkgZGVlcGxpbmtzXG4gICAgICAgICAgICAgICAgICAgIChhd2FpdCBwcm92aWRlci5yZXF1ZXN0KHsgbWV0aG9kOiAnZXRoX2NoYWluSWQnIH0pKSk7XG4gICAgICAgICAgICAgICAgICAgIC8vIGB2YWx1ZWAgZG9lc24ndCBtYXRjaCBleHBlY3RlZCBgY2hhaW5JZGAsIHRocm93IHRvIHRyaWdnZXIgcmV0cnlcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZhbHVlICE9PSBjaGFpbklkKVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIHJlamVjdGVkIHN3aXRjaCBhZnRlciBhZGRpbmcgbmV0d29yay4nKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgICAgICAgICAgICAgIH0sIHtcbiAgICAgICAgICAgICAgICAgICAgZGVsYXk6IDUwLFxuICAgICAgICAgICAgICAgICAgICByZXRyeUNvdW50OiAyMCwgLy8gYW5kcm9pZCBkZXZpY2UgZW5jcnlwdGlvbiBpcyBzbG93ZXJcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFzeW5jIGZ1bmN0aW9uIHNlbmRBbmRXYWl0Rm9yQ2hhbmdlRXZlbnQoY2hhaW5JZCkge1xuICAgICAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxpc3RlbmVyID0gKChkYXRhKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoJ2NoYWluSWQnIGluIGRhdGEgJiYgZGF0YS5jaGFpbklkID09PSBjaGFpbklkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIub2ZmKCdjaGFuZ2UnLCBsaXN0ZW5lcik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIub24oJ2NoYW5nZScsIGxpc3RlbmVyKTtcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLmVtaXR0ZXIuZW1pdCgnY2hhbmdlJywgeyBjaGFpbklkIH0pO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBvbkFjY291bnRzQ2hhbmdlZChhY2NvdW50cykge1xuICAgICAgICAgICAgLy8gRGlzY29ubmVjdCBpZiB0aGVyZSBhcmUgbm8gYWNjb3VudHNcbiAgICAgICAgICAgIGlmIChhY2NvdW50cy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgICAgICAvLyAuLi4gYW5kIHVzaW5nIGJyb3dzZXIgZXh0ZW5zaW9uXG4gICAgICAgICAgICAgICAgaWYgKHNkay5pc0V4dGVuc2lvbkFjdGl2ZSgpKVxuICAgICAgICAgICAgICAgICAgICB0aGlzLm9uRGlzY29ubmVjdCgpO1xuICAgICAgICAgICAgICAgIC8vIEZJWE1FKHVwc3RyZWFtKTogTW9iaWxlIGFwcCBzb21ldGltZXMgZW1pdHMgaW52YWxpZCBgYWNjb3VudHNDaGFuZ2VkYCBldmVudCB3aXRoIGVtcHR5IGFjY291bnRzIGFycmF5XG4gICAgICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBDb25uZWN0IGlmIGVtaXR0ZXIgaXMgbGlzdGVuaW5nIGZvciBjb25uZWN0IGV2ZW50IChlLmcuIGlzIGRpc2Nvbm5lY3RlZCBhbmQgY29ubmVjdHMgdGhyb3VnaCB3YWxsZXQgaW50ZXJmYWNlKVxuICAgICAgICAgICAgZWxzZSBpZiAoY29uZmlnLmVtaXR0ZXIubGlzdGVuZXJDb3VudCgnY29ubmVjdCcpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IChhd2FpdCB0aGlzLmdldENoYWluSWQoKSkudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICB0aGlzLm9uQ29ubmVjdCh7IGNoYWluSWQgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBSZWd1bGFyIGNoYW5nZSBldmVudFxuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2NoYW5nZScsIHtcbiAgICAgICAgICAgICAgICAgICAgYWNjb3VudHM6IGFjY291bnRzLm1hcCgoeCkgPT4gZ2V0QWRkcmVzcyh4KSksXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uQ2hhaW5DaGFuZ2VkKGNoYWluKSB7XG4gICAgICAgICAgICBjb25zdCBjaGFpbklkID0gTnVtYmVyKGNoYWluKTtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2NoYW5nZScsIHsgY2hhaW5JZCB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgYXN5bmMgb25Db25uZWN0KGNvbm5lY3RJbmZvKSB7XG4gICAgICAgICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IHRoaXMuZ2V0QWNjb3VudHMoKTtcbiAgICAgICAgICAgIGlmIChhY2NvdW50cy5sZW5ndGggPT09IDApXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgY29uc3QgY2hhaW5JZCA9IE51bWJlcihjb25uZWN0SW5mby5jaGFpbklkKTtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2Nvbm5lY3QnLCB7IGFjY291bnRzLCBjaGFpbklkIH0pO1xuICAgICAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBhd2FpdCB0aGlzLmdldFByb3ZpZGVyKCk7XG4gICAgICAgICAgICBpZiAoY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghYWNjb3VudHNDaGFuZ2VkKSB7XG4gICAgICAgICAgICAgICAgYWNjb3VudHNDaGFuZ2VkID0gdGhpcy5vbkFjY291bnRzQ2hhbmdlZC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdhY2NvdW50c0NoYW5nZWQnLCBhY2NvdW50c0NoYW5nZWQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB0aGlzLm9uQ2hhaW5DaGFuZ2VkLmJpbmQodGhpcyk7XG4gICAgICAgICAgICAgICAgcHJvdmlkZXIub24oJ2NoYWluQ2hhbmdlZCcsIGNoYWluQ2hhbmdlZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIWRpc2Nvbm5lY3QpIHtcbiAgICAgICAgICAgICAgICBkaXNjb25uZWN0ID0gdGhpcy5vbkRpc2Nvbm5lY3QuYmluZCh0aGlzKTtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5vbignZGlzY29ubmVjdCcsIGRpc2Nvbm5lY3QpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBhc3luYyBvbkRpc2Nvbm5lY3QoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgdGhpcy5nZXRQcm92aWRlcigpO1xuICAgICAgICAgICAgLy8gSWYgTWV0YU1hc2sgZW1pdHMgYSBgY29kZTogMTAxM2AgZXJyb3IsIHdhaXQgZm9yIHJlY29ubmVjdGlvbiBiZWZvcmUgZGlzY29ubmVjdGluZ1xuICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL01ldGFNYXNrL3Byb3ZpZGVycy9wdWxsLzEyMFxuICAgICAgICAgICAgaWYgKGVycm9yICYmIGVycm9yLmNvZGUgPT09IDEwMTMpIHtcbiAgICAgICAgICAgICAgICBpZiAocHJvdmlkZXIgJiYgISEoYXdhaXQgdGhpcy5nZXRBY2NvdW50cygpKS5sZW5ndGgpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ2Rpc2Nvbm5lY3QnKTtcbiAgICAgICAgICAgIC8vIE1hbmFnZSBFSVAtMTE5MyBldmVudCBsaXN0ZW5lcnNcbiAgICAgICAgICAgIGlmIChjaGFpbkNoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICBwcm92aWRlci5yZW1vdmVMaXN0ZW5lcignY2hhaW5DaGFuZ2VkJywgY2hhaW5DaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICBjaGFpbkNoYW5nZWQgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZGlzY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLnJlbW92ZUxpc3RlbmVyKCdkaXNjb25uZWN0JywgZGlzY29ubmVjdCk7XG4gICAgICAgICAgICAgICAgZGlzY29ubmVjdCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghY29ubmVjdCkge1xuICAgICAgICAgICAgICAgIGNvbm5lY3QgPSB0aGlzLm9uQ29ubmVjdC5iaW5kKHRoaXMpO1xuICAgICAgICAgICAgICAgIHByb3ZpZGVyLm9uKCdjb25uZWN0JywgY29ubmVjdCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIG9uRGlzcGxheVVyaSh1cmkpIHtcbiAgICAgICAgICAgIGNvbmZpZy5lbWl0dGVyLmVtaXQoJ21lc3NhZ2UnLCB7IHR5cGU6ICdkaXNwbGF5X3VyaScsIGRhdGE6IHVyaSB9KTtcbiAgICAgICAgfSxcbiAgICB9KSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tZXRhTWFzay5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/connectors/dist/esm/metaMask.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/connectors/dist/esm/safe.js":
/*!**********************************************************!*\
  !*** ../node_modules/@wagmi/connectors/dist/esm/safe.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: () => (/* binding */ safe)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/promise/withTimeout.js\");\n\n\nsafe.type = 'safe';\nfunction safe(parameters = {}) {\n    const { shimDisconnect = false } = parameters;\n    let provider_;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'safe',\n        name: 'Safe',\n        type: safe.type,\n        async connect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await this.getAccounts();\n            const chainId = await this.getChainId();\n            if (!disconnect) {\n                disconnect = this.onDisconnect.bind(this);\n                provider.on('disconnect', disconnect);\n            }\n            // Remove disconnected shim if it exists\n            if (shimDisconnect)\n                await config.storage?.removeItem('safe.disconnected');\n            return { accounts, chainId };\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect)\n                await config.storage?.setItem('safe.disconnected', true);\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return (await provider.request({ method: 'eth_accounts' })).map(viem__WEBPACK_IMPORTED_MODULE_2__.getAddress);\n        },\n        async getProvider() {\n            // Only allowed in iframe context\n            const isIframe = typeof window !== 'undefined' && window?.parent !== window;\n            if (!isIframe)\n                return;\n            if (!provider_) {\n                const { default: SDK } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@safe-global\").then(__webpack_require__.bind(__webpack_require__, /*! @safe-global/safe-apps-sdk */ \"(ssr)/../node_modules/@safe-global/safe-apps-sdk/dist/esm/index.js\"));\n                const sdk = new SDK(parameters);\n                // `getInfo` hangs when not used in Safe App iFrame\n                // https://github.com/safe-global/safe-apps-sdk/issues/263#issuecomment-**********\n                const safe = await (0,viem__WEBPACK_IMPORTED_MODULE_3__.withTimeout)(() => sdk.safe.getInfo(), {\n                    timeout: parameters.unstable_getInfoTimeout ?? 10,\n                });\n                if (!safe)\n                    throw new Error('Could not load Safe information');\n                // Unwrapping import for Vite compatibility.\n                // See: https://github.com/vitejs/vite/issues/9703\n                const SafeAppProvider = await (async () => {\n                    const Provider = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/viem\"), __webpack_require__.e(\"vendor-chunks/ox\"), __webpack_require__.e(\"vendor-chunks/abitype\"), __webpack_require__.e(\"vendor-chunks/node-gyp-build\"), __webpack_require__.e(\"vendor-chunks/utf-8-validate\"), __webpack_require__.e(\"vendor-chunks/bufferutil\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/@safe-global\"), __webpack_require__.e(\"vendor-chunks/isows\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! @safe-global/safe-apps-provider */ \"(ssr)/../node_modules/@safe-global/safe-apps-provider/dist/index.js\", 19));\n                    if (typeof Provider.SafeAppProvider !== 'function' &&\n                        typeof Provider.default.SafeAppProvider === 'function')\n                        return Provider.default.SafeAppProvider;\n                    return Provider.SafeAppProvider;\n                })();\n                provider_ = new SafeAppProvider(safe, sdk);\n            }\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            return Number(provider.chainId);\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem('safe.disconnected'));\n                if (isDisconnected)\n                    return false;\n                const accounts = await this.getAccounts();\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        onAccountsChanged() {\n            // Not relevant for Safe because changing account requires app reload.\n        },\n        onChainChanged() {\n            // Not relevant for Safe because Safe smart contract wallets only exist on single chain.\n        },\n        onDisconnect() {\n            config.emitter.emit('disconnect');\n        },\n    }));\n}\n//# sourceMappingURL=safe.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/connectors/dist/esm/safe.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/connectors/dist/esm/walletConnect.js":
/*!*******************************************************************!*\
  !*** ../node_modules/@wagmi/connectors/dist/esm/walletConnect.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walletConnect: () => (/* binding */ walletConnect)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/encoding/toHex.js\");\n\n\nwalletConnect.type = 'walletConnect';\nfunction walletConnect(parameters) {\n    const isNewChainsStale = parameters.isNewChainsStale ?? true;\n    let provider_;\n    let providerPromise;\n    const NAMESPACE = 'eip155';\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let displayUri;\n    let sessionDelete;\n    let disconnect;\n    return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        id: 'walletConnect',\n        name: 'WalletConnect',\n        type: walletConnect.type,\n        async setup() {\n            const provider = await this.getProvider().catch(() => null);\n            if (!provider)\n                return;\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            if (!sessionDelete) {\n                sessionDelete = this.onSessionDelete.bind(this);\n                provider.on('session_delete', sessionDelete);\n            }\n        },\n        async connect({ chainId, ...rest } = {}) {\n            try {\n                const provider = await this.getProvider();\n                if (!provider)\n                    throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                if (!displayUri) {\n                    displayUri = this.onDisplayUri;\n                    provider.on('display_uri', displayUri);\n                }\n                let targetChainId = chainId;\n                if (!targetChainId) {\n                    const state = (await config.storage?.getItem('state')) ?? {};\n                    const isChainSupported = config.chains.some((x) => x.id === state.chainId);\n                    if (isChainSupported)\n                        targetChainId = state.chainId;\n                    else\n                        targetChainId = config.chains[0]?.id;\n                }\n                if (!targetChainId)\n                    throw new Error('No chains found on connector.');\n                const isChainsStale = await this.isChainsStale();\n                // If there is an active session with stale chains, disconnect current session.\n                if (provider.session && isChainsStale)\n                    await provider.disconnect();\n                // If there isn't an active session or chains are stale, connect.\n                if (!provider.session || isChainsStale) {\n                    const optionalChains = config.chains\n                        .filter((chain) => chain.id !== targetChainId)\n                        .map((optionalChain) => optionalChain.id);\n                    await provider.connect({\n                        optionalChains: [targetChainId, ...optionalChains],\n                        ...('pairingTopic' in rest\n                            ? { pairingTopic: rest.pairingTopic }\n                            : {}),\n                    });\n                    this.setRequestedChainsIds(config.chains.map((x) => x.id));\n                }\n                // If session exists and chains are authorized, enable provider for required chain\n                const accounts = (await provider.enable()).map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                const currentChainId = await this.getChainId();\n                if (displayUri) {\n                    provider.removeListener('display_uri', displayUri);\n                    displayUri = undefined;\n                }\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                if (!sessionDelete) {\n                    sessionDelete = this.onSessionDelete.bind(this);\n                    provider.on('session_delete', sessionDelete);\n                }\n                return { accounts, chainId: currentChainId };\n            }\n            catch (error) {\n                if (/(user rejected|connection request reset)/i.test(error?.message)) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            try {\n                await provider?.disconnect();\n            }\n            catch (error) {\n                if (!/No matching key/i.test(error.message))\n                    throw error;\n            }\n            finally {\n                if (chainChanged) {\n                    provider?.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider?.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider?.on('connect', connect);\n                }\n                if (accountsChanged) {\n                    provider?.removeListener('accountsChanged', accountsChanged);\n                    accountsChanged = undefined;\n                }\n                if (sessionDelete) {\n                    provider?.removeListener('session_delete', sessionDelete);\n                    sessionDelete = undefined;\n                }\n                this.setRequestedChainsIds([]);\n            }\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            return provider.accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getProvider({ chainId } = {}) {\n            async function initProvider() {\n                const optionalChains = config.chains.map((x) => x.id);\n                if (!optionalChains.length)\n                    return;\n                const { EthereumProvider } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/node-gyp-build\"), __webpack_require__.e(\"vendor-chunks/utf-8-validate\"), __webpack_require__.e(\"vendor-chunks/bufferutil\"), __webpack_require__.e(\"vendor-chunks/@walletconnect\"), __webpack_require__.e(\"vendor-chunks/tr46\"), __webpack_require__.e(\"vendor-chunks/node-fetch\"), __webpack_require__.e(\"vendor-chunks/whatwg-url\"), __webpack_require__.e(\"vendor-chunks/fast-safe-stringify\"), __webpack_require__.e(\"vendor-chunks/webidl-conversions\"), __webpack_require__.e(\"vendor-chunks/pino-pretty\"), __webpack_require__.e(\"vendor-chunks/multiformats\"), __webpack_require__.e(\"vendor-chunks/pino\"), __webpack_require__.e(\"vendor-chunks/uint8arrays\"), __webpack_require__.e(\"vendor-chunks/fast-redact\"), __webpack_require__.e(\"vendor-chunks/pino-std-serializers\"), __webpack_require__.e(\"vendor-chunks/thread-stream\"), __webpack_require__.e(\"vendor-chunks/unstorage\"), __webpack_require__.e(\"vendor-chunks/on-exit-leak-free\"), __webpack_require__.e(\"vendor-chunks/atomic-sleep\"), __webpack_require__.e(\"vendor-chunks/idb-keyval\"), __webpack_require__.e(\"vendor-chunks/destr\"), __webpack_require__.e(\"vendor-chunks/fast-copy\"), __webpack_require__.e(\"vendor-chunks/colorette\"), __webpack_require__.e(\"vendor-chunks/sonic-boom\"), __webpack_require__.e(\"vendor-chunks/wrappy\"), __webpack_require__.e(\"vendor-chunks/split2\"), __webpack_require__.e(\"vendor-chunks/secure-json-parse\"), __webpack_require__.e(\"vendor-chunks/pump\"), __webpack_require__.e(\"vendor-chunks/process\"), __webpack_require__.e(\"vendor-chunks/pino-abstract-transport\"), __webpack_require__.e(\"vendor-chunks/once\"), __webpack_require__.e(\"vendor-chunks/event-target-shim\"), __webpack_require__.e(\"vendor-chunks/end-of-stream\"), __webpack_require__.e(\"vendor-chunks/dateformat\"), __webpack_require__.e(\"vendor-chunks/abort-controller\"), __webpack_require__.e(\"vendor-chunks/safe-stable-stringify\"), __webpack_require__.e(\"vendor-chunks/quick-format-unescaped\"), __webpack_require__.e(\"vendor-chunks/process-warning\"), __webpack_require__.e(\"vendor-chunks/detect-browser\")]).then(__webpack_require__.bind(__webpack_require__, /*! @walletconnect/ethereum-provider */ \"(ssr)/../node_modules/@walletconnect/ethereum-provider/dist/index.es.js\"));\n                return await EthereumProvider.init({\n                    ...parameters,\n                    disableProviderPing: true,\n                    optionalChains,\n                    projectId: parameters.projectId,\n                    rpcMap: Object.fromEntries(config.chains.map((chain) => {\n                        const [url] = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_4__.extractRpcUrls)({\n                            chain,\n                            transports: config.transports,\n                        });\n                        return [chain.id, url];\n                    })),\n                    showQrModal: parameters.showQrModal ?? true,\n                });\n            }\n            if (!provider_) {\n                if (!providerPromise)\n                    providerPromise = initProvider();\n                provider_ = await providerPromise;\n                provider_?.events.setMaxListeners(Number.POSITIVE_INFINITY);\n            }\n            if (chainId)\n                await this.switchChain?.({ chainId });\n            return provider_;\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            return provider.chainId;\n        },\n        async isAuthorized() {\n            try {\n                const [accounts, provider] = await Promise.all([\n                    this.getAccounts(),\n                    this.getProvider(),\n                ]);\n                // If an account does not exist on the session, then the connector is unauthorized.\n                if (!accounts.length)\n                    return false;\n                // If the chains are stale on the session, then the connector is unauthorized.\n                const isChainsStale = await this.isChainsStale();\n                if (isChainsStale && provider.session) {\n                    await provider.disconnect().catch(() => { });\n                    return false;\n                }\n                return true;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _wagmi_core__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _wagmi_core__WEBPACK_IMPORTED_MODULE_5__.ChainNotConfiguredError());\n            try {\n                await Promise.all([\n                    new Promise((resolve) => {\n                        const listener = ({ chainId: currentChainId, }) => {\n                            if (currentChainId === chainId) {\n                                config.emitter.off('change', listener);\n                                resolve();\n                            }\n                        };\n                        config.emitter.on('change', listener);\n                    }),\n                    provider.request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId) }],\n                    }),\n                ]);\n                const requestedChains = await this.getRequestedChainsIds();\n                this.setRequestedChainsIds([...requestedChains, chainId]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                if (/(user rejected)/i.test(error.message))\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                // Indicates chain is not added to provider\n                try {\n                    let blockExplorerUrls;\n                    if (addEthereumChainParameter?.blockExplorerUrls)\n                        blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                    else\n                        blockExplorerUrls = chain.blockExplorers?.default.url\n                            ? [chain.blockExplorers?.default.url]\n                            : [];\n                    let rpcUrls;\n                    if (addEthereumChainParameter?.rpcUrls?.length)\n                        rpcUrls = addEthereumChainParameter.rpcUrls;\n                    else\n                        rpcUrls = [...chain.rpcUrls.default.http];\n                    const addEthereumChain = {\n                        blockExplorerUrls,\n                        chainId: (0,viem__WEBPACK_IMPORTED_MODULE_6__.numberToHex)(chainId),\n                        chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                        iconUrls: addEthereumChainParameter?.iconUrls,\n                        nativeCurrency: addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,\n                        rpcUrls,\n                    };\n                    await provider.request({\n                        method: 'wallet_addEthereumChain',\n                        params: [addEthereumChain],\n                    });\n                    const requestedChains = await this.getRequestedChainsIds();\n                    this.setRequestedChainsIds([...requestedChains, chainId]);\n                    return chain;\n                }\n                catch (error) {\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                }\n            }\n        },\n        onAccountsChanged(accounts) {\n            if (accounts.length === 0)\n                this.onDisconnect();\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const chainId = Number(connectInfo.chainId);\n            const accounts = await this.getAccounts();\n            config.emitter.emit('connect', { accounts, chainId });\n        },\n        async onDisconnect(_error) {\n            this.setRequestedChainsIds([]);\n            config.emitter.emit('disconnect');\n            const provider = await this.getProvider();\n            if (accountsChanged) {\n                provider.removeListener('accountsChanged', accountsChanged);\n                accountsChanged = undefined;\n            }\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (sessionDelete) {\n                provider.removeListener('session_delete', sessionDelete);\n                sessionDelete = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n        },\n        onDisplayUri(uri) {\n            config.emitter.emit('message', { type: 'display_uri', data: uri });\n        },\n        onSessionDelete() {\n            this.onDisconnect();\n        },\n        getNamespaceChainsIds() {\n            if (!provider_)\n                return [];\n            const chainIds = provider_.session?.namespaces[NAMESPACE]?.accounts?.map((account) => Number.parseInt(account.split(':')[1] || ''));\n            return chainIds ?? [];\n        },\n        async getRequestedChainsIds() {\n            return ((await config.storage?.getItem(this.requestedChainsStorageKey)) ?? []);\n        },\n        /**\n         * Checks if the target chains match the chains that were\n         * initially requested by the connector for the WalletConnect session.\n         * If there is a mismatch, this means that the chains on the connector\n         * are considered stale, and need to be revalidated at a later point (via\n         * connection).\n         *\n         * There may be a scenario where a dapp adds a chain to the\n         * connector later on, however, this chain will not have been approved or rejected\n         * by the wallet. In this case, the chain is considered stale.\n         */\n        async isChainsStale() {\n            if (!isNewChainsStale)\n                return false;\n            const connectorChains = config.chains.map((x) => x.id);\n            const namespaceChains = this.getNamespaceChainsIds();\n            if (namespaceChains.length &&\n                !namespaceChains.some((id) => connectorChains.includes(id)))\n                return false;\n            const requestedChains = await this.getRequestedChainsIds();\n            return !connectorChains.every((id) => requestedChains.includes(id));\n        },\n        async setRequestedChainsIds(chains) {\n            await config.storage?.setItem(this.requestedChainsStorageKey, chains);\n        },\n        get requestedChainsStorageKey() {\n            return `${this.id}.requestedChains`;\n        },\n    }));\n}\n//# sourceMappingURL=walletConnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/connectors/dist/esm/walletConnect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/connect.js":
/*!***************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/connect.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connect: () => (/* binding */ connect)\n/* harmony export */ });\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n/** https://wagmi.sh/core/api/actions/connect */\nasync function connect(config, parameters) {\n    // \"Register\" connector if not already created\n    let connector;\n    if (typeof parameters.connector === 'function') {\n        connector = config._internal.connectors.setup(parameters.connector);\n    }\n    else\n        connector = parameters.connector;\n    // Check if connector is already connected\n    if (connector.uid === config.state.current)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAlreadyConnectedError();\n    try {\n        config.setState((x) => ({ ...x, status: 'connecting' }));\n        connector.emitter.emit('message', { type: 'connecting' });\n        const { connector: _, ...rest } = parameters;\n        const data = await connector.connect(rest);\n        const accounts = data.accounts;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        await config.storage?.setItem('recentConnectorId', connector.id);\n        config.setState((x) => ({\n            ...x,\n            connections: new Map(x.connections).set(connector.uid, {\n                accounts,\n                chainId: data.chainId,\n                connector: connector,\n            }),\n            current: connector.uid,\n            status: 'connected',\n        }));\n        return { accounts, chainId: data.chainId };\n    }\n    catch (error) {\n        config.setState((x) => ({\n            ...x,\n            // Keep existing connector connected in case of error\n            status: x.current ? 'connected' : 'disconnected',\n        }));\n        throw error;\n    }\n}\n//# sourceMappingURL=connect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/connect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/disconnect.js":
/*!******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/disconnect.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnect: () => (/* binding */ disconnect)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/disconnect */\nasync function disconnect(config, parameters = {}) {\n    let connector;\n    if (parameters.connector)\n        connector = parameters.connector;\n    else {\n        const { connections, current } = config.state;\n        const connection = connections.get(current);\n        connector = connection?.connector;\n    }\n    const connections = config.state.connections;\n    if (connector) {\n        await connector.disconnect();\n        connector.emitter.off('change', config._internal.events.change);\n        connector.emitter.off('disconnect', config._internal.events.disconnect);\n        connector.emitter.on('connect', config._internal.events.connect);\n        connections.delete(connector.uid);\n    }\n    config.setState((x) => {\n        // if no connections exist, move to disconnected state\n        if (connections.size === 0)\n            return {\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            };\n        // switch over to another connection\n        const nextConnection = connections.values().next().value;\n        return {\n            ...x,\n            connections: new Map(connections),\n            current: nextConnection.connector.uid,\n        };\n    });\n    // Set recent connector if exists\n    {\n        const current = config.state.current;\n        if (!current)\n            return;\n        const connector = config.state.connections.get(current)?.connector;\n        if (!connector)\n            return;\n        await config.storage?.setItem('recentConnectorId', connector.id);\n    }\n}\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/disconnect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getAccount.js":
/*!******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getAccount.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccount: () => (/* binding */ getAccount)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/getAccount */\nfunction getAccount(config) {\n    const uid = config.state.current;\n    const connection = config.state.connections.get(uid);\n    const addresses = connection?.accounts;\n    const address = addresses?.[0];\n    const chain = config.chains.find((chain) => chain.id === connection?.chainId);\n    const status = config.state.status;\n    switch (status) {\n        case 'connected':\n            return {\n                address: address,\n                addresses: addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: true,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'reconnecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: !!address,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: true,\n                status,\n            };\n        case 'connecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: false,\n                isConnecting: true,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'disconnected':\n            return {\n                address: undefined,\n                addresses: undefined,\n                chain: undefined,\n                chainId: undefined,\n                connector: undefined,\n                isConnected: false,\n                isConnecting: false,\n                isDisconnected: true,\n                isReconnecting: false,\n                status,\n            };\n    }\n}\n//# sourceMappingURL=getAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getAccount.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getBalance.js":
/*!******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getBalance.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBalance: () => (/* binding */ getBalance)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/data/trim.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/unit/formatUnits.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/public/getBalance.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/getUnit.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getUnit.js\");\n/* harmony import */ var _readContracts_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./readContracts.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/readContracts.js\");\n\n\n\n\n\n/** https://wagmi.sh/core/api/actions/getBalance */\nasync function getBalance(config, parameters) {\n    const { address, blockNumber, blockTag, chainId, token: tokenAddress, unit = 'ether', } = parameters;\n    if (tokenAddress) {\n        try {\n            return await getTokenBalance(config, {\n                balanceAddress: address,\n                chainId,\n                symbolType: 'string',\n                tokenAddress,\n            });\n        }\n        catch (error) {\n            // In the chance that there is an error upon decoding the contract result,\n            // it could be likely that the contract data is represented as bytes32 instead\n            // of a string.\n            if (error.name ===\n                'ContractFunctionExecutionError') {\n                const balance = await getTokenBalance(config, {\n                    balanceAddress: address,\n                    chainId,\n                    symbolType: 'bytes32',\n                    tokenAddress,\n                });\n                const symbol = (0,viem__WEBPACK_IMPORTED_MODULE_0__.hexToString)((0,viem__WEBPACK_IMPORTED_MODULE_1__.trim)(balance.symbol, { dir: 'right' }));\n                return { ...balance, symbol };\n            }\n            throw error;\n        }\n    }\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_2__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.getBalance, 'getBalance');\n    const value = await action(blockNumber ? { address, blockNumber } : { address, blockTag });\n    const chain = config.chains.find((x) => x.id === chainId) ?? client.chain;\n    return {\n        decimals: chain.nativeCurrency.decimals,\n        formatted: (0,viem__WEBPACK_IMPORTED_MODULE_4__.formatUnits)(value, (0,_utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__.getUnit)(unit)),\n        symbol: chain.nativeCurrency.symbol,\n        value,\n    };\n}\nasync function getTokenBalance(config, parameters) {\n    const { balanceAddress, chainId, symbolType, tokenAddress, unit } = parameters;\n    const contract = {\n        abi: [\n            {\n                type: 'function',\n                name: 'balanceOf',\n                stateMutability: 'view',\n                inputs: [{ type: 'address' }],\n                outputs: [{ type: 'uint256' }],\n            },\n            {\n                type: 'function',\n                name: 'decimals',\n                stateMutability: 'view',\n                inputs: [],\n                outputs: [{ type: 'uint8' }],\n            },\n            {\n                type: 'function',\n                name: 'symbol',\n                stateMutability: 'view',\n                inputs: [],\n                outputs: [{ type: symbolType }],\n            },\n        ],\n        address: tokenAddress,\n    };\n    const [value, decimals, symbol] = await (0,_readContracts_js__WEBPACK_IMPORTED_MODULE_6__.readContracts)(config, {\n        allowFailure: false,\n        contracts: [\n            {\n                ...contract,\n                functionName: 'balanceOf',\n                args: [balanceAddress],\n                chainId,\n            },\n            { ...contract, functionName: 'decimals', chainId },\n            { ...contract, functionName: 'symbol', chainId },\n        ],\n    });\n    const formatted = (0,viem__WEBPACK_IMPORTED_MODULE_4__.formatUnits)(value ?? '0', (0,_utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__.getUnit)(unit ?? decimals));\n    return { decimals, formatted, symbol, value };\n}\n//# sourceMappingURL=getBalance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getBalance.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getChainId.js":
/*!******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getChainId.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChainId: () => (/* binding */ getChainId)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/getChainId */\nfunction getChainId(config) {\n    return config.state.chainId;\n}\n//# sourceMappingURL=getChainId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q2hhaW5JZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL2dldENoYWluSWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9nZXRDaGFpbklkICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q2hhaW5JZChjb25maWcpIHtcbiAgICByZXR1cm4gY29uZmlnLnN0YXRlLmNoYWluSWQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRDaGFpbklkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getChainId.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getChains.js":
/*!*****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getChains.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChains: () => (/* binding */ getChains)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n\nlet previousChains = [];\n/** https://wagmi.sh/core/api/actions/getChains */\nfunction getChains(config) {\n    const chains = config.chains;\n    if ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(previousChains, chains))\n        return previousChains;\n    previousChains = chains;\n    return chains;\n}\n//# sourceMappingURL=getChains.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q2hhaW5zLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtEO0FBQ2xEO0FBQ0E7QUFDTztBQUNQO0FBQ0EsUUFBUSw4REFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q2hhaW5zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlZXBFcXVhbCB9IGZyb20gJy4uL3V0aWxzL2RlZXBFcXVhbC5qcyc7XG5sZXQgcHJldmlvdXNDaGFpbnMgPSBbXTtcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvZ2V0Q2hhaW5zICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q2hhaW5zKGNvbmZpZykge1xuICAgIGNvbnN0IGNoYWlucyA9IGNvbmZpZy5jaGFpbnM7XG4gICAgaWYgKGRlZXBFcXVhbChwcmV2aW91c0NoYWlucywgY2hhaW5zKSlcbiAgICAgICAgcmV0dXJuIHByZXZpb3VzQ2hhaW5zO1xuICAgIHByZXZpb3VzQ2hhaW5zID0gY2hhaW5zO1xuICAgIHJldHVybiBjaGFpbnM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRDaGFpbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getChains.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getClient.js":
/*!*****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getClient.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getClient: () => (/* binding */ getClient)\n/* harmony export */ });\nfunction getClient(config, parameters = {}) {\n    let client = undefined;\n    try {\n        client = config.getClient(parameters);\n    }\n    catch { }\n    return client;\n}\n//# sourceMappingURL=getClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTywwQ0FBMEM7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL2dldENsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZ2V0Q2xpZW50KGNvbmZpZywgcGFyYW1ldGVycyA9IHt9KSB7XG4gICAgbGV0IGNsaWVudCA9IHVuZGVmaW5lZDtcbiAgICB0cnkge1xuICAgICAgICBjbGllbnQgPSBjb25maWcuZ2V0Q2xpZW50KHBhcmFtZXRlcnMpO1xuICAgIH1cbiAgICBjYXRjaCB7IH1cbiAgICByZXR1cm4gY2xpZW50O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0Q2xpZW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getClient.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnections.js":
/*!**********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getConnections.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnections: () => (/* binding */ getConnections)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n\nlet previousConnections = [];\n/** https://wagmi.sh/core/api/actions/getConnections */\nfunction getConnections(config) {\n    const connections = [...config.state.connections.values()];\n    if (config.state.status === 'reconnecting')\n        return previousConnections;\n    if ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(previousConnections, connections))\n        return previousConnections;\n    previousConnections = connections;\n    return connections;\n}\n//# sourceMappingURL=getConnections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q29ubmVjdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0Q7QUFDbEQ7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4REFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q29ubmVjdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVlcEVxdWFsIH0gZnJvbSAnLi4vdXRpbHMvZGVlcEVxdWFsLmpzJztcbmxldCBwcmV2aW91c0Nvbm5lY3Rpb25zID0gW107XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL2dldENvbm5lY3Rpb25zICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q29ubmVjdGlvbnMoY29uZmlnKSB7XG4gICAgY29uc3QgY29ubmVjdGlvbnMgPSBbLi4uY29uZmlnLnN0YXRlLmNvbm5lY3Rpb25zLnZhbHVlcygpXTtcbiAgICBpZiAoY29uZmlnLnN0YXRlLnN0YXR1cyA9PT0gJ3JlY29ubmVjdGluZycpXG4gICAgICAgIHJldHVybiBwcmV2aW91c0Nvbm5lY3Rpb25zO1xuICAgIGlmIChkZWVwRXF1YWwocHJldmlvdXNDb25uZWN0aW9ucywgY29ubmVjdGlvbnMpKVxuICAgICAgICByZXR1cm4gcHJldmlvdXNDb25uZWN0aW9ucztcbiAgICBwcmV2aW91c0Nvbm5lY3Rpb25zID0gY29ubmVjdGlvbnM7XG4gICAgcmV0dXJuIGNvbm5lY3Rpb25zO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0Q29ubmVjdGlvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnections.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js":
/*!**************************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnectorClient: () => (/* binding */ getConnectorClient)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/utils */ \"(ssr)/../node_modules/viem/_esm/accounts/utils/parseAccount.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/utils */ \"(ssr)/../node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/getConnectorClient */\nasync function getConnectorClient(config, parameters = {}) {\n    // Get connection\n    let connection;\n    if (parameters.connector) {\n        const { connector } = parameters;\n        if (config.state.status === 'reconnecting' &&\n            !connector.getAccounts &&\n            !connector.getChainId)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorUnavailableReconnectingError({ connector });\n        const [accounts, chainId] = await Promise.all([\n            connector.getAccounts().catch((e) => {\n                if (parameters.account === null)\n                    return [];\n                throw e;\n            }),\n            connector.getChainId(),\n        ]);\n        connection = {\n            accounts: accounts,\n            chainId,\n            connector,\n        };\n    }\n    else\n        connection = config.state.connections.get(config.state.current);\n    if (!connection)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorNotConnectedError();\n    const chainId = parameters.chainId ?? connection.chainId;\n    // Check connector using same chainId as connection\n    const connectorChainId = await connection.connector.getChainId();\n    if (connectorChainId !== connection.chainId)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorChainMismatchError({\n            connectionChainId: connection.chainId,\n            connectorChainId,\n        });\n    const connector = connection.connector;\n    if (connector.getClient)\n        return connector.getClient({ chainId });\n    // Default using `custom` transport\n    const account = (0,viem_utils__WEBPACK_IMPORTED_MODULE_1__.parseAccount)(parameters.account ?? connection.accounts[0]);\n    if (account)\n        account.address = (0,viem_utils__WEBPACK_IMPORTED_MODULE_2__.getAddress)(account.address); // TODO: Checksum address as part of `parseAccount`?\n    // If account was provided, check that it exists on the connector\n    if (parameters.account &&\n        !connection.accounts.some((x) => x.toLowerCase() === account.address.toLowerCase()))\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAccountNotFoundError({\n            address: account.address,\n            connector,\n        });\n    const chain = config.chains.find((chain) => chain.id === chainId);\n    const provider = (await connection.connector.getProvider({ chainId }));\n    return (0,viem__WEBPACK_IMPORTED_MODULE_3__.createClient)({\n        account,\n        chain,\n        name: 'Connector Client',\n        transport: (opts) => (0,viem__WEBPACK_IMPORTED_MODULE_4__.custom)(provider)({ ...opts, retryCount: 0 }),\n    });\n}\n//# sourceMappingURL=getConnectorClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnectors.js":
/*!*********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getConnectors.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnectors: () => (/* binding */ getConnectors)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n\nlet previousConnectors = [];\n/** https://wagmi.sh/core/api/actions/getConnectors */\nfunction getConnectors(config) {\n    const connectors = config.connectors;\n    if ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(previousConnectors, connectors))\n        return previousConnectors;\n    previousConnectors = connectors;\n    return connectors;\n}\n//# sourceMappingURL=getConnectors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0Q29ubmVjdG9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUNsRDtBQUNBO0FBQ087QUFDUDtBQUNBLFFBQVEsOERBQVM7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL2dldENvbm5lY3RvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVlcEVxdWFsIH0gZnJvbSAnLi4vdXRpbHMvZGVlcEVxdWFsLmpzJztcbmxldCBwcmV2aW91c0Nvbm5lY3RvcnMgPSBbXTtcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvZ2V0Q29ubmVjdG9ycyAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldENvbm5lY3RvcnMoY29uZmlnKSB7XG4gICAgY29uc3QgY29ubmVjdG9ycyA9IGNvbmZpZy5jb25uZWN0b3JzO1xuICAgIGlmIChkZWVwRXF1YWwocHJldmlvdXNDb25uZWN0b3JzLCBjb25uZWN0b3JzKSlcbiAgICAgICAgcmV0dXJuIHByZXZpb3VzQ29ubmVjdG9ycztcbiAgICBwcmV2aW91c0Nvbm5lY3RvcnMgPSBjb25uZWN0b3JzO1xuICAgIHJldHVybiBjb25uZWN0b3JzO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0Q29ubmVjdG9ycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnectors.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getEnsAvatar.js":
/*!********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getEnsAvatar.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnsAvatar: () => (/* binding */ getEnsAvatar)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/ens/getEnsAvatar.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n/** https://wagmi.sh/core/api/actions/getEnsAvatar */\nfunction getEnsAvatar(config, parameters) {\n    const { chainId, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.getEnsAvatar, 'getEnsAvatar');\n    return action(rest);\n}\n//# sourceMappingURL=getEnsAvatar.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0RW5zQXZhdGFyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRTtBQUNoQjtBQUNsRDtBQUNPO0FBQ1AsWUFBWSxtQkFBbUI7QUFDL0Isc0NBQXNDLFNBQVM7QUFDL0MsbUJBQW1CLDhEQUFTLFNBQVMsc0RBQWlCO0FBQ3REO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL2dldEVuc0F2YXRhci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRFbnNBdmF0YXIgYXMgdmllbV9nZXRFbnNBdmF0YXIsIH0gZnJvbSAndmllbS9hY3Rpb25zJztcbmltcG9ydCB7IGdldEFjdGlvbiB9IGZyb20gJy4uL3V0aWxzL2dldEFjdGlvbi5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL2dldEVuc0F2YXRhciAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEVuc0F2YXRhcihjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGNoYWluSWQsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgY2xpZW50ID0gY29uZmlnLmdldENsaWVudCh7IGNoYWluSWQgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV9nZXRFbnNBdmF0YXIsICdnZXRFbnNBdmF0YXInKTtcbiAgICByZXR1cm4gYWN0aW9uKHJlc3QpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0RW5zQXZhdGFyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getEnsAvatar.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getEnsName.js":
/*!******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getEnsName.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnsName: () => (/* binding */ getEnsName)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/ens/getEnsName.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n/** https://wagmi.sh/core/api/actions/getEnsName */\nfunction getEnsName(config, parameters) {\n    const { chainId, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.getEnsName, 'getEnsName');\n    return action(rest);\n}\n//# sourceMappingURL=getEnsName.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0RW5zTmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEQ7QUFDWjtBQUNsRDtBQUNPO0FBQ1AsWUFBWSxtQkFBbUI7QUFDL0Isc0NBQXNDLFNBQVM7QUFDL0MsbUJBQW1CLDhEQUFTLFNBQVMsb0RBQWU7QUFDcEQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0RW5zTmFtZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRFbnNOYW1lIGFzIHZpZW1fZ2V0RW5zTmFtZSwgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvZ2V0RW5zTmFtZSAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEVuc05hbWUoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBjaGFpbklkLCAuLi5yZXN0IH0gPSBwYXJhbWV0ZXJzO1xuICAgIGNvbnN0IGNsaWVudCA9IGNvbmZpZy5nZXRDbGllbnQoeyBjaGFpbklkIH0pO1xuICAgIGNvbnN0IGFjdGlvbiA9IGdldEFjdGlvbihjbGllbnQsIHZpZW1fZ2V0RW5zTmFtZSwgJ2dldEVuc05hbWUnKTtcbiAgICByZXR1cm4gYWN0aW9uKHJlc3QpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0RW5zTmFtZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getEnsName.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getPublicClient.js":
/*!***********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/getPublicClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPublicClient: () => (/* binding */ getPublicClient)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/clients/decorators/public.js\");\n/* harmony import */ var _getClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getClient.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getClient.js\");\n\n\nfunction getPublicClient(config, parameters = {}) {\n    const client = (0,_getClient_js__WEBPACK_IMPORTED_MODULE_0__.getClient)(config, parameters);\n    return client?.extend(viem__WEBPACK_IMPORTED_MODULE_1__.publicActions);\n}\n//# sourceMappingURL=getPublicClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZ2V0UHVibGljQ2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQztBQUNNO0FBQ3BDLGdEQUFnRDtBQUN2RCxtQkFBbUIsd0RBQVM7QUFDNUIsMEJBQTBCLCtDQUFhO0FBQ3ZDO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRQdWJsaWNDbGllbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHVibGljQWN0aW9ucyB9IGZyb20gJ3ZpZW0nO1xuaW1wb3J0IHsgZ2V0Q2xpZW50IH0gZnJvbSAnLi9nZXRDbGllbnQuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGdldFB1YmxpY0NsaWVudChjb25maWcsIHBhcmFtZXRlcnMgPSB7fSkge1xuICAgIGNvbnN0IGNsaWVudCA9IGdldENsaWVudChjb25maWcsIHBhcmFtZXRlcnMpO1xuICAgIHJldHVybiBjbGllbnQ/LmV4dGVuZChwdWJsaWNBY3Rpb25zKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldFB1YmxpY0NsaWVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getPublicClient.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/multicall.js":
/*!*****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/multicall.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   multicall: () => (/* binding */ multicall)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/public/multicall.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\nasync function multicall(config, parameters) {\n    const { allowFailure = true, chainId, contracts, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.multicall, 'multicall');\n    return action({\n        allowFailure,\n        contracts,\n        ...rest,\n    });\n}\n//# sourceMappingURL=multicall.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvbXVsdGljYWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyRDtBQUNUO0FBQzNDO0FBQ1AsWUFBWSxtREFBbUQ7QUFDL0Qsc0NBQXNDLFNBQVM7QUFDL0MsbUJBQW1CLDhEQUFTLFNBQVMsbURBQWM7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL211bHRpY2FsbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtdWx0aWNhbGwgYXMgdmllbV9tdWx0aWNhbGwgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBtdWx0aWNhbGwoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBhbGxvd0ZhaWx1cmUgPSB0cnVlLCBjaGFpbklkLCBjb250cmFjdHMsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgY2xpZW50ID0gY29uZmlnLmdldENsaWVudCh7IGNoYWluSWQgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV9tdWx0aWNhbGwsICdtdWx0aWNhbGwnKTtcbiAgICByZXR1cm4gYWN0aW9uKHtcbiAgICAgICAgYWxsb3dGYWlsdXJlLFxuICAgICAgICBjb250cmFjdHMsXG4gICAgICAgIC4uLnJlc3QsXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tdWx0aWNhbGwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/multicall.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/readContract.js":
/*!********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/readContract.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContract: () => (/* binding */ readContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/public/readContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n/** https://wagmi.sh/core/api/actions/readContract */\nfunction readContract(config, parameters) {\n    const { chainId, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.readContract, 'readContract');\n    return action(rest);\n}\n//# sourceMappingURL=readContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvcmVhZENvbnRyYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRTtBQUNoQjtBQUNsRDtBQUNPO0FBQ1AsWUFBWSxtQkFBbUI7QUFDL0Isc0NBQXNDLFNBQVM7QUFDL0MsbUJBQW1CLDhEQUFTLFNBQVMsc0RBQWlCO0FBQ3REO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3JlYWRDb250cmFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZWFkQ29udHJhY3QgYXMgdmllbV9yZWFkQ29udHJhY3QsIH0gZnJvbSAndmllbS9hY3Rpb25zJztcbmltcG9ydCB7IGdldEFjdGlvbiB9IGZyb20gJy4uL3V0aWxzL2dldEFjdGlvbi5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3JlYWRDb250cmFjdCAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJlYWRDb250cmFjdChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGNoYWluSWQsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgY2xpZW50ID0gY29uZmlnLmdldENsaWVudCh7IGNoYWluSWQgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV9yZWFkQ29udHJhY3QsICdyZWFkQ29udHJhY3QnKTtcbiAgICByZXR1cm4gYWN0aW9uKHJlc3QpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVhZENvbnRyYWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/readContract.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/readContracts.js":
/*!*********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/readContracts.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContracts: () => (/* binding */ readContracts)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/errors/contract.js\");\n/* harmony import */ var _multicall_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./multicall.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/multicall.js\");\n/* harmony import */ var _readContract_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./readContract.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/readContract.js\");\n\n\n\nasync function readContracts(config, parameters) {\n    const { allowFailure = true, blockNumber, blockTag, ...rest } = parameters;\n    const contracts = parameters.contracts;\n    try {\n        const contractsByChainId = {};\n        for (const [index, contract] of contracts.entries()) {\n            const chainId = contract.chainId ?? config.state.chainId;\n            if (!contractsByChainId[chainId])\n                contractsByChainId[chainId] = [];\n            contractsByChainId[chainId]?.push({ contract, index });\n        }\n        const promises = () => Object.entries(contractsByChainId).map(([chainId, contracts]) => (0,_multicall_js__WEBPACK_IMPORTED_MODULE_0__.multicall)(config, {\n            ...rest,\n            allowFailure,\n            blockNumber,\n            blockTag,\n            chainId: Number.parseInt(chainId),\n            contracts: contracts.map(({ contract }) => contract),\n        }));\n        const multicallResults = (await Promise.all(promises())).flat();\n        // Reorder the contract results back to the order they were\n        // provided in.\n        const resultIndexes = Object.values(contractsByChainId).flatMap((contracts) => contracts.map(({ index }) => index));\n        return multicallResults.reduce((results, result, index) => {\n            if (results)\n                results[resultIndexes[index]] = result;\n            return results;\n        }, []);\n    }\n    catch (error) {\n        if (error instanceof viem__WEBPACK_IMPORTED_MODULE_1__.ContractFunctionExecutionError)\n            throw error;\n        const promises = () => contracts.map((contract) => (0,_readContract_js__WEBPACK_IMPORTED_MODULE_2__.readContract)(config, { ...contract, blockNumber, blockTag }));\n        if (allowFailure)\n            return (await Promise.allSettled(promises())).map((result) => {\n                if (result.status === 'fulfilled')\n                    return { result: result.value, status: 'success' };\n                return { error: result.reason, result: undefined, status: 'failure' };\n            });\n        return (await Promise.all(promises()));\n    }\n}\n//# sourceMappingURL=readContracts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/readContracts.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/reconnect.js":
/*!*****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/reconnect.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reconnect: () => (/* binding */ reconnect)\n/* harmony export */ });\nlet isReconnecting = false;\n/** https://wagmi.sh/core/api/actions/reconnect */\nasync function reconnect(config, parameters = {}) {\n    // If already reconnecting, do nothing\n    if (isReconnecting)\n        return [];\n    isReconnecting = true;\n    config.setState((x) => ({\n        ...x,\n        status: x.current ? 'reconnecting' : 'connecting',\n    }));\n    const connectors = [];\n    if (parameters.connectors?.length) {\n        for (const connector_ of parameters.connectors) {\n            let connector;\n            // \"Register\" connector if not already created\n            if (typeof connector_ === 'function')\n                connector = config._internal.connectors.setup(connector_);\n            else\n                connector = connector_;\n            connectors.push(connector);\n        }\n    }\n    else\n        connectors.push(...config.connectors);\n    // Try recently-used connectors first\n    let recentConnectorId;\n    try {\n        recentConnectorId = await config.storage?.getItem('recentConnectorId');\n    }\n    catch { }\n    const scores = {};\n    for (const [, connection] of config.state.connections) {\n        scores[connection.connector.id] = 1;\n    }\n    if (recentConnectorId)\n        scores[recentConnectorId] = 0;\n    const sorted = Object.keys(scores).length > 0\n        ? // .toSorted()\n            [...connectors].sort((a, b) => (scores[a.id] ?? 10) - (scores[b.id] ?? 10))\n        : connectors;\n    // Iterate through each connector and try to connect\n    let connected = false;\n    const connections = [];\n    const providers = [];\n    for (const connector of sorted) {\n        const provider = await connector.getProvider().catch(() => undefined);\n        if (!provider)\n            continue;\n        // If we already have an instance of this connector's provider,\n        // then we have already checked it (ie. injected connectors can\n        // share the same `window.ethereum` instance, so we don't want to\n        // connect to it again).\n        if (providers.some((x) => x === provider))\n            continue;\n        const isAuthorized = await connector.isAuthorized();\n        if (!isAuthorized)\n            continue;\n        const data = await connector\n            .connect({ isReconnecting: true })\n            .catch(() => null);\n        if (!data)\n            continue;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        config.setState((x) => {\n            const connections = new Map(connected ? x.connections : new Map()).set(connector.uid, { accounts: data.accounts, chainId: data.chainId, connector });\n            return {\n                ...x,\n                current: connected ? x.current : connector.uid,\n                connections,\n            };\n        });\n        connections.push({\n            accounts: data.accounts,\n            chainId: data.chainId,\n            connector,\n        });\n        providers.push(provider);\n        connected = true;\n    }\n    // Prevent overwriting connected status from race condition\n    if (config.state.status === 'reconnecting' ||\n        config.state.status === 'connecting') {\n        // If connecting didn't succeed, set to disconnected\n        if (!connected)\n            config.setState((x) => ({\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            }));\n        else\n            config.setState((x) => ({ ...x, status: 'connected' }));\n    }\n    isReconnecting = false;\n    return connections;\n}\n//# sourceMappingURL=reconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvcmVjb25uZWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ08sZ0RBQWdEO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixzQkFBc0I7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvR0FBb0csMkRBQTJEO0FBQy9KO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxzQ0FBc0MsMkJBQTJCO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9yZWNvbm5lY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGlzUmVjb25uZWN0aW5nID0gZmFsc2U7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3JlY29ubmVjdCAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlY29ubmVjdChjb25maWcsIHBhcmFtZXRlcnMgPSB7fSkge1xuICAgIC8vIElmIGFscmVhZHkgcmVjb25uZWN0aW5nLCBkbyBub3RoaW5nXG4gICAgaWYgKGlzUmVjb25uZWN0aW5nKVxuICAgICAgICByZXR1cm4gW107XG4gICAgaXNSZWNvbm5lY3RpbmcgPSB0cnVlO1xuICAgIGNvbmZpZy5zZXRTdGF0ZSgoeCkgPT4gKHtcbiAgICAgICAgLi4ueCxcbiAgICAgICAgc3RhdHVzOiB4LmN1cnJlbnQgPyAncmVjb25uZWN0aW5nJyA6ICdjb25uZWN0aW5nJyxcbiAgICB9KSk7XG4gICAgY29uc3QgY29ubmVjdG9ycyA9IFtdO1xuICAgIGlmIChwYXJhbWV0ZXJzLmNvbm5lY3RvcnM/Lmxlbmd0aCkge1xuICAgICAgICBmb3IgKGNvbnN0IGNvbm5lY3Rvcl8gb2YgcGFyYW1ldGVycy5jb25uZWN0b3JzKSB7XG4gICAgICAgICAgICBsZXQgY29ubmVjdG9yO1xuICAgICAgICAgICAgLy8gXCJSZWdpc3RlclwiIGNvbm5lY3RvciBpZiBub3QgYWxyZWFkeSBjcmVhdGVkXG4gICAgICAgICAgICBpZiAodHlwZW9mIGNvbm5lY3Rvcl8gPT09ICdmdW5jdGlvbicpXG4gICAgICAgICAgICAgICAgY29ubmVjdG9yID0gY29uZmlnLl9pbnRlcm5hbC5jb25uZWN0b3JzLnNldHVwKGNvbm5lY3Rvcl8pO1xuICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgIGNvbm5lY3RvciA9IGNvbm5lY3Rvcl87XG4gICAgICAgICAgICBjb25uZWN0b3JzLnB1c2goY29ubmVjdG9yKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlXG4gICAgICAgIGNvbm5lY3RvcnMucHVzaCguLi5jb25maWcuY29ubmVjdG9ycyk7XG4gICAgLy8gVHJ5IHJlY2VudGx5LXVzZWQgY29ubmVjdG9ycyBmaXJzdFxuICAgIGxldCByZWNlbnRDb25uZWN0b3JJZDtcbiAgICB0cnkge1xuICAgICAgICByZWNlbnRDb25uZWN0b3JJZCA9IGF3YWl0IGNvbmZpZy5zdG9yYWdlPy5nZXRJdGVtKCdyZWNlbnRDb25uZWN0b3JJZCcpO1xuICAgIH1cbiAgICBjYXRjaCB7IH1cbiAgICBjb25zdCBzY29yZXMgPSB7fTtcbiAgICBmb3IgKGNvbnN0IFssIGNvbm5lY3Rpb25dIG9mIGNvbmZpZy5zdGF0ZS5jb25uZWN0aW9ucykge1xuICAgICAgICBzY29yZXNbY29ubmVjdGlvbi5jb25uZWN0b3IuaWRdID0gMTtcbiAgICB9XG4gICAgaWYgKHJlY2VudENvbm5lY3RvcklkKVxuICAgICAgICBzY29yZXNbcmVjZW50Q29ubmVjdG9ySWRdID0gMDtcbiAgICBjb25zdCBzb3J0ZWQgPSBPYmplY3Qua2V5cyhzY29yZXMpLmxlbmd0aCA+IDBcbiAgICAgICAgPyAvLyAudG9Tb3J0ZWQoKVxuICAgICAgICAgICAgWy4uLmNvbm5lY3RvcnNdLnNvcnQoKGEsIGIpID0+IChzY29yZXNbYS5pZF0gPz8gMTApIC0gKHNjb3Jlc1tiLmlkXSA/PyAxMCkpXG4gICAgICAgIDogY29ubmVjdG9ycztcbiAgICAvLyBJdGVyYXRlIHRocm91Z2ggZWFjaCBjb25uZWN0b3IgYW5kIHRyeSB0byBjb25uZWN0XG4gICAgbGV0IGNvbm5lY3RlZCA9IGZhbHNlO1xuICAgIGNvbnN0IGNvbm5lY3Rpb25zID0gW107XG4gICAgY29uc3QgcHJvdmlkZXJzID0gW107XG4gICAgZm9yIChjb25zdCBjb25uZWN0b3Igb2Ygc29ydGVkKSB7XG4gICAgICAgIGNvbnN0IHByb3ZpZGVyID0gYXdhaXQgY29ubmVjdG9yLmdldFByb3ZpZGVyKCkuY2F0Y2goKCkgPT4gdW5kZWZpbmVkKTtcbiAgICAgICAgaWYgKCFwcm92aWRlcilcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAvLyBJZiB3ZSBhbHJlYWR5IGhhdmUgYW4gaW5zdGFuY2Ugb2YgdGhpcyBjb25uZWN0b3IncyBwcm92aWRlcixcbiAgICAgICAgLy8gdGhlbiB3ZSBoYXZlIGFscmVhZHkgY2hlY2tlZCBpdCAoaWUuIGluamVjdGVkIGNvbm5lY3RvcnMgY2FuXG4gICAgICAgIC8vIHNoYXJlIHRoZSBzYW1lIGB3aW5kb3cuZXRoZXJldW1gIGluc3RhbmNlLCBzbyB3ZSBkb24ndCB3YW50IHRvXG4gICAgICAgIC8vIGNvbm5lY3QgdG8gaXQgYWdhaW4pLlxuICAgICAgICBpZiAocHJvdmlkZXJzLnNvbWUoKHgpID0+IHggPT09IHByb3ZpZGVyKSlcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICBjb25zdCBpc0F1dGhvcml6ZWQgPSBhd2FpdCBjb25uZWN0b3IuaXNBdXRob3JpemVkKCk7XG4gICAgICAgIGlmICghaXNBdXRob3JpemVkKVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBjb25uZWN0b3JcbiAgICAgICAgICAgIC5jb25uZWN0KHsgaXNSZWNvbm5lY3Rpbmc6IHRydWUgfSlcbiAgICAgICAgICAgIC5jYXRjaCgoKSA9PiBudWxsKTtcbiAgICAgICAgaWYgKCFkYXRhKVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIGNvbm5lY3Rvci5lbWl0dGVyLm9mZignY29ubmVjdCcsIGNvbmZpZy5faW50ZXJuYWwuZXZlbnRzLmNvbm5lY3QpO1xuICAgICAgICBjb25uZWN0b3IuZW1pdHRlci5vbignY2hhbmdlJywgY29uZmlnLl9pbnRlcm5hbC5ldmVudHMuY2hhbmdlKTtcbiAgICAgICAgY29ubmVjdG9yLmVtaXR0ZXIub24oJ2Rpc2Nvbm5lY3QnLCBjb25maWcuX2ludGVybmFsLmV2ZW50cy5kaXNjb25uZWN0KTtcbiAgICAgICAgY29uZmlnLnNldFN0YXRlKCh4KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBjb25uZWN0aW9ucyA9IG5ldyBNYXAoY29ubmVjdGVkID8geC5jb25uZWN0aW9ucyA6IG5ldyBNYXAoKSkuc2V0KGNvbm5lY3Rvci51aWQsIHsgYWNjb3VudHM6IGRhdGEuYWNjb3VudHMsIGNoYWluSWQ6IGRhdGEuY2hhaW5JZCwgY29ubmVjdG9yIH0pO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAuLi54LFxuICAgICAgICAgICAgICAgIGN1cnJlbnQ6IGNvbm5lY3RlZCA/IHguY3VycmVudCA6IGNvbm5lY3Rvci51aWQsXG4gICAgICAgICAgICAgICAgY29ubmVjdGlvbnMsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9KTtcbiAgICAgICAgY29ubmVjdGlvbnMucHVzaCh7XG4gICAgICAgICAgICBhY2NvdW50czogZGF0YS5hY2NvdW50cyxcbiAgICAgICAgICAgIGNoYWluSWQ6IGRhdGEuY2hhaW5JZCxcbiAgICAgICAgICAgIGNvbm5lY3RvcixcbiAgICAgICAgfSk7XG4gICAgICAgIHByb3ZpZGVycy5wdXNoKHByb3ZpZGVyKTtcbiAgICAgICAgY29ubmVjdGVkID0gdHJ1ZTtcbiAgICB9XG4gICAgLy8gUHJldmVudCBvdmVyd3JpdGluZyBjb25uZWN0ZWQgc3RhdHVzIGZyb20gcmFjZSBjb25kaXRpb25cbiAgICBpZiAoY29uZmlnLnN0YXRlLnN0YXR1cyA9PT0gJ3JlY29ubmVjdGluZycgfHxcbiAgICAgICAgY29uZmlnLnN0YXRlLnN0YXR1cyA9PT0gJ2Nvbm5lY3RpbmcnKSB7XG4gICAgICAgIC8vIElmIGNvbm5lY3RpbmcgZGlkbid0IHN1Y2NlZWQsIHNldCB0byBkaXNjb25uZWN0ZWRcbiAgICAgICAgaWYgKCFjb25uZWN0ZWQpXG4gICAgICAgICAgICBjb25maWcuc2V0U3RhdGUoKHgpID0+ICh7XG4gICAgICAgICAgICAgICAgLi4ueCxcbiAgICAgICAgICAgICAgICBjb25uZWN0aW9uczogbmV3IE1hcCgpLFxuICAgICAgICAgICAgICAgIGN1cnJlbnQ6IG51bGwsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiAnZGlzY29ubmVjdGVkJyxcbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgZWxzZVxuICAgICAgICAgICAgY29uZmlnLnNldFN0YXRlKCh4KSA9PiAoeyAuLi54LCBzdGF0dXM6ICdjb25uZWN0ZWQnIH0pKTtcbiAgICB9XG4gICAgaXNSZWNvbm5lY3RpbmcgPSBmYWxzZTtcbiAgICByZXR1cm4gY29ubmVjdGlvbnM7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWNvbm5lY3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/reconnect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/signMessage.js":
/*!*******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/signMessage.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signMessage: () => (/* binding */ signMessage)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/wallet/signMessage.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/signMessage */\nasync function signMessage(config, parameters) {\n    const { account, connector, ...rest } = parameters;\n    let client;\n    if (typeof account === 'object' && account.type === 'local')\n        client = config.getClient();\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, { account, connector });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.signMessage, 'signMessage');\n    return action({\n        ...rest,\n        ...(account ? { account } : {}),\n    });\n}\n//# sourceMappingURL=signMessage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvc2lnbk1lc3NhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnRTtBQUNkO0FBQ1k7QUFDOUQ7QUFDTztBQUNQLFlBQVksOEJBQThCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDBFQUFrQixXQUFXLG9CQUFvQjtBQUN4RSxtQkFBbUIsOERBQVMsU0FBUyxxREFBZ0I7QUFDckQ7QUFDQTtBQUNBLHdCQUF3QixVQUFVLElBQUk7QUFDdEMsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9zaWduTWVzc2FnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzaWduTWVzc2FnZSBhcyB2aWVtX3NpZ25NZXNzYWdlLCB9IGZyb20gJ3ZpZW0vYWN0aW9ucyc7XG5pbXBvcnQgeyBnZXRBY3Rpb24gfSBmcm9tICcuLi91dGlscy9nZXRBY3Rpb24uanMnO1xuaW1wb3J0IHsgZ2V0Q29ubmVjdG9yQ2xpZW50LCB9IGZyb20gJy4vZ2V0Q29ubmVjdG9yQ2xpZW50LmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvc2lnbk1lc3NhZ2UgKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzaWduTWVzc2FnZShjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGFjY291bnQsIGNvbm5lY3RvciwgLi4ucmVzdCB9ID0gcGFyYW1ldGVycztcbiAgICBsZXQgY2xpZW50O1xuICAgIGlmICh0eXBlb2YgYWNjb3VudCA9PT0gJ29iamVjdCcgJiYgYWNjb3VudC50eXBlID09PSAnbG9jYWwnKVxuICAgICAgICBjbGllbnQgPSBjb25maWcuZ2V0Q2xpZW50KCk7XG4gICAgZWxzZVxuICAgICAgICBjbGllbnQgPSBhd2FpdCBnZXRDb25uZWN0b3JDbGllbnQoY29uZmlnLCB7IGFjY291bnQsIGNvbm5lY3RvciB9KTtcbiAgICBjb25zdCBhY3Rpb24gPSBnZXRBY3Rpb24oY2xpZW50LCB2aWVtX3NpZ25NZXNzYWdlLCAnc2lnbk1lc3NhZ2UnKTtcbiAgICByZXR1cm4gYWN0aW9uKHtcbiAgICAgICAgLi4ucmVzdCxcbiAgICAgICAgLi4uKGFjY291bnQgPyB7IGFjY291bnQgfSA6IHt9KSxcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNpZ25NZXNzYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/signMessage.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/switchChain.js":
/*!*******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/switchChain.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   switchChain: () => (/* binding */ switchChain)\n/* harmony export */ });\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n\n\n/** https://wagmi.sh/core/api/actions/switchChain */\nasync function switchChain(config, parameters) {\n    const { addEthereumChainParameter, chainId } = parameters;\n    const connection = config.state.connections.get(parameters.connector?.uid ?? config.state.current);\n    if (connection) {\n        const connector = connection.connector;\n        if (!connector.switchChain)\n            throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_0__.SwitchChainNotSupportedError({ connector });\n        const chain = await connector.switchChain({\n            addEthereumChainParameter,\n            chainId,\n        });\n        return chain;\n    }\n    const chain = config.chains.find((x) => x.id === chainId);\n    if (!chain)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_1__.ChainNotConfiguredError();\n    config.setState((x) => ({ ...x, chainId }));\n    return chain;\n}\n//# sourceMappingURL=switchChain.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvc3dpdGNoQ2hhaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStEO0FBQ1E7QUFDdkU7QUFDTztBQUNQLFlBQVkscUNBQXFDO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDhFQUE0QixHQUFHLFdBQVc7QUFDaEU7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHNFQUF1QjtBQUN6Qyw4QkFBOEIsZUFBZTtBQUM3QztBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9zd2l0Y2hDaGFpbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDaGFpbk5vdENvbmZpZ3VyZWRFcnJvciwgfSBmcm9tICcuLi9lcnJvcnMvY29uZmlnLmpzJztcbmltcG9ydCB7IFN3aXRjaENoYWluTm90U3VwcG9ydGVkRXJyb3IsIH0gZnJvbSAnLi4vZXJyb3JzL2Nvbm5lY3Rvci5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3N3aXRjaENoYWluICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc3dpdGNoQ2hhaW4oY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLCBjaGFpbklkIH0gPSBwYXJhbWV0ZXJzO1xuICAgIGNvbnN0IGNvbm5lY3Rpb24gPSBjb25maWcuc3RhdGUuY29ubmVjdGlvbnMuZ2V0KHBhcmFtZXRlcnMuY29ubmVjdG9yPy51aWQgPz8gY29uZmlnLnN0YXRlLmN1cnJlbnQpO1xuICAgIGlmIChjb25uZWN0aW9uKSB7XG4gICAgICAgIGNvbnN0IGNvbm5lY3RvciA9IGNvbm5lY3Rpb24uY29ubmVjdG9yO1xuICAgICAgICBpZiAoIWNvbm5lY3Rvci5zd2l0Y2hDaGFpbilcbiAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbk5vdFN1cHBvcnRlZEVycm9yKHsgY29ubmVjdG9yIH0pO1xuICAgICAgICBjb25zdCBjaGFpbiA9IGF3YWl0IGNvbm5lY3Rvci5zd2l0Y2hDaGFpbih7XG4gICAgICAgICAgICBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLFxuICAgICAgICAgICAgY2hhaW5JZCxcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBjaGFpbjtcbiAgICB9XG4gICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKHgpID0+IHguaWQgPT09IGNoYWluSWQpO1xuICAgIGlmICghY2hhaW4pXG4gICAgICAgIHRocm93IG5ldyBDaGFpbk5vdENvbmZpZ3VyZWRFcnJvcigpO1xuICAgIGNvbmZpZy5zZXRTdGF0ZSgoeCkgPT4gKHsgLi4ueCwgY2hhaW5JZCB9KSk7XG4gICAgcmV0dXJuIGNoYWluO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3dpdGNoQ2hhaW4uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/switchChain.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   waitForTransactionReceipt: () => (/* binding */ waitForTransactionReceipt)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/public/waitForTransactionReceipt.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/public/getTransaction.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n\nasync function waitForTransactionReceipt(config, parameters) {\n    const { chainId, timeout = 0, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.waitForTransactionReceipt, 'waitForTransactionReceipt');\n    const receipt = await action({ ...rest, timeout });\n    if (receipt.status === 'reverted') {\n        const action_getTransaction = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.getTransaction, 'getTransaction');\n        const txn = await action_getTransaction({ hash: receipt.transactionHash });\n        const action_call = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.call, 'call');\n        const code = await action_call({\n            ...txn,\n            data: txn.input,\n            gasPrice: txn.type !== 'eip1559' ? txn.gasPrice : undefined,\n            maxFeePerGas: txn.type === 'eip1559' ? txn.maxFeePerGas : undefined,\n            maxPriorityFeePerGas: txn.type === 'eip1559' ? txn.maxPriorityFeePerGas : undefined,\n        });\n        const reason = code?.data\n            ? (0,viem__WEBPACK_IMPORTED_MODULE_4__.hexToString)(`0x${code.data.substring(138)}`)\n            : 'unknown reason';\n        throw new Error(reason);\n    }\n    return {\n        ...receipt,\n        chainId: client.chain.id,\n    };\n}\n//# sourceMappingURL=waitForTransactionReceipt.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchAccount.js":
/*!********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/watchAccount.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchAccount: () => (/* binding */ watchAccount)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var _getAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAccount.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getAccount.js\");\n\n\n/** https://wagmi.sh/core/api/actions/watchAccount */\nfunction watchAccount(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getAccount_js__WEBPACK_IMPORTED_MODULE_0__.getAccount)(config), onChange, {\n        equalityFn(a, b) {\n            const { connector: aConnector, ...aRest } = a;\n            const { connector: bConnector, ...bRest } = b;\n            return ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__.deepEqual)(aRest, bRest) &&\n                // check connector separately\n                aConnector?.id === bConnector?.id &&\n                aConnector?.uid === bConnector?.uid);\n        },\n    });\n}\n//# sourceMappingURL=watchAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd2F0Y2hBY2NvdW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNMO0FBQzdDO0FBQ087QUFDUCxZQUFZLFdBQVc7QUFDdkIsa0NBQWtDLDBEQUFVO0FBQzVDO0FBQ0Esb0JBQW9CLGtDQUFrQztBQUN0RCxvQkFBb0Isa0NBQWtDO0FBQ3RELG9CQUFvQiw4REFBUztBQUM3QjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaEFjY291bnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVlcEVxdWFsIH0gZnJvbSAnLi4vdXRpbHMvZGVlcEVxdWFsLmpzJztcbmltcG9ydCB7IGdldEFjY291bnQgfSBmcm9tICcuL2dldEFjY291bnQuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy93YXRjaEFjY291bnQgKi9cbmV4cG9ydCBmdW5jdGlvbiB3YXRjaEFjY291bnQoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBvbkNoYW5nZSB9ID0gcGFyYW1ldGVycztcbiAgICByZXR1cm4gY29uZmlnLnN1YnNjcmliZSgoKSA9PiBnZXRBY2NvdW50KGNvbmZpZyksIG9uQ2hhbmdlLCB7XG4gICAgICAgIGVxdWFsaXR5Rm4oYSwgYikge1xuICAgICAgICAgICAgY29uc3QgeyBjb25uZWN0b3I6IGFDb25uZWN0b3IsIC4uLmFSZXN0IH0gPSBhO1xuICAgICAgICAgICAgY29uc3QgeyBjb25uZWN0b3I6IGJDb25uZWN0b3IsIC4uLmJSZXN0IH0gPSBiO1xuICAgICAgICAgICAgcmV0dXJuIChkZWVwRXF1YWwoYVJlc3QsIGJSZXN0KSAmJlxuICAgICAgICAgICAgICAgIC8vIGNoZWNrIGNvbm5lY3RvciBzZXBhcmF0ZWx5XG4gICAgICAgICAgICAgICAgYUNvbm5lY3Rvcj8uaWQgPT09IGJDb25uZWN0b3I/LmlkICYmXG4gICAgICAgICAgICAgICAgYUNvbm5lY3Rvcj8udWlkID09PSBiQ29ubmVjdG9yPy51aWQpO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2F0Y2hBY2NvdW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchAccount.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchChainId.js":
/*!********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/watchChainId.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchChainId: () => (/* binding */ watchChainId)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/watchChainId */\nfunction watchChainId(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe((state) => state.chainId, onChange);\n}\n//# sourceMappingURL=watchChainId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd2F0Y2hDaGFpbklkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1AsWUFBWSxXQUFXO0FBQ3ZCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3dhdGNoQ2hhaW5JZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3dhdGNoQ2hhaW5JZCAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdhdGNoQ2hhaW5JZChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IG9uQ2hhbmdlIH0gPSBwYXJhbWV0ZXJzO1xuICAgIHJldHVybiBjb25maWcuc3Vic2NyaWJlKChzdGF0ZSkgPT4gc3RhdGUuY2hhaW5JZCwgb25DaGFuZ2UpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2F0Y2hDaGFpbklkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchChainId.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchChains.js":
/*!*******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/watchChains.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchChains: () => (/* binding */ watchChains)\n/* harmony export */ });\n/**\n * @internal\n * We don't expose this because as far as consumers know, you can't chainge (lol) `config.chains` at runtime.\n * Setting `config.chains` via `config._internal.chains.setState(...)` is an extremely advanced use case that's not worth documenting or supporting in the public API at this time.\n */\nfunction watchChains(config, parameters) {\n    const { onChange } = parameters;\n    return config._internal.chains.subscribe((chains, prevChains) => {\n        onChange(chains, prevChains);\n    });\n}\n//# sourceMappingURL=watchChains.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd2F0Y2hDaGFpbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFlBQVksV0FBVztBQUN2QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENoYWlucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbnRlcm5hbFxuICogV2UgZG9uJ3QgZXhwb3NlIHRoaXMgYmVjYXVzZSBhcyBmYXIgYXMgY29uc3VtZXJzIGtub3csIHlvdSBjYW4ndCBjaGFpbmdlIChsb2wpIGBjb25maWcuY2hhaW5zYCBhdCBydW50aW1lLlxuICogU2V0dGluZyBgY29uZmlnLmNoYWluc2AgdmlhIGBjb25maWcuX2ludGVybmFsLmNoYWlucy5zZXRTdGF0ZSguLi4pYCBpcyBhbiBleHRyZW1lbHkgYWR2YW5jZWQgdXNlIGNhc2UgdGhhdCdzIG5vdCB3b3J0aCBkb2N1bWVudGluZyBvciBzdXBwb3J0aW5nIGluIHRoZSBwdWJsaWMgQVBJIGF0IHRoaXMgdGltZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdhdGNoQ2hhaW5zKGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgb25DaGFuZ2UgfSA9IHBhcmFtZXRlcnM7XG4gICAgcmV0dXJuIGNvbmZpZy5faW50ZXJuYWwuY2hhaW5zLnN1YnNjcmliZSgoY2hhaW5zLCBwcmV2Q2hhaW5zKSA9PiB7XG4gICAgICAgIG9uQ2hhbmdlKGNoYWlucywgcHJldkNoYWlucyk7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXRjaENoYWlucy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchChains.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchConnections.js":
/*!************************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/watchConnections.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchConnections: () => (/* binding */ watchConnections)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var _getConnections_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnections.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnections.js\");\n\n\n/** https://wagmi.sh/core/api/actions/watchConnections */\nfunction watchConnections(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getConnections_js__WEBPACK_IMPORTED_MODULE_0__.getConnections)(config), onChange, {\n        equalityFn: _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__.deepEqual,\n    });\n}\n//# sourceMappingURL=watchConnections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd2F0Y2hDb25uZWN0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDSTtBQUN0RDtBQUNPO0FBQ1AsWUFBWSxXQUFXO0FBQ3ZCLGtDQUFrQyxrRUFBYztBQUNoRCxvQkFBb0IsMERBQVM7QUFDN0IsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENvbm5lY3Rpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlZXBFcXVhbCB9IGZyb20gJy4uL3V0aWxzL2RlZXBFcXVhbC5qcyc7XG5pbXBvcnQgeyBnZXRDb25uZWN0aW9ucywgfSBmcm9tICcuL2dldENvbm5lY3Rpb25zLmpzJztcbi8qKiBodHRwczovL3dhZ21pLnNoL2NvcmUvYXBpL2FjdGlvbnMvd2F0Y2hDb25uZWN0aW9ucyAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdhdGNoQ29ubmVjdGlvbnMoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBvbkNoYW5nZSB9ID0gcGFyYW1ldGVycztcbiAgICByZXR1cm4gY29uZmlnLnN1YnNjcmliZSgoKSA9PiBnZXRDb25uZWN0aW9ucyhjb25maWcpLCBvbkNoYW5nZSwge1xuICAgICAgICBlcXVhbGl0eUZuOiBkZWVwRXF1YWwsXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXRjaENvbm5lY3Rpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchConnections.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js":
/*!***********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchConnectors: () => (/* binding */ watchConnectors)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/watchConnectors */\nfunction watchConnectors(config, parameters) {\n    const { onChange } = parameters;\n    return config._internal.connectors.subscribe((connectors, prevConnectors) => {\n        onChange(Object.values(connectors), prevConnectors);\n    });\n}\n//# sourceMappingURL=watchConnectors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd2F0Y2hDb25uZWN0b3JzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1AsWUFBWSxXQUFXO0FBQ3ZCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3dhdGNoQ29ubmVjdG9ycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3dhdGNoQ29ubmVjdG9ycyAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdhdGNoQ29ubmVjdG9ycyhjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IG9uQ2hhbmdlIH0gPSBwYXJhbWV0ZXJzO1xuICAgIHJldHVybiBjb25maWcuX2ludGVybmFsLmNvbm5lY3RvcnMuc3Vic2NyaWJlKChjb25uZWN0b3JzLCBwcmV2Q29ubmVjdG9ycykgPT4ge1xuICAgICAgICBvbkNoYW5nZShPYmplY3QudmFsdWVzKGNvbm5lY3RvcnMpLCBwcmV2Q29ubmVjdG9ycyk7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXRjaENvbm5lY3RvcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchPublicClient.js":
/*!*************************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/watchPublicClient.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchPublicClient: () => (/* binding */ watchPublicClient)\n/* harmony export */ });\n/* harmony import */ var _getPublicClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getPublicClient.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getPublicClient.js\");\n\n/** https://wagmi.sh/core/api/actions/watchPublicClient */\nfunction watchPublicClient(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getPublicClient_js__WEBPACK_IMPORTED_MODULE_0__.getPublicClient)(config), onChange, {\n        equalityFn(a, b) {\n            return a?.uid === b?.uid;\n        },\n    });\n}\n//# sourceMappingURL=watchPublicClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd2F0Y2hQdWJsaWNDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0Q7QUFDeEQ7QUFDTztBQUNQLFlBQVksV0FBVztBQUN2QixrQ0FBa0Msb0VBQWU7QUFDakQ7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3dhdGNoUHVibGljQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldFB1YmxpY0NsaWVudCwgfSBmcm9tICcuL2dldFB1YmxpY0NsaWVudC5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3dhdGNoUHVibGljQ2xpZW50ICovXG5leHBvcnQgZnVuY3Rpb24gd2F0Y2hQdWJsaWNDbGllbnQoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBvbkNoYW5nZSB9ID0gcGFyYW1ldGVycztcbiAgICByZXR1cm4gY29uZmlnLnN1YnNjcmliZSgoKSA9PiBnZXRQdWJsaWNDbGllbnQoY29uZmlnKSwgb25DaGFuZ2UsIHtcbiAgICAgICAgZXF1YWxpdHlGbihhLCBiKSB7XG4gICAgICAgICAgICByZXR1cm4gYT8udWlkID09PSBiPy51aWQ7XG4gICAgICAgIH0sXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXRjaFB1YmxpY0NsaWVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchPublicClient.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/actions/writeContract.js":
/*!*********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/actions/writeContract.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeContract: () => (/* binding */ writeContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/../node_modules/viem/_esm/actions/wallet/writeContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/writeContract */\nasync function writeContract(config, parameters) {\n    const { account, chainId, connector, ...request } = parameters;\n    let client;\n    if (typeof account === 'object' && account?.type === 'local')\n        client = config.getClient({ chainId });\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: account ?? undefined,\n            chainId,\n            connector,\n        });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.writeContract, 'writeContract');\n    const hash = await action({\n        ...request,\n        ...(account ? { account } : {}),\n        chain: chainId ? { id: chainId } : null,\n    });\n    return hash;\n}\n//# sourceMappingURL=writeContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd3JpdGVDb250cmFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9FO0FBQ2xCO0FBQ1k7QUFDOUQ7QUFDTztBQUNQLFlBQVksMENBQTBDO0FBQ3REO0FBQ0E7QUFDQSxvQ0FBb0MsU0FBUztBQUM3QztBQUNBLHVCQUF1QiwwRUFBa0I7QUFDekM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULG1CQUFtQiw4REFBUyxTQUFTLHVEQUFrQjtBQUN2RDtBQUNBO0FBQ0Esd0JBQXdCLFVBQVUsSUFBSTtBQUN0QywyQkFBMkIsY0FBYztBQUN6QyxLQUFLO0FBQ0w7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd3JpdGVDb250cmFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB3cml0ZUNvbnRyYWN0IGFzIHZpZW1fd3JpdGVDb250cmFjdCwgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbmltcG9ydCB7IGdldENvbm5lY3RvckNsaWVudCwgfSBmcm9tICcuL2dldENvbm5lY3RvckNsaWVudC5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3dyaXRlQ29udHJhY3QgKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB3cml0ZUNvbnRyYWN0KGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgYWNjb3VudCwgY2hhaW5JZCwgY29ubmVjdG9yLCAuLi5yZXF1ZXN0IH0gPSBwYXJhbWV0ZXJzO1xuICAgIGxldCBjbGllbnQ7XG4gICAgaWYgKHR5cGVvZiBhY2NvdW50ID09PSAnb2JqZWN0JyAmJiBhY2NvdW50Py50eXBlID09PSAnbG9jYWwnKVxuICAgICAgICBjbGllbnQgPSBjb25maWcuZ2V0Q2xpZW50KHsgY2hhaW5JZCB9KTtcbiAgICBlbHNlXG4gICAgICAgIGNsaWVudCA9IGF3YWl0IGdldENvbm5lY3RvckNsaWVudChjb25maWcsIHtcbiAgICAgICAgICAgIGFjY291bnQ6IGFjY291bnQgPz8gdW5kZWZpbmVkLFxuICAgICAgICAgICAgY2hhaW5JZCxcbiAgICAgICAgICAgIGNvbm5lY3RvcixcbiAgICAgICAgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV93cml0ZUNvbnRyYWN0LCAnd3JpdGVDb250cmFjdCcpO1xuICAgIGNvbnN0IGhhc2ggPSBhd2FpdCBhY3Rpb24oe1xuICAgICAgICAuLi5yZXF1ZXN0LFxuICAgICAgICAuLi4oYWNjb3VudCA/IHsgYWNjb3VudCB9IDoge30pLFxuICAgICAgICBjaGFpbjogY2hhaW5JZCA/IHsgaWQ6IGNoYWluSWQgfSA6IG51bGwsXG4gICAgfSk7XG4gICAgcmV0dXJuIGhhc2g7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13cml0ZUNvbnRyYWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/actions/writeContract.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/createConnector.js":
/*!**************************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/connectors/createConnector.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConnector: () => (/* binding */ createConnector)\n/* harmony export */ });\nfunction createConnector(createConnectorFn) {\n    return createConnectorFn;\n}\n//# sourceMappingURL=createConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2Nvbm5lY3RvcnMvY3JlYXRlQ29ubmVjdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9jb25uZWN0b3JzL2NyZWF0ZUNvbm5lY3Rvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gY3JlYXRlQ29ubmVjdG9yKGNyZWF0ZUNvbm5lY3RvckZuKSB7XG4gICAgcmV0dXJuIGNyZWF0ZUNvbm5lY3RvckZuO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3JlYXRlQ29ubmVjdG9yLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/injected.js":
/*!*******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/connectors/injected.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   injected: () => (/* binding */ injected)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\ninjected.type = 'injected';\nfunction injected(parameters = {}) {\n    const { shimDisconnect = true, unstable_shimAsyncInject } = parameters;\n    function getTarget() {\n        const target = parameters.target;\n        if (typeof target === 'function') {\n            const result = target();\n            if (result)\n                return result;\n        }\n        if (typeof target === 'object')\n            return target;\n        if (typeof target === 'string')\n            return {\n                ...(targetMap[target] ?? {\n                    id: target,\n                    name: `${target[0].toUpperCase()}${target.slice(1)}`,\n                    provider: `is${target[0].toUpperCase()}${target.slice(1)}`,\n                }),\n            };\n        return {\n            id: 'injected',\n            name: 'Injected',\n            provider(window) {\n                return window?.ethereum;\n            },\n        };\n    }\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let disconnect;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        get icon() {\n            return getTarget().icon;\n        },\n        get id() {\n            return getTarget().id;\n        },\n        get name() {\n            return getTarget().name;\n        },\n        /** @deprecated */\n        get supportsSimulation() {\n            return true;\n        },\n        type: injected.type,\n        async setup() {\n            const provider = await this.getProvider();\n            // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n            if (provider?.on && parameters.target) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            else if (shimDisconnect) {\n                // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n                try {\n                    const permissions = await provider.request({\n                        method: 'wallet_requestPermissions',\n                        params: [{ eth_accounts: {} }],\n                    });\n                    accounts = permissions[0]?.caveats?.[0]?.value?.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                    // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n                    // switch to `'eth_accounts'` ordering if more than one account is connected\n                    // https://github.com/wevm/wagmi/issues/4140\n                    if (accounts.length > 0) {\n                        const sortedAccounts = await this.getAccounts();\n                        accounts = sortedAccounts;\n                    }\n                }\n                catch (err) {\n                    const error = err;\n                    // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n                    // Only bubble up error if user rejects request\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    // Or prompt is already open\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                        throw error;\n                }\n            }\n            try {\n                if (!accounts?.length && !isReconnecting) {\n                    const requestedAccounts = await provider.request({\n                        method: 'eth_requestAccounts',\n                    });\n                    accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                }\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n                // Add connected shim if no target exists\n                if (!parameters.target)\n                    await config.storage?.setItem('injected.connected', true);\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            // Experimental support for MetaMask disconnect\n            // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n            try {\n                // Adding timeout as not all wallets support this method and can hang\n                // https://github.com/wevm/wagmi/issues/4064\n                await (0,viem__WEBPACK_IMPORTED_MODULE_4__.withTimeout)(() => \n                // TODO: Remove explicit type for viem@3\n                provider.request({\n                    // `'wallet_revokePermissions'` added in `viem@2.10.3`\n                    method: 'wallet_revokePermissions',\n                    params: [{ eth_accounts: {} }],\n                }), { timeout: 100 });\n            }\n            catch { }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect) {\n                await config.storage?.setItem(`${this.id}.disconnected`, true);\n            }\n            if (!parameters.target)\n                await config.storage?.removeItem('injected.connected');\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return Number(hexChainId);\n        },\n        async getProvider() {\n            if (typeof window === 'undefined')\n                return undefined;\n            let provider;\n            const target = getTarget();\n            if (typeof target.provider === 'function')\n                provider = target.provider(window);\n            else if (typeof target.provider === 'string')\n                provider = findProvider(window, target.provider);\n            else\n                provider = target.provider;\n            // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n            // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n            if (provider && !provider.removeListener) {\n                // Try using `off` handler if it exists, otherwise noop\n                if ('off' in provider && typeof provider.off === 'function')\n                    provider.removeListener =\n                        provider.off;\n                else\n                    provider.removeListener = () => { };\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem(`${this.id}.disconnected`));\n                if (isDisconnected)\n                    return false;\n                // Don't allow injected connector to connect if no target is set and it hasn't already connected\n                // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n                // automatically whenever there is a targeted connector configured.\n                if (!parameters.target) {\n                    const connected = await config.storage?.getItem('injected.connected');\n                    if (!connected)\n                        return false;\n                }\n                const provider = await this.getProvider();\n                if (!provider) {\n                    if (unstable_shimAsyncInject !== undefined &&\n                        unstable_shimAsyncInject !== false) {\n                        // If no provider is found, check for async injection\n                        // https://github.com/wevm/references/issues/167\n                        // https://github.com/MetaMask/detect-provider\n                        const handleEthereum = async () => {\n                            if (typeof window !== 'undefined')\n                                window.removeEventListener('ethereum#initialized', handleEthereum);\n                            const provider = await this.getProvider();\n                            return !!provider;\n                        };\n                        const timeout = typeof unstable_shimAsyncInject === 'number'\n                            ? unstable_shimAsyncInject\n                            : 1_000;\n                        const res = await Promise.race([\n                            ...(typeof window !== 'undefined'\n                                ? [\n                                    new Promise((resolve) => window.addEventListener('ethereum#initialized', () => resolve(handleEthereum()), { once: true })),\n                                ]\n                                : []),\n                            new Promise((resolve) => setTimeout(() => resolve(handleEthereum()), timeout)),\n                        ]);\n                        if (res)\n                            return true;\n                    }\n                    throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                }\n                // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n                // immediately resolve JSON-RPC requests on page load.\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_5__.withRetry)(() => this.getAccounts());\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError());\n            const promise = new Promise((resolve) => {\n                const listener = ((data) => {\n                    if ('chainId' in data && data.chainId === chainId) {\n                        config.emitter.off('change', listener);\n                        resolve();\n                    }\n                });\n                config.emitter.on('change', listener);\n            });\n            try {\n                await Promise.all([\n                    provider\n                        .request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId) }],\n                    })\n                        // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                        // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                        // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                        // this callback or an externally emitted `'chainChanged'` event.\n                        // https://github.com/MetaMask/metamask-extension/issues/24247\n                        .then(async () => {\n                        const currentChainId = await this.getChainId();\n                        if (currentChainId === chainId)\n                            config.emitter.emit('change', { chainId });\n                    }),\n                    promise,\n                ]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else if (blockExplorer)\n                            blockExplorerUrls = [\n                                blockExplorer.url,\n                                ...Object.values(blockExplorers).map((x) => x.url),\n                            ];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await Promise.all([\n                            provider\n                                .request({\n                                method: 'wallet_addEthereumChain',\n                                params: [addEthereumChain],\n                            })\n                                .then(async () => {\n                                const currentChainId = await this.getChainId();\n                                if (currentChainId === chainId)\n                                    config.emitter.emit('change', { chainId });\n                                else\n                                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(new Error('User rejected switch after adding network.'));\n                            }),\n                            promise,\n                        ]);\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    }\n                }\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(error);\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0)\n                this.onDisconnect();\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            // Manage EIP-1193 event listeners\n            const provider = await this.getProvider();\n            if (provider) {\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n            // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n            // actually disconnected and we don't need to simulate it.\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (provider) {\n                if (chainChanged) {\n                    provider.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n            }\n        },\n    }));\n}\nconst targetMap = {\n    coinbaseWallet: {\n        id: 'coinbaseWallet',\n        name: 'Coinbase Wallet',\n        provider(window) {\n            if (window?.coinbaseWalletExtension)\n                return window.coinbaseWalletExtension;\n            return findProvider(window, 'isCoinbaseWallet');\n        },\n    },\n    metaMask: {\n        id: 'metaMask',\n        name: 'MetaMask',\n        provider(window) {\n            return findProvider(window, (provider) => {\n                if (!provider.isMetaMask)\n                    return false;\n                // Brave tries to make itself look like MetaMask\n                // Could also try RPC `web3_clientVersion` if following is unreliable\n                if (provider.isBraveWallet && !provider._events && !provider._state)\n                    return false;\n                // Other wallets that try to look like MetaMask\n                const flags = [\n                    'isApexWallet',\n                    'isAvalanche',\n                    'isBitKeep',\n                    'isBlockWallet',\n                    'isKuCoinWallet',\n                    'isMathWallet',\n                    'isOkxWallet',\n                    'isOKExWallet',\n                    'isOneInchIOSWallet',\n                    'isOneInchAndroidWallet',\n                    'isOpera',\n                    'isPhantom',\n                    'isPortal',\n                    'isRabby',\n                    'isTokenPocket',\n                    'isTokenary',\n                    'isUniswapWallet',\n                    'isZerion',\n                ];\n                for (const flag of flags)\n                    if (provider[flag])\n                        return false;\n                return true;\n            });\n        },\n    },\n    phantom: {\n        id: 'phantom',\n        name: 'Phantom',\n        provider(window) {\n            if (window?.phantom?.ethereum)\n                return window.phantom?.ethereum;\n            return findProvider(window, 'isPhantom');\n        },\n    },\n};\nfunction findProvider(window, select) {\n    function isProvider(provider) {\n        if (typeof select === 'function')\n            return select(provider);\n        if (typeof select === 'string')\n            return provider[select];\n        return true;\n    }\n    const ethereum = window.ethereum;\n    if (ethereum?.providers)\n        return ethereum.providers.find((provider) => isProvider(provider));\n    if (ethereum && isProvider(ethereum))\n        return ethereum;\n    return undefined;\n}\n//# sourceMappingURL=injected.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/injected.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/createConfig.js":
/*!************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/createConfig.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfig: () => (/* binding */ createConfig)\n/* harmony export */ });\n/* harmony import */ var mipd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mipd */ \"(ssr)/../node_modules/mipd/dist/esm/store.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/../node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/../node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var _connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./connectors/injected.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _createEmitter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createEmitter.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/createEmitter.js\");\n/* harmony import */ var _createStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createStorage.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors/config.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/uid.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/uid.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./version.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/version.js\");\n\n\n\n\n\n\n\n\n\n\nfunction createConfig(parameters) {\n    const { multiInjectedProviderDiscovery = true, storage = (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.createStorage)({\n        storage: (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultStorage)(),\n    }), syncConnectedChain = true, ssr = false, ...rest } = parameters;\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Set up connectors, clients, etc.\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    const mipd = typeof window !== 'undefined' && multiInjectedProviderDiscovery\n        ? (0,mipd__WEBPACK_IMPORTED_MODULE_1__.createStore)()\n        : undefined;\n    const chains = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => rest.chains);\n    const connectors = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => {\n        const collection = [];\n        const rdnsSet = new Set();\n        for (const connectorFns of rest.connectors ?? []) {\n            const connector = setup(connectorFns);\n            collection.push(connector);\n            if (!ssr && connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    rdnsSet.add(rdns);\n                }\n            }\n        }\n        if (!ssr && mipd) {\n            const providers = mipd.getProviders();\n            for (const provider of providers) {\n                if (rdnsSet.has(provider.info.rdns))\n                    continue;\n                collection.push(setup(providerDetailToConnector(provider)));\n            }\n        }\n        return collection;\n    });\n    function setup(connectorFn) {\n        // Set up emitter with uid and add to connector so they are \"linked\" together.\n        const emitter = (0,_createEmitter_js__WEBPACK_IMPORTED_MODULE_3__.createEmitter)((0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_4__.uid)());\n        const connector = {\n            ...connectorFn({\n                emitter,\n                chains: chains.getState(),\n                storage,\n                transports: rest.transports,\n            }),\n            emitter,\n            uid: emitter.uid,\n        };\n        // Start listening for `connect` events on connector setup\n        // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n        emitter.on('connect', connect);\n        connector.setup?.();\n        return connector;\n    }\n    function providerDetailToConnector(providerDetail) {\n        const { info } = providerDetail;\n        const provider = providerDetail.provider;\n        return (0,_connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__.injected)({ target: { ...info, id: info.rdns, provider } });\n    }\n    const clients = new Map();\n    function getClient(config = {}) {\n        const chainId = config.chainId ?? store.getState().chainId;\n        const chain = chains.getState().find((x) => x.id === chainId);\n        // chainId specified and not configured\n        if (config.chainId && !chain)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        {\n            const client = clients.get(store.getState().chainId);\n            if (client && !chain)\n                return client;\n            if (!chain)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        }\n        // If a memoized client exists for a chain id, use that.\n        {\n            const client = clients.get(chainId);\n            if (client)\n                return client;\n        }\n        let client;\n        if (rest.client)\n            client = rest.client({ chain });\n        else {\n            const chainId = chain.id;\n            const chainIds = chains.getState().map((x) => x.id);\n            // Grab all properties off `rest` and resolve for use in `createClient`\n            const properties = {};\n            const entries = Object.entries(rest);\n            for (const [key, value] of entries) {\n                if (key === 'chains' ||\n                    key === 'client' ||\n                    key === 'connectors' ||\n                    key === 'transports')\n                    continue;\n                if (typeof value === 'object') {\n                    // check if value is chainId-specific since some values can be objects\n                    // e.g. { batch: { multicall: { batchSize: 1024 } } }\n                    if (chainId in value)\n                        properties[key] = value[chainId];\n                    else {\n                        // check if value is chainId-specific, but does not have value for current chainId\n                        const hasChainSpecificValue = chainIds.some((x) => x in value);\n                        if (hasChainSpecificValue)\n                            continue;\n                        properties[key] = value;\n                    }\n                }\n                else\n                    properties[key] = value;\n            }\n            client = (0,viem__WEBPACK_IMPORTED_MODULE_7__.createClient)({\n                ...properties,\n                chain,\n                batch: properties.batch ?? { multicall: true },\n                transport: (parameters) => rest.transports[chainId]({ ...parameters, connectors }),\n            });\n        }\n        clients.set(chainId, client);\n        return client;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Create store\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function getInitialState() {\n        return {\n            chainId: chains.getState()[0].id,\n            connections: new Map(),\n            current: null,\n            status: 'disconnected',\n        };\n    }\n    let currentVersion;\n    const prefix = '0.0.0-canary-';\n    if (_version_js__WEBPACK_IMPORTED_MODULE_8__.version.startsWith(prefix))\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.replace(prefix, ''));\n    // use package major version to version store\n    else\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.split('.')[0] ?? '0');\n    const store = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.subscribeWithSelector)(\n    // only use persist middleware if storage exists\n    storage\n        ? (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.persist)(getInitialState, {\n            migrate(persistedState, version) {\n                if (version === currentVersion)\n                    return persistedState;\n                const initialState = getInitialState();\n                const chainId = validatePersistedChainId(persistedState, initialState.chainId);\n                return { ...initialState, chainId };\n            },\n            name: 'store',\n            partialize(state) {\n                // Only persist \"critical\" store properties to preserve storage size.\n                return {\n                    connections: {\n                        __type: 'Map',\n                        value: Array.from(state.connections.entries()).map(([key, connection]) => {\n                            const { id, name, type, uid } = connection.connector;\n                            const connector = { id, name, type, uid };\n                            return [key, { ...connection, connector }];\n                        }),\n                    },\n                    chainId: state.chainId,\n                    current: state.current,\n                };\n            },\n            merge(persistedState, currentState) {\n                // `status` should not be persisted as it messes with reconnection\n                if (typeof persistedState === 'object' &&\n                    persistedState &&\n                    'status' in persistedState)\n                    delete persistedState.status;\n                // Make sure persisted `chainId` is valid\n                const chainId = validatePersistedChainId(persistedState, currentState.chainId);\n                return {\n                    ...currentState,\n                    ...persistedState,\n                    chainId,\n                };\n            },\n            skipHydration: ssr,\n            storage: storage,\n            version: currentVersion,\n        })\n        : getInitialState));\n    store.setState(getInitialState());\n    function validatePersistedChainId(persistedState, defaultChainId) {\n        return persistedState &&\n            typeof persistedState === 'object' &&\n            'chainId' in persistedState &&\n            typeof persistedState.chainId === 'number' &&\n            chains.getState().some((x) => x.id === persistedState.chainId)\n            ? persistedState.chainId\n            : defaultChainId;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Subscribe to changes\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Update default chain when connector chain changes\n    if (syncConnectedChain)\n        store.subscribe(({ connections, current }) => current ? connections.get(current)?.chainId : undefined, (chainId) => {\n            // If chain is not configured, then don't switch over to it.\n            const isChainConfigured = chains\n                .getState()\n                .some((x) => x.id === chainId);\n            if (!isChainConfigured)\n                return;\n            return store.setState((x) => ({\n                ...x,\n                chainId: chainId ?? x.chainId,\n            }));\n        });\n    // EIP-6963 subscribe for new wallet providers\n    mipd?.subscribe((providerDetails) => {\n        const connectorIdSet = new Set();\n        const connectorRdnsSet = new Set();\n        for (const connector of connectors.getState()) {\n            connectorIdSet.add(connector.id);\n            if (connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    connectorRdnsSet.add(rdns);\n                }\n            }\n        }\n        const newConnectors = [];\n        for (const providerDetail of providerDetails) {\n            if (connectorRdnsSet.has(providerDetail.info.rdns))\n                continue;\n            const connector = setup(providerDetailToConnector(providerDetail));\n            if (connectorIdSet.has(connector.id))\n                continue;\n            newConnectors.push(connector);\n        }\n        if (storage && !store.persist.hasHydrated())\n            return;\n        connectors.setState((x) => [...x, ...newConnectors], true);\n    });\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Emitter listeners\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function change(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (!connection)\n                return x;\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts ??\n                        connection.accounts,\n                    chainId: data.chainId ?? connection.chainId,\n                    connector: connection.connector,\n                }),\n            };\n        });\n    }\n    function connect(data) {\n        // Disable handling if reconnecting/connecting\n        if (store.getState().status === 'connecting' ||\n            store.getState().status === 'reconnecting')\n            return;\n        store.setState((x) => {\n            const connector = connectors.getState().find((x) => x.uid === data.uid);\n            if (!connector)\n                return x;\n            if (connector.emitter.listenerCount('connect'))\n                connector.emitter.off('connect', change);\n            if (!connector.emitter.listenerCount('change'))\n                connector.emitter.on('change', change);\n            if (!connector.emitter.listenerCount('disconnect'))\n                connector.emitter.on('disconnect', disconnect);\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts,\n                    chainId: data.chainId,\n                    connector: connector,\n                }),\n                current: data.uid,\n                status: 'connected',\n            };\n        });\n    }\n    function disconnect(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (connection) {\n                const connector = connection.connector;\n                if (connector.emitter.listenerCount('change'))\n                    connection.connector.emitter.off('change', change);\n                if (connector.emitter.listenerCount('disconnect'))\n                    connection.connector.emitter.off('disconnect', disconnect);\n                if (!connector.emitter.listenerCount('connect'))\n                    connection.connector.emitter.on('connect', connect);\n            }\n            x.connections.delete(data.uid);\n            if (x.connections.size === 0)\n                return {\n                    ...x,\n                    connections: new Map(),\n                    current: null,\n                    status: 'disconnected',\n                };\n            const nextConnection = x.connections.values().next().value;\n            return {\n                ...x,\n                connections: new Map(x.connections),\n                current: nextConnection.connector.uid,\n            };\n        });\n    }\n    return {\n        get chains() {\n            return chains.getState();\n        },\n        get connectors() {\n            return connectors.getState();\n        },\n        storage,\n        getClient,\n        get state() {\n            return store.getState();\n        },\n        setState(value) {\n            let newState;\n            if (typeof value === 'function')\n                newState = value(store.getState());\n            else\n                newState = value;\n            // Reset state if it got set to something not matching the base state\n            const initialState = getInitialState();\n            if (typeof newState !== 'object')\n                newState = initialState;\n            const isCorrupt = Object.keys(initialState).some((x) => !(x in newState));\n            if (isCorrupt)\n                newState = initialState;\n            store.setState(newState, true);\n        },\n        subscribe(selector, listener, options) {\n            return store.subscribe(selector, listener, options\n                ? {\n                    ...options,\n                    fireImmediately: options.emitImmediately,\n                    // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n                }\n                : undefined);\n        },\n        _internal: {\n            mipd,\n            store,\n            ssr: Boolean(ssr),\n            syncConnectedChain,\n            transports: rest.transports,\n            chains: {\n                setState(value) {\n                    const nextChains = (typeof value === 'function' ? value(chains.getState()) : value);\n                    if (nextChains.length === 0)\n                        return;\n                    return chains.setState(nextChains, true);\n                },\n                subscribe(listener) {\n                    return chains.subscribe(listener);\n                },\n            },\n            connectors: {\n                providerDetailToConnector,\n                setup: setup,\n                setState(value) {\n                    return connectors.setState(typeof value === 'function' ? value(connectors.getState()) : value, true);\n                },\n                subscribe(listener) {\n                    return connectors.subscribe(listener);\n                },\n            },\n            events: { change, connect, disconnect },\n        },\n    };\n}\n//# sourceMappingURL=createConfig.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/createConfig.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/createEmitter.js":
/*!*************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/createEmitter.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter),\n/* harmony export */   createEmitter: () => (/* binding */ createEmitter)\n/* harmony export */ });\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventemitter3 */ \"(ssr)/../node_modules/eventemitter3/index.mjs\");\n\nclass Emitter {\n    constructor(uid) {\n        Object.defineProperty(this, \"uid\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: uid\n        });\n        Object.defineProperty(this, \"_emitter\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new eventemitter3__WEBPACK_IMPORTED_MODULE_0__.EventEmitter()\n        });\n    }\n    on(eventName, fn) {\n        this._emitter.on(eventName, fn);\n    }\n    once(eventName, fn) {\n        this._emitter.once(eventName, fn);\n    }\n    off(eventName, fn) {\n        this._emitter.off(eventName, fn);\n    }\n    emit(eventName, ...params) {\n        const data = params[0];\n        this._emitter.emit(eventName, { uid: this.uid, ...data });\n    }\n    listenerCount(eventName) {\n        return this._emitter.listenerCount(eventName);\n    }\n}\nfunction createEmitter(uid) {\n    return new Emitter(uid);\n}\n//# sourceMappingURL=createEmitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/createEmitter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/createStorage.js":
/*!*************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/createStorage.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   getDefaultStorage: () => (/* binding */ getDefaultStorage),\n/* harmony export */   noopStorage: () => (/* binding */ noopStorage)\n/* harmony export */ });\n/* harmony import */ var _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/deserialize.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deserialize.js\");\n/* harmony import */ var _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/serialize.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/serialize.js\");\n\n\nfunction createStorage(parameters) {\n    const { deserialize = _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize, key: prefix = 'wagmi', serialize = _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize, storage = noopStorage, } = parameters;\n    function unwrap(value) {\n        if (value instanceof Promise)\n            return value.then((x) => x).catch(() => null);\n        return value;\n    }\n    return {\n        ...storage,\n        key: prefix,\n        async getItem(key, defaultValue) {\n            const value = storage.getItem(`${prefix}.${key}`);\n            const unwrapped = await unwrap(value);\n            if (unwrapped)\n                return deserialize(unwrapped) ?? null;\n            return (defaultValue ?? null);\n        },\n        async setItem(key, value) {\n            const storageKey = `${prefix}.${key}`;\n            if (value === null)\n                await unwrap(storage.removeItem(storageKey));\n            else\n                await unwrap(storage.setItem(storageKey, serialize(value)));\n        },\n        async removeItem(key) {\n            await unwrap(storage.removeItem(`${prefix}.${key}`));\n        },\n    };\n}\nconst noopStorage = {\n    getItem: () => null,\n    setItem: () => { },\n    removeItem: () => { },\n};\nfunction getDefaultStorage() {\n    const storage = (() => {\n        if (typeof window !== 'undefined' && window.localStorage)\n            return window.localStorage;\n        return noopStorage;\n    })();\n    return {\n        getItem(key) {\n            return storage.getItem(key);\n        },\n        removeItem(key) {\n            storage.removeItem(key);\n        },\n        setItem(key, value) {\n            try {\n                storage.setItem(key, value);\n                // silence errors by default (QuotaExceededError, SecurityError, etc.)\n            }\n            catch { }\n        },\n    };\n}\n//# sourceMappingURL=createStorage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/createStorage.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/errors/base.js":
/*!***********************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/errors/base.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getVersion.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getVersion.js\");\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _BaseError_instances, _BaseError_walk;\n\nclass BaseError extends Error {\n    get docsBaseUrl() {\n        return 'https://wagmi.sh/core';\n    }\n    get version() {\n        return (0,_utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)();\n    }\n    constructor(shortMessage, options = {}) {\n        super();\n        _BaseError_instances.add(this);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiCoreError'\n        });\n        const details = options.cause instanceof BaseError\n            ? options.cause.details\n            : options.cause?.message\n                ? options.cause.message\n                : options.details;\n        const docsPath = options.cause instanceof BaseError\n            ? options.cause.docsPath || options.docsPath\n            : options.docsPath;\n        this.message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(options.metaMessages ? [...options.metaMessages, ''] : []),\n            ...(docsPath\n                ? [\n                    `Docs: ${this.docsBaseUrl}${docsPath}.html${options.docsSlug ? `#${options.docsSlug}` : ''}`,\n                ]\n                : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: ${this.version}`,\n        ].join('\\n');\n        if (options.cause)\n            this.cause = options.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = options.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, this, fn);\n    }\n}\n_BaseError_instances = new WeakSet(), _BaseError_walk = function _BaseError_walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err.cause)\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, err.cause, fn);\n    return err;\n};\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js":
/*!*************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/errors/config.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChainNotConfiguredError: () => (/* binding */ ChainNotConfiguredError),\n/* harmony export */   ConnectorAccountNotFoundError: () => (/* binding */ ConnectorAccountNotFoundError),\n/* harmony export */   ConnectorAlreadyConnectedError: () => (/* binding */ ConnectorAlreadyConnectedError),\n/* harmony export */   ConnectorChainMismatchError: () => (/* binding */ ConnectorChainMismatchError),\n/* harmony export */   ConnectorNotConnectedError: () => (/* binding */ ConnectorNotConnectedError),\n/* harmony export */   ConnectorNotFoundError: () => (/* binding */ ConnectorNotFoundError),\n/* harmony export */   ConnectorUnavailableReconnectingError: () => (/* binding */ ConnectorUnavailableReconnectingError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ChainNotConfiguredError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Chain not configured.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ChainNotConfiguredError'\n        });\n    }\n}\nclass ConnectorAlreadyConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector already connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAlreadyConnectedError'\n        });\n    }\n}\nclass ConnectorNotConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotConnectedError'\n        });\n    }\n}\nclass ConnectorNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotFoundError'\n        });\n    }\n}\nclass ConnectorAccountNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ address, connector, }) {\n        super(`Account \"${address}\" not found for connector \"${connector.name}\".`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAccountNotFoundError'\n        });\n    }\n}\nclass ConnectorChainMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connectionChainId, connectorChainId, }) {\n        super(`The current chain of the connector (id: ${connectorChainId}) does not match the connection's chain (id: ${connectionChainId}).`, {\n            metaMessages: [\n                `Current Chain ID:  ${connectorChainId}`,\n                `Expected Chain ID: ${connectionChainId}`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorChainMismatchError'\n        });\n    }\n}\nclass ConnectorUnavailableReconnectingError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`Connector \"${connector.name}\" unavailable while reconnecting.`, {\n            details: [\n                'During the reconnection step, the only connector methods guaranteed to be available are: `id`, `name`, `type`, `uid`.',\n                'All other methods are not guaranteed to be available until reconnection completes and connectors are fully restored.',\n                'This error commonly occurs for connectors that asynchronously inject after reconnection has already started.',\n            ].join(' '),\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorUnavailableReconnectingError'\n        });\n    }\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/errors/config.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/errors/connector.js":
/*!****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/errors/connector.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderNotFoundError: () => (/* binding */ ProviderNotFoundError),\n/* harmony export */   SwitchChainNotSupportedError: () => (/* binding */ SwitchChainNotSupportedError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ProviderNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Provider not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ProviderNotFoundError'\n        });\n    }\n}\nclass SwitchChainNotSupportedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`\"${connector.name}\" does not support programmatic chain switching.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SwitchChainNotSupportedError'\n        });\n    }\n}\n//# sourceMappingURL=connector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2Vycm9ycy9jb25uZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNDO0FBQy9CLG9DQUFvQywrQ0FBUztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ08sMkNBQTJDLCtDQUFTO0FBQzNELGtCQUFrQixXQUFXO0FBQzdCLGtCQUFrQixlQUFlO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2Vycm9ycy9jb25uZWN0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjbGFzcyBQcm92aWRlck5vdEZvdW5kRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcignUHJvdmlkZXIgbm90IGZvdW5kLicpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnUHJvdmlkZXJOb3RGb3VuZEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgU3dpdGNoQ2hhaW5Ob3RTdXBwb3J0ZWRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyBjb25uZWN0b3IgfSkge1xuICAgICAgICBzdXBlcihgXCIke2Nvbm5lY3Rvci5uYW1lfVwiIGRvZXMgbm90IHN1cHBvcnQgcHJvZ3JhbW1hdGljIGNoYWluIHN3aXRjaGluZy5gKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ1N3aXRjaENoYWluTm90U3VwcG9ydGVkRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbm5lY3Rvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/errors/connector.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/hydrate.js":
/*!*******************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/hydrate.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions/reconnect.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/reconnect.js\");\n\nfunction hydrate(config, parameters) {\n    const { initialState, reconnectOnMount } = parameters;\n    if (initialState && !config._internal.store.persist.hasHydrated())\n        config.setState({\n            ...initialState,\n            chainId: config.chains.some((x) => x.id === initialState.chainId)\n                ? initialState.chainId\n                : config.chains[0].id,\n            connections: reconnectOnMount ? initialState.connections : new Map(),\n            status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n        });\n    return {\n        async onMount() {\n            if (config._internal.ssr) {\n                await config._internal.store.persist.rehydrate();\n                if (config._internal.mipd) {\n                    config._internal.connectors.setState((connectors) => {\n                        const rdnsSet = new Set();\n                        for (const connector of connectors ?? []) {\n                            if (connector.rdns) {\n                                const rdnsValues = Array.isArray(connector.rdns)\n                                    ? connector.rdns\n                                    : [connector.rdns];\n                                for (const rdns of rdnsValues) {\n                                    rdnsSet.add(rdns);\n                                }\n                            }\n                        }\n                        const mipdConnectors = [];\n                        const providers = config._internal.mipd?.getProviders() ?? [];\n                        for (const provider of providers) {\n                            if (rdnsSet.has(provider.info.rdns))\n                                continue;\n                            const connectorFn = config._internal.connectors.providerDetailToConnector(provider);\n                            const connector = config._internal.connectors.setup(connectorFn);\n                            mipdConnectors.push(connector);\n                        }\n                        return [...connectors, ...mipdConnectors];\n                    });\n                }\n            }\n            if (reconnectOnMount)\n                (0,_actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__.reconnect)(config);\n            else if (config.storage)\n                // Reset connections that may have been hydrated from storage.\n                config.setState((x) => ({\n                    ...x,\n                    connections: new Map(),\n                }));\n        },\n    };\n}\n//# sourceMappingURL=hydrate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/hydrate.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/connect.js":
/*!*************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/connect.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connectMutationOptions: () => (/* binding */ connectMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_connect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/connect.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/connect.js\");\n\nfunction connectMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_connect_js__WEBPACK_IMPORTED_MODULE_0__.connect)(config, variables);\n        },\n        mutationKey: ['connect'],\n    };\n}\n//# sourceMappingURL=connect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L2Nvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUQ7QUFDMUM7QUFDUDtBQUNBO0FBQ0EsbUJBQW1CLDREQUFPO0FBQzFCLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9xdWVyeS9jb25uZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbm5lY3QsIH0gZnJvbSAnLi4vYWN0aW9ucy9jb25uZWN0LmpzJztcbmV4cG9ydCBmdW5jdGlvbiBjb25uZWN0TXV0YXRpb25PcHRpb25zKGNvbmZpZykge1xuICAgIHJldHVybiB7XG4gICAgICAgIG11dGF0aW9uRm4odmFyaWFibGVzKSB7XG4gICAgICAgICAgICByZXR1cm4gY29ubmVjdChjb25maWcsIHZhcmlhYmxlcyk7XG4gICAgICAgIH0sXG4gICAgICAgIG11dGF0aW9uS2V5OiBbJ2Nvbm5lY3QnXSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29ubmVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/connect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/disconnect.js":
/*!****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/disconnect.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   disconnectMutationOptions: () => (/* binding */ disconnectMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_disconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/disconnect.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/disconnect.js\");\n\nfunction disconnectMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_disconnect_js__WEBPACK_IMPORTED_MODULE_0__.disconnect)(config, variables);\n        },\n        mutationKey: ['disconnect'],\n    };\n}\n//# sourceMappingURL=disconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L2Rpc2Nvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUQ7QUFDaEQ7QUFDUDtBQUNBO0FBQ0EsbUJBQW1CLGtFQUFVO0FBQzdCLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9xdWVyeS9kaXNjb25uZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRpc2Nvbm5lY3QsIH0gZnJvbSAnLi4vYWN0aW9ucy9kaXNjb25uZWN0LmpzJztcbmV4cG9ydCBmdW5jdGlvbiBkaXNjb25uZWN0TXV0YXRpb25PcHRpb25zKGNvbmZpZykge1xuICAgIHJldHVybiB7XG4gICAgICAgIG11dGF0aW9uRm4odmFyaWFibGVzKSB7XG4gICAgICAgICAgICByZXR1cm4gZGlzY29ubmVjdChjb25maWcsIHZhcmlhYmxlcyk7XG4gICAgICAgIH0sXG4gICAgICAgIG11dGF0aW9uS2V5OiBbJ2Rpc2Nvbm5lY3QnXSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGlzY29ubmVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/disconnect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/getBalance.js":
/*!****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/getBalance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBalanceQueryKey: () => (/* binding */ getBalanceQueryKey),\n/* harmony export */   getBalanceQueryOptions: () => (/* binding */ getBalanceQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_getBalance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/getBalance.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getBalance.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\nfunction getBalanceQueryOptions(config, options = {}) {\n    return {\n        async queryFn({ queryKey }) {\n            const { address, scopeKey: _, ...parameters } = queryKey[1];\n            if (!address)\n                throw new Error('address is required');\n            const balance = await (0,_actions_getBalance_js__WEBPACK_IMPORTED_MODULE_0__.getBalance)(config, {\n                ...parameters,\n                address,\n            });\n            return balance ?? null;\n        },\n        queryKey: getBalanceQueryKey(options),\n    };\n}\nfunction getBalanceQueryKey(options = {}) {\n    return ['balance', (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.filterQueryOptions)(options)];\n}\n//# sourceMappingURL=getBalance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L2dldEJhbGFuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1RDtBQUNQO0FBQ3pDLG9EQUFvRDtBQUMzRDtBQUNBLHdCQUF3QixVQUFVO0FBQ2xDLG9CQUFvQixzQ0FBc0M7QUFDMUQ7QUFDQTtBQUNBLGtDQUFrQyxrRUFBVTtBQUM1QztBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNPLHdDQUF3QztBQUMvQyx1QkFBdUIsNkRBQWtCO0FBQ3pDO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvZ2V0QmFsYW5jZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRCYWxhbmNlLCB9IGZyb20gJy4uL2FjdGlvbnMvZ2V0QmFsYW5jZS5qcyc7XG5pbXBvcnQgeyBmaWx0ZXJRdWVyeU9wdGlvbnMgfSBmcm9tICcuL3V0aWxzLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBnZXRCYWxhbmNlUXVlcnlPcHRpb25zKGNvbmZpZywgb3B0aW9ucyA9IHt9KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYXN5bmMgcXVlcnlGbih7IHF1ZXJ5S2V5IH0pIHtcbiAgICAgICAgICAgIGNvbnN0IHsgYWRkcmVzcywgc2NvcGVLZXk6IF8sIC4uLnBhcmFtZXRlcnMgfSA9IHF1ZXJ5S2V5WzFdO1xuICAgICAgICAgICAgaWYgKCFhZGRyZXNzKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignYWRkcmVzcyBpcyByZXF1aXJlZCcpO1xuICAgICAgICAgICAgY29uc3QgYmFsYW5jZSA9IGF3YWl0IGdldEJhbGFuY2UoY29uZmlnLCB7XG4gICAgICAgICAgICAgICAgLi4ucGFyYW1ldGVycyxcbiAgICAgICAgICAgICAgICBhZGRyZXNzLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gYmFsYW5jZSA/PyBudWxsO1xuICAgICAgICB9LFxuICAgICAgICBxdWVyeUtleTogZ2V0QmFsYW5jZVF1ZXJ5S2V5KG9wdGlvbnMpLFxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gZ2V0QmFsYW5jZVF1ZXJ5S2V5KG9wdGlvbnMgPSB7fSkge1xuICAgIHJldHVybiBbJ2JhbGFuY2UnLCBmaWx0ZXJRdWVyeU9wdGlvbnMob3B0aW9ucyldO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0QmFsYW5jZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/getBalance.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/getEnsAvatar.js":
/*!******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/getEnsAvatar.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnsAvatarQueryKey: () => (/* binding */ getEnsAvatarQueryKey),\n/* harmony export */   getEnsAvatarQueryOptions: () => (/* binding */ getEnsAvatarQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_getEnsAvatar_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/getEnsAvatar.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getEnsAvatar.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\nfunction getEnsAvatarQueryOptions(config, options = {}) {\n    return {\n        async queryFn({ queryKey }) {\n            const { name, scopeKey: _, ...parameters } = queryKey[1];\n            if (!name)\n                throw new Error('name is required');\n            return (0,_actions_getEnsAvatar_js__WEBPACK_IMPORTED_MODULE_0__.getEnsAvatar)(config, { ...parameters, name });\n        },\n        queryKey: getEnsAvatarQueryKey(options),\n    };\n}\nfunction getEnsAvatarQueryKey(options = {}) {\n    return ['ensAvatar', (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.filterQueryOptions)(options)];\n}\n//# sourceMappingURL=getEnsAvatar.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L2dldEVuc0F2YXRhci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTJEO0FBQ1g7QUFDekMsc0RBQXNEO0FBQzdEO0FBQ0Esd0JBQXdCLFVBQVU7QUFDbEMsb0JBQW9CLG1DQUFtQztBQUN2RDtBQUNBO0FBQ0EsbUJBQW1CLHNFQUFZLFdBQVcscUJBQXFCO0FBQy9ELFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDTywwQ0FBMEM7QUFDakQseUJBQXlCLDZEQUFrQjtBQUMzQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L2dldEVuc0F2YXRhci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRFbnNBdmF0YXIsIH0gZnJvbSAnLi4vYWN0aW9ucy9nZXRFbnNBdmF0YXIuanMnO1xuaW1wb3J0IHsgZmlsdGVyUXVlcnlPcHRpb25zIH0gZnJvbSAnLi91dGlscy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gZ2V0RW5zQXZhdGFyUXVlcnlPcHRpb25zKGNvbmZpZywgb3B0aW9ucyA9IHt9KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYXN5bmMgcXVlcnlGbih7IHF1ZXJ5S2V5IH0pIHtcbiAgICAgICAgICAgIGNvbnN0IHsgbmFtZSwgc2NvcGVLZXk6IF8sIC4uLnBhcmFtZXRlcnMgfSA9IHF1ZXJ5S2V5WzFdO1xuICAgICAgICAgICAgaWYgKCFuYW1lKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignbmFtZSBpcyByZXF1aXJlZCcpO1xuICAgICAgICAgICAgcmV0dXJuIGdldEVuc0F2YXRhcihjb25maWcsIHsgLi4ucGFyYW1ldGVycywgbmFtZSB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgcXVlcnlLZXk6IGdldEVuc0F2YXRhclF1ZXJ5S2V5KG9wdGlvbnMpLFxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gZ2V0RW5zQXZhdGFyUXVlcnlLZXkob3B0aW9ucyA9IHt9KSB7XG4gICAgcmV0dXJuIFsnZW5zQXZhdGFyJywgZmlsdGVyUXVlcnlPcHRpb25zKG9wdGlvbnMpXTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldEVuc0F2YXRhci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/getEnsAvatar.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/getEnsName.js":
/*!****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/getEnsName.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnsNameQueryKey: () => (/* binding */ getEnsNameQueryKey),\n/* harmony export */   getEnsNameQueryOptions: () => (/* binding */ getEnsNameQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_getEnsName_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/getEnsName.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getEnsName.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\nfunction getEnsNameQueryOptions(config, options = {}) {\n    return {\n        async queryFn({ queryKey }) {\n            const { address, scopeKey: _, ...parameters } = queryKey[1];\n            if (!address)\n                throw new Error('address is required');\n            return (0,_actions_getEnsName_js__WEBPACK_IMPORTED_MODULE_0__.getEnsName)(config, { ...parameters, address });\n        },\n        queryKey: getEnsNameQueryKey(options),\n    };\n}\nfunction getEnsNameQueryKey(options = {}) {\n    return ['ensName', (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.filterQueryOptions)(options)];\n}\n//# sourceMappingURL=getEnsName.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L2dldEVuc05hbWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1RDtBQUNQO0FBQ3pDLG9EQUFvRDtBQUMzRDtBQUNBLHdCQUF3QixVQUFVO0FBQ2xDLG9CQUFvQixzQ0FBc0M7QUFDMUQ7QUFDQTtBQUNBLG1CQUFtQixrRUFBVSxXQUFXLHdCQUF3QjtBQUNoRSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ08sd0NBQXdDO0FBQy9DLHVCQUF1Qiw2REFBa0I7QUFDekM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9xdWVyeS9nZXRFbnNOYW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldEVuc05hbWUsIH0gZnJvbSAnLi4vYWN0aW9ucy9nZXRFbnNOYW1lLmpzJztcbmltcG9ydCB7IGZpbHRlclF1ZXJ5T3B0aW9ucyB9IGZyb20gJy4vdXRpbHMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGdldEVuc05hbWVRdWVyeU9wdGlvbnMoY29uZmlnLCBvcHRpb25zID0ge30pIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBhc3luYyBxdWVyeUZuKHsgcXVlcnlLZXkgfSkge1xuICAgICAgICAgICAgY29uc3QgeyBhZGRyZXNzLCBzY29wZUtleTogXywgLi4ucGFyYW1ldGVycyB9ID0gcXVlcnlLZXlbMV07XG4gICAgICAgICAgICBpZiAoIWFkZHJlc3MpXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdhZGRyZXNzIGlzIHJlcXVpcmVkJyk7XG4gICAgICAgICAgICByZXR1cm4gZ2V0RW5zTmFtZShjb25maWcsIHsgLi4ucGFyYW1ldGVycywgYWRkcmVzcyB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgcXVlcnlLZXk6IGdldEVuc05hbWVRdWVyeUtleShvcHRpb25zKSxcbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldEVuc05hbWVRdWVyeUtleShvcHRpb25zID0ge30pIHtcbiAgICByZXR1cm4gWydlbnNOYW1lJywgZmlsdGVyUXVlcnlPcHRpb25zKG9wdGlvbnMpXTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldEVuc05hbWUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/getEnsName.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/readContract.js":
/*!******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/readContract.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContractQueryKey: () => (/* binding */ readContractQueryKey),\n/* harmony export */   readContractQueryOptions: () => (/* binding */ readContractQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_readContract_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/readContract.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/readContract.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\nfunction readContractQueryOptions(config, options = {}) {\n    return {\n        // TODO: Support `signal` once Viem actions allow passthrough\n        // https://tkdodo.eu/blog/why-you-want-react-query#bonus-cancellation\n        async queryFn({ queryKey }) {\n            const abi = options.abi;\n            if (!abi)\n                throw new Error('abi is required');\n            const { functionName, scopeKey: _, ...parameters } = queryKey[1];\n            const addressOrCodeParams = (() => {\n                const params = queryKey[1];\n                if (params.address)\n                    return { address: params.address };\n                if (params.code)\n                    return { code: params.code };\n                throw new Error('address or code is required');\n            })();\n            if (!functionName)\n                throw new Error('functionName is required');\n            return (0,_actions_readContract_js__WEBPACK_IMPORTED_MODULE_0__.readContract)(config, {\n                abi,\n                functionName,\n                args: parameters.args,\n                ...addressOrCodeParams,\n                ...parameters,\n            });\n        },\n        queryKey: readContractQueryKey(options),\n    };\n}\nfunction readContractQueryKey(options = {}) {\n    const { abi: _, ...rest } = options;\n    return ['readContract', (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.filterQueryOptions)(rest)];\n}\n//# sourceMappingURL=readContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/readContract.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/signMessage.js":
/*!*****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/signMessage.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signMessageMutationOptions: () => (/* binding */ signMessageMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_signMessage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/signMessage.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/signMessage.js\");\n\nfunction signMessageMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_signMessage_js__WEBPACK_IMPORTED_MODULE_0__.signMessage)(config, variables);\n        },\n        mutationKey: ['signMessage'],\n    };\n}\n//# sourceMappingURL=signMessage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L3NpZ25NZXNzYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlEO0FBQ2xEO0FBQ1A7QUFDQTtBQUNBLG1CQUFtQixvRUFBVztBQUM5QixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvc2lnbk1lc3NhZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2lnbk1lc3NhZ2UsIH0gZnJvbSAnLi4vYWN0aW9ucy9zaWduTWVzc2FnZS5qcyc7XG5leHBvcnQgZnVuY3Rpb24gc2lnbk1lc3NhZ2VNdXRhdGlvbk9wdGlvbnMoY29uZmlnKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbXV0YXRpb25Gbih2YXJpYWJsZXMpIHtcbiAgICAgICAgICAgIHJldHVybiBzaWduTWVzc2FnZShjb25maWcsIHZhcmlhYmxlcyk7XG4gICAgICAgIH0sXG4gICAgICAgIG11dGF0aW9uS2V5OiBbJ3NpZ25NZXNzYWdlJ10sXG4gICAgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNpZ25NZXNzYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/signMessage.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/switchChain.js":
/*!*****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/switchChain.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   switchChainMutationOptions: () => (/* binding */ switchChainMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_switchChain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/switchChain.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/switchChain.js\");\n\nfunction switchChainMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_switchChain_js__WEBPACK_IMPORTED_MODULE_0__.switchChain)(config, variables);\n        },\n        mutationKey: ['switchChain'],\n    };\n}\n//# sourceMappingURL=switchChain.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L3N3aXRjaENoYWluLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlEO0FBQ2xEO0FBQ1A7QUFDQTtBQUNBLG1CQUFtQixvRUFBVztBQUM5QixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvc3dpdGNoQ2hhaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3dpdGNoQ2hhaW4sIH0gZnJvbSAnLi4vYWN0aW9ucy9zd2l0Y2hDaGFpbi5qcyc7XG5leHBvcnQgZnVuY3Rpb24gc3dpdGNoQ2hhaW5NdXRhdGlvbk9wdGlvbnMoY29uZmlnKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbXV0YXRpb25Gbih2YXJpYWJsZXMpIHtcbiAgICAgICAgICAgIHJldHVybiBzd2l0Y2hDaGFpbihjb25maWcsIHZhcmlhYmxlcyk7XG4gICAgICAgIH0sXG4gICAgICAgIG11dGF0aW9uS2V5OiBbJ3N3aXRjaENoYWluJ10sXG4gICAgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN3aXRjaENoYWluLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/switchChain.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js":
/*!***********************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/utils.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterQueryOptions: () => (/* binding */ filterQueryOptions),\n/* harmony export */   hashFn: () => (/* binding */ hashFn),\n/* harmony export */   structuralSharing: () => (/* binding */ structuralSharing)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/../node_modules/@tanstack/query-core/build/modern/utils.js\");\n\nfunction structuralSharing(oldData, newData) {\n    return (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__.replaceEqualDeep)(oldData, newData);\n}\nfunction hashFn(queryKey) {\n    return JSON.stringify(queryKey, (_, value) => {\n        if (isPlainObject(value))\n            return Object.keys(value)\n                .sort()\n                .reduce((result, key) => {\n                result[key] = value[key];\n                return result;\n            }, {});\n        if (typeof value === 'bigint')\n            return value.toString();\n        return value;\n    });\n}\n// biome-ignore lint/complexity/noBannedTypes:\nfunction isPlainObject(value) {\n    if (!hasObjectPrototype(value)) {\n        return false;\n    }\n    // If has modified constructor\n    const ctor = value.constructor;\n    if (typeof ctor === 'undefined')\n        return true;\n    // If has modified prototype\n    const prot = ctor.prototype;\n    if (!hasObjectPrototype(prot))\n        return false;\n    // If constructor does not have an Object-specific method\n    // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>\n    if (!prot.hasOwnProperty('isPrototypeOf'))\n        return false;\n    // Most likely a plain Object\n    return true;\n}\nfunction hasObjectPrototype(o) {\n    return Object.prototype.toString.call(o) === '[object Object]';\n}\nfunction filterQueryOptions(options) {\n    // destructuring is super fast\n    // biome-ignore format: no formatting\n    const { \n    // import('@tanstack/query-core').QueryOptions\n    _defaulted, behavior, gcTime, initialData, initialDataUpdatedAt, maxPages, meta, networkMode, queryFn, queryHash, queryKey, queryKeyHashFn, retry, retryDelay, structuralSharing, \n    // import('@tanstack/query-core').InfiniteQueryObserverOptions\n    getPreviousPageParam, getNextPageParam, initialPageParam, \n    // import('@tanstack/react-query').UseQueryOptions\n    _optimisticResults, enabled, notifyOnChangeProps, placeholderData, refetchInterval, refetchIntervalInBackground, refetchOnMount, refetchOnReconnect, refetchOnWindowFocus, retryOnMount, select, staleTime, suspense, throwOnError, \n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    // wagmi\n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    config, connector, query, ...rest } = options;\n    return rest;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/waitForTransactionReceipt.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/waitForTransactionReceipt.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   waitForTransactionReceiptQueryKey: () => (/* binding */ waitForTransactionReceiptQueryKey),\n/* harmony export */   waitForTransactionReceiptQueryOptions: () => (/* binding */ waitForTransactionReceiptQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_waitForTransactionReceipt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/waitForTransactionReceipt.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\nfunction waitForTransactionReceiptQueryOptions(config, options = {}) {\n    return {\n        async queryFn({ queryKey }) {\n            const { hash, ...parameters } = queryKey[1];\n            if (!hash)\n                throw new Error('hash is required');\n            return (0,_actions_waitForTransactionReceipt_js__WEBPACK_IMPORTED_MODULE_0__.waitForTransactionReceipt)(config, {\n                ...parameters,\n                onReplaced: options.onReplaced,\n                hash,\n            });\n        },\n        queryKey: waitForTransactionReceiptQueryKey(options),\n    };\n}\nfunction waitForTransactionReceiptQueryKey(options = {}) {\n    const { onReplaced: _, ...rest } = options;\n    return ['waitForTransactionReceipt', (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.filterQueryOptions)(rest)];\n}\n//# sourceMappingURL=waitForTransactionReceipt.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L3dhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFxRjtBQUNyQztBQUN6QyxtRUFBbUU7QUFDMUU7QUFDQSx3QkFBd0IsVUFBVTtBQUNsQyxvQkFBb0Isc0JBQXNCO0FBQzFDO0FBQ0E7QUFDQSxtQkFBbUIsZ0dBQXlCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ08sdURBQXVEO0FBQzlELFlBQVkseUJBQXlCO0FBQ3JDLHlDQUF5Qyw2REFBa0I7QUFDM0Q7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9xdWVyeS93YWl0Rm9yVHJhbnNhY3Rpb25SZWNlaXB0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHdhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHQsIH0gZnJvbSAnLi4vYWN0aW9ucy93YWl0Rm9yVHJhbnNhY3Rpb25SZWNlaXB0LmpzJztcbmltcG9ydCB7IGZpbHRlclF1ZXJ5T3B0aW9ucyB9IGZyb20gJy4vdXRpbHMuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHdhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHRRdWVyeU9wdGlvbnMoY29uZmlnLCBvcHRpb25zID0ge30pIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBhc3luYyBxdWVyeUZuKHsgcXVlcnlLZXkgfSkge1xuICAgICAgICAgICAgY29uc3QgeyBoYXNoLCAuLi5wYXJhbWV0ZXJzIH0gPSBxdWVyeUtleVsxXTtcbiAgICAgICAgICAgIGlmICghaGFzaClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2hhc2ggaXMgcmVxdWlyZWQnKTtcbiAgICAgICAgICAgIHJldHVybiB3YWl0Rm9yVHJhbnNhY3Rpb25SZWNlaXB0KGNvbmZpZywge1xuICAgICAgICAgICAgICAgIC4uLnBhcmFtZXRlcnMsXG4gICAgICAgICAgICAgICAgb25SZXBsYWNlZDogb3B0aW9ucy5vblJlcGxhY2VkLFxuICAgICAgICAgICAgICAgIGhhc2gsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgcXVlcnlLZXk6IHdhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHRRdWVyeUtleShvcHRpb25zKSxcbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIHdhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHRRdWVyeUtleShvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCB7IG9uUmVwbGFjZWQ6IF8sIC4uLnJlc3QgfSA9IG9wdGlvbnM7XG4gICAgcmV0dXJuIFsnd2FpdEZvclRyYW5zYWN0aW9uUmVjZWlwdCcsIGZpbHRlclF1ZXJ5T3B0aW9ucyhyZXN0KV07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13YWl0Rm9yVHJhbnNhY3Rpb25SZWNlaXB0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/waitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/query/writeContract.js":
/*!*******************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/query/writeContract.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeContractMutationOptions: () => (/* binding */ writeContractMutationOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_writeContract_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/writeContract.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/writeContract.js\");\n\nfunction writeContractMutationOptions(config) {\n    return {\n        mutationFn(variables) {\n            return (0,_actions_writeContract_js__WEBPACK_IMPORTED_MODULE_0__.writeContract)(config, variables);\n        },\n        mutationKey: ['writeContract'],\n    };\n}\n//# sourceMappingURL=writeContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3F1ZXJ5L3dyaXRlQ29udHJhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkQ7QUFDdEQ7QUFDUDtBQUNBO0FBQ0EsbUJBQW1CLHdFQUFhO0FBQ2hDLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9xdWVyeS93cml0ZUNvbnRyYWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHdyaXRlQ29udHJhY3QsIH0gZnJvbSAnLi4vYWN0aW9ucy93cml0ZUNvbnRyYWN0LmpzJztcbmV4cG9ydCBmdW5jdGlvbiB3cml0ZUNvbnRyYWN0TXV0YXRpb25PcHRpb25zKGNvbmZpZykge1xuICAgIHJldHVybiB7XG4gICAgICAgIG11dGF0aW9uRm4odmFyaWFibGVzKSB7XG4gICAgICAgICAgICByZXR1cm4gd3JpdGVDb250cmFjdChjb25maWcsIHZhcmlhYmxlcyk7XG4gICAgICAgIH0sXG4gICAgICAgIG11dGF0aW9uS2V5OiBbJ3dyaXRlQ29udHJhY3QnXSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d3JpdGVDb250cmFjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/query/writeContract.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js":
/*!***************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual)\n/* harmony export */ });\n/** Forked from https://github.com/epoberezkin/fast-deep-equal */\nfunction deepEqual(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a === 'object' && typeof b === 'object') {\n        if (a.constructor !== b.constructor)\n            return false;\n        let length;\n        let i;\n        if (Array.isArray(a) && Array.isArray(b)) {\n            length = a.length;\n            if (length !== b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!deepEqual(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (a.valueOf !== Object.prototype.valueOf)\n            return a.valueOf() === b.valueOf();\n        if (a.toString !== Object.prototype.toString)\n            return a.toString() === b.toString();\n        const keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!Object.prototype.hasOwnProperty.call(b, keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            const key = keys[i];\n            if (key && !deepEqual(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    // true if both NaN, false otherwise\n    // biome-ignore lint/suspicious/noSelfCompare: <explanation>\n    return a !== a && b !== b;\n}\n//# sourceMappingURL=deepEqual.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deserialize.js":
/*!*****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/utils/deserialize.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\nfunction deserialize(value, reviver) {\n    return JSON.parse(value, (key, value_) => {\n        let value = value_;\n        if (value?.__type === 'bigint')\n            value = BigInt(value.value);\n        if (value?.__type === 'Map')\n            value = new Map(value.value);\n        return reviver?.(key, value) ?? value;\n    });\n}\n//# sourceMappingURL=deserialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2Rlc2VyaWFsaXplLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVzZXJpYWxpemUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRlc2VyaWFsaXplKHZhbHVlLCByZXZpdmVyKSB7XG4gICAgcmV0dXJuIEpTT04ucGFyc2UodmFsdWUsIChrZXksIHZhbHVlXykgPT4ge1xuICAgICAgICBsZXQgdmFsdWUgPSB2YWx1ZV87XG4gICAgICAgIGlmICh2YWx1ZT8uX190eXBlID09PSAnYmlnaW50JylcbiAgICAgICAgICAgIHZhbHVlID0gQmlnSW50KHZhbHVlLnZhbHVlKTtcbiAgICAgICAgaWYgKHZhbHVlPy5fX3R5cGUgPT09ICdNYXAnKVxuICAgICAgICAgICAgdmFsdWUgPSBuZXcgTWFwKHZhbHVlLnZhbHVlKTtcbiAgICAgICAgcmV0dXJuIHJldml2ZXI/LihrZXksIHZhbHVlKSA/PyB2YWx1ZTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRlc2VyaWFsaXplLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deserialize.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js":
/*!********************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractRpcUrls: () => (/* binding */ extractRpcUrls)\n/* harmony export */ });\nfunction extractRpcUrls(parameters) {\n    const { chain } = parameters;\n    const fallbackUrl = chain.rpcUrls.default.http[0];\n    if (!parameters.transports)\n        return [fallbackUrl];\n    const transport = parameters.transports?.[chain.id]?.({ chain });\n    const transports = transport?.value?.transports || [transport];\n    return transports.map(({ value }) => value?.url || fallbackUrl);\n}\n//# sourceMappingURL=extractRpcUrls.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2V4dHJhY3RScGNVcmxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQLFlBQVksUUFBUTtBQUNwQjtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsT0FBTztBQUNuRTtBQUNBLDZCQUE2QixPQUFPO0FBQ3BDO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZXh0cmFjdFJwY1VybHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGV4dHJhY3RScGNVcmxzKHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGNoYWluIH0gPSBwYXJhbWV0ZXJzO1xuICAgIGNvbnN0IGZhbGxiYWNrVXJsID0gY2hhaW4ucnBjVXJscy5kZWZhdWx0Lmh0dHBbMF07XG4gICAgaWYgKCFwYXJhbWV0ZXJzLnRyYW5zcG9ydHMpXG4gICAgICAgIHJldHVybiBbZmFsbGJhY2tVcmxdO1xuICAgIGNvbnN0IHRyYW5zcG9ydCA9IHBhcmFtZXRlcnMudHJhbnNwb3J0cz8uW2NoYWluLmlkXT8uKHsgY2hhaW4gfSk7XG4gICAgY29uc3QgdHJhbnNwb3J0cyA9IHRyYW5zcG9ydD8udmFsdWU/LnRyYW5zcG9ydHMgfHwgW3RyYW5zcG9ydF07XG4gICAgcmV0dXJuIHRyYW5zcG9ydHMubWFwKCh7IHZhbHVlIH0pID0+IHZhbHVlPy51cmwgfHwgZmFsbGJhY2tVcmwpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXh0cmFjdFJwY1VybHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/utils/extractRpcUrls.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js":
/*!***************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/utils/getAction.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAction: () => (/* binding */ getAction)\n/* harmony export */ });\n/**\n * Retrieves and returns an action from the client (if exists), and falls\n * back to the tree-shakable action.\n *\n * Useful for extracting overridden actions from a client (ie. if a consumer\n * wants to override the `sendTransaction` implementation).\n */\nfunction getAction(client, actionFn, \n// Some minifiers drop `Function.prototype.name`, or replace it with short letters,\n// meaning that `actionFn.name` will not always work. For that case, the consumer\n// needs to pass the name explicitly.\nname) {\n    const action_implicit = client[actionFn.name];\n    if (typeof action_implicit === 'function')\n        return action_implicit;\n    const action_explicit = client[name];\n    if (typeof action_explicit === 'function')\n        return action_explicit;\n    return (params) => actionFn(client, params);\n}\n//# sourceMappingURL=getAction.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2dldEFjdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2dldEFjdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJldHJpZXZlcyBhbmQgcmV0dXJucyBhbiBhY3Rpb24gZnJvbSB0aGUgY2xpZW50IChpZiBleGlzdHMpLCBhbmQgZmFsbHNcbiAqIGJhY2sgdG8gdGhlIHRyZWUtc2hha2FibGUgYWN0aW9uLlxuICpcbiAqIFVzZWZ1bCBmb3IgZXh0cmFjdGluZyBvdmVycmlkZGVuIGFjdGlvbnMgZnJvbSBhIGNsaWVudCAoaWUuIGlmIGEgY29uc3VtZXJcbiAqIHdhbnRzIHRvIG92ZXJyaWRlIHRoZSBgc2VuZFRyYW5zYWN0aW9uYCBpbXBsZW1lbnRhdGlvbikuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRBY3Rpb24oY2xpZW50LCBhY3Rpb25GbiwgXG4vLyBTb21lIG1pbmlmaWVycyBkcm9wIGBGdW5jdGlvbi5wcm90b3R5cGUubmFtZWAsIG9yIHJlcGxhY2UgaXQgd2l0aCBzaG9ydCBsZXR0ZXJzLFxuLy8gbWVhbmluZyB0aGF0IGBhY3Rpb25Gbi5uYW1lYCB3aWxsIG5vdCBhbHdheXMgd29yay4gRm9yIHRoYXQgY2FzZSwgdGhlIGNvbnN1bWVyXG4vLyBuZWVkcyB0byBwYXNzIHRoZSBuYW1lIGV4cGxpY2l0bHkuXG5uYW1lKSB7XG4gICAgY29uc3QgYWN0aW9uX2ltcGxpY2l0ID0gY2xpZW50W2FjdGlvbkZuLm5hbWVdO1xuICAgIGlmICh0eXBlb2YgYWN0aW9uX2ltcGxpY2l0ID09PSAnZnVuY3Rpb24nKVxuICAgICAgICByZXR1cm4gYWN0aW9uX2ltcGxpY2l0O1xuICAgIGNvbnN0IGFjdGlvbl9leHBsaWNpdCA9IGNsaWVudFtuYW1lXTtcbiAgICBpZiAodHlwZW9mIGFjdGlvbl9leHBsaWNpdCA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgcmV0dXJuIGFjdGlvbl9leHBsaWNpdDtcbiAgICByZXR1cm4gKHBhcmFtcykgPT4gYWN0aW9uRm4oY2xpZW50LCBwYXJhbXMpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0QWN0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getAction.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getUnit.js":
/*!*************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/utils/getUnit.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUnit: () => (/* binding */ getUnit)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/../node_modules/viem/_esm/constants/unit.js\");\n\nfunction getUnit(unit) {\n    if (typeof unit === 'number')\n        return unit;\n    if (unit === 'wei')\n        return 0;\n    return Math.abs(viem__WEBPACK_IMPORTED_MODULE_0__.weiUnits[unit]);\n}\n//# sourceMappingURL=getUnit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2dldFVuaXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0M7QUFDekI7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiwwQ0FBUTtBQUM1QjtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2dldFVuaXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd2VpVW5pdHMgfSBmcm9tICd2aWVtJztcbmV4cG9ydCBmdW5jdGlvbiBnZXRVbml0KHVuaXQpIHtcbiAgICBpZiAodHlwZW9mIHVuaXQgPT09ICdudW1iZXInKVxuICAgICAgICByZXR1cm4gdW5pdDtcbiAgICBpZiAodW5pdCA9PT0gJ3dlaScpXG4gICAgICAgIHJldHVybiAwO1xuICAgIHJldHVybiBNYXRoLmFicyh3ZWlVbml0c1t1bml0XSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRVbml0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getUnit.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getVersion.js":
/*!****************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/utils/getVersion.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/version.js\");\n\nconst getVersion = () => `@wagmi/core@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`;\n//# sourceMappingURL=getVersion.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2dldFZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0M7QUFDakMsd0NBQXdDLGdEQUFPLENBQUM7QUFDdkQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG5leHBvcnQgY29uc3QgZ2V0VmVyc2lvbiA9ICgpID0+IGBAd2FnbWkvY29yZUAke3ZlcnNpb259YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldFZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/utils/getVersion.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/utils/serialize.js":
/*!***************************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/utils/serialize.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys, cutoff) {\n    return keys.slice(0, cutoff).join('.') || '.';\n}\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array, value) {\n    const { length } = array;\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n    return 0;\n}\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(replacer, circularReplacer) {\n    const hasReplacer = typeof replacer === 'function';\n    const hasCircularReplacer = typeof circularReplacer === 'function';\n    const cache = [];\n    const keys = [];\n    return function replace(key, value) {\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                }\n                else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n                keys[keys.length] = key;\n                const valueCutoff = getCutoff(cache, value);\n                if (valueCutoff !== 0) {\n                    return hasCircularReplacer\n                        ? circularReplacer.call(this, key, value, getReferenceKey(keys, valueCutoff))\n                        : `[ref=${getReferenceKey(keys, valueCutoff)}]`;\n                }\n            }\n            else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n        }\n        return hasReplacer ? replacer.call(this, key, value) : value;\n    };\n}\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nfunction serialize(value, replacer, indent, circularReplacer) {\n    return JSON.stringify(value, createReplacer((key, value_) => {\n        let value = value_;\n        if (typeof value === 'bigint')\n            value = { __type: 'bigint', value: value_.toString() };\n        if (value instanceof Map)\n            value = { __type: 'Map', value: Array.from(value_.entries()) };\n        return replacer?.(key, value) ?? value;\n    }, circularReplacer), indent ?? undefined);\n}\n//# sourceMappingURL=serialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/utils/serialize.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/utils/uid.js":
/*!*********************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/utils/uid.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uid: () => (/* binding */ uid)\n/* harmony export */ });\nconst size = 256;\nlet index = size;\nlet buffer;\nfunction uid(length = 11) {\n    if (!buffer || index + length > size * 2) {\n        buffer = '';\n        index = 0;\n        for (let i = 0; i < size; i++) {\n            buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);\n        }\n    }\n    return buffer.substring(index, index++ + length);\n}\n//# sourceMappingURL=uid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL3VpZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvdWlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNpemUgPSAyNTY7XG5sZXQgaW5kZXggPSBzaXplO1xubGV0IGJ1ZmZlcjtcbmV4cG9ydCBmdW5jdGlvbiB1aWQobGVuZ3RoID0gMTEpIHtcbiAgICBpZiAoIWJ1ZmZlciB8fCBpbmRleCArIGxlbmd0aCA+IHNpemUgKiAyKSB7XG4gICAgICAgIGJ1ZmZlciA9ICcnO1xuICAgICAgICBpbmRleCA9IDA7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2l6ZTsgaSsrKSB7XG4gICAgICAgICAgICBidWZmZXIgKz0gKCgyNTYgKyBNYXRoLnJhbmRvbSgpICogMjU2KSB8IDApLnRvU3RyaW5nKDE2KS5zdWJzdHJpbmcoMSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGJ1ZmZlci5zdWJzdHJpbmcoaW5kZXgsIGluZGV4KysgKyBsZW5ndGgpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dWlkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/utils/uid.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/@wagmi/core/dist/esm/version.js":
/*!*******************************************************!*\
  !*** ../node_modules/@wagmi/core/dist/esm/version.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.17.3';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjE3LjMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/@wagmi/core/dist/esm/version.js\n");

/***/ })

};
;