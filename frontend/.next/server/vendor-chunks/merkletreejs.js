"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/merkletreejs";
exports.ids = ["vendor-chunks/merkletreejs"];
exports.modules = {

/***/ "(rsc)/../node_modules/merkletreejs/dist/Base.js":
/*!*************************************************!*\
  !*** ../node_modules/merkletreejs/dist/Base.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Base = void 0;\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst crypto_js_1 = __importDefault(__webpack_require__(/*! crypto-js */ \"(rsc)/../node_modules/crypto-js/index.js\"));\nclass Base {\n    /**\n     * print\n     * @desc Prints out a visual representation of the merkle tree.\n     * @example\n     *```js\n     *tree.print()\n     *```\n     */\n    print() {\n        Base.print(this);\n    }\n    /**\n     * bufferIndexOf\n     * @desc Returns the first index of which given buffer is found in array.\n     * @param {Buffer[]} haystack - Array of buffers.\n     * @param {Buffer} needle - Buffer to find.\n     * @return {Number} - Index number\n     *\n     * @example\n     * ```js\n     *const index = tree.bufferIndexOf(haystack, needle)\n     *```\n     */\n    bufferIndexOf(array, element, isSorted = false) {\n        if (isSorted) {\n            return this.binarySearch(array, element, buffer_1.Buffer.compare);\n        }\n        const eqChecker = (buffer1, buffer2) => buffer1.equals(buffer2);\n        return this.linearSearch(array, element, eqChecker);\n    }\n    /**\n     * binarySearch\n     * @desc Returns the first index of which given item is found in array using binary search.\n     * @param {Buffer[]} array - Array of items.\n     * @param {Buffer} element - Item to find.\n     * @param {Function} compareFunction\n     * @return {Number} - Index number\n     *\n     * @example\n     * ```js\n     *const index = MerkleTree.binarySearch(array, element, Buffer.compare)\n     *```\n     */\n    static binarySearch(array, element, compareFunction) {\n        let start = 0;\n        let end = array.length - 1;\n        // Iterate while start not meets end\n        while (start <= end) {\n            // Find the mid index\n            const mid = Math.floor((start + end) / 2);\n            // Check if the mid value is greater than, equal to, or less than search element.\n            const ordering = compareFunction(array[mid], element);\n            // If element is present at mid, start iterating for searching first appearance.\n            if (ordering === 0) {\n                // Linear reverse iteration until the first matching item index is found.\n                for (let i = mid - 1; i >= 0; i--) {\n                    if (compareFunction(array[i], element) === 0)\n                        continue;\n                    return i + 1;\n                }\n                return 0;\n            } /* Else look in left or right half accordingly */\n            else if (ordering < 0) {\n                start = mid + 1;\n            }\n            else {\n                end = mid - 1;\n            }\n        }\n        return -1;\n    }\n    /**\n     * binarySearch\n     * @desc Returns the first index of which given item is found in array using binary search.\n     * @param {Buffer[]} array - Array of items.\n     * @param {Buffer} element - Item to find.\n     * @param {Function} compareFunction\n     * @return {Number} - Index number\n     *\n     * @example\n     * ```js\n     *const index = tree.binarySearch(array, element, Buffer.compare)\n     *```\n     */\n    binarySearch(array, element, compareFunction) {\n        return Base.binarySearch(array, element, compareFunction);\n    }\n    /**\n     * linearSearch\n     * @desc Returns the first index of which given item is found in array using linear search.\n     * @param {Buffer[]} array - Array of items.\n     * @param {Buffer} element - Item to find.\n     * @param {Function} eqChecker\n     * @return {Number} - Index number\n     *\n     * @example\n     * ```js\n     *const index = MerkleTree.linearSearch(array, element, (a, b) => a === b)\n     *```\n     */\n    static linearSearch(array, element, eqChecker) {\n        for (let i = 0; i < array.length; i++) {\n            if (eqChecker(array[i], element)) {\n                return i;\n            }\n        }\n        return -1;\n    }\n    /**\n     * linearSearch\n     * @desc Returns the first index of which given item is found in array using linear search.\n     * @param {Buffer[]} array - Array of items.\n     * @param {Buffer} element - Item to find.\n     * @param {Function} eqChecker\n     * @return {Number} - Index number\n     *\n     * @example\n     * ```js\n     *const index = tree.linearSearch(array, element, (a, b) => a === b)\n     *```\n     */\n    linearSearch(array, element, eqChecker) {\n        return Base.linearSearch(array, element, eqChecker);\n    }\n    /**\n     * bufferify\n     * @desc Returns a buffer type for the given value.\n     * @param {String|Number|Object|Buffer|ArrayBuffer} value\n     * @return {Buffer}\n     *\n     * @example\n     * ```js\n     *const buf = MerkleTree.bufferify('0x1234')\n     *```\n     */\n    static bufferify(value) {\n        if (!buffer_1.Buffer.isBuffer(value)) {\n            // crypto-js support\n            if (typeof value === 'object' && value.words) {\n                return buffer_1.Buffer.from(value.toString(crypto_js_1.default.enc.Hex), 'hex');\n            }\n            else if (Base.isHexString(value)) {\n                const hexString = value.replace('0x', '');\n                const paddedHexString = hexString.length % 2 ? '0' + hexString : hexString;\n                return buffer_1.Buffer.from(paddedHexString, 'hex');\n            }\n            else if (typeof value === 'string') {\n                return buffer_1.Buffer.from(value);\n            }\n            else if (typeof value === 'bigint') {\n                const hexString = value.toString(16).length % 2 ? '0' + value.toString(16) : value.toString(16);\n                return buffer_1.Buffer.from(hexString, 'hex');\n            }\n            else if (value instanceof Uint8Array) {\n                return buffer_1.Buffer.from(value.buffer, value.byteOffset, value.byteLength);\n            }\n            else if (typeof value === 'number') {\n                let s = value.toString();\n                if (s.length % 2) {\n                    s = `0${s}`;\n                }\n                return buffer_1.Buffer.from(s, 'hex');\n            }\n            else if (ArrayBuffer.isView(value)) {\n                return buffer_1.Buffer.from(value.buffer, value.byteOffset, value.byteLength);\n            }\n        }\n        return value;\n    }\n    /**\n     * bufferifyFn\n     * @desc Returns a function that will bufferify the return value.\n     * @param {Function}\n     * @return {Function}\n     *\n     * @example\n     * ```js\n     *const fn = MerkleTree.bufferifyFn((value) => sha256(value))\n     *```\n     */\n    static bufferifyFn(f) {\n        if (typeof f !== 'function') {\n            throw new Error(`bufferifyFn expects a function, received: ${typeof f}`);\n        }\n        return (value) => {\n            const v = f(value);\n            if (buffer_1.Buffer.isBuffer(v)) {\n                return v;\n            }\n            if (Base.isHexString(v)) {\n                const hexString = v.replace('0x', '');\n                const paddedHexString = hexString.length % 2 ? '0' + hexString : hexString;\n                return buffer_1.Buffer.from(paddedHexString, 'hex');\n            }\n            if (typeof v === 'string') {\n                return buffer_1.Buffer.from(v);\n            }\n            if (typeof v === 'bigint') {\n                const hexString = v.toString(16).length % 2 ? '0' + v.toString(16) : v.toString(16);\n                return buffer_1.Buffer.from(hexString, 'hex');\n            }\n            if (ArrayBuffer.isView(v)) {\n                return buffer_1.Buffer.from(v.buffer, v.byteOffset, v.byteLength);\n            }\n            // crypto-js support\n            return buffer_1.Buffer.from(f(crypto_js_1.default.enc.Hex.parse(value.toString('hex'))).toString(crypto_js_1.default.enc.Hex), 'hex');\n        };\n    }\n    bigNumberify(value) {\n        return Base.bigNumberify(value);\n    }\n    static bigNumberify(value) {\n        if (typeof value === 'bigint') {\n            return value;\n        }\n        if (typeof value === 'string') {\n            if (value.startsWith('0x') && Base.isHexString(value)) {\n                // Remove '0x' and ensure even-length hex string\n                const hexString = value.replace('0x', '');\n                const paddedHexString = hexString.length % 2 ? '0' + hexString : (hexString || '0');\n                return BigInt('0x' + paddedHexString);\n            }\n            return BigInt(value);\n        }\n        if (buffer_1.Buffer.isBuffer(value)) {\n            // Convert buffer to hex string and ensure even-length hex string\n            const hexString = value.toString('hex');\n            const paddedHexString = hexString.length % 2 ? '0' + hexString : (hexString || '0');\n            return BigInt('0x' + paddedHexString);\n        }\n        if (value instanceof Uint8Array) {\n            // Convert Uint8Array to hex string and ensure even-length hex string\n            const hexString = buffer_1.Buffer.from(value).toString('hex');\n            const paddedHexString = hexString.length % 2 ? '0' + hexString : (hexString || '0');\n            return BigInt('0x' + paddedHexString);\n        }\n        if (typeof value === 'number') {\n            return BigInt(value);\n        }\n        throw new Error('cannot bigNumberify');\n    }\n    /**\n     * isHexString\n     * @desc Returns true if value is a hex string.\n     * @param {String} value\n     * @return {Boolean}\n     *\n     * @example\n     * ```js\n     *console.log(MerkleTree.isHexString('0x1234'))\n     *```\n     */\n    static isHexString(v) {\n        return typeof v === 'string' && /^(0x)?[0-9A-Fa-f]*$/.test(v);\n    }\n    /**\n     * print\n     * @desc Prints out a visual representation of the given merkle tree.\n     * @param {Object} tree - Merkle tree instance.\n     * @return {String}\n     * @example\n     *```js\n     *MerkleTree.print(tree)\n     *```\n     */\n    static print(tree) {\n        console.log(tree.toString());\n    }\n    /**\n     * bufferToHex\n     * @desc Returns a hex string with 0x prefix for given buffer.\n     * @param {Buffer} value\n     * @return {String}\n     * @example\n     *```js\n     *const hexStr = tree.bufferToHex(Buffer.from('A'))\n     *```\n     */\n    bufferToHex(value, withPrefix = true) {\n        return Base.bufferToHex(value, withPrefix);\n    }\n    /**\n     * bufferToHex\n     * @desc Returns a hex string with 0x prefix for given buffer.\n     * @param {Buffer} value\n     * @return {String}\n     * @example\n     *```js\n     *const hexStr = MerkleTree.bufferToHex(Buffer.from('A'))\n     *```\n     */\n    static bufferToHex(value, withPrefix = true) {\n        return `${withPrefix ? '0x' : ''}${(value || buffer_1.Buffer.alloc(0)).toString('hex')}`;\n    }\n    /**\n     * bufferify\n     * @desc Returns a buffer type for the given value.\n     * @param {String|Number|Object|Buffer} value\n     * @return {Buffer}\n     *\n     * @example\n     * ```js\n     *const buf = tree.bufferify('0x1234')\n     *```\n     */\n    bufferify(value) {\n        return Base.bufferify(value);\n    }\n    /**\n     * bufferifyFn\n     * @desc Returns a function that will bufferify the return value.\n     * @param {Function}\n     * @return {Function}\n     *\n     * @example\n     * ```js\n     *const fn = tree.bufferifyFn((value) => sha256(value))\n     *```\n     */\n    bufferifyFn(f) {\n        return Base.bufferifyFn(f);\n    }\n    /**\n     * isHexString\n     * @desc Returns true if value is a hex string.\n     * @param {String} value\n     * @return {Boolean}\n     *\n     * @example\n     * ```js\n     *console.log(MerkleTree.isHexString('0x1234'))\n     *```\n     */\n    isHexString(value) {\n        return Base.isHexString(value);\n    }\n    /**\n     * log2\n     * @desc Returns the log2 of number.\n     * @param {Number} value\n     * @return {Number}\n     */\n    log2(n) {\n        return n === 1 ? 0 : 1 + this.log2((n / 2) | 0);\n    }\n    /**\n     * zip\n     * @desc Returns true if value is a hex string.\n     * @param {String[]|Number[]|Buffer[]} a - first array\n     * @param {String[]|Number[]|Buffer[]} b -  second array\n     * @return {String[][]|Number[][]|Buffer[][]}\n     *\n     * @example\n     * ```js\n     *const zipped = tree.zip(['a', 'b'],['A', 'B'])\n     *console.log(zipped) // [ [ 'a', 'A' ], [ 'b', 'B' ] ]\n     *```\n     */\n    zip(a, b) {\n        return a.map((e, i) => [e, b[i]]);\n    }\n    static hexZeroPad(hexStr, length) {\n        return '0x' + hexStr.replace('0x', '').padStart(length, '0');\n    }\n    bufferArrayIncludes(bufferArray, targetBuffer) {\n        return bufferArray.some(buffer => buffer.equals(targetBuffer !== null && targetBuffer !== void 0 ? targetBuffer : buffer_1.Buffer.alloc(0)));\n    }\n}\nexports.Base = Base;\nexports[\"default\"] = Base;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/merkletreejs/dist/Base.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/merkletreejs/dist/IncrementalMerkleTree.js":
/*!******************************************************************!*\
  !*** ../node_modules/merkletreejs/dist/IncrementalMerkleTree.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.IncrementalMerkleTree = void 0;\nconst Base_1 = __importDefault(__webpack_require__(/*! ./Base */ \"(rsc)/../node_modules/merkletreejs/dist/Base.js\"));\nconst treeify_1 = __importDefault(__webpack_require__(/*! treeify */ \"(rsc)/../node_modules/treeify/treeify.js\"));\nclass IncrementalMerkleTree extends Base_1.default {\n    constructor(hashFn, options) {\n        super();\n        this.hashFn = hashFn;\n        if (options.depth) {\n            this.depth = options.depth;\n        }\n        if (options.arity) {\n            this.arity = options.arity;\n        }\n        if (this.depth < 1) {\n            throw new Error('depth must be greater than 0');\n        }\n        if (this.arity < 1) {\n            throw new Error('arity must be greater than 0');\n        }\n        const nodes = [];\n        let zeroValue = options.zeroValue;\n        this.zeroValue = zeroValue;\n        this.zeroes = [];\n        if (this.depth) {\n            for (let i = 0; i < this.depth; i++) {\n                this.zeroes.push(zeroValue);\n                nodes[i] = [];\n                zeroValue = this.hashFn(Array(this.arity).fill(zeroValue));\n            }\n        }\n        this.nodes = nodes;\n        this.root = zeroValue;\n    }\n    getRoot() {\n        return this.root;\n    }\n    getHexRoot() {\n        return this.bufferToHex(this.bufferify(this.getRoot()));\n    }\n    insert(leaf) {\n        if (this.depth && this.arity) {\n            if (this.nodes[0].length >= this.getMaxLeaves()) {\n                throw new Error('tree is full');\n            }\n        }\n        let node = leaf;\n        let index = this.nodes[0].length;\n        for (let level = 0; level < this.depth; level += 1) {\n            const position = index % this.arity;\n            const levelStartIndex = index - position;\n            const levelEndIndex = levelStartIndex + this.arity;\n            const children = [];\n            this.nodes[level][index] = node;\n            for (let i = levelStartIndex; i < levelEndIndex; i += 1) {\n                if (i < this.nodes[level].length) {\n                    children.push(this.nodes[level][i]);\n                }\n                else {\n                    children.push(this.zeroes[level]);\n                }\n            }\n            node = this.hashFn(children);\n            index = Math.floor(index / this.arity);\n        }\n        this.root = node;\n    }\n    delete(index) {\n        this.update(index, this.zeroValue);\n    }\n    update(index, newLeaf) {\n        if (index < 0 || index >= this.nodes[0].length) {\n            throw new Error('out of bounds');\n        }\n        let node = newLeaf;\n        for (let level = 0; level < this.depth; level += 1) {\n            const position = index % this.arity;\n            const levelStartIndex = index - position;\n            const levelEndIndex = levelStartIndex + this.arity;\n            const children = [];\n            this.nodes[level][index] = node;\n            for (let i = levelStartIndex; i < levelEndIndex; i += 1) {\n                if (i < this.nodes[level].length) {\n                    children.push(this.nodes[level][i]);\n                }\n                else {\n                    children.push(this.zeroes[level]);\n                }\n            }\n            node = this.hashFn(children);\n            index = Math.floor(index / this.arity);\n        }\n        this.root = node;\n    }\n    getDepth() {\n        return this.depth;\n    }\n    getArity() {\n        return this.arity;\n    }\n    getMaxLeaves() {\n        return Math.pow(this.depth, this.arity);\n    }\n    indexOf(leaf) {\n        return this.nodes[0].indexOf(leaf);\n    }\n    getLeaves() {\n        const leaves = this.copyList(this.nodes[0]);\n        const index = this.nodes[0].length;\n        for (let i = index; i < this.getMaxLeaves(); i++) {\n            leaves[i] = this.zeroValue;\n        }\n        return leaves;\n    }\n    copyList(list) {\n        return list.map((x) => BigInt(x));\n    }\n    getLayers() {\n        const layers = [];\n        for (const list of this.nodes) {\n            layers.push(this.copyList(list));\n        }\n        if (layers[0].length < this.getMaxLeaves()) {\n            let index = layers[0].length;\n            for (let i = index; i < this.getMaxLeaves(); i++) {\n                layers[0][i] = this.zeroValue;\n            }\n            for (let level = 0; level < this.depth; level++) {\n                const position = index % this.arity;\n                const levelStartIndex = index - position;\n                const levelEndIndex = levelStartIndex + this.arity;\n                for (let i = levelStartIndex; i < levelEndIndex; i++) {\n                    if (i >= layers[level].length) {\n                        layers[level][i] = this.zeroes[level];\n                    }\n                }\n                index = Math.floor(index / this.arity);\n            }\n        }\n        layers.push([this.root]);\n        return layers;\n    }\n    getHexLayers() {\n        return this.getLayers().reduce((acc, item) => {\n            if (Array.isArray(item)) {\n                acc.push(item.map(layer => this.bufferToHex(this.bufferify(layer))));\n            }\n            else {\n                acc.push(item);\n            }\n            return acc;\n        }, []);\n    }\n    getLayersAsObject() {\n        const layers = this.getLayers().map((layer) => layer.map((value) => this.bufferToHex(this.bufferify(value), false)));\n        const objs = [];\n        for (let i = 0; i < layers.length; i++) {\n            const arr = [];\n            for (let j = 0; j < layers[i].length; j++) {\n                const obj = { [layers[i][j]]: null };\n                if (objs.length) {\n                    obj[layers[i][j]] = {};\n                    const a = objs.shift();\n                    const akey = Object.keys(a)[0];\n                    obj[layers[i][j]][akey] = a[akey];\n                    if (objs.length) {\n                        const b = objs.shift();\n                        const bkey = Object.keys(b)[0];\n                        obj[layers[i][j]][bkey] = b[bkey];\n                    }\n                }\n                arr.push(obj);\n            }\n            objs.push(...arr);\n        }\n        return objs[0];\n    }\n    computeRoot() {\n        let node;\n        let index = this.nodes[0].length;\n        for (let level = 0; level < this.depth; level += 1) {\n            const position = index % this.arity;\n            const levelStartIndex = index - position;\n            const levelEndIndex = levelStartIndex + this.arity;\n            const children = [];\n            for (let i = levelStartIndex; i < levelEndIndex; i += 1) {\n                if (i < this.nodes[level].length) {\n                    children.push(this.nodes[level][i]);\n                }\n                else {\n                    children.push(this.zeroes[level]);\n                }\n            }\n            node = this.hashFn(children);\n            index = Math.floor(index / this.arity);\n        }\n        return node;\n    }\n    getProof(index) {\n        if (index < 0 || index >= this.nodes[0].length) {\n            throw new Error('The leaf does not exist in this tree');\n        }\n        const siblings = [];\n        const pathIndices = [];\n        const leafIndex = index;\n        for (let level = 0; level < this.depth; level += 1) {\n            const position = index % this.arity;\n            const levelStartIndex = index - position;\n            const levelEndIndex = levelStartIndex + this.arity;\n            pathIndices[level] = position;\n            siblings[level] = [];\n            for (let i = levelStartIndex; i < levelEndIndex; i += 1) {\n                if (i !== index) {\n                    if (i < this.nodes[level].length) {\n                        siblings[level].push(this.nodes[level][i]);\n                    }\n                    else {\n                        siblings[level].push(this.zeroes[level]);\n                    }\n                }\n            }\n            index = Math.floor(index / this.arity);\n        }\n        return { root: this.root, leaf: this.nodes[0][leafIndex], pathIndices, siblings };\n    }\n    verify(proof) {\n        let node = proof.leaf;\n        for (let i = 0; i < proof.siblings.length; i += 1) {\n            const children = proof.siblings[i].slice();\n            children.splice(proof.pathIndices[i], 0, node);\n            node = this.hashFn(children);\n        }\n        return proof.root === node;\n    }\n    toString() {\n        return this.toTreeString();\n    }\n    toTreeString() {\n        const obj = this.getLayersAsObject();\n        return treeify_1.default.asTree(obj, true);\n    }\n}\nexports.IncrementalMerkleTree = IncrementalMerkleTree;\nif (typeof window !== 'undefined') {\n    ;\n    window.IncrementalMerkleTree = IncrementalMerkleTree;\n}\nexports[\"default\"] = IncrementalMerkleTree;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/merkletreejs/dist/IncrementalMerkleTree.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/merkletreejs/dist/MerkleMountainRange.js":
/*!****************************************************************!*\
  !*** ../node_modules/merkletreejs/dist/MerkleMountainRange.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MerkleMountainRange = void 0;\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst sha256_1 = __importDefault(__webpack_require__(/*! crypto-js/sha256 */ \"(rsc)/../node_modules/crypto-js/sha256.js\"));\nconst Base_1 = __importDefault(__webpack_require__(/*! ./Base */ \"(rsc)/../node_modules/merkletreejs/dist/Base.js\"));\n// @credit: https://github.com/wanseob/solidity-mmr\n/**\n * @desc The index of this MMR implementation starts from 1 not 0.\n */\nclass MerkleMountainRange extends Base_1.default {\n    constructor(hashFn = sha256_1.default, leaves = [], hashLeafFn, peakBaggingFn, hashBranchFn) {\n        super();\n        this.root = buffer_1.Buffer.alloc(0);\n        this.size = 0;\n        this.width = 0;\n        this.hashes = {};\n        this.data = {};\n        leaves = leaves.map(this.bufferify);\n        this.hashFn = this.bufferifyFn(hashFn);\n        this.hashLeafFn = hashLeafFn;\n        this.peakBaggingFn = peakBaggingFn;\n        this.hashBranchFn = hashBranchFn;\n        for (const leaf of leaves) {\n            this.append(leaf);\n        }\n    }\n    /**\n     * @desc This only stores the hashed value of the leaf.\n     * If you need to retrieve the detail data later, use a map to store them.\n     */\n    append(data) {\n        data = this.bufferify(data);\n        const dataHash = this.hashFn(data);\n        const dataHashHex = this.bufferToHex(dataHash);\n        if (!this.data[dataHashHex] || this.bufferToHex(this.hashFn(this.data[dataHashHex])) !== dataHashHex) {\n            this.data[dataHashHex] = data;\n        }\n        const leaf = this.hashLeaf(this.size + 1, dataHash);\n        this.hashes[this.size + 1] = leaf;\n        this.width += 1;\n        // find peaks for enlarged tree\n        const peakIndexes = this.getPeakIndexes(this.width);\n        // the right most peak's value is the new size of the updated tree\n        this.size = this.getSize(this.width);\n        // starting from the left-most peak, get all peak hashes\n        const peaks = [];\n        for (let i = 0; i < peakIndexes.length; i++) {\n            peaks[i] = this._getOrCreateNode(peakIndexes[i]);\n        }\n        // update the tree root hash\n        this.root = this.peakBagging(this.width, peaks);\n    }\n    /**\n     * @desc It returns the hash of a leaf node with hash(M | DATA )\n     *       M is the index of the node.\n     */\n    hashLeaf(index, dataHash) {\n        dataHash = this.bufferify(dataHash);\n        if (this.hashLeafFn) {\n            return this.bufferify(this.hashLeafFn(index, dataHash));\n        }\n        return this.hashFn(buffer_1.Buffer.concat([this.bufferify(index), dataHash]));\n    }\n    /**\n     * @desc It returns the hash a parent node with hash(M | Left child | Right child)\n     *       M is the index of the node.\n     */\n    hashBranch(index, left, right) {\n        if (this.hashBranchFn) {\n            return this.bufferify(this.hashBranchFn(index, left, right));\n        }\n        return this.hashFn(buffer_1.Buffer.concat([this.bufferify(index), this.bufferify(left), this.bufferify(right)]));\n    }\n    getPeaks() {\n        const peakIndexes = this.getPeakIndexes(this.width);\n        const peaks = [];\n        for (let i = 0; i < peakIndexes.length; i++) {\n            peaks[i] = this.hashes[peakIndexes[i]];\n        }\n        return peaks;\n    }\n    getLeafIndex(width) {\n        if (width % 2 === 1) {\n            return this.getSize(width);\n        }\n        return this.getSize(width - 1) + 1;\n    }\n    /**\n     * @desc It returns all peaks of the smallest merkle mountain range tree which includes\n     *       the given index(size).\n     */\n    getPeakIndexes(width) {\n        const numPeaks = this.numOfPeaks(width);\n        const peakIndexes = [];\n        let count = 0;\n        let size = 0;\n        for (let i = 255; i > 0; i--) {\n            if ((width & (1 << (i - 1))) !== 0) {\n                // peak exists\n                size = size + (1 << i) - 1;\n                peakIndexes[count++] = size;\n                if (peakIndexes.length >= numPeaks) {\n                    break;\n                }\n            }\n        }\n        if (count !== peakIndexes.length) {\n            throw new Error('invalid bit calculation');\n        }\n        return peakIndexes;\n    }\n    numOfPeaks(width) {\n        let bits = width;\n        let num = 0;\n        while (bits > 0) {\n            if (bits % 2 === 1) {\n                num++;\n            }\n            bits = bits >> 1;\n        }\n        return num;\n    }\n    peakBagging(width, peaks) {\n        const size = this.getSize(width);\n        if (this.numOfPeaks(width) !== peaks.length) {\n            throw new Error('received invalid number of peaks');\n        }\n        if (width === 0 && !peaks.length) {\n            return buffer_1.Buffer.alloc(0);\n        }\n        if (this.peakBaggingFn) {\n            return this.bufferify(this.peakBaggingFn(size, peaks));\n        }\n        return this.hashFn(buffer_1.Buffer.concat([this.bufferify(size), ...peaks.map(this.bufferify)]));\n    }\n    /**\n     * @desc It returns the size of the tree.\n     */\n    getSize(width) {\n        return (width << 1) - this.numOfPeaks(width);\n    }\n    /**\n     * @desc It returns the root value of the tree.\n     */\n    getRoot() {\n        return this.root;\n    }\n    getHexRoot() {\n        return this.bufferToHex(this.getRoot());\n    }\n    /**\n     * @dev It returns the hash value of a node for the given position. Note that the index starts from 1.\n     */\n    getNode(index) {\n        return this.hashes[index];\n    }\n    /**\n     * @desc It returns the height of the highest peak.\n     */\n    mountainHeight(size) {\n        let height = 1;\n        while (1 << height <= size + height) {\n            height++;\n        }\n        return height - 1;\n    }\n    /**\n     * @desc It returns the height of the index.\n     */\n    heightAt(index) {\n        let reducedIndex = index;\n        let peakIndex = 0;\n        let height = 0;\n        // if an index has a left mountain then subtract the mountain\n        while (reducedIndex > peakIndex) {\n            reducedIndex -= (1 << height) - 1;\n            height = this.mountainHeight(reducedIndex);\n            peakIndex = (1 << height) - 1;\n        }\n        // index is on the right slope\n        return height - (peakIndex - reducedIndex);\n    }\n    /**\n     * @desc It returns whether the index is the leaf node or not\n     */\n    isLeaf(index) {\n        return this.heightAt(index) === 1;\n    }\n    /**\n     * @desc It returns the children when it is a parent node.\n     */\n    getChildren(index) {\n        const left = index - (1 << (this.heightAt(index) - 1));\n        const right = index - 1;\n        if (left === right) {\n            throw new Error('not a parent');\n        }\n        return [left, right];\n    }\n    /**\n     * @desc It returns a merkle proof for a leaf. Note that the index starts from 1.\n     */\n    getMerkleProof(index) {\n        if (index > this.size) {\n            throw new Error('out of range');\n        }\n        if (!this.isLeaf(index)) {\n            throw new Error('not a leaf');\n        }\n        const root = this.root;\n        const width = this.width;\n        // find all peaks for bagging\n        const peaks = this.getPeakIndexes(this.width);\n        const peakBagging = [];\n        let cursor = 0;\n        for (let i = 0; i < peaks.length; i++) {\n            // collect the hash of all peaks\n            peakBagging[i] = this.hashes[peaks[i]];\n            // find the peak which includes the target index\n            if (peaks[i] >= index && cursor === 0) {\n                cursor = peaks[i];\n            }\n        }\n        let left = 0;\n        let right = 0;\n        // get hashes of the siblings in the mountain which the index belgons to.\n        // it moves the cursor from the summit of the mountain down to the target index\n        let height = this.heightAt(cursor);\n        const siblings = [];\n        while (cursor !== index) {\n            height--;\n            ([left, right] = this.getChildren(cursor));\n            // move the cursor down to the left size or right size\n            cursor = index <= left ? left : right;\n            // remaining node is the sibling\n            siblings[height - 1] = this.hashes[index <= left ? right : left];\n        }\n        return {\n            root,\n            width,\n            peakBagging,\n            siblings\n        };\n    }\n    /**\n     * @desc It returns true when the given params verifies that the given value exists in the tree or reverts the transaction.\n     */\n    verify(root, width, index, value, peaks, siblings) {\n        value = this.bufferify(value);\n        const size = this.getSize(width);\n        if (size < index) {\n            throw new Error('index is out of range');\n        }\n        // check the root equals the peak bagging hash\n        if (!root.equals(this.peakBagging(width, peaks))) {\n            throw new Error('invalid root hash from the peaks');\n        }\n        // find the mountain where the target index belongs to\n        let cursor = 0;\n        let targetPeak;\n        const peakIndexes = this.getPeakIndexes(width);\n        for (let i = 0; i < peakIndexes.length; i++) {\n            if (peakIndexes[i] >= index) {\n                targetPeak = peaks[i];\n                cursor = peakIndexes[i];\n                break;\n            }\n        }\n        if (!targetPeak) {\n            throw new Error('target not found');\n        }\n        // find the path climbing down\n        let height = siblings.length + 1;\n        const path = new Array(height);\n        let left = 0;\n        let right = 0;\n        while (height > 0) {\n            // record the current cursor and climb down\n            path[--height] = cursor;\n            if (cursor === index) {\n                // on the leaf node. Stop climbing down\n                break;\n            }\n            else {\n                // on the parent node. Go left or right\n                ([left, right] = this.getChildren(cursor));\n                cursor = index > left ? right : left;\n                continue;\n            }\n        }\n        // calculate the summit hash climbing up again\n        let node;\n        while (height < path.length) {\n            // move cursor\n            cursor = path[height];\n            if (height === 0) {\n                // cusor is on the leaf\n                node = this.hashLeaf(cursor, this.hashFn(value));\n            }\n            else if (cursor - 1 === path[height - 1]) {\n                // cursor is on a parent and a siblings is on the left\n                node = this.hashBranch(cursor, siblings[height - 1], node);\n            }\n            else {\n                // cursor is on a parent and a siblings is on the right\n                node = this.hashBranch(cursor, node, siblings[height - 1]);\n            }\n            // climb up\n            height++;\n        }\n        // computed hash value of the summit should equal to the target peak hash\n        if (!node.equals(targetPeak)) {\n            throw new Error('hashed peak is invalid');\n        }\n        return true;\n    }\n    peaksToPeakMap(width, peaks) {\n        const peakMap = {};\n        let bitIndex = 0;\n        let peakRef = 0;\n        let count = peaks.length;\n        for (let height = 1; height <= 32; height++) {\n            // index starts from the right most bit\n            bitIndex = 32 - height;\n            peakRef = 1 << (height - 1);\n            if ((width & peakRef) !== 0) {\n                peakMap[bitIndex] = peaks[--count];\n            }\n            else {\n                peakMap[bitIndex] = 0;\n            }\n        }\n        if (count !== 0) {\n            throw new Error('invalid number of peaks');\n        }\n        return peakMap;\n    }\n    peakMapToPeaks(width, peakMap) {\n        const arrLength = this.numOfPeaks(width);\n        const peaks = new Array(arrLength);\n        let count = 0;\n        for (let i = 0; i < 32; i++) {\n            if (peakMap[i] !== 0) {\n                peaks[count++] = peakMap[i];\n            }\n        }\n        if (count !== arrLength) {\n            throw new Error('invalid number of peaks');\n        }\n        return peaks;\n    }\n    peakUpdate(width, prevPeakMap, itemHash) {\n        const nextPeakMap = {};\n        const newWidth = width + 1;\n        let cursorIndex = this.getLeafIndex(newWidth);\n        let cursorNode = this.hashLeaf(cursorIndex, itemHash);\n        let bitIndex = 0;\n        let peakRef = 0;\n        let prevPeakExist = false;\n        let nextPeakExist = false;\n        let obtained = false;\n        for (let height = 1; height <= 32; height++) {\n            // index starts from the right most bit\n            bitIndex = 32 - height;\n            if (obtained) {\n                nextPeakMap[bitIndex] = prevPeakMap[bitIndex];\n            }\n            else {\n                peakRef = 1 << (height - 1);\n                prevPeakExist = (width & peakRef) !== 0;\n                nextPeakExist = (newWidth & peakRef) !== 0;\n                // get new cursor node with hashing the peak and the current cursor\n                cursorIndex++;\n                if (prevPeakExist) {\n                    cursorNode = this.hashBranch(cursorIndex, prevPeakMap[bitIndex], cursorNode);\n                }\n                // if new peak exists for the bit index\n                if (nextPeakExist) {\n                    // if prev peak exists for the bit index\n                    if (prevPeakExist) {\n                        nextPeakMap[bitIndex] = prevPeakMap[bitIndex];\n                    }\n                    else {\n                        nextPeakMap[bitIndex] = cursorNode;\n                    }\n                    obtained = true;\n                }\n                else {\n                    nextPeakMap[bitIndex] = 0;\n                }\n            }\n        }\n        return nextPeakMap;\n    }\n    rollUp(root, width, peaks, itemHashes) {\n        // check the root equals the peak bagging hash\n        if (!root.equals(this.peakBagging(width, peaks))) {\n            throw new Error('invalid root hash from the peaks');\n        }\n        let tmpWidth = width;\n        let tmpPeakMap = this.peaksToPeakMap(width, peaks);\n        for (let i = 0; i < itemHashes.length; i++) {\n            tmpPeakMap = this.peakUpdate(tmpWidth, tmpPeakMap, itemHashes[i]);\n            tmpWidth++;\n        }\n        return this.peakBagging(tmpWidth, this.peakMapToPeaks(tmpWidth, tmpPeakMap));\n    }\n    /**\n     * @desc It returns the hash value of the node for the index.\n     *      If the hash already exists it simply returns the stored value. On the other hand,\n     *      it computes hashes recursively downward.\n     *      Only appending an item calls this function.\n     */\n    _getOrCreateNode(index) {\n        if (index > this.size) {\n            throw new Error('out of range');\n        }\n        if (!this.hashes[index]) {\n            const [leftIndex, rightIndex] = this.getChildren(index);\n            const leftHash = this._getOrCreateNode(leftIndex);\n            const rightHash = this._getOrCreateNode(rightIndex);\n            this.hashes[index] = this.hashBranch(index, leftHash, rightHash);\n        }\n        return this.hashes[index];\n    }\n}\nexports.MerkleMountainRange = MerkleMountainRange;\nif (typeof window !== 'undefined') {\n    ;\n    window.MerkleMountainRange = MerkleMountainRange;\n}\nexports[\"default\"] = MerkleMountainRange;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/merkletreejs/dist/MerkleMountainRange.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/merkletreejs/dist/MerkleRadixTree.js":
/*!************************************************************!*\
  !*** ../node_modules/merkletreejs/dist/MerkleRadixTree.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MerkleRadixTree = void 0;\nconst Base_1 = __importDefault(__webpack_require__(/*! ./Base */ \"(rsc)/../node_modules/merkletreejs/dist/Base.js\"));\nclass MerkleRadixNode {\n    constructor(key = '', value = null, hashFn) {\n        this.key = key;\n        this.value = value;\n        this.children = new Map();\n        this.hashFn = hashFn;\n        this.hash = this.computeHash();\n    }\n    computeHash() {\n        let hash = this.hashFn('');\n        hash = Buffer.concat([hash, Base_1.default.bufferify(this.key), this.value != null ? Base_1.default.bufferify(this.value) : Buffer.alloc(0)]);\n        for (const child of this.children.values()) {\n            hash = Buffer.concat([hash, child.hash]);\n        }\n        const result = this.hashFn(hash);\n        return result;\n    }\n    updateHash() {\n        this.hash = this.computeHash();\n    }\n}\nclass MerkleRadixTree extends Base_1.default {\n    constructor(hashFn) {\n        super();\n        this.hashFn = this.bufferifyFn(hashFn);\n        this.root = new MerkleRadixNode('', null, this.hashFn);\n    }\n    insert(key, value) {\n        let node = this.root;\n        let commonPrefixLength = 0;\n        while (key.length > 0) {\n            const child = [...node.children.values()].find(child => key.startsWith(child.key));\n            if (!child) {\n                node.children.set(key, new MerkleRadixNode(key, value, this.hashFn));\n                node.updateHash(); // Update the hash of the current node\n                return;\n            }\n            commonPrefixLength = this.commonPrefixLength(key, child.key);\n            if (commonPrefixLength === child.key.length) {\n                node = child;\n                key = key.slice(commonPrefixLength);\n            }\n            else {\n                const commonPrefix = key.slice(0, commonPrefixLength);\n                const childSuffix = child.key.slice(commonPrefixLength);\n                const newNode = new MerkleRadixNode(commonPrefix, null, this.hashFn);\n                node.children.delete(child.key);\n                node.children.set(commonPrefix, newNode);\n                newNode.children.set(childSuffix, child);\n                child.key = childSuffix;\n                if (commonPrefixLength < key.length) {\n                    const suffix = key.slice(commonPrefixLength);\n                    newNode.children.set(suffix, new MerkleRadixNode(suffix, value, this.hashFn));\n                }\n                else {\n                    newNode.value = value;\n                }\n                node.updateHash();\n                newNode.updateHash(); // Update the hash of the new node\n                return;\n            }\n        }\n        node.value = value;\n        node.updateHash(); // Update the hash of the node where the value was inserted\n    }\n    lookup(key) {\n        let node = this.root;\n        while (key.length > 0) {\n            const child = [...node.children.values()].find(child => key.startsWith(child.key));\n            if (!child) {\n                return null;\n            }\n            const commonPrefixLength = this.commonPrefixLength(key, child.key);\n            if (commonPrefixLength === child.key.length) {\n                node = child;\n                key = key.slice(commonPrefixLength);\n            }\n            else {\n                return null;\n            }\n        }\n        return node.value;\n    }\n    commonPrefixLength(str1, str2) {\n        let length = 0;\n        while (length < str1.length && length < str2.length && str1[length] === str2[length]) {\n            length++;\n        }\n        return length;\n    }\n    generateProof(key) {\n        let node = this.root;\n        const proof = [];\n        while (key.length > 0) {\n            const siblings = [];\n            for (const child of node.children.values()) {\n                if (child.key !== key) {\n                    siblings.push({\n                        key: child.key,\n                        hash: child.hash\n                    });\n                }\n            }\n            proof.push({\n                key: node.key,\n                hash: node.hash,\n                siblings\n            });\n            const child = [...node.children.values()].find(child => key.startsWith(child.key));\n            if (!child) {\n                return null;\n            }\n            const commonPrefixLength = this.commonPrefixLength(key, child.key);\n            if (commonPrefixLength === child.key.length) {\n                node = child;\n                key = key.slice(commonPrefixLength);\n            }\n            else {\n                return null;\n            }\n        }\n        proof.push({\n            key: node.key,\n            hash: node.hash,\n            siblings: []\n        });\n        return proof;\n    }\n    verifyProof(proof, rootHash) {\n        if (!proof || proof.length === 0) {\n            return false;\n        }\n        let currentHash = proof[proof.length - 1].hash;\n        for (let i = proof.length - 2; i >= 0; i--) {\n            const item = proof[i];\n            let concatenatedHash = Buffer.concat([this.hashFn(''), this.bufferify(item.key), currentHash]);\n            for (const sibling of item.siblings) {\n                concatenatedHash = Buffer.concat([concatenatedHash, sibling.hash]);\n            }\n            currentHash = this.hashFn(concatenatedHash);\n        }\n        return currentHash.equals(rootHash);\n    }\n}\nexports.MerkleRadixTree = MerkleRadixTree;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/merkletreejs/dist/MerkleRadixTree.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/merkletreejs/dist/MerkleSumTree.js":
/*!**********************************************************!*\
  !*** ../node_modules/merkletreejs/dist/MerkleSumTree.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MerkleSumTree = exports.ProofStep = exports.Leaf = exports.Bucket = void 0;\nconst Base_1 = __webpack_require__(/*! ./Base */ \"(rsc)/../node_modules/merkletreejs/dist/Base.js\");\nclass Bucket {\n    constructor(size, hashed) {\n        this.size = BigInt(size);\n        this.hashed = hashed;\n        // each node in the tree can have a parent, and a left or right sibling\n        this.parent = null;\n        this.left = null;\n        this.right = null;\n    }\n}\nexports.Bucket = Bucket;\nclass Leaf {\n    constructor(hashFn, rng, data) {\n        this.hashFn = hashFn;\n        this.rng = rng.map(x => BigInt(x));\n        this.data = data;\n    }\n    getBucket() {\n        let hashed;\n        if (this.data) {\n            hashed = this.hashFn(this.data);\n        }\n        else {\n            hashed = Buffer.alloc(32);\n        }\n        return new Bucket(BigInt(this.rng[1]) - BigInt(this.rng[0]), hashed);\n    }\n}\nexports.Leaf = Leaf;\nclass ProofStep {\n    constructor(bucket, right) {\n        this.bucket = bucket;\n        this.right = right; // whether the bucket hash should be appeded on the right side in this step (default is left\n    }\n}\nexports.ProofStep = ProofStep;\nclass MerkleSumTree extends Base_1.Base {\n    constructor(leaves, hashFn) {\n        super();\n        this.leaves = leaves;\n        this.hashFn = hashFn;\n        MerkleSumTree.checkConsecutive(leaves);\n        this.buckets = [];\n        for (const l of leaves) {\n            this.buckets.push(l.getBucket());\n        }\n        let buckets = [];\n        for (const bucket of this.buckets) {\n            buckets.push(bucket);\n        }\n        while (buckets.length !== 1) {\n            const newBuckets = [];\n            while (buckets.length) {\n                if (buckets.length >= 2) {\n                    const b1 = buckets.shift();\n                    const b2 = buckets.shift();\n                    const size = b1.size + b2.size;\n                    const hashed = this.hashFn(Buffer.concat([this.sizeToBuffer(b1.size), this.bufferify(b1.hashed), this.sizeToBuffer(b2.size), this.bufferify(b2.hashed)]));\n                    const b = new Bucket(size, hashed);\n                    b2.parent = b;\n                    b1.parent = b2.parent;\n                    b1.right = b2;\n                    b2.left = b1;\n                    newBuckets.push(b);\n                }\n                else {\n                    newBuckets.push(buckets.shift());\n                }\n            }\n            buckets = newBuckets;\n        }\n        this.root = buckets[0];\n    }\n    sizeToBuffer(size) {\n        const buf = Buffer.alloc(8);\n        const view = new DataView(buf.buffer);\n        view.setBigInt64(0, BigInt(size), false); // true when little endian\n        return buf;\n    }\n    static checkConsecutive(leaves) {\n        let curr = BigInt(0);\n        for (const leaf of leaves) {\n            if (leaf.rng[0] !== curr) {\n                throw new Error('leaf ranges are invalid');\n            }\n            curr = BigInt(leaf.rng[1]);\n        }\n    }\n    // gets inclusion/exclusion proof of a bucket in the specified index\n    getProof(index) {\n        let curr = this.buckets[Number(index)];\n        const proof = [];\n        while (curr && curr.parent) {\n            const right = !!curr.right;\n            const bucket = curr.right ? curr.right : curr.left;\n            curr = curr.parent;\n            proof.push(new ProofStep(bucket, right));\n        }\n        return proof;\n    }\n    sum(arr) {\n        let total = BigInt(0);\n        for (const value of arr) {\n            total += BigInt(value);\n        }\n        return total;\n    }\n    // validates the suppplied proof for a specified leaf according to the root bucket\n    verifyProof(root, leaf, proof) {\n        const rng = [this.sum(proof.filter(x => !x.right).map(x => x.bucket.size)), BigInt(root.size) - this.sum(proof.filter(x => x.right).map(x => x.bucket.size))];\n        if (!(rng[0] === leaf.rng[0] && rng[1] === leaf.rng[1])) {\n            // supplied steps are not routing to the range specified\n            return false;\n        }\n        let curr = leaf.getBucket();\n        let hashed;\n        for (const step of proof) {\n            if (step.right) {\n                hashed = this.hashFn(Buffer.concat([this.sizeToBuffer(curr.size), this.bufferify(curr.hashed), this.sizeToBuffer(step.bucket.size), this.bufferify(step.bucket.hashed)]));\n            }\n            else {\n                hashed = this.hashFn(Buffer.concat([this.sizeToBuffer(step.bucket.size), this.bufferify(step.bucket.hashed), this.sizeToBuffer(curr.size), this.bufferify(curr.hashed)]));\n            }\n            curr = new Bucket(BigInt(curr.size) + BigInt(step.bucket.size), hashed);\n        }\n        return curr.size === root.size && curr.hashed.toString('hex') === root.hashed.toString('hex');\n    }\n}\nexports.MerkleSumTree = MerkleSumTree;\nif (typeof window !== 'undefined') {\n    ;\n    window.MerkleSumTree = MerkleSumTree;\n}\nexports[\"default\"] = MerkleSumTree;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/merkletreejs/dist/MerkleSumTree.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/merkletreejs/dist/MerkleTree.js":
/*!*******************************************************!*\
  !*** ../node_modules/merkletreejs/dist/MerkleTree.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MerkleTree = void 0;\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst buffer_reverse_1 = __importDefault(__webpack_require__(/*! buffer-reverse */ \"(rsc)/../node_modules/buffer-reverse/index.js\"));\nconst sha256_1 = __importDefault(__webpack_require__(/*! crypto-js/sha256 */ \"(rsc)/../node_modules/crypto-js/sha256.js\"));\nconst treeify_1 = __importDefault(__webpack_require__(/*! treeify */ \"(rsc)/../node_modules/treeify/treeify.js\"));\nconst Base_1 = __importDefault(__webpack_require__(/*! ./Base */ \"(rsc)/../node_modules/merkletreejs/dist/Base.js\"));\n/**\n * Class reprensenting a Merkle Tree\n * @namespace MerkleTree\n */\nclass MerkleTree extends Base_1.default {\n    /**\n     * @desc Constructs a Merkle Tree.\n     * All nodes and leaves are stored as Buffers.\n     * Lonely leaf nodes are promoted to the next level up without being hashed again.\n     * @param {Buffer[]} leaves - Array of hashed leaves. Each leaf must be a Buffer.\n     * @param {Function} hashFunction - Hash function to use for hashing leaves and nodes\n     * @param {Object} options - Additional options\n     * @example\n     *```js\n     *const MerkleTree = require('merkletreejs')\n     *const crypto = require('crypto')\n     *\n     *function sha256(data) {\n     *  // returns Buffer\n     *  return crypto.createHash('sha256').update(data).digest()\n     *}\n     *\n     *const leaves = ['a', 'b', 'c'].map(value => keccak(value))\n     *\n     *const tree = new MerkleTree(leaves, sha256)\n     *```\n     */\n    constructor(leaves, hashFn = sha256_1.default, options = {}) {\n        super();\n        this.duplicateOdd = false;\n        this.hashLeaves = false;\n        this.isBitcoinTree = false;\n        this.leaves = [];\n        this.layers = [];\n        this.sortLeaves = false;\n        this.sortPairs = false;\n        this.sort = false;\n        this.fillDefaultHash = null;\n        this.complete = false;\n        if (options.complete) {\n            if (options.isBitcoinTree) {\n                throw new Error('option \"complete\" is incompatible with \"isBitcoinTree\"');\n            }\n            if (options.duplicateOdd) {\n                throw new Error('option \"complete\" is incompatible with \"duplicateOdd\"');\n            }\n        }\n        this.isBitcoinTree = !!options.isBitcoinTree;\n        this.hashLeaves = !!options.hashLeaves;\n        this.sortLeaves = !!options.sortLeaves;\n        this.sortPairs = !!options.sortPairs;\n        this.complete = !!options.complete;\n        if (options.fillDefaultHash) {\n            if (typeof options.fillDefaultHash === 'function') {\n                this.fillDefaultHash = options.fillDefaultHash;\n            }\n            else if (buffer_1.Buffer.isBuffer(options.fillDefaultHash) || typeof options.fillDefaultHash === 'string') {\n                this.fillDefaultHash = (idx, hashFn) => options.fillDefaultHash;\n            }\n            else {\n                throw new Error('method \"fillDefaultHash\" must be a function, Buffer, or string');\n            }\n        }\n        this.sort = !!options.sort;\n        if (this.sort) {\n            this.sortLeaves = true;\n            this.sortPairs = true;\n        }\n        this.duplicateOdd = !!options.duplicateOdd;\n        if (options.concatenator) {\n            this.concatenator = options.concatenator;\n        }\n        else {\n            this.concatenator = buffer_1.Buffer.concat;\n        }\n        if (typeof hashFn !== 'function') {\n            throw new Error('hashFn must be a function');\n        }\n        this.hashFn = this.bufferifyFn(hashFn);\n        this.processLeaves(leaves);\n    }\n    getOptions() {\n        var _a, _b;\n        return {\n            complete: this.complete,\n            isBitcoinTree: this.isBitcoinTree,\n            hashLeaves: this.hashLeaves,\n            sortLeaves: this.sortLeaves,\n            sortPairs: this.sortPairs,\n            sort: this.sort,\n            fillDefaultHash: (_b = (_a = this.fillDefaultHash) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : null,\n            duplicateOdd: this.duplicateOdd\n        };\n    }\n    processLeaves(leaves) {\n        if (this.hashLeaves) {\n            leaves = leaves.map(this.hashFn);\n        }\n        this.leaves = leaves.map(this.bufferify);\n        if (this.sortLeaves) {\n            this.leaves = this.leaves.sort(buffer_1.Buffer.compare);\n        }\n        if (this.fillDefaultHash) {\n            for (let i = this.leaves.length; i < Math.pow(2, Math.ceil(Math.log2(this.leaves.length))); i++) {\n                this.leaves.push(this.bufferify(this.fillDefaultHash(i, this.hashFn)));\n            }\n        }\n        this.createHashes(this.leaves);\n    }\n    createHashes(nodes) {\n        this.layers = [nodes];\n        while (nodes.length > 1) {\n            const layerIndex = this.layers.length;\n            this.layers.push([]);\n            const layerLimit = this.complete && layerIndex === 1 && !Number.isInteger(Math.log2(nodes.length))\n                ? (2 * nodes.length) - (Math.pow(2, Math.ceil(Math.log2(nodes.length))))\n                : nodes.length;\n            for (let i = 0; i < nodes.length; i += 2) {\n                if (i >= layerLimit) {\n                    this.layers[layerIndex].push(...nodes.slice(layerLimit));\n                    break;\n                }\n                else if (i + 1 === nodes.length) {\n                    if (nodes.length % 2 === 1) {\n                        const data = nodes[nodes.length - 1];\n                        let hash = data;\n                        // is bitcoin tree\n                        if (this.isBitcoinTree) {\n                            // Bitcoin method of duplicating the odd ending nodes\n                            hash = this.hashFn(this.concatenator([buffer_reverse_1.default(data), buffer_reverse_1.default(data)]));\n                            hash = buffer_reverse_1.default(this.hashFn(hash));\n                            this.layers[layerIndex].push(hash);\n                            continue;\n                        }\n                        else {\n                            if (this.duplicateOdd) {\n                                // continue with creating layer\n                            }\n                            else {\n                                // push copy of hash and continue iteration\n                                this.layers[layerIndex].push(nodes[i]);\n                                continue;\n                            }\n                        }\n                    }\n                }\n                const left = nodes[i];\n                const right = i + 1 === nodes.length ? left : nodes[i + 1];\n                let combined = null;\n                if (this.isBitcoinTree) {\n                    combined = [buffer_reverse_1.default(left), buffer_reverse_1.default(right)];\n                }\n                else {\n                    combined = [left, right];\n                }\n                if (this.sortPairs) {\n                    combined.sort(buffer_1.Buffer.compare);\n                }\n                let hash = this.hashFn(this.concatenator(combined));\n                // double hash if bitcoin tree\n                if (this.isBitcoinTree) {\n                    hash = buffer_reverse_1.default(this.hashFn(hash));\n                }\n                this.layers[layerIndex].push(hash);\n            }\n            nodes = this.layers[layerIndex];\n        }\n    }\n    /**\n     * addLeaf\n     * @desc Adds a leaf to the tree and re-calculates layers.\n     * @param {String|Buffer} - Leaf\n     * @param {Boolean} - Set to true if the leaf should be hashed before being added to tree.\n     * @example\n     *```js\n     *tree.addLeaf(newLeaf)\n     *```\n     */\n    addLeaf(leaf, shouldHash = false) {\n        if (shouldHash) {\n            leaf = this.hashFn(leaf);\n        }\n        this.processLeaves(this.leaves.concat(leaf));\n    }\n    /**\n     * addLeaves\n     * @desc Adds multiple leaves to the tree and re-calculates layers.\n     * @param {String[]|Buffer[]} - Array of leaves\n     * @param {Boolean} - Set to true if the leaves should be hashed before being added to tree.\n     * @example\n     *```js\n     *tree.addLeaves(newLeaves)\n     *```\n     */\n    addLeaves(leaves, shouldHash = false) {\n        if (shouldHash) {\n            leaves = leaves.map(this.hashFn);\n        }\n        this.processLeaves(this.leaves.concat(leaves));\n    }\n    /**\n     * getLeaves\n     * @desc Returns array of leaves of Merkle Tree.\n     * @return {Buffer[]}\n     * @example\n     *```js\n     *const leaves = tree.getLeaves()\n     *```\n     */\n    getLeaves(values) {\n        if (Array.isArray(values)) {\n            if (this.hashLeaves) {\n                values = values.map(this.hashFn);\n                if (this.sortLeaves) {\n                    values = values.sort(buffer_1.Buffer.compare);\n                }\n            }\n            return this.leaves.filter(leaf => this.bufferIndexOf(values, leaf, this.sortLeaves) !== -1);\n        }\n        return this.leaves;\n    }\n    removeLeaf(index) {\n        if (!this.isValidLeafIndex(index)) {\n            throw new Error(`\"${index}\" is not a valid leaf index. Expected to be [0, ${this.getLeafCount() - 1}]`);\n        }\n        const result = this.leaves.splice(index, 1);\n        this.processLeaves(this.leaves);\n        return result[0];\n    }\n    updateLeaf(index, value, shouldHash = false) {\n        if (!this.isValidLeafIndex(index)) {\n            throw new Error(`\"${index}\" is not a valid leaf index. Expected to be [0, ${this.getLeafCount() - 1}]`);\n        }\n        if (shouldHash)\n            value = this.hashFn(value);\n        this.leaves[index] = value;\n        this.processLeaves(this.leaves);\n    }\n    /**\n     * getLeaf\n     * @desc Returns the leaf at the given index.\n     * @param {Number} - Index number\n     * @return {Buffer}\n     * @example\n     *```js\n     *const leaf = tree.getLeaf(1)\n     *```\n     */\n    getLeaf(index) {\n        if (index < 0 || index > this.leaves.length - 1) {\n            return buffer_1.Buffer.from([]);\n        }\n        return this.leaves[index];\n    }\n    /**\n     * getLeafIndex\n     * @desc Returns the index of the given leaf, or -1 if the leaf is not found.\n     * @param {String|Buffer} - Target leaf\n     * @return {number}\n     * @example\n     *```js\n     *const leaf = Buffer.from('abc')\n     *const index = tree.getLeafIndex(leaf)\n     *```\n     */\n    getLeafIndex(target) {\n        target = this.bufferify(target);\n        const leaves = this.getLeaves();\n        for (let i = 0; i < leaves.length; i++) {\n            const leaf = leaves[i];\n            if (leaf.equals(target)) {\n                return i;\n            }\n        }\n        return -1;\n    }\n    /**\n     * getLeafCount\n     * @desc Returns the total number of leaves.\n     * @return {number}\n     * @example\n     *```js\n     *const count = tree.getLeafCount()\n     *```\n     */\n    getLeafCount() {\n        return this.leaves.length;\n    }\n    /**\n     * getHexLeaves\n     * @desc Returns array of leaves of Merkle Tree as hex strings.\n     * @return {String[]}\n     * @example\n     *```js\n     *const leaves = tree.getHexLeaves()\n     *```\n     */\n    getHexLeaves() {\n        return this.leaves.map(leaf => this.bufferToHex(leaf));\n    }\n    /**\n     * marshalLeaves\n     * @desc Returns array of leaves of Merkle Tree as a JSON string.\n     * @param {String[]|Buffer[]} - Merkle tree leaves\n     * @return {String} - List of leaves as JSON string\n     * @example\n     *```js\n     *const jsonStr = MerkleTree.marshalLeaves(leaves)\n     *```\n     */\n    static marshalLeaves(leaves) {\n        return JSON.stringify(leaves.map(leaf => MerkleTree.bufferToHex(leaf)), null, 2);\n    }\n    /**\n     * unmarshalLeaves\n     * @desc Returns array of leaves of Merkle Tree as a Buffers.\n     * @param {String|Object} - JSON stringified leaves\n     * @return {Buffer[]} - Unmarshalled list of leaves\n     * @example\n     *```js\n     *const leaves = MerkleTree.unmarshalLeaves(jsonStr)\n     *```\n     */\n    static unmarshalLeaves(jsonStr) {\n        let parsed = null;\n        if (typeof jsonStr === 'string') {\n            parsed = JSON.parse(jsonStr);\n        }\n        else if (jsonStr instanceof Object) {\n            parsed = jsonStr;\n        }\n        else {\n            throw new Error('Expected type of string or object');\n        }\n        if (!parsed) {\n            return [];\n        }\n        if (!Array.isArray(parsed)) {\n            throw new Error('Expected JSON string to be array');\n        }\n        return parsed.map(MerkleTree.bufferify);\n    }\n    /**\n     * getLayers\n     * @desc Returns multi-dimensional array of all layers of Merkle Tree, including leaves and root.\n     * @return {Buffer[][]}\n     * @example\n     *```js\n     *const layers = tree.getLayers()\n     *```\n     */\n    getLayers() {\n        return this.layers;\n    }\n    /**\n     * getHexLayers\n     * @desc Returns multi-dimensional array of all layers of Merkle Tree, including leaves and root as hex strings.\n     * @return {String[][]}\n     * @example\n     *```js\n     *const layers = tree.getHexLayers()\n     *```\n     */\n    getHexLayers() {\n        return this.layers.reduce((acc, item) => {\n            if (Array.isArray(item)) {\n                acc.push(item.map(layer => this.bufferToHex(layer)));\n            }\n            else {\n                acc.push(item);\n            }\n            return acc;\n        }, []);\n    }\n    /**\n     * getLayersFlat\n     * @desc Returns single flat array of all layers of Merkle Tree, including leaves and root.\n     * @return {Buffer[]}\n     * @example\n     *```js\n     *const layers = tree.getLayersFlat()\n     *```\n     */\n    getLayersFlat() {\n        const layers = this.layers.reduce((acc, item) => {\n            if (Array.isArray(item)) {\n                acc.unshift(...item);\n            }\n            else {\n                acc.unshift(item);\n            }\n            return acc;\n        }, []);\n        layers.unshift(buffer_1.Buffer.from([0]));\n        return layers;\n    }\n    /**\n     * getHexLayersFlat\n     * @desc Returns single flat array of all layers of Merkle Tree, including leaves and root as hex string.\n     * @return {String[]}\n     * @example\n     *```js\n     *const layers = tree.getHexLayersFlat()\n     *```\n     */\n    getHexLayersFlat() {\n        return this.getLayersFlat().map(layer => this.bufferToHex(layer));\n    }\n    /**\n     * getLayerCount\n     * @desc Returns the total number of layers.\n     * @return {number}\n     * @example\n     *```js\n     *const count = tree.getLayerCount()\n     *```\n     */\n    getLayerCount() {\n        return this.getLayers().length;\n    }\n    /**\n     * getRoot\n     * @desc Returns the Merkle root hash as a Buffer.\n     * @return {Buffer}\n     * @example\n     *```js\n     *const root = tree.getRoot()\n     *```\n     */\n    getRoot() {\n        if (this.layers.length === 0) {\n            return buffer_1.Buffer.from([]);\n        }\n        return this.layers[this.layers.length - 1][0] || buffer_1.Buffer.from([]);\n    }\n    /**\n     * getHexRoot\n     * @desc Returns the Merkle root hash as a hex string.\n     * @return {String}\n     * @example\n     *```js\n     *const root = tree.getHexRoot()\n     *```\n     */\n    getHexRoot() {\n        return this.bufferToHex(this.getRoot());\n    }\n    /**\n     * getProof\n     * @desc Returns the proof for a target leaf.\n     * @param {Buffer} leaf - Target leaf\n     * @param {Number} [index] - Target leaf index in leaves array.\n     * Use if there are leaves containing duplicate data in order to distinguish it.\n     * @return {Object[]} - Array of objects containing a position property of type string\n     * with values of 'left' or 'right' and a data property of type Buffer.\n     * @example\n     * ```js\n     *const proof = tree.getProof(leaves[2])\n     *```\n     *\n     * @example\n     *```js\n     *const leaves = ['a', 'b', 'a'].map(value => keccak(value))\n     *const tree = new MerkleTree(leaves, keccak)\n     *const proof = tree.getProof(leaves[2], 2)\n     *```\n     */\n    getProof(leaf, index) {\n        if (typeof leaf === 'undefined') {\n            throw new Error('leaf is required');\n        }\n        leaf = this.bufferify(leaf);\n        const proof = [];\n        if (!Number.isInteger(index)) {\n            index = -1;\n            for (let i = 0; i < this.leaves.length; i++) {\n                if (buffer_1.Buffer.compare(leaf, this.leaves[i]) === 0) {\n                    index = i;\n                }\n            }\n        }\n        if (index <= -1) {\n            return [];\n        }\n        for (let i = 0; i < this.layers.length; i++) {\n            const layer = this.layers[i];\n            const isRightNode = index % 2;\n            const pairIndex = (isRightNode ? index - 1\n                : this.isBitcoinTree && index === layer.length - 1 && i < this.layers.length - 1\n                    // Proof Generation for Bitcoin Trees\n                    ? index\n                    // Proof Generation for Non-Bitcoin Trees\n                    : index + 1);\n            if (pairIndex < layer.length) {\n                proof.push({\n                    position: isRightNode ? 'left' : 'right',\n                    data: layer[pairIndex]\n                });\n            }\n            // set index to parent index\n            index = (index / 2) | 0;\n        }\n        return proof;\n    }\n    /**\n     * getHexProof\n     * @desc Returns the proof for a target leaf as hex strings.\n     * @param {Buffer} leaf - Target leaf\n     * @param {Number} [index] - Target leaf index in leaves array.\n     * Use if there are leaves containing duplicate data in order to distinguish it.\n     * @return {String[]} - Proof array as hex strings.\n     * @example\n     * ```js\n     *const proof = tree.getHexProof(leaves[2])\n     *```\n     */\n    getHexProof(leaf, index) {\n        return this.getProof(leaf, index).map(item => this.bufferToHex(item.data));\n    }\n    /**\n     * getProofs\n     * @desc Returns the proofs for all leaves.\n     * @return {Object[]} - Array of objects containing a position property of type string\n     * with values of 'left' or 'right' and a data property of type Buffer for all leaves.\n     * @example\n     * ```js\n     *const proofs = tree.getProofs()\n     *```\n     *\n     * @example\n     *```js\n     *const leaves = ['a', 'b', 'a'].map(value => keccak(value))\n     *const tree = new MerkleTree(leaves, keccak)\n     *const proofs = tree.getProofs()\n     *```\n     */\n    getProofs() {\n        const proof = [];\n        const proofs = [];\n        this.getProofsDFS(this.layers.length - 1, 0, proof, proofs);\n        return proofs;\n    }\n    /**\n     * getProofsDFS\n     * @desc Get all proofs through single traverse\n     * @param {Number} currentLayer - Current layer index in traverse.\n     * @param {Number} index - Current tarvese node index in traverse.\n     * @param {Object[]} proof - Proof chain for single leaf.\n     * @param {Object[]} proofs - Proofs for all leaves\n     * @example\n     * ```js\n     *const layers = tree.getLayers()\n     *const index = 0;\n     *let proof = [];\n     *let proofs = [];\n     *const proof = tree.getProofsDFS(layers, index, proof, proofs)\n     *```\n     */\n    getProofsDFS(currentLayer, index, proof, proofs) {\n        const isRightNode = index % 2;\n        if (currentLayer === -1) {\n            if (!isRightNode)\n                proofs.push([...proof].reverse());\n            return;\n        }\n        if (index >= this.layers[currentLayer].length)\n            return;\n        const layer = this.layers[currentLayer];\n        const pairIndex = isRightNode ? index - 1 : index + 1;\n        let pushed = false;\n        if (pairIndex < layer.length) {\n            pushed = true;\n            proof.push({\n                position: isRightNode ? 'left' : 'right',\n                data: layer[pairIndex]\n            });\n        }\n        const leftchildIndex = index * 2;\n        const rightchildIndex = index * 2 + 1;\n        this.getProofsDFS(currentLayer - 1, leftchildIndex, proof, proofs);\n        this.getProofsDFS(currentLayer - 1, rightchildIndex, proof, proofs);\n        if (pushed)\n            proof.splice(proof.length - 1, 1);\n    }\n    /**\n     * getHexProofs\n     * @desc Returns the proofs for all leaves as hex strings.\n     * @return {String[]} - Proofs array as hex strings.\n     * @example\n     * ```js\n     *const proofs = tree.getHexProofs()\n     *```\n     */\n    getHexProofs() {\n        return this.getProofs().map(item => this.bufferToHex(item.data));\n    }\n    /**\n    * getPositionalHexProof\n    * @desc Returns the proof for a target leaf as hex strings and the position in binary (left == 0).\n    * @param {Buffer} leaf - Target leaf\n    * @param {Number} [index] - Target leaf index in leaves array.\n    * Use if there are leaves containing duplicate data in order to distinguish it.\n    * @return {(string | number)[][]} - Proof array as hex strings. position at index 0\n    * @example\n    * ```js\n    *const proof = tree.getPositionalHexProof(leaves[2])\n    *```\n    */\n    getPositionalHexProof(leaf, index) {\n        return this.getProof(leaf, index).map(item => {\n            return [\n                item.position === 'left' ? 0 : 1,\n                this.bufferToHex(item.data)\n            ];\n        });\n    }\n    /**\n     * marshalProof\n     * @desc Returns proof array as JSON string.\n     * @param {String[]|Object[]} proof - Merkle tree proof array\n     * @return {String} - Proof array as JSON string.\n     * @example\n     * ```js\n     *const jsonStr = MerkleTree.marshalProof(proof)\n     *```\n     */\n    static marshalProof(proof) {\n        const json = proof.map(item => {\n            if (typeof item === 'string') {\n                return item;\n            }\n            if (buffer_1.Buffer.isBuffer(item)) {\n                return MerkleTree.bufferToHex(item);\n            }\n            return {\n                position: item.position,\n                data: MerkleTree.bufferToHex(item.data)\n            };\n        });\n        return JSON.stringify(json, null, 2);\n    }\n    /**\n     * unmarshalProof\n     * @desc Returns the proof for a target leaf as a list of Buffers.\n     * @param {String|Object} - Merkle tree leaves\n     * @return {String|Object} - Marshalled proof\n     * @example\n     * ```js\n     *const proof = MerkleTree.unmarshalProof(jsonStr)\n     *```\n     */\n    static unmarshalProof(jsonStr) {\n        let parsed = null;\n        if (typeof jsonStr === 'string') {\n            parsed = JSON.parse(jsonStr);\n        }\n        else if (jsonStr instanceof Object) {\n            parsed = jsonStr;\n        }\n        else {\n            throw new Error('Expected type of string or object');\n        }\n        if (!parsed) {\n            return [];\n        }\n        if (!Array.isArray(parsed)) {\n            throw new Error('Expected JSON string to be array');\n        }\n        return parsed.map(item => {\n            if (typeof item === 'string') {\n                return MerkleTree.bufferify(item);\n            }\n            else if (item instanceof Object) {\n                return {\n                    position: item.position,\n                    data: MerkleTree.bufferify(item.data)\n                };\n            }\n            else {\n                throw new Error('Expected item to be of type string or object');\n            }\n        });\n    }\n    static marshalTree(tree) {\n        const root = tree.getHexRoot();\n        const leaves = tree.leaves.map(leaf => MerkleTree.bufferToHex(leaf));\n        const layers = tree.getHexLayers();\n        const options = tree.getOptions();\n        return JSON.stringify({\n            options,\n            root,\n            layers,\n            leaves\n        }, null, 2);\n    }\n    static unmarshalTree(jsonStr, hashFn = sha256_1.default, options = {}) {\n        let parsed = null;\n        if (typeof jsonStr === 'string') {\n            parsed = JSON.parse(jsonStr);\n        }\n        else if (jsonStr instanceof Object) {\n            parsed = jsonStr;\n        }\n        else {\n            throw new Error('Expected type of string or object');\n        }\n        if (!parsed) {\n            throw new Error('could not parse json');\n        }\n        options = Object.assign({}, parsed.options || {}, options);\n        return new MerkleTree(parsed.leaves, hashFn, options);\n    }\n    /**\n     * getProofIndices\n     * @desc Returns the proof indices for given tree indices.\n     * @param {Number[]} treeIndices - Tree indices\n     * @param {Number} depth - Tree depth; number of layers.\n     * @return {Number[]} - Proof indices\n     * @example\n     * ```js\n     *const proofIndices = tree.getProofIndices([2,5,6], 4)\n     *console.log(proofIndices) // [ 23, 20, 19, 8, 3 ]\n     *```\n     */\n    getProofIndices(treeIndices, depth) {\n        const leafCount = Math.pow(2, depth);\n        let maximalIndices = new Set();\n        for (const index of treeIndices) {\n            let x = leafCount + index;\n            while (x > 1) {\n                maximalIndices.add(x ^ 1);\n                x = (x / 2) | 0;\n            }\n        }\n        const a = treeIndices.map(index => leafCount + index);\n        const b = Array.from(maximalIndices).sort((a, b) => a - b).reverse();\n        maximalIndices = a.concat(b);\n        const redundantIndices = new Set();\n        const proof = [];\n        for (let index of maximalIndices) {\n            if (!redundantIndices.has(index)) {\n                proof.push(index);\n                while (index > 1) {\n                    redundantIndices.add(index);\n                    if (!redundantIndices.has(index ^ 1))\n                        break;\n                    index = (index / 2) | 0;\n                }\n            }\n        }\n        return proof.filter(index => {\n            return !treeIndices.includes(index - leafCount);\n        });\n    }\n    getProofIndicesForUnevenTree(sortedLeafIndices, leavesCount) {\n        const depth = Math.ceil(Math.log2(leavesCount));\n        const unevenLayers = [];\n        for (let index = 0; index < depth; index++) {\n            const unevenLayer = leavesCount % 2 !== 0;\n            if (unevenLayer) {\n                unevenLayers.push({ index, leavesCount });\n            }\n            leavesCount = Math.ceil(leavesCount / 2);\n        }\n        const proofIndices = [];\n        let layerNodes = sortedLeafIndices;\n        for (let layerIndex = 0; layerIndex < depth; layerIndex++) {\n            const siblingIndices = layerNodes.map((index) => {\n                if (index % 2 === 0) {\n                    return index + 1;\n                }\n                return index - 1;\n            });\n            let proofNodeIndices = siblingIndices.filter((index) => !layerNodes.includes(index));\n            const unevenLayer = unevenLayers.find(({ index }) => index === layerIndex);\n            if (unevenLayer && layerNodes.includes(unevenLayer.leavesCount - 1)) {\n                proofNodeIndices = proofNodeIndices.slice(0, -1);\n            }\n            proofIndices.push(proofNodeIndices);\n            layerNodes = [...new Set(layerNodes.map((index) => {\n                    if (index % 2 === 0) {\n                        return index / 2;\n                    }\n                    if (index % 2 === 0) {\n                        return (index + 1) / 2;\n                    }\n                    return (index - 1) / 2;\n                }))];\n        }\n        return proofIndices;\n    }\n    /**\n     * getMultiProof\n     * @desc Returns the multiproof for given tree indices.\n     * @param {Number[]} indices - Tree indices.\n     * @return {Buffer[]} - Multiproofs\n     * @example\n     * ```js\n     *const indices = [2, 5, 6]\n     *const proof = tree.getMultiProof(indices)\n     *```\n     */\n    getMultiProof(tree, indices) {\n        if (!this.complete) {\n            console.warn('Warning: For correct multiProofs it\\'s strongly recommended to set complete: true');\n        }\n        if (!indices) {\n            indices = tree;\n            tree = this.getLayersFlat();\n        }\n        const isUneven = this.isUnevenTree();\n        if (isUneven) {\n            if (indices.every(Number.isInteger)) {\n                return this.getMultiProofForUnevenTree(indices);\n            }\n        }\n        if (!indices.every(Number.isInteger)) {\n            let els = indices;\n            if (this.sortPairs) {\n                els = els.sort(buffer_1.Buffer.compare);\n            }\n            let ids = els.map((el) => this.bufferIndexOf(this.leaves, el, this.sortLeaves)).sort((a, b) => a === b ? 0 : a > b ? 1 : -1);\n            if (!ids.every((idx) => idx !== -1)) {\n                throw new Error('Element does not exist in Merkle tree');\n            }\n            const hashes = [];\n            const proof = [];\n            let nextIds = [];\n            for (let i = 0; i < this.layers.length; i++) {\n                const layer = this.layers[i];\n                for (let j = 0; j < ids.length; j++) {\n                    const idx = ids[j];\n                    const pairElement = this.getPairNode(layer, idx);\n                    hashes.push(layer[idx]);\n                    if (pairElement) {\n                        proof.push(pairElement);\n                    }\n                    nextIds.push((idx / 2) | 0);\n                }\n                ids = nextIds.filter((value, i, self) => self.indexOf(value) === i);\n                nextIds = [];\n            }\n            return proof.filter((value) => !hashes.includes(value));\n        }\n        return this.getProofIndices(indices, Math.log2((tree.length / 2) | 0)).map(index => tree[index]);\n    }\n    getMultiProofForUnevenTree(tree, indices) {\n        if (!indices) {\n            indices = tree;\n            tree = this.getLayers();\n        }\n        let proofHashes = [];\n        let currentLayerIndices = indices;\n        for (const treeLayer of tree) {\n            const siblings = [];\n            for (const index of currentLayerIndices) {\n                if (index % 2 === 0) {\n                    const idx = index + 1;\n                    if (!currentLayerIndices.includes(idx)) {\n                        if (treeLayer[idx]) {\n                            siblings.push(treeLayer[idx]);\n                            continue;\n                        }\n                    }\n                }\n                const idx = index - 1;\n                if (!currentLayerIndices.includes(idx)) {\n                    if (treeLayer[idx]) {\n                        siblings.push(treeLayer[idx]);\n                        continue;\n                    }\n                }\n            }\n            proofHashes = proofHashes.concat(siblings);\n            const uniqueIndices = new Set();\n            for (const index of currentLayerIndices) {\n                if (index % 2 === 0) {\n                    uniqueIndices.add(index / 2);\n                    continue;\n                }\n                if (index % 2 === 0) {\n                    uniqueIndices.add((index + 1) / 2);\n                    continue;\n                }\n                uniqueIndices.add((index - 1) / 2);\n            }\n            currentLayerIndices = Array.from(uniqueIndices);\n        }\n        return proofHashes;\n    }\n    /**\n     * getHexMultiProof\n     * @desc Returns the multiproof for given tree indices as hex strings.\n     * @param {Number[]} indices - Tree indices.\n     * @return {String[]} - Multiproofs as hex strings.\n     * @example\n     * ```js\n     *const indices = [2, 5, 6]\n     *const proof = tree.getHexMultiProof(indices)\n     *```\n     */\n    getHexMultiProof(tree, indices) {\n        return this.getMultiProof(tree, indices).map((x) => this.bufferToHex(x));\n    }\n    /**\n     * getProofFlags\n     * @desc Returns list of booleans where proofs should be used instead of hashing.\n     * Proof flags are used in the Solidity multiproof verifiers.\n     * @param {Number[]|Buffer[]} leaves\n     * @param {Buffer[]} proofs\n     * @return {Boolean[]} - Boolean flags\n     * @example\n     * ```js\n     *const indices = [2, 5, 6]\n     *const proof = tree.getMultiProof(indices)\n     *const proofFlags = tree.getProofFlags(leaves, proof)\n     *```\n     */\n    getProofFlags(leaves, proofs) {\n        if (!Array.isArray(leaves) || leaves.length <= 0) {\n            throw new Error('Invalid Inputs!');\n        }\n        let ids;\n        if (leaves.every(Number.isInteger)) {\n            ids = [...leaves].sort((a, b) => a === b ? 0 : a > b ? 1 : -1); // Indices where passed\n        }\n        else {\n            ids = leaves.map((el) => this.bufferIndexOf(this.leaves, el, this.sortLeaves)).sort((a, b) => a === b ? 0 : a > b ? 1 : -1);\n        }\n        if (!ids.every((idx) => idx !== -1)) {\n            throw new Error('Element does not exist in Merkle tree');\n        }\n        const proofBufs = proofs.map(item => this.bufferify(item));\n        const tested = [];\n        const flags = [];\n        for (let index = 0; index < this.layers.length; index++) {\n            const layer = this.layers[index];\n            ids = ids.reduce((ids, idx) => {\n                const skipped = tested.includes(layer[idx]);\n                if (!skipped) {\n                    const pairElement = this.getPairNode(layer, idx);\n                    const proofUsed = this.bufferArrayIncludes(proofBufs, layer[idx]) || this.bufferArrayIncludes(proofBufs, pairElement);\n                    pairElement && flags.push(!proofUsed);\n                    tested.push(layer[idx]);\n                    tested.push(pairElement);\n                }\n                ids.push((idx / 2) | 0);\n                return ids;\n            }, []);\n        }\n        return flags;\n    }\n    /**\n     * verify\n     * @desc Returns true if the proof path (array of hashes) can connect the target node\n     * to the Merkle root.\n     * @param {Object[]} proof - Array of proof objects that should connect\n     * target node to Merkle root.\n     * @param {Buffer} targetNode - Target node Buffer\n     * @param {Buffer} root - Merkle root Buffer\n     * @return {Boolean}\n     * @example\n     *```js\n     *const root = tree.getRoot()\n     *const proof = tree.getProof(leaves[2])\n     *const verified = tree.verify(proof, leaves[2], root)\n     *```\n     */\n    verify(proof, targetNode, root) {\n        let hash = this.bufferify(targetNode);\n        root = this.bufferify(root);\n        if (!Array.isArray(proof) ||\n            !targetNode ||\n            !root) {\n            return false;\n        }\n        for (let i = 0; i < proof.length; i++) {\n            const node = proof[i];\n            let data = null;\n            let isLeftNode = null;\n            // case for when proof is hex values only\n            if (typeof node === 'string') {\n                data = this.bufferify(node);\n                isLeftNode = true;\n            }\n            else if (Array.isArray(node)) {\n                isLeftNode = (node[0] === 0);\n                data = this.bufferify(node[1]);\n            }\n            else if (buffer_1.Buffer.isBuffer(node)) {\n                data = node;\n                isLeftNode = true;\n            }\n            else if (node instanceof Object) {\n                data = this.bufferify(node.data);\n                isLeftNode = (node.position === 'left');\n            }\n            else {\n                throw new Error('Expected node to be of type string or object');\n            }\n            const buffers = [];\n            if (this.isBitcoinTree) {\n                buffers.push(buffer_reverse_1.default(hash));\n                buffers[isLeftNode ? 'unshift' : 'push'](buffer_reverse_1.default(data));\n                hash = this.hashFn(this.concatenator(buffers));\n                hash = buffer_reverse_1.default(this.hashFn(hash));\n            }\n            else {\n                if (this.sortPairs) {\n                    if (buffer_1.Buffer.compare(hash, data) === -1) {\n                        buffers.push(hash, data);\n                        hash = this.hashFn(this.concatenator(buffers));\n                    }\n                    else {\n                        buffers.push(data, hash);\n                        hash = this.hashFn(this.concatenator(buffers));\n                    }\n                }\n                else {\n                    buffers.push(hash);\n                    buffers[isLeftNode ? 'unshift' : 'push'](data);\n                    hash = this.hashFn(this.concatenator(buffers));\n                }\n            }\n        }\n        return buffer_1.Buffer.compare(hash, root) === 0;\n    }\n    /**\n     * verifyMultiProof\n     * @desc Returns true if the multiproofs can connect the leaves to the Merkle root.\n     * @param {Buffer} root - Merkle tree root\n     * @param {Number[]} proofIndices - Leave indices for proof\n     * @param {Buffer[]} proofLeaves - Leaf values at indices for proof\n     * @param {Number} leavesCount - Count of original leaves\n     * @param {Buffer[]} proof - Multiproofs given indices\n     * @return {Boolean}\n     * @example\n     *```js\n     *const leaves = tree.getLeaves()\n     *const root = tree.getRoot()\n     *const treeFlat = tree.getLayersFlat()\n     *const leavesCount = leaves.length\n     *const proofIndices = [2, 5, 6]\n     *const proofLeaves = proofIndices.map(i => leaves[i])\n     *const proof = tree.getMultiProof(treeFlat, indices)\n     *const verified = tree.verifyMultiProof(root, proofIndices, proofLeaves, leavesCount, proof)\n     *```\n     */\n    verifyMultiProof(root, proofIndices, proofLeaves, leavesCount, proof) {\n        const isUneven = this.isUnevenTree();\n        if (isUneven) {\n            // TODO: combine these functions and simplify\n            return this.verifyMultiProofForUnevenTree(root, proofIndices, proofLeaves, leavesCount, proof);\n        }\n        const depth = Math.ceil(Math.log2(leavesCount));\n        root = this.bufferify(root);\n        proofLeaves = proofLeaves.map(leaf => this.bufferify(leaf));\n        proof = proof.map(leaf => this.bufferify(leaf));\n        const tree = {};\n        for (const [index, leaf] of this.zip(proofIndices, proofLeaves)) {\n            tree[(Math.pow(2, depth)) + index] = leaf;\n        }\n        for (const [index, proofitem] of this.zip(this.getProofIndices(proofIndices, depth), proof)) {\n            tree[index] = proofitem;\n        }\n        let indexqueue = Object.keys(tree).map(value => +value).sort((a, b) => a - b);\n        indexqueue = indexqueue.slice(0, indexqueue.length - 1);\n        let i = 0;\n        while (i < indexqueue.length) {\n            const index = indexqueue[i];\n            if (index >= 2 && ({}).hasOwnProperty.call(tree, index ^ 1)) {\n                let pair = [tree[index - (index % 2)], tree[index - (index % 2) + 1]];\n                if (this.sortPairs) {\n                    pair = pair.sort(buffer_1.Buffer.compare);\n                }\n                const hash = pair[1] ? this.hashFn(this.concatenator(pair)) : pair[0];\n                tree[(index / 2) | 0] = hash;\n                indexqueue.push((index / 2) | 0);\n            }\n            i += 1;\n        }\n        return !proofIndices.length || (({}).hasOwnProperty.call(tree, 1) && tree[1].equals(root));\n    }\n    verifyMultiProofWithFlags(root, leaves, proofs, proofFlag) {\n        root = this.bufferify(root);\n        leaves = leaves.map(this.bufferify);\n        proofs = proofs.map(this.bufferify);\n        const leavesLen = leaves.length;\n        const totalHashes = proofFlag.length;\n        const hashes = [];\n        let leafPos = 0;\n        let hashPos = 0;\n        let proofPos = 0;\n        for (let i = 0; i < totalHashes; i++) {\n            const bufA = proofFlag[i] ? (leafPos < leavesLen ? leaves[leafPos++] : hashes[hashPos++]) : proofs[proofPos++];\n            const bufB = leafPos < leavesLen ? leaves[leafPos++] : hashes[hashPos++];\n            const buffers = [bufA, bufB].sort(buffer_1.Buffer.compare);\n            hashes[i] = this.hashFn(this.concatenator(buffers));\n        }\n        return buffer_1.Buffer.compare(hashes[totalHashes - 1], root) === 0;\n    }\n    verifyMultiProofForUnevenTree(root, indices, leaves, leavesCount, proof) {\n        root = this.bufferify(root);\n        leaves = leaves.map(leaf => this.bufferify(leaf));\n        proof = proof.map(leaf => this.bufferify(leaf));\n        const computedRoot = this.calculateRootForUnevenTree(indices, leaves, leavesCount, proof);\n        return root.equals(computedRoot);\n    }\n    /**\n     * getDepth\n     * @desc Returns the tree depth (number of layers)\n     * @return {Number}\n     * @example\n     *```js\n     *const depth = tree.getDepth()\n     *```\n     */\n    getDepth() {\n        return this.getLayers().length - 1;\n    }\n    /**\n     * getLayersAsObject\n     * @desc Returns the layers as nested objects instead of an array.\n     * @example\n     *```js\n     *const layersObj = tree.getLayersAsObject()\n     *```\n     */\n    getLayersAsObject() {\n        const layers = this.getLayers().map((layer) => layer.map((value) => this.bufferToHex(value, false)));\n        const objs = [];\n        for (let i = 0; i < layers.length; i++) {\n            const arr = [];\n            for (let j = 0; j < layers[i].length; j++) {\n                const obj = { [layers[i][j]]: null };\n                if (objs.length) {\n                    obj[layers[i][j]] = {};\n                    const a = objs.shift();\n                    const akey = Object.keys(a)[0];\n                    obj[layers[i][j]][akey] = a[akey];\n                    if (objs.length) {\n                        const b = objs.shift();\n                        const bkey = Object.keys(b)[0];\n                        obj[layers[i][j]][bkey] = b[bkey];\n                    }\n                }\n                arr.push(obj);\n            }\n            objs.push(...arr);\n        }\n        return objs[0];\n    }\n    /**\n     * verify\n     * @desc Returns true if the proof path (array of hashes) can connect the target node\n     * to the Merkle root.\n     * @param {Object[]} proof - Array of proof objects that should connect\n     * target node to Merkle root.\n     * @param {Buffer} targetNode - Target node Buffer\n     * @param {Buffer} root - Merkle root Buffer\n     * @param {Function} hashFunction - Hash function for hashing leaves and nodes\n     * @param {Object} options - Additional options\n     * @return {Boolean}\n     * @example\n     *```js\n     *const verified = MerkleTree.verify(proof, leaf, root, sha256, options)\n     *```\n     */\n    static verify(proof, targetNode, root, hashFn = sha256_1.default, options = {}) {\n        const tree = new MerkleTree([], hashFn, options);\n        return tree.verify(proof, targetNode, root);\n    }\n    /**\n     * getMultiProof\n     * @desc Returns the multiproof for given tree indices.\n     * @param {Buffer[]} tree - Tree as a flat array.\n     * @param {Number[]} indices - Tree indices.\n     * @return {Buffer[]} - Multiproofs\n     *\n     *@example\n     * ```js\n     *const flatTree = tree.getLayersFlat()\n     *const indices = [2, 5, 6]\n     *const proof = MerkleTree.getMultiProof(flatTree, indices)\n     *```\n     */\n    static getMultiProof(tree, indices) {\n        const t = new MerkleTree([]);\n        return t.getMultiProof(tree, indices);\n    }\n    /**\n     * resetTree\n     * @desc Resets the tree by clearing the leaves and layers.\n     * @example\n     *```js\n     *tree.resetTree()\n     *```\n     */\n    resetTree() {\n        this.leaves = [];\n        this.layers = [];\n    }\n    /**\n     * getPairNode\n     * @desc Returns the node at the index for given layer.\n     * @param {Buffer[]} layer - Tree layer\n     * @param {Number} index - Index at layer.\n     * @return {Buffer} - Node\n     *\n     *@example\n     * ```js\n     *const node = tree.getPairNode(layer, index)\n     *```\n     */\n    getPairNode(layer, idx) {\n        const pairIdx = idx % 2 === 0 ? idx + 1 : idx - 1;\n        if (pairIdx < layer.length) {\n            return layer[pairIdx];\n        }\n        else {\n            return null;\n        }\n    }\n    /**\n     * toTreeString\n     * @desc Returns a visual representation of the merkle tree as a string.\n     * @return {String}\n     * @example\n     *```js\n     *console.log(tree.toTreeString())\n     *```\n     */\n    toTreeString() {\n        const obj = this.getLayersAsObject();\n        return treeify_1.default.asTree(obj, true);\n    }\n    /**\n     * toString\n     * @desc Returns a visual representation of the merkle tree as a string.\n     * @example\n     *```js\n     *console.log(tree.toString())\n     *```\n     */\n    toString() {\n        return this.toTreeString();\n    }\n    isUnevenTree(treeLayers) {\n        const depth = (treeLayers === null || treeLayers === void 0 ? void 0 : treeLayers.length) || this.getDepth();\n        return !this.isPowOf2(depth);\n    }\n    isPowOf2(v) {\n        return v && !(v & (v - 1));\n    }\n    isValidLeafIndex(idx) {\n        return idx >= 0 && idx < this.getLeafCount();\n    }\n    calculateRootForUnevenTree(leafIndices, leafHashes, totalLeavesCount, proofHashes) {\n        const leafTuples = this.zip(leafIndices, leafHashes).sort(([indexA], [indexB]) => indexA - indexB);\n        const leafTupleIndices = leafTuples.map(([index]) => index);\n        const proofIndices = this.getProofIndicesForUnevenTree(leafTupleIndices, totalLeavesCount);\n        let nextSliceStart = 0;\n        const proofTuplesByLayers = [];\n        for (let i = 0; i < proofIndices.length; i++) {\n            const indices = proofIndices[i];\n            const sliceStart = nextSliceStart;\n            nextSliceStart += indices.length;\n            proofTuplesByLayers[i] = this.zip(indices, proofHashes.slice(sliceStart, nextSliceStart));\n        }\n        const tree = [leafTuples];\n        for (let layerIndex = 0; layerIndex < proofTuplesByLayers.length; layerIndex++) {\n            const currentLayer = proofTuplesByLayers[layerIndex].concat(tree[layerIndex]).sort(([indexA], [indexB]) => indexA - indexB)\n                .map(([, hash]) => hash);\n            const s = tree[layerIndex].map(([layerIndex]) => layerIndex);\n            const parentIndices = [...new Set(s.map((index) => {\n                    if (index % 2 === 0) {\n                        return index / 2;\n                    }\n                    if (index % 2 === 0) {\n                        return (index + 1) / 2;\n                    }\n                    return (index - 1) / 2;\n                }))];\n            const parentLayer = [];\n            for (let i = 0; i < parentIndices.length; i++) {\n                const parentNodeTreeIndex = parentIndices[i];\n                const bufA = currentLayer[i * 2];\n                const bufB = currentLayer[i * 2 + 1];\n                const hash = bufB ? this.hashFn(this.concatenator([bufA, bufB])) : bufA;\n                parentLayer.push([parentNodeTreeIndex, hash]);\n            }\n            tree.push(parentLayer);\n        }\n        return tree[tree.length - 1][0][1];\n    }\n}\nexports.MerkleTree = MerkleTree;\nif (typeof window !== 'undefined') {\n    ;\n    window.MerkleTree = MerkleTree;\n}\nexports[\"default\"] = MerkleTree;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/merkletreejs/dist/MerkleTree.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/merkletreejs/dist/UnifiedBinaryTree.js":
/*!**************************************************************!*\
  !*** ../node_modules/merkletreejs/dist/UnifiedBinaryTree.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UnifiedBinaryTree = exports.InternalNode = exports.StemNode = exports.chunkifyCode = exports.getTreeKeyForCodeChunk = exports.getTreeKeyForStorageSlot = exports.getTreeKeyForCodeHash = exports.getTreeKeyForBasicData = exports.getTreeKey = exports.treeHash = exports.oldStyleAddressToAddress32 = exports.push32 = exports.push1 = exports.pushOffset = exports.MAIN_STORAGE_OFFSET = exports.STEM_SUBTREE_WIDTH = exports.CODE_OFFSET = exports.HEADER_STORAGE_OFFSET = exports.CODE_HASH_LEAF_KEY = exports.BASIC_DATA_LEAF_KEY = void 0;\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst Base_1 = __webpack_require__(/*! ./Base */ \"(rsc)/../node_modules/merkletreejs/dist/Base.js\");\n// -----------------------------------------------------------------------------\n// Constants\n// -----------------------------------------------------------------------------\n/**\n * Constants used for key derivation and tree organization.\n * These define the structure and layout of the binary tree.\n */\n// Leaf key types\nexports.BASIC_DATA_LEAF_KEY = 0; // Used for account basic data (nonce, balance, etc.)\nexports.CODE_HASH_LEAF_KEY = 1; // Used for contract code hash\n// Storage layout offsets\nexports.HEADER_STORAGE_OFFSET = 64; // Start of header storage slots\nexports.CODE_OFFSET = 128; // Start of code chunks\nexports.STEM_SUBTREE_WIDTH = 256; // Width of each stem subtree (8 bits)\nexports.MAIN_STORAGE_OFFSET = 256; // Start of main storage slots\n// EVM PUSH instruction constants\nexports.pushOffset = 95; // Base offset for PUSH instructions\nexports.push1 = exports.pushOffset + 1; // PUSH1 opcode (0x60)\nexports.push32 = exports.pushOffset + 32; // PUSH32 opcode (0x7F)\n// -----------------------------------------------------------------------------\n// Utility Functions for Key Derivation and Code Chunkification\n// -----------------------------------------------------------------------------\n/**\n * Converts a 20-byte Ethereum address to a 32-byte address by left-padding with zeros.\n *\n * @example\n * ```typescript\n * const addr20 = Buffer.from('1234567890123456789012345678901234567890', 'hex')\n * const addr32 = oldStyleAddressToAddress32(addr20)\n * // addr32 = 0x000000000000123456789012345678901234567890 (32 bytes)\n * ```\n */\nfunction oldStyleAddressToAddress32(address) {\n    if (address.length !== 20) {\n        throw new Error('Address must be 20 bytes.');\n    }\n    return buffer_1.Buffer.concat([buffer_1.Buffer.alloc(12, 0), address]);\n}\nexports.oldStyleAddressToAddress32 = oldStyleAddressToAddress32;\n/**\n * Applies a hash function to input data with proper buffering.\n *\n * @example\n * ```typescript\n * const input = Buffer.from('Hello World')\n * const hashFn = (data) => blake3.hash(data)\n * const hash = treeHash(input, hashFn)\n * // hash = 32-byte BLAKE3 hash of 'Hello World'\n * ```\n */\nfunction treeHash(input, hashFn) {\n    return treeHashFn(hashFn)(input);\n}\nexports.treeHash = treeHash;\nfunction treeHashFn(hashFn) {\n    return Base_1.Base.bufferifyFn(hashFn);\n}\n/**\n * Derives a tree key from an address and indices using a hash function.\n * Used to generate unique keys for different parts of the tree structure.\n * The resulting key is composed of a 31-byte stem (derived from address and treeIndex)\n * and a 1-byte subIndex.\n *\n * @param address - A 32-byte address to derive the key from\n * @param treeIndex - Primary index used to derive different trees for the same address\n * @param subIndex - Secondary index used to derive different keys within the same tree\n * @param hashFn - Hash function to use for key derivation\n * @returns A 32-byte key that uniquely identifies this storage slot\n * @throws Error if address is not 32 bytes\n *\n * @example\n * ```typescript\n * const addr32 = oldStyleAddressToAddress32(address)\n * const treeKey = getTreeKey(addr32, 0, 1, blake3.hash)\n * // Returns a unique key for this address's tree at index 0, subIndex 1\n * ```\n */\nfunction getTreeKey(address, treeIndex, subIndex, hashFn) {\n    // Validate address length\n    if (address.length !== 32) {\n        throw new Error('Address must be 32 bytes.');\n    }\n    // Get the tree-specific hash function\n    const treeHash = treeHashFn(hashFn);\n    // Create a buffer to store the tree index\n    const indexBuffer = buffer_1.Buffer.alloc(32, 0);\n    indexBuffer.writeUInt32LE(treeIndex, 0);\n    // Generate the stem by:\n    // 1. Concatenating address and index buffer\n    // 2. Hashing the result\n    // 3. Taking first 31 bytes\n    const stem = treeHash(buffer_1.Buffer.concat([address, indexBuffer]), hashFn).subarray(0, 31);\n    // Combine the stem with the subIndex to create the final 32-byte key\n    return buffer_1.Buffer.concat([stem, buffer_1.Buffer.from([subIndex])]);\n}\nexports.getTreeKey = getTreeKey;\n/**\n * Derives a key for storing an account's basic data (nonce, balance, etc.).\n *\n * @example\n * ```typescript\n * const addr32 = oldStyleAddressToAddress32(address)\n * const basicDataKey = getTreeKeyForBasicData(addr32, hashFn)\n * tree.insert(basicDataKey, accountData)\n * ```\n */\nfunction getTreeKeyForBasicData(address, hashFn) {\n    return getTreeKey(address, 0, exports.BASIC_DATA_LEAF_KEY, hashFn);\n}\nexports.getTreeKeyForBasicData = getTreeKeyForBasicData;\n/**\n * Derives a key for storing a contract's code hash.\n *\n * @example\n * ```typescript\n * const addr32 = oldStyleAddressToAddress32(contractAddress)\n * const codeHashKey = getTreeKeyForCodeHash(addr32, hashFn)\n * tree.insert(codeHashKey, codeHash)\n * ```\n */\nfunction getTreeKeyForCodeHash(address, hashFn) {\n    return getTreeKey(address, 0, exports.CODE_HASH_LEAF_KEY, hashFn);\n}\nexports.getTreeKeyForCodeHash = getTreeKeyForCodeHash;\n/**\n * Derives a tree key for a storage slot in a contract's storage.\n * Handles two types of storage:\n * 1. Header storage (slots 0-63): Used for contract metadata and special storage\n * 2. Main storage (slots 256+): Used for regular contract storage\n *\n * The storage layout is:\n * - Header storage: slots [0, 63] mapped to positions [64, 127]\n * - Main storage: slots [256+] mapped to positions [384+]\n * This creates gaps in the tree to allow for future extensions.\n *\n * @param address - The 32-byte contract address\n * @param storageKey - The storage slot number to access\n * @param hashFn - Hash function to use for key derivation\n * @returns A 32-byte key that uniquely identifies this storage slot\n *\n * @example\n * ```typescript\n * const addr32 = oldStyleAddressToAddress32(contractAddress)\n * // Get key for a header storage slot (0-63)\n * const headerKey = getTreeKeyForStorageSlot(addr32, 5, blake3.hash)\n * // Get key for a main storage slot (256+)\n * const mainKey = getTreeKeyForStorageSlot(addr32, 300, blake3.hash)\n * ```\n */\nfunction getTreeKeyForStorageSlot(address, storageKey, hashFn) {\n    let pos;\n    // If storage key is in header range (0-63), map it to positions 64-127\n    if (storageKey < exports.CODE_OFFSET - exports.HEADER_STORAGE_OFFSET) {\n        pos = exports.HEADER_STORAGE_OFFSET + storageKey;\n    }\n    else {\n        // Otherwise, map it to main storage starting at position 384\n        pos = exports.MAIN_STORAGE_OFFSET + storageKey;\n    }\n    // Convert the position to tree coordinates:\n    // - treeIndex: Which subtree to use (pos / 256)\n    // - subIndex: Which leaf in the subtree (pos % 256)\n    return getTreeKey(address, Math.floor(pos / exports.STEM_SUBTREE_WIDTH), pos % exports.STEM_SUBTREE_WIDTH, hashFn);\n}\nexports.getTreeKeyForStorageSlot = getTreeKeyForStorageSlot;\n/**\n * Derives a key for storing a chunk of contract code.\n * Used when contract code is split into 32-byte chunks.\n *\n * @example\n * ```typescript\n * const addr32 = oldStyleAddressToAddress32(contractAddress)\n * const chunks = chunkifyCode(contractCode)\n * chunks.forEach((chunk, i) => {\n *   const key = getTreeKeyForCodeChunk(addr32, i, hashFn)\n *   tree.insert(key, chunk)\n * })\n * ```\n */\nfunction getTreeKeyForCodeChunk(address, chunkId, hashFn) {\n    const pos = exports.CODE_OFFSET + chunkId;\n    return getTreeKey(address, Math.floor(pos / exports.STEM_SUBTREE_WIDTH), pos % exports.STEM_SUBTREE_WIDTH, hashFn);\n}\nexports.getTreeKeyForCodeChunk = getTreeKeyForCodeChunk;\n/**\n * Splits EVM bytecode into 31-byte chunks with metadata.\n * Each chunk is prefixed with a byte indicating the number of bytes\n * that are part of PUSH data in the next chunk.\n *\n * @example\n * ```typescript\n * const code = Buffer.from('6001600201', 'hex') // PUSH1 01 PUSH1 02 ADD\n * const chunks = chunkifyCode(code)\n * // chunks[0] = [0x01, 0x60, 0x01, 0x60, 0x02, 0x01, 0x00...] (32 bytes)\n * ```\n */\nfunction chunkifyCode(code) {\n    // If code length is not divisible by 31, pad it with zeros\n    // This ensures all chunks (except last) are exactly 31 bytes\n    const remainder = code.length % 31;\n    if (remainder !== 0) {\n        code = buffer_1.Buffer.concat([code, buffer_1.Buffer.alloc(31 - remainder, 0)]);\n    }\n    // Create array to track how many bytes of PUSH data follow each position\n    // Size is code.length + 32 to handle edge cases where PUSH data crosses chunk boundaries\n    const bytesToExecData = new Array(code.length + 32).fill(0);\n    // Iterate through the bytecode to identify PUSH operations and their data\n    let pos = 0;\n    while (pos < code.length) {\n        const opcode = code[pos];\n        let pushdataBytes = 0;\n        // Check if opcode is a PUSH operation (0x60 to 0x7F)\n        if (opcode >= exports.push1 && opcode <= exports.push32) {\n            // Calculate number of bytes to push (PUSH1 = 1 byte, PUSH2 = 2 bytes, etc.)\n            pushdataBytes = opcode - exports.pushOffset;\n        }\n        pos += 1; // Move past the opcode\n        // For each byte of PUSH data, store how many remaining PUSH bytes follow\n        // This helps identify which bytes are executable vs PUSH data when chunking\n        for (let x = 0; x < pushdataBytes; x++) {\n            bytesToExecData[pos + x] = pushdataBytes - x;\n        }\n        pos += pushdataBytes; // Skip over the PUSH data bytes\n    }\n    // Split the code into 32-byte chunks (1 prefix byte + 31 code bytes)\n    const chunks = [];\n    for (let start = 0; start < code.length; start += 31) {\n        // First byte of chunk indicates how many PUSH data bytes are at start of next chunk\n        const prefix = Math.min(bytesToExecData[start], 31);\n        // Create a new chunk by combining:\n        // 1. Single prefix byte indicating PUSH data count\n        // 2. 31 bytes of code starting at current position\n        const chunk = buffer_1.Buffer.concat([\n            buffer_1.Buffer.from([prefix]),\n            code.slice(start, start + 31)\n        ]);\n        chunks.push(chunk);\n    }\n    return chunks;\n}\nexports.chunkifyCode = chunkifyCode;\n/**\n * Leaf node in the binary tree that stores actual values.\n * Contains a 31-byte stem and an array of 256 possible values.\n *\n * @example\n * ```typescript\n * const stem = Buffer.alloc(31, 0)\n * const node = new StemNode(stem)\n * node.setValue(0, Buffer.alloc(32).fill(1)) // Set value at index 0\n * ```\n */\nclass StemNode {\n    /**\n     * Creates a new StemNode with the given stem.\n     *\n     * @param stem - The 31-byte stem for this node.\n     */\n    constructor(stem) {\n        this.nodeType = 'stem';\n        if (stem.length !== 31) {\n            throw new Error('Stem must be 31 bytes.');\n        }\n        this.stem = stem;\n        this.values = new Array(256).fill(null);\n    }\n    /**\n     * Sets the value at the given index.\n     *\n     * @param index - The index to set the value at.\n     * @param value - The 32-byte value to set.\n     */\n    setValue(index, value) {\n        if (value.length !== 32) {\n            throw new Error('Value must be 32 bytes.');\n        }\n        this.values[index] = value;\n    }\n}\nexports.StemNode = StemNode;\n/**\n * Internal node in the binary tree with left and right children.\n * Used to create the tree structure based on key bit patterns.\n *\n * @example\n * ```typescript\n * const node = new InternalNode()\n * node.left = new StemNode(Buffer.alloc(31, 0))\n * node.right = new StemNode(Buffer.alloc(31, 1))\n * ```\n */\nclass InternalNode {\n    constructor() {\n        this.left = null;\n        this.right = null;\n        this.nodeType = 'internal';\n    }\n}\nexports.InternalNode = InternalNode;\n/**\n * Main binary tree implementation that stores key-value pairs.\n * Uses a configurable hash function and supports various operations.\n *\n * @example\n * ```typescript\n * const tree = new BinaryTree(blake3.hash)\n * tree.insert(key, value)\n * const root = tree.merkelize()\n * const serialized = tree.serialize()\n * ```\n */\nclass UnifiedBinaryTree {\n    /**\n     * Creates a new BinaryTree instance with the given hash function.\n     *\n     * @param hashFn - The hash function to use for key derivation.\n     */\n    constructor(hashFn) {\n        this.root = null;\n        this.hashFn = Base_1.Base.bufferifyFn(hashFn);\n    }\n    /**\n     * Inserts a key-value pair into the binary tree.\n     * The key is split into two parts:\n     * - stem (first 31 bytes): Determines the path in the tree\n     * - subIndex (last byte): Determines the position within a leaf node\n     *\n     * If this is the first insertion, creates a new leaf node.\n     * Otherwise, recursively traverses or builds the tree structure.\n     *\n     * @param key - A 32-byte key that determines where to store the value\n     * @param value - A 32-byte value to store\n     * @throws Error if key or value is not exactly 32 bytes\n     *\n     * @example\n     * ```typescript\n     * const tree = new BinaryTree(hashFn)\n     * const key = getTreeKey(address, 0, 1, hashFn)\n     * const value = Buffer.alloc(32).fill(1)\n     * tree.insert(key, value)\n     * ```\n     */\n    insert(key, value) {\n        // Validate input lengths\n        if (key.length !== 32) {\n            throw new Error('Key must be 32 bytes.');\n        }\n        if (value.length !== 32) {\n            throw new Error('Value must be 32 bytes.');\n        }\n        // Split key into stem (path) and subIndex (leaf position)\n        const stem = key.slice(0, 31);\n        const subIndex = key[31];\n        // If tree is empty, create first leaf node\n        if (this.root === null) {\n            this.root = new StemNode(stem);\n            this.root.setValue(subIndex, value);\n            return;\n        }\n        // Otherwise, recursively insert into existing tree\n        // Starting at depth 0 (root level)\n        this.root = this.insertRecursive(this.root, stem, subIndex, value, 0);\n    }\n    /**\n     * Recursively inserts a key-value pair into the tree.\n     * This method handles three cases:\n     * 1. Empty node: Creates a new leaf node\n     * 2. Stem node: Either updates value or splits into internal node\n     * 3. Internal node: Recursively traverses left or right based on stem bits\n     *\n     * @param node - Current node in traversal (null if empty)\n     * @param stem - The 31-byte path component of the key\n     * @param subIndex - The leaf position component of the key\n     * @param value - The 32-byte value to store\n     * @param depth - Current depth in the tree (max 247 to prevent hash collisions)\n     * @returns The new or updated node\n     * @throws Error if tree depth exceeds 247 levels\n     */\n    insertRecursive(node, stem, subIndex, value, depth) {\n        // Prevent deep recursion that could lead to hash collisions\n        if (depth >= 248) {\n            throw new Error('Depth must be less than 248.');\n        }\n        // Case 1: Empty node - create new leaf\n        if (node === null) {\n            const newNode = new StemNode(stem);\n            newNode.setValue(subIndex, value);\n            return newNode;\n        }\n        // Convert stem to bit array for path decisions\n        const stemBits = this.bytesToBits(stem);\n        // Case 2: Reached a leaf node (StemNode)\n        if (node instanceof StemNode) {\n            // If stems match, just update the value\n            if (node.stem.equals(stem)) {\n                node.setValue(subIndex, value);\n                return node;\n            }\n            // If stems differ, need to split this leaf node\n            const existingStemBits = this.bytesToBits(node.stem);\n            return this.splitLeaf(node, stemBits, existingStemBits, subIndex, value, depth);\n        }\n        else { // Case 3: Internal node - traverse left or right\n            // Use current depth's bit to decide path (0 = left, 1 = right)\n            const bit = stemBits[depth];\n            if (bit === 0) {\n                node.left = this.insertRecursive(node.left, stem, subIndex, value, depth + 1);\n            }\n            else {\n                node.right = this.insertRecursive(node.right, stem, subIndex, value, depth + 1);\n            }\n            return node;\n        }\n    }\n    /**\n     * Converts a byte array to an array of individual bits.\n     * Each byte is converted to 8 bits, maintaining the most-significant-bit first order.\n     * Used for making path decisions in the binary tree based on stem bytes.\n     *\n     * @param data - Buffer containing bytes to convert\n     * @returns Array of bits (0s and 1s) in MSB-first order\n     *\n     * @example\n     * ```typescript\n     * const bytes = Buffer.from([0xA5]) // Binary: 10100101\n     * const bits = bytesToBits(bytes)\n     * // bits = [1,0,1,0,0,1,0,1]\n     * //         ^ MSB        LSB ^\n     * ```\n     *\n     * Process for each byte:\n     * 1. Right shift by (7-i) positions to get desired bit to LSB\n     * 2. AND with 1 to isolate that bit\n     * 3. Push result (0 or 1) to output array\n     */\n    bytesToBits(data) {\n        const bits = [];\n        // Process each byte in the input buffer\n        for (const byte of data) {\n            // Extract each bit from the byte, MSB first\n            for (let i = 0; i < 8; i++) {\n                // Right shift to position + mask to get bit value\n                // i=0: shift 7 (10100101 -> 00000001)\n                // i=1: shift 6 (10100101 -> 00000000)\n                // i=2: shift 5 (10100101 -> 00000001)\n                // etc.\n                bits.push((byte >> (7 - i)) & 1);\n            }\n        }\n        return bits;\n    }\n    /**\n     * Converts an array of bits back into a Buffer of bytes.\n     * This is the inverse operation of bytesToBits.\n     * Processes bits in groups of 8, maintaining MSB-first order.\n     *\n     * @param bits - Array of 0s and 1s to convert to bytes\n     * @returns Buffer containing the reconstructed bytes\n     * @throws Error if the number of bits is not divisible by 8\n     *\n     * @example\n     * ```typescript\n     * const bits = [1,0,1,0,0,1,0,1] // Represents binary 10100101\n     * const bytes = bitsToBytes(bits)\n     * // bytes = Buffer.from([0xA5])\n     * ```\n     *\n     * Process for each byte:\n     * 1. Take 8 bits at a time\n     * 2. For each bit:\n     *    - Shift it left to its correct position (7-j positions)\n     *    - OR it with the accumulating byte value\n     * 3. Add completed byte to array\n     */\n    bitsToBytes(bits) {\n        // Ensure we have complete bytes (groups of 8 bits)\n        if (bits.length % 8 !== 0) {\n            throw new Error('Number of bits must be a multiple of 8.');\n        }\n        const bytes = [];\n        // Process bits in groups of 8\n        for (let i = 0; i < bits.length; i += 8) {\n            let byte = 0;\n            // Build each byte bit by bit\n            for (let j = 0; j < 8; j++) {\n                // Left shift each bit to its position and OR with current byte\n                // j=0: bit goes to position 7 (MSB)\n                // j=1: bit goes to position 6\n                // j=2: bit goes to position 5\n                // etc.\n                byte |= bits[i + j] << (7 - j);\n            }\n            bytes.push(byte);\n        }\n        return buffer_1.Buffer.from(bytes);\n    }\n    /**\n     * Applies the hash function to the given data with special handling for null values.\n     * Used primarily for Merkle tree calculations and node hashing.\n     *\n     * Special cases:\n     * - null input -> returns 32-byte zero buffer\n     * - 64-byte zero buffer -> returns 32-byte zero buffer\n     * This handling ensures consistent treatment of empty/uninitialized nodes.\n     *\n     * @param data - Buffer to hash, must be either 32 or 64 bytes, or null\n     * @returns A 32-byte hash of the data, or zero32 for empty cases\n     * @throws Error if data length is not 32 or 64 bytes\n     *\n     * @example\n     * ```typescript\n     * // Regular hashing\n     * const hash1 = hashData(nodeBuffer) // Returns hash of data\n     *\n     * // Empty cases - all return 32 zeros\n     * const hash2 = hashData(null)\n     * const hash3 = hashData(Buffer.alloc(64, 0))\n     * ```\n     */\n    hashData(data) {\n        // Pre-allocate zero buffers for comparison and return values\n        const zero64 = buffer_1.Buffer.alloc(64, 0); // Used to detect empty 64-byte input\n        const zero32 = buffer_1.Buffer.alloc(32, 0); // Returned for empty/zero cases\n        // Return zero32 for either null input or a 64-byte zero buffer\n        // This treats empty nodes consistently in the tree\n        if (data === null || data.equals(zero64)) {\n            return zero32;\n        }\n        // Validate input size - must be either a single node (32 bytes)\n        // or a pair of nodes being combined (64 bytes)\n        if (data.length !== 32 && data.length !== 64) {\n            throw new Error('Data must be 32 or 64 bytes.');\n        }\n        // Apply the configured hash function to valid data\n        return this.hashFn(data);\n    }\n    /**\n     * Computes the Merkle root of the entire tree.\n     * The Merkle root is a single 32-byte hash that uniquely represents the entire tree state.\n     *\n     * The computation follows these rules:\n     * 1. For Internal nodes: hash(leftChild || rightChild)\n     * 2. For Stem nodes: hash(stem || 0x00 || merkleOfValues)\n     * 3. For empty nodes: return 32 bytes of zeros\n     *\n     * @returns A 32-byte Buffer containing the Merkle root\n     *\n     * @example\n     * ```typescript\n     * const tree = new BinaryTree(hashFn)\n     * tree.insert(key1, value1)\n     * tree.insert(key2, value2)\n     * const root = tree.merkelize()\n     * // root now contains a 32-byte hash representing the entire tree\n     * ```\n     */\n    merkelize() {\n        /**\n         * Recursive helper function to compute the Merkle root of a subtree\n         * @param node - Root of the subtree to compute hash for\n         * @returns 32-byte Buffer containing the node's Merkle hash\n         */\n        const computeMerkle = (node) => {\n            const zero32 = buffer_1.Buffer.alloc(32, 0);\n            // Base case: empty node returns zero hash\n            if (node === null) {\n                return zero32;\n            }\n            // Case 1: Internal node\n            if (node instanceof InternalNode) {\n                // Recursively compute hashes of left and right children\n                const leftHash = computeMerkle(node.left);\n                const rightHash = computeMerkle(node.right);\n                // Combine and hash the children\n                return this.hashData(buffer_1.Buffer.concat([leftHash, rightHash]));\n            }\n            // Case 2: Stem node (leaf)\n            // First compute Merkle tree of the 256 values in this node\n            const level = node.values.map(val => this.hashData(val));\n            // Build a balanced binary tree from the value hashes\n            // Each iteration combines pairs of hashes until only root remains\n            while (level.length > 1) {\n                const newLevel = [];\n                for (let i = 0; i < level.length; i += 2) {\n                    // Combine each pair of hashes\n                    newLevel.push(this.hashData(buffer_1.Buffer.concat([level[i], level[i + 1]])));\n                }\n                // Replace old level with new level\n                level.splice(0, level.length, ...newLevel);\n            }\n            // Final stem node hash combines:\n            // 1. The stem (31 bytes)\n            // 2. A zero byte (1 byte)\n            // 3. The Merkle root of values (32 bytes)\n            return this.hashData(buffer_1.Buffer.concat([\n                node.stem,\n                buffer_1.Buffer.from([0]),\n                level[0] // 32-byte value root\n            ]));\n        };\n        // Start computation from root\n        return computeMerkle(this.root);\n    }\n    // -------------------------------------------------------------\n    // New Features\n    // -------------------------------------------------------------\n    /**\n     * Incrementally updates the value for an existing key.\n     * For our implementation, update is the same as insert.\n     *\n     * @param key - A 32-byte key.\n     * @param value - A 32-byte value.\n     */\n    update(key, value) {\n        // Simply re-insert; our insert() method will update an existing key.\n        this.insert(key, value);\n    }\n    /**\n     * Performs a batch insertion of key-value pairs.\n     *\n     * @param entries - An array of objects with 'key' and 'value' properties.\n     */\n    insertBatch(entries) {\n        for (const { key, value } of entries) {\n            this.insert(key, value);\n        }\n    }\n    /**\n     * Serializes the entire tree structure into a JSON Buffer.\n     * Converts the tree into a format that can be stored or transmitted,\n     * preserving the complete structure and all values.\n     *\n     * The serialized format for each node type is:\n     * 1. Stem Node:\n     *    ```json\n     *    {\n     *      \"nodeType\": \"stem\",\n     *      \"stem\": \"hex string of 31 bytes\",\n     *      \"values\": [\"hex string or null\", ...] // 256 entries\n     *    }\n     *    ```\n     * 2. Internal Node:\n     *    ```json\n     *    {\n     *      \"nodeType\": \"internal\",\n     *      \"left\": <node or null>,\n     *      \"right\": <node or null>\n     *    }\n     *    ```\n     *\n     * @returns Buffer containing the JSON string representation of the tree\n     *\n     * @example\n     * ```typescript\n     * const tree = new BinaryTree(hashFn)\n     * tree.insert(key, value)\n     * const serialized = tree.serialize()\n     * // Save to file or transmit\n     * const newTree = UnifiedBinaryTree.deserialize(serialized, hashFn)\n     * ```\n     */\n    serialize() {\n        /**\n         * Helper function to recursively serialize each node in the tree\n         * Converts Buffer data to hex strings for JSON compatibility\n         *\n         * @param node - The node to serialize\n         * @returns JSON-compatible object representation of the node\n         */\n        function serializeNode(node) {\n            // Handle empty nodes\n            if (!node)\n                return null;\n            // Case 1: Stem (leaf) node\n            if (node instanceof StemNode) {\n                return {\n                    nodeType: 'stem',\n                    stem: node.stem.toString('hex'),\n                    values: node.values.map(val => // Convert 256 values to hex\n                     (val ? val.toString('hex') : null)) // Preserve null values\n                };\n            }\n            else { // Case 2: Internal node\n                return {\n                    nodeType: 'internal',\n                    left: serializeNode(node.left),\n                    right: serializeNode(node.right) // Recursively serialize right subtree\n                };\n            }\n        }\n        // Wrap the serialized tree in a root object and convert to Buffer\n        const obj = { root: serializeNode(this.root) };\n        return buffer_1.Buffer.from(JSON.stringify(obj), 'utf8');\n    }\n    /**\n     * Reconstructs a BinaryTree from its serialized form.\n     * This is the inverse operation of serialize().\n     *\n     * Expected input format:\n     * ```json\n     * {\n     *   \"root\": {\n     *     \"nodeType\": \"internal\"|\"stem\",\n     *     // For stem nodes:\n     *     \"stem\": \"hex string\",\n     *     \"values\": [\"hex string\"|null, ...],\n     *     // For internal nodes:\n     *     \"left\": <node|null>,\n     *     \"right\": <node|null>\n     *   }\n     * }\n     * ```\n     *\n     * @param data - Buffer containing the JSON serialized tree\n     * @param hashFn - Hash function to use for the reconstructed tree\n     * @returns A new BinaryTree instance with the deserialized structure\n     * @throws Error if JSON parsing fails or format is invalid\n     *\n     * @example\n     * ```typescript\n     * const serialized = existingTree.serialize()\n     * const newTree = UnifiedBinaryTree.deserialize(serialized, hashFn)\n     * // newTree is now identical to existingTree\n     * ```\n     */\n    static deserialize(data, hashFn) {\n        // Parse the JSON string from the buffer\n        const json = JSON.parse(data.toString('utf8'));\n        /**\n         * Helper function to recursively deserialize nodes\n         * Converts hex strings back to Buffers and reconstructs the tree structure\n         *\n         * @param obj - JSON object representing a node\n         * @returns Reconstructed BinaryTreeNode or null\n         */\n        function deserializeNode(obj) {\n            // Handle null nodes\n            if (obj === null)\n                return null;\n            // Case 1: Reconstruct stem (leaf) node\n            if (obj.nodeType === 'stem') {\n                // Convert hex stem back to Buffer\n                const node = new StemNode(buffer_1.Buffer.from(obj.stem, 'hex'));\n                // Convert hex values back to Buffers, preserving nulls\n                node.values = obj.values.map((v) => (v !== null ? buffer_1.Buffer.from(v, 'hex') : null));\n                return node;\n            }\n            else if (obj.nodeType === 'internal') { // Case 2: Reconstruct internal node\n                const node = new InternalNode();\n                // Recursively deserialize left and right subtrees\n                node.left = deserializeNode(obj.left);\n                node.right = deserializeNode(obj.right);\n                return node;\n            }\n            // Invalid node type\n            return null;\n        }\n        // Create new tree with provided hash function\n        const tree = new UnifiedBinaryTree(hashFn);\n        // Deserialize and set the root node\n        tree.root = deserializeNode(json.root);\n        return tree;\n    }\n    /**\n     * Splits a leaf node when inserting a new key with a different stem.\n     * This method handles two cases:\n     * 1. Matching bits at current depth: Continue splitting recursively\n     * 2. Different bits at current depth: Create new internal node and arrange leaves\n     *\n     * The process ensures that keys with different stems are properly distributed\n     * in the tree based on their binary representation.\n     *\n     * @param leaf - The existing leaf node to split\n     * @param stemBits - Binary representation of the new stem\n     * @param existingStemBits - Binary representation of the existing stem\n     * @param subIndex - Position within leaf node for new value\n     * @param value - Value to store at the new position\n     * @param depth - Current depth in the tree\n     * @returns A new internal node containing both the existing and new data\n     *\n     * Example:\n     * If stems differ at bit 3:\n     * - New stem:      [1,0,1,0,...]\n     * - Existing stem: [1,0,1,1,...]\n     *                        ^ split here\n     * Creates an internal node with the leaf nodes arranged based on bit 3\n     */\n    splitLeaf(leaf, stemBits, existingStemBits, subIndex, value, depth) {\n        // Case 1: Bits match at current depth, need to go deeper\n        if (stemBits[depth] === existingStemBits[depth]) {\n            const newInternal = new InternalNode();\n            const bit = stemBits[depth];\n            // Continue splitting recursively in the matching direction\n            if (bit === 0) {\n                newInternal.left = this.splitLeaf(leaf, stemBits, existingStemBits, subIndex, value, depth + 1);\n            }\n            else {\n                newInternal.right = this.splitLeaf(leaf, stemBits, existingStemBits, subIndex, value, depth + 1);\n            }\n            return newInternal;\n        }\n        else { // Case 2: Bits differ at current depth, create split point\n            const newInternal = new InternalNode();\n            const bit = stemBits[depth];\n            // Create new leaf node for the new stem\n            const newStem = this.bitsToBytes(stemBits);\n            const newNode = new StemNode(newStem);\n            newNode.setValue(subIndex, value);\n            // Arrange nodes based on their bits at current depth\n            // bit = 0: new node goes left, existing goes right\n            // bit = 1: new node goes right, existing goes left\n            if (bit === 0) {\n                newInternal.left = newNode;\n                newInternal.right = leaf;\n            }\n            else {\n                newInternal.right = newNode;\n                newInternal.left = leaf;\n            }\n            return newInternal;\n        }\n    }\n}\nexports.UnifiedBinaryTree = UnifiedBinaryTree;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/merkletreejs/dist/UnifiedBinaryTree.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/merkletreejs/dist/index.js":
/*!**************************************************!*\
  !*** ../node_modules/merkletreejs/dist/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MerkleTree = void 0;\nconst MerkleTree_1 = __importDefault(__webpack_require__(/*! ./MerkleTree */ \"(rsc)/../node_modules/merkletreejs/dist/MerkleTree.js\"));\nexports.MerkleTree = MerkleTree_1.default;\nvar MerkleMountainRange_1 = __webpack_require__(/*! ./MerkleMountainRange */ \"(rsc)/../node_modules/merkletreejs/dist/MerkleMountainRange.js\");\nObject.defineProperty(exports, \"MerkleMountainRange\", ({ enumerable: true, get: function () { return MerkleMountainRange_1.MerkleMountainRange; } }));\nvar IncrementalMerkleTree_1 = __webpack_require__(/*! ./IncrementalMerkleTree */ \"(rsc)/../node_modules/merkletreejs/dist/IncrementalMerkleTree.js\");\nObject.defineProperty(exports, \"IncrementalMerkleTree\", ({ enumerable: true, get: function () { return IncrementalMerkleTree_1.IncrementalMerkleTree; } }));\nvar MerkleSumTree_1 = __webpack_require__(/*! ./MerkleSumTree */ \"(rsc)/../node_modules/merkletreejs/dist/MerkleSumTree.js\");\nObject.defineProperty(exports, \"MerkleSumTree\", ({ enumerable: true, get: function () { return MerkleSumTree_1.MerkleSumTree; } }));\nvar MerkleRadixTree_1 = __webpack_require__(/*! ./MerkleRadixTree */ \"(rsc)/../node_modules/merkletreejs/dist/MerkleRadixTree.js\");\nObject.defineProperty(exports, \"MerkleRadixTree\", ({ enumerable: true, get: function () { return MerkleRadixTree_1.MerkleRadixTree; } }));\nvar UnifiedBinaryTree_1 = __webpack_require__(/*! ./UnifiedBinaryTree */ \"(rsc)/../node_modules/merkletreejs/dist/UnifiedBinaryTree.js\");\nObject.defineProperty(exports, \"UnifiedBinaryTree\", ({ enumerable: true, get: function () { return UnifiedBinaryTree_1.UnifiedBinaryTree; } }));\nexports[\"default\"] = MerkleTree_1.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/merkletreejs/dist/index.js\n");

/***/ })

};
;