"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-rpc-errors";
exports.ids = ["vendor-chunks/eth-rpc-errors"];
exports.modules = {

/***/ "(ssr)/../node_modules/eth-rpc-errors/dist/classes.js":
/*!******************************************************!*\
  !*** ../node_modules/eth-rpc-errors/dist/classes.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EthereumProviderError = exports.EthereumRpcError = void 0;\nconst fast_safe_stringify_1 = __webpack_require__(/*! fast-safe-stringify */ \"(ssr)/../node_modules/fast-safe-stringify/index.js\");\n/**\n * Error subclass implementing JSON RPC 2.0 errors and Ethereum RPC errors\n * per EIP-1474.\n * Permits any integer error code.\n */\nclass EthereumRpcError extends Error {\n    constructor(code, message, data) {\n        if (!Number.isInteger(code)) {\n            throw new Error('\"code\" must be an integer.');\n        }\n        if (!message || typeof message !== 'string') {\n            throw new Error('\"message\" must be a nonempty string.');\n        }\n        super(message);\n        this.code = code;\n        if (data !== undefined) {\n            this.data = data;\n        }\n    }\n    /**\n     * Returns a plain object with all public class properties.\n     */\n    serialize() {\n        const serialized = {\n            code: this.code,\n            message: this.message,\n        };\n        if (this.data !== undefined) {\n            serialized.data = this.data;\n        }\n        if (this.stack) {\n            serialized.stack = this.stack;\n        }\n        return serialized;\n    }\n    /**\n     * Return a string representation of the serialized error, omitting\n     * any circular references.\n     */\n    toString() {\n        return fast_safe_stringify_1.default(this.serialize(), stringifyReplacer, 2);\n    }\n}\nexports.EthereumRpcError = EthereumRpcError;\n/**\n * Error subclass implementing Ethereum Provider errors per EIP-1193.\n * Permits integer error codes in the [ 1000 <= 4999 ] range.\n */\nclass EthereumProviderError extends EthereumRpcError {\n    /**\n     * Create an Ethereum Provider JSON-RPC error.\n     * `code` must be an integer in the 1000 <= 4999 range.\n     */\n    constructor(code, message, data) {\n        if (!isValidEthProviderCode(code)) {\n            throw new Error('\"code\" must be an integer such that: 1000 <= code <= 4999');\n        }\n        super(code, message, data);\n    }\n}\nexports.EthereumProviderError = EthereumProviderError;\n// Internal\nfunction isValidEthProviderCode(code) {\n    return Number.isInteger(code) && code >= 1000 && code <= 4999;\n}\nfunction stringifyReplacer(_, value) {\n    if (value === '[Circular]') {\n        return undefined;\n    }\n    return value;\n}\n//# sourceMappingURL=data:application/json;base64,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//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-rpc-errors/dist/classes.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-rpc-errors/dist/error-constants.js":
/*!**************************************************************!*\
  !*** ../node_modules/eth-rpc-errors/dist/error-constants.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.errorValues = exports.errorCodes = void 0;\nexports.errorCodes = {\n    rpc: {\n        invalidInput: -32000,\n        resourceNotFound: -32001,\n        resourceUnavailable: -32002,\n        transactionRejected: -32003,\n        methodNotSupported: -32004,\n        limitExceeded: -32005,\n        parse: -32700,\n        invalidRequest: -32600,\n        methodNotFound: -32601,\n        invalidParams: -32602,\n        internal: -32603,\n    },\n    provider: {\n        userRejectedRequest: 4001,\n        unauthorized: 4100,\n        unsupportedMethod: 4200,\n        disconnected: 4900,\n        chainDisconnected: 4901,\n    },\n};\nexports.errorValues = {\n    '-32700': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n    },\n    '-32600': {\n        standard: 'JSON RPC 2.0',\n        message: 'The JSON sent is not a valid Request object.',\n    },\n    '-32601': {\n        standard: 'JSON RPC 2.0',\n        message: 'The method does not exist / is not available.',\n    },\n    '-32602': {\n        standard: 'JSON RPC 2.0',\n        message: 'Invalid method parameter(s).',\n    },\n    '-32603': {\n        standard: 'JSON RPC 2.0',\n        message: 'Internal JSON-RPC error.',\n    },\n    '-32000': {\n        standard: 'EIP-1474',\n        message: 'Invalid input.',\n    },\n    '-32001': {\n        standard: 'EIP-1474',\n        message: 'Resource not found.',\n    },\n    '-32002': {\n        standard: 'EIP-1474',\n        message: 'Resource unavailable.',\n    },\n    '-32003': {\n        standard: 'EIP-1474',\n        message: 'Transaction rejected.',\n    },\n    '-32004': {\n        standard: 'EIP-1474',\n        message: 'Method not supported.',\n    },\n    '-32005': {\n        standard: 'EIP-1474',\n        message: 'Request limit exceeded.',\n    },\n    '4001': {\n        standard: 'EIP-1193',\n        message: 'User rejected the request.',\n    },\n    '4100': {\n        standard: 'EIP-1193',\n        message: 'The requested account and/or method has not been authorized by the user.',\n    },\n    '4200': {\n        standard: 'EIP-1193',\n        message: 'The requested method is not supported by this Ethereum provider.',\n    },\n    '4900': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from all chains.',\n    },\n    '4901': {\n        standard: 'EIP-1193',\n        message: 'The provider is disconnected from the specified chain.',\n    },\n};\n//# sourceMappingURL=data:application/json;base64,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//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ycGMtZXJyb3JzL2Rpc3QvZXJyb3ItY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQixHQUFHLGtCQUFrQjtBQUN4QyxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSwyQ0FBMkMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLXJwYy1lcnJvcnMvZGlzdC9lcnJvci1jb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmVycm9yVmFsdWVzID0gZXhwb3J0cy5lcnJvckNvZGVzID0gdm9pZCAwO1xuZXhwb3J0cy5lcnJvckNvZGVzID0ge1xuICAgIHJwYzoge1xuICAgICAgICBpbnZhbGlkSW5wdXQ6IC0zMjAwMCxcbiAgICAgICAgcmVzb3VyY2VOb3RGb3VuZDogLTMyMDAxLFxuICAgICAgICByZXNvdXJjZVVuYXZhaWxhYmxlOiAtMzIwMDIsXG4gICAgICAgIHRyYW5zYWN0aW9uUmVqZWN0ZWQ6IC0zMjAwMyxcbiAgICAgICAgbWV0aG9kTm90U3VwcG9ydGVkOiAtMzIwMDQsXG4gICAgICAgIGxpbWl0RXhjZWVkZWQ6IC0zMjAwNSxcbiAgICAgICAgcGFyc2U6IC0zMjcwMCxcbiAgICAgICAgaW52YWxpZFJlcXVlc3Q6IC0zMjYwMCxcbiAgICAgICAgbWV0aG9kTm90Rm91bmQ6IC0zMjYwMSxcbiAgICAgICAgaW52YWxpZFBhcmFtczogLTMyNjAyLFxuICAgICAgICBpbnRlcm5hbDogLTMyNjAzLFxuICAgIH0sXG4gICAgcHJvdmlkZXI6IHtcbiAgICAgICAgdXNlclJlamVjdGVkUmVxdWVzdDogNDAwMSxcbiAgICAgICAgdW5hdXRob3JpemVkOiA0MTAwLFxuICAgICAgICB1bnN1cHBvcnRlZE1ldGhvZDogNDIwMCxcbiAgICAgICAgZGlzY29ubmVjdGVkOiA0OTAwLFxuICAgICAgICBjaGFpbkRpc2Nvbm5lY3RlZDogNDkwMSxcbiAgICB9LFxufTtcbmV4cG9ydHMuZXJyb3JWYWx1ZXMgPSB7XG4gICAgJy0zMjcwMCc6IHtcbiAgICAgICAgc3RhbmRhcmQ6ICdKU09OIFJQQyAyLjAnLFxuICAgICAgICBtZXNzYWdlOiAnSW52YWxpZCBKU09OIHdhcyByZWNlaXZlZCBieSB0aGUgc2VydmVyLiBBbiBlcnJvciBvY2N1cnJlZCBvbiB0aGUgc2VydmVyIHdoaWxlIHBhcnNpbmcgdGhlIEpTT04gdGV4dC4nLFxuICAgIH0sXG4gICAgJy0zMjYwMCc6IHtcbiAgICAgICAgc3RhbmRhcmQ6ICdKU09OIFJQQyAyLjAnLFxuICAgICAgICBtZXNzYWdlOiAnVGhlIEpTT04gc2VudCBpcyBub3QgYSB2YWxpZCBSZXF1ZXN0IG9iamVjdC4nLFxuICAgIH0sXG4gICAgJy0zMjYwMSc6IHtcbiAgICAgICAgc3RhbmRhcmQ6ICdKU09OIFJQQyAyLjAnLFxuICAgICAgICBtZXNzYWdlOiAnVGhlIG1ldGhvZCBkb2VzIG5vdCBleGlzdCAvIGlzIG5vdCBhdmFpbGFibGUuJyxcbiAgICB9LFxuICAgICctMzI2MDInOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnSlNPTiBSUEMgMi4wJyxcbiAgICAgICAgbWVzc2FnZTogJ0ludmFsaWQgbWV0aG9kIHBhcmFtZXRlcihzKS4nLFxuICAgIH0sXG4gICAgJy0zMjYwMyc6IHtcbiAgICAgICAgc3RhbmRhcmQ6ICdKU09OIFJQQyAyLjAnLFxuICAgICAgICBtZXNzYWdlOiAnSW50ZXJuYWwgSlNPTi1SUEMgZXJyb3IuJyxcbiAgICB9LFxuICAgICctMzIwMDAnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTE0NzQnLFxuICAgICAgICBtZXNzYWdlOiAnSW52YWxpZCBpbnB1dC4nLFxuICAgIH0sXG4gICAgJy0zMjAwMSc6IHtcbiAgICAgICAgc3RhbmRhcmQ6ICdFSVAtMTQ3NCcsXG4gICAgICAgIG1lc3NhZ2U6ICdSZXNvdXJjZSBub3QgZm91bmQuJyxcbiAgICB9LFxuICAgICctMzIwMDInOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTE0NzQnLFxuICAgICAgICBtZXNzYWdlOiAnUmVzb3VyY2UgdW5hdmFpbGFibGUuJyxcbiAgICB9LFxuICAgICctMzIwMDMnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTE0NzQnLFxuICAgICAgICBtZXNzYWdlOiAnVHJhbnNhY3Rpb24gcmVqZWN0ZWQuJyxcbiAgICB9LFxuICAgICctMzIwMDQnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTE0NzQnLFxuICAgICAgICBtZXNzYWdlOiAnTWV0aG9kIG5vdCBzdXBwb3J0ZWQuJyxcbiAgICB9LFxuICAgICctMzIwMDUnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTE0NzQnLFxuICAgICAgICBtZXNzYWdlOiAnUmVxdWVzdCBsaW1pdCBleGNlZWRlZC4nLFxuICAgIH0sXG4gICAgJzQwMDEnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTExOTMnLFxuICAgICAgICBtZXNzYWdlOiAnVXNlciByZWplY3RlZCB0aGUgcmVxdWVzdC4nLFxuICAgIH0sXG4gICAgJzQxMDAnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTExOTMnLFxuICAgICAgICBtZXNzYWdlOiAnVGhlIHJlcXVlc3RlZCBhY2NvdW50IGFuZC9vciBtZXRob2QgaGFzIG5vdCBiZWVuIGF1dGhvcml6ZWQgYnkgdGhlIHVzZXIuJyxcbiAgICB9LFxuICAgICc0MjAwJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xMTkzJyxcbiAgICAgICAgbWVzc2FnZTogJ1RoZSByZXF1ZXN0ZWQgbWV0aG9kIGlzIG5vdCBzdXBwb3J0ZWQgYnkgdGhpcyBFdGhlcmV1bSBwcm92aWRlci4nLFxuICAgIH0sXG4gICAgJzQ5MDAnOiB7XG4gICAgICAgIHN0YW5kYXJkOiAnRUlQLTExOTMnLFxuICAgICAgICBtZXNzYWdlOiAnVGhlIHByb3ZpZGVyIGlzIGRpc2Nvbm5lY3RlZCBmcm9tIGFsbCBjaGFpbnMuJyxcbiAgICB9LFxuICAgICc0OTAxJzoge1xuICAgICAgICBzdGFuZGFyZDogJ0VJUC0xMTkzJyxcbiAgICAgICAgbWVzc2FnZTogJ1RoZSBwcm92aWRlciBpcyBkaXNjb25uZWN0ZWQgZnJvbSB0aGUgc3BlY2lmaWVkIGNoYWluLicsXG4gICAgfSxcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kYXRhOmFwcGxpY2F0aW9uL2pzb247YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0ptYVd4bElqb2laWEp5YjNJdFkyOXVjM1JoYm5SekxtcHpJaXdpYzI5MWNtTmxVbTl2ZENJNklpSXNJbk52ZFhKalpYTWlPbHNpTGk0dmMzSmpMMlZ5Y205eUxXTnZibk4wWVc1MGN5NTBjeUpkTENKdVlXMWxjeUk2VzEwc0ltMWhjSEJwYm1keklqb2lPenM3UVVGMVFtRXNVVUZCUVN4VlFVRlZMRWRCUVdVN1NVRkRjRU1zUjBGQlJ5eEZRVUZGTzFGQlEwZ3NXVUZCV1N4RlFVRkZMRU5CUVVNc1MwRkJTenRSUVVOd1FpeG5Ra0ZCWjBJc1JVRkJSU3hEUVVGRExFdEJRVXM3VVVGRGVFSXNiVUpCUVcxQ0xFVkJRVVVzUTBGQlF5eExRVUZMTzFGQlF6TkNMRzFDUVVGdFFpeEZRVUZGTEVOQlFVTXNTMEZCU3p0UlFVTXpRaXhyUWtGQmEwSXNSVUZCUlN4RFFVRkRMRXRCUVVzN1VVRkRNVUlzWVVGQllTeEZRVUZGTEVOQlFVTXNTMEZCU3p0UlFVTnlRaXhMUVVGTExFVkJRVVVzUTBGQlF5eExRVUZMTzFGQlEySXNZMEZCWXl4RlFVRkZMRU5CUVVNc1MwRkJTenRSUVVOMFFpeGpRVUZqTEVWQlFVVXNRMEZCUXl4TFFVRkxPMUZCUTNSQ0xHRkJRV0VzUlVGQlJTeERRVUZETEV0QlFVczdVVUZEY2tJc1VVRkJVU3hGUVVGRkxFTkJRVU1zUzBGQlN6dExRVU5xUWp0SlFVTkVMRkZCUVZFc1JVRkJSVHRSUVVOU0xHMUNRVUZ0UWl4RlFVRkZMRWxCUVVrN1VVRkRla0lzV1VGQldTeEZRVUZGTEVsQlFVazdVVUZEYkVJc2FVSkJRV2xDTEVWQlFVVXNTVUZCU1R0UlFVTjJRaXhaUVVGWkxFVkJRVVVzU1VGQlNUdFJRVU5zUWl4cFFrRkJhVUlzUlVGQlJTeEpRVUZKTzB0QlEzaENPME5CUTBZc1EwRkJRenRCUVVWWExGRkJRVUVzVjBGQlZ5eEhRVUZITzBsQlEzcENMRkZCUVZFc1JVRkJSVHRSUVVOU0xGRkJRVkVzUlVGQlJTeGpRVUZqTzFGQlEzaENMRTlCUVU4c1JVRkJSU3gxUjBGQmRVYzdTMEZEYWtnN1NVRkRSQ3hSUVVGUkxFVkJRVVU3VVVGRFVpeFJRVUZSTEVWQlFVVXNZMEZCWXp0UlFVTjRRaXhQUVVGUExFVkJRVVVzT0VOQlFUaERPMHRCUTNoRU8wbEJRMFFzVVVGQlVTeEZRVUZGTzFGQlExSXNVVUZCVVN4RlFVRkZMR05CUVdNN1VVRkRlRUlzVDBGQlR5eEZRVUZGTEN0RFFVRXJRenRMUVVONlJEdEpRVU5FTEZGQlFWRXNSVUZCUlR0UlFVTlNMRkZCUVZFc1JVRkJSU3hqUVVGak8xRkJRM2hDTEU5QlFVOHNSVUZCUlN3NFFrRkJPRUk3UzBGRGVFTTdTVUZEUkN4UlFVRlJMRVZCUVVVN1VVRkRVaXhSUVVGUkxFVkJRVVVzWTBGQll6dFJRVU40UWl4UFFVRlBMRVZCUVVVc01FSkJRVEJDTzB0QlEzQkRPMGxCUTBRc1VVRkJVU3hGUVVGRk8xRkJRMUlzVVVGQlVTeEZRVUZGTEZWQlFWVTdVVUZEY0VJc1QwRkJUeXhGUVVGRkxHZENRVUZuUWp0TFFVTXhRanRKUVVORUxGRkJRVkVzUlVGQlJUdFJRVU5TTEZGQlFWRXNSVUZCUlN4VlFVRlZPMUZCUTNCQ0xFOUJRVThzUlVGQlJTeHhRa0ZCY1VJN1MwRkRMMEk3U1VGRFJDeFJRVUZSTEVWQlFVVTdVVUZEVWl4UlFVRlJMRVZCUVVVc1ZVRkJWVHRSUVVOd1FpeFBRVUZQTEVWQlFVVXNkVUpCUVhWQ08wdEJRMnBETzBsQlEwUXNVVUZCVVN4RlFVRkZPMUZCUTFJc1VVRkJVU3hGUVVGRkxGVkJRVlU3VVVGRGNFSXNUMEZCVHl4RlFVRkZMSFZDUVVGMVFqdExRVU5xUXp0SlFVTkVMRkZCUVZFc1JVRkJSVHRSUVVOU0xGRkJRVkVzUlVGQlJTeFZRVUZWTzFGQlEzQkNMRTlCUVU4c1JVRkJSU3gxUWtGQmRVSTdTMEZEYWtNN1NVRkRSQ3hSUVVGUkxFVkJRVVU3VVVGRFVpeFJRVUZSTEVWQlFVVXNWVUZCVlR0UlFVTndRaXhQUVVGUExFVkJRVVVzZVVKQlFYbENPMHRCUTI1RE8wbEJRMFFzVFVGQlRTeEZRVUZGTzFGQlEwNHNVVUZCVVN4RlFVRkZMRlZCUVZVN1VVRkRjRUlzVDBGQlR5eEZRVUZGTERSQ1FVRTBRanRMUVVOMFF6dEpRVU5FTEUxQlFVMHNSVUZCUlR0UlFVTk9MRkZCUVZFc1JVRkJSU3hWUVVGVk8xRkJRM0JDTEU5QlFVOHNSVUZCUlN3d1JVRkJNRVU3UzBGRGNFWTdTVUZEUkN4TlFVRk5MRVZCUVVVN1VVRkRUaXhSUVVGUkxFVkJRVVVzVlVGQlZUdFJRVU53UWl4UFFVRlBMRVZCUVVVc2EwVkJRV3RGTzB0QlF6VkZPMGxCUTBRc1RVRkJUU3hGUVVGRk8xRkJRMDRzVVVGQlVTeEZRVUZGTEZWQlFWVTdVVUZEY0VJc1QwRkJUeXhGUVVGRkxDdERRVUVyUXp0TFFVTjZSRHRKUVVORUxFMUJRVTBzUlVGQlJUdFJRVU5PTEZGQlFWRXNSVUZCUlN4VlFVRlZPMUZCUTNCQ0xFOUJRVThzUlVGQlJTeDNSRUZCZDBRN1MwRkRiRVU3UTBGRFJpeERRVUZESW4wPSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-rpc-errors/dist/error-constants.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-rpc-errors/dist/errors.js":
/*!*****************************************************!*\
  !*** ../node_modules/eth-rpc-errors/dist/errors.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ethErrors = void 0;\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/../node_modules/eth-rpc-errors/dist/classes.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/eth-rpc-errors/dist/utils.js\");\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/../node_modules/eth-rpc-errors/dist/error-constants.js\");\nexports.ethErrors = {\n    rpc: {\n        /**\n         * Get a JSON RPC 2.0 Parse (-32700) error.\n         */\n        parse: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.parse, arg),\n        /**\n         * Get a JSON RPC 2.0 Invalid Request (-32600) error.\n         */\n        invalidRequest: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidRequest, arg),\n        /**\n         * Get a JSON RPC 2.0 Invalid Params (-32602) error.\n         */\n        invalidParams: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidParams, arg),\n        /**\n         * Get a JSON RPC 2.0 Method Not Found (-32601) error.\n         */\n        methodNotFound: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.methodNotFound, arg),\n        /**\n         * Get a JSON RPC 2.0 Internal (-32603) error.\n         */\n        internal: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.internal, arg),\n        /**\n         * Get a JSON RPC 2.0 Server error.\n         * Permits integer error codes in the [ -32099 <= -32005 ] range.\n         * Codes -32000 through -32004 are reserved by EIP-1474.\n         */\n        server: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum RPC Server errors must provide single object argument.');\n            }\n            const { code } = opts;\n            if (!Number.isInteger(code) || code > -32005 || code < -32099) {\n                throw new Error('\"code\" must be an integer such that: -32099 <= code <= -32005');\n            }\n            return getEthJsonRpcError(code, opts);\n        },\n        /**\n         * Get an Ethereum JSON RPC Invalid Input (-32000) error.\n         */\n        invalidInput: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.invalidInput, arg),\n        /**\n         * Get an Ethereum JSON RPC Resource Not Found (-32001) error.\n         */\n        resourceNotFound: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.resourceNotFound, arg),\n        /**\n         * Get an Ethereum JSON RPC Resource Unavailable (-32002) error.\n         */\n        resourceUnavailable: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.resourceUnavailable, arg),\n        /**\n         * Get an Ethereum JSON RPC Transaction Rejected (-32003) error.\n         */\n        transactionRejected: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.transactionRejected, arg),\n        /**\n         * Get an Ethereum JSON RPC Method Not Supported (-32004) error.\n         */\n        methodNotSupported: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.methodNotSupported, arg),\n        /**\n         * Get an Ethereum JSON RPC Limit Exceeded (-32005) error.\n         */\n        limitExceeded: (arg) => getEthJsonRpcError(error_constants_1.errorCodes.rpc.limitExceeded, arg),\n    },\n    provider: {\n        /**\n         * Get an Ethereum Provider User Rejected Request (4001) error.\n         */\n        userRejectedRequest: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.userRejectedRequest, arg);\n        },\n        /**\n         * Get an Ethereum Provider Unauthorized (4100) error.\n         */\n        unauthorized: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.unauthorized, arg);\n        },\n        /**\n         * Get an Ethereum Provider Unsupported Method (4200) error.\n         */\n        unsupportedMethod: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.unsupportedMethod, arg);\n        },\n        /**\n         * Get an Ethereum Provider Not Connected (4900) error.\n         */\n        disconnected: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.disconnected, arg);\n        },\n        /**\n         * Get an Ethereum Provider Chain Not Connected (4901) error.\n         */\n        chainDisconnected: (arg) => {\n            return getEthProviderError(error_constants_1.errorCodes.provider.chainDisconnected, arg);\n        },\n        /**\n         * Get a custom Ethereum Provider error.\n         */\n        custom: (opts) => {\n            if (!opts || typeof opts !== 'object' || Array.isArray(opts)) {\n                throw new Error('Ethereum Provider custom errors must provide single object argument.');\n            }\n            const { code, message, data } = opts;\n            if (!message || typeof message !== 'string') {\n                throw new Error('\"message\" must be a nonempty string');\n            }\n            return new classes_1.EthereumProviderError(code, message, data);\n        },\n    },\n};\n// Internal\nfunction getEthJsonRpcError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new classes_1.EthereumRpcError(code, message || utils_1.getMessageFromCode(code), data);\n}\nfunction getEthProviderError(code, arg) {\n    const [message, data] = parseOpts(arg);\n    return new classes_1.EthereumProviderError(code, message || utils_1.getMessageFromCode(code), data);\n}\nfunction parseOpts(arg) {\n    if (arg) {\n        if (typeof arg === 'string') {\n            return [arg];\n        }\n        else if (typeof arg === 'object' && !Array.isArray(arg)) {\n            const { message, data } = arg;\n            if (message && typeof message !== 'string') {\n                throw new Error('Must specify string message.');\n            }\n            return [message || undefined, data];\n        }\n    }\n    return [];\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-rpc-errors/dist/errors.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-rpc-errors/dist/index.js":
/*!****************************************************!*\
  !*** ../node_modules/eth-rpc-errors/dist/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getMessageFromCode = exports.serializeError = exports.EthereumProviderError = exports.EthereumRpcError = exports.ethErrors = exports.errorCodes = void 0;\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/../node_modules/eth-rpc-errors/dist/classes.js\");\nObject.defineProperty(exports, \"EthereumRpcError\", ({ enumerable: true, get: function () { return classes_1.EthereumRpcError; } }));\nObject.defineProperty(exports, \"EthereumProviderError\", ({ enumerable: true, get: function () { return classes_1.EthereumProviderError; } }));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/eth-rpc-errors/dist/utils.js\");\nObject.defineProperty(exports, \"serializeError\", ({ enumerable: true, get: function () { return utils_1.serializeError; } }));\nObject.defineProperty(exports, \"getMessageFromCode\", ({ enumerable: true, get: function () { return utils_1.getMessageFromCode; } }));\nconst errors_1 = __webpack_require__(/*! ./errors */ \"(ssr)/../node_modules/eth-rpc-errors/dist/errors.js\");\nObject.defineProperty(exports, \"ethErrors\", ({ enumerable: true, get: function () { return errors_1.ethErrors; } }));\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/../node_modules/eth-rpc-errors/dist/error-constants.js\");\nObject.defineProperty(exports, \"errorCodes\", ({ enumerable: true, get: function () { return error_constants_1.errorCodes; } }));\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsdUNBQW9FO0FBVWxFLGlHQVZPLDBCQUFnQixPQVVQO0FBQ2hCLHNHQVh5QiwrQkFBcUIsT0FXekI7QUFWdkIsbUNBRWlCO0FBU2YsK0ZBVkEsc0JBQWMsT0FVQTtBQUNkLG1HQVhnQiwwQkFBa0IsT0FXaEI7QUFUcEIscUNBQXFDO0FBS25DLDBGQUxPLGtCQUFTLE9BS1A7QUFKWCx1REFBK0M7QUFHN0MsMkZBSE8sNEJBQVUsT0FHUCJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-rpc-errors/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-rpc-errors/dist/utils.js":
/*!****************************************************!*\
  !*** ../node_modules/eth-rpc-errors/dist/utils.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.serializeError = exports.isValidCode = exports.getMessageFromCode = exports.JSON_RPC_SERVER_ERROR_MESSAGE = void 0;\nconst error_constants_1 = __webpack_require__(/*! ./error-constants */ \"(ssr)/../node_modules/eth-rpc-errors/dist/error-constants.js\");\nconst classes_1 = __webpack_require__(/*! ./classes */ \"(ssr)/../node_modules/eth-rpc-errors/dist/classes.js\");\nconst FALLBACK_ERROR_CODE = error_constants_1.errorCodes.rpc.internal;\nconst FALLBACK_MESSAGE = 'Unspecified error message. This is a bug, please report it.';\nconst FALLBACK_ERROR = {\n    code: FALLBACK_ERROR_CODE,\n    message: getMessageFromCode(FALLBACK_ERROR_CODE),\n};\nexports.JSON_RPC_SERVER_ERROR_MESSAGE = 'Unspecified server error.';\n/**\n * Gets the message for a given code, or a fallback message if the code has\n * no corresponding message.\n */\nfunction getMessageFromCode(code, fallbackMessage = FALLBACK_MESSAGE) {\n    if (Number.isInteger(code)) {\n        const codeString = code.toString();\n        if (hasKey(error_constants_1.errorValues, codeString)) {\n            return error_constants_1.errorValues[codeString].message;\n        }\n        if (isJsonRpcServerError(code)) {\n            return exports.JSON_RPC_SERVER_ERROR_MESSAGE;\n        }\n    }\n    return fallbackMessage;\n}\nexports.getMessageFromCode = getMessageFromCode;\n/**\n * Returns whether the given code is valid.\n * A code is only valid if it has a message.\n */\nfunction isValidCode(code) {\n    if (!Number.isInteger(code)) {\n        return false;\n    }\n    const codeString = code.toString();\n    if (error_constants_1.errorValues[codeString]) {\n        return true;\n    }\n    if (isJsonRpcServerError(code)) {\n        return true;\n    }\n    return false;\n}\nexports.isValidCode = isValidCode;\n/**\n * Serializes the given error to an Ethereum JSON RPC-compatible error object.\n * Merely copies the given error's values if it is already compatible.\n * If the given error is not fully compatible, it will be preserved on the\n * returned object's data.originalError property.\n */\nfunction serializeError(error, { fallbackError = FALLBACK_ERROR, shouldIncludeStack = false, } = {}) {\n    var _a, _b;\n    if (!fallbackError ||\n        !Number.isInteger(fallbackError.code) ||\n        typeof fallbackError.message !== 'string') {\n        throw new Error('Must provide fallback error with integer number code and string message.');\n    }\n    if (error instanceof classes_1.EthereumRpcError) {\n        return error.serialize();\n    }\n    const serialized = {};\n    if (error &&\n        typeof error === 'object' &&\n        !Array.isArray(error) &&\n        hasKey(error, 'code') &&\n        isValidCode(error.code)) {\n        const _error = error;\n        serialized.code = _error.code;\n        if (_error.message && typeof _error.message === 'string') {\n            serialized.message = _error.message;\n            if (hasKey(_error, 'data')) {\n                serialized.data = _error.data;\n            }\n        }\n        else {\n            serialized.message = getMessageFromCode(serialized.code);\n            serialized.data = { originalError: assignOriginalError(error) };\n        }\n    }\n    else {\n        serialized.code = fallbackError.code;\n        const message = (_a = error) === null || _a === void 0 ? void 0 : _a.message;\n        serialized.message = (message && typeof message === 'string'\n            ? message\n            : fallbackError.message);\n        serialized.data = { originalError: assignOriginalError(error) };\n    }\n    const stack = (_b = error) === null || _b === void 0 ? void 0 : _b.stack;\n    if (shouldIncludeStack && error && stack && typeof stack === 'string') {\n        serialized.stack = stack;\n    }\n    return serialized;\n}\nexports.serializeError = serializeError;\n// Internal\nfunction isJsonRpcServerError(code) {\n    return code >= -32099 && code <= -32000;\n}\nfunction assignOriginalError(error) {\n    if (error && typeof error === 'object' && !Array.isArray(error)) {\n        return Object.assign({}, error);\n    }\n    return error;\n}\nfunction hasKey(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ycGMtZXJyb3JzL2Rpc3QvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsc0JBQXNCLEdBQUcsbUJBQW1CLEdBQUcsMEJBQTBCLEdBQUcscUNBQXFDO0FBQ2pILDBCQUEwQixtQkFBTyxDQUFDLHVGQUFtQjtBQUNyRCxrQkFBa0IsbUJBQU8sQ0FBQyx1RUFBVztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsOERBQThELElBQUk7QUFDbkc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ycGMtZXJyb3JzL2Rpc3QvdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnNlcmlhbGl6ZUVycm9yID0gZXhwb3J0cy5pc1ZhbGlkQ29kZSA9IGV4cG9ydHMuZ2V0TWVzc2FnZUZyb21Db2RlID0gZXhwb3J0cy5KU09OX1JQQ19TRVJWRVJfRVJST1JfTUVTU0FHRSA9IHZvaWQgMDtcbmNvbnN0IGVycm9yX2NvbnN0YW50c18xID0gcmVxdWlyZShcIi4vZXJyb3ItY29uc3RhbnRzXCIpO1xuY29uc3QgY2xhc3Nlc18xID0gcmVxdWlyZShcIi4vY2xhc3Nlc1wiKTtcbmNvbnN0IEZBTExCQUNLX0VSUk9SX0NPREUgPSBlcnJvcl9jb25zdGFudHNfMS5lcnJvckNvZGVzLnJwYy5pbnRlcm5hbDtcbmNvbnN0IEZBTExCQUNLX01FU1NBR0UgPSAnVW5zcGVjaWZpZWQgZXJyb3IgbWVzc2FnZS4gVGhpcyBpcyBhIGJ1ZywgcGxlYXNlIHJlcG9ydCBpdC4nO1xuY29uc3QgRkFMTEJBQ0tfRVJST1IgPSB7XG4gICAgY29kZTogRkFMTEJBQ0tfRVJST1JfQ09ERSxcbiAgICBtZXNzYWdlOiBnZXRNZXNzYWdlRnJvbUNvZGUoRkFMTEJBQ0tfRVJST1JfQ09ERSksXG59O1xuZXhwb3J0cy5KU09OX1JQQ19TRVJWRVJfRVJST1JfTUVTU0FHRSA9ICdVbnNwZWNpZmllZCBzZXJ2ZXIgZXJyb3IuJztcbi8qKlxuICogR2V0cyB0aGUgbWVzc2FnZSBmb3IgYSBnaXZlbiBjb2RlLCBvciBhIGZhbGxiYWNrIG1lc3NhZ2UgaWYgdGhlIGNvZGUgaGFzXG4gKiBubyBjb3JyZXNwb25kaW5nIG1lc3NhZ2UuXG4gKi9cbmZ1bmN0aW9uIGdldE1lc3NhZ2VGcm9tQ29kZShjb2RlLCBmYWxsYmFja01lc3NhZ2UgPSBGQUxMQkFDS19NRVNTQUdFKSB7XG4gICAgaWYgKE51bWJlci5pc0ludGVnZXIoY29kZSkpIHtcbiAgICAgICAgY29uc3QgY29kZVN0cmluZyA9IGNvZGUudG9TdHJpbmcoKTtcbiAgICAgICAgaWYgKGhhc0tleShlcnJvcl9jb25zdGFudHNfMS5lcnJvclZhbHVlcywgY29kZVN0cmluZykpIHtcbiAgICAgICAgICAgIHJldHVybiBlcnJvcl9jb25zdGFudHNfMS5lcnJvclZhbHVlc1tjb2RlU3RyaW5nXS5tZXNzYWdlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChpc0pzb25ScGNTZXJ2ZXJFcnJvcihjb2RlKSkge1xuICAgICAgICAgICAgcmV0dXJuIGV4cG9ydHMuSlNPTl9SUENfU0VSVkVSX0VSUk9SX01FU1NBR0U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGZhbGxiYWNrTWVzc2FnZTtcbn1cbmV4cG9ydHMuZ2V0TWVzc2FnZUZyb21Db2RlID0gZ2V0TWVzc2FnZUZyb21Db2RlO1xuLyoqXG4gKiBSZXR1cm5zIHdoZXRoZXIgdGhlIGdpdmVuIGNvZGUgaXMgdmFsaWQuXG4gKiBBIGNvZGUgaXMgb25seSB2YWxpZCBpZiBpdCBoYXMgYSBtZXNzYWdlLlxuICovXG5mdW5jdGlvbiBpc1ZhbGlkQ29kZShjb2RlKSB7XG4gICAgaWYgKCFOdW1iZXIuaXNJbnRlZ2VyKGNvZGUpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3QgY29kZVN0cmluZyA9IGNvZGUudG9TdHJpbmcoKTtcbiAgICBpZiAoZXJyb3JfY29uc3RhbnRzXzEuZXJyb3JWYWx1ZXNbY29kZVN0cmluZ10pIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGlmIChpc0pzb25ScGNTZXJ2ZXJFcnJvcihjb2RlKSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufVxuZXhwb3J0cy5pc1ZhbGlkQ29kZSA9IGlzVmFsaWRDb2RlO1xuLyoqXG4gKiBTZXJpYWxpemVzIHRoZSBnaXZlbiBlcnJvciB0byBhbiBFdGhlcmV1bSBKU09OIFJQQy1jb21wYXRpYmxlIGVycm9yIG9iamVjdC5cbiAqIE1lcmVseSBjb3BpZXMgdGhlIGdpdmVuIGVycm9yJ3MgdmFsdWVzIGlmIGl0IGlzIGFscmVhZHkgY29tcGF0aWJsZS5cbiAqIElmIHRoZSBnaXZlbiBlcnJvciBpcyBub3QgZnVsbHkgY29tcGF0aWJsZSwgaXQgd2lsbCBiZSBwcmVzZXJ2ZWQgb24gdGhlXG4gKiByZXR1cm5lZCBvYmplY3QncyBkYXRhLm9yaWdpbmFsRXJyb3IgcHJvcGVydHkuXG4gKi9cbmZ1bmN0aW9uIHNlcmlhbGl6ZUVycm9yKGVycm9yLCB7IGZhbGxiYWNrRXJyb3IgPSBGQUxMQkFDS19FUlJPUiwgc2hvdWxkSW5jbHVkZVN0YWNrID0gZmFsc2UsIH0gPSB7fSkge1xuICAgIHZhciBfYSwgX2I7XG4gICAgaWYgKCFmYWxsYmFja0Vycm9yIHx8XG4gICAgICAgICFOdW1iZXIuaXNJbnRlZ2VyKGZhbGxiYWNrRXJyb3IuY29kZSkgfHxcbiAgICAgICAgdHlwZW9mIGZhbGxiYWNrRXJyb3IubWVzc2FnZSAhPT0gJ3N0cmluZycpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdNdXN0IHByb3ZpZGUgZmFsbGJhY2sgZXJyb3Igd2l0aCBpbnRlZ2VyIG51bWJlciBjb2RlIGFuZCBzdHJpbmcgbWVzc2FnZS4nKTtcbiAgICB9XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgY2xhc3Nlc18xLkV0aGVyZXVtUnBjRXJyb3IpIHtcbiAgICAgICAgcmV0dXJuIGVycm9yLnNlcmlhbGl6ZSgpO1xuICAgIH1cbiAgICBjb25zdCBzZXJpYWxpemVkID0ge307XG4gICAgaWYgKGVycm9yICYmXG4gICAgICAgIHR5cGVvZiBlcnJvciA9PT0gJ29iamVjdCcgJiZcbiAgICAgICAgIUFycmF5LmlzQXJyYXkoZXJyb3IpICYmXG4gICAgICAgIGhhc0tleShlcnJvciwgJ2NvZGUnKSAmJlxuICAgICAgICBpc1ZhbGlkQ29kZShlcnJvci5jb2RlKSkge1xuICAgICAgICBjb25zdCBfZXJyb3IgPSBlcnJvcjtcbiAgICAgICAgc2VyaWFsaXplZC5jb2RlID0gX2Vycm9yLmNvZGU7XG4gICAgICAgIGlmIChfZXJyb3IubWVzc2FnZSAmJiB0eXBlb2YgX2Vycm9yLm1lc3NhZ2UgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICBzZXJpYWxpemVkLm1lc3NhZ2UgPSBfZXJyb3IubWVzc2FnZTtcbiAgICAgICAgICAgIGlmIChoYXNLZXkoX2Vycm9yLCAnZGF0YScpKSB7XG4gICAgICAgICAgICAgICAgc2VyaWFsaXplZC5kYXRhID0gX2Vycm9yLmRhdGE7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzZXJpYWxpemVkLm1lc3NhZ2UgPSBnZXRNZXNzYWdlRnJvbUNvZGUoc2VyaWFsaXplZC5jb2RlKTtcbiAgICAgICAgICAgIHNlcmlhbGl6ZWQuZGF0YSA9IHsgb3JpZ2luYWxFcnJvcjogYXNzaWduT3JpZ2luYWxFcnJvcihlcnJvcikgfTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgc2VyaWFsaXplZC5jb2RlID0gZmFsbGJhY2tFcnJvci5jb2RlO1xuICAgICAgICBjb25zdCBtZXNzYWdlID0gKF9hID0gZXJyb3IpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5tZXNzYWdlO1xuICAgICAgICBzZXJpYWxpemVkLm1lc3NhZ2UgPSAobWVzc2FnZSAmJiB0eXBlb2YgbWVzc2FnZSA9PT0gJ3N0cmluZydcbiAgICAgICAgICAgID8gbWVzc2FnZVxuICAgICAgICAgICAgOiBmYWxsYmFja0Vycm9yLm1lc3NhZ2UpO1xuICAgICAgICBzZXJpYWxpemVkLmRhdGEgPSB7IG9yaWdpbmFsRXJyb3I6IGFzc2lnbk9yaWdpbmFsRXJyb3IoZXJyb3IpIH07XG4gICAgfVxuICAgIGNvbnN0IHN0YWNrID0gKF9iID0gZXJyb3IpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5zdGFjaztcbiAgICBpZiAoc2hvdWxkSW5jbHVkZVN0YWNrICYmIGVycm9yICYmIHN0YWNrICYmIHR5cGVvZiBzdGFjayA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgc2VyaWFsaXplZC5zdGFjayA9IHN0YWNrO1xuICAgIH1cbiAgICByZXR1cm4gc2VyaWFsaXplZDtcbn1cbmV4cG9ydHMuc2VyaWFsaXplRXJyb3IgPSBzZXJpYWxpemVFcnJvcjtcbi8vIEludGVybmFsXG5mdW5jdGlvbiBpc0pzb25ScGNTZXJ2ZXJFcnJvcihjb2RlKSB7XG4gICAgcmV0dXJuIGNvZGUgPj0gLTMyMDk5ICYmIGNvZGUgPD0gLTMyMDAwO1xufVxuZnVuY3Rpb24gYXNzaWduT3JpZ2luYWxFcnJvcihlcnJvcikge1xuICAgIGlmIChlcnJvciAmJiB0eXBlb2YgZXJyb3IgPT09ICdvYmplY3QnICYmICFBcnJheS5pc0FycmF5KGVycm9yKSkge1xuICAgICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbih7fSwgZXJyb3IpO1xuICAgIH1cbiAgICByZXR1cm4gZXJyb3I7XG59XG5mdW5jdGlvbiBoYXNLZXkob2JqLCBrZXkpIHtcbiAgICByZXR1cm4gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG9iaiwga2V5KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRhdGE6YXBwbGljYXRpb24vanNvbjtiYXNlNjQsZXlKMlpYSnphVzl1SWpvekxDSm1hV3hsSWpvaWRYUnBiSE11YW5NaUxDSnpiM1Z5WTJWU2IyOTBJam9pSWl3aWMyOTFjbU5sY3lJNld5SXVMaTl6Y21NdmRYUnBiSE11ZEhNaVhTd2libUZ0WlhNaU9sdGRMQ0p0WVhCd2FXNW5jeUk2SWpzN08wRkJRVUVzZFVSQlFUUkVPMEZCUXpWRUxIVkRRVUY1UlR0QlFVVjZSU3hOUVVGTkxHMUNRVUZ0UWl4SFFVRkhMRFJDUVVGVkxFTkJRVU1zUjBGQlJ5eERRVUZETEZGQlFWRXNRMEZCUXp0QlFVTndSQ3hOUVVGTkxHZENRVUZuUWl4SFFVRkhMRFpFUVVFMlJDeERRVUZETzBGQlEzWkdMRTFCUVUwc1kwRkJZeXhIUVVFclFqdEpRVU5xUkN4SlFVRkpMRVZCUVVVc2JVSkJRVzFDTzBsQlEzcENMRTlCUVU4c1JVRkJSU3hyUWtGQmEwSXNRMEZCUXl4dFFrRkJiVUlzUTBGQlF6dERRVU5xUkN4RFFVRkRPMEZCUlZjc1VVRkJRU3cyUWtGQk5rSXNSMEZCUnl3eVFrRkJNa0lzUTBGQlF6dEJRVWw2UlRzN08wZEJSMGM3UVVGRFNDeFRRVUZuUWl4clFrRkJhMElzUTBGRGFFTXNTVUZCV1N4RlFVTmFMR3RDUVVFd1FpeG5Ra0ZCWjBJN1NVRkZNVU1zU1VGQlNTeE5RVUZOTEVOQlFVTXNVMEZCVXl4RFFVRkRMRWxCUVVrc1EwRkJReXhGUVVGRk8xRkJRekZDTEUxQlFVMHNWVUZCVlN4SFFVRkhMRWxCUVVrc1EwRkJReXhSUVVGUkxFVkJRVVVzUTBGQlF6dFJRVVZ1UXl4SlFVRkpMRTFCUVUwc1EwRkJReXcyUWtGQlZ5eEZRVUZGTEZWQlFWVXNRMEZCUXl4RlFVRkZPMWxCUTI1RExFOUJRVThzTmtKQlFWY3NRMEZCUXl4VlFVRXlRaXhEUVVGRExFTkJRVU1zVDBGQlR5eERRVUZETzFOQlEzcEVPMUZCUTBRc1NVRkJTU3h2UWtGQmIwSXNRMEZCUXl4SlFVRkpMRU5CUVVNc1JVRkJSVHRaUVVNNVFpeFBRVUZQTEhGRFFVRTJRaXhEUVVGRE8xTkJRM1JETzB0QlEwWTdTVUZEUkN4UFFVRlBMR1ZCUVdVc1EwRkJRenRCUVVONlFpeERRVUZETzBGQlprUXNaMFJCWlVNN1FVRkZSRHM3TzBkQlIwYzdRVUZEU0N4VFFVRm5RaXhYUVVGWExFTkJRVU1zU1VGQldUdEpRVU4wUXl4SlFVRkpMRU5CUVVNc1RVRkJUU3hEUVVGRExGTkJRVk1zUTBGQlF5eEpRVUZKTEVOQlFVTXNSVUZCUlR0UlFVTXpRaXhQUVVGUExFdEJRVXNzUTBGQlF6dExRVU5rTzBsQlJVUXNUVUZCVFN4VlFVRlZMRWRCUVVjc1NVRkJTU3hEUVVGRExGRkJRVkVzUlVGQlJTeERRVUZETzBsQlEyNURMRWxCUVVrc05rSkJRVmNzUTBGQlF5eFZRVUV5UWl4RFFVRkRMRVZCUVVVN1VVRkROVU1zVDBGQlR5eEpRVUZKTEVOQlFVTTdTMEZEWWp0SlFVVkVMRWxCUVVrc2IwSkJRVzlDTEVOQlFVTXNTVUZCU1N4RFFVRkRMRVZCUVVVN1VVRkRPVUlzVDBGQlR5eEpRVUZKTEVOQlFVTTdTMEZEWWp0SlFVTkVMRTlCUVU4c1MwRkJTeXhEUVVGRE8wRkJRMllzUTBGQlF6dEJRV1JFTEd0RFFXTkRPMEZCUlVRN096czdPMGRCUzBjN1FVRkRTQ3hUUVVGblFpeGpRVUZqTEVOQlF6VkNMRXRCUVdNc1JVRkRaQ3hGUVVORkxHRkJRV0VzUjBGQlJ5eGpRVUZqTEVWQlF6bENMR3RDUVVGclFpeEhRVUZITEV0QlFVc3NSMEZETTBJc1IwRkJSeXhGUVVGRk96dEpRVWRPTEVsQlEwVXNRMEZCUXl4aFFVRmhPMUZCUTJRc1EwRkJReXhOUVVGTkxFTkJRVU1zVTBGQlV5eERRVUZETEdGQlFXRXNRMEZCUXl4SlFVRkpMRU5CUVVNN1VVRkRja01zVDBGQlR5eGhRVUZoTEVOQlFVTXNUMEZCVHl4TFFVRkxMRkZCUVZFc1JVRkRla003VVVGRFFTeE5RVUZOTEVsQlFVa3NTMEZCU3l4RFFVTmlMREJGUVVFd1JTeERRVU16UlN4RFFVRkRPMHRCUTBnN1NVRkZSQ3hKUVVGSkxFdEJRVXNzV1VGQldTd3dRa0ZCWjBJc1JVRkJSVHRSUVVOeVF5eFBRVUZQTEV0QlFVc3NRMEZCUXl4VFFVRlRMRVZCUVVVc1EwRkJRenRMUVVNeFFqdEpRVVZFTEUxQlFVMHNWVUZCVlN4SFFVRjNReXhGUVVGRkxFTkJRVU03U1VGRk0wUXNTVUZEUlN4TFFVRkxPMUZCUTB3c1QwRkJUeXhMUVVGTExFdEJRVXNzVVVGQlVUdFJRVU42UWl4RFFVRkRMRXRCUVVzc1EwRkJReXhQUVVGUExFTkJRVU1zUzBGQlN5eERRVUZETzFGQlEzSkNMRTFCUVUwc1EwRkJReXhMUVVGblF5eEZRVUZGTEUxQlFVMHNRMEZCUXp0UlFVTm9SQ3hYUVVGWExFTkJRVVVzUzBGQmIwTXNRMEZCUXl4SlFVRkpMRU5CUVVNc1JVRkRka1E3VVVGRFFTeE5RVUZOTEUxQlFVMHNSMEZCUnl4TFFVRTBReXhEUVVGRE8xRkJRelZFTEZWQlFWVXNRMEZCUXl4SlFVRkpMRWRCUVVjc1RVRkJUU3hEUVVGRExFbEJRVWtzUTBGQlF6dFJRVVU1UWl4SlFVRkpMRTFCUVUwc1EwRkJReXhQUVVGUExFbEJRVWtzVDBGQlR5eE5RVUZOTEVOQlFVTXNUMEZCVHl4TFFVRkxMRkZCUVZFc1JVRkJSVHRaUVVONFJDeFZRVUZWTEVOQlFVTXNUMEZCVHl4SFFVRkhMRTFCUVUwc1EwRkJReXhQUVVGUExFTkJRVU03V1VGRmNFTXNTVUZCU1N4TlFVRk5MRU5CUVVNc1RVRkJUU3hGUVVGRkxFMUJRVTBzUTBGQlF5eEZRVUZGTzJkQ1FVTXhRaXhWUVVGVkxFTkJRVU1zU1VGQlNTeEhRVUZITEUxQlFVMHNRMEZCUXl4SlFVRkpMRU5CUVVNN1lVRkRMMEk3VTBGRFJqdGhRVUZOTzFsQlEwd3NWVUZCVlN4RFFVRkRMRTlCUVU4c1IwRkJSeXhyUWtGQmEwSXNRMEZEY0VNc1ZVRkJlVU1zUTBGQlF5eEpRVUZKTEVOQlEyaEVMRU5CUVVNN1dVRkZSaXhWUVVGVkxFTkJRVU1zU1VGQlNTeEhRVUZITEVWQlFVVXNZVUZCWVN4RlFVRkZMRzFDUVVGdFFpeERRVUZETEV0QlFVc3NRMEZCUXl4RlFVRkZMRU5CUVVNN1UwRkRha1U3UzBGRFJqdFRRVUZOTzFGQlEwd3NWVUZCVlN4RFFVRkRMRWxCUVVrc1IwRkJSeXhoUVVGaExFTkJRVU1zU1VGQlNTeERRVUZETzFGQlJYSkRMRTFCUVUwc1QwRkJUeXhUUVVGSkxFdEJRV0VzTUVOQlFVVXNUMEZCVHl4RFFVRkRPMUZCUlhoRExGVkJRVlVzUTBGQlF5eFBRVUZQTEVkQlFVY3NRMEZEYmtJc1QwRkJUeXhKUVVGSkxFOUJRVThzVDBGQlR5eExRVUZMTEZGQlFWRTdXVUZEY0VNc1EwRkJReXhEUVVGRExFOUJRVTg3V1VGRFZDeERRVUZETEVOQlFVTXNZVUZCWVN4RFFVRkRMRTlCUVU4c1EwRkRNVUlzUTBGQlF6dFJRVU5HTEZWQlFWVXNRMEZCUXl4SlFVRkpMRWRCUVVjc1JVRkJSU3hoUVVGaExFVkJRVVVzYlVKQlFXMUNMRU5CUVVNc1MwRkJTeXhEUVVGRExFVkJRVVVzUTBGQlF6dExRVU5xUlR0SlFVVkVMRTFCUVUwc1MwRkJTeXhUUVVGSkxFdEJRV0VzTUVOQlFVVXNTMEZCU3l4RFFVRkRPMGxCUlhCRExFbEJRVWtzYTBKQlFXdENMRWxCUVVrc1MwRkJTeXhKUVVGSkxFdEJRVXNzU1VGQlNTeFBRVUZQTEV0QlFVc3NTMEZCU3l4UlFVRlJMRVZCUVVVN1VVRkRja1VzVlVGQlZTeERRVUZETEV0QlFVc3NSMEZCUnl4TFFVRkxMRU5CUVVNN1MwRkRNVUk3U1VGRFJDeFBRVUZQTEZWQlFYZERMRU5CUVVNN1FVRkRiRVFzUTBGQlF6dEJRV3hGUkN4M1EwRnJSVU03UVVGRlJDeFhRVUZYTzBGQlJWZ3NVMEZCVXl4dlFrRkJiMElzUTBGQlF5eEpRVUZaTzBsQlEzaERMRTlCUVU4c1NVRkJTU3hKUVVGSkxFTkJRVU1zUzBGQlN5eEpRVUZKTEVsQlFVa3NTVUZCU1N4RFFVRkRMRXRCUVVzc1EwRkJRenRCUVVNeFF5eERRVUZETzBGQlJVUXNVMEZCVXl4dFFrRkJiVUlzUTBGQlF5eExRVUZqTzBsQlEzcERMRWxCUVVrc1MwRkJTeXhKUVVGSkxFOUJRVThzUzBGQlN5eExRVUZMTEZGQlFWRXNTVUZCU1N4RFFVRkRMRXRCUVVzc1EwRkJReXhQUVVGUExFTkJRVU1zUzBGQlN5eERRVUZETEVWQlFVVTdVVUZETDBRc1QwRkJUeXhOUVVGTkxFTkJRVU1zVFVGQlRTeERRVUZETEVWQlFVVXNSVUZCUlN4TFFVRkxMRU5CUVVNc1EwRkJRenRMUVVOcVF6dEpRVU5FTEU5QlFVOHNTMEZCU3l4RFFVRkRPMEZCUTJZc1EwRkJRenRCUVVWRUxGTkJRVk1zVFVGQlRTeERRVUZETEVkQlFUUkNMRVZCUVVVc1IwRkJWenRKUVVOMlJDeFBRVUZQTEUxQlFVMHNRMEZCUXl4VFFVRlRMRU5CUVVNc1kwRkJZeXhEUVVGRExFbEJRVWtzUTBGQlF5eEhRVUZITEVWQlFVVXNSMEZCUnl4RFFVRkRMRU5CUVVNN1FVRkRlRVFzUTBGQlF5SjkiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-rpc-errors/dist/utils.js\n");

/***/ })

};
;