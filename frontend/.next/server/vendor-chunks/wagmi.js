"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/wagmi";
exports.ids = ["vendor-chunks/wagmi"];
exports.modules = {

/***/ "(ssr)/../node_modules/wagmi/dist/esm/context.js":
/*!*************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/context.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WagmiContext: () => (/* binding */ WagmiContext),\n/* harmony export */   WagmiProvider: () => (/* binding */ WagmiProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _hydrate_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hydrate.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hydrate.js\");\n/* __next_internal_client_entry_do_not_use__ WagmiContext,WagmiProvider auto */ \n\nconst WagmiContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction WagmiProvider(parameters) {\n    const { children, config } = parameters;\n    const props = {\n        value: config\n    };\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_hydrate_js__WEBPACK_IMPORTED_MODULE_1__.Hydrate, parameters, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WagmiContext.Provider, props, children));\n} //# sourceMappingURL=context.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztnRkFHb0Q7QUFDZDtBQUUvQixNQUFNLFlBQVksaUJBQUcsb0RBQWEsQ0FFdkMsU0FBUyxDQUFDO0FBUU4sU0FBVSxhQUFhLENBQzNCLFVBQXVEO0lBRXZELE1BQU0sRUFBRSxRQUFRLEVBQUUsTUFBTSxFQUFFLEdBQUcsVUFBVTtJQUV2QyxNQUFNLEtBQUssR0FBRztRQUFFLEtBQUssRUFBRSxNQUFNO0lBQUEsQ0FBRTtJQUMvQixxQkFBTyxvREFBYSxDQUNsQixnREFBTyxFQUNQLFVBQVUsZ0JBQ1Ysb0RBQWEsQ0FBQyxZQUFZLENBQUMsUUFBUSxFQUFFLEtBQUssRUFBRSxRQUFRLENBQUMsQ0FDdEQ7QUFDSCxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL3NyYy9jb250ZXh0LnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/context.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/errors/base.js":
/*!*****************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/errors/base.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/errors/base.js\");\n/* harmony import */ var _utils_getVersion_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getVersion.js */ \"(ssr)/../node_modules/wagmi/dist/esm/utils/getVersion.js\");\n\n\nclass BaseError extends _wagmi_core__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super(...arguments);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiError'\n        });\n    }\n    get docsBaseUrl() {\n        return 'https://wagmi.sh/react';\n    }\n    get version() {\n        return (0,_utils_getVersion_js__WEBPACK_IMPORTED_MODULE_1__.getVersion)();\n    }\n}\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2Vycm9ycy9iYXNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUNEO0FBQzdDLHdCQUF3QixrREFBUztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxnRUFBVTtBQUN6QjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vZXJyb3JzL2Jhc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIGFzIENvcmVFcnJvciB9IGZyb20gJ0B3YWdtaS9jb3JlJztcbmltcG9ydCB7IGdldFZlcnNpb24gfSBmcm9tICcuLi91dGlscy9nZXRWZXJzaW9uLmpzJztcbmV4cG9ydCBjbGFzcyBCYXNlRXJyb3IgZXh0ZW5kcyBDb3JlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnV2FnbWlFcnJvcidcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGdldCBkb2NzQmFzZVVybCgpIHtcbiAgICAgICAgcmV0dXJuICdodHRwczovL3dhZ21pLnNoL3JlYWN0JztcbiAgICB9XG4gICAgZ2V0IHZlcnNpb24oKSB7XG4gICAgICAgIHJldHVybiBnZXRWZXJzaW9uKCk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YmFzZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/errors/context.js":
/*!********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/errors/context.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WagmiProviderNotFoundError: () => (/* binding */ WagmiProviderNotFoundError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/../node_modules/wagmi/dist/esm/errors/base.js\");\n\nclass WagmiProviderNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('`useConfig` must be used within `WagmiProvider`.', {\n            docsPath: '/api/WagmiProvider',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiProviderNotFoundError'\n        });\n    }\n}\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2Vycm9ycy9jb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBQy9CLHlDQUF5QywrQ0FBUztBQUN6RDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vZXJyb3JzL2NvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjbGFzcyBXYWdtaVByb3ZpZGVyTm90Rm91bmRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCdgdXNlQ29uZmlnYCBtdXN0IGJlIHVzZWQgd2l0aGluIGBXYWdtaVByb3ZpZGVyYC4nLCB7XG4gICAgICAgICAgICBkb2NzUGF0aDogJy9hcGkvV2FnbWlQcm92aWRlcicsXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnV2FnbWlQcm92aWRlck5vdEZvdW5kRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/errors/context.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useAccount.js":
/*!**********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useAccount.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAccount: () => (/* binding */ useAccount)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchAccount.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getAccount.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* harmony import */ var _useSyncExternalStoreWithTracked_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useSyncExternalStoreWithTracked.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useSyncExternalStoreWithTracked.js\");\n/* __next_internal_client_entry_do_not_use__ useAccount auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useAccount */ function useAccount(parameters = {}) {\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    return (0,_useSyncExternalStoreWithTracked_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStoreWithTracked)({\n        \"useAccount.useSyncExternalStoreWithTracked\": (onChange)=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_2__.watchAccount)(config, {\n                onChange\n            })\n    }[\"useAccount.useSyncExternalStoreWithTracked\"], {\n        \"useAccount.useSyncExternalStoreWithTracked\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getAccount)(config)\n    }[\"useAccount.useSyncExternalStoreWithTracked\"]);\n} //# sourceMappingURL=useAccount.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUFjY291bnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Z0VBUW9CO0FBR3NCO0FBQzRDO0FBUXRGLGtEQUFrRCxDQUM1QyxTQUFVLFVBQVUsQ0FDeEIsYUFBMkMsRUFBRTtJQUU3QyxNQUFNLE1BQU0sR0FBRyx3REFBUyxDQUFDLFVBQVUsQ0FBQztJQUVwQyxPQUFPLG9HQUErQjtzREFDcEMsQ0FBQyxRQUFRLEVBQUUsQ0FBRyx5REFBWSxDQUFDLE1BQU0sRUFBRTtnQkFBRSxRQUFRO1lBQUEsQ0FBRSxDQUFDOztzREFDaEQsR0FBRyxDQUFHLHVEQUFVLENBQUMsTUFBTSxDQUFDOztBQUU1QixDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvam9lL3NyYy9ob29rcy91c2VBY2NvdW50LnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useAccount.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useAccountEffect.js":
/*!****************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useAccountEffect.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAccountEffect: () => (/* binding */ useAccountEffect)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchAccount.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useAccountEffect auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useAccountEffect */ function useAccountEffect(parameters = {}) {\n    const { onConnect, onDisconnect } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAccountEffect.useEffect\": ()=>{\n            return (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_2__.watchAccount)(config, {\n                onChange (data, prevData) {\n                    if ((prevData.status === 'reconnecting' || prevData.status === 'connecting' && prevData.address === undefined) && data.status === 'connected') {\n                        const { address, addresses, chain, chainId, connector } = data;\n                        const isReconnected = prevData.status === 'reconnecting' || // if `previousAccount.status` is `undefined`, the connector connected immediately.\n                        prevData.status === undefined;\n                        onConnect?.({\n                            address,\n                            addresses,\n                            chain,\n                            chainId,\n                            connector,\n                            isReconnected\n                        });\n                    } else if (prevData.status === 'connected' && data.status === 'disconnected') onDisconnect?.();\n                }\n            });\n        }\n    }[\"useAccountEffect.useEffect\"], [\n        config,\n        onConnect,\n        onDisconnect\n    ]);\n} //# sourceMappingURL=useAccountEffect.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUFjY291bnRFZmZlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztzRUFFcUU7QUFFcEM7QUFHUztBQWtCMUMsd0RBQXdELENBQ2xELFNBQVUsZ0JBQWdCLENBQUMsYUFBeUMsRUFBRTtJQUMxRSxNQUFNLEVBQUUsU0FBUyxFQUFFLFlBQVksRUFBRSxHQUFHLFVBQVU7SUFFOUMsTUFBTSxNQUFNLEdBQUcsd0RBQVMsQ0FBQyxVQUFVLENBQUM7SUFFcEMsZ0RBQVM7c0NBQUMsR0FBRyxFQUFFO1lBQ2IsT0FBTyx5REFBWSxDQUFDLE1BQU0sRUFBRTtnQkFDMUIsUUFBUSxFQUFDLElBQUksRUFBRSxRQUFRO29CQUNyQixJQUNFLENBQUMsUUFBUSxDQUFDLE1BQU0sS0FBSyxjQUFjLElBQ2hDLFFBQVEsQ0FBQyxNQUFNLEtBQUssWUFBWSxJQUMvQixRQUFRLENBQUMsT0FBTyxLQUFLLFNBQVMsQ0FBQyxDQUFDLEdBQ3BDLElBQUksQ0FBQyxNQUFNLEtBQUssV0FBVyxFQUMzQixDQUFDO3dCQUNELE1BQU0sRUFBRSxPQUFPLEVBQUUsU0FBUyxFQUFFLEtBQUssRUFBRSxPQUFPLEVBQUUsU0FBUyxFQUFFLEdBQUcsSUFBSTt3QkFDOUQsTUFBTSxhQUFhLEdBQ2pCLFFBQVEsQ0FBQyxNQUFNLEtBQUssY0FBYyxJQUNsQyxtRkFBbUY7d0JBQ25GLFFBQVEsQ0FBQyxNQUFNLEtBQUssU0FBUzt3QkFDL0IsU0FBUyxFQUFFLENBQUM7NEJBQ1YsT0FBTzs0QkFDUCxTQUFTOzRCQUNULEtBQUs7NEJBQ0wsT0FBTzs0QkFDUCxTQUFTOzRCQUNULGFBQWE7eUJBQ2QsQ0FBQztvQkFDSixDQUFDLE1BQU0sSUFDTCxRQUFRLENBQUMsTUFBTSxLQUFLLFdBQVcsSUFDL0IsSUFBSSxDQUFDLE1BQU0sS0FBSyxjQUFjLEVBRTlCLFlBQVksRUFBRSxFQUFFO2dCQUNwQixDQUFDO2FBQ0YsQ0FBQztRQUNKLENBQUM7cUNBQUU7UUFBQyxNQUFNO1FBQUUsU0FBUztRQUFFLFlBQVk7S0FBQyxDQUFDO0FBQ3ZDLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZUFjY291bnRFZmZlY3QudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useAccountEffect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useBalance.js":
/*!**********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useBalance.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBalance: () => (/* binding */ useBalance)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/getBalance.js\");\n/* harmony import */ var _utils_query_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/query.js */ \"(ssr)/../node_modules/wagmi/dist/esm/utils/query.js\");\n/* harmony import */ var _useChainId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useChainId.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useChainId.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useBalance auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useBalance */ function useBalance(parameters = {}) {\n    const { address, query = {} } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const chainId = (0,_useChainId_js__WEBPACK_IMPORTED_MODULE_1__.useChainId)({\n        config\n    });\n    const options = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.getBalanceQueryOptions)(config, {\n        ...parameters,\n        chainId: parameters.chainId ?? chainId\n    });\n    const enabled = Boolean(address && (query.enabled ?? true));\n    return (0,_utils_query_js__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        ...query,\n        ...options,\n        enabled\n    });\n} //# sourceMappingURL=useBalance.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUJhbGFuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Z0VBUzBCO0FBSTJDO0FBQ3pCO0FBQ0Y7QUFtQjFDLGtEQUFrRCxDQUM1QyxTQUFVLFVBQVUsQ0FJeEIsYUFBdUQsRUFBRTtJQUV6RCxNQUFNLEVBQUUsT0FBTyxFQUFFLEtBQUssR0FBRyxFQUFFLEVBQUUsR0FBRyxVQUFVO0lBRTFDLE1BQU0sTUFBTSxHQUFHLHdEQUFTLENBQUMsVUFBVSxDQUFDO0lBQ3BDLE1BQU0sT0FBTyxHQUFHLDBEQUFVLENBQUM7UUFBRSxNQUFNO0lBQUEsQ0FBRSxDQUFDO0lBRXRDLE1BQU0sT0FBTyxHQUFHLHlFQUFzQixDQUFDLE1BQU0sRUFBRTtRQUM3QyxHQUFHLFVBQVU7UUFDYixPQUFPLEVBQUUsVUFBVSxDQUFDLE9BQU8sSUFBSSxPQUFPO0tBQ3ZDLENBQUM7SUFDRixNQUFNLE9BQU8sR0FBRyxPQUFPLENBQUMsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sSUFBSSxLQUFJLENBQUMsQ0FBQztJQUUzRCxPQUFPLHlEQUFRLENBQUM7UUFBRSxHQUFHLEtBQUs7UUFBRSxHQUFHLE9BQU87UUFBRSxPQUFPO0lBQUEsQ0FBRSxDQUFDO0FBQ3BELENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZUJhbGFuY2UudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useBalance.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useChainId.js":
/*!**********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useChainId.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChainId: () => (/* binding */ useChainId)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchChainId.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getChainId.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useChainId auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useChainId */ function useChainId(parameters = {}) {\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)({\n        \"useChainId.useSyncExternalStore\": (onChange)=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_2__.watchChainId)(config, {\n                onChange\n            })\n    }[\"useChainId.useSyncExternalStore\"], {\n        \"useChainId.useSyncExternalStore\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getChainId)(config)\n    }[\"useChainId.useSyncExternalStore\"], {\n        \"useChainId.useSyncExternalStore\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getChainId)(config)\n    }[\"useChainId.useSyncExternalStore\"]);\n} //# sourceMappingURL=useChainId.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUNoYWluSWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Z0VBUW9CO0FBQ3dCO0FBR0Y7QUFRMUMsa0RBQWtELENBQzVDLFNBQVUsVUFBVSxDQUN4QixhQUEyQyxFQUFFO0lBRTdDLE1BQU0sTUFBTSxHQUFHLHdEQUFTLENBQUMsVUFBVSxDQUFDO0lBRXBDLE9BQU8sMkRBQW9COzJDQUN6QixDQUFDLFFBQVEsRUFBRSxDQUFHLHlEQUFZLENBQUMsTUFBTSxFQUFFO2dCQUFFLFFBQVE7WUFBQSxDQUFFLENBQUM7OzJDQUNoRCxHQUFHLENBQUcsdURBQVUsQ0FBQyxNQUFNLENBQUM7OzJDQUN4QixHQUFHLENBQUcsdURBQVUsQ0FBQyxNQUFNLENBQUM7O0FBRTVCLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZUNoYWluSWQudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useChainId.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useChains.js":
/*!*********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useChains.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChains: () => (/* binding */ useChains)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getChains.js\");\n/* harmony import */ var _wagmi_core_internal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/internal */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchChains.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useChains auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useChains */ function useChains(parameters = {}) {\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)({\n        \"useChains.useSyncExternalStore\": (onChange)=>(0,_wagmi_core_internal__WEBPACK_IMPORTED_MODULE_2__.watchChains)(config, {\n                onChange\n            })\n    }[\"useChains.useSyncExternalStore\"], {\n        \"useChains.useSyncExternalStore\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getChains)(config)\n    }[\"useChains.useSyncExternalStore\"], {\n        \"useChains.useSyncExternalStore\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getChains)(config)\n    }[\"useChains.useSyncExternalStore\"]);\n} //# sourceMappingURL=useChains.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUNoYWlucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OzsrREFPb0I7QUFDOEI7QUFDTjtBQUdGO0FBUTFDLGlEQUFpRCxDQUMzQyxTQUFVLFNBQVMsQ0FDdkIsYUFBMEMsRUFBRTtJQUU1QyxNQUFNLE1BQU0sR0FBRyx3REFBUyxDQUFDLFVBQVUsQ0FBQztJQUVwQyxPQUFPLDJEQUFvQjswQ0FDekIsQ0FBQyxRQUFRLEVBQUUsQ0FBRyxpRUFBVyxDQUFDLE1BQU0sRUFBRTtnQkFBRSxRQUFRO1lBQUEsQ0FBRSxDQUFDOzswQ0FDL0MsR0FBRyxDQUFHLHNEQUFTLENBQUMsTUFBTSxDQUFDOzswQ0FDdkIsR0FBRyxDQUFHLHNEQUFTLENBQUMsTUFBTSxDQUFDOztBQUUzQixDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvam9lL3NyYy9ob29rcy91c2VDaGFpbnMudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useChains.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js":
/*!*********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useConfig.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConfig: () => (/* binding */ useConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context.js */ \"(ssr)/../node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _errors_context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../errors/context.js */ \"(ssr)/../node_modules/wagmi/dist/esm/errors/context.js\");\n/* __next_internal_client_entry_do_not_use__ useConfig auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useConfig */ function useConfig(parameters = {}) {\n    const config = parameters.config ?? (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.WagmiContext);\n    if (!config) throw new _errors_context_js__WEBPACK_IMPORTED_MODULE_2__.WagmiProviderNotFoundError();\n    return config;\n} //# sourceMappingURL=useConfig.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUNvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OytEQUdrQztBQUVVO0FBQ3FCO0FBUWpFLGlEQUFpRCxDQUMzQyxTQUFVLFNBQVMsQ0FDdkIsYUFBMEMsRUFBRTtJQUU1QyxNQUFNLE1BQU0sR0FBRyxVQUFVLENBQUMsTUFBTSxJQUFJLGlEQUFVLENBQUMscURBQVksQ0FBQztJQUM1RCxJQUFJLENBQUMsTUFBTSxFQUFFLE1BQU0sSUFBSSwwRUFBMEIsRUFBRTtJQUNuRCxPQUFPLE1BQXFDO0FBQzlDLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZUNvbmZpZy50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useConnect.js":
/*!**********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useConnect.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnect: () => (/* binding */ useConnect)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/connect.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* harmony import */ var _useConnectors_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useConnectors.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConnectors.js\");\n/* __next_internal_client_entry_do_not_use__ useConnect auto */ \n\n\n\n\n/** https://wagmi.sh/react/api/hooks/useConnect */ function useConnect(parameters = {}) {\n    const { mutation } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    const mutationOptions = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.connectMutationOptions)(config);\n    const { mutate, mutateAsync, ...result } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        ...mutation,\n        ...mutationOptions\n    });\n    // Reset mutation back to an idle state when the connector disconnects.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useConnect.useEffect\": ()=>{\n            return config.subscribe({\n                \"useConnect.useEffect\": ({ status })=>status\n            }[\"useConnect.useEffect\"], {\n                \"useConnect.useEffect\": (status, previousStatus)=>{\n                    if (previousStatus === 'connected' && status === 'disconnected') result.reset();\n                }\n            }[\"useConnect.useEffect\"]);\n        }\n    }[\"useConnect.useEffect\"], [\n        config,\n        result.reset\n    ]);\n    return {\n        ...result,\n        connect: mutate,\n        connectAsync: mutateAsync,\n        connectors: (0,_useConnectors_js__WEBPACK_IMPORTED_MODULE_4__.useConnectors)({\n            config\n        })\n    };\n} //# sourceMappingURL=useConnect.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUNvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O2dFQUVtRDtBQVN6QjtBQUNPO0FBT1M7QUFDc0M7QUFrQ2hGLGtEQUFrRCxDQUM1QyxTQUFVLFVBQVUsQ0FJeEIsYUFBb0QsRUFBRTtJQUV0RCxNQUFNLEVBQUUsUUFBUSxFQUFFLEdBQUcsVUFBVTtJQUUvQixNQUFNLE1BQU0sR0FBRyx3REFBUyxDQUFDLFVBQVUsQ0FBQztJQUVwQyxNQUFNLGVBQWUsR0FBRyx5RUFBc0IsQ0FBQyxNQUFNLENBQUM7SUFDdEQsTUFBTSxFQUFFLE1BQU0sRUFBRSxXQUFXLEVBQUUsR0FBRyxNQUFNLEVBQUUsR0FBRyxrRUFBVyxDQUFDO1FBQ3JELEdBQUcsUUFBUTtRQUNYLEdBQUcsZUFBZTtLQUNuQixDQUFDO0lBRUYsdUVBQXVFO0lBQ3ZFLGdEQUFTO2dDQUFDLEdBQUcsRUFBRTtZQUNiLE9BQU8sTUFBTSxDQUFDLFNBQVM7d0NBQ3JCLENBQUMsRUFBRSxNQUFNLEVBQUUsRUFBRSxDQUFHLENBQUQsS0FBTzs7d0NBQ3RCLENBQUMsTUFBTSxFQUFFLGNBQWMsRUFBRSxFQUFFO29CQUN6QixJQUFJLGNBQWMsS0FBSyxXQUFXLElBQUksTUFBTSxLQUFLLGNBQWMsRUFDN0QsTUFBTSxDQUFDLEtBQUssRUFBRTtnQkFDbEIsQ0FBQzs7UUFFTCxDQUFDOytCQUFFO1FBQUMsTUFBTTtRQUFFLE1BQU0sQ0FBQyxLQUFLO0tBQUMsQ0FBQztJQUcxQixPQUFPO1FBQ0wsR0FBSSxNQUFpQjtRQUNyQixPQUFPLEVBQUUsTUFBMkI7UUFDcEMsWUFBWSxFQUFFLFdBQXFDO1FBQ25ELFVBQVUsRUFBRSxnRUFBYSxDQUFDO1lBQUUsTUFBTTtRQUFBLENBQUUsQ0FBQztLQUN0QztBQUNILENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZUNvbm5lY3QudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useConnect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useConnections.js":
/*!**************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useConnections.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnections: () => (/* binding */ useConnections)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchConnections.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnections.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useConnections auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useConnections */ function useConnections(parameters = {}) {\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)({\n        \"useConnections.useSyncExternalStore\": (onChange)=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_2__.watchConnections)(config, {\n                onChange\n            })\n    }[\"useConnections.useSyncExternalStore\"], {\n        \"useConnections.useSyncExternalStore\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getConnections)(config)\n    }[\"useConnections.useSyncExternalStore\"], {\n        \"useConnections.useSyncExternalStore\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getConnections)(config)\n    }[\"useConnections.useSyncExternalStore\"]);\n} //# sourceMappingURL=useConnections.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUNvbm5lY3Rpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O29FQU1vQjtBQUN3QjtBQUdGO0FBTTFDLHNEQUFzRCxDQUNoRCxTQUFVLGNBQWMsQ0FDNUIsYUFBdUMsRUFBRTtJQUV6QyxNQUFNLE1BQU0sR0FBRyx3REFBUyxDQUFDLFVBQVUsQ0FBQztJQUVwQyxPQUFPLDJEQUFvQjsrQ0FDekIsQ0FBQyxRQUFRLEVBQUUsQ0FBRyw2REFBZ0IsQ0FBQyxNQUFNLEVBQUU7Z0JBQUUsUUFBUTtZQUFBLENBQUUsQ0FBQzs7K0NBQ3BELEdBQUcsQ0FBRywyREFBYyxDQUFDLE1BQU0sQ0FBQzs7K0NBQzVCLEdBQUcsQ0FBRywyREFBYyxDQUFDLE1BQU0sQ0FBQzs7QUFFaEMsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9zcmMvaG9va3MvdXNlQ29ubmVjdGlvbnMudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useConnections.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useConnectors.js":
/*!*************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useConnectors.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnectors: () => (/* binding */ useConnectors)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchConnectors.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getConnectors.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useConnectors auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useConnectors */ function useConnectors(parameters = {}) {\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)({\n        \"useConnectors.useSyncExternalStore\": (onChange)=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_2__.watchConnectors)(config, {\n                onChange\n            })\n    }[\"useConnectors.useSyncExternalStore\"], {\n        \"useConnectors.useSyncExternalStore\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getConnectors)(config)\n    }[\"useConnectors.useSyncExternalStore\"], {\n        \"useConnectors.useSyncExternalStore\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getConnectors)(config)\n    }[\"useConnectors.useSyncExternalStore\"]);\n} //# sourceMappingURL=useConnectors.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUNvbm5lY3RvcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7bUVBUW9CO0FBQ3dCO0FBR0Y7QUFRMUMscURBQXFELENBQy9DLFNBQVUsYUFBYSxDQUczQixhQUE4QyxFQUFFO0lBRWhELE1BQU0sTUFBTSxHQUFHLHdEQUFTLENBQUMsVUFBVSxDQUFDO0lBRXBDLE9BQU8sMkRBQW9COzhDQUN6QixDQUFDLFFBQVEsRUFBRSxDQUFHLDREQUFlLENBQUMsTUFBTSxFQUFFO2dCQUFFLFFBQVE7WUFBQSxDQUFFLENBQUM7OzhDQUNuRCxHQUFHLENBQUcsMERBQWEsQ0FBQyxNQUFNLENBQUM7OzhDQUMzQixHQUFHLENBQUcsMERBQWEsQ0FBQyxNQUFNLENBQUM7O0FBRS9CLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZUNvbm5lY3RvcnMudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useConnectors.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useDisconnect.js":
/*!*************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useDisconnect.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDisconnect: () => (/* binding */ useDisconnect)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/disconnect.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* harmony import */ var _useConnections_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useConnections.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConnections.js\");\n/* __next_internal_client_entry_do_not_use__ useDisconnect auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useDisconnect */ function useDisconnect(parameters = {}) {\n    const { mutation } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const mutationOptions = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__.disconnectMutationOptions)(config);\n    const { mutate, mutateAsync, ...result } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)({\n        ...mutation,\n        ...mutationOptions\n    });\n    return {\n        ...result,\n        connectors: (0,_useConnections_js__WEBPACK_IMPORTED_MODULE_3__.useConnections)({\n            config\n        }).map((connection)=>connection.connector),\n        disconnect: mutate,\n        disconnectAsync: mutateAsync\n    };\n} //# sourceMappingURL=useDisconnect.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZURpc2Nvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7bUVBRW1EO0FBU3pCO0FBT2dCO0FBQ1U7QUE0QnBELHFEQUFxRCxDQUMvQyxTQUFVLGFBQWEsQ0FDM0IsYUFBK0MsRUFBRTtJQUVqRCxNQUFNLEVBQUUsUUFBUSxFQUFFLEdBQUcsVUFBVTtJQUUvQixNQUFNLE1BQU0sR0FBRyx3REFBUyxDQUFDLFVBQVUsQ0FBQztJQUVwQyxNQUFNLGVBQWUsR0FBRyw0RUFBeUIsQ0FBQyxNQUFNLENBQUM7SUFDekQsTUFBTSxFQUFFLE1BQU0sRUFBRSxXQUFXLEVBQUUsR0FBRyxNQUFNLEVBQUUsR0FBRyxrRUFBVyxDQUFDO1FBQ3JELEdBQUcsUUFBUTtRQUNYLEdBQUcsZUFBZTtLQUNuQixDQUFDO0lBRUYsT0FBTztRQUNMLEdBQUcsTUFBTTtRQUNULFVBQVUsRUFBRSxrRUFBYyxDQUFDO1lBQUUsTUFBTTtRQUFBLENBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FDeEMsQ0FBQyxVQUFVLEVBQUUsQ0FBRyxDQUFELFNBQVcsQ0FBQyxTQUFTLENBQ3JDO1FBQ0QsVUFBVSxFQUFFLE1BQU07UUFDbEIsZUFBZSxFQUFFLFdBQVc7S0FDN0I7QUFDSCxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvam9lL3NyYy9ob29rcy91c2VEaXNjb25uZWN0LnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useDisconnect.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useEnsAvatar.js":
/*!************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useEnsAvatar.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnsAvatar: () => (/* binding */ useEnsAvatar)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/getEnsAvatar.js\");\n/* harmony import */ var _utils_query_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/query.js */ \"(ssr)/../node_modules/wagmi/dist/esm/utils/query.js\");\n/* harmony import */ var _useChainId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useChainId.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useChainId.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useEnsAvatar auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useEnsAvatar */ function useEnsAvatar(parameters = {}) {\n    const { name, query = {} } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const chainId = (0,_useChainId_js__WEBPACK_IMPORTED_MODULE_1__.useChainId)({\n        config\n    });\n    const options = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.getEnsAvatarQueryOptions)(config, {\n        ...parameters,\n        chainId: parameters.chainId ?? chainId\n    });\n    const enabled = Boolean(name && (query.enabled ?? true));\n    return (0,_utils_query_js__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        ...query,\n        ...options,\n        enabled\n    });\n} //# sourceMappingURL=useEnsAvatar.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUVuc0F2YXRhci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztrRUFjMEI7QUFHMkM7QUFDekI7QUFDRjtBQW1CMUMsb0RBQW9ELENBQzlDLFNBQVUsWUFBWSxDQUkxQixhQUF5RCxFQUFFO0lBRTNELE1BQU0sRUFBRSxJQUFJLEVBQUUsS0FBSyxHQUFHLEVBQUUsRUFBRSxHQUFHLFVBQVU7SUFFdkMsTUFBTSxNQUFNLEdBQUcsd0RBQVMsQ0FBQyxVQUFVLENBQUM7SUFDcEMsTUFBTSxPQUFPLEdBQUcsMERBQVUsQ0FBQztRQUFFLE1BQU07SUFBQSxDQUFFLENBQUM7SUFFdEMsTUFBTSxPQUFPLEdBQUcsMkVBQXdCLENBQUMsTUFBTSxFQUFFO1FBQy9DLEdBQUcsVUFBVTtRQUNiLE9BQU8sRUFBRSxVQUFVLENBQUMsT0FBTyxJQUFJLE9BQU87S0FDdkMsQ0FBQztJQUNGLE1BQU0sT0FBTyxHQUFHLE9BQU8sQ0FBQyxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxJQUFJLEtBQUksQ0FBQyxDQUFDO0lBRXhELE9BQU8seURBQVEsQ0FBQztRQUFFLEdBQUcsS0FBSztRQUFFLEdBQUcsT0FBTztRQUFFLE9BQU87SUFBQSxDQUFFLENBQUM7QUFDcEQsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9zcmMvaG9va3MvdXNlRW5zQXZhdGFyLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useEnsAvatar.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useEnsName.js":
/*!**********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useEnsName.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnsName: () => (/* binding */ useEnsName)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/getEnsName.js\");\n/* harmony import */ var _utils_query_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/query.js */ \"(ssr)/../node_modules/wagmi/dist/esm/utils/query.js\");\n/* harmony import */ var _useChainId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useChainId.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useChainId.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useEnsName auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useEnsName */ function useEnsName(parameters = {}) {\n    const { address, query = {} } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const chainId = (0,_useChainId_js__WEBPACK_IMPORTED_MODULE_1__.useChainId)({\n        config\n    });\n    const options = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.getEnsNameQueryOptions)(config, {\n        ...parameters,\n        chainId: parameters.chainId ?? chainId\n    });\n    const enabled = Boolean(address && (query.enabled ?? true));\n    return (0,_utils_query_js__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        ...query,\n        ...options,\n        enabled\n    });\n} //# sourceMappingURL=useEnsName.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZUVuc05hbWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Z0VBVTBCO0FBRzJDO0FBQ3pCO0FBQ0Y7QUFtQjFDLGtEQUFrRCxDQUM1QyxTQUFVLFVBQVUsQ0FJeEIsYUFBdUQsRUFBRTtJQUV6RCxNQUFNLEVBQUUsT0FBTyxFQUFFLEtBQUssR0FBRyxFQUFFLEVBQUUsR0FBRyxVQUFVO0lBRTFDLE1BQU0sTUFBTSxHQUFHLHdEQUFTLENBQUMsVUFBVSxDQUFDO0lBQ3BDLE1BQU0sT0FBTyxHQUFHLDBEQUFVLENBQUM7UUFBRSxNQUFNO0lBQUEsQ0FBRSxDQUFDO0lBRXRDLE1BQU0sT0FBTyxHQUFHLHlFQUFzQixDQUFDLE1BQU0sRUFBRTtRQUM3QyxHQUFHLFVBQVU7UUFDYixPQUFPLEVBQUUsVUFBVSxDQUFDLE9BQU8sSUFBSSxPQUFPO0tBQ3ZDLENBQUM7SUFDRixNQUFNLE9BQU8sR0FBRyxPQUFPLENBQUMsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sSUFBSSxLQUFJLENBQUMsQ0FBQztJQUUzRCxPQUFPLHlEQUFRLENBQUM7UUFBRSxHQUFHLEtBQUs7UUFBRSxHQUFHLE9BQU87UUFBRSxPQUFPO0lBQUEsQ0FBRSxDQUFDO0FBQ3BELENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZUVuc05hbWUudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useEnsName.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/usePublicClient.js":
/*!***************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/usePublicClient.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePublicClient: () => (/* binding */ usePublicClient)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/watchPublicClient.js\");\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/actions/getPublicClient.js\");\n/* harmony import */ var use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector.js */ \"(ssr)/../node_modules/use-sync-external-store/shim/with-selector.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ usePublicClient auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/usePublicClient */ function usePublicClient(parameters = {}) {\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_1__.useConfig)(parameters);\n    return (0,use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStoreWithSelector)({\n        \"usePublicClient.useSyncExternalStoreWithSelector\": (onChange)=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_2__.watchPublicClient)(config, {\n                onChange\n            })\n    }[\"usePublicClient.useSyncExternalStoreWithSelector\"], {\n        \"usePublicClient.useSyncExternalStoreWithSelector\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getPublicClient)(config, parameters)\n    }[\"usePublicClient.useSyncExternalStoreWithSelector\"], {\n        \"usePublicClient.useSyncExternalStoreWithSelector\": ()=>(0,_wagmi_core__WEBPACK_IMPORTED_MODULE_3__.getPublicClient)(config, parameters)\n    }[\"usePublicClient.useSyncExternalStoreWithSelector\"], {\n        \"usePublicClient.useSyncExternalStoreWithSelector\": (x)=>x\n    }[\"usePublicClient.useSyncExternalStoreWithSelector\"], {\n        \"usePublicClient.useSyncExternalStoreWithSelector\": (a, b)=>a?.uid === b?.uid\n    }[\"usePublicClient.useSyncExternalStoreWithSelector\"]);\n} //# sourceMappingURL=usePublicClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZVB1YmxpY0NsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztxRUFTb0I7QUFFNEU7QUFHdEQ7QUFrQjFDLHVEQUF1RCxDQUNqRCxTQUFVLGVBQWUsQ0FNN0IsYUFBeUQsRUFBRTtJQUUzRCxNQUFNLE1BQU0sR0FBRyx3REFBUyxDQUFDLFVBQVUsQ0FBQztJQUVwQyxPQUFPLCtHQUFnQzs0REFDckMsQ0FBQyxRQUFRLEVBQUUsQ0FBRyw4REFBaUIsQ0FBQyxNQUFNLEVBQUU7Z0JBQUUsUUFBUTtZQUFBLENBQUUsQ0FBQzs7NERBQ3JELEdBQUcsQ0FBRyw0REFBZSxDQUFDLE1BQU0sRUFBRSxVQUFVLENBQUM7OzREQUN6QyxHQUFHLENBQUcsNERBQWUsQ0FBQyxNQUFNLEVBQUUsVUFBVSxDQUFDOzs0REFDekMsQ0FBQyxDQUFDLEVBQUUsQ0FBRyxDQUFEOzs0REFDTixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBRyxDQUFELEVBQUksR0FBRyxLQUFLLENBQUMsRUFBRSxHQUFHOztBQUUvQixDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvam9lL3NyYy9ob29rcy91c2VQdWJsaWNDbGllbnQudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/usePublicClient.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useReadContract.js":
/*!***************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useReadContract.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useReadContract: () => (/* binding */ useReadContract)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/readContract.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js\");\n/* harmony import */ var _utils_query_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/query.js */ \"(ssr)/../node_modules/wagmi/dist/esm/utils/query.js\");\n/* harmony import */ var _useChainId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useChainId.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useChainId.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useReadContract auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useReadContract */ function useReadContract(parameters = {}) {\n    const { abi, address, functionName, query = {} } = parameters;\n    // @ts-ignore\n    const code = parameters.code;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const chainId = (0,_useChainId_js__WEBPACK_IMPORTED_MODULE_1__.useChainId)({\n        config\n    });\n    const options = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.readContractQueryOptions)(config, {\n        ...parameters,\n        chainId: parameters.chainId ?? chainId\n    });\n    const enabled = Boolean((address || code) && abi && functionName && (query.enabled ?? true));\n    return (0,_utils_query_js__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        ...query,\n        ...options,\n        enabled,\n        structuralSharing: query.structuralSharing ?? _wagmi_core_query__WEBPACK_IMPORTED_MODULE_4__.structuralSharing\n    });\n} //# sourceMappingURL=useReadContract.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZVJlYWRDb250cmFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7cUVBZTBCO0FBSTJDO0FBQ3pCO0FBQ0Y7QUF3QzFDLHVEQUF1RCxDQUNqRCxTQUFVLGVBQWUsQ0FPN0IsYUFNSSxFQUFTO0lBRWIsTUFBTSxFQUFFLEdBQUcsRUFBRSxPQUFPLEVBQUUsWUFBWSxFQUFFLEtBQUssR0FBRyxFQUFFLEVBQUUsR0FBRyxVQUFVO0lBQzdELGFBQWE7SUFDYixNQUFNLElBQUksR0FBRyxVQUFVLENBQUMsSUFBdUI7SUFFL0MsTUFBTSxNQUFNLEdBQUcsd0RBQVMsQ0FBQyxVQUFVLENBQUM7SUFDcEMsTUFBTSxPQUFPLEdBQUcsMERBQVUsQ0FBQztRQUFFLE1BQU07SUFBQSxDQUFFLENBQUM7SUFFdEMsTUFBTSxPQUFPLEdBQUcsMkVBQXdCLENBQ3RDLE1BQU0sRUFDTjtRQUFFLEdBQUksVUFBa0I7UUFBRSxPQUFPLEVBQUUsVUFBVSxDQUFDLE9BQU8sSUFBSSxPQUFPO0lBQUEsQ0FBRSxDQUNuRTtJQUNELE1BQU0sT0FBTyxHQUFHLE9BQU8sQ0FDckIsQ0FBQyxPQUFPLElBQUksS0FBSSxDQUFDLEdBQUksR0FBRyxJQUFJLFlBQVksSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLElBQUksS0FBSSxDQUFDLENBQ3BFO0lBRUQsT0FBTyx5REFBUSxDQUFDO1FBQ2QsR0FBRyxLQUFLO1FBQ1IsR0FBRyxPQUFPO1FBQ1YsT0FBTztRQUNQLGlCQUFpQixFQUFFLEtBQUssQ0FBQyxpQkFBaUIsSUFBSSxnRUFBaUI7S0FDaEUsQ0FBQztBQUNKLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZVJlYWRDb250cmFjdC50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useReadContract.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useSignMessage.js":
/*!**************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useSignMessage.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSignMessage: () => (/* binding */ useSignMessage)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/signMessage.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useSignMessage auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useSignMessage */ function useSignMessage(parameters = {}) {\n    const { mutation } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const mutationOptions = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__.signMessageMutationOptions)(config);\n    const { mutate, mutateAsync, ...result } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)({\n        ...mutation,\n        ...mutationOptions\n    });\n    return {\n        ...result,\n        signMessage: mutate,\n        signMessageAsync: mutateAsync\n    };\n} //# sourceMappingURL=useSignMessage.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZVNpZ25NZXNzYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7b0VBRW1EO0FBU3pCO0FBT2dCO0FBMkIxQyxzREFBc0QsQ0FDaEQsU0FBVSxjQUFjLENBQzVCLGFBQWdELEVBQUU7SUFFbEQsTUFBTSxFQUFFLFFBQVEsRUFBRSxHQUFHLFVBQVU7SUFFL0IsTUFBTSxNQUFNLEdBQUcsd0RBQVMsQ0FBQyxVQUFVLENBQUM7SUFFcEMsTUFBTSxlQUFlLEdBQUcsNkVBQTBCLENBQUMsTUFBTSxDQUFDO0lBQzFELE1BQU0sRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLEdBQUcsTUFBTSxFQUFFLEdBQUcsa0VBQVcsQ0FBQztRQUNyRCxHQUFHLFFBQVE7UUFDWCxHQUFHLGVBQWU7S0FDbkIsQ0FBQztJQUVGLE9BQU87UUFDTCxHQUFHLE1BQU07UUFDVCxXQUFXLEVBQUUsTUFBTTtRQUNuQixnQkFBZ0IsRUFBRSxXQUFXO0tBQzlCO0FBQ0gsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9zcmMvaG9va3MvdXNlU2lnbk1lc3NhZ2UudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useSignMessage.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useSwitchChain.js":
/*!**************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useSwitchChain.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSwitchChain: () => (/* binding */ useSwitchChain)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/switchChain.js\");\n/* harmony import */ var _useChains_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useChains.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useChains.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useSwitchChain auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useSwitchChain */ function useSwitchChain(parameters = {}) {\n    const { mutation } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const mutationOptions = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__.switchChainMutationOptions)(config);\n    const { mutate, mutateAsync, ...result } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)({\n        ...mutation,\n        ...mutationOptions\n    });\n    return {\n        ...result,\n        chains: (0,_useChains_js__WEBPACK_IMPORTED_MODULE_3__.useChains)({\n            config\n        }),\n        switchChain: mutate,\n        switchChainAsync: mutateAsync\n    };\n} //# sourceMappingURL=useSwitchChain.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZVN3aXRjaENoYWluLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O29FQUVtRDtBQWF6QjtBQU9nQjtBQUNBO0FBa0MxQyxzREFBc0QsQ0FDaEQsU0FBVSxjQUFjLENBSTVCLGFBQXdELEVBQUU7SUFFMUQsTUFBTSxFQUFFLFFBQVEsRUFBRSxHQUFHLFVBQVU7SUFFL0IsTUFBTSxNQUFNLEdBQUcsd0RBQVMsQ0FBQyxVQUFVLENBQUM7SUFFcEMsTUFBTSxlQUFlLEdBQUcsNkVBQTBCLENBQUMsTUFBTSxDQUFDO0lBQzFELE1BQU0sRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLEdBQUcsTUFBTSxFQUFFLEdBQUcsa0VBQVcsQ0FBQztRQUNyRCxHQUFHLFFBQVE7UUFDWCxHQUFHLGVBQWU7S0FDbkIsQ0FBQztJQUdGLE9BQU87UUFDTCxHQUFHLE1BQU07UUFDVCxNQUFNLEVBQUUsd0RBQVMsQ0FBQztZQUFFLE1BQU07UUFBQSxDQUFFLENBQWdDO1FBQzVELFdBQVcsRUFBRSxNQUErQjtRQUM1QyxnQkFBZ0IsRUFBRSxXQUF5QztLQUM1RDtBQUNILENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZVN3aXRjaENoYWluLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useSwitchChain.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useSyncExternalStoreWithTracked.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useSyncExternalStoreWithTracked.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSyncExternalStoreWithTracked: () => (/* binding */ useSyncExternalStoreWithTracked)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core_internal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/internal */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector.js */ \"(ssr)/../node_modules/use-sync-external-store/shim/with-selector.js\");\n/* __next_internal_client_entry_do_not_use__ useSyncExternalStoreWithTracked auto */ \n\n\nconst isPlainObject = (obj)=>typeof obj === 'object' && !Array.isArray(obj);\nfunction useSyncExternalStoreWithTracked(subscribe, getSnapshot, getServerSnapshot = getSnapshot, isEqual = _wagmi_core_internal__WEBPACK_IMPORTED_MODULE_2__.deepEqual) {\n    const trackedKeys = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const result = (0,use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStoreWithSelector)(subscribe, getSnapshot, getServerSnapshot, {\n        \"useSyncExternalStoreWithTracked.useSyncExternalStoreWithSelector[result]\": (x)=>x\n    }[\"useSyncExternalStoreWithTracked.useSyncExternalStoreWithSelector[result]\"], {\n        \"useSyncExternalStoreWithTracked.useSyncExternalStoreWithSelector[result]\": (a, b)=>{\n            if (isPlainObject(a) && isPlainObject(b) && trackedKeys.current.length) {\n                for (const key of trackedKeys.current){\n                    const equal = isEqual(a[key], b[key]);\n                    if (!equal) return false;\n                }\n                return true;\n            }\n            return isEqual(a, b);\n        }\n    }[\"useSyncExternalStoreWithTracked.useSyncExternalStoreWithSelector[result]\"]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useSyncExternalStoreWithTracked.useMemo\": ()=>{\n            if (isPlainObject(result)) {\n                const trackedResult = {\n                    ...result\n                };\n                let properties = {};\n                for (const [key, value] of Object.entries(trackedResult)){\n                    properties = {\n                        ...properties,\n                        [key]: {\n                            configurable: false,\n                            enumerable: true,\n                            get: ({\n                                \"useSyncExternalStoreWithTracked.useMemo\": ()=>{\n                                    if (!trackedKeys.current.includes(key)) {\n                                        trackedKeys.current.push(key);\n                                    }\n                                    return value;\n                                }\n                            })[\"useSyncExternalStoreWithTracked.useMemo\"]\n                        }\n                    };\n                }\n                Object.defineProperties(trackedResult, properties);\n                return trackedResult;\n            }\n            return result;\n        }\n    }[\"useSyncExternalStoreWithTracked.useMemo\"], [\n        result\n    ]);\n} //# sourceMappingURL=useSyncExternalStoreWithTracked.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useSyncExternalStoreWithTracked.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js":
/*!****************************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWaitForTransactionReceipt: () => (/* binding */ useWaitForTransactionReceipt)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/waitForTransactionReceipt.js\");\n/* harmony import */ var _utils_query_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/query.js */ \"(ssr)/../node_modules/wagmi/dist/esm/utils/query.js\");\n/* harmony import */ var _useChainId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useChainId.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useChainId.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useWaitForTransactionReceipt auto */ \n\n\n\n/** https://wagmi.sh/react/api/hooks/useWaitForTransactionReceipt */ function useWaitForTransactionReceipt(parameters = {}) {\n    const { hash, query = {} } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const chainId = (0,_useChainId_js__WEBPACK_IMPORTED_MODULE_1__.useChainId)({\n        config\n    });\n    const options = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.waitForTransactionReceiptQueryOptions)(config, {\n        ...parameters,\n        chainId: parameters.chainId ?? chainId\n    });\n    const enabled = Boolean(hash && (query.enabled ?? true));\n    return (0,_utils_query_js__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        ...query,\n        ...options,\n        enabled\n    });\n} //# sourceMappingURL=useWaitForTransactionReceipt.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZVdhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7a0ZBYzBCO0FBRzJDO0FBQ3pCO0FBQ0Y7QUF5QjFDLG9FQUFvRSxDQUM5RCxTQUFVLDRCQUE0QixDQU0xQyxhQUlJLEVBQUU7SUFFTixNQUFNLEVBQUUsSUFBSSxFQUFFLEtBQUssR0FBRyxFQUFFLEVBQUUsR0FBRyxVQUFVO0lBRXZDLE1BQU0sTUFBTSxHQUFHLHdEQUFTLENBQUMsVUFBVSxDQUFDO0lBQ3BDLE1BQU0sT0FBTyxHQUFHLDBEQUFVLENBQUM7UUFBRSxNQUFNO0lBQUEsQ0FBRSxDQUFDO0lBRXRDLE1BQU0sT0FBTyxHQUFHLHdGQUFxQyxDQUFDLE1BQU0sRUFBRTtRQUM1RCxHQUFHLFVBQVU7UUFDYixPQUFPLEVBQUUsVUFBVSxDQUFDLE9BQU8sSUFBSSxPQUFPO0tBQ3ZDLENBQUM7SUFDRixNQUFNLE9BQU8sR0FBRyxPQUFPLENBQUMsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sSUFBSSxLQUFJLENBQUMsQ0FBQztJQUV4RCxPQUFPLHlEQUFRLENBQUM7UUFDZCxHQUFJLEtBQWE7UUFDakIsR0FBRyxPQUFPO1FBQ1YsT0FBTztLQUNSLENBQXdFO0FBQzNFLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uvc3JjL2hvb2tzL3VzZVdhaXRGb3JUcmFuc2FjdGlvblJlY2VpcHQudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useWaitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hooks/useWriteContract.js":
/*!****************************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hooks/useWriteContract.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWriteContract: () => (/* binding */ useWriteContract)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/writeContract.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useConfig.js\");\n/* __next_internal_client_entry_do_not_use__ useWriteContract auto */ \n\n\n/** https://wagmi.sh/react/api/hooks/useWriteContract */ function useWriteContract(parameters = {}) {\n    const { mutation } = parameters;\n    const config = (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__.useConfig)(parameters);\n    const mutationOptions = (0,_wagmi_core_query__WEBPACK_IMPORTED_MODULE_1__.writeContractMutationOptions)(config);\n    const { mutate, mutateAsync, ...result } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)({\n        ...mutation,\n        ...mutationOptions\n    });\n    return {\n        ...result,\n        writeContract: mutate,\n        writeContractAsync: mutateAsync\n    };\n} //# sourceMappingURL=useWriteContract.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2hvb2tzL3VzZVdyaXRlQ29udHJhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztzRUFFbUQ7QUFZekI7QUFRZ0I7QUF5QzFDLHdEQUF3RCxDQUNsRCxTQUFVLGdCQUFnQixDQUk5QixhQUEwRCxFQUFFO0lBRTVELE1BQU0sRUFBRSxRQUFRLEVBQUUsR0FBRyxVQUFVO0lBRS9CLE1BQU0sTUFBTSxHQUFHLHdEQUFTLENBQUMsVUFBVSxDQUFDO0lBRXBDLE1BQU0sZUFBZSxHQUFHLCtFQUE0QixDQUFDLE1BQU0sQ0FBQztJQUM1RCxNQUFNLEVBQUUsTUFBTSxFQUFFLFdBQVcsRUFBRSxHQUFHLE1BQU0sRUFBRSxHQUFHLGtFQUFXLENBQUM7UUFDckQsR0FBRyxRQUFRO1FBQ1gsR0FBRyxlQUFlO0tBQ25CLENBQUM7SUFHRixPQUFPO1FBQ0wsR0FBRyxNQUFNO1FBQ1QsYUFBYSxFQUFFLE1BQWlDO1FBQ2hELGtCQUFrQixFQUFFLFdBQTJDO0tBQ2hFO0FBQ0gsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9zcmMvaG9va3MvdXNlV3JpdGVDb250cmFjdC50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hooks/useWriteContract.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/hydrate.js":
/*!*************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/hydrate.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hydrate: () => (/* binding */ Hydrate)\n/* harmony export */ });\n/* harmony import */ var _wagmi_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wagmi/core */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/hydrate.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Hydrate auto */ \n\nfunction Hydrate(parameters) {\n    const { children, config, initialState, reconnectOnMount = true } = parameters;\n    const { onMount } = (0,_wagmi_core__WEBPACK_IMPORTED_MODULE_1__.hydrate)(config, {\n        initialState,\n        reconnectOnMount\n    });\n    // Hydrate for non-SSR\n    if (!config._internal.ssr) onMount();\n    // Hydrate for SSR\n    const active = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: `queryKey` not required\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Hydrate.useEffect\": ()=>{\n            if (!active.current) return;\n            if (!config._internal.ssr) return;\n            onMount();\n            return ({\n                \"Hydrate.useEffect\": ()=>{\n                    active.current = false;\n                }\n            })[\"Hydrate.useEffect\"];\n        }\n    }[\"Hydrate.useEffect\"], []);\n    return children;\n} //# sourceMappingURL=hydrate.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL2h5ZHJhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUV3RTtBQUNaO0FBUXRELFNBQVUsT0FBTyxDQUFDLFVBQWlEO0lBQ3ZFLE1BQU0sRUFBRSxRQUFRLEVBQUUsTUFBTSxFQUFFLFlBQVksRUFBRSxnQkFBZ0IsR0FBRyxJQUFJLEVBQUUsR0FBRyxVQUFVO0lBRTlFLE1BQU0sRUFBRSxPQUFPLEVBQUUsR0FBRyxvREFBTyxDQUFDLE1BQU0sRUFBRTtRQUNsQyxZQUFZO1FBQ1osZ0JBQWdCO0tBQ2pCLENBQUM7SUFFRixzQkFBc0I7SUFDdEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsR0FBRyxFQUFFLE9BQU8sRUFBRTtJQUVwQyxrQkFBa0I7SUFDbEIsTUFBTSxNQUFNLEdBQUcsNkNBQU0sQ0FBQyxJQUFJLENBQUM7SUFDM0IsbUZBQW1GO0lBQ25GLGdEQUFTOzZCQUFDLEdBQUcsRUFBRTtZQUNiLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLE9BQU07WUFDM0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsR0FBRyxFQUFFLE9BQU07WUFDakMsT0FBTyxFQUFFO1lBQ1Q7cUNBQU8sR0FBRyxFQUFFO29CQUNWLE1BQU0sQ0FBQyxPQUFPLEdBQUcsS0FBSztnQkFDeEIsQ0FBQzs7UUFDSCxDQUFDOzRCQUFFLEVBQUUsQ0FBQztJQUVOLE9BQU8sUUFBd0I7QUFDakMsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9zcmMvaHlkcmF0ZS50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/hydrate.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/utils/getVersion.js":
/*!**********************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/utils/getVersion.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/../node_modules/wagmi/dist/esm/version.js\");\n\nconst getVersion = () => `wagmi@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`;\n//# sourceMappingURL=getVersion.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL3V0aWxzL2dldFZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0M7QUFDakMsa0NBQWtDLGdEQUFPLENBQUM7QUFDakQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG5leHBvcnQgY29uc3QgZ2V0VmVyc2lvbiA9ICgpID0+IGB3YWdtaUAke3ZlcnNpb259YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldFZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/utils/getVersion.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/utils/query.js":
/*!*****************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/utils/query.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery),\n/* harmony export */   useMutation: () => (/* reexport safe */ _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation),\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js\");\n/* harmony import */ var _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wagmi/core/query */ \"(ssr)/../node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\n\n// Adding some basic customization.\n// Ideally we don't have this function, but `import('@tanstack/react-query').useQuery` currently has some quirks where it is super hard to\n// pass down the inferred `initialData` type because of it's discriminated overload in the on `useQuery`.\nfunction useQuery(parameters) {\n    const result = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        ...parameters,\n        queryKeyHashFn: _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.hashFn, // for bigint support\n    });\n    result.queryKey = parameters.queryKey;\n    return result;\n}\n// Adding some basic customization.\nfunction useInfiniteQuery(parameters) {\n    const result = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useInfiniteQuery)({\n        ...parameters,\n        queryKeyHashFn: _wagmi_core_query__WEBPACK_IMPORTED_MODULE_2__.hashFn, // for bigint support\n    });\n    result.queryKey = parameters.queryKey;\n    return result;\n}\n//# sourceMappingURL=query.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL3V0aWxzL3F1ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBbUk7QUFDeEY7QUFDcEI7QUFDdkI7QUFDQTtBQUNBO0FBQ087QUFDUCxtQkFBbUIsK0RBQWlCO0FBQ3BDO0FBQ0Esd0JBQXdCLHFEQUFNO0FBQzlCLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsbUJBQW1CLHVFQUF5QjtBQUM1QztBQUNBLHdCQUF3QixxREFBTTtBQUM5QixLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vdXRpbHMvcXVlcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlSW5maW5pdGVRdWVyeSBhcyB0YW5zdGFja191c2VJbmZpbml0ZVF1ZXJ5LCB1c2VRdWVyeSBhcyB0YW5zdGFja191c2VRdWVyeSwgdXNlTXV0YXRpb24sIH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IGhhc2hGbiB9IGZyb20gJ0B3YWdtaS9jb3JlL3F1ZXJ5JztcbmV4cG9ydCB7IHVzZU11dGF0aW9uIH07XG4vLyBBZGRpbmcgc29tZSBiYXNpYyBjdXN0b21pemF0aW9uLlxuLy8gSWRlYWxseSB3ZSBkb24ndCBoYXZlIHRoaXMgZnVuY3Rpb24sIGJ1dCBgaW1wb3J0KCdAdGFuc3RhY2svcmVhY3QtcXVlcnknKS51c2VRdWVyeWAgY3VycmVudGx5IGhhcyBzb21lIHF1aXJrcyB3aGVyZSBpdCBpcyBzdXBlciBoYXJkIHRvXG4vLyBwYXNzIGRvd24gdGhlIGluZmVycmVkIGBpbml0aWFsRGF0YWAgdHlwZSBiZWNhdXNlIG9mIGl0J3MgZGlzY3JpbWluYXRlZCBvdmVybG9hZCBpbiB0aGUgb24gYHVzZVF1ZXJ5YC5cbmV4cG9ydCBmdW5jdGlvbiB1c2VRdWVyeShwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgcmVzdWx0ID0gdGFuc3RhY2tfdXNlUXVlcnkoe1xuICAgICAgICAuLi5wYXJhbWV0ZXJzLFxuICAgICAgICBxdWVyeUtleUhhc2hGbjogaGFzaEZuLCAvLyBmb3IgYmlnaW50IHN1cHBvcnRcbiAgICB9KTtcbiAgICByZXN1bHQucXVlcnlLZXkgPSBwYXJhbWV0ZXJzLnF1ZXJ5S2V5O1xuICAgIHJldHVybiByZXN1bHQ7XG59XG4vLyBBZGRpbmcgc29tZSBiYXNpYyBjdXN0b21pemF0aW9uLlxuZXhwb3J0IGZ1bmN0aW9uIHVzZUluZmluaXRlUXVlcnkocGFyYW1ldGVycykge1xuICAgIGNvbnN0IHJlc3VsdCA9IHRhbnN0YWNrX3VzZUluZmluaXRlUXVlcnkoe1xuICAgICAgICAuLi5wYXJhbWV0ZXJzLFxuICAgICAgICBxdWVyeUtleUhhc2hGbjogaGFzaEZuLCAvLyBmb3IgYmlnaW50IHN1cHBvcnRcbiAgICB9KTtcbiAgICByZXN1bHQucXVlcnlLZXkgPSBwYXJhbWV0ZXJzLnF1ZXJ5S2V5O1xuICAgIHJldHVybiByZXN1bHQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1xdWVyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/utils/query.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/wagmi/dist/esm/version.js":
/*!*************************************************!*\
  !*** ../node_modules/wagmi/dist/esm/version.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.15.6';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3dhZ21pL2Rpc3QvZXNtL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvd2FnbWkvZGlzdC9lc20vdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjE1LjYnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/wagmi/dist/esm/version.js\n");

/***/ })

};
;