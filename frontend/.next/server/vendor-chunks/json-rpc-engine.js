"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json-rpc-engine";
exports.ids = ["vendor-chunks/json-rpc-engine"];
exports.modules = {

/***/ "(ssr)/../node_modules/json-rpc-engine/dist/JsonRpcEngine.js":
/*!*************************************************************!*\
  !*** ../node_modules/json-rpc-engine/dist/JsonRpcEngine.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.JsonRpcEngine = void 0;\nconst safe_event_emitter_1 = __importDefault(__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/../node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js\"));\nconst eth_rpc_errors_1 = __webpack_require__(/*! eth-rpc-errors */ \"(ssr)/../node_modules/eth-rpc-errors/dist/index.js\");\n/**\n * A JSON-RPC request and response processor.\n * Give it a stack of middleware, pass it requests, and get back responses.\n */\nclass JsonRpcEngine extends safe_event_emitter_1.default {\n    constructor() {\n        super();\n        this._middleware = [];\n    }\n    /**\n     * Add a middleware function to the engine's middleware stack.\n     *\n     * @param middleware - The middleware function to add.\n     */\n    push(middleware) {\n        this._middleware.push(middleware);\n    }\n    handle(req, cb) {\n        if (cb && typeof cb !== 'function') {\n            throw new Error('\"callback\" must be a function if provided.');\n        }\n        if (Array.isArray(req)) {\n            if (cb) {\n                return this._handleBatch(req, cb);\n            }\n            return this._handleBatch(req);\n        }\n        if (cb) {\n            return this._handle(req, cb);\n        }\n        return this._promiseHandle(req);\n    }\n    /**\n     * Returns this engine as a middleware function that can be pushed to other\n     * engines.\n     *\n     * @returns This engine as a middleware function.\n     */\n    asMiddleware() {\n        return async (req, res, next, end) => {\n            try {\n                const [middlewareError, isComplete, returnHandlers,] = await JsonRpcEngine._runAllMiddleware(req, res, this._middleware);\n                if (isComplete) {\n                    await JsonRpcEngine._runReturnHandlers(returnHandlers);\n                    return end(middlewareError);\n                }\n                return next(async (handlerCallback) => {\n                    try {\n                        await JsonRpcEngine._runReturnHandlers(returnHandlers);\n                    }\n                    catch (error) {\n                        return handlerCallback(error);\n                    }\n                    return handlerCallback();\n                });\n            }\n            catch (error) {\n                return end(error);\n            }\n        };\n    }\n    async _handleBatch(reqs, cb) {\n        // The order here is important\n        try {\n            // 2. Wait for all requests to finish, or throw on some kind of fatal\n            // error\n            const responses = await Promise.all(\n            // 1. Begin executing each request in the order received\n            reqs.map(this._promiseHandle.bind(this)));\n            // 3. Return batch response\n            if (cb) {\n                return cb(null, responses);\n            }\n            return responses;\n        }\n        catch (error) {\n            if (cb) {\n                return cb(error);\n            }\n            throw error;\n        }\n    }\n    /**\n     * A promise-wrapped _handle.\n     */\n    _promiseHandle(req) {\n        return new Promise((resolve) => {\n            this._handle(req, (_err, res) => {\n                // There will always be a response, and it will always have any error\n                // that is caught and propagated.\n                resolve(res);\n            });\n        });\n    }\n    /**\n     * Ensures that the request object is valid, processes it, and passes any\n     * error and the response object to the given callback.\n     *\n     * Does not reject.\n     */\n    async _handle(callerReq, cb) {\n        if (!callerReq ||\n            Array.isArray(callerReq) ||\n            typeof callerReq !== 'object') {\n            const error = new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.invalidRequest, `Requests must be plain objects. Received: ${typeof callerReq}`, { request: callerReq });\n            return cb(error, { id: undefined, jsonrpc: '2.0', error });\n        }\n        if (typeof callerReq.method !== 'string') {\n            const error = new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.invalidRequest, `Must specify a string method. Received: ${typeof callerReq.method}`, { request: callerReq });\n            return cb(error, { id: callerReq.id, jsonrpc: '2.0', error });\n        }\n        const req = Object.assign({}, callerReq);\n        const res = {\n            id: req.id,\n            jsonrpc: req.jsonrpc,\n        };\n        let error = null;\n        try {\n            await this._processRequest(req, res);\n        }\n        catch (_error) {\n            // A request handler error, a re-thrown middleware error, or something\n            // unexpected.\n            error = _error;\n        }\n        if (error) {\n            // Ensure no result is present on an errored response\n            delete res.result;\n            if (!res.error) {\n                res.error = eth_rpc_errors_1.serializeError(error);\n            }\n        }\n        return cb(error, res);\n    }\n    /**\n     * For the given request and response, runs all middleware and their return\n     * handlers, if any, and ensures that internal request processing semantics\n     * are satisfied.\n     */\n    async _processRequest(req, res) {\n        const [error, isComplete, returnHandlers,] = await JsonRpcEngine._runAllMiddleware(req, res, this._middleware);\n        // Throw if \"end\" was not called, or if the response has neither a result\n        // nor an error.\n        JsonRpcEngine._checkForCompletion(req, res, isComplete);\n        // The return handlers should run even if an error was encountered during\n        // middleware processing.\n        await JsonRpcEngine._runReturnHandlers(returnHandlers);\n        // Now we re-throw the middleware processing error, if any, to catch it\n        // further up the call chain.\n        if (error) {\n            throw error;\n        }\n    }\n    /**\n     * Serially executes the given stack of middleware.\n     *\n     * @returns An array of any error encountered during middleware execution,\n     * a boolean indicating whether the request was completed, and an array of\n     * middleware-defined return handlers.\n     */\n    static async _runAllMiddleware(req, res, middlewareStack) {\n        const returnHandlers = [];\n        let error = null;\n        let isComplete = false;\n        // Go down stack of middleware, call and collect optional returnHandlers\n        for (const middleware of middlewareStack) {\n            [error, isComplete] = await JsonRpcEngine._runMiddleware(req, res, middleware, returnHandlers);\n            if (isComplete) {\n                break;\n            }\n        }\n        return [error, isComplete, returnHandlers.reverse()];\n    }\n    /**\n     * Runs an individual middleware.\n     *\n     * @returns An array of any error encountered during middleware exection,\n     * and a boolean indicating whether the request should end.\n     */\n    static _runMiddleware(req, res, middleware, returnHandlers) {\n        return new Promise((resolve) => {\n            const end = (err) => {\n                const error = err || res.error;\n                if (error) {\n                    res.error = eth_rpc_errors_1.serializeError(error);\n                }\n                // True indicates that the request should end\n                resolve([error, true]);\n            };\n            const next = (returnHandler) => {\n                if (res.error) {\n                    end(res.error);\n                }\n                else {\n                    if (returnHandler) {\n                        if (typeof returnHandler !== 'function') {\n                            end(new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: \"next\" return handlers must be functions. ` +\n                                `Received \"${typeof returnHandler}\" for request:\\n${jsonify(req)}`, { request: req }));\n                        }\n                        returnHandlers.push(returnHandler);\n                    }\n                    // False indicates that the request should not end\n                    resolve([null, false]);\n                }\n            };\n            try {\n                middleware(req, res, next, end);\n            }\n            catch (error) {\n                end(error);\n            }\n        });\n    }\n    /**\n     * Serially executes array of return handlers. The request and response are\n     * assumed to be in their scope.\n     */\n    static async _runReturnHandlers(handlers) {\n        for (const handler of handlers) {\n            await new Promise((resolve, reject) => {\n                handler((err) => (err ? reject(err) : resolve()));\n            });\n        }\n    }\n    /**\n     * Throws an error if the response has neither a result nor an error, or if\n     * the \"isComplete\" flag is falsy.\n     */\n    static _checkForCompletion(req, res, isComplete) {\n        if (!('result' in res) && !('error' in res)) {\n            throw new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: Response has no error or result for request:\\n${jsonify(req)}`, { request: req });\n        }\n        if (!isComplete) {\n            throw new eth_rpc_errors_1.EthereumRpcError(eth_rpc_errors_1.errorCodes.rpc.internal, `JsonRpcEngine: Nothing ended request:\\n${jsonify(req)}`, { request: req });\n        }\n    }\n}\nexports.JsonRpcEngine = JsonRpcEngine;\nfunction jsonify(request) {\n    return JSON.stringify(request, null, 2);\n}\n//# sourceMappingURL=data:application/json;base64,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# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/json-rpc-engine/dist/JsonRpcEngine.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/json-rpc-engine/dist/createAsyncMiddleware.js":
/*!*********************************************************************!*\
  !*** ../node_modules/json-rpc-engine/dist/createAsyncMiddleware.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createAsyncMiddleware = void 0;\n/**\n * JsonRpcEngine only accepts callback-based middleware directly.\n * createAsyncMiddleware exists to enable consumers to pass in async middleware\n * functions.\n *\n * Async middleware have no \"end\" function. Instead, they \"end\" if they return\n * without calling \"next\". Rather than passing in explicit return handlers,\n * async middleware can simply await \"next\", and perform operations on the\n * response object when execution resumes.\n *\n * To accomplish this, createAsyncMiddleware passes the async middleware a\n * wrapped \"next\" function. That function calls the internal JsonRpcEngine\n * \"next\" function with a return handler that resolves a promise when called.\n *\n * The return handler will always be called. Its resolution of the promise\n * enables the control flow described above.\n */\nfunction createAsyncMiddleware(asyncMiddleware) {\n    return async (req, res, next, end) => {\n        // nextPromise is the key to the implementation\n        // it is resolved by the return handler passed to the\n        // \"next\" function\n        let resolveNextPromise;\n        const nextPromise = new Promise((resolve) => {\n            resolveNextPromise = resolve;\n        });\n        let returnHandlerCallback = null;\n        let nextWasCalled = false;\n        // This will be called by the consumer's async middleware.\n        const asyncNext = async () => {\n            nextWasCalled = true;\n            // We pass a return handler to next(). When it is called by the engine,\n            // the consumer's async middleware will resume executing.\n            // eslint-disable-next-line node/callback-return\n            next((runReturnHandlersCallback) => {\n                // This callback comes from JsonRpcEngine._runReturnHandlers\n                returnHandlerCallback = runReturnHandlersCallback;\n                resolveNextPromise();\n            });\n            await nextPromise;\n        };\n        try {\n            await asyncMiddleware(req, res, asyncNext);\n            if (nextWasCalled) {\n                await nextPromise; // we must wait until the return handler is called\n                returnHandlerCallback(null);\n            }\n            else {\n                end(null);\n            }\n        }\n        catch (error) {\n            if (returnHandlerCallback) {\n                returnHandlerCallback(error);\n            }\n            else {\n                end(error);\n            }\n        }\n    };\n}\nexports.createAsyncMiddleware = createAsyncMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3JlYXRlQXN5bmNNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2NyZWF0ZUFzeW5jTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFnQkE7Ozs7Ozs7Ozs7Ozs7Ozs7R0FnQkc7QUFDSCxTQUFnQixxQkFBcUIsQ0FDbkMsZUFBNkM7SUFFN0MsT0FBTyxLQUFLLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLEVBQUU7UUFDbkMsK0NBQStDO1FBQy9DLHFEQUFxRDtRQUNyRCxrQkFBa0I7UUFDbEIsSUFBSSxrQkFBOEIsQ0FBQztRQUNuQyxNQUFNLFdBQVcsR0FBRyxJQUFJLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxFQUFFO1lBQzFDLGtCQUFrQixHQUFHLE9BQU8sQ0FBQztRQUMvQixDQUFDLENBQUMsQ0FBQztRQUVILElBQUkscUJBQXFCLEdBQVksSUFBSSxDQUFDO1FBQzFDLElBQUksYUFBYSxHQUFHLEtBQUssQ0FBQztRQUUxQiwwREFBMEQ7UUFDMUQsTUFBTSxTQUFTLEdBQUcsS0FBSyxJQUFJLEVBQUU7WUFDM0IsYUFBYSxHQUFHLElBQUksQ0FBQztZQUVyQix1RUFBdUU7WUFDdkUseURBQXlEO1lBQ3pELGdEQUFnRDtZQUNoRCxJQUFJLENBQUMsQ0FBQyx5QkFBeUIsRUFBRSxFQUFFO2dCQUNqQyw0REFBNEQ7Z0JBQzVELHFCQUFxQixHQUFHLHlCQUF5QixDQUFDO2dCQUNsRCxrQkFBa0IsRUFBRSxDQUFDO1lBQ3ZCLENBQUMsQ0FBQyxDQUFDO1lBQ0gsTUFBTSxXQUFXLENBQUM7UUFDcEIsQ0FBQyxDQUFDO1FBRUYsSUFBSTtZQUNGLE1BQU0sZUFBZSxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsU0FBUyxDQUFDLENBQUM7WUFFM0MsSUFBSSxhQUFhLEVBQUU7Z0JBQ2pCLE1BQU0sV0FBVyxDQUFDLENBQUMsa0RBQWtEO2dCQUNwRSxxQkFBK0MsQ0FBQyxJQUFJLENBQUMsQ0FBQzthQUN4RDtpQkFBTTtnQkFDTCxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUM7YUFDWDtTQUNGO1FBQUMsT0FBTyxLQUFLLEVBQUU7WUFDZCxJQUFJLHFCQUFxQixFQUFFO2dCQUN4QixxQkFBK0MsQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUN6RDtpQkFBTTtnQkFDTCxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUM7YUFDWjtTQUNGO0lBQ0gsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQS9DRCxzREErQ0MifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/json-rpc-engine/dist/createAsyncMiddleware.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js":
/*!************************************************************************!*\
  !*** ../node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createScaffoldMiddleware = void 0;\nfunction createScaffoldMiddleware(handlers) {\n    return (req, res, next, end) => {\n        const handler = handlers[req.method];\n        // if no handler, return\n        if (handler === undefined) {\n            return next();\n        }\n        // if handler is fn, call as middleware\n        if (typeof handler === 'function') {\n            return handler(req, res, next, end);\n        }\n        // if handler is some other value, use as result\n        res.result = handler;\n        return end();\n    };\n}\nexports.createScaffoldMiddleware = createScaffoldMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3JlYXRlU2NhZmZvbGRNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL2NyZWF0ZVNjYWZmb2xkTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFJQSxTQUFnQix3QkFBd0IsQ0FBQyxRQUV4QztJQUNDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxHQUFHLEVBQUUsRUFBRTtRQUM3QixNQUFNLE9BQU8sR0FBRyxRQUFRLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ3JDLHdCQUF3QjtRQUN4QixJQUFJLE9BQU8sS0FBSyxTQUFTLEVBQUU7WUFDekIsT0FBTyxJQUFJLEVBQUUsQ0FBQztTQUNmO1FBQ0QsdUNBQXVDO1FBQ3ZDLElBQUksT0FBTyxPQUFPLEtBQUssVUFBVSxFQUFFO1lBQ2pDLE9BQU8sT0FBTyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO1NBQ3JDO1FBQ0QsZ0RBQWdEO1FBQy9DLEdBQStCLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQztRQUNsRCxPQUFPLEdBQUcsRUFBRSxDQUFDO0lBQ2YsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQWpCRCw0REFpQkMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/json-rpc-engine/dist/getUniqueId.js":
/*!***********************************************************!*\
  !*** ../node_modules/json-rpc-engine/dist/getUniqueId.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getUniqueId = void 0;\n// uint32 (two's complement) max\n// more conservative than Number.MAX_SAFE_INTEGER\nconst MAX = 4294967295;\nlet idCounter = Math.floor(Math.random() * MAX);\nfunction getUniqueId() {\n    idCounter = (idCounter + 1) % MAX;\n    return idCounter;\n}\nexports.getUniqueId = getUniqueId;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZ2V0VW5pcXVlSWQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvZ2V0VW5pcXVlSWQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsZ0NBQWdDO0FBQ2hDLGlEQUFpRDtBQUNqRCxNQUFNLEdBQUcsR0FBRyxVQUFVLENBQUM7QUFDdkIsSUFBSSxTQUFTLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUM7QUFFaEQsU0FBZ0IsV0FBVztJQUN6QixTQUFTLEdBQUcsQ0FBQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBQ2xDLE9BQU8sU0FBUyxDQUFDO0FBQ25CLENBQUM7QUFIRCxrQ0FHQyJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2pzb24tcnBjLWVuZ2luZS9kaXN0L2dldFVuaXF1ZUlkLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CLDJDQUEyQyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9qc29uLXJwYy1lbmdpbmUvZGlzdC9nZXRVbmlxdWVJZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuZ2V0VW5pcXVlSWQgPSB2b2lkIDA7XG4vLyB1aW50MzIgKHR3bydzIGNvbXBsZW1lbnQpIG1heFxuLy8gbW9yZSBjb25zZXJ2YXRpdmUgdGhhbiBOdW1iZXIuTUFYX1NBRkVfSU5URUdFUlxuY29uc3QgTUFYID0gNDI5NDk2NzI5NTtcbmxldCBpZENvdW50ZXIgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBNQVgpO1xuZnVuY3Rpb24gZ2V0VW5pcXVlSWQoKSB7XG4gICAgaWRDb3VudGVyID0gKGlkQ291bnRlciArIDEpICUgTUFYO1xuICAgIHJldHVybiBpZENvdW50ZXI7XG59XG5leHBvcnRzLmdldFVuaXF1ZUlkID0gZ2V0VW5pcXVlSWQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kYXRhOmFwcGxpY2F0aW9uL2pzb247YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0ptYVd4bElqb2laMlYwVlc1cGNYVmxTV1F1YW5NaUxDSnpiM1Z5WTJWU2IyOTBJam9pSWl3aWMyOTFjbU5sY3lJNld5SXVMaTl6Y21NdloyVjBWVzVwY1hWbFNXUXVkSE1pWFN3aWJtRnRaWE1pT2x0ZExDSnRZWEJ3YVc1bmN5STZJanM3TzBGQlFVRXNaME5CUVdkRE8wRkJRMmhETEdsRVFVRnBSRHRCUVVOcVJDeE5RVUZOTEVkQlFVY3NSMEZCUnl4VlFVRlZMRU5CUVVNN1FVRkRka0lzU1VGQlNTeFRRVUZUTEVkQlFVY3NTVUZCU1N4RFFVRkRMRXRCUVVzc1EwRkJReXhKUVVGSkxFTkJRVU1zVFVGQlRTeEZRVUZGTEVkQlFVY3NSMEZCUnl4RFFVRkRMRU5CUVVNN1FVRkZhRVFzVTBGQlowSXNWMEZCVnp0SlFVTjZRaXhUUVVGVExFZEJRVWNzUTBGQlF5eFRRVUZUTEVkQlFVY3NRMEZCUXl4RFFVRkRMRWRCUVVjc1IwRkJSeXhEUVVGRE8wbEJRMnhETEU5QlFVOHNVMEZCVXl4RFFVRkRPMEZCUTI1Q0xFTkJRVU03UVVGSVJDeHJRMEZIUXlKOSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/json-rpc-engine/dist/getUniqueId.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/json-rpc-engine/dist/idRemapMiddleware.js":
/*!*****************************************************************!*\
  !*** ../node_modules/json-rpc-engine/dist/idRemapMiddleware.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createIdRemapMiddleware = void 0;\nconst getUniqueId_1 = __webpack_require__(/*! ./getUniqueId */ \"(ssr)/../node_modules/json-rpc-engine/dist/getUniqueId.js\");\nfunction createIdRemapMiddleware() {\n    return (req, res, next, _end) => {\n        const originalId = req.id;\n        const newId = getUniqueId_1.getUniqueId();\n        req.id = newId;\n        res.id = newId;\n        next((done) => {\n            req.id = originalId;\n            res.id = originalId;\n            done();\n        });\n    };\n}\nexports.createIdRemapMiddleware = createIdRemapMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaWRSZW1hcE1pZGRsZXdhcmUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaWRSZW1hcE1pZGRsZXdhcmUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsK0NBQTRDO0FBRzVDLFNBQWdCLHVCQUF1QjtJQUNyQyxPQUFPLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLEVBQUU7UUFDOUIsTUFBTSxVQUFVLEdBQUcsR0FBRyxDQUFDLEVBQUUsQ0FBQztRQUMxQixNQUFNLEtBQUssR0FBRyx5QkFBVyxFQUFFLENBQUM7UUFDNUIsR0FBRyxDQUFDLEVBQUUsR0FBRyxLQUFLLENBQUM7UUFDZixHQUFHLENBQUMsRUFBRSxHQUFHLEtBQUssQ0FBQztRQUNmLElBQUksQ0FBQyxDQUFDLElBQUksRUFBRSxFQUFFO1lBQ1osR0FBRyxDQUFDLEVBQUUsR0FBRyxVQUFVLENBQUM7WUFDcEIsR0FBRyxDQUFDLEVBQUUsR0FBRyxVQUFVLENBQUM7WUFDcEIsSUFBSSxFQUFFLENBQUM7UUFDVCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQztBQUNKLENBQUM7QUFaRCwwREFZQyJ9//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/json-rpc-engine/dist/idRemapMiddleware.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/json-rpc-engine/dist/index.js":
/*!*****************************************************!*\
  !*** ../node_modules/json-rpc-engine/dist/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./idRemapMiddleware */ \"(ssr)/../node_modules/json-rpc-engine/dist/idRemapMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./createAsyncMiddleware */ \"(ssr)/../node_modules/json-rpc-engine/dist/createAsyncMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./createScaffoldMiddleware */ \"(ssr)/../node_modules/json-rpc-engine/dist/createScaffoldMiddleware.js\"), exports);\n__exportStar(__webpack_require__(/*! ./getUniqueId */ \"(ssr)/../node_modules/json-rpc-engine/dist/getUniqueId.js\"), exports);\n__exportStar(__webpack_require__(/*! ./JsonRpcEngine */ \"(ssr)/../node_modules/json-rpc-engine/dist/JsonRpcEngine.js\"), exports);\n__exportStar(__webpack_require__(/*! ./mergeMiddleware */ \"(ssr)/../node_modules/json-rpc-engine/dist/mergeMiddleware.js\"), exports);\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsc0RBQW9DO0FBQ3BDLDBEQUF3QztBQUN4Qyw2REFBMkM7QUFDM0MsZ0RBQThCO0FBQzlCLGtEQUFnQztBQUNoQyxvREFBa0MifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/json-rpc-engine/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/json-rpc-engine/dist/mergeMiddleware.js":
/*!***************************************************************!*\
  !*** ../node_modules/json-rpc-engine/dist/mergeMiddleware.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.mergeMiddleware = void 0;\nconst JsonRpcEngine_1 = __webpack_require__(/*! ./JsonRpcEngine */ \"(ssr)/../node_modules/json-rpc-engine/dist/JsonRpcEngine.js\");\nfunction mergeMiddleware(middlewareStack) {\n    const engine = new JsonRpcEngine_1.JsonRpcEngine();\n    middlewareStack.forEach((middleware) => engine.push(middleware));\n    return engine.asMiddleware();\n}\nexports.mergeMiddleware = mergeMiddleware;\n//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWVyZ2VNaWRkbGV3YXJlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vc3JjL21lcmdlTWlkZGxld2FyZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7QUFBQSxtREFBbUU7QUFFbkUsU0FBZ0IsZUFBZSxDQUFDLGVBQXNEO0lBQ3BGLE1BQU0sTUFBTSxHQUFHLElBQUksNkJBQWEsRUFBRSxDQUFDO0lBQ25DLGVBQWUsQ0FBQyxPQUFPLENBQUMsQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQztJQUNqRSxPQUFPLE1BQU0sQ0FBQyxZQUFZLEVBQUUsQ0FBQztBQUMvQixDQUFDO0FBSkQsMENBSUMifQ==//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2pzb24tcnBjLWVuZ2luZS9kaXN0L21lcmdlTWlkZGxld2FyZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx1QkFBdUI7QUFDdkIsd0JBQXdCLG1CQUFPLENBQUMsb0ZBQWlCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkIsMkNBQTJDIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2pzb24tcnBjLWVuZ2luZS9kaXN0L21lcmdlTWlkZGxld2FyZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMubWVyZ2VNaWRkbGV3YXJlID0gdm9pZCAwO1xuY29uc3QgSnNvblJwY0VuZ2luZV8xID0gcmVxdWlyZShcIi4vSnNvblJwY0VuZ2luZVwiKTtcbmZ1bmN0aW9uIG1lcmdlTWlkZGxld2FyZShtaWRkbGV3YXJlU3RhY2spIHtcbiAgICBjb25zdCBlbmdpbmUgPSBuZXcgSnNvblJwY0VuZ2luZV8xLkpzb25ScGNFbmdpbmUoKTtcbiAgICBtaWRkbGV3YXJlU3RhY2suZm9yRWFjaCgobWlkZGxld2FyZSkgPT4gZW5naW5lLnB1c2gobWlkZGxld2FyZSkpO1xuICAgIHJldHVybiBlbmdpbmUuYXNNaWRkbGV3YXJlKCk7XG59XG5leHBvcnRzLm1lcmdlTWlkZGxld2FyZSA9IG1lcmdlTWlkZGxld2FyZTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRhdGE6YXBwbGljYXRpb24vanNvbjtiYXNlNjQsZXlKMlpYSnphVzl1SWpvekxDSm1hV3hsSWpvaWJXVnlaMlZOYVdSa2JHVjNZWEpsTG1weklpd2ljMjkxY21ObFVtOXZkQ0k2SWlJc0luTnZkWEpqWlhNaU9sc2lMaTR2YzNKakwyMWxjbWRsVFdsa1pHeGxkMkZ5WlM1MGN5SmRMQ0p1WVcxbGN5STZXMTBzSW0xaGNIQnBibWR6SWpvaU96czdRVUZCUVN4dFJFRkJiVVU3UVVGRmJrVXNVMEZCWjBJc1pVRkJaU3hEUVVGRExHVkJRWE5FTzBsQlEzQkdMRTFCUVUwc1RVRkJUU3hIUVVGSExFbEJRVWtzTmtKQlFXRXNSVUZCUlN4RFFVRkRPMGxCUTI1RExHVkJRV1VzUTBGQlF5eFBRVUZQTEVOQlFVTXNRMEZCUXl4VlFVRlZMRVZCUVVVc1JVRkJSU3hEUVVGRExFMUJRVTBzUTBGQlF5eEpRVUZKTEVOQlFVTXNWVUZCVlN4RFFVRkRMRU5CUVVNc1EwRkJRenRKUVVOcVJTeFBRVUZQTEUxQlFVMHNRMEZCUXl4WlFVRlpMRVZCUVVVc1EwRkJRenRCUVVNdlFpeERRVUZETzBGQlNrUXNNRU5CU1VNaWZRPT0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/json-rpc-engine/dist/mergeMiddleware.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js":
/*!******************************************************************************************!*\
  !*** ../node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nfunction safeApply(handler, context, args) {\n    try {\n        Reflect.apply(handler, context, args);\n    }\n    catch (err) {\n        // Throw error after timeout so as not to interrupt the stack\n        setTimeout(() => {\n            throw err;\n        });\n    }\n}\nfunction arrayClone(arr) {\n    const n = arr.length;\n    const copy = new Array(n);\n    for (let i = 0; i < n; i += 1) {\n        copy[i] = arr[i];\n    }\n    return copy;\n}\nclass SafeEventEmitter extends events_1.EventEmitter {\n    emit(type, ...args) {\n        let doError = type === 'error';\n        const events = this._events;\n        if (events !== undefined) {\n            doError = doError && events.error === undefined;\n        }\n        else if (!doError) {\n            return false;\n        }\n        // If there is no 'error' event listener then throw.\n        if (doError) {\n            let er;\n            if (args.length > 0) {\n                [er] = args;\n            }\n            if (er instanceof Error) {\n                // Note: The comments on the `throw` lines are intentional, they show\n                // up in Node's output if this results in an unhandled exception.\n                throw er; // Unhandled 'error' event\n            }\n            // At least give some kind of context to the user\n            const err = new Error(`Unhandled error.${er ? ` (${er.message})` : ''}`);\n            err.context = er;\n            throw err; // Unhandled 'error' event\n        }\n        const handler = events[type];\n        if (handler === undefined) {\n            return false;\n        }\n        if (typeof handler === 'function') {\n            safeApply(handler, this, args);\n        }\n        else {\n            const len = handler.length;\n            const listeners = arrayClone(handler);\n            for (let i = 0; i < len; i += 1) {\n                safeApply(listeners[i], this, args);\n            }\n        }\n        return true;\n    }\n}\nexports[\"default\"] = SafeEventEmitter;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/json-rpc-engine/node_modules/@metamask/safe-event-emitter/index.js\n");

/***/ })

};
;