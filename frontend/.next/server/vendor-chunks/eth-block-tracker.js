"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-block-tracker";
exports.ids = ["vendor-chunks/eth-block-tracker"];
exports.modules = {

/***/ "(ssr)/../node_modules/eth-block-tracker/dist/BaseBlockTracker.js":
/*!******************************************************************!*\
  !*** ../node_modules/eth-block-tracker/dist/BaseBlockTracker.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BaseBlockTracker = void 0;\nconst safe_event_emitter_1 = __importDefault(__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/../node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\"));\nconst sec = 1000;\nconst calculateSum = (accumulator, currentValue) => accumulator + currentValue;\nconst blockTrackerEvents = ['sync', 'latest'];\nclass BaseBlockTracker extends safe_event_emitter_1.default {\n    constructor(opts) {\n        super();\n        // config\n        this._blockResetDuration = opts.blockResetDuration || 20 * sec;\n        this._usePastBlocks = opts.usePastBlocks || false;\n        // state\n        this._currentBlock = null;\n        this._isRunning = false;\n        // bind functions for internal use\n        this._onNewListener = this._onNewListener.bind(this);\n        this._onRemoveListener = this._onRemoveListener.bind(this);\n        this._resetCurrentBlock = this._resetCurrentBlock.bind(this);\n        // listen for handler changes\n        this._setupInternalEvents();\n    }\n    async destroy() {\n        this._cancelBlockResetTimeout();\n        await this._maybeEnd();\n        super.removeAllListeners();\n    }\n    isRunning() {\n        return this._isRunning;\n    }\n    getCurrentBlock() {\n        return this._currentBlock;\n    }\n    async getLatestBlock() {\n        // return if available\n        if (this._currentBlock) {\n            return this._currentBlock;\n        }\n        // wait for a new latest block\n        const latestBlock = await new Promise((resolve) => this.once('latest', resolve));\n        // return newly set current block\n        return latestBlock;\n    }\n    // dont allow module consumer to remove our internal event listeners\n    removeAllListeners(eventName) {\n        // perform default behavior, preserve fn arity\n        if (eventName) {\n            super.removeAllListeners(eventName);\n        }\n        else {\n            super.removeAllListeners();\n        }\n        // re-add internal events\n        this._setupInternalEvents();\n        // trigger stop check just in case\n        this._onRemoveListener();\n        return this;\n    }\n    _setupInternalEvents() {\n        // first remove listeners for idempotence\n        this.removeListener('newListener', this._onNewListener);\n        this.removeListener('removeListener', this._onRemoveListener);\n        // then add them\n        this.on('newListener', this._onNewListener);\n        this.on('removeListener', this._onRemoveListener);\n    }\n    _onNewListener(eventName) {\n        // `newListener` is called *before* the listener is added\n        if (blockTrackerEvents.includes(eventName)) {\n            this._maybeStart();\n        }\n    }\n    _onRemoveListener() {\n        // `removeListener` is called *after* the listener is removed\n        if (this._getBlockTrackerEventCount() > 0) {\n            return;\n        }\n        this._maybeEnd();\n    }\n    async _maybeStart() {\n        if (this._isRunning) {\n            return;\n        }\n        this._isRunning = true;\n        // cancel setting latest block to stale\n        this._cancelBlockResetTimeout();\n        await this._start();\n        this.emit('_started');\n    }\n    async _maybeEnd() {\n        if (!this._isRunning) {\n            return;\n        }\n        this._isRunning = false;\n        this._setupBlockResetTimeout();\n        await this._end();\n        this.emit('_ended');\n    }\n    _getBlockTrackerEventCount() {\n        return blockTrackerEvents\n            .map((eventName) => this.listenerCount(eventName))\n            .reduce(calculateSum);\n    }\n    _shouldUseNewBlock(newBlock) {\n        const currentBlock = this._currentBlock;\n        if (!currentBlock) {\n            return true;\n        }\n        const newBlockInt = hexToInt(newBlock);\n        const currentBlockInt = hexToInt(currentBlock);\n        return ((this._usePastBlocks && newBlockInt < currentBlockInt) ||\n            newBlockInt > currentBlockInt);\n    }\n    _newPotentialLatest(newBlock) {\n        if (!this._shouldUseNewBlock(newBlock)) {\n            return;\n        }\n        this._setCurrentBlock(newBlock);\n    }\n    _setCurrentBlock(newBlock) {\n        const oldBlock = this._currentBlock;\n        this._currentBlock = newBlock;\n        this.emit('latest', newBlock);\n        this.emit('sync', { oldBlock, newBlock });\n    }\n    _setupBlockResetTimeout() {\n        // clear any existing timeout\n        this._cancelBlockResetTimeout();\n        // clear latest block when stale\n        this._blockResetTimeout = setTimeout(this._resetCurrentBlock, this._blockResetDuration);\n        // nodejs - dont hold process open\n        if (this._blockResetTimeout.unref) {\n            this._blockResetTimeout.unref();\n        }\n    }\n    _cancelBlockResetTimeout() {\n        if (this._blockResetTimeout) {\n            clearTimeout(this._blockResetTimeout);\n        }\n    }\n    _resetCurrentBlock() {\n        this._currentBlock = null;\n    }\n}\nexports.BaseBlockTracker = BaseBlockTracker;\n/**\n * Converts a number represented as a string in hexadecimal format into a native\n * number.\n *\n * @param hexInt - The hex string.\n * @returns The number.\n */\nfunction hexToInt(hexInt) {\n    return Number.parseInt(hexInt, 16);\n}\n//# sourceMappingURL=BaseBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/dist/BaseBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/dist/PollingBlockTracker.js":
/*!*********************************************************************!*\
  !*** ../node_modules/eth-block-tracker/dist/PollingBlockTracker.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PollingBlockTracker = void 0;\nconst json_rpc_random_id_1 = __importDefault(__webpack_require__(/*! json-rpc-random-id */ \"(ssr)/../node_modules/json-rpc-random-id/index.js\"));\nconst pify_1 = __importDefault(__webpack_require__(/*! pify */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/pify/index.js\"));\nconst BaseBlockTracker_1 = __webpack_require__(/*! ./BaseBlockTracker */ \"(ssr)/../node_modules/eth-block-tracker/dist/BaseBlockTracker.js\");\nconst logging_utils_1 = __webpack_require__(/*! ./logging-utils */ \"(ssr)/../node_modules/eth-block-tracker/dist/logging-utils.js\");\nconst log = (0, logging_utils_1.createModuleLogger)(logging_utils_1.projectLogger, 'polling-block-tracker');\nconst createRandomId = (0, json_rpc_random_id_1.default)();\nconst sec = 1000;\nclass PollingBlockTracker extends BaseBlockTracker_1.BaseBlockTracker {\n    constructor(opts = {}) {\n        var _a;\n        // parse + validate args\n        if (!opts.provider) {\n            throw new Error('PollingBlockTracker - no provider specified.');\n        }\n        super(Object.assign(Object.assign({}, opts), { blockResetDuration: (_a = opts.blockResetDuration) !== null && _a !== void 0 ? _a : opts.pollingInterval }));\n        // config\n        this._provider = opts.provider;\n        this._pollingInterval = opts.pollingInterval || 20 * sec;\n        this._retryTimeout = opts.retryTimeout || this._pollingInterval / 10;\n        this._keepEventLoopActive =\n            opts.keepEventLoopActive === undefined ? true : opts.keepEventLoopActive;\n        this._setSkipCacheFlag = opts.setSkipCacheFlag || false;\n    }\n    // trigger block polling\n    async checkForLatestBlock() {\n        await this._updateLatestBlock();\n        return await this.getLatestBlock();\n    }\n    async _start() {\n        this._synchronize();\n    }\n    async _end() {\n        // No-op\n    }\n    async _synchronize() {\n        var _a;\n        while (this._isRunning) {\n            try {\n                await this._updateLatestBlock();\n                const promise = timeout(this._pollingInterval, !this._keepEventLoopActive);\n                this.emit('_waitingForNextIteration');\n                await promise;\n            }\n            catch (err) {\n                const newErr = new Error(`PollingBlockTracker - encountered an error while attempting to update latest block:\\n${(_a = err.stack) !== null && _a !== void 0 ? _a : err}`);\n                try {\n                    this.emit('error', newErr);\n                }\n                catch (emitErr) {\n                    console.error(newErr);\n                }\n                const promise = timeout(this._retryTimeout, !this._keepEventLoopActive);\n                this.emit('_waitingForNextIteration');\n                await promise;\n            }\n        }\n    }\n    async _updateLatestBlock() {\n        // fetch + set latest block\n        const latestBlock = await this._fetchLatestBlock();\n        this._newPotentialLatest(latestBlock);\n    }\n    async _fetchLatestBlock() {\n        const req = {\n            jsonrpc: '2.0',\n            id: createRandomId(),\n            method: 'eth_blockNumber',\n            params: [],\n        };\n        if (this._setSkipCacheFlag) {\n            req.skipCache = true;\n        }\n        log('Making request', req);\n        const res = await (0, pify_1.default)((cb) => this._provider.sendAsync(req, cb))();\n        log('Got response', res);\n        if (res.error) {\n            throw new Error(`PollingBlockTracker - encountered error fetching block:\\n${res.error.message}`);\n        }\n        return res.result;\n    }\n}\nexports.PollingBlockTracker = PollingBlockTracker;\n/**\n * Waits for the specified amount of time.\n *\n * @param duration - The amount of time in milliseconds.\n * @param unref - Assuming this function is run in a Node context, governs\n * whether Node should wait before the `setTimeout` has completed before ending\n * the process (true for no, false for yes). Defaults to false.\n * @returns A promise that can be used to wait.\n */\nfunction timeout(duration, unref) {\n    return new Promise((resolve) => {\n        const timeoutRef = setTimeout(resolve, duration);\n        // don't keep process open\n        if (timeoutRef.unref && unref) {\n            timeoutRef.unref();\n        }\n    });\n}\n//# sourceMappingURL=PollingBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/dist/PollingBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js":
/*!***********************************************************************!*\
  !*** ../node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SubscribeBlockTracker = void 0;\nconst json_rpc_random_id_1 = __importDefault(__webpack_require__(/*! json-rpc-random-id */ \"(ssr)/../node_modules/json-rpc-random-id/index.js\"));\nconst BaseBlockTracker_1 = __webpack_require__(/*! ./BaseBlockTracker */ \"(ssr)/../node_modules/eth-block-tracker/dist/BaseBlockTracker.js\");\nconst createRandomId = (0, json_rpc_random_id_1.default)();\nclass SubscribeBlockTracker extends BaseBlockTracker_1.BaseBlockTracker {\n    constructor(opts = {}) {\n        // parse + validate args\n        if (!opts.provider) {\n            throw new Error('SubscribeBlockTracker - no provider specified.');\n        }\n        // BaseBlockTracker constructor\n        super(opts);\n        // config\n        this._provider = opts.provider;\n        this._subscriptionId = null;\n    }\n    async checkForLatestBlock() {\n        return await this.getLatestBlock();\n    }\n    async _start() {\n        if (this._subscriptionId === undefined || this._subscriptionId === null) {\n            try {\n                const blockNumber = (await this._call('eth_blockNumber'));\n                this._subscriptionId = (await this._call('eth_subscribe', 'newHeads'));\n                this._provider.on('data', this._handleSubData.bind(this));\n                this._newPotentialLatest(blockNumber);\n            }\n            catch (e) {\n                this.emit('error', e);\n            }\n        }\n    }\n    async _end() {\n        if (this._subscriptionId !== null && this._subscriptionId !== undefined) {\n            try {\n                await this._call('eth_unsubscribe', this._subscriptionId);\n                this._subscriptionId = null;\n            }\n            catch (e) {\n                this.emit('error', e);\n            }\n        }\n    }\n    _call(method, ...params) {\n        return new Promise((resolve, reject) => {\n            this._provider.sendAsync({\n                id: createRandomId(),\n                method,\n                params,\n                jsonrpc: '2.0',\n            }, (err, res) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(res.result);\n                }\n            });\n        });\n    }\n    _handleSubData(_, response) {\n        var _a;\n        if (response.method === 'eth_subscription' &&\n            ((_a = response.params) === null || _a === void 0 ? void 0 : _a.subscription) === this._subscriptionId) {\n            this._newPotentialLatest(response.params.result.number);\n        }\n    }\n}\nexports.SubscribeBlockTracker = SubscribeBlockTracker;\n//# sourceMappingURL=SubscribeBlockTracker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/dist/index.js":
/*!*******************************************************!*\
  !*** ../node_modules/eth-block-tracker/dist/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./PollingBlockTracker */ \"(ssr)/../node_modules/eth-block-tracker/dist/PollingBlockTracker.js\"), exports);\n__exportStar(__webpack_require__(/*! ./SubscribeBlockTracker */ \"(ssr)/../node_modules/eth-block-tracker/dist/SubscribeBlockTracker.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0EsbUNBQW1DLG9DQUFvQyxnQkFBZ0I7QUFDdkYsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGFBQWEsbUJBQU8sQ0FBQyxrR0FBdUI7QUFDNUMsYUFBYSxtQkFBTyxDQUFDLHNHQUF5QjtBQUM5QyIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9kaXN0L2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9fY3JlYXRlQmluZGluZyA9ICh0aGlzICYmIHRoaXMuX19jcmVhdGVCaW5kaW5nKSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH0pO1xufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIG9bazJdID0gbVtrXTtcbn0pKTtcbnZhciBfX2V4cG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9fZXhwb3J0U3RhcikgfHwgZnVuY3Rpb24obSwgZXhwb3J0cykge1xuICAgIGZvciAodmFyIHAgaW4gbSkgaWYgKHAgIT09IFwiZGVmYXVsdFwiICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZXhwb3J0cywgcCkpIF9fY3JlYXRlQmluZGluZyhleHBvcnRzLCBtLCBwKTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vUG9sbGluZ0Jsb2NrVHJhY2tlclwiKSwgZXhwb3J0cyk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vU3Vic2NyaWJlQmxvY2tUcmFja2VyXCIpLCBleHBvcnRzKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/dist/logging-utils.js":
/*!***************************************************************!*\
  !*** ../node_modules/eth-block-tracker/dist/logging-utils.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createModuleLogger = exports.projectLogger = void 0;\nconst utils_1 = __webpack_require__(/*! @metamask/utils */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js\");\nObject.defineProperty(exports, \"createModuleLogger\", ({ enumerable: true, get: function () { return utils_1.createModuleLogger; } }));\nexports.projectLogger = (0, utils_1.createProjectLogger)('eth-block-tracker');\n//# sourceMappingURL=logging-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL2Rpc3QvbG9nZ2luZy11dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCwwQkFBMEIsR0FBRyxxQkFBcUI7QUFDbEQsZ0JBQWdCLG1CQUFPLENBQUMsMkdBQWlCO0FBQ3pDLHNEQUFxRCxFQUFFLHFDQUFxQyxzQ0FBc0MsRUFBQztBQUNuSSxxQkFBcUI7QUFDckIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvZGlzdC9sb2dnaW5nLXV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jcmVhdGVNb2R1bGVMb2dnZXIgPSBleHBvcnRzLnByb2plY3RMb2dnZXIgPSB2b2lkIDA7XG5jb25zdCB1dGlsc18xID0gcmVxdWlyZShcIkBtZXRhbWFzay91dGlsc1wiKTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImNyZWF0ZU1vZHVsZUxvZ2dlclwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gdXRpbHNfMS5jcmVhdGVNb2R1bGVMb2dnZXI7IH0gfSk7XG5leHBvcnRzLnByb2plY3RMb2dnZXIgPSAoMCwgdXRpbHNfMS5jcmVhdGVQcm9qZWN0TG9nZ2VyKSgnZXRoLWJsb2NrLXRyYWNrZXInKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWxvZ2dpbmctdXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/dist/logging-utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assertExhaustive = exports.assertStruct = exports.assert = exports.AssertionError = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/../node_modules/superstruct/dist/index.mjs\");\n/**\n * Type guard for determining whether the given value is an error object with a\n * `message` property, such as an instance of Error.\n *\n * @param error - The object to check.\n * @returns True or false, depending on the result.\n */\nfunction isErrorWithMessage(error) {\n    return typeof error === 'object' && error !== null && 'message' in error;\n}\n/**\n * Check if a value is a constructor, i.e., a function that can be called with\n * the `new` keyword.\n *\n * @param fn - The value to check.\n * @returns `true` if the value is a constructor, or `false` otherwise.\n */\nfunction isConstructable(fn) {\n    var _a, _b;\n    /* istanbul ignore next */\n    return Boolean(typeof ((_b = (_a = fn === null || fn === void 0 ? void 0 : fn.prototype) === null || _a === void 0 ? void 0 : _a.constructor) === null || _b === void 0 ? void 0 : _b.name) === 'string');\n}\n/**\n * Get the error message from an unknown error object. If the error object has\n * a `message` property, that property is returned. Otherwise, the stringified\n * error object is returned.\n *\n * @param error - The error object to get the message from.\n * @returns The error message.\n */\nfunction getErrorMessage(error) {\n    const message = isErrorWithMessage(error) ? error.message : String(error);\n    // If the error ends with a period, remove it, as we'll add our own period.\n    if (message.endsWith('.')) {\n        return message.slice(0, -1);\n    }\n    return message;\n}\n/**\n * Initialise an {@link AssertionErrorConstructor} error.\n *\n * @param ErrorWrapper - The error class to use.\n * @param message - The error message.\n * @returns The error object.\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nfunction getError(ErrorWrapper, message) {\n    if (isConstructable(ErrorWrapper)) {\n        return new ErrorWrapper({\n            message,\n        });\n    }\n    return ErrorWrapper({\n        message,\n    });\n}\n/**\n * The default error class that is thrown if an assertion fails.\n */\nclass AssertionError extends Error {\n    constructor(options) {\n        super(options.message);\n        this.code = 'ERR_ASSERTION';\n    }\n}\nexports.AssertionError = AssertionError;\n/**\n * Same as Node.js assert.\n * If the value is falsy, throws an error, does nothing otherwise.\n *\n * @throws {@link AssertionError} If value is falsy.\n * @param value - The test that should be truthy to pass.\n * @param message - Message to be passed to {@link AssertionError} or an\n * {@link Error} instance to throw.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}. If a custom error class is provided for\n * the `message` argument, this argument is ignored.\n */\nfunction assert(value, message = 'Assertion failed.', \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper = AssertionError) {\n    if (!value) {\n        if (message instanceof Error) {\n            throw message;\n        }\n        throw getError(ErrorWrapper, message);\n    }\n}\nexports.assert = assert;\n/**\n * Assert a value against a Superstruct struct.\n *\n * @param value - The value to validate.\n * @param struct - The struct to validate against.\n * @param errorPrefix - A prefix to add to the error message. Defaults to\n * \"Assertion failed\".\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the value is not valid.\n */\nfunction assertStruct(value, struct, errorPrefix = 'Assertion failed', \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper = AssertionError) {\n    try {\n        (0, superstruct_1.assert)(value, struct);\n    }\n    catch (error) {\n        throw getError(ErrorWrapper, `${errorPrefix}: ${getErrorMessage(error)}.`);\n    }\n}\nexports.assertStruct = assertStruct;\n/**\n * Use in the default case of a switch that you want to be fully exhaustive.\n * Using this function forces the compiler to enforce exhaustivity during\n * compile-time.\n *\n * @example\n * ```\n * const number = 1;\n * switch (number) {\n *   case 0:\n *     ...\n *   case 1:\n *     ...\n *   default:\n *     assertExhaustive(snapPrefix);\n * }\n * ```\n * @param _object - The object on which the switch is being operated.\n */\nfunction assertExhaustive(_object) {\n    throw new Error('Invalid branch reached. Should be detected during compilation.');\n}\nexports.assertExhaustive = assertExhaustive;\n//# sourceMappingURL=assert.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.base64 = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/../node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * Ensure that a provided string-based struct is valid base64.\n *\n * @param struct - The string based struct.\n * @param options - Optional options to specialize base64 validation. See {@link Base64Options} documentation.\n * @returns A superstruct validating base64.\n */\nconst base64 = (struct, options = {}) => {\n    var _a, _b;\n    const paddingRequired = (_a = options.paddingRequired) !== null && _a !== void 0 ? _a : false;\n    const characterSet = (_b = options.characterSet) !== null && _b !== void 0 ? _b : 'base64';\n    let letters;\n    if (characterSet === 'base64') {\n        letters = String.raw `[A-Za-z0-9+\\/]`;\n    }\n    else {\n        (0, assert_1.assert)(characterSet === 'base64url');\n        letters = String.raw `[-_A-Za-z0-9]`;\n    }\n    let re;\n    if (paddingRequired) {\n        re = new RegExp(`^(?:${letters}{4})*(?:${letters}{3}=|${letters}{2}==)?$`, 'u');\n    }\n    else {\n        re = new RegExp(`^(?:${letters}{4})*(?:${letters}{2,3}|${letters}{3}=|${letters}{2}==)?$`, 'u');\n    }\n    return (0, superstruct_1.pattern)(struct, re);\n};\nexports.base64 = base64;\n//# sourceMappingURL=base64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js":
/*!************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createDataView = exports.concatBytes = exports.valueToBytes = exports.stringToBytes = exports.numberToBytes = exports.signedBigIntToBytes = exports.bigIntToBytes = exports.hexToBytes = exports.bytesToString = exports.bytesToNumber = exports.bytesToSignedBigInt = exports.bytesToBigInt = exports.bytesToHex = exports.assertIsBytes = exports.isBytes = void 0;\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\n// '0'.charCodeAt(0) === 48\nconst HEX_MINIMUM_NUMBER_CHARACTER = 48;\n// '9'.charCodeAt(0) === 57\nconst HEX_MAXIMUM_NUMBER_CHARACTER = 58;\nconst HEX_CHARACTER_OFFSET = 87;\n/**\n * Memoized function that returns an array to be used as a lookup table for\n * converting bytes to hexadecimal values.\n *\n * The array is created lazily and then cached for future use. The benefit of\n * this approach is that the performance of converting bytes to hex is much\n * better than if we were to call `toString(16)` on each byte.\n *\n * The downside is that the array is created once and then never garbage\n * collected. This is not a problem in practice because the array is only 256\n * elements long.\n *\n * @returns A function that returns the lookup table.\n */\nfunction getPrecomputedHexValuesBuilder() {\n    // To avoid issues with tree shaking, we need to use a function to return the\n    // array. This is because the array is only used in the `bytesToHex` function\n    // and if we were to use a global variable, the array might be removed by the\n    // tree shaker.\n    const lookupTable = [];\n    return () => {\n        if (lookupTable.length === 0) {\n            for (let i = 0; i < 256; i++) {\n                lookupTable.push(i.toString(16).padStart(2, '0'));\n            }\n        }\n        return lookupTable;\n    };\n}\n/**\n * Function implementation of the {@link getPrecomputedHexValuesBuilder}\n * function.\n */\nconst getPrecomputedHexValues = getPrecomputedHexValuesBuilder();\n/**\n * Check if a value is a `Uint8Array`.\n *\n * @param value - The value to check.\n * @returns Whether the value is a `Uint8Array`.\n */\nfunction isBytes(value) {\n    return value instanceof Uint8Array;\n}\nexports.isBytes = isBytes;\n/**\n * Assert that a value is a `Uint8Array`.\n *\n * @param value - The value to check.\n * @throws If the value is not a `Uint8Array`.\n */\nfunction assertIsBytes(value) {\n    (0, assert_1.assert)(isBytes(value), 'Value must be a Uint8Array.');\n}\nexports.assertIsBytes = assertIsBytes;\n/**\n * Convert a `Uint8Array` to a hexadecimal string.\n *\n * @param bytes - The bytes to convert to a hexadecimal string.\n * @returns The hexadecimal string.\n */\nfunction bytesToHex(bytes) {\n    assertIsBytes(bytes);\n    if (bytes.length === 0) {\n        return '0x';\n    }\n    const lookupTable = getPrecomputedHexValues();\n    const hexadecimal = new Array(bytes.length);\n    for (let i = 0; i < bytes.length; i++) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        hexadecimal[i] = lookupTable[bytes[i]];\n    }\n    return (0, hex_1.add0x)(hexadecimal.join(''));\n}\nexports.bytesToHex = bytesToHex;\n/**\n * Convert a `Uint8Array` to a `bigint`.\n *\n * To convert a `Uint8Array` to a `number` instead, use {@link bytesToNumber}.\n * To convert a two's complement encoded `Uint8Array` to a `bigint`, use\n * {@link bytesToSignedBigInt}.\n *\n * @param bytes - The bytes to convert to a `bigint`.\n * @returns The `bigint`.\n */\nfunction bytesToBigInt(bytes) {\n    assertIsBytes(bytes);\n    const hexadecimal = bytesToHex(bytes);\n    return BigInt(hexadecimal);\n}\nexports.bytesToBigInt = bytesToBigInt;\n/**\n * Convert a `Uint8Array` to a signed `bigint`. This assumes that the bytes are\n * encoded in two's complement.\n *\n * To convert a `Uint8Array` to an unsigned `bigint` instead, use\n * {@link bytesToBigInt}.\n *\n * @see https://en.wikipedia.org/wiki/Two%27s_complement\n * @param bytes - The bytes to convert to a signed `bigint`.\n * @returns The signed `bigint`.\n */\nfunction bytesToSignedBigInt(bytes) {\n    assertIsBytes(bytes);\n    let value = BigInt(0);\n    for (const byte of bytes) {\n        // eslint-disable-next-line no-bitwise\n        value = (value << BigInt(8)) + BigInt(byte);\n    }\n    return BigInt.asIntN(bytes.length * 8, value);\n}\nexports.bytesToSignedBigInt = bytesToSignedBigInt;\n/**\n * Convert a `Uint8Array` to a `number`.\n *\n * To convert a `Uint8Array` to a `bigint` instead, use {@link bytesToBigInt}.\n *\n * @param bytes - The bytes to convert to a number.\n * @returns The number.\n * @throws If the resulting number is not a safe integer.\n */\nfunction bytesToNumber(bytes) {\n    assertIsBytes(bytes);\n    const bigint = bytesToBigInt(bytes);\n    (0, assert_1.assert)(bigint <= BigInt(Number.MAX_SAFE_INTEGER), 'Number is not a safe integer. Use `bytesToBigInt` instead.');\n    return Number(bigint);\n}\nexports.bytesToNumber = bytesToNumber;\n/**\n * Convert a UTF-8 encoded `Uint8Array` to a `string`.\n *\n * @param bytes - The bytes to convert to a string.\n * @returns The string.\n */\nfunction bytesToString(bytes) {\n    assertIsBytes(bytes);\n    return new TextDecoder().decode(bytes);\n}\nexports.bytesToString = bytesToString;\n/**\n * Convert a hexadecimal string to a `Uint8Array`. The string can optionally be\n * prefixed with `0x`. It accepts even and odd length strings.\n *\n * If the value is \"0x\", an empty `Uint8Array` is returned.\n *\n * @param value - The hexadecimal string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction hexToBytes(value) {\n    var _a;\n    // \"0x\" is often used as empty byte array.\n    if (((_a = value === null || value === void 0 ? void 0 : value.toLowerCase) === null || _a === void 0 ? void 0 : _a.call(value)) === '0x') {\n        return new Uint8Array();\n    }\n    (0, hex_1.assertIsHexString)(value);\n    // Remove the `0x` prefix if it exists, and pad the string to have an even\n    // number of characters.\n    const strippedValue = (0, hex_1.remove0x)(value).toLowerCase();\n    const normalizedValue = strippedValue.length % 2 === 0 ? strippedValue : `0${strippedValue}`;\n    const bytes = new Uint8Array(normalizedValue.length / 2);\n    for (let i = 0; i < bytes.length; i++) {\n        // While this is not the prettiest way to convert a hexadecimal string to a\n        // `Uint8Array`, it is a lot faster than using `parseInt` to convert each\n        // character.\n        const c1 = normalizedValue.charCodeAt(i * 2);\n        const c2 = normalizedValue.charCodeAt(i * 2 + 1);\n        const n1 = c1 -\n            (c1 < HEX_MAXIMUM_NUMBER_CHARACTER\n                ? HEX_MINIMUM_NUMBER_CHARACTER\n                : HEX_CHARACTER_OFFSET);\n        const n2 = c2 -\n            (c2 < HEX_MAXIMUM_NUMBER_CHARACTER\n                ? HEX_MINIMUM_NUMBER_CHARACTER\n                : HEX_CHARACTER_OFFSET);\n        bytes[i] = n1 * 16 + n2;\n    }\n    return bytes;\n}\nexports.hexToBytes = hexToBytes;\n/**\n * Convert a `bigint` to a `Uint8Array`.\n *\n * This assumes that the `bigint` is an unsigned integer. To convert a signed\n * `bigint` instead, use {@link signedBigIntToBytes}.\n *\n * @param value - The bigint to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction bigIntToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(value >= BigInt(0), 'Value must be a non-negative bigint.');\n    const hexadecimal = value.toString(16);\n    return hexToBytes(hexadecimal);\n}\nexports.bigIntToBytes = bigIntToBytes;\n/**\n * Check if a `bigint` fits in a certain number of bytes.\n *\n * @param value - The `bigint` to check.\n * @param bytes - The number of bytes.\n * @returns Whether the `bigint` fits in the number of bytes.\n */\nfunction bigIntFits(value, bytes) {\n    (0, assert_1.assert)(bytes > 0);\n    /* eslint-disable no-bitwise */\n    const mask = value >> BigInt(31);\n    return !(((~value & mask) + (value & ~mask)) >> BigInt(bytes * 8 + ~0));\n    /* eslint-enable no-bitwise */\n}\n/**\n * Convert a signed `bigint` to a `Uint8Array`. This uses two's complement\n * encoding to represent negative numbers.\n *\n * To convert an unsigned `bigint` to a `Uint8Array` instead, use\n * {@link bigIntToBytes}.\n *\n * @see https://en.wikipedia.org/wiki/Two%27s_complement\n * @param value - The number to convert to bytes.\n * @param byteLength - The length of the resulting `Uint8Array`. If the number\n * is larger than the maximum value that can be represented by the given length,\n * an error is thrown.\n * @returns The bytes as `Uint8Array`.\n */\nfunction signedBigIntToBytes(value, byteLength) {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(typeof byteLength === 'number', 'Byte length must be a number.');\n    (0, assert_1.assert)(byteLength > 0, 'Byte length must be greater than 0.');\n    (0, assert_1.assert)(bigIntFits(value, byteLength), 'Byte length is too small to represent the given value.');\n    // ESLint doesn't like mutating function parameters, so to avoid having to\n    // disable the rule, we create a new variable.\n    let numberValue = value;\n    const bytes = new Uint8Array(byteLength);\n    for (let i = 0; i < bytes.length; i++) {\n        bytes[i] = Number(BigInt.asUintN(8, numberValue));\n        // eslint-disable-next-line no-bitwise\n        numberValue >>= BigInt(8);\n    }\n    return bytes.reverse();\n}\nexports.signedBigIntToBytes = signedBigIntToBytes;\n/**\n * Convert a `number` to a `Uint8Array`.\n *\n * @param value - The number to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n * @throws If the number is not a safe integer.\n */\nfunction numberToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'number', 'Value must be a number.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative number.');\n    (0, assert_1.assert)(Number.isSafeInteger(value), 'Value is not a safe integer. Use `bigIntToBytes` instead.');\n    const hexadecimal = value.toString(16);\n    return hexToBytes(hexadecimal);\n}\nexports.numberToBytes = numberToBytes;\n/**\n * Convert a `string` to a UTF-8 encoded `Uint8Array`.\n *\n * @param value - The string to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction stringToBytes(value) {\n    (0, assert_1.assert)(typeof value === 'string', 'Value must be a string.');\n    return new TextEncoder().encode(value);\n}\nexports.stringToBytes = stringToBytes;\n/**\n * Convert a byte-like value to a `Uint8Array`. The value can be a `Uint8Array`,\n * a `bigint`, a `number`, or a `string`.\n *\n * This will attempt to guess the type of the value based on its type and\n * contents. For more control over the conversion, use the more specific\n * conversion functions, such as {@link hexToBytes} or {@link stringToBytes}.\n *\n * If the value is a `string`, and it is prefixed with `0x`, it will be\n * interpreted as a hexadecimal string. Otherwise, it will be interpreted as a\n * UTF-8 string. To convert a hexadecimal string to bytes without interpreting\n * it as a UTF-8 string, use {@link hexToBytes} instead.\n *\n * If the value is a `bigint`, it is assumed to be unsigned. To convert a signed\n * `bigint` to bytes, use {@link signedBigIntToBytes} instead.\n *\n * If the value is a `Uint8Array`, it will be returned as-is.\n *\n * @param value - The value to convert to bytes.\n * @returns The bytes as `Uint8Array`.\n */\nfunction valueToBytes(value) {\n    if (typeof value === 'bigint') {\n        return bigIntToBytes(value);\n    }\n    if (typeof value === 'number') {\n        return numberToBytes(value);\n    }\n    if (typeof value === 'string') {\n        if (value.startsWith('0x')) {\n            return hexToBytes(value);\n        }\n        return stringToBytes(value);\n    }\n    if (isBytes(value)) {\n        return value;\n    }\n    throw new TypeError(`Unsupported value type: \"${typeof value}\".`);\n}\nexports.valueToBytes = valueToBytes;\n/**\n * Concatenate multiple byte-like values into a single `Uint8Array`. The values\n * can be `Uint8Array`, `bigint`, `number`, or `string`. This uses\n * {@link valueToBytes} under the hood to convert each value to bytes. Refer to\n * the documentation of that function for more information.\n *\n * @param values - The values to concatenate.\n * @returns The concatenated bytes as `Uint8Array`.\n */\nfunction concatBytes(values) {\n    const normalizedValues = new Array(values.length);\n    let byteLength = 0;\n    for (let i = 0; i < values.length; i++) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const value = valueToBytes(values[i]);\n        normalizedValues[i] = value;\n        byteLength += value.length;\n    }\n    const bytes = new Uint8Array(byteLength);\n    for (let i = 0, offset = 0; i < normalizedValues.length; i++) {\n        // While we could simply spread the values into an array and use\n        // `Uint8Array.from`, that is a lot slower than using `Uint8Array.set`.\n        bytes.set(normalizedValues[i], offset);\n        offset += normalizedValues[i].length;\n    }\n    return bytes;\n}\nexports.concatBytes = concatBytes;\n/**\n * Create a {@link DataView} from a {@link Uint8Array}. This is a convenience\n * function that avoids having to create a {@link DataView} manually, which\n * requires passing the `byteOffset` and `byteLength` parameters every time.\n *\n * Not passing the `byteOffset` and `byteLength` parameters can result in\n * unexpected behavior when the {@link Uint8Array} is a view of a larger\n * {@link ArrayBuffer}, e.g., when using {@link Uint8Array.subarray}.\n *\n * This function also supports Node.js {@link Buffer}s.\n *\n * @example\n * ```typescript\n * const bytes = new Uint8Array([1, 2, 3]);\n *\n * // This is equivalent to:\n * // const dataView = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n * const dataView = createDataView(bytes);\n * ```\n * @param bytes - The bytes to create the {@link DataView} from.\n * @returns The {@link DataView}.\n */\nfunction createDataView(bytes) {\n    // To maintain compatibility with Node.js, we need to check if the bytes are\n    // a Buffer. If so, we need to slice the buffer to get the underlying\n    // ArrayBuffer.\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof Buffer !== 'undefined' && bytes instanceof Buffer) {\n        const buffer = bytes.buffer.slice(bytes.byteOffset, bytes.byteOffset + bytes.byteLength);\n        return new DataView(buffer);\n    }\n    return new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n}\nexports.createDataView = createDataView;\n//# sourceMappingURL=bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js":
/*!***************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ChecksumStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/../node_modules/superstruct/dist/index.mjs\");\nconst base64_1 = __webpack_require__(/*! ./base64 */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\");\nexports.ChecksumStruct = (0, superstruct_1.size)((0, base64_1.base64)((0, superstruct_1.string)(), { paddingRequired: true }), 44, 44);\n//# sourceMappingURL=checksum.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC9jaGVja3N1bS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxzQkFBc0I7QUFDdEIsc0JBQXNCLG1CQUFPLENBQUMscUVBQWE7QUFDM0MsaUJBQWlCLG1CQUFPLENBQUMscUdBQVU7QUFDbkMsc0JBQXNCLCtFQUErRSx1QkFBdUI7QUFDNUgiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L2NoZWNrc3VtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5DaGVja3N1bVN0cnVjdCA9IHZvaWQgMDtcbmNvbnN0IHN1cGVyc3RydWN0XzEgPSByZXF1aXJlKFwic3VwZXJzdHJ1Y3RcIik7XG5jb25zdCBiYXNlNjRfMSA9IHJlcXVpcmUoXCIuL2Jhc2U2NFwiKTtcbmV4cG9ydHMuQ2hlY2tzdW1TdHJ1Y3QgPSAoMCwgc3VwZXJzdHJ1Y3RfMS5zaXplKSgoMCwgYmFzZTY0XzEuYmFzZTY0KSgoMCwgc3VwZXJzdHJ1Y3RfMS5zdHJpbmcpKCksIHsgcGFkZGluZ1JlcXVpcmVkOiB0cnVlIH0pLCA0NCwgNDQpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2hlY2tzdW0uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js":
/*!***************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createHex = exports.createBytes = exports.createBigInt = exports.createNumber = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/../node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst bytes_1 = __webpack_require__(/*! ./bytes */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\nconst NumberLikeStruct = (0, superstruct_1.union)([(0, superstruct_1.number)(), (0, superstruct_1.bigint)(), (0, superstruct_1.string)(), hex_1.StrictHexStruct]);\nconst NumberCoercer = (0, superstruct_1.coerce)((0, superstruct_1.number)(), NumberLikeStruct, Number);\nconst BigIntCoercer = (0, superstruct_1.coerce)((0, superstruct_1.bigint)(), NumberLikeStruct, BigInt);\nconst BytesLikeStruct = (0, superstruct_1.union)([hex_1.StrictHexStruct, (0, superstruct_1.instance)(Uint8Array)]);\nconst BytesCoercer = (0, superstruct_1.coerce)((0, superstruct_1.instance)(Uint8Array), (0, superstruct_1.union)([hex_1.StrictHexStruct]), bytes_1.hexToBytes);\nconst HexCoercer = (0, superstruct_1.coerce)(hex_1.StrictHexStruct, (0, superstruct_1.instance)(Uint8Array), bytes_1.bytesToHex);\n/**\n * Create a number from a number-like value.\n *\n * - If the value is a number, it is returned as-is.\n * - If the value is a `bigint`, it is converted to a number.\n * - If the value is a string, it is interpreted as a decimal number.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is\n * interpreted as a hexadecimal number.\n *\n * This validates that the value is a number-like value, and that the resulting\n * number is not `NaN` or `Infinity`.\n *\n * @example\n * ```typescript\n * const value = createNumber('0x010203');\n * console.log(value); // 66051\n *\n * const otherValue = createNumber(123n);\n * console.log(otherValue); // 123\n * ```\n * @param value - The value to create the number from.\n * @returns The created number.\n * @throws If the value is not a number-like value, or if the resulting number\n * is `NaN` or `Infinity`.\n */\nfunction createNumber(value) {\n    try {\n        const result = (0, superstruct_1.create)(value, NumberCoercer);\n        (0, assert_1.assert)(Number.isFinite(result), `Expected a number-like value, got \"${value}\".`);\n        return result;\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a number-like value, got \"${value}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createNumber = createNumber;\n/**\n * Create a `bigint` from a number-like value.\n *\n * - If the value is a number, it is converted to a `bigint`.\n * - If the value is a `bigint`, it is returned as-is.\n * - If the value is a string, it is interpreted as a decimal number and\n * converted to a `bigint`.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is\n * interpreted as a hexadecimal number and converted to a `bigint`.\n *\n * @example\n * ```typescript\n * const value = createBigInt('0x010203');\n * console.log(value); // 16909060n\n *\n * const otherValue = createBigInt(123);\n * console.log(otherValue); // 123n\n * ```\n * @param value - The value to create the bigint from.\n * @returns The created bigint.\n * @throws If the value is not a number-like value.\n */\nfunction createBigInt(value) {\n    try {\n        // The `BigInt` constructor throws if the value is not a number-like value.\n        // There is no need to validate the value manually.\n        return (0, superstruct_1.create)(value, BigIntCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a number-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createBigInt = createBigInt;\n/**\n * Create a byte array from a bytes-like value.\n *\n * - If the value is a byte array, it is returned as-is.\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is interpreted\n * as a hexadecimal number and converted to a byte array.\n *\n * @example\n * ```typescript\n * const value = createBytes('0x010203');\n * console.log(value); // Uint8Array [ 1, 2, 3 ]\n *\n * const otherValue = createBytes('0x010203');\n * console.log(otherValue); // Uint8Array [ 1, 2, 3 ]\n * ```\n * @param value - The value to create the byte array from.\n * @returns The created byte array.\n * @throws If the value is not a bytes-like value.\n */\nfunction createBytes(value) {\n    if (typeof value === 'string' && value.toLowerCase() === '0x') {\n        return new Uint8Array();\n    }\n    try {\n        return (0, superstruct_1.create)(value, BytesCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a bytes-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createBytes = createBytes;\n/**\n * Create a hexadecimal string from a bytes-like value.\n *\n * - If the value is a hex string (i.e., it starts with \"0x\"), it is returned\n * as-is.\n * - If the value is a `Uint8Array`, it is converted to a hex string.\n *\n * @example\n * ```typescript\n * const value = createHex(new Uint8Array([1, 2, 3]));\n * console.log(value); // '0x010203'\n *\n * const otherValue = createHex('0x010203');\n * console.log(otherValue); // '0x010203'\n * ```\n * @param value - The value to create the hex string from.\n * @returns The created hex string.\n * @throws If the value is not a bytes-like value.\n */\nfunction createHex(value) {\n    if ((value instanceof Uint8Array && value.length === 0) ||\n        (typeof value === 'string' && value.toLowerCase() === '0x')) {\n        return '0x';\n    }\n    try {\n        return (0, superstruct_1.create)(value, HexCoercer);\n    }\n    catch (error) {\n        if (error instanceof superstruct_1.StructError) {\n            throw new Error(`Expected a bytes-like value, got \"${String(error.value)}\".`);\n        }\n        /* istanbul ignore next */\n        throw error;\n    }\n}\nexports.createHex = createHex;\n//# sourceMappingURL=coercers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js":
/*!******************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js ***!
  \******************************************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _FrozenMap_map, _FrozenSet_set;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FrozenSet = exports.FrozenMap = void 0;\n/**\n * A {@link ReadonlyMap} that cannot be modified after instantiation.\n * The implementation uses an inner map hidden via a private field, and the\n * immutability guarantee relies on it being impossible to get a reference\n * to this map.\n */\nclass FrozenMap {\n    constructor(entries) {\n        _FrozenMap_map.set(this, void 0);\n        __classPrivateFieldSet(this, _FrozenMap_map, new Map(entries), \"f\");\n        Object.freeze(this);\n    }\n    get size() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").size;\n    }\n    [(_FrozenMap_map = new WeakMap(), Symbol.iterator)]() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\")[Symbol.iterator]();\n    }\n    entries() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").entries();\n    }\n    forEach(callbackfn, thisArg) {\n        // We have to wrap the specified callback in order to prevent it from\n        // receiving a reference to the inner map.\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").forEach((value, key, _map) => callbackfn.call(thisArg, value, key, this));\n    }\n    get(key) {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").get(key);\n    }\n    has(key) {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").has(key);\n    }\n    keys() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").keys();\n    }\n    values() {\n        return __classPrivateFieldGet(this, _FrozenMap_map, \"f\").values();\n    }\n    toString() {\n        return `FrozenMap(${this.size}) {${this.size > 0\n            ? ` ${[...this.entries()]\n                .map(([key, value]) => `${String(key)} => ${String(value)}`)\n                .join(', ')} `\n            : ''}}`;\n    }\n}\nexports.FrozenMap = FrozenMap;\n/**\n * A {@link ReadonlySet} that cannot be modified after instantiation.\n * The implementation uses an inner set hidden via a private field, and the\n * immutability guarantee relies on it being impossible to get a reference\n * to this set.\n */\nclass FrozenSet {\n    constructor(values) {\n        _FrozenSet_set.set(this, void 0);\n        __classPrivateFieldSet(this, _FrozenSet_set, new Set(values), \"f\");\n        Object.freeze(this);\n    }\n    get size() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").size;\n    }\n    [(_FrozenSet_set = new WeakMap(), Symbol.iterator)]() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\")[Symbol.iterator]();\n    }\n    entries() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").entries();\n    }\n    forEach(callbackfn, thisArg) {\n        // We have to wrap the specified callback in order to prevent it from\n        // receiving a reference to the inner set.\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").forEach((value, value2, _set) => callbackfn.call(thisArg, value, value2, this));\n    }\n    has(value) {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").has(value);\n    }\n    keys() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").keys();\n    }\n    values() {\n        return __classPrivateFieldGet(this, _FrozenSet_set, \"f\").values();\n    }\n    toString() {\n        return `FrozenSet(${this.size}) {${this.size > 0\n            ? ` ${[...this.values()].map((member) => String(member)).join(', ')} `\n            : ''}}`;\n    }\n}\nexports.FrozenSet = FrozenSet;\nObject.freeze(FrozenMap);\nObject.freeze(FrozenMap.prototype);\nObject.freeze(FrozenSet);\nObject.freeze(FrozenSet.prototype);\n//# sourceMappingURL=collections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js":
/*!***********************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=encryption-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC9lbmNyeXB0aW9uLXR5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC9lbmNyeXB0aW9uLXR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW5jcnlwdGlvbi10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.remove0x = exports.add0x = exports.assertIsStrictHexString = exports.assertIsHexString = exports.isStrictHexString = exports.isHexString = exports.StrictHexStruct = exports.HexStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/../node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nexports.HexStruct = (0, superstruct_1.pattern)((0, superstruct_1.string)(), /^(?:0x)?[0-9a-f]+$/iu);\nexports.StrictHexStruct = (0, superstruct_1.pattern)((0, superstruct_1.string)(), /^0x[0-9a-f]+$/iu);\n/**\n * Check if a string is a valid hex string.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid hex string.\n */\nfunction isHexString(value) {\n    return (0, superstruct_1.is)(value, exports.HexStruct);\n}\nexports.isHexString = isHexString;\n/**\n * Strictly check if a string is a valid hex string. A valid hex string must\n * start with the \"0x\"-prefix.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid hex string.\n */\nfunction isStrictHexString(value) {\n    return (0, superstruct_1.is)(value, exports.StrictHexStruct);\n}\nexports.isStrictHexString = isStrictHexString;\n/**\n * Assert that a value is a valid hex string.\n *\n * @param value - The value to check.\n * @throws If the value is not a valid hex string.\n */\nfunction assertIsHexString(value) {\n    (0, assert_1.assert)(isHexString(value), 'Value must be a hexadecimal string.');\n}\nexports.assertIsHexString = assertIsHexString;\n/**\n * Assert that a value is a valid hex string. A valid hex string must start with\n * the \"0x\"-prefix.\n *\n * @param value - The value to check.\n * @throws If the value is not a valid hex string.\n */\nfunction assertIsStrictHexString(value) {\n    (0, assert_1.assert)(isStrictHexString(value), 'Value must be a hexadecimal string, starting with \"0x\".');\n}\nexports.assertIsStrictHexString = assertIsStrictHexString;\n/**\n * Add the `0x`-prefix to a hexadecimal string. If the string already has the\n * prefix, it is returned as-is.\n *\n * @param hexadecimal - The hexadecimal string to add the prefix to.\n * @returns The prefixed hexadecimal string.\n */\nfunction add0x(hexadecimal) {\n    if (hexadecimal.startsWith('0x')) {\n        return hexadecimal;\n    }\n    if (hexadecimal.startsWith('0X')) {\n        return `0x${hexadecimal.substring(2)}`;\n    }\n    return `0x${hexadecimal}`;\n}\nexports.add0x = add0x;\n/**\n * Remove the `0x`-prefix from a hexadecimal string. If the string doesn't have\n * the prefix, it is returned as-is.\n *\n * @param hexadecimal - The hexadecimal string to remove the prefix from.\n * @returns The un-prefixed hexadecimal string.\n */\nfunction remove0x(hexadecimal) {\n    if (hexadecimal.startsWith('0x') || hexadecimal.startsWith('0X')) {\n        return hexadecimal.substring(2);\n    }\n    return hexadecimal;\n}\nexports.remove0x = remove0x;\n//# sourceMappingURL=hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js":
/*!************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./assert */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\"), exports);\n__exportStar(__webpack_require__(/*! ./base64 */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/base64.js\"), exports);\n__exportStar(__webpack_require__(/*! ./bytes */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/bytes.js\"), exports);\n__exportStar(__webpack_require__(/*! ./checksum */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/checksum.js\"), exports);\n__exportStar(__webpack_require__(/*! ./coercers */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/coercers.js\"), exports);\n__exportStar(__webpack_require__(/*! ./collections */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/collections.js\"), exports);\n__exportStar(__webpack_require__(/*! ./encryption-types */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/encryption-types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./hex */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\"), exports);\n__exportStar(__webpack_require__(/*! ./json */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js\"), exports);\n__exportStar(__webpack_require__(/*! ./keyring */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js\"), exports);\n__exportStar(__webpack_require__(/*! ./logging */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js\"), exports);\n__exportStar(__webpack_require__(/*! ./misc */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js\"), exports);\n__exportStar(__webpack_require__(/*! ./number */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js\"), exports);\n__exportStar(__webpack_require__(/*! ./opaque */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js\"), exports);\n__exportStar(__webpack_require__(/*! ./time */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js\"), exports);\n__exportStar(__webpack_require__(/*! ./transaction-types */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./versions */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getJsonRpcIdValidator = exports.assertIsJsonRpcError = exports.isJsonRpcError = exports.assertIsJsonRpcFailure = exports.isJsonRpcFailure = exports.assertIsJsonRpcSuccess = exports.isJsonRpcSuccess = exports.assertIsJsonRpcResponse = exports.isJsonRpcResponse = exports.assertIsPendingJsonRpcResponse = exports.isPendingJsonRpcResponse = exports.JsonRpcResponseStruct = exports.JsonRpcFailureStruct = exports.JsonRpcSuccessStruct = exports.PendingJsonRpcResponseStruct = exports.assertIsJsonRpcRequest = exports.isJsonRpcRequest = exports.assertIsJsonRpcNotification = exports.isJsonRpcNotification = exports.JsonRpcNotificationStruct = exports.JsonRpcRequestStruct = exports.JsonRpcParamsStruct = exports.JsonRpcErrorStruct = exports.JsonRpcIdStruct = exports.JsonRpcVersionStruct = exports.jsonrpc2 = exports.getJsonSize = exports.isValidJson = exports.JsonStruct = exports.UnsafeJsonStruct = void 0;\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/../node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * A struct to check if the given value is finite number. Superstruct's\n * `number()` struct does not check if the value is finite.\n *\n * @returns A struct to check if the given value is finite number.\n */\nconst finiteNumber = () => (0, superstruct_1.define)('finite number', (value) => {\n    return (0, superstruct_1.is)(value, (0, superstruct_1.number)()) && Number.isFinite(value);\n});\n/**\n * A struct to check if the given value is a valid JSON-serializable value.\n *\n * Note that this struct is unsafe. For safe validation, use {@link JsonStruct}.\n */\n// We cannot infer the type of the struct, because it is recursive.\nexports.UnsafeJsonStruct = (0, superstruct_1.union)([\n    (0, superstruct_1.literal)(null),\n    (0, superstruct_1.boolean)(),\n    finiteNumber(),\n    (0, superstruct_1.string)(),\n    (0, superstruct_1.array)((0, superstruct_1.lazy)(() => exports.UnsafeJsonStruct)),\n    (0, superstruct_1.record)((0, superstruct_1.string)(), (0, superstruct_1.lazy)(() => exports.UnsafeJsonStruct)),\n]);\n/**\n * A struct to check if the given value is a valid JSON-serializable value.\n *\n * This struct sanitizes the value before validating it, so that it is safe to\n * use with untrusted input.\n */\nexports.JsonStruct = (0, superstruct_1.define)('Json', (value, context) => {\n    /**\n     * Helper function that runs the given struct validator and returns the\n     * validation errors, if any. If the value is valid, it returns `true`.\n     *\n     * @param innerValue - The value to validate.\n     * @param struct - The struct to use for validation.\n     * @returns The validation errors, or `true` if the value is valid.\n     */\n    function checkStruct(innerValue, struct) {\n        const iterator = struct.validator(innerValue, context);\n        const errors = [...iterator];\n        if (errors.length > 0) {\n            return errors;\n        }\n        return true;\n    }\n    try {\n        // The plain value must be a valid JSON value, but it may be altered in the\n        // process of JSON serialization, so we need to validate it again after\n        // serialization. This has the added benefit that the returned error messages\n        // will be more helpful, as they will point to the exact location of the\n        // invalid value.\n        //\n        // This seems overcomplicated, but without checking the plain value first,\n        // there are some cases where the validation passes, even though the value is\n        // not valid JSON. For example, `undefined` is not valid JSON, but serializing\n        // it will remove it from the object, so the validation will pass.\n        const unsafeResult = checkStruct(value, exports.UnsafeJsonStruct);\n        if (unsafeResult !== true) {\n            return unsafeResult;\n        }\n        // JavaScript engines are highly optimized for this specific use case of\n        // JSON parsing and stringifying, so there should be no performance impact.\n        return checkStruct(JSON.parse(JSON.stringify(value)), exports.UnsafeJsonStruct);\n    }\n    catch (error) {\n        if (error instanceof RangeError) {\n            return 'Circular reference detected';\n        }\n        return false;\n    }\n});\n/**\n * Check if the given value is a valid {@link Json} value, i.e., a value that is\n * serializable to JSON.\n *\n * @param value - The value to check.\n * @returns Whether the value is a valid {@link Json} value.\n */\nfunction isValidJson(value) {\n    return (0, superstruct_1.is)(value, exports.JsonStruct);\n}\nexports.isValidJson = isValidJson;\n/**\n * Get the size of a JSON value in bytes. This also validates the value.\n *\n * @param value - The JSON value to get the size of.\n * @returns The size of the JSON value in bytes.\n */\nfunction getJsonSize(value) {\n    (0, assert_1.assertStruct)(value, exports.JsonStruct, 'Invalid JSON value');\n    const json = JSON.stringify(value);\n    return new TextEncoder().encode(json).byteLength;\n}\nexports.getJsonSize = getJsonSize;\n/**\n * The string '2.0'.\n */\nexports.jsonrpc2 = '2.0';\nexports.JsonRpcVersionStruct = (0, superstruct_1.literal)(exports.jsonrpc2);\nexports.JsonRpcIdStruct = (0, superstruct_1.nullable)((0, superstruct_1.union)([(0, superstruct_1.number)(), (0, superstruct_1.string)()]));\nexports.JsonRpcErrorStruct = (0, superstruct_1.object)({\n    code: (0, superstruct_1.integer)(),\n    message: (0, superstruct_1.string)(),\n    data: (0, superstruct_1.optional)(exports.JsonStruct),\n    stack: (0, superstruct_1.optional)((0, superstruct_1.string)()),\n});\nexports.JsonRpcParamsStruct = (0, superstruct_1.optional)((0, superstruct_1.union)([(0, superstruct_1.record)((0, superstruct_1.string)(), exports.JsonStruct), (0, superstruct_1.array)(exports.JsonStruct)]));\nexports.JsonRpcRequestStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    method: (0, superstruct_1.string)(),\n    params: exports.JsonRpcParamsStruct,\n});\nexports.JsonRpcNotificationStruct = (0, superstruct_1.omit)(exports.JsonRpcRequestStruct, ['id']);\n/**\n * Check if the given value is a valid {@link JsonRpcNotification} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcNotification}\n * object.\n */\nfunction isJsonRpcNotification(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcNotificationStruct);\n}\nexports.isJsonRpcNotification = isJsonRpcNotification;\n/**\n * Assert that the given value is a valid {@link JsonRpcNotification} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcNotification} object.\n */\nfunction assertIsJsonRpcNotification(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcNotificationStruct, 'Invalid JSON-RPC notification', ErrorWrapper);\n}\nexports.assertIsJsonRpcNotification = assertIsJsonRpcNotification;\n/**\n * Check if the given value is a valid {@link JsonRpcRequest} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcRequest} object.\n */\nfunction isJsonRpcRequest(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcRequestStruct);\n}\nexports.isJsonRpcRequest = isJsonRpcRequest;\n/**\n * Assert that the given value is a valid {@link JsonRpcRequest} object.\n *\n * @param value - The JSON-RPC request or notification to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcRequest} object.\n */\nfunction assertIsJsonRpcRequest(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcRequestStruct, 'Invalid JSON-RPC request', ErrorWrapper);\n}\nexports.assertIsJsonRpcRequest = assertIsJsonRpcRequest;\nexports.PendingJsonRpcResponseStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    result: (0, superstruct_1.optional)((0, superstruct_1.unknown)()),\n    error: (0, superstruct_1.optional)(exports.JsonRpcErrorStruct),\n});\nexports.JsonRpcSuccessStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    result: exports.JsonStruct,\n});\nexports.JsonRpcFailureStruct = (0, superstruct_1.object)({\n    id: exports.JsonRpcIdStruct,\n    jsonrpc: exports.JsonRpcVersionStruct,\n    error: exports.JsonRpcErrorStruct,\n});\nexports.JsonRpcResponseStruct = (0, superstruct_1.union)([\n    exports.JsonRpcSuccessStruct,\n    exports.JsonRpcFailureStruct,\n]);\n/**\n * Type guard to check whether specified JSON-RPC response is a\n * {@link PendingJsonRpcResponse}.\n *\n * @param response - The JSON-RPC response to check.\n * @returns Whether the specified JSON-RPC response is pending.\n */\nfunction isPendingJsonRpcResponse(response) {\n    return (0, superstruct_1.is)(response, exports.PendingJsonRpcResponseStruct);\n}\nexports.isPendingJsonRpcResponse = isPendingJsonRpcResponse;\n/**\n * Assert that the given value is a valid {@link PendingJsonRpcResponse} object.\n *\n * @param response - The JSON-RPC response to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link PendingJsonRpcResponse}\n * object.\n */\nfunction assertIsPendingJsonRpcResponse(response, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(response, exports.PendingJsonRpcResponseStruct, 'Invalid pending JSON-RPC response', ErrorWrapper);\n}\nexports.assertIsPendingJsonRpcResponse = assertIsPendingJsonRpcResponse;\n/**\n * Type guard to check if a value is a {@link JsonRpcResponse}.\n *\n * @param response - The object to check.\n * @returns Whether the object is a JsonRpcResponse.\n */\nfunction isJsonRpcResponse(response) {\n    return (0, superstruct_1.is)(response, exports.JsonRpcResponseStruct);\n}\nexports.isJsonRpcResponse = isJsonRpcResponse;\n/**\n * Assert that the given value is a valid {@link JsonRpcResponse} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcResponse} object.\n */\nfunction assertIsJsonRpcResponse(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcResponseStruct, 'Invalid JSON-RPC response', ErrorWrapper);\n}\nexports.assertIsJsonRpcResponse = assertIsJsonRpcResponse;\n/**\n * Check if the given value is a valid {@link JsonRpcSuccess} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcSuccess} object.\n */\nfunction isJsonRpcSuccess(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcSuccessStruct);\n}\nexports.isJsonRpcSuccess = isJsonRpcSuccess;\n/**\n * Assert that the given value is a valid {@link JsonRpcSuccess} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcSuccess} object.\n */\nfunction assertIsJsonRpcSuccess(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcSuccessStruct, 'Invalid JSON-RPC success response', ErrorWrapper);\n}\nexports.assertIsJsonRpcSuccess = assertIsJsonRpcSuccess;\n/**\n * Check if the given value is a valid {@link JsonRpcFailure} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcFailure} object.\n */\nfunction isJsonRpcFailure(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcFailureStruct);\n}\nexports.isJsonRpcFailure = isJsonRpcFailure;\n/**\n * Assert that the given value is a valid {@link JsonRpcFailure} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcFailure} object.\n */\nfunction assertIsJsonRpcFailure(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcFailureStruct, 'Invalid JSON-RPC failure response', ErrorWrapper);\n}\nexports.assertIsJsonRpcFailure = assertIsJsonRpcFailure;\n/**\n * Check if the given value is a valid {@link JsonRpcError} object.\n *\n * @param value - The value to check.\n * @returns Whether the given value is a valid {@link JsonRpcError} object.\n */\nfunction isJsonRpcError(value) {\n    return (0, superstruct_1.is)(value, exports.JsonRpcErrorStruct);\n}\nexports.isJsonRpcError = isJsonRpcError;\n/**\n * Assert that the given value is a valid {@link JsonRpcError} object.\n *\n * @param value - The value to check.\n * @param ErrorWrapper - The error class to throw if the assertion fails.\n * Defaults to {@link AssertionError}.\n * @throws If the given value is not a valid {@link JsonRpcError} object.\n */\nfunction assertIsJsonRpcError(value, \n// eslint-disable-next-line @typescript-eslint/naming-convention\nErrorWrapper) {\n    (0, assert_1.assertStruct)(value, exports.JsonRpcErrorStruct, 'Invalid JSON-RPC error', ErrorWrapper);\n}\nexports.assertIsJsonRpcError = assertIsJsonRpcError;\n/**\n * Gets a function for validating JSON-RPC request / response `id` values.\n *\n * By manipulating the options of this factory, you can control the behavior\n * of the resulting validator for some edge cases. This is useful because e.g.\n * `null` should sometimes but not always be permitted.\n *\n * Note that the empty string (`''`) is always permitted by the JSON-RPC\n * specification, but that kind of sucks and you may want to forbid it in some\n * instances anyway.\n *\n * For more details, see the\n * [JSON-RPC Specification](https://www.jsonrpc.org/specification).\n *\n * @param options - An options object.\n * @param options.permitEmptyString - Whether the empty string (i.e. `''`)\n * should be treated as a valid ID. Default: `true`\n * @param options.permitFractions - Whether fractional numbers (e.g. `1.2`)\n * should be treated as valid IDs. Default: `false`\n * @param options.permitNull - Whether `null` should be treated as a valid ID.\n * Default: `true`\n * @returns The JSON-RPC ID validator function.\n */\nfunction getJsonRpcIdValidator(options) {\n    const { permitEmptyString, permitFractions, permitNull } = Object.assign({ permitEmptyString: true, permitFractions: false, permitNull: true }, options);\n    /**\n     * Type guard for {@link JsonRpcId}.\n     *\n     * @param id - The JSON-RPC ID value to check.\n     * @returns Whether the given ID is valid per the options given to the\n     * factory.\n     */\n    const isValidJsonRpcId = (id) => {\n        return Boolean((typeof id === 'number' && (permitFractions || Number.isInteger(id))) ||\n            (typeof id === 'string' && (permitEmptyString || id.length > 0)) ||\n            (permitNull && id === null));\n    };\n    return isValidJsonRpcId;\n}\nexports.getJsonRpcIdValidator = getJsonRpcIdValidator;\n//# sourceMappingURL=json.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/json.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=keyring.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC9rZXlyaW5nLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC9rZXlyaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9a2V5cmluZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/keyring.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createModuleLogger = exports.createProjectLogger = void 0;\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(ssr)/../node_modules/debug/src/index.js\"));\nconst globalLogger = (0, debug_1.default)('metamask');\n/**\n * Creates a logger via the `debug` library whose log messages will be tagged\n * using the name of your project. By default, such messages will be\n * suppressed, but you can reveal them by setting the `DEBUG` environment\n * variable to `metamask:<projectName>`. You can also set this variable to\n * `metamask:*` if you want to see log messages from all MetaMask projects that\n * are also using this function to create their loggers.\n *\n * @param projectName - The name of your project. This should be the name of\n * your NPM package if you're developing one.\n * @returns An instance of `debug`.\n */\nfunction createProjectLogger(projectName) {\n    return globalLogger.extend(projectName);\n}\nexports.createProjectLogger = createProjectLogger;\n/**\n * Creates a logger via the `debug` library which is derived from the logger for\n * the whole project whose log messages will be tagged using the name of your\n * module. By default, such messages will be suppressed, but you can reveal them\n * by setting the `DEBUG` environment variable to\n * `metamask:<projectName>:<moduleName>`. You can also set this variable to\n * `metamask:<projectName>:*` if you want to see log messages from the project,\n * or `metamask:*` if you want to see log messages from all MetaMask projects.\n *\n * @param projectLogger - The logger created via {@link createProjectLogger}.\n * @param moduleName - The name of your module. You could use the name of the\n * file where you're using this logger or some other name.\n * @returns An instance of `debug`.\n */\nfunction createModuleLogger(projectLogger, moduleName) {\n    return projectLogger.extend(moduleName);\n}\nexports.createModuleLogger = createModuleLogger;\n//# sourceMappingURL=logging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/logging.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n//\n// Types\n//\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.calculateNumberSize = exports.calculateStringSize = exports.isASCII = exports.isPlainObject = exports.ESCAPE_CHARACTERS_REGEXP = exports.JsonSize = exports.hasProperty = exports.isObject = exports.isNullOrUndefined = exports.isNonEmptyArray = void 0;\n//\n// Type Guards\n//\n/**\n * A {@link NonEmptyArray} type guard.\n *\n * @template Element - The non-empty array member type.\n * @param value - The value to check.\n * @returns Whether the value is a non-empty array.\n */\nfunction isNonEmptyArray(value) {\n    return Array.isArray(value) && value.length > 0;\n}\nexports.isNonEmptyArray = isNonEmptyArray;\n/**\n * Type guard for \"nullishness\".\n *\n * @param value - Any value.\n * @returns `true` if the value is null or undefined, `false` otherwise.\n */\nfunction isNullOrUndefined(value) {\n    return value === null || value === undefined;\n}\nexports.isNullOrUndefined = isNullOrUndefined;\n/**\n * A type guard for {@link RuntimeObject}.\n *\n * @param value - The value to check.\n * @returns Whether the specified value has a runtime type of `object` and is\n * neither `null` nor an `Array`.\n */\nfunction isObject(value) {\n    return Boolean(value) && typeof value === 'object' && !Array.isArray(value);\n}\nexports.isObject = isObject;\n//\n// Other utility functions\n//\n/**\n * A type guard for ensuring an object has a property.\n *\n * @param objectToCheck - The object to check.\n * @param name - The property name to check for.\n * @returns Whether the specified object has an own property with the specified\n * name, regardless of whether it is enumerable or not.\n */\nconst hasProperty = (objectToCheck, name) => Object.hasOwnProperty.call(objectToCheck, name);\nexports.hasProperty = hasProperty;\n/**\n * Predefined sizes (in Bytes) of specific parts of JSON structure.\n */\nvar JsonSize;\n(function (JsonSize) {\n    JsonSize[JsonSize[\"Null\"] = 4] = \"Null\";\n    JsonSize[JsonSize[\"Comma\"] = 1] = \"Comma\";\n    JsonSize[JsonSize[\"Wrapper\"] = 1] = \"Wrapper\";\n    JsonSize[JsonSize[\"True\"] = 4] = \"True\";\n    JsonSize[JsonSize[\"False\"] = 5] = \"False\";\n    JsonSize[JsonSize[\"Quote\"] = 1] = \"Quote\";\n    JsonSize[JsonSize[\"Colon\"] = 1] = \"Colon\";\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    JsonSize[JsonSize[\"Date\"] = 24] = \"Date\";\n})(JsonSize = exports.JsonSize || (exports.JsonSize = {}));\n/**\n * Regular expression with pattern matching for (special) escaped characters.\n */\nexports.ESCAPE_CHARACTERS_REGEXP = /\"|\\\\|\\n|\\r|\\t/gu;\n/**\n * Check if the value is plain object.\n *\n * @param value - Value to be checked.\n * @returns True if an object is the plain JavaScript object,\n * false if the object is not plain (e.g. function).\n */\nfunction isPlainObject(value) {\n    if (typeof value !== 'object' || value === null) {\n        return false;\n    }\n    try {\n        let proto = value;\n        while (Object.getPrototypeOf(proto) !== null) {\n            proto = Object.getPrototypeOf(proto);\n        }\n        return Object.getPrototypeOf(value) === proto;\n    }\n    catch (_) {\n        return false;\n    }\n}\nexports.isPlainObject = isPlainObject;\n/**\n * Check if character is ASCII.\n *\n * @param character - Character.\n * @returns True if a character code is ASCII, false if not.\n */\nfunction isASCII(character) {\n    return character.charCodeAt(0) <= 127;\n}\nexports.isASCII = isASCII;\n/**\n * Calculate string size.\n *\n * @param value - String value to calculate size.\n * @returns Number of bytes used to store whole string value.\n */\nfunction calculateStringSize(value) {\n    var _a;\n    const size = value.split('').reduce((total, character) => {\n        if (isASCII(character)) {\n            return total + 1;\n        }\n        return total + 2;\n    }, 0);\n    // Also detect characters that need backslash escape\n    return size + ((_a = value.match(exports.ESCAPE_CHARACTERS_REGEXP)) !== null && _a !== void 0 ? _a : []).length;\n}\nexports.calculateStringSize = calculateStringSize;\n/**\n * Calculate size of a number ofter JSON serialization.\n *\n * @param value - Number value to calculate size.\n * @returns Number of bytes used to store whole number in JSON.\n */\nfunction calculateNumberSize(value) {\n    return value.toString().length;\n}\nexports.calculateNumberSize = calculateNumberSize;\n//# sourceMappingURL=misc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/misc.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hexToBigInt = exports.hexToNumber = exports.bigIntToHex = exports.numberToHex = void 0;\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\nconst hex_1 = __webpack_require__(/*! ./hex */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/hex.js\");\n/**\n * Convert a number to a hexadecimal string. This verifies that the number is a\n * non-negative safe integer.\n *\n * To convert a `bigint` to a hexadecimal string instead, use\n * {@link bigIntToHex}.\n *\n * @example\n * ```typescript\n * numberToHex(0); // '0x0'\n * numberToHex(1); // '0x1'\n * numberToHex(16); // '0x10'\n * ```\n * @param value - The number to convert to a hexadecimal string.\n * @returns The hexadecimal string, with the \"0x\"-prefix.\n * @throws If the number is not a non-negative safe integer.\n */\nconst numberToHex = (value) => {\n    (0, assert_1.assert)(typeof value === 'number', 'Value must be a number.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative number.');\n    (0, assert_1.assert)(Number.isSafeInteger(value), 'Value is not a safe integer. Use `bigIntToHex` instead.');\n    return (0, hex_1.add0x)(value.toString(16));\n};\nexports.numberToHex = numberToHex;\n/**\n * Convert a `bigint` to a hexadecimal string. This verifies that the `bigint`\n * is a non-negative integer.\n *\n * To convert a number to a hexadecimal string instead, use {@link numberToHex}.\n *\n * @example\n * ```typescript\n * bigIntToHex(0n); // '0x0'\n * bigIntToHex(1n); // '0x1'\n * bigIntToHex(16n); // '0x10'\n * ```\n * @param value - The `bigint` to convert to a hexadecimal string.\n * @returns The hexadecimal string, with the \"0x\"-prefix.\n * @throws If the `bigint` is not a non-negative integer.\n */\nconst bigIntToHex = (value) => {\n    (0, assert_1.assert)(typeof value === 'bigint', 'Value must be a bigint.');\n    (0, assert_1.assert)(value >= 0, 'Value must be a non-negative bigint.');\n    return (0, hex_1.add0x)(value.toString(16));\n};\nexports.bigIntToHex = bigIntToHex;\n/**\n * Convert a hexadecimal string to a number. This verifies that the string is a\n * valid hex string, and that the resulting number is a safe integer. Both\n * \"0x\"-prefixed and unprefixed strings are supported.\n *\n * To convert a hexadecimal string to a `bigint` instead, use\n * {@link hexToBigInt}.\n *\n * @example\n * ```typescript\n * hexToNumber('0x0'); // 0\n * hexToNumber('0x1'); // 1\n * hexToNumber('0x10'); // 16\n * ```\n * @param value - The hexadecimal string to convert to a number.\n * @returns The number.\n * @throws If the value is not a valid hexadecimal string, or if the resulting\n * number is not a safe integer.\n */\nconst hexToNumber = (value) => {\n    (0, hex_1.assertIsHexString)(value);\n    // `parseInt` accepts values without the \"0x\"-prefix, whereas `Number` does\n    // not. Using this is slightly faster than `Number(add0x(value))`.\n    const numberValue = parseInt(value, 16);\n    (0, assert_1.assert)(Number.isSafeInteger(numberValue), 'Value is not a safe integer. Use `hexToBigInt` instead.');\n    return numberValue;\n};\nexports.hexToNumber = hexToNumber;\n/**\n * Convert a hexadecimal string to a `bigint`. This verifies that the string is\n * a valid hex string. Both \"0x\"-prefixed and unprefixed strings are supported.\n *\n * To convert a hexadecimal string to a number instead, use {@link hexToNumber}.\n *\n * @example\n * ```typescript\n * hexToBigInt('0x0'); // 0n\n * hexToBigInt('0x1'); // 1n\n * hexToBigInt('0x10'); // 16n\n * ```\n * @param value - The hexadecimal string to convert to a `bigint`.\n * @returns The `bigint`.\n * @throws If the value is not a valid hexadecimal string.\n */\nconst hexToBigInt = (value) => {\n    (0, hex_1.assertIsHexString)(value);\n    // The `BigInt` constructor requires the \"0x\"-prefix to parse a hex string.\n    return BigInt((0, hex_1.add0x)(value));\n};\nexports.hexToBigInt = hexToBigInt;\n//# sourceMappingURL=number.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/number.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=opaque.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC9vcGFxdWUuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL0BtZXRhbWFzay91dGlscy9kaXN0L29wYXF1ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW9wYXF1ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/opaque.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.timeSince = exports.inMilliseconds = exports.Duration = void 0;\n/**\n * Common duration constants, in milliseconds.\n */\nvar Duration;\n(function (Duration) {\n    /**\n     * A millisecond.\n     */\n    Duration[Duration[\"Millisecond\"] = 1] = \"Millisecond\";\n    /**\n     * A second, in milliseconds.\n     */\n    Duration[Duration[\"Second\"] = 1000] = \"Second\";\n    /**\n     * A minute, in milliseconds.\n     */\n    Duration[Duration[\"Minute\"] = 60000] = \"Minute\";\n    /**\n     * An hour, in milliseconds.\n     */\n    Duration[Duration[\"Hour\"] = 3600000] = \"Hour\";\n    /**\n     * A day, in milliseconds.\n     */\n    Duration[Duration[\"Day\"] = 86400000] = \"Day\";\n    /**\n     * A week, in milliseconds.\n     */\n    Duration[Duration[\"Week\"] = 604800000] = \"Week\";\n    /**\n     * A year, in milliseconds.\n     */\n    Duration[Duration[\"Year\"] = 31536000000] = \"Year\";\n})(Duration = exports.Duration || (exports.Duration = {}));\nconst isNonNegativeInteger = (number) => Number.isInteger(number) && number >= 0;\nconst assertIsNonNegativeInteger = (number, name) => {\n    if (!isNonNegativeInteger(number)) {\n        throw new Error(`\"${name}\" must be a non-negative integer. Received: \"${number}\".`);\n    }\n};\n/**\n * Calculates the millisecond value of the specified number of units of time.\n *\n * @param count - The number of units of time.\n * @param duration - The unit of time to count.\n * @returns The count multiplied by the specified duration.\n */\nfunction inMilliseconds(count, duration) {\n    assertIsNonNegativeInteger(count, 'count');\n    return count * duration;\n}\nexports.inMilliseconds = inMilliseconds;\n/**\n * Gets the milliseconds since a particular Unix epoch timestamp.\n *\n * @param timestamp - A Unix millisecond timestamp.\n * @returns The number of milliseconds elapsed since the specified timestamp.\n */\nfunction timeSince(timestamp) {\n    assertIsNonNegativeInteger(timestamp, 'timestamp');\n    return Date.now() - timestamp;\n}\nexports.timeSince = timeSince;\n//# sourceMappingURL=time.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/time.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js":
/*!************************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=transaction-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9AbWV0YW1hc2svdXRpbHMvZGlzdC90cmFuc2FjdGlvbi10eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvQG1ldGFtYXNrL3V0aWxzL2Rpc3QvdHJhbnNhY3Rpb24tdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10cmFuc2FjdGlvbi10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/transaction-types.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js":
/*!***************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.satisfiesVersionRange = exports.gtRange = exports.gtVersion = exports.assertIsSemVerRange = exports.assertIsSemVerVersion = exports.isValidSemVerRange = exports.isValidSemVerVersion = exports.VersionRangeStruct = exports.VersionStruct = void 0;\nconst semver_1 = __webpack_require__(/*! semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/index.js\");\nconst superstruct_1 = __webpack_require__(/*! superstruct */ \"(ssr)/../node_modules/superstruct/dist/index.mjs\");\nconst assert_1 = __webpack_require__(/*! ./assert */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/assert.js\");\n/**\n * A struct for validating a version string.\n */\nexports.VersionStruct = (0, superstruct_1.refine)((0, superstruct_1.string)(), 'Version', (value) => {\n    if ((0, semver_1.valid)(value) === null) {\n        return `Expected SemVer version, got \"${value}\"`;\n    }\n    return true;\n});\nexports.VersionRangeStruct = (0, superstruct_1.refine)((0, superstruct_1.string)(), 'Version range', (value) => {\n    if ((0, semver_1.validRange)(value) === null) {\n        return `Expected SemVer range, got \"${value}\"`;\n    }\n    return true;\n});\n/**\n * Checks whether a SemVer version is valid.\n *\n * @param version - A potential version.\n * @returns `true` if the version is valid, and `false` otherwise.\n */\nfunction isValidSemVerVersion(version) {\n    return (0, superstruct_1.is)(version, exports.VersionStruct);\n}\nexports.isValidSemVerVersion = isValidSemVerVersion;\n/**\n * Checks whether a SemVer version range is valid.\n *\n * @param versionRange - A potential version range.\n * @returns `true` if the version range is valid, and `false` otherwise.\n */\nfunction isValidSemVerRange(versionRange) {\n    return (0, superstruct_1.is)(versionRange, exports.VersionRangeStruct);\n}\nexports.isValidSemVerRange = isValidSemVerRange;\n/**\n * Asserts that a value is a valid concrete SemVer version.\n *\n * @param version - A potential SemVer concrete version.\n */\nfunction assertIsSemVerVersion(version) {\n    (0, assert_1.assertStruct)(version, exports.VersionStruct);\n}\nexports.assertIsSemVerVersion = assertIsSemVerVersion;\n/**\n * Asserts that a value is a valid SemVer range.\n *\n * @param range - A potential SemVer range.\n */\nfunction assertIsSemVerRange(range) {\n    (0, assert_1.assertStruct)(range, exports.VersionRangeStruct);\n}\nexports.assertIsSemVerRange = assertIsSemVerRange;\n/**\n * Checks whether a SemVer version is greater than another.\n *\n * @param version1 - The left-hand version.\n * @param version2 - The right-hand version.\n * @returns `version1 > version2`.\n */\nfunction gtVersion(version1, version2) {\n    return (0, semver_1.gt)(version1, version2);\n}\nexports.gtVersion = gtVersion;\n/**\n * Checks whether a SemVer version is greater than all possibilities in a range.\n *\n * @param version - A SemvVer version.\n * @param range - The range to check against.\n * @returns `version > range`.\n */\nfunction gtRange(version, range) {\n    return (0, semver_1.gtr)(version, range);\n}\nexports.gtRange = gtRange;\n/**\n * Returns whether a SemVer version satisfies a SemVer range.\n *\n * @param version - The SemVer version to check.\n * @param versionRange - The SemVer version range to check against.\n * @returns Whether the version satisfied the version range.\n */\nfunction satisfiesVersionRange(version, versionRange) {\n    return (0, semver_1.satisfies)(version, versionRange, {\n        includePrerelease: true,\n    });\n}\nexports.satisfiesVersionRange = satisfiesVersionRange;\n//# sourceMappingURL=versions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/@metamask/utils/dist/versions.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/pify/index.js":
/*!********************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/pify/index.js ***!
  \********************************************************************/
/***/ ((module) => {

eval("\n\nconst processFn = (fn, opts) => function () {\n\tconst P = opts.promiseModule;\n\tconst args = new Array(arguments.length);\n\n\tfor (let i = 0; i < arguments.length; i++) {\n\t\targs[i] = arguments[i];\n\t}\n\n\treturn new P((resolve, reject) => {\n\t\tif (opts.errorFirst) {\n\t\t\targs.push(function (err, result) {\n\t\t\t\tif (opts.multiArgs) {\n\t\t\t\t\tconst results = new Array(arguments.length - 1);\n\n\t\t\t\t\tfor (let i = 1; i < arguments.length; i++) {\n\t\t\t\t\t\tresults[i - 1] = arguments[i];\n\t\t\t\t\t}\n\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\tresults.unshift(err);\n\t\t\t\t\t\treject(results);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresolve(results);\n\t\t\t\t\t}\n\t\t\t\t} else if (err) {\n\t\t\t\t\treject(err);\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\targs.push(function (result) {\n\t\t\t\tif (opts.multiArgs) {\n\t\t\t\t\tconst results = new Array(arguments.length - 1);\n\n\t\t\t\t\tfor (let i = 0; i < arguments.length; i++) {\n\t\t\t\t\t\tresults[i] = arguments[i];\n\t\t\t\t\t}\n\n\t\t\t\t\tresolve(results);\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tfn.apply(this, args);\n\t});\n};\n\nmodule.exports = (obj, opts) => {\n\topts = Object.assign({\n\t\texclude: [/.+(Sync|Stream)$/],\n\t\terrorFirst: true,\n\t\tpromiseModule: Promise\n\t}, opts);\n\n\tconst filter = key => {\n\t\tconst match = pattern => typeof pattern === 'string' ? key === pattern : pattern.test(key);\n\t\treturn opts.include ? opts.include.some(match) : !opts.exclude.some(match);\n\t};\n\n\tlet ret;\n\tif (typeof obj === 'function') {\n\t\tret = function () {\n\t\t\tif (opts.excludeMain) {\n\t\t\t\treturn obj.apply(this, arguments);\n\t\t\t}\n\n\t\t\treturn processFn(obj, opts).apply(this, arguments);\n\t\t};\n\t} else {\n\t\tret = Object.create(Object.getPrototypeOf(obj));\n\t}\n\n\tfor (const key in obj) { // eslint-disable-line guard-for-in\n\t\tconst x = obj[key];\n\t\tret[key] = typeof x === 'function' && filter(key) ? processFn(x, opts) : x;\n\t}\n\n\treturn ret;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/pify/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/comparator.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/classes/comparator.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst ANY = Symbol('SemVer ANY')\n// hoisted class for cyclic dependency\nclass Comparator {\n  static get ANY () {\n    return ANY\n  }\n\n  constructor (comp, options) {\n    options = parseOptions(options)\n\n    if (comp instanceof Comparator) {\n      if (comp.loose === !!options.loose) {\n        return comp\n      } else {\n        comp = comp.value\n      }\n    }\n\n    comp = comp.trim().split(/\\s+/).join(' ')\n    debug('comparator', comp, options)\n    this.options = options\n    this.loose = !!options.loose\n    this.parse(comp)\n\n    if (this.semver === ANY) {\n      this.value = ''\n    } else {\n      this.value = this.operator + this.semver.version\n    }\n\n    debug('comp', this)\n  }\n\n  parse (comp) {\n    const r = this.options.loose ? re[t.COMPARATORLOOSE] : re[t.COMPARATOR]\n    const m = comp.match(r)\n\n    if (!m) {\n      throw new TypeError(`Invalid comparator: ${comp}`)\n    }\n\n    this.operator = m[1] !== undefined ? m[1] : ''\n    if (this.operator === '=') {\n      this.operator = ''\n    }\n\n    // if it literally is just '>' or '' then allow anything.\n    if (!m[2]) {\n      this.semver = ANY\n    } else {\n      this.semver = new SemVer(m[2], this.options.loose)\n    }\n  }\n\n  toString () {\n    return this.value\n  }\n\n  test (version) {\n    debug('Comparator.test', version, this.options.loose)\n\n    if (this.semver === ANY || version === ANY) {\n      return true\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    return cmp(version, this.operator, this.semver, this.options)\n  }\n\n  intersects (comp, options) {\n    if (!(comp instanceof Comparator)) {\n      throw new TypeError('a Comparator is required')\n    }\n\n    if (this.operator === '') {\n      if (this.value === '') {\n        return true\n      }\n      return new Range(comp.value, options).test(this.value)\n    } else if (comp.operator === '') {\n      if (comp.value === '') {\n        return true\n      }\n      return new Range(this.value, options).test(comp.semver)\n    }\n\n    options = parseOptions(options)\n\n    // Special cases where nothing can possibly be lower\n    if (options.includePrerelease &&\n      (this.value === '<0.0.0-0' || comp.value === '<0.0.0-0')) {\n      return false\n    }\n    if (!options.includePrerelease &&\n      (this.value.startsWith('<0.0.0') || comp.value.startsWith('<0.0.0'))) {\n      return false\n    }\n\n    // Same direction increasing (> or >=)\n    if (this.operator.startsWith('>') && comp.operator.startsWith('>')) {\n      return true\n    }\n    // Same direction decreasing (< or <=)\n    if (this.operator.startsWith('<') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // same SemVer and both sides are inclusive (<= or >=)\n    if (\n      (this.semver.version === comp.semver.version) &&\n      this.operator.includes('=') && comp.operator.includes('=')) {\n      return true\n    }\n    // opposite directions less than\n    if (cmp(this.semver, '<', comp.semver, options) &&\n      this.operator.startsWith('>') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // opposite directions greater than\n    if (cmp(this.semver, '>', comp.semver, options) &&\n      this.operator.startsWith('<') && comp.operator.startsWith('>')) {\n      return true\n    }\n    return false\n  }\n}\n\nmodule.exports = Comparator\n\nconst parseOptions = __webpack_require__(/*! ../internal/parse-options */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/parse-options.js\")\nconst { safeRe: re, t } = __webpack_require__(/*! ../internal/re */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/re.js\")\nconst cmp = __webpack_require__(/*! ../functions/cmp */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/cmp.js\")\nconst debug = __webpack_require__(/*! ../internal/debug */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/debug.js\")\nconst SemVer = __webpack_require__(/*! ./semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst Range = __webpack_require__(/*! ./range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/comparator.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js":
/*!******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/classes/range.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SPACE_CHARACTERS = /\\s+/g\n\n// hoisted class for cyclic dependency\nclass Range {\n  constructor (range, options) {\n    options = parseOptions(options)\n\n    if (range instanceof Range) {\n      if (\n        range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease\n      ) {\n        return range\n      } else {\n        return new Range(range.raw, options)\n      }\n    }\n\n    if (range instanceof Comparator) {\n      // just put it in the set and return\n      this.raw = range.value\n      this.set = [[range]]\n      this.formatted = undefined\n      return this\n    }\n\n    this.options = options\n    this.loose = !!options.loose\n    this.includePrerelease = !!options.includePrerelease\n\n    // First reduce all whitespace as much as possible so we do not have to rely\n    // on potentially slow regexes like \\s*. This is then stored and used for\n    // future error messages as well.\n    this.raw = range.trim().replace(SPACE_CHARACTERS, ' ')\n\n    // First, split on ||\n    this.set = this.raw\n      .split('||')\n      // map the range to a 2d array of comparators\n      .map(r => this.parseRange(r.trim()))\n      // throw out any comparator lists that are empty\n      // this generally means that it was not a valid range, which is allowed\n      // in loose mode, but will still throw if the WHOLE range is invalid.\n      .filter(c => c.length)\n\n    if (!this.set.length) {\n      throw new TypeError(`Invalid SemVer Range: ${this.raw}`)\n    }\n\n    // if we have any that are not the null set, throw out null sets.\n    if (this.set.length > 1) {\n      // keep the first one, in case they're all null sets\n      const first = this.set[0]\n      this.set = this.set.filter(c => !isNullSet(c[0]))\n      if (this.set.length === 0) {\n        this.set = [first]\n      } else if (this.set.length > 1) {\n        // if we have any that are *, then the range is just *\n        for (const c of this.set) {\n          if (c.length === 1 && isAny(c[0])) {\n            this.set = [c]\n            break\n          }\n        }\n      }\n    }\n\n    this.formatted = undefined\n  }\n\n  get range () {\n    if (this.formatted === undefined) {\n      this.formatted = ''\n      for (let i = 0; i < this.set.length; i++) {\n        if (i > 0) {\n          this.formatted += '||'\n        }\n        const comps = this.set[i]\n        for (let k = 0; k < comps.length; k++) {\n          if (k > 0) {\n            this.formatted += ' '\n          }\n          this.formatted += comps[k].toString().trim()\n        }\n      }\n    }\n    return this.formatted\n  }\n\n  format () {\n    return this.range\n  }\n\n  toString () {\n    return this.range\n  }\n\n  parseRange (range) {\n    // memoize range parsing for performance.\n    // this is a very hot path, and fully deterministic.\n    const memoOpts =\n      (this.options.includePrerelease && FLAG_INCLUDE_PRERELEASE) |\n      (this.options.loose && FLAG_LOOSE)\n    const memoKey = memoOpts + ':' + range\n    const cached = cache.get(memoKey)\n    if (cached) {\n      return cached\n    }\n\n    const loose = this.options.loose\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    const hr = loose ? re[t.HYPHENRANGELOOSE] : re[t.HYPHENRANGE]\n    range = range.replace(hr, hyphenReplace(this.options.includePrerelease))\n    debug('hyphen replace', range)\n\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    range = range.replace(re[t.COMPARATORTRIM], comparatorTrimReplace)\n    debug('comparator trim', range)\n\n    // `~ 1.2.3` => `~1.2.3`\n    range = range.replace(re[t.TILDETRIM], tildeTrimReplace)\n    debug('tilde trim', range)\n\n    // `^ 1.2.3` => `^1.2.3`\n    range = range.replace(re[t.CARETTRIM], caretTrimReplace)\n    debug('caret trim', range)\n\n    // At this point, the range is completely trimmed and\n    // ready to be split into comparators.\n\n    let rangeList = range\n      .split(' ')\n      .map(comp => parseComparator(comp, this.options))\n      .join(' ')\n      .split(/\\s+/)\n      // >=0.0.0 is equivalent to *\n      .map(comp => replaceGTE0(comp, this.options))\n\n    if (loose) {\n      // in loose mode, throw out any that are not valid comparators\n      rangeList = rangeList.filter(comp => {\n        debug('loose invalid filter', comp, this.options)\n        return !!comp.match(re[t.COMPARATORLOOSE])\n      })\n    }\n    debug('range list', rangeList)\n\n    // if any comparators are the null set, then replace with JUST null set\n    // if more than one comparator, remove any * comparators\n    // also, don't include the same comparator more than once\n    const rangeMap = new Map()\n    const comparators = rangeList.map(comp => new Comparator(comp, this.options))\n    for (const comp of comparators) {\n      if (isNullSet(comp)) {\n        return [comp]\n      }\n      rangeMap.set(comp.value, comp)\n    }\n    if (rangeMap.size > 1 && rangeMap.has('')) {\n      rangeMap.delete('')\n    }\n\n    const result = [...rangeMap.values()]\n    cache.set(memoKey, result)\n    return result\n  }\n\n  intersects (range, options) {\n    if (!(range instanceof Range)) {\n      throw new TypeError('a Range is required')\n    }\n\n    return this.set.some((thisComparators) => {\n      return (\n        isSatisfiable(thisComparators, options) &&\n        range.set.some((rangeComparators) => {\n          return (\n            isSatisfiable(rangeComparators, options) &&\n            thisComparators.every((thisComparator) => {\n              return rangeComparators.every((rangeComparator) => {\n                return thisComparator.intersects(rangeComparator, options)\n              })\n            })\n          )\n        })\n      )\n    })\n  }\n\n  // if ANY of the sets match ALL of its comparators, then pass\n  test (version) {\n    if (!version) {\n      return false\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    for (let i = 0; i < this.set.length; i++) {\n      if (testSet(this.set[i], version, this.options)) {\n        return true\n      }\n    }\n    return false\n  }\n}\n\nmodule.exports = Range\n\nconst LRU = __webpack_require__(/*! ../internal/lrucache */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/lrucache.js\")\nconst cache = new LRU()\n\nconst parseOptions = __webpack_require__(/*! ../internal/parse-options */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/parse-options.js\")\nconst Comparator = __webpack_require__(/*! ./comparator */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/comparator.js\")\nconst debug = __webpack_require__(/*! ../internal/debug */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/debug.js\")\nconst SemVer = __webpack_require__(/*! ./semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst {\n  safeRe: re,\n  t,\n  comparatorTrimReplace,\n  tildeTrimReplace,\n  caretTrimReplace,\n} = __webpack_require__(/*! ../internal/re */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/re.js\")\nconst { FLAG_INCLUDE_PRERELEASE, FLAG_LOOSE } = __webpack_require__(/*! ../internal/constants */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/constants.js\")\n\nconst isNullSet = c => c.value === '<0.0.0-0'\nconst isAny = c => c.value === ''\n\n// take a set of comparators and determine whether there\n// exists a version which can satisfy it\nconst isSatisfiable = (comparators, options) => {\n  let result = true\n  const remainingComparators = comparators.slice()\n  let testComparator = remainingComparators.pop()\n\n  while (result && remainingComparators.length) {\n    result = remainingComparators.every((otherComparator) => {\n      return testComparator.intersects(otherComparator, options)\n    })\n\n    testComparator = remainingComparators.pop()\n  }\n\n  return result\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nconst parseComparator = (comp, options) => {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nconst isX = id => !id || id.toLowerCase() === 'x' || id === '*'\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n// ~0.0.1 --> >=0.0.1 <0.1.0-0\nconst replaceTildes = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceTilde(c, options))\n    .join(' ')\n}\n\nconst replaceTilde = (comp, options) => {\n  const r = options.loose ? re[t.TILDELOOSE] : re[t.TILDE]\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('tilde', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0 <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0-0\n      ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = `>=${M}.${m}.${p}-${pr\n      } <${M}.${+m + 1}.0-0`\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0-0\n      ret = `>=${M}.${m}.${p\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n// ^1.2.3 --> >=1.2.3 <2.0.0-0\n// ^1.2.0 --> >=1.2.0 <2.0.0-0\n// ^0.0.1 --> >=0.0.1 <0.0.2-0\n// ^0.1.0 --> >=0.1.0 <0.2.0-0\nconst replaceCarets = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceCaret(c, options))\n    .join(' ')\n}\n\nconst replaceCaret = (comp, options) => {\n  debug('caret', comp, options)\n  const r = options.loose ? re[t.CARETLOOSE] : re[t.CARET]\n  const z = options.includePrerelease ? '-0' : ''\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('caret', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`\n      } else {\n        ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p}-${pr\n        } <${+M + 1}.0.0-0`\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p\n        } <${+M + 1}.0.0-0`\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nconst replaceXRanges = (comp, options) => {\n  debug('replaceXRanges', comp, options)\n  return comp\n    .split(/\\s+/)\n    .map((c) => replaceXRange(c, options))\n    .join(' ')\n}\n\nconst replaceXRange = (comp, options) => {\n  comp = comp.trim()\n  const r = options.loose ? re[t.XRANGELOOSE] : re[t.XRANGE]\n  return comp.replace(r, (ret, gtlt, M, m, p, pr) => {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    const xM = isX(M)\n    const xm = xM || isX(m)\n    const xp = xm || isX(p)\n    const anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    // if we're including prereleases in the match, then we need\n    // to fix this to -0, the lowest possible prerelease value\n    pr = options.includePrerelease ? '-0' : ''\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0-0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      if (gtlt === '<') {\n        pr = '-0'\n      }\n\n      ret = `${gtlt + M}.${m}.${p}${pr}`\n    } else if (xm) {\n      ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`\n    } else if (xp) {\n      ret = `>=${M}.${m}.0${pr\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nconst replaceStars = (comp, options) => {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp\n    .trim()\n    .replace(re[t.STAR], '')\n}\n\nconst replaceGTE0 = (comp, options) => {\n  debug('replaceGTE0', comp, options)\n  return comp\n    .trim()\n    .replace(re[options.includePrerelease ? t.GTE0PRE : t.GTE0], '')\n}\n\n// This function is passed to string.replace(re[t.HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0-0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0-0\n// TODO build?\nconst hyphenReplace = incPr => ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr) => {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = `>=${fM}.0.0${incPr ? '-0' : ''}`\n  } else if (isX(fp)) {\n    from = `>=${fM}.${fm}.0${incPr ? '-0' : ''}`\n  } else if (fpr) {\n    from = `>=${from}`\n  } else {\n    from = `>=${from}${incPr ? '-0' : ''}`\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = `<${+tM + 1}.0.0-0`\n  } else if (isX(tp)) {\n    to = `<${tM}.${+tm + 1}.0-0`\n  } else if (tpr) {\n    to = `<=${tM}.${tm}.${tp}-${tpr}`\n  } else if (incPr) {\n    to = `<${tM}.${tm}.${+tp + 1}-0`\n  } else {\n    to = `<=${to}`\n  }\n\n  return `${from} ${to}`.trim()\n}\n\nconst testSet = (set, version, options) => {\n  for (let i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (let i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === Comparator.ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        const allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst debug = __webpack_require__(/*! ../internal/debug */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/debug.js\")\nconst { MAX_LENGTH, MAX_SAFE_INTEGER } = __webpack_require__(/*! ../internal/constants */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/constants.js\")\nconst { safeRe: re, t } = __webpack_require__(/*! ../internal/re */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/re.js\")\n\nconst parseOptions = __webpack_require__(/*! ../internal/parse-options */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/parse-options.js\")\nconst { compareIdentifiers } = __webpack_require__(/*! ../internal/identifiers */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/identifiers.js\")\nclass SemVer {\n  constructor (version, options) {\n    options = parseOptions(options)\n\n    if (version instanceof SemVer) {\n      if (version.loose === !!options.loose &&\n        version.includePrerelease === !!options.includePrerelease) {\n        return version\n      } else {\n        version = version.version\n      }\n    } else if (typeof version !== 'string') {\n      throw new TypeError(`Invalid version. Must be a string. Got type \"${typeof version}\".`)\n    }\n\n    if (version.length > MAX_LENGTH) {\n      throw new TypeError(\n        `version is longer than ${MAX_LENGTH} characters`\n      )\n    }\n\n    debug('SemVer', version, options)\n    this.options = options\n    this.loose = !!options.loose\n    // this isn't actually relevant for versions, but keep it so that we\n    // don't run into trouble passing this.options around.\n    this.includePrerelease = !!options.includePrerelease\n\n    const m = version.trim().match(options.loose ? re[t.LOOSE] : re[t.FULL])\n\n    if (!m) {\n      throw new TypeError(`Invalid Version: ${version}`)\n    }\n\n    this.raw = version\n\n    // these are actually numbers\n    this.major = +m[1]\n    this.minor = +m[2]\n    this.patch = +m[3]\n\n    if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n      throw new TypeError('Invalid major version')\n    }\n\n    if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n      throw new TypeError('Invalid minor version')\n    }\n\n    if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n      throw new TypeError('Invalid patch version')\n    }\n\n    // numberify any prerelease numeric ids\n    if (!m[4]) {\n      this.prerelease = []\n    } else {\n      this.prerelease = m[4].split('.').map((id) => {\n        if (/^[0-9]+$/.test(id)) {\n          const num = +id\n          if (num >= 0 && num < MAX_SAFE_INTEGER) {\n            return num\n          }\n        }\n        return id\n      })\n    }\n\n    this.build = m[5] ? m[5].split('.') : []\n    this.format()\n  }\n\n  format () {\n    this.version = `${this.major}.${this.minor}.${this.patch}`\n    if (this.prerelease.length) {\n      this.version += `-${this.prerelease.join('.')}`\n    }\n    return this.version\n  }\n\n  toString () {\n    return this.version\n  }\n\n  compare (other) {\n    debug('SemVer.compare', this.version, this.options, other)\n    if (!(other instanceof SemVer)) {\n      if (typeof other === 'string' && other === this.version) {\n        return 0\n      }\n      other = new SemVer(other, this.options)\n    }\n\n    if (other.version === this.version) {\n      return 0\n    }\n\n    return this.compareMain(other) || this.comparePre(other)\n  }\n\n  compareMain (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    return (\n      compareIdentifiers(this.major, other.major) ||\n      compareIdentifiers(this.minor, other.minor) ||\n      compareIdentifiers(this.patch, other.patch)\n    )\n  }\n\n  comparePre (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    // NOT having a prerelease is > having one\n    if (this.prerelease.length && !other.prerelease.length) {\n      return -1\n    } else if (!this.prerelease.length && other.prerelease.length) {\n      return 1\n    } else if (!this.prerelease.length && !other.prerelease.length) {\n      return 0\n    }\n\n    let i = 0\n    do {\n      const a = this.prerelease[i]\n      const b = other.prerelease[i]\n      debug('prerelease compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  compareBuild (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    let i = 0\n    do {\n      const a = this.build[i]\n      const b = other.build[i]\n      debug('build compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  // preminor will bump the version up to the next minor release, and immediately\n  // down to pre-release. premajor and prepatch work the same way.\n  inc (release, identifier, identifierBase) {\n    if (release.startsWith('pre')) {\n      if (!identifier && identifierBase === false) {\n        throw new Error('invalid increment argument: identifier is empty')\n      }\n      // Avoid an invalid semver results\n      if (identifier) {\n        const match = `-${identifier}`.match(this.options.loose ? re[t.PRERELEASELOOSE] : re[t.PRERELEASE])\n        if (!match || match[1] !== identifier) {\n          throw new Error(`invalid identifier: ${identifier}`)\n        }\n      }\n    }\n\n    switch (release) {\n      case 'premajor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor = 0\n        this.major++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'preminor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'prepatch':\n        // If this is already a prerelease, it will bump to the next version\n        // drop any prereleases that might already exist, since they are not\n        // relevant at this point.\n        this.prerelease.length = 0\n        this.inc('patch', identifier, identifierBase)\n        this.inc('pre', identifier, identifierBase)\n        break\n      // If the input is a non-prerelease version, this acts the same as\n      // prepatch.\n      case 'prerelease':\n        if (this.prerelease.length === 0) {\n          this.inc('patch', identifier, identifierBase)\n        }\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'release':\n        if (this.prerelease.length === 0) {\n          throw new Error(`version ${this.raw} is not a prerelease`)\n        }\n        this.prerelease.length = 0\n        break\n\n      case 'major':\n        // If this is a pre-major version, bump up to the same major version.\n        // Otherwise increment major.\n        // 1.0.0-5 bumps to 1.0.0\n        // 1.1.0 bumps to 2.0.0\n        if (\n          this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0\n        ) {\n          this.major++\n        }\n        this.minor = 0\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'minor':\n        // If this is a pre-minor version, bump up to the same minor version.\n        // Otherwise increment minor.\n        // 1.2.0-5 bumps to 1.2.0\n        // 1.2.1 bumps to 1.3.0\n        if (this.patch !== 0 || this.prerelease.length === 0) {\n          this.minor++\n        }\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'patch':\n        // If this is not a pre-release version, it will increment the patch.\n        // If it is a pre-release it will bump up to the same patch version.\n        // 1.2.0-5 patches to 1.2.0\n        // 1.2.0 patches to 1.2.1\n        if (this.prerelease.length === 0) {\n          this.patch++\n        }\n        this.prerelease = []\n        break\n      // This probably shouldn't be used publicly.\n      // 1.0.0 'pre' would become 1.0.0-0 which is the wrong direction.\n      case 'pre': {\n        const base = Number(identifierBase) ? 1 : 0\n\n        if (this.prerelease.length === 0) {\n          this.prerelease = [base]\n        } else {\n          let i = this.prerelease.length\n          while (--i >= 0) {\n            if (typeof this.prerelease[i] === 'number') {\n              this.prerelease[i]++\n              i = -2\n            }\n          }\n          if (i === -1) {\n            // didn't increment anything\n            if (identifier === this.prerelease.join('.') && identifierBase === false) {\n              throw new Error('invalid increment argument: identifier already exists')\n            }\n            this.prerelease.push(base)\n          }\n        }\n        if (identifier) {\n          // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n          // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n          let prerelease = [identifier, base]\n          if (identifierBase === false) {\n            prerelease = [identifier]\n          }\n          if (compareIdentifiers(this.prerelease[0], identifier) === 0) {\n            if (isNaN(this.prerelease[1])) {\n              this.prerelease = prerelease\n            }\n          } else {\n            this.prerelease = prerelease\n          }\n        }\n        break\n      }\n      default:\n        throw new Error(`invalid increment argument: ${release}`)\n    }\n    this.raw = this.format()\n    if (this.build.length) {\n      this.raw += `+${this.build.join('.')}`\n    }\n    return this\n  }\n}\n\nmodule.exports = SemVer\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/clean.js":
/*!********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/clean.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst parse = __webpack_require__(/*! ./parse */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js\")\nconst clean = (version, options) => {\n  const s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\nmodule.exports = clean\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2NsZWFuLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGNBQWMsbUJBQU8sQ0FBQywrRkFBUztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2NsZWFuLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBwYXJzZSA9IHJlcXVpcmUoJy4vcGFyc2UnKVxuY29uc3QgY2xlYW4gPSAodmVyc2lvbiwgb3B0aW9ucykgPT4ge1xuICBjb25zdCBzID0gcGFyc2UodmVyc2lvbi50cmltKCkucmVwbGFjZSgvXls9dl0rLywgJycpLCBvcHRpb25zKVxuICByZXR1cm4gcyA/IHMudmVyc2lvbiA6IG51bGxcbn1cbm1vZHVsZS5leHBvcnRzID0gY2xlYW5cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/clean.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/cmp.js":
/*!******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/cmp.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst eq = __webpack_require__(/*! ./eq */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/eq.js\")\nconst neq = __webpack_require__(/*! ./neq */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/neq.js\")\nconst gt = __webpack_require__(/*! ./gt */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gt.js\")\nconst gte = __webpack_require__(/*! ./gte */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gte.js\")\nconst lt = __webpack_require__(/*! ./lt */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lt.js\")\nconst lte = __webpack_require__(/*! ./lte */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lte.js\")\n\nconst cmp = (a, op, b, loose) => {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError(`Invalid operator: ${op}`)\n  }\n}\nmodule.exports = cmp\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/cmp.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/coerce.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/coerce.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst parse = __webpack_require__(/*! ./parse */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js\")\nconst { safeRe: re, t } = __webpack_require__(/*! ../internal/re */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/re.js\")\n\nconst coerce = (version, options) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version === 'number') {\n    version = String(version)\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  options = options || {}\n\n  let match = null\n  if (!options.rtl) {\n    match = version.match(options.includePrerelease ? re[t.COERCEFULL] : re[t.COERCE])\n  } else {\n    // Find the right-most coercible string that does not share\n    // a terminus with a more left-ward coercible string.\n    // Eg, '1.2.3.4' wants to coerce '2.3.4', not '3.4' or '4'\n    // With includePrerelease option set, '1.2.3.4-rc' wants to coerce '2.3.4-rc', not '2.3.4'\n    //\n    // Walk through the string checking with a /g regexp\n    // Manually set the index so as to pick up overlapping matches.\n    // Stop when we get a match that ends at the string end, since no\n    // coercible string can be more right-ward without the same terminus.\n    const coerceRtlRegex = options.includePrerelease ? re[t.COERCERTLFULL] : re[t.COERCERTL]\n    let next\n    while ((next = coerceRtlRegex.exec(version)) &&\n        (!match || match.index + match[0].length !== version.length)\n    ) {\n      if (!match ||\n            next.index + next[0].length !== match.index + match[0].length) {\n        match = next\n      }\n      coerceRtlRegex.lastIndex = next.index + next[1].length + next[2].length\n    }\n    // leave it in a clean state\n    coerceRtlRegex.lastIndex = -1\n  }\n\n  if (match === null) {\n    return null\n  }\n\n  const major = match[2]\n  const minor = match[3] || '0'\n  const patch = match[4] || '0'\n  const prerelease = options.includePrerelease && match[5] ? `-${match[5]}` : ''\n  const build = options.includePrerelease && match[6] ? `+${match[6]}` : ''\n\n  return parse(`${major}.${minor}.${patch}${prerelease}${build}`, options)\n}\nmodule.exports = coerce\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/coerce.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare-build.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/compare-build.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst compareBuild = (a, b, loose) => {\n  const versionA = new SemVer(a, loose)\n  const versionB = new SemVer(b, loose)\n  return versionA.compare(versionB) || versionA.compareBuild(versionB)\n}\nmodule.exports = compareBuild\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2NvbXBhcmUtYnVpbGQuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLHdHQUFtQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvY29tcGFyZS1idWlsZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgY29tcGFyZUJ1aWxkID0gKGEsIGIsIGxvb3NlKSA9PiB7XG4gIGNvbnN0IHZlcnNpb25BID0gbmV3IFNlbVZlcihhLCBsb29zZSlcbiAgY29uc3QgdmVyc2lvbkIgPSBuZXcgU2VtVmVyKGIsIGxvb3NlKVxuICByZXR1cm4gdmVyc2lvbkEuY29tcGFyZSh2ZXJzaW9uQikgfHwgdmVyc2lvbkEuY29tcGFyZUJ1aWxkKHZlcnNpb25CKVxufVxubW9kdWxlLmV4cG9ydHMgPSBjb21wYXJlQnVpbGRcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare-build.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare-loose.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/compare-loose.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst compareLoose = (a, b) => compare(a, b, true)\nmodule.exports = compareLoose\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2NvbXBhcmUtbG9vc2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZ0JBQWdCLG1CQUFPLENBQUMsbUdBQVc7QUFDbkM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9jb21wYXJlLWxvb3NlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBjb21wYXJlID0gcmVxdWlyZSgnLi9jb21wYXJlJylcbmNvbnN0IGNvbXBhcmVMb29zZSA9IChhLCBiKSA9PiBjb21wYXJlKGEsIGIsIHRydWUpXG5tb2R1bGUuZXhwb3J0cyA9IGNvbXBhcmVMb29zZVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare-loose.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst compare = (a, b, loose) =>\n  new SemVer(a, loose).compare(new SemVer(b, loose))\n\nmodule.exports = compare\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2NvbXBhcmUuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLHdHQUFtQjtBQUMxQztBQUNBOztBQUVBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2NvbXBhcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IFNlbVZlciA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvc2VtdmVyJylcbmNvbnN0IGNvbXBhcmUgPSAoYSwgYiwgbG9vc2UpID0+XG4gIG5ldyBTZW1WZXIoYSwgbG9vc2UpLmNvbXBhcmUobmV3IFNlbVZlcihiLCBsb29zZSkpXG5cbm1vZHVsZS5leHBvcnRzID0gY29tcGFyZVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/diff.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/diff.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst parse = __webpack_require__(/*! ./parse.js */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js\")\n\nconst diff = (version1, version2) => {\n  const v1 = parse(version1, null, true)\n  const v2 = parse(version2, null, true)\n  const comparison = v1.compare(v2)\n\n  if (comparison === 0) {\n    return null\n  }\n\n  const v1Higher = comparison > 0\n  const highVersion = v1Higher ? v1 : v2\n  const lowVersion = v1Higher ? v2 : v1\n  const highHasPre = !!highVersion.prerelease.length\n  const lowHasPre = !!lowVersion.prerelease.length\n\n  if (lowHasPre && !highHasPre) {\n    // Going from prerelease -> no prerelease requires some special casing\n\n    // If the low version has only a major, then it will always be a major\n    // Some examples:\n    // 1.0.0-1 -> 1.0.0\n    // 1.0.0-1 -> 1.1.1\n    // 1.0.0-1 -> 2.0.0\n    if (!lowVersion.patch && !lowVersion.minor) {\n      return 'major'\n    }\n\n    // If the main part has no difference\n    if (lowVersion.compareMain(highVersion) === 0) {\n      if (lowVersion.minor && !lowVersion.patch) {\n        return 'minor'\n      }\n      return 'patch'\n    }\n  }\n\n  // add the `pre` prefix if we are going to a prerelease version\n  const prefix = highHasPre ? 'pre' : ''\n\n  if (v1.major !== v2.major) {\n    return prefix + 'major'\n  }\n\n  if (v1.minor !== v2.minor) {\n    return prefix + 'minor'\n  }\n\n  if (v1.patch !== v2.patch) {\n    return prefix + 'patch'\n  }\n\n  // high and low are preleases\n  return 'prerelease'\n}\n\nmodule.exports = diff\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/diff.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/eq.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/eq.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst eq = (a, b, loose) => compare(a, b, loose) === 0\nmodule.exports = eq\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2VxLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLG1HQUFXO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvZXEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IGNvbXBhcmUgPSByZXF1aXJlKCcuL2NvbXBhcmUnKVxuY29uc3QgZXEgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpID09PSAwXG5tb2R1bGUuZXhwb3J0cyA9IGVxXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/eq.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gt.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/gt.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst gt = (a, b, loose) => compare(a, b, loose) > 0\nmodule.exports = gt\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2d0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLG1HQUFXO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvZ3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IGNvbXBhcmUgPSByZXF1aXJlKCcuL2NvbXBhcmUnKVxuY29uc3QgZ3QgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpID4gMFxubW9kdWxlLmV4cG9ydHMgPSBndFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gt.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gte.js":
/*!******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/gte.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst gte = (a, b, loose) => compare(a, b, loose) >= 0\nmodule.exports = gte\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2d0ZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixnQkFBZ0IsbUJBQU8sQ0FBQyxtR0FBVztBQUNuQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2d0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgY29tcGFyZSA9IHJlcXVpcmUoJy4vY29tcGFyZScpXG5jb25zdCBndGUgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpID49IDBcbm1vZHVsZS5leHBvcnRzID0gZ3RlXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gte.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/inc.js":
/*!******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/inc.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\n\nconst inc = (version, release, options, identifier, identifierBase) => {\n  if (typeof (options) === 'string') {\n    identifierBase = identifier\n    identifier = options\n    options = undefined\n  }\n\n  try {\n    return new SemVer(\n      version instanceof SemVer ? version.version : version,\n      options\n    ).inc(release, identifier, identifierBase).version\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = inc\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2luYy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixlQUFlLG1CQUFPLENBQUMsd0dBQW1COztBQUUxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9pbmMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IFNlbVZlciA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvc2VtdmVyJylcblxuY29uc3QgaW5jID0gKHZlcnNpb24sIHJlbGVhc2UsIG9wdGlvbnMsIGlkZW50aWZpZXIsIGlkZW50aWZpZXJCYXNlKSA9PiB7XG4gIGlmICh0eXBlb2YgKG9wdGlvbnMpID09PSAnc3RyaW5nJykge1xuICAgIGlkZW50aWZpZXJCYXNlID0gaWRlbnRpZmllclxuICAgIGlkZW50aWZpZXIgPSBvcHRpb25zXG4gICAgb3B0aW9ucyA9IHVuZGVmaW5lZFxuICB9XG5cbiAgdHJ5IHtcbiAgICByZXR1cm4gbmV3IFNlbVZlcihcbiAgICAgIHZlcnNpb24gaW5zdGFuY2VvZiBTZW1WZXIgPyB2ZXJzaW9uLnZlcnNpb24gOiB2ZXJzaW9uLFxuICAgICAgb3B0aW9uc1xuICAgICkuaW5jKHJlbGVhc2UsIGlkZW50aWZpZXIsIGlkZW50aWZpZXJCYXNlKS52ZXJzaW9uXG4gIH0gY2F0Y2ggKGVyKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBpbmNcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/inc.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lt.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/lt.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst lt = (a, b, loose) => compare(a, b, loose) < 0\nmodule.exports = lt\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2x0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLG1HQUFXO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvbHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IGNvbXBhcmUgPSByZXF1aXJlKCcuL2NvbXBhcmUnKVxuY29uc3QgbHQgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpIDwgMFxubW9kdWxlLmV4cG9ydHMgPSBsdFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lt.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lte.js":
/*!******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/lte.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst lte = (a, b, loose) => compare(a, b, loose) <= 0\nmodule.exports = lte\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2x0ZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixnQkFBZ0IsbUJBQU8sQ0FBQyxtR0FBVztBQUNuQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2x0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgY29tcGFyZSA9IHJlcXVpcmUoJy4vY29tcGFyZScpXG5jb25zdCBsdGUgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpIDw9IDBcbm1vZHVsZS5leHBvcnRzID0gbHRlXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lte.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/major.js":
/*!********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/major.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst major = (a, loose) => new SemVer(a, loose).major\nmodule.exports = major\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL21ham9yLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGVBQWUsbUJBQU8sQ0FBQyx3R0FBbUI7QUFDMUM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9tYWpvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgbWFqb3IgPSAoYSwgbG9vc2UpID0+IG5ldyBTZW1WZXIoYSwgbG9vc2UpLm1ham9yXG5tb2R1bGUuZXhwb3J0cyA9IG1ham9yXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/major.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/minor.js":
/*!********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/minor.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst minor = (a, loose) => new SemVer(a, loose).minor\nmodule.exports = minor\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL21pbm9yLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGVBQWUsbUJBQU8sQ0FBQyx3R0FBbUI7QUFDMUM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9taW5vci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgbWlub3IgPSAoYSwgbG9vc2UpID0+IG5ldyBTZW1WZXIoYSwgbG9vc2UpLm1pbm9yXG5tb2R1bGUuZXhwb3J0cyA9IG1pbm9yXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/minor.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/neq.js":
/*!******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/neq.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst neq = (a, b, loose) => compare(a, b, loose) !== 0\nmodule.exports = neq\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL25lcS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixnQkFBZ0IsbUJBQU8sQ0FBQyxtR0FBVztBQUNuQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL25lcS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgY29tcGFyZSA9IHJlcXVpcmUoJy4vY29tcGFyZScpXG5jb25zdCBuZXEgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpICE9PSAwXG5tb2R1bGUuZXhwb3J0cyA9IG5lcVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/neq.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js":
/*!********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst parse = (version, options, throwErrors = false) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    if (!throwErrors) {\n      return null\n    }\n    throw er\n  }\n}\n\nmodule.exports = parse\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3BhcnNlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGVBQWUsbUJBQU8sQ0FBQyx3R0FBbUI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9wYXJzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgcGFyc2UgPSAodmVyc2lvbiwgb3B0aW9ucywgdGhyb3dFcnJvcnMgPSBmYWxzZSkgPT4ge1xuICBpZiAodmVyc2lvbiBpbnN0YW5jZW9mIFNlbVZlcikge1xuICAgIHJldHVybiB2ZXJzaW9uXG4gIH1cbiAgdHJ5IHtcbiAgICByZXR1cm4gbmV3IFNlbVZlcih2ZXJzaW9uLCBvcHRpb25zKVxuICB9IGNhdGNoIChlcikge1xuICAgIGlmICghdGhyb3dFcnJvcnMpIHtcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICAgIHRocm93IGVyXG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBwYXJzZVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/patch.js":
/*!********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/patch.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst patch = (a, loose) => new SemVer(a, loose).patch\nmodule.exports = patch\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3BhdGNoLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGVBQWUsbUJBQU8sQ0FBQyx3R0FBbUI7QUFDMUM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9wYXRjaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgcGF0Y2ggPSAoYSwgbG9vc2UpID0+IG5ldyBTZW1WZXIoYSwgbG9vc2UpLnBhdGNoXG5tb2R1bGUuZXhwb3J0cyA9IHBhdGNoXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/patch.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/prerelease.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/prerelease.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst parse = __webpack_require__(/*! ./parse */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js\")\nconst prerelease = (version, options) => {\n  const parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\nmodule.exports = prerelease\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3ByZXJlbGVhc2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosY0FBYyxtQkFBTyxDQUFDLCtGQUFTO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcHJlcmVsZWFzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgcGFyc2UgPSByZXF1aXJlKCcuL3BhcnNlJylcbmNvbnN0IHByZXJlbGVhc2UgPSAodmVyc2lvbiwgb3B0aW9ucykgPT4ge1xuICBjb25zdCBwYXJzZWQgPSBwYXJzZSh2ZXJzaW9uLCBvcHRpb25zKVxuICByZXR1cm4gKHBhcnNlZCAmJiBwYXJzZWQucHJlcmVsZWFzZS5sZW5ndGgpID8gcGFyc2VkLnByZXJlbGVhc2UgOiBudWxsXG59XG5tb2R1bGUuZXhwb3J0cyA9IHByZXJlbGVhc2VcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/prerelease.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/rcompare.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/rcompare.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst rcompare = (a, b, loose) => compare(b, a, loose)\nmodule.exports = rcompare\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3Jjb21wYXJlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLG1HQUFXO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcmNvbXBhcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IGNvbXBhcmUgPSByZXF1aXJlKCcuL2NvbXBhcmUnKVxuY29uc3QgcmNvbXBhcmUgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYiwgYSwgbG9vc2UpXG5tb2R1bGUuZXhwb3J0cyA9IHJjb21wYXJlXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/rcompare.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/rsort.js":
/*!********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/rsort.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compareBuild = __webpack_require__(/*! ./compare-build */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare-build.js\")\nconst rsort = (list, loose) => list.sort((a, b) => compareBuild(b, a, loose))\nmodule.exports = rsort\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3Jzb3J0LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLHFCQUFxQixtQkFBTyxDQUFDLCtHQUFpQjtBQUM5QztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3Jzb3J0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBjb21wYXJlQnVpbGQgPSByZXF1aXJlKCcuL2NvbXBhcmUtYnVpbGQnKVxuY29uc3QgcnNvcnQgPSAobGlzdCwgbG9vc2UpID0+IGxpc3Quc29ydCgoYSwgYikgPT4gY29tcGFyZUJ1aWxkKGIsIGEsIGxvb3NlKSlcbm1vZHVsZS5leHBvcnRzID0gcnNvcnRcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/rsort.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/satisfies.js":
/*!************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/satisfies.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\nconst satisfies = (version, range, options) => {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\nmodule.exports = satisfies\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3NhdGlzZmllcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixjQUFjLG1CQUFPLENBQUMsc0dBQWtCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3NhdGlzZmllcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgUmFuZ2UgPSByZXF1aXJlKCcuLi9jbGFzc2VzL3JhbmdlJylcbmNvbnN0IHNhdGlzZmllcyA9ICh2ZXJzaW9uLCByYW5nZSwgb3B0aW9ucykgPT4ge1xuICB0cnkge1xuICAgIHJhbmdlID0gbmV3IFJhbmdlKHJhbmdlLCBvcHRpb25zKVxuICB9IGNhdGNoIChlcikge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG4gIHJldHVybiByYW5nZS50ZXN0KHZlcnNpb24pXG59XG5tb2R1bGUuZXhwb3J0cyA9IHNhdGlzZmllc1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/satisfies.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/sort.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/sort.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compareBuild = __webpack_require__(/*! ./compare-build */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare-build.js\")\nconst sort = (list, loose) => list.sort((a, b) => compareBuild(a, b, loose))\nmodule.exports = sort\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3NvcnQuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVoscUJBQXFCLG1CQUFPLENBQUMsK0dBQWlCO0FBQzlDO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvc29ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgY29tcGFyZUJ1aWxkID0gcmVxdWlyZSgnLi9jb21wYXJlLWJ1aWxkJylcbmNvbnN0IHNvcnQgPSAobGlzdCwgbG9vc2UpID0+IGxpc3Quc29ydCgoYSwgYikgPT4gY29tcGFyZUJ1aWxkKGEsIGIsIGxvb3NlKSlcbm1vZHVsZS5leHBvcnRzID0gc29ydFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/sort.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/valid.js":
/*!********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/functions/valid.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst parse = __webpack_require__(/*! ./parse */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js\")\nconst valid = (version, options) => {\n  const v = parse(version, options)\n  return v ? v.version : null\n}\nmodule.exports = valid\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3ZhbGlkLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGNBQWMsbUJBQU8sQ0FBQywrRkFBUztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3ZhbGlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBwYXJzZSA9IHJlcXVpcmUoJy4vcGFyc2UnKVxuY29uc3QgdmFsaWQgPSAodmVyc2lvbiwgb3B0aW9ucykgPT4ge1xuICBjb25zdCB2ID0gcGFyc2UodmVyc2lvbiwgb3B0aW9ucylcbiAgcmV0dXJuIHYgPyB2LnZlcnNpb24gOiBudWxsXG59XG5tb2R1bGUuZXhwb3J0cyA9IHZhbGlkXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/valid.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/index.js":
/*!**********************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/index.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// just pre-load all the stuff that index.js lazily exports\nconst internalRe = __webpack_require__(/*! ./internal/re */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/re.js\")\nconst constants = __webpack_require__(/*! ./internal/constants */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/constants.js\")\nconst SemVer = __webpack_require__(/*! ./classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst identifiers = __webpack_require__(/*! ./internal/identifiers */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/identifiers.js\")\nconst parse = __webpack_require__(/*! ./functions/parse */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/parse.js\")\nconst valid = __webpack_require__(/*! ./functions/valid */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/valid.js\")\nconst clean = __webpack_require__(/*! ./functions/clean */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/clean.js\")\nconst inc = __webpack_require__(/*! ./functions/inc */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/inc.js\")\nconst diff = __webpack_require__(/*! ./functions/diff */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/diff.js\")\nconst major = __webpack_require__(/*! ./functions/major */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/major.js\")\nconst minor = __webpack_require__(/*! ./functions/minor */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/minor.js\")\nconst patch = __webpack_require__(/*! ./functions/patch */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/patch.js\")\nconst prerelease = __webpack_require__(/*! ./functions/prerelease */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/prerelease.js\")\nconst compare = __webpack_require__(/*! ./functions/compare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nconst rcompare = __webpack_require__(/*! ./functions/rcompare */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/rcompare.js\")\nconst compareLoose = __webpack_require__(/*! ./functions/compare-loose */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare-loose.js\")\nconst compareBuild = __webpack_require__(/*! ./functions/compare-build */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare-build.js\")\nconst sort = __webpack_require__(/*! ./functions/sort */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/sort.js\")\nconst rsort = __webpack_require__(/*! ./functions/rsort */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/rsort.js\")\nconst gt = __webpack_require__(/*! ./functions/gt */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gt.js\")\nconst lt = __webpack_require__(/*! ./functions/lt */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lt.js\")\nconst eq = __webpack_require__(/*! ./functions/eq */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/eq.js\")\nconst neq = __webpack_require__(/*! ./functions/neq */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/neq.js\")\nconst gte = __webpack_require__(/*! ./functions/gte */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gte.js\")\nconst lte = __webpack_require__(/*! ./functions/lte */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lte.js\")\nconst cmp = __webpack_require__(/*! ./functions/cmp */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/cmp.js\")\nconst coerce = __webpack_require__(/*! ./functions/coerce */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/coerce.js\")\nconst Comparator = __webpack_require__(/*! ./classes/comparator */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/comparator.js\")\nconst Range = __webpack_require__(/*! ./classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\nconst satisfies = __webpack_require__(/*! ./functions/satisfies */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/satisfies.js\")\nconst toComparators = __webpack_require__(/*! ./ranges/to-comparators */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/to-comparators.js\")\nconst maxSatisfying = __webpack_require__(/*! ./ranges/max-satisfying */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/max-satisfying.js\")\nconst minSatisfying = __webpack_require__(/*! ./ranges/min-satisfying */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/min-satisfying.js\")\nconst minVersion = __webpack_require__(/*! ./ranges/min-version */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/min-version.js\")\nconst validRange = __webpack_require__(/*! ./ranges/valid */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/valid.js\")\nconst outside = __webpack_require__(/*! ./ranges/outside */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/outside.js\")\nconst gtr = __webpack_require__(/*! ./ranges/gtr */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/gtr.js\")\nconst ltr = __webpack_require__(/*! ./ranges/ltr */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/ltr.js\")\nconst intersects = __webpack_require__(/*! ./ranges/intersects */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/intersects.js\")\nconst simplifyRange = __webpack_require__(/*! ./ranges/simplify */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/simplify.js\")\nconst subset = __webpack_require__(/*! ./ranges/subset */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/subset.js\")\nmodule.exports = {\n  parse,\n  valid,\n  clean,\n  inc,\n  diff,\n  major,\n  minor,\n  patch,\n  prerelease,\n  compare,\n  rcompare,\n  compareLoose,\n  compareBuild,\n  sort,\n  rsort,\n  gt,\n  lt,\n  eq,\n  neq,\n  gte,\n  lte,\n  cmp,\n  coerce,\n  Comparator,\n  Range,\n  satisfies,\n  toComparators,\n  maxSatisfying,\n  minSatisfying,\n  minVersion,\n  validRange,\n  outside,\n  gtr,\n  ltr,\n  intersects,\n  simplifyRange,\n  subset,\n  SemVer,\n  re: internalRe.re,\n  src: internalRe.src,\n  tokens: internalRe.t,\n  SEMVER_SPEC_VERSION: constants.SEMVER_SPEC_VERSION,\n  RELEASE_TYPES: constants.RELEASE_TYPES,\n  compareIdentifiers: identifiers.compareIdentifiers,\n  rcompareIdentifiers: identifiers.rcompareIdentifiers,\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/constants.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/internal/constants.js ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nconst SEMVER_SPEC_VERSION = '2.0.0'\n\nconst MAX_LENGTH = 256\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n/* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nconst MAX_SAFE_COMPONENT_LENGTH = 16\n\n// Max safe length for a build identifier. The max length minus 6 characters for\n// the shortest version with a build 0.0.0+BUILD.\nconst MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\nconst RELEASE_TYPES = [\n  'major',\n  'premajor',\n  'minor',\n  'preminor',\n  'patch',\n  'prepatch',\n  'prerelease',\n]\n\nmodule.exports = {\n  MAX_LENGTH,\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_SAFE_INTEGER,\n  RELEASE_TYPES,\n  SEMVER_SPEC_VERSION,\n  FLAG_INCLUDE_PRERELEASE: 0b001,\n  FLAG_LOOSE: 0b010,\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG4vLyBOb3RlOiB0aGlzIGlzIHRoZSBzZW12ZXIub3JnIHZlcnNpb24gb2YgdGhlIHNwZWMgdGhhdCBpdCBpbXBsZW1lbnRzXG4vLyBOb3QgbmVjZXNzYXJpbHkgdGhlIHBhY2thZ2UgdmVyc2lvbiBvZiB0aGlzIGNvZGUuXG5jb25zdCBTRU1WRVJfU1BFQ19WRVJTSU9OID0gJzIuMC4wJ1xuXG5jb25zdCBNQVhfTEVOR1RIID0gMjU2XG5jb25zdCBNQVhfU0FGRV9JTlRFR0VSID0gTnVtYmVyLk1BWF9TQUZFX0lOVEVHRVIgfHxcbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovIDkwMDcxOTkyNTQ3NDA5OTFcblxuLy8gTWF4IHNhZmUgc2VnbWVudCBsZW5ndGggZm9yIGNvZXJjaW9uLlxuY29uc3QgTUFYX1NBRkVfQ09NUE9ORU5UX0xFTkdUSCA9IDE2XG5cbi8vIE1heCBzYWZlIGxlbmd0aCBmb3IgYSBidWlsZCBpZGVudGlmaWVyLiBUaGUgbWF4IGxlbmd0aCBtaW51cyA2IGNoYXJhY3RlcnMgZm9yXG4vLyB0aGUgc2hvcnRlc3QgdmVyc2lvbiB3aXRoIGEgYnVpbGQgMC4wLjArQlVJTEQuXG5jb25zdCBNQVhfU0FGRV9CVUlMRF9MRU5HVEggPSBNQVhfTEVOR1RIIC0gNlxuXG5jb25zdCBSRUxFQVNFX1RZUEVTID0gW1xuICAnbWFqb3InLFxuICAncHJlbWFqb3InLFxuICAnbWlub3InLFxuICAncHJlbWlub3InLFxuICAncGF0Y2gnLFxuICAncHJlcGF0Y2gnLFxuICAncHJlcmVsZWFzZScsXG5dXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBNQVhfTEVOR1RILFxuICBNQVhfU0FGRV9DT01QT05FTlRfTEVOR1RILFxuICBNQVhfU0FGRV9CVUlMRF9MRU5HVEgsXG4gIE1BWF9TQUZFX0lOVEVHRVIsXG4gIFJFTEVBU0VfVFlQRVMsXG4gIFNFTVZFUl9TUEVDX1ZFUlNJT04sXG4gIEZMQUdfSU5DTFVERV9QUkVSRUxFQVNFOiAwYjAwMSxcbiAgRkxBR19MT09TRTogMGIwMTAsXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/constants.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/debug.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/internal/debug.js ***!
  \*******************************************************************************/
/***/ ((module) => {

eval("\n\nconst debug = (\n  typeof process === 'object' &&\n  process.env &&\n  process.env.NODE_DEBUG &&\n  /\\bsemver\\b/i.test(process.env.NODE_DEBUG)\n) ? (...args) => console.error('SEMVER', ...args)\n  : () => {}\n\nmodule.exports = debug\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvZGVidWcuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9pbnRlcm5hbC9kZWJ1Zy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgZGVidWcgPSAoXG4gIHR5cGVvZiBwcm9jZXNzID09PSAnb2JqZWN0JyAmJlxuICBwcm9jZXNzLmVudiAmJlxuICBwcm9jZXNzLmVudi5OT0RFX0RFQlVHICYmXG4gIC9cXGJzZW12ZXJcXGIvaS50ZXN0KHByb2Nlc3MuZW52Lk5PREVfREVCVUcpXG4pID8gKC4uLmFyZ3MpID0+IGNvbnNvbGUuZXJyb3IoJ1NFTVZFUicsIC4uLmFyZ3MpXG4gIDogKCkgPT4ge31cblxubW9kdWxlLmV4cG9ydHMgPSBkZWJ1Z1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/debug.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/identifiers.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/internal/identifiers.js ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("\n\nconst numeric = /^[0-9]+$/\nconst compareIdentifiers = (a, b) => {\n  const anum = numeric.test(a)\n  const bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nconst rcompareIdentifiers = (a, b) => compareIdentifiers(b, a)\n\nmodule.exports = {\n  compareIdentifiers,\n  rcompareIdentifiers,\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvaWRlbnRpZmllcnMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvaWRlbnRpZmllcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IG51bWVyaWMgPSAvXlswLTldKyQvXG5jb25zdCBjb21wYXJlSWRlbnRpZmllcnMgPSAoYSwgYikgPT4ge1xuICBjb25zdCBhbnVtID0gbnVtZXJpYy50ZXN0KGEpXG4gIGNvbnN0IGJudW0gPSBudW1lcmljLnRlc3QoYilcblxuICBpZiAoYW51bSAmJiBibnVtKSB7XG4gICAgYSA9ICthXG4gICAgYiA9ICtiXG4gIH1cblxuICByZXR1cm4gYSA9PT0gYiA/IDBcbiAgICA6IChhbnVtICYmICFibnVtKSA/IC0xXG4gICAgOiAoYm51bSAmJiAhYW51bSkgPyAxXG4gICAgOiBhIDwgYiA/IC0xXG4gICAgOiAxXG59XG5cbmNvbnN0IHJjb21wYXJlSWRlbnRpZmllcnMgPSAoYSwgYikgPT4gY29tcGFyZUlkZW50aWZpZXJzKGIsIGEpXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBjb21wYXJlSWRlbnRpZmllcnMsXG4gIHJjb21wYXJlSWRlbnRpZmllcnMsXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/identifiers.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/lrucache.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/internal/lrucache.js ***!
  \**********************************************************************************/
/***/ ((module) => {

eval("\n\nclass LRUCache {\n  constructor () {\n    this.max = 1000\n    this.map = new Map()\n  }\n\n  get (key) {\n    const value = this.map.get(key)\n    if (value === undefined) {\n      return undefined\n    } else {\n      // Remove the key from the map and add it to the end\n      this.map.delete(key)\n      this.map.set(key, value)\n      return value\n    }\n  }\n\n  delete (key) {\n    return this.map.delete(key)\n  }\n\n  set (key, value) {\n    const deleted = this.delete(key)\n\n    if (!deleted && value !== undefined) {\n      // If cache is full, delete the least recently used item\n      if (this.map.size >= this.max) {\n        const firstKey = this.map.keys().next().value\n        this.delete(firstKey)\n      }\n\n      this.map.set(key, value)\n    }\n\n    return this\n  }\n}\n\nmodule.exports = LRUCache\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvbHJ1Y2FjaGUuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvbHJ1Y2FjaGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNsYXNzIExSVUNhY2hlIHtcbiAgY29uc3RydWN0b3IgKCkge1xuICAgIHRoaXMubWF4ID0gMTAwMFxuICAgIHRoaXMubWFwID0gbmV3IE1hcCgpXG4gIH1cblxuICBnZXQgKGtleSkge1xuICAgIGNvbnN0IHZhbHVlID0gdGhpcy5tYXAuZ2V0KGtleSlcbiAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgcmV0dXJuIHVuZGVmaW5lZFxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBSZW1vdmUgdGhlIGtleSBmcm9tIHRoZSBtYXAgYW5kIGFkZCBpdCB0byB0aGUgZW5kXG4gICAgICB0aGlzLm1hcC5kZWxldGUoa2V5KVxuICAgICAgdGhpcy5tYXAuc2V0KGtleSwgdmFsdWUpXG4gICAgICByZXR1cm4gdmFsdWVcbiAgICB9XG4gIH1cblxuICBkZWxldGUgKGtleSkge1xuICAgIHJldHVybiB0aGlzLm1hcC5kZWxldGUoa2V5KVxuICB9XG5cbiAgc2V0IChrZXksIHZhbHVlKSB7XG4gICAgY29uc3QgZGVsZXRlZCA9IHRoaXMuZGVsZXRlKGtleSlcblxuICAgIGlmICghZGVsZXRlZCAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAvLyBJZiBjYWNoZSBpcyBmdWxsLCBkZWxldGUgdGhlIGxlYXN0IHJlY2VudGx5IHVzZWQgaXRlbVxuICAgICAgaWYgKHRoaXMubWFwLnNpemUgPj0gdGhpcy5tYXgpIHtcbiAgICAgICAgY29uc3QgZmlyc3RLZXkgPSB0aGlzLm1hcC5rZXlzKCkubmV4dCgpLnZhbHVlXG4gICAgICAgIHRoaXMuZGVsZXRlKGZpcnN0S2V5KVxuICAgICAgfVxuXG4gICAgICB0aGlzLm1hcC5zZXQoa2V5LCB2YWx1ZSlcbiAgICB9XG5cbiAgICByZXR1cm4gdGhpc1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gTFJVQ2FjaGVcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/lrucache.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/parse-options.js":
/*!***************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/internal/parse-options.js ***!
  \***************************************************************************************/
/***/ ((module) => {

eval("\n\n// parse out just the options we care about\nconst looseOption = Object.freeze({ loose: true })\nconst emptyOpts = Object.freeze({ })\nconst parseOptions = options => {\n  if (!options) {\n    return emptyOpts\n  }\n\n  if (typeof options !== 'object') {\n    return looseOption\n  }\n\n  return options\n}\nmodule.exports = parseOptions\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvcGFyc2Utb3B0aW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBLG9DQUFvQyxhQUFhO0FBQ2pELG1DQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvcGFyc2Utb3B0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuLy8gcGFyc2Ugb3V0IGp1c3QgdGhlIG9wdGlvbnMgd2UgY2FyZSBhYm91dFxuY29uc3QgbG9vc2VPcHRpb24gPSBPYmplY3QuZnJlZXplKHsgbG9vc2U6IHRydWUgfSlcbmNvbnN0IGVtcHR5T3B0cyA9IE9iamVjdC5mcmVlemUoeyB9KVxuY29uc3QgcGFyc2VPcHRpb25zID0gb3B0aW9ucyA9PiB7XG4gIGlmICghb3B0aW9ucykge1xuICAgIHJldHVybiBlbXB0eU9wdHNcbiAgfVxuXG4gIGlmICh0eXBlb2Ygb3B0aW9ucyAhPT0gJ29iamVjdCcpIHtcbiAgICByZXR1cm4gbG9vc2VPcHRpb25cbiAgfVxuXG4gIHJldHVybiBvcHRpb25zXG59XG5tb2R1bGUuZXhwb3J0cyA9IHBhcnNlT3B0aW9uc1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/parse-options.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/re.js":
/*!****************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/internal/re.js ***!
  \****************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\n\nconst {\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_LENGTH,\n} = __webpack_require__(/*! ./constants */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/constants.js\")\nconst debug = __webpack_require__(/*! ./debug */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/debug.js\")\nexports = module.exports = {}\n\n// The actual regexps go on exports.re\nconst re = exports.re = []\nconst safeRe = exports.safeRe = []\nconst src = exports.src = []\nconst safeSrc = exports.safeSrc = []\nconst t = exports.t = {}\nlet R = 0\n\nconst LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nconst safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nconst makeSafeRegex = (value) => {\n  for (const [token, max] of safeRegexReplacements) {\n    value = value\n      .split(`${token}*`).join(`${token}{0,${max}}`)\n      .split(`${token}+`).join(`${token}{1,${max}}`)\n  }\n  return value\n}\n\nconst createToken = (name, value, isGlobal) => {\n  const safe = makeSafeRegex(value)\n  const index = R++\n  debug(name, index, value)\n  t[name] = index\n  src[index] = value\n  safeSrc[index] = safe\n  re[index] = new RegExp(value, isGlobal ? 'g' : undefined)\n  safeRe[index] = new RegExp(safe, isGlobal ? 'g' : undefined)\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\ncreateToken('NUMERICIDENTIFIER', '0|[1-9]\\\\d*')\ncreateToken('NUMERICIDENTIFIERLOOSE', '\\\\d+')\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\ncreateToken('NONNUMERICIDENTIFIER', `\\\\d*[a-zA-Z-]${LETTERDASHNUMBER}*`)\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\ncreateToken('MAINVERSION', `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('MAINVERSIONLOOSE', `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n// Non-numberic identifiers include numberic identifiers but can be longer.\n// Therefore non-numberic identifiers must go first.\n\ncreateToken('PRERELEASEIDENTIFIER', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('PRERELEASEIDENTIFIERLOOSE', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\ncreateToken('PRERELEASE', `(?:-(${src[t.PRERELEASEIDENTIFIER]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIER]})*))`)\n\ncreateToken('PRERELEASELOOSE', `(?:-?(${src[t.PRERELEASEIDENTIFIERLOOSE]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIERLOOSE]})*))`)\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\ncreateToken('BUILDIDENTIFIER', `${LETTERDASHNUMBER}+`)\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\ncreateToken('BUILD', `(?:\\\\+(${src[t.BUILDIDENTIFIER]\n}(?:\\\\.${src[t.BUILDIDENTIFIER]})*))`)\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\ncreateToken('FULLPLAIN', `v?${src[t.MAINVERSION]\n}${src[t.PRERELEASE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('FULL', `^${src[t.FULLPLAIN]}$`)\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\ncreateToken('LOOSEPLAIN', `[v=\\\\s]*${src[t.MAINVERSIONLOOSE]\n}${src[t.PRERELEASELOOSE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('LOOSE', `^${src[t.LOOSEPLAIN]}$`)\n\ncreateToken('GTLT', '((?:<|>)?=?)')\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\ncreateToken('XRANGEIDENTIFIERLOOSE', `${src[t.NUMERICIDENTIFIERLOOSE]}|x|X|\\\\*`)\ncreateToken('XRANGEIDENTIFIER', `${src[t.NUMERICIDENTIFIER]}|x|X|\\\\*`)\n\ncreateToken('XRANGEPLAIN', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:${src[t.PRERELEASE]})?${\n                     src[t.BUILD]}?` +\n                   `)?)?`)\n\ncreateToken('XRANGEPLAINLOOSE', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:${src[t.PRERELEASELOOSE]})?${\n                          src[t.BUILD]}?` +\n                        `)?)?`)\n\ncreateToken('XRANGE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAIN]}$`)\ncreateToken('XRANGELOOSE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\ncreateToken('COERCEPLAIN', `${'(^|[^\\\\d])' +\n              '(\\\\d{1,'}${MAX_SAFE_COMPONENT_LENGTH}})` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?`)\ncreateToken('COERCE', `${src[t.COERCEPLAIN]}(?:$|[^\\\\d])`)\ncreateToken('COERCEFULL', src[t.COERCEPLAIN] +\n              `(?:${src[t.PRERELEASE]})?` +\n              `(?:${src[t.BUILD]})?` +\n              `(?:$|[^\\\\d])`)\ncreateToken('COERCERTL', src[t.COERCE], true)\ncreateToken('COERCERTLFULL', src[t.COERCEFULL], true)\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\ncreateToken('LONETILDE', '(?:~>?)')\n\ncreateToken('TILDETRIM', `(\\\\s*)${src[t.LONETILDE]}\\\\s+`, true)\nexports.tildeTrimReplace = '$1~'\n\ncreateToken('TILDE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('TILDELOOSE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\ncreateToken('LONECARET', '(?:\\\\^)')\n\ncreateToken('CARETTRIM', `(\\\\s*)${src[t.LONECARET]}\\\\s+`, true)\nexports.caretTrimReplace = '$1^'\n\ncreateToken('CARET', `^${src[t.LONECARET]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('CARETLOOSE', `^${src[t.LONECARET]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\ncreateToken('COMPARATORLOOSE', `^${src[t.GTLT]}\\\\s*(${src[t.LOOSEPLAIN]})$|^$`)\ncreateToken('COMPARATOR', `^${src[t.GTLT]}\\\\s*(${src[t.FULLPLAIN]})$|^$`)\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\ncreateToken('COMPARATORTRIM', `(\\\\s*)${src[t.GTLT]\n}\\\\s*(${src[t.LOOSEPLAIN]}|${src[t.XRANGEPLAIN]})`, true)\nexports.comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\ncreateToken('HYPHENRANGE', `^\\\\s*(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s+-\\\\s+` +\n                   `(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s*$`)\n\ncreateToken('HYPHENRANGELOOSE', `^\\\\s*(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s+-\\\\s+` +\n                        `(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s*$`)\n\n// Star ranges basically just allow anything at all.\ncreateToken('STAR', '(<|>)?=?\\\\s*\\\\*')\n// >=0.0.0 is like a star\ncreateToken('GTE0', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0\\\\s*$')\ncreateToken('GTE0PRE', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0-0\\\\s*$')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/internal/re.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/gtr.js":
/*!***************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/gtr.js ***!
  \***************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Determine if version is greater than all the versions possible in the range.\nconst outside = __webpack_require__(/*! ./outside */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/outside.js\")\nconst gtr = (version, range, options) => outside(version, range, '>', options)\nmodule.exports = gtr\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL2d0ci5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBLGdCQUFnQixtQkFBTyxDQUFDLGdHQUFXO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvZ3RyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG4vLyBEZXRlcm1pbmUgaWYgdmVyc2lvbiBpcyBncmVhdGVyIHRoYW4gYWxsIHRoZSB2ZXJzaW9ucyBwb3NzaWJsZSBpbiB0aGUgcmFuZ2UuXG5jb25zdCBvdXRzaWRlID0gcmVxdWlyZSgnLi9vdXRzaWRlJylcbmNvbnN0IGd0ciA9ICh2ZXJzaW9uLCByYW5nZSwgb3B0aW9ucykgPT4gb3V0c2lkZSh2ZXJzaW9uLCByYW5nZSwgJz4nLCBvcHRpb25zKVxubW9kdWxlLmV4cG9ydHMgPSBndHJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/gtr.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/intersects.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/intersects.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\nconst intersects = (r1, r2, options) => {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2, options)\n}\nmodule.exports = intersects\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL2ludGVyc2VjdHMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosY0FBYyxtQkFBTyxDQUFDLHNHQUFrQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvaW50ZXJzZWN0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgUmFuZ2UgPSByZXF1aXJlKCcuLi9jbGFzc2VzL3JhbmdlJylcbmNvbnN0IGludGVyc2VjdHMgPSAocjEsIHIyLCBvcHRpb25zKSA9PiB7XG4gIHIxID0gbmV3IFJhbmdlKHIxLCBvcHRpb25zKVxuICByMiA9IG5ldyBSYW5nZShyMiwgb3B0aW9ucylcbiAgcmV0dXJuIHIxLmludGVyc2VjdHMocjIsIG9wdGlvbnMpXG59XG5tb2R1bGUuZXhwb3J0cyA9IGludGVyc2VjdHNcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/intersects.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/ltr.js":
/*!***************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/ltr.js ***!
  \***************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst outside = __webpack_require__(/*! ./outside */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/outside.js\")\n// Determine if version is less than all the versions possible in the range\nconst ltr = (version, range, options) => outside(version, range, '<', options)\nmodule.exports = ltr\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL2x0ci5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixnQkFBZ0IsbUJBQU8sQ0FBQyxnR0FBVztBQUNuQztBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvbHRyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBvdXRzaWRlID0gcmVxdWlyZSgnLi9vdXRzaWRlJylcbi8vIERldGVybWluZSBpZiB2ZXJzaW9uIGlzIGxlc3MgdGhhbiBhbGwgdGhlIHZlcnNpb25zIHBvc3NpYmxlIGluIHRoZSByYW5nZVxuY29uc3QgbHRyID0gKHZlcnNpb24sIHJhbmdlLCBvcHRpb25zKSA9PiBvdXRzaWRlKHZlcnNpb24sIHJhbmdlLCAnPCcsIG9wdGlvbnMpXG5tb2R1bGUuZXhwb3J0cyA9IGx0clxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/ltr.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/max-satisfying.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/max-satisfying.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\n\nconst maxSatisfying = (versions, range, options) => {\n  let max = null\n  let maxSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\nmodule.exports = maxSatisfying\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL21heC1zYXRpc2Z5aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGVBQWUsbUJBQU8sQ0FBQyx3R0FBbUI7QUFDMUMsY0FBYyxtQkFBTyxDQUFDLHNHQUFrQjs7QUFFeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL21heC1zYXRpc2Z5aW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBTZW1WZXIgPSByZXF1aXJlKCcuLi9jbGFzc2VzL3NlbXZlcicpXG5jb25zdCBSYW5nZSA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvcmFuZ2UnKVxuXG5jb25zdCBtYXhTYXRpc2Z5aW5nID0gKHZlcnNpb25zLCByYW5nZSwgb3B0aW9ucykgPT4ge1xuICBsZXQgbWF4ID0gbnVsbFxuICBsZXQgbWF4U1YgPSBudWxsXG4gIGxldCByYW5nZU9iaiA9IG51bGxcbiAgdHJ5IHtcbiAgICByYW5nZU9iaiA9IG5ldyBSYW5nZShyYW5nZSwgb3B0aW9ucylcbiAgfSBjYXRjaCAoZXIpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG4gIHZlcnNpb25zLmZvckVhY2goKHYpID0+IHtcbiAgICBpZiAocmFuZ2VPYmoudGVzdCh2KSkge1xuICAgICAgLy8gc2F0aXNmaWVzKHYsIHJhbmdlLCBvcHRpb25zKVxuICAgICAgaWYgKCFtYXggfHwgbWF4U1YuY29tcGFyZSh2KSA9PT0gLTEpIHtcbiAgICAgICAgLy8gY29tcGFyZShtYXgsIHYsIHRydWUpXG4gICAgICAgIG1heCA9IHZcbiAgICAgICAgbWF4U1YgPSBuZXcgU2VtVmVyKG1heCwgb3B0aW9ucylcbiAgICAgIH1cbiAgICB9XG4gIH0pXG4gIHJldHVybiBtYXhcbn1cbm1vZHVsZS5leHBvcnRzID0gbWF4U2F0aXNmeWluZ1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/max-satisfying.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/min-satisfying.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/min-satisfying.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\nconst minSatisfying = (versions, range, options) => {\n  let min = null\n  let minSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\nmodule.exports = minSatisfying\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL21pbi1zYXRpc2Z5aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGVBQWUsbUJBQU8sQ0FBQyx3R0FBbUI7QUFDMUMsY0FBYyxtQkFBTyxDQUFDLHNHQUFrQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qb2Uva2VpdGgvYWthcy9ub2RlX21vZHVsZXMvZXRoLWJsb2NrLXRyYWNrZXIvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvbWluLXNhdGlzZnlpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IFNlbVZlciA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvc2VtdmVyJylcbmNvbnN0IFJhbmdlID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9yYW5nZScpXG5jb25zdCBtaW5TYXRpc2Z5aW5nID0gKHZlcnNpb25zLCByYW5nZSwgb3B0aW9ucykgPT4ge1xuICBsZXQgbWluID0gbnVsbFxuICBsZXQgbWluU1YgPSBudWxsXG4gIGxldCByYW5nZU9iaiA9IG51bGxcbiAgdHJ5IHtcbiAgICByYW5nZU9iaiA9IG5ldyBSYW5nZShyYW5nZSwgb3B0aW9ucylcbiAgfSBjYXRjaCAoZXIpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG4gIHZlcnNpb25zLmZvckVhY2goKHYpID0+IHtcbiAgICBpZiAocmFuZ2VPYmoudGVzdCh2KSkge1xuICAgICAgLy8gc2F0aXNmaWVzKHYsIHJhbmdlLCBvcHRpb25zKVxuICAgICAgaWYgKCFtaW4gfHwgbWluU1YuY29tcGFyZSh2KSA9PT0gMSkge1xuICAgICAgICAvLyBjb21wYXJlKG1pbiwgdiwgdHJ1ZSlcbiAgICAgICAgbWluID0gdlxuICAgICAgICBtaW5TViA9IG5ldyBTZW1WZXIobWluLCBvcHRpb25zKVxuICAgICAgfVxuICAgIH1cbiAgfSlcbiAgcmV0dXJuIG1pblxufVxubW9kdWxlLmV4cG9ydHMgPSBtaW5TYXRpc2Z5aW5nXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/min-satisfying.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/min-version.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/min-version.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\nconst gt = __webpack_require__(/*! ../functions/gt */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gt.js\")\n\nconst minVersion = (range, loose) => {\n  range = new Range(range, loose)\n\n  let minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let setMin = null\n    comparators.forEach((comparator) => {\n      // Clone to avoid manipulating the comparator's semver object.\n      const compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!setMin || gt(compver, setMin)) {\n            setMin = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error(`Unexpected operation: ${comparator.operator}`)\n      }\n    })\n    if (setMin && (!minver || gt(minver, setMin))) {\n      minver = setMin\n    }\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\nmodule.exports = minVersion\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/min-version.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/outside.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/outside.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/semver.js\")\nconst Comparator = __webpack_require__(/*! ../classes/comparator */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/comparator.js\")\nconst { ANY } = Comparator\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\nconst satisfies = __webpack_require__(/*! ../functions/satisfies */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/satisfies.js\")\nconst gt = __webpack_require__(/*! ../functions/gt */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gt.js\")\nconst lt = __webpack_require__(/*! ../functions/lt */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lt.js\")\nconst lte = __webpack_require__(/*! ../functions/lte */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/lte.js\")\nconst gte = __webpack_require__(/*! ../functions/gte */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/gte.js\")\n\nconst outside = (version, range, hilo, options) => {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  let gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisfies the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let high = null\n    let low = null\n\n    comparators.forEach((comparator) => {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nmodule.exports = outside\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/outside.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/simplify.js":
/*!********************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/simplify.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// given a set of versions and a range, create a \"simplified\" range\n// that includes the same versions that the original range does\n// If the original range is shorter than the simplified one, return that.\nconst satisfies = __webpack_require__(/*! ../functions/satisfies.js */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/satisfies.js\")\nconst compare = __webpack_require__(/*! ../functions/compare.js */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\nmodule.exports = (versions, range, options) => {\n  const set = []\n  let first = null\n  let prev = null\n  const v = versions.sort((a, b) => compare(a, b, options))\n  for (const version of v) {\n    const included = satisfies(version, range, options)\n    if (included) {\n      prev = version\n      if (!first) {\n        first = version\n      }\n    } else {\n      if (prev) {\n        set.push([first, prev])\n      }\n      prev = null\n      first = null\n    }\n  }\n  if (first) {\n    set.push([first, null])\n  }\n\n  const ranges = []\n  for (const [min, max] of set) {\n    if (min === max) {\n      ranges.push(min)\n    } else if (!max && min === v[0]) {\n      ranges.push('*')\n    } else if (!max) {\n      ranges.push(`>=${min}`)\n    } else if (min === v[0]) {\n      ranges.push(`<=${max}`)\n    } else {\n      ranges.push(`${min} - ${max}`)\n    }\n  }\n  const simplified = ranges.join(' || ')\n  const original = typeof range.raw === 'string' ? range.raw : String(range)\n  return simplified.length < original.length ? simplified : range\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/simplify.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/subset.js":
/*!******************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/subset.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range.js */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\nconst Comparator = __webpack_require__(/*! ../classes/comparator.js */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/comparator.js\")\nconst { ANY } = Comparator\nconst satisfies = __webpack_require__(/*! ../functions/satisfies.js */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/satisfies.js\")\nconst compare = __webpack_require__(/*! ../functions/compare.js */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/functions/compare.js\")\n\n// Complex range `r1 || r2 || ...` is a subset of `R1 || R2 || ...` iff:\n// - Every simple range `r1, r2, ...` is a null set, OR\n// - Every simple range `r1, r2, ...` which is not a null set is a subset of\n//   some `R1, R2, ...`\n//\n// Simple range `c1 c2 ...` is a subset of simple range `C1 C2 ...` iff:\n// - If c is only the ANY comparator\n//   - If C is only the ANY comparator, return true\n//   - Else if in prerelease mode, return false\n//   - else replace c with `[>=0.0.0]`\n// - If C is only the ANY comparator\n//   - if in prerelease mode, return true\n//   - else replace C with `[>=0.0.0]`\n// - Let EQ be the set of = comparators in c\n// - If EQ is more than one, return true (null set)\n// - Let GT be the highest > or >= comparator in c\n// - Let LT be the lowest < or <= comparator in c\n// - If GT and LT, and GT.semver > LT.semver, return true (null set)\n// - If any C is a = range, and GT or LT are set, return false\n// - If EQ\n//   - If GT, and EQ does not satisfy GT, return true (null set)\n//   - If LT, and EQ does not satisfy LT, return true (null set)\n//   - If EQ satisfies every C, return true\n//   - Else return false\n// - If GT\n//   - If GT.semver is lower than any > or >= comp in C, return false\n//   - If GT is >=, and GT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the GT.semver tuple, return false\n// - If LT\n//   - If LT.semver is greater than any < or <= comp in C, return false\n//   - If LT is <=, and LT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the LT.semver tuple, return false\n// - Else return true\n\nconst subset = (sub, dom, options = {}) => {\n  if (sub === dom) {\n    return true\n  }\n\n  sub = new Range(sub, options)\n  dom = new Range(dom, options)\n  let sawNonNull = false\n\n  OUTER: for (const simpleSub of sub.set) {\n    for (const simpleDom of dom.set) {\n      const isSub = simpleSubset(simpleSub, simpleDom, options)\n      sawNonNull = sawNonNull || isSub !== null\n      if (isSub) {\n        continue OUTER\n      }\n    }\n    // the null set is a subset of everything, but null simple ranges in\n    // a complex range should be ignored.  so if we saw a non-null range,\n    // then we know this isn't a subset, but if EVERY simple range was null,\n    // then it is a subset.\n    if (sawNonNull) {\n      return false\n    }\n  }\n  return true\n}\n\nconst minimumVersionWithPreRelease = [new Comparator('>=0.0.0-0')]\nconst minimumVersion = [new Comparator('>=0.0.0')]\n\nconst simpleSubset = (sub, dom, options) => {\n  if (sub === dom) {\n    return true\n  }\n\n  if (sub.length === 1 && sub[0].semver === ANY) {\n    if (dom.length === 1 && dom[0].semver === ANY) {\n      return true\n    } else if (options.includePrerelease) {\n      sub = minimumVersionWithPreRelease\n    } else {\n      sub = minimumVersion\n    }\n  }\n\n  if (dom.length === 1 && dom[0].semver === ANY) {\n    if (options.includePrerelease) {\n      return true\n    } else {\n      dom = minimumVersion\n    }\n  }\n\n  const eqSet = new Set()\n  let gt, lt\n  for (const c of sub) {\n    if (c.operator === '>' || c.operator === '>=') {\n      gt = higherGT(gt, c, options)\n    } else if (c.operator === '<' || c.operator === '<=') {\n      lt = lowerLT(lt, c, options)\n    } else {\n      eqSet.add(c.semver)\n    }\n  }\n\n  if (eqSet.size > 1) {\n    return null\n  }\n\n  let gtltComp\n  if (gt && lt) {\n    gtltComp = compare(gt.semver, lt.semver, options)\n    if (gtltComp > 0) {\n      return null\n    } else if (gtltComp === 0 && (gt.operator !== '>=' || lt.operator !== '<=')) {\n      return null\n    }\n  }\n\n  // will iterate one or zero times\n  for (const eq of eqSet) {\n    if (gt && !satisfies(eq, String(gt), options)) {\n      return null\n    }\n\n    if (lt && !satisfies(eq, String(lt), options)) {\n      return null\n    }\n\n    for (const c of dom) {\n      if (!satisfies(eq, String(c), options)) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  let higher, lower\n  let hasDomLT, hasDomGT\n  // if the subset has a prerelease, we need a comparator in the superset\n  // with the same tuple and a prerelease, or it's not a subset\n  let needDomLTPre = lt &&\n    !options.includePrerelease &&\n    lt.semver.prerelease.length ? lt.semver : false\n  let needDomGTPre = gt &&\n    !options.includePrerelease &&\n    gt.semver.prerelease.length ? gt.semver : false\n  // exception: <1.2.3-0 is the same as <1.2.3\n  if (needDomLTPre && needDomLTPre.prerelease.length === 1 &&\n      lt.operator === '<' && needDomLTPre.prerelease[0] === 0) {\n    needDomLTPre = false\n  }\n\n  for (const c of dom) {\n    hasDomGT = hasDomGT || c.operator === '>' || c.operator === '>='\n    hasDomLT = hasDomLT || c.operator === '<' || c.operator === '<='\n    if (gt) {\n      if (needDomGTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomGTPre.major &&\n            c.semver.minor === needDomGTPre.minor &&\n            c.semver.patch === needDomGTPre.patch) {\n          needDomGTPre = false\n        }\n      }\n      if (c.operator === '>' || c.operator === '>=') {\n        higher = higherGT(gt, c, options)\n        if (higher === c && higher !== gt) {\n          return false\n        }\n      } else if (gt.operator === '>=' && !satisfies(gt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (lt) {\n      if (needDomLTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomLTPre.major &&\n            c.semver.minor === needDomLTPre.minor &&\n            c.semver.patch === needDomLTPre.patch) {\n          needDomLTPre = false\n        }\n      }\n      if (c.operator === '<' || c.operator === '<=') {\n        lower = lowerLT(lt, c, options)\n        if (lower === c && lower !== lt) {\n          return false\n        }\n      } else if (lt.operator === '<=' && !satisfies(lt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (!c.operator && (lt || gt) && gtltComp !== 0) {\n      return false\n    }\n  }\n\n  // if there was a < or >, and nothing in the dom, then must be false\n  // UNLESS it was limited by another range in the other direction.\n  // Eg, >1.0.0 <1.0.1 is still a subset of <2.0.0\n  if (gt && hasDomLT && !lt && gtltComp !== 0) {\n    return false\n  }\n\n  if (lt && hasDomGT && !gt && gtltComp !== 0) {\n    return false\n  }\n\n  // we needed a prerelease range in a specific tuple, but didn't get one\n  // then this isn't a subset.  eg >=1.2.3-pre is not a subset of >=1.0.0,\n  // because it includes prereleases in the 1.2.3 tuple\n  if (needDomGTPre || needDomLTPre) {\n    return false\n  }\n\n  return true\n}\n\n// >=1.2.3 is lower than >1.2.3\nconst higherGT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp > 0 ? a\n    : comp < 0 ? b\n    : b.operator === '>' && a.operator === '>=' ? b\n    : a\n}\n\n// <=1.2.3 is higher than <1.2.3\nconst lowerLT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp < 0 ? a\n    : comp > 0 ? b\n    : b.operator === '<' && a.operator === '<=' ? b\n    : a\n}\n\nmodule.exports = subset\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/subset.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/to-comparators.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/to-comparators.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\n\n// Mostly just for testing and legacy API reasons\nconst toComparators = (range, options) =>\n  new Range(range, options).set\n    .map(comp => comp.map(c => c.value).join(' ').trim().split(' '))\n\nmodule.exports = toComparators\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL3RvLWNvbXBhcmF0b3JzLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGNBQWMsbUJBQU8sQ0FBQyxzR0FBa0I7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL3RvLWNvbXBhcmF0b3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBSYW5nZSA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvcmFuZ2UnKVxuXG4vLyBNb3N0bHkganVzdCBmb3IgdGVzdGluZyBhbmQgbGVnYWN5IEFQSSByZWFzb25zXG5jb25zdCB0b0NvbXBhcmF0b3JzID0gKHJhbmdlLCBvcHRpb25zKSA9PlxuICBuZXcgUmFuZ2UocmFuZ2UsIG9wdGlvbnMpLnNldFxuICAgIC5tYXAoY29tcCA9PiBjb21wLm1hcChjID0+IGMudmFsdWUpLmpvaW4oJyAnKS50cmltKCkuc3BsaXQoJyAnKSlcblxubW9kdWxlLmV4cG9ydHMgPSB0b0NvbXBhcmF0b3JzXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/to-comparators.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/valid.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/eth-block-tracker/node_modules/semver/ranges/valid.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/../node_modules/eth-block-tracker/node_modules/semver/classes/range.js\")\nconst validRange = (range, options) => {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = validRange\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1ibG9jay10cmFja2VyL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL3ZhbGlkLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGNBQWMsbUJBQU8sQ0FBQyxzR0FBa0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtYmxvY2stdHJhY2tlci9ub2RlX21vZHVsZXMvc2VtdmVyL3Jhbmdlcy92YWxpZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgUmFuZ2UgPSByZXF1aXJlKCcuLi9jbGFzc2VzL3JhbmdlJylcbmNvbnN0IHZhbGlkUmFuZ2UgPSAocmFuZ2UsIG9wdGlvbnMpID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBSZXR1cm4gJyonIGluc3RlYWQgb2YgJycgc28gdGhhdCB0cnV0aGluZXNzIHdvcmtzLlxuICAgIC8vIFRoaXMgd2lsbCB0aHJvdyBpZiBpdCdzIGludmFsaWQgYW55d2F5XG4gICAgcmV0dXJuIG5ldyBSYW5nZShyYW5nZSwgb3B0aW9ucykucmFuZ2UgfHwgJyonXG4gIH0gY2F0Y2ggKGVyKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSB2YWxpZFJhbmdlXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-block-tracker/node_modules/semver/ranges/valid.js\n");

/***/ })

};
;