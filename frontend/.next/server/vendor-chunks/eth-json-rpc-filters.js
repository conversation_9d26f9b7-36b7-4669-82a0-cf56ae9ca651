/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eth-json-rpc-filters";
exports.ids = ["vendor-chunks/eth-json-rpc-filters"];
exports.modules = {

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/base-filter-history.js":
/*!*******************************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/base-filter-history.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/../node_modules/eth-json-rpc-filters/base-filter.js\")\n\n// tracks all results ever recorded\nclass BaseFilterWithHistory extends BaseFilter {\n\n  constructor () {\n    super()\n    this.allResults = []\n  }\n\n  async update () {\n    throw new Error('BaseFilterWithHistory - no update method specified')\n  }\n\n  addResults (newResults) {\n    this.allResults = this.allResults.concat(newResults)\n    super.addResults(newResults)\n  }\n\n  addInitialResults (newResults) {\n    this.allResults = this.allResults.concat(newResults)\n    super.addInitialResults(newResults)\n  }\n\n  getAllResults () {\n    return this.allResults\n  }\n\n}\n\nmodule.exports = BaseFilterWithHistory//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1qc29uLXJwYy1maWx0ZXJzL2Jhc2UtZmlsdGVyLWhpc3RvcnkuanMiLCJtYXBwaW5ncyI6IkFBQUEsbUJBQW1CLG1CQUFPLENBQUMsZ0ZBQWU7O0FBRTFDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtanNvbi1ycGMtZmlsdGVycy9iYXNlLWZpbHRlci1oaXN0b3J5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEJhc2VGaWx0ZXIgPSByZXF1aXJlKCcuL2Jhc2UtZmlsdGVyJylcblxuLy8gdHJhY2tzIGFsbCByZXN1bHRzIGV2ZXIgcmVjb3JkZWRcbmNsYXNzIEJhc2VGaWx0ZXJXaXRoSGlzdG9yeSBleHRlbmRzIEJhc2VGaWx0ZXIge1xuXG4gIGNvbnN0cnVjdG9yICgpIHtcbiAgICBzdXBlcigpXG4gICAgdGhpcy5hbGxSZXN1bHRzID0gW11cbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZSAoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdCYXNlRmlsdGVyV2l0aEhpc3RvcnkgLSBubyB1cGRhdGUgbWV0aG9kIHNwZWNpZmllZCcpXG4gIH1cblxuICBhZGRSZXN1bHRzIChuZXdSZXN1bHRzKSB7XG4gICAgdGhpcy5hbGxSZXN1bHRzID0gdGhpcy5hbGxSZXN1bHRzLmNvbmNhdChuZXdSZXN1bHRzKVxuICAgIHN1cGVyLmFkZFJlc3VsdHMobmV3UmVzdWx0cylcbiAgfVxuXG4gIGFkZEluaXRpYWxSZXN1bHRzIChuZXdSZXN1bHRzKSB7XG4gICAgdGhpcy5hbGxSZXN1bHRzID0gdGhpcy5hbGxSZXN1bHRzLmNvbmNhdChuZXdSZXN1bHRzKVxuICAgIHN1cGVyLmFkZEluaXRpYWxSZXN1bHRzKG5ld1Jlc3VsdHMpXG4gIH1cblxuICBnZXRBbGxSZXN1bHRzICgpIHtcbiAgICByZXR1cm4gdGhpcy5hbGxSZXN1bHRzXG4gIH1cblxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IEJhc2VGaWx0ZXJXaXRoSGlzdG9yeSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/base-filter-history.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/base-filter.js":
/*!***********************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/base-filter.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const SafeEventEmitter = (__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/../node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\")[\"default\"])\n\nclass BaseFilter extends SafeEventEmitter {\n\n  constructor () {\n    super()\n    this.updates = []\n  }\n\n  async initialize () {}\n\n  async update () {\n    throw new Error('BaseFilter - no update method specified')\n  }\n\n  addResults (newResults) {\n    this.updates = this.updates.concat(newResults)\n    newResults.forEach(result => this.emit('update', result))\n  }\n\n  addInitialResults (newResults) {}\n\n  getChangesAndClear () {\n    const updates = this.updates\n    this.updates = []\n    return updates\n  }\n  \n}\n\nmodule.exports = BaseFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1qc29uLXJwYy1maWx0ZXJzL2Jhc2UtZmlsdGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlCQUF5Qiw0SUFBK0M7O0FBRXhFOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtanNvbi1ycGMtZmlsdGVycy9iYXNlLWZpbHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTYWZlRXZlbnRFbWl0dGVyID0gcmVxdWlyZSgnQG1ldGFtYXNrL3NhZmUtZXZlbnQtZW1pdHRlcicpLmRlZmF1bHRcblxuY2xhc3MgQmFzZUZpbHRlciBleHRlbmRzIFNhZmVFdmVudEVtaXR0ZXIge1xuXG4gIGNvbnN0cnVjdG9yICgpIHtcbiAgICBzdXBlcigpXG4gICAgdGhpcy51cGRhdGVzID0gW11cbiAgfVxuXG4gIGFzeW5jIGluaXRpYWxpemUgKCkge31cblxuICBhc3luYyB1cGRhdGUgKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignQmFzZUZpbHRlciAtIG5vIHVwZGF0ZSBtZXRob2Qgc3BlY2lmaWVkJylcbiAgfVxuXG4gIGFkZFJlc3VsdHMgKG5ld1Jlc3VsdHMpIHtcbiAgICB0aGlzLnVwZGF0ZXMgPSB0aGlzLnVwZGF0ZXMuY29uY2F0KG5ld1Jlc3VsdHMpXG4gICAgbmV3UmVzdWx0cy5mb3JFYWNoKHJlc3VsdCA9PiB0aGlzLmVtaXQoJ3VwZGF0ZScsIHJlc3VsdCkpXG4gIH1cblxuICBhZGRJbml0aWFsUmVzdWx0cyAobmV3UmVzdWx0cykge31cblxuICBnZXRDaGFuZ2VzQW5kQ2xlYXIgKCkge1xuICAgIGNvbnN0IHVwZGF0ZXMgPSB0aGlzLnVwZGF0ZXNcbiAgICB0aGlzLnVwZGF0ZXMgPSBbXVxuICAgIHJldHVybiB1cGRhdGVzXG4gIH1cbiAgXG59XG5cbm1vZHVsZS5leHBvcnRzID0gQmFzZUZpbHRlclxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/base-filter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/block-filter.js":
/*!************************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/block-filter.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/../node_modules/eth-json-rpc-filters/base-filter.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange */ \"(ssr)/../node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\nconst { incrementHexInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/../node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass BlockFilter extends BaseFilter {\n\n  constructor ({ provider, params }) {\n    super()\n    this.type = 'block'\n    this.provider = provider\n  }\n\n  async update ({ oldBlock, newBlock }) {\n    const toBlock = newBlock\n    const fromBlock = incrementHexInt(oldBlock)\n    const blockBodies = await getBlocksForRange({ provider: this.provider, fromBlock, toBlock })\n    const blockHashes = blockBodies.map((block) => block.hash)\n    this.addResults(blockHashes)\n  }\n\n}\n\nmodule.exports = BlockFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1qc29uLXJwYy1maWx0ZXJzL2Jsb2NrLWZpbHRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxtQkFBbUIsbUJBQU8sQ0FBQyxnRkFBZTtBQUMxQywwQkFBMEIsbUJBQU8sQ0FBQyw0RkFBcUI7QUFDdkQsUUFBUSxrQkFBa0IsRUFBRSxtQkFBTyxDQUFDLDBFQUFZOztBQUVoRDs7QUFFQSxpQkFBaUIsa0JBQWtCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixvQkFBb0I7QUFDdEM7QUFDQTtBQUNBLGtEQUFrRCw2Q0FBNkM7QUFDL0Y7QUFDQTtBQUNBOztBQUVBOztBQUVBIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvbm9kZV9tb2R1bGVzL2V0aC1qc29uLXJwYy1maWx0ZXJzL2Jsb2NrLWZpbHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBCYXNlRmlsdGVyID0gcmVxdWlyZSgnLi9iYXNlLWZpbHRlcicpXG5jb25zdCBnZXRCbG9ja3NGb3JSYW5nZSA9IHJlcXVpcmUoJy4vZ2V0QmxvY2tzRm9yUmFuZ2UnKVxuY29uc3QgeyBpbmNyZW1lbnRIZXhJbnQgfSA9IHJlcXVpcmUoJy4vaGV4VXRpbHMnKVxuXG5jbGFzcyBCbG9ja0ZpbHRlciBleHRlbmRzIEJhc2VGaWx0ZXIge1xuXG4gIGNvbnN0cnVjdG9yICh7IHByb3ZpZGVyLCBwYXJhbXMgfSkge1xuICAgIHN1cGVyKClcbiAgICB0aGlzLnR5cGUgPSAnYmxvY2snXG4gICAgdGhpcy5wcm92aWRlciA9IHByb3ZpZGVyXG4gIH1cblxuICBhc3luYyB1cGRhdGUgKHsgb2xkQmxvY2ssIG5ld0Jsb2NrIH0pIHtcbiAgICBjb25zdCB0b0Jsb2NrID0gbmV3QmxvY2tcbiAgICBjb25zdCBmcm9tQmxvY2sgPSBpbmNyZW1lbnRIZXhJbnQob2xkQmxvY2spXG4gICAgY29uc3QgYmxvY2tCb2RpZXMgPSBhd2FpdCBnZXRCbG9ja3NGb3JSYW5nZSh7IHByb3ZpZGVyOiB0aGlzLnByb3ZpZGVyLCBmcm9tQmxvY2ssIHRvQmxvY2sgfSlcbiAgICBjb25zdCBibG9ja0hhc2hlcyA9IGJsb2NrQm9kaWVzLm1hcCgoYmxvY2spID0+IGJsb2NrLmhhc2gpXG4gICAgdGhpcy5hZGRSZXN1bHRzKGJsb2NrSGFzaGVzKVxuICB9XG5cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBCbG9ja0ZpbHRlclxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/block-filter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/getBlocksForRange.js":
/*!*****************************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/getBlocksForRange.js ***!
  \*****************************************************************/
/***/ ((module) => {

eval("module.exports = getBlocksForRange\n\nasync function getBlocksForRange({ provider, fromBlock, toBlock }) {\n  if (!fromBlock) fromBlock = toBlock\n\n  const fromBlockNumber = hexToInt(fromBlock)\n  const toBlockNumber = hexToInt(toBlock)\n  const blockCountToQuery = toBlockNumber - fromBlockNumber + 1\n  // load all blocks from old to new (inclusive)\n  const missingBlockNumbers = Array(blockCountToQuery).fill()\n                              .map((_,index) => fromBlockNumber + index)\n                              .map(intToHex)\n  let blockBodies = await Promise.all(\n    missingBlockNumbers.map(blockNum => query(provider, 'eth_getBlockByNumber', [blockNum, false]))\n  )\n  blockBodies = blockBodies.filter(block => block !== null);\n  return blockBodies\n}\n\nfunction hexToInt(hexString) {\n  if (hexString === undefined || hexString === null) return hexString\n  return Number.parseInt(hexString, 16)\n}\n\nfunction incrementHexInt(hexString){\n  if (hexString === undefined || hexString === null) return hexString\n  const value = hexToInt(hexString)\n  return intToHex(value + 1)\n}\n\nfunction intToHex(int) {\n  if (int === undefined || int === null) return int\n  const hexString = int.toString(16)\n  return '0x' + hexString\n}\n\nfunction sendAsync(provider, request) {\n  return new Promise((resolve, reject) => {\n    provider.sendAsync(request, (error, response) => {\n      if (error) {\n        reject(error);\n      } else if (response.error) {\n        reject(response.error);\n      } else if (response.result) {\n        resolve(response.result);\n      } else {\n        reject(new Error(\"Result was empty\"));\n      }\n    });\n  });\n}\n\nasync function query(provider, method, params) {\n  for (let i = 0; i < 3; i++) {\n    try {\n      return await sendAsync(provider, {\n        id: 1,\n        jsonrpc: \"2.0\",\n        method,\n        params,\n      });\n    } catch (error) {\n      console.error(\n        `provider.sendAsync failed: ${error.stack || error.message || error}`\n      );\n    }\n  }\n  return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/getBlocksForRange.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/hexUtils.js":
/*!********************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/hexUtils.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n  minBlockRef,\n  maxBlockRef,\n  sortBlockRefs,\n  bnToHex,\n  blockRefIsNumber,\n  hexToInt,\n  incrementHexInt,\n  intToHex,\n  unsafeRandomBytes,\n}\n\nfunction minBlockRef(...refs) {\n  const sortedRefs = sortBlockRefs(refs)\n  return sortedRefs[0]\n}\n\nfunction maxBlockRef(...refs) {\n  const sortedRefs = sortBlockRefs(refs)\n  return sortedRefs[sortedRefs.length-1]\n}\n\nfunction sortBlockRefs(refs) {\n  return refs.sort((refA, refB) => {\n    if (refA === 'latest' || refB === 'earliest') return 1\n    if (refB === 'latest' || refA === 'earliest') return -1\n    return hexToInt(refA) - hexToInt(refB)\n  })\n}\n\nfunction bnToHex(bn) {\n  return '0x' + bn.toString(16)\n}\n\nfunction blockRefIsNumber(blockRef){\n  return blockRef && !['earliest', 'latest', 'pending'].includes(blockRef)\n}\n\nfunction hexToInt(hexString) {\n  if (hexString === undefined || hexString === null) return hexString\n  return Number.parseInt(hexString, 16)\n}\n\nfunction incrementHexInt(hexString){\n  if (hexString === undefined || hexString === null) return hexString\n  const value = hexToInt(hexString)\n  return intToHex(value + 1)\n}\n\nfunction intToHex(int) {\n  if (int === undefined || int === null) return int\n  let hexString = int.toString(16)\n  const needsLeftPad = hexString.length % 2\n  if (needsLeftPad) hexString = '0' + hexString\n  return '0x' + hexString\n}\n\nfunction unsafeRandomBytes(byteCount) {\n  let result = '0x'\n  for (let i = 0; i < byteCount; i++) {\n    result += unsafeRandomNibble()\n    result += unsafeRandomNibble()\n  }\n  return result\n}\n\nfunction unsafeRandomNibble() {\n  return Math.floor(Math.random() * 16).toString(16)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/hexUtils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/index.js":
/*!*****************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Mutex = (__webpack_require__(/*! async-mutex */ \"(ssr)/../node_modules/async-mutex/lib/index.js\").Mutex)\nconst { createAsyncMiddleware, createScaffoldMiddleware } = __webpack_require__(/*! json-rpc-engine */ \"(ssr)/../node_modules/json-rpc-engine/dist/index.js\")\nconst LogFilter = __webpack_require__(/*! ./log-filter.js */ \"(ssr)/../node_modules/eth-json-rpc-filters/log-filter.js\")\nconst BlockFilter = __webpack_require__(/*! ./block-filter.js */ \"(ssr)/../node_modules/eth-json-rpc-filters/block-filter.js\")\nconst TxFilter = __webpack_require__(/*! ./tx-filter.js */ \"(ssr)/../node_modules/eth-json-rpc-filters/tx-filter.js\")\nconst { intToHex, hexToInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/../node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nmodule.exports = createEthFilterMiddleware\n\nfunction createEthFilterMiddleware({ blockTracker, provider }) {\n\n  // create filter collection\n  let filterIndex = 0\n  let filters = {}\n  // create update mutex\n  const mutex = new Mutex()\n  const waitForFree = mutexMiddlewareWrapper({ mutex })\n\n  const middleware = createScaffoldMiddleware({\n    // install filters\n    eth_newFilter:                   waitForFree(toFilterCreationMiddleware(newLogFilter)),\n    eth_newBlockFilter:              waitForFree(toFilterCreationMiddleware(newBlockFilter)),\n    eth_newPendingTransactionFilter: waitForFree(toFilterCreationMiddleware(newPendingTransactionFilter)),\n    // uninstall filters\n    eth_uninstallFilter:             waitForFree(toAsyncRpcMiddleware(uninstallFilterHandler)),\n    // checking filter changes\n    eth_getFilterChanges:            waitForFree(toAsyncRpcMiddleware(getFilterChanges)),\n    eth_getFilterLogs:               waitForFree(toAsyncRpcMiddleware(getFilterLogs)),\n  })\n\n  // setup filter updating and destroy handler\n  const filterUpdater = async ({ oldBlock, newBlock }) => {\n    if (filters.length === 0) return\n    // lock update reads\n    const releaseLock = await mutex.acquire()\n    try {\n      // process all filters in parallel\n      await Promise.all(objValues(filters).map(async (filter) => {\n        try {\n         await filter.update({ oldBlock, newBlock })\n        } catch (err) {\n          // handle each error individually so filter update errors don't affect other filters\n          console.error(err)\n        }\n      }))\n    } catch (err) {\n      // log error so we don't skip the releaseLock\n      console.error(err)\n    }\n    // unlock update reads\n    releaseLock()\n  }\n\n  // expose filter methods directly\n  middleware.newLogFilter = newLogFilter\n  middleware.newBlockFilter = newBlockFilter\n  middleware.newPendingTransactionFilter = newPendingTransactionFilter\n  middleware.uninstallFilter = uninstallFilterHandler\n  middleware.getFilterChanges = getFilterChanges\n  middleware.getFilterLogs = getFilterLogs\n\n  // expose destroy method for cleanup\n  middleware.destroy = () => {\n    uninstallAllFilters()\n  }\n\n  return middleware\n\n  //\n  // new filters\n  //\n\n  async function newLogFilter(params) {\n    const filter = new LogFilter({ provider, params })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  async function newBlockFilter() {\n    const filter = new BlockFilter({ provider })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  async function newPendingTransactionFilter() {\n    const filter = new TxFilter({ provider })\n    const filterIndex = await installFilter(filter)\n    return filter\n  }\n\n  //\n  // get filter changes\n  //\n\n  async function getFilterChanges(filterIndexHex) {\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    if (!filter) {\n      throw new Error(`No filter for index \"${filterIndex}\"`)\n    }\n    const results = filter.getChangesAndClear()\n    return results\n  }\n\n  async function getFilterLogs(filterIndexHex) {\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    if (!filter) {\n      throw new Error(`No filter for index \"${filterIndex}\"`)\n    }\n    // only return results for log filters\n    let results = []\n    if (filter.type === 'log') {\n      results = filter.getAllResults()\n    }\n    return results\n  }\n\n\n  //\n  // remove filters\n  //\n\n\n  async function uninstallFilterHandler(filterIndexHex) {\n    // check filter exists\n    const filterIndex = hexToInt(filterIndexHex)\n    const filter = filters[filterIndex]\n    const result = Boolean(filter)\n    // uninstall filter\n    if (result) {\n      await uninstallFilter(filterIndex)\n    }\n    return result\n  }\n\n  //\n  // utils\n  //\n\n  async function installFilter(filter) {\n    const prevFilterCount = objValues(filters).length\n    // install filter\n    const currentBlock = await blockTracker.getLatestBlock()\n    await filter.initialize({ currentBlock })\n    filterIndex++\n    filters[filterIndex] = filter\n    filter.id = filterIndex\n    filter.idHex = intToHex(filterIndex)\n    // update block tracker subs\n    const newFilterCount = objValues(filters).length\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount })\n    return filterIndex\n  }\n\n  async function uninstallFilter(filterIndex) {\n    const prevFilterCount = objValues(filters).length\n    delete filters[filterIndex]\n    // update block tracker subs\n    const newFilterCount = objValues(filters).length\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount })\n  }\n\n  async function uninstallAllFilters() {\n    const prevFilterCount = objValues(filters).length\n    filters = {}\n    // update block tracker subs\n    updateBlockTrackerSubs({ prevFilterCount, newFilterCount: 0 })\n  }\n\n  function updateBlockTrackerSubs({ prevFilterCount, newFilterCount }) {\n    // subscribe\n    if (prevFilterCount === 0 && newFilterCount > 0) {\n      blockTracker.on('sync', filterUpdater)\n      return\n    }\n    // unsubscribe\n    if (prevFilterCount > 0 && newFilterCount === 0) {\n      blockTracker.removeListener('sync', filterUpdater)\n      return\n    }\n  }\n\n}\n\n// helper for turning filter constructors into rpc middleware\nfunction toFilterCreationMiddleware(createFilterFn) {\n  return toAsyncRpcMiddleware(async (...args) => {\n    const filter = await createFilterFn(...args)\n    const result = intToHex(filter.id)\n    return result\n  })\n}\n\n// helper for pulling out req.params and setting res.result\nfunction toAsyncRpcMiddleware(asyncFn) {\n  return createAsyncMiddleware(async (req, res) => {\n    const result = await asyncFn.apply(null, req.params)\n    res.result = result\n  })\n}\n\nfunction mutexMiddlewareWrapper({ mutex }) {\n  return (middleware) => {\n    return async (req, res, next, end) => {\n      // wait for mutex available\n      // we can release immediately because\n      // we just need to make sure updates aren't active\n      const releaseLock = await mutex.acquire()\n      releaseLock()\n      middleware(req, res, next, end)\n    }\n  }\n}\n\nfunction objValues(obj, fn){\n  const values = []\n  for (let key in obj) {\n    values.push(obj[key])\n  }\n  return values\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/log-filter.js":
/*!**********************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/log-filter.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const EthQuery = __webpack_require__(/*! eth-query */ \"(ssr)/../node_modules/eth-query/index.js\")\nconst pify = __webpack_require__(/*! pify */ \"(ssr)/../node_modules/eth-json-rpc-filters/node_modules/pify/index.js\")\nconst BaseFilterWithHistory = __webpack_require__(/*! ./base-filter-history */ \"(ssr)/../node_modules/eth-json-rpc-filters/base-filter-history.js\")\nconst { bnToHex, hexToInt, incrementHexInt, minBlockRef, blockRefIsNumber } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/../node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass LogFilter extends BaseFilterWithHistory {\n\n  constructor ({ provider, params }) {\n    super()\n    this.type = 'log'\n    this.ethQuery = new EthQuery(provider)\n    this.params = Object.assign({\n      fromBlock: 'latest',\n      toBlock: 'latest',\n      address: undefined,\n      topics: [],\n    }, params)\n    // normalize address parameter\n    if (this.params.address) {\n      // ensure array\n      if (!Array.isArray(this.params.address)) {\n        this.params.address = [this.params.address]\n      }\n      // ensure lowercase\n      this.params.address = this.params.address.map(address => address.toLowerCase())\n    }\n  }\n\n  async initialize({ currentBlock }) {\n    // resolve params.fromBlock\n    let fromBlock = this.params.fromBlock\n    if (['latest', 'pending'].includes(fromBlock)) fromBlock = currentBlock\n    if ('earliest' === fromBlock) fromBlock = '0x0'\n    this.params.fromBlock = fromBlock\n    // set toBlock for initial lookup\n    const toBlock = minBlockRef(this.params.toBlock, currentBlock)\n    const params = Object.assign({}, this.params, { toBlock })\n    // fetch logs and add to results\n    const newLogs = await this._fetchLogs(params)\n    this.addInitialResults(newLogs)\n  }\n\n  async update ({ oldBlock, newBlock }) {\n    // configure params for this update\n    const toBlock = newBlock\n    let fromBlock\n    // oldBlock is empty on first sync\n    if (oldBlock) {\n      fromBlock = incrementHexInt(oldBlock)\n    } else {\n      fromBlock = newBlock\n    }\n    // fetch logs\n    const params = Object.assign({}, this.params, { fromBlock, toBlock })\n    const newLogs = await this._fetchLogs(params)\n    const matchingLogs = newLogs.filter(log => this.matchLog(log))\n\n    // add to results\n    this.addResults(matchingLogs)\n  }\n\n  async _fetchLogs (params) {\n    const newLogs = await pify(cb => this.ethQuery.getLogs(params, cb))()\n    // add to results\n    return newLogs\n  }\n\n  matchLog(log) {\n    // check if block number in bounds:\n    if (hexToInt(this.params.fromBlock) >= hexToInt(log.blockNumber)) return false\n    if (blockRefIsNumber(this.params.toBlock) && hexToInt(this.params.toBlock) <= hexToInt(log.blockNumber)) return false\n\n    // address is correct:\n    const normalizedLogAddress = log.address && log.address.toLowerCase()\n    if (this.params.address && normalizedLogAddress && !this.params.address.includes(normalizedLogAddress)) return false\n\n    // topics match:\n    // topics are position-dependant\n    // topics can be nested to represent `or` [[a || b], c]\n    // topics can be null, representing a wild card for that position\n    const topicsMatch = this.params.topics.every((topicPattern, index) => {\n      // pattern is longer than actual topics\n      let logTopic = log.topics[index]\n      if (!logTopic) return false\n      logTopic = logTopic.toLowerCase()\n      // normalize subTopics\n      let subtopicsToMatch = Array.isArray(topicPattern) ? topicPattern : [topicPattern]\n      // check for wild card\n      const subtopicsIncludeWildcard = subtopicsToMatch.includes(null)\n      if (subtopicsIncludeWildcard) return true\n      subtopicsToMatch = subtopicsToMatch.map(topic => topic.toLowerCase())\n      // check each possible matching topic\n      const topicDoesMatch = subtopicsToMatch.includes(logTopic)\n      return topicDoesMatch\n    })\n\n    return topicsMatch\n  }\n\n}\n\nmodule.exports = LogFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/log-filter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/node_modules/pify/index.js":
/*!***********************************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/node_modules/pify/index.js ***!
  \***********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst processFn = (fn, options, proxy, unwrapped) => function (...arguments_) {\n\tconst P = options.promiseModule;\n\n\treturn new P((resolve, reject) => {\n\t\tif (options.multiArgs) {\n\t\t\targuments_.push((...result) => {\n\t\t\t\tif (options.errorFirst) {\n\t\t\t\t\tif (result[0]) {\n\t\t\t\t\t\treject(result);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.shift();\n\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t} else if (options.errorFirst) {\n\t\t\targuments_.push((error, result) => {\n\t\t\t\tif (error) {\n\t\t\t\t\treject(error);\n\t\t\t\t} else {\n\t\t\t\t\tresolve(result);\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\targuments_.push(resolve);\n\t\t}\n\n\t\tconst self = this === proxy ? unwrapped : this;\n\t\tReflect.apply(fn, self, arguments_);\n\t});\n};\n\nconst filterCache = new WeakMap();\n\nmodule.exports = (input, options) => {\n\toptions = {\n\t\texclude: [/.+(?:Sync|Stream)$/],\n\t\terrorFirst: true,\n\t\tpromiseModule: Promise,\n\t\t...options\n\t};\n\n\tconst objectType = typeof input;\n\tif (!(input !== null && (objectType === 'object' || objectType === 'function'))) {\n\t\tthrow new TypeError(`Expected \\`input\\` to be a \\`Function\\` or \\`Object\\`, got \\`${input === null ? 'null' : objectType}\\``);\n\t}\n\n\tconst filter = (target, key) => {\n\t\tlet cached = filterCache.get(target);\n\n\t\tif (!cached) {\n\t\t\tcached = {};\n\t\t\tfilterCache.set(target, cached);\n\t\t}\n\n\t\tif (key in cached) {\n\t\t\treturn cached[key];\n\t\t}\n\n\t\tconst match = pattern => (typeof pattern === 'string' || typeof key === 'symbol') ? key === pattern : pattern.test(key);\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(target, key);\n\t\tconst writableOrConfigurableOwn = (desc === undefined || desc.writable || desc.configurable);\n\t\tconst included = options.include ? options.include.some(match) : !options.exclude.some(match);\n\t\tconst shouldFilter = included && writableOrConfigurableOwn;\n\t\tcached[key] = shouldFilter;\n\t\treturn shouldFilter;\n\t};\n\n\tconst cache = new WeakMap();\n\n\tconst proxy = new Proxy(input, {\n\t\tapply(target, thisArg, args) {\n\t\t\tconst cached = cache.get(target);\n\n\t\t\tif (cached) {\n\t\t\t\treturn Reflect.apply(cached, thisArg, args);\n\t\t\t}\n\n\t\t\tconst pified = options.excludeMain ? target : processFn(target, options, proxy, target);\n\t\t\tcache.set(target, pified);\n\t\t\treturn Reflect.apply(pified, thisArg, args);\n\t\t},\n\n\t\tget(target, key) {\n\t\t\tconst property = target[key];\n\n\t\t\t// eslint-disable-next-line no-use-extend-native/no-use-extend-native\n\t\t\tif (!filter(target, key) || property === Function.prototype[key]) {\n\t\t\t\treturn property;\n\t\t\t}\n\n\t\t\tconst cached = cache.get(property);\n\n\t\t\tif (cached) {\n\t\t\t\treturn cached;\n\t\t\t}\n\n\t\t\tif (typeof property === 'function') {\n\t\t\t\tconst pified = processFn(property, options, proxy, target);\n\t\t\t\tcache.set(property, pified);\n\t\t\t\treturn pified;\n\t\t\t}\n\n\t\t\treturn property;\n\t\t}\n\t});\n\n\treturn proxy;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/node_modules/pify/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/subscriptionManager.js":
/*!*******************************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/subscriptionManager.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const SafeEventEmitter = (__webpack_require__(/*! @metamask/safe-event-emitter */ \"(ssr)/../node_modules/@metamask/safe-event-emitter/dist/cjs/index.js\")[\"default\"])\nconst { createAsyncMiddleware, createScaffoldMiddleware } = __webpack_require__(/*! json-rpc-engine */ \"(ssr)/../node_modules/json-rpc-engine/dist/index.js\")\nconst createFilterMiddleware = __webpack_require__(/*! ./index.js */ \"(ssr)/../node_modules/eth-json-rpc-filters/index.js\")\nconst { unsafeRandomBytes, incrementHexInt } = __webpack_require__(/*! ./hexUtils.js */ \"(ssr)/../node_modules/eth-json-rpc-filters/hexUtils.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange.js */ \"(ssr)/../node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\n\nmodule.exports = createSubscriptionMiddleware\n\n\nfunction createSubscriptionMiddleware({ blockTracker, provider }) {\n  // state and utilities for handling subscriptions\n  const subscriptions = {}\n  const filterManager = createFilterMiddleware({ blockTracker, provider })\n\n  // internal flag\n  let isDestroyed = false\n\n  // create subscriptionManager api object\n  const events = new SafeEventEmitter()\n  const middleware = createScaffoldMiddleware({\n    eth_subscribe: createAsyncMiddleware(subscribe),\n    eth_unsubscribe: createAsyncMiddleware(unsubscribe),\n  })\n  middleware.destroy = destroy\n  return { events, middleware }\n\n  async function subscribe(req, res) {\n\n    if (isDestroyed) throw new Error(\n      'SubscriptionManager - attempting to use after destroying'\n    )\n\n    const subscriptionType = req.params[0]\n    // subId is 16 byte hex string\n    const subId = unsafeRandomBytes(16)\n\n    // create sub\n    let sub\n    switch (subscriptionType) {\n      case 'newHeads':\n        sub = createSubNewHeads({ subId })\n        break\n      case 'logs':\n        const filterParams = req.params[1]\n        const filter = await filterManager.newLogFilter(filterParams)\n        sub = createSubFromFilter({ subId, filter })\n        break\n      default:\n        throw new Error(`SubscriptionManager - unsupported subscription type \"${subscriptionType}\"`)\n\n    }\n    subscriptions[subId] = sub\n\n    res.result = subId\n    return\n\n    function createSubNewHeads({ subId }) {\n      const sub = {\n        type: subscriptionType,\n        destroy: async () => {\n          blockTracker.removeListener('sync', sub.update)\n        },\n        update: async ({ oldBlock, newBlock }) => {\n          // for newHeads\n          const toBlock = newBlock\n          const fromBlock = incrementHexInt(oldBlock)\n          const rawBlocks = await getBlocksForRange({ provider, fromBlock, toBlock })\n          const results = rawBlocks.map(normalizeBlock).filter(block => block !== null)\n          results.forEach((value) => {\n            _emitSubscriptionResult(subId, value)\n          })\n        }\n      }\n      // check for subscription updates on new block\n      blockTracker.on('sync', sub.update)\n      return sub\n    }\n\n    function createSubFromFilter({ subId, filter }) {\n      filter.on('update', result => _emitSubscriptionResult(subId, result))\n      const sub = {\n        type: subscriptionType,\n        destroy: async () => {\n          return await filterManager.uninstallFilter(filter.idHex)\n        },\n      }\n      return sub\n    }\n  }\n\n  async function unsubscribe(req, res) {\n\n    if (isDestroyed) throw new Error(\n      'SubscriptionManager - attempting to use after destroying'\n    )\n\n    const id = req.params[0]\n    const subscription = subscriptions[id]\n    // if missing, return \"false\" to indicate it was not removed\n    if (!subscription) {\n      res.result = false\n      return\n    }\n    // cleanup subscription\n    delete subscriptions[id]\n    await subscription.destroy()\n    res.result = true\n  }\n\n  function _emitSubscriptionResult(filterIdHex, value) {\n    events.emit('notification', {\n      jsonrpc: '2.0',\n      method: 'eth_subscription',\n      params: {\n        subscription: filterIdHex,\n        result: value,\n      },\n    })\n  }\n\n  function destroy() {\n    events.removeAllListeners()\n    for (const id in subscriptions) {\n      subscriptions[id].destroy()\n      delete subscriptions[id]\n    }\n    isDestroyed = true\n  }\n}\n\nfunction normalizeBlock(block) {\n  if (block === null || block === undefined) {\n    return null;\n  }\n  return {\n    hash: block.hash,\n    parentHash: block.parentHash,\n    sha3Uncles: block.sha3Uncles,\n    miner: block.miner,\n    stateRoot: block.stateRoot,\n    transactionsRoot: block.transactionsRoot,\n    receiptsRoot: block.receiptsRoot,\n    logsBloom: block.logsBloom,\n    difficulty: block.difficulty,\n    number: block.number,\n    gasLimit: block.gasLimit,\n    gasUsed: block.gasUsed,\n    nonce: block.nonce,\n    mixHash: block.mixHash,\n    timestamp: block.timestamp,\n    extraData: block.extraData,\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/subscriptionManager.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/eth-json-rpc-filters/tx-filter.js":
/*!*********************************************************!*\
  !*** ../node_modules/eth-json-rpc-filters/tx-filter.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BaseFilter = __webpack_require__(/*! ./base-filter */ \"(ssr)/../node_modules/eth-json-rpc-filters/base-filter.js\")\nconst getBlocksForRange = __webpack_require__(/*! ./getBlocksForRange */ \"(ssr)/../node_modules/eth-json-rpc-filters/getBlocksForRange.js\")\nconst { incrementHexInt } = __webpack_require__(/*! ./hexUtils */ \"(ssr)/../node_modules/eth-json-rpc-filters/hexUtils.js\")\n\nclass TxFilter extends BaseFilter {\n\n  constructor ({ provider }) {\n    super()\n    this.type = 'tx'\n    this.provider = provider\n  }\n\n  async update ({ oldBlock }) {\n    const toBlock = oldBlock\n    const fromBlock = incrementHexInt(oldBlock)\n    const blocks = await getBlocksForRange({ provider: this.provider, fromBlock, toBlock })\n    const blockTxHashes = []\n    for (const block of blocks) {\n      blockTxHashes.push(...block.transactions)\n    }\n    // add to results\n    this.addResults(blockTxHashes)\n  }\n\n}\n\nmodule.exports = TxFilter\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2V0aC1qc29uLXJwYy1maWx0ZXJzL3R4LWZpbHRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxtQkFBbUIsbUJBQU8sQ0FBQyxnRkFBZTtBQUMxQywwQkFBMEIsbUJBQU8sQ0FBQyw0RkFBcUI7QUFDdkQsUUFBUSxrQkFBa0IsRUFBRSxtQkFBTyxDQUFDLDBFQUFZOztBQUVoRDs7QUFFQSxpQkFBaUIsVUFBVTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBa0IsVUFBVTtBQUM1QjtBQUNBO0FBQ0EsNkNBQTZDLDZDQUE2QztBQUMxRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSIsInNvdXJjZXMiOlsiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL25vZGVfbW9kdWxlcy9ldGgtanNvbi1ycGMtZmlsdGVycy90eC1maWx0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQmFzZUZpbHRlciA9IHJlcXVpcmUoJy4vYmFzZS1maWx0ZXInKVxuY29uc3QgZ2V0QmxvY2tzRm9yUmFuZ2UgPSByZXF1aXJlKCcuL2dldEJsb2Nrc0ZvclJhbmdlJylcbmNvbnN0IHsgaW5jcmVtZW50SGV4SW50IH0gPSByZXF1aXJlKCcuL2hleFV0aWxzJylcblxuY2xhc3MgVHhGaWx0ZXIgZXh0ZW5kcyBCYXNlRmlsdGVyIHtcblxuICBjb25zdHJ1Y3RvciAoeyBwcm92aWRlciB9KSB7XG4gICAgc3VwZXIoKVxuICAgIHRoaXMudHlwZSA9ICd0eCdcbiAgICB0aGlzLnByb3ZpZGVyID0gcHJvdmlkZXJcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZSAoeyBvbGRCbG9jayB9KSB7XG4gICAgY29uc3QgdG9CbG9jayA9IG9sZEJsb2NrXG4gICAgY29uc3QgZnJvbUJsb2NrID0gaW5jcmVtZW50SGV4SW50KG9sZEJsb2NrKVxuICAgIGNvbnN0IGJsb2NrcyA9IGF3YWl0IGdldEJsb2Nrc0ZvclJhbmdlKHsgcHJvdmlkZXI6IHRoaXMucHJvdmlkZXIsIGZyb21CbG9jaywgdG9CbG9jayB9KVxuICAgIGNvbnN0IGJsb2NrVHhIYXNoZXMgPSBbXVxuICAgIGZvciAoY29uc3QgYmxvY2sgb2YgYmxvY2tzKSB7XG4gICAgICBibG9ja1R4SGFzaGVzLnB1c2goLi4uYmxvY2sudHJhbnNhY3Rpb25zKVxuICAgIH1cbiAgICAvLyBhZGQgdG8gcmVzdWx0c1xuICAgIHRoaXMuYWRkUmVzdWx0cyhibG9ja1R4SGFzaGVzKVxuICB9XG5cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBUeEZpbHRlclxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/eth-json-rpc-filters/tx-filter.js\n");

/***/ })

};
;