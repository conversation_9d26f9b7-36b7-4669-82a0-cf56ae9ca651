/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?fc76\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/keith/akas/frontend/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmpvZSUyRmtlaXRoJTJGYWthcyUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL2Zyb250ZW5kL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fcomponents%2Fproviders%2FWeb3Provider.tsx%22%2C%22ids%22%3A%5B%22Web3Provider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fcomponents%2Fproviders%2FWeb3Provider.tsx%22%2C%22ids%22%3A%5B%22Web3Provider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Web3Provider.tsx */ \"(rsc)/./src/components/providers/Web3Provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fcomponents%2Fproviders%2FWeb3Provider.tsx%22%2C%22ids%22%3A%5B%22Web3Provider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \***************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/../node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxFQUFpRjs7QUFFakYsRUFBRSxpRUFBZTtBQUNqQix1QkFBdUI7QUFDdkIscUJBQXFCLDhGQUFtQjs7QUFFeEM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvZnJvbnRlbmQvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvam9lL2tlaXRoL2FrYXMvZnJvbnRlbmQvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_Web3Provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/Web3Provider */ \"(rsc)/./src/components/providers/Web3Provider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"AkashaDao Community Pass\",\n    description: \"AkashaDao社区入场券 - 专属NFT通行证\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_Web3Provider__WEBPACK_IMPORTED_MODULE_2__.Web3Provider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/keith/akas/frontend/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/Web3Provider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/Web3Provider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Web3Provider: () => (/* binding */ Web3Provider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Web3Provider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Web3Provider() from the server but Web3Provider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/keith/akas/frontend/src/components/providers/Web3Provider.tsx",
"Web3Provider",
);

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmpvZSUyRmtlaXRoJTJGYWthcyUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2pvZS9rZWl0aC9ha2FzL2Zyb250ZW5kL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fcomponents%2Fproviders%2FWeb3Provider.tsx%22%2C%22ids%22%3A%5B%22Web3Provider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fcomponents%2Fproviders%2FWeb3Provider.tsx%22%2C%22ids%22%3A%5B%22Web3Provider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Web3Provider.tsx */ \"(ssr)/./src/components/providers/Web3Provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fcomponents%2Fproviders%2FWeb3Provider.tsx%22%2C%22ids%22%3A%5B%22Web3Provider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjoe%2Fkeith%2Fakas%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! wagmi */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useAccount.js\");\n/* harmony import */ var _components_wallet_WalletButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/wallet/WalletButton */ \"(ssr)/./src/components/wallet/WalletButton.tsx\");\n/* harmony import */ var _components_nft_NFTPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/nft/NFTPreview */ \"(ssr)/./src/components/nft/NFTPreview.tsx\");\n/* harmony import */ var _lib_web3__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/web3 */ \"(ssr)/./src/lib/web3.ts\");\n/* harmony import */ var _components_ui_Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Logo */ \"(ssr)/./src/components/ui/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const { address, isConnected } = (0,wagmi__WEBPACK_IMPORTED_MODULE_6__.useAccount)();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setIsVisible(true);\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    setCurrentSlide({\n                        \"Home.useEffect.interval\": (prev)=>(prev + 1) % 3\n                    }[\"Home.useEffect.interval\"]);\n                }\n            }[\"Home.useEffect.interval\"], 5000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Logo__WEBPACK_IMPORTED_MODULE_5__.Logo, {\n                                size: \"lg\",\n                                showText: true,\n                                showSubtitle: true,\n                                href: \"/\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-slate-300 hover:text-white transition-colors\",\n                                        children: \"特性\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#benefits\",\n                                        className: \"text-slate-300 hover:text-white transition-colors\",\n                                        children: \"权益\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#roadmap\",\n                                        className: \"text-slate-300 hover:text-white transition-colors\",\n                                        children: \"路线图\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#community\",\n                                        className: \"text-slate-300 hover:text-white transition-colors\",\n                                        children: \"社区\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/mint\",\n                                        className: \"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-medium transition-all transform hover:scale-105 shadow-lg\",\n                                        children: \"铸造 NFT\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletButton__WEBPACK_IMPORTED_MODULE_2__.WalletButton, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40\",\n                                style: {\n                                    backgroundImage: 'url(/images/hero-background.webp)',\n                                    backgroundBlendMode: 'overlay'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 right-10 w-32 h-32 opacity-20\",\n                                style: {\n                                    backgroundImage: 'url(/images/geometric-decoration.webp)',\n                                    backgroundSize: 'contain',\n                                    backgroundRepeat: 'no-repeat'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 left-10 w-24 h-24 opacity-15 animate-float\",\n                                style: {\n                                    backgroundImage: 'url(/images/geometric-decoration.webp)',\n                                    backgroundSize: 'contain',\n                                    backgroundRepeat: 'no-repeat',\n                                    transform: 'rotate(45deg)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `space-y-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center px-4 py-2 bg-purple-500/20 border border-purple-500/30 rounded-full text-purple-300 text-sm font-medium\",\n                                                    children: \"\\uD83D\\uDE80 Web3社区的未来\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-5xl lg:text-7xl font-bold text-white leading-tight\",\n                                                    children: [\n                                                        \"AkashaDao\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"block text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400\",\n                                                            children: \"Community Pass\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl text-slate-300 max-w-2xl leading-relaxed\",\n                                                    children: \"获得专属NFT通行证，解锁Web3世界的无限可能。 享受无限制内容访问、社区治理权、专属活动和顶级VC网络连接。\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/mint\",\n                                                    className: \"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-bold text-lg transition-all transform hover:scale-105 shadow-2xl\",\n                                                    children: [\n                                                        \"立即铸造 NFT\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"ml-2 w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#features\",\n                                                    className: \"inline-flex items-center justify-center px-8 py-4 border-2 border-slate-600 hover:border-purple-500 text-white rounded-xl font-bold text-lg transition-all hover:bg-purple-500/10\",\n                                                    children: \"了解更多\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-8 pt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white\",\n                                                            children: \"3000+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-slate-400 text-sm\",\n                                                            children: \"社区成员\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white\",\n                                                            children: \"500+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-slate-400 text-sm\",\n                                                            children: \"NFT持有者\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white\",\n                                                            children: \"50+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-slate-400 text-sm\",\n                                                            children: \"合作伙伴\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `relative transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full max-w-md mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-hidden rounded-3xl\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex transition-transform duration-500 ease-in-out\",\n                                                            style: {\n                                                                transform: `translateX(-${currentSlide * 100}%)`\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nft_NFTPreview__WEBPACK_IMPORTED_MODULE_3__.NFTPreview, {\n                                                                        tier: \"whitelist\",\n                                                                        className: \"shadow-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 153,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nft_NFTPreview__WEBPACK_IMPORTED_MODULE_3__.NFTPreview, {\n                                                                        tier: \"student\",\n                                                                        className: \"shadow-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"aspect-square bg-gradient-to-br from-emerald-400 to-cyan-500 rounded-2xl flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center text-white\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-4xl mb-4\",\n                                                                                    children: \"\\uD83D\\uDD2E\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                                    lineNumber: 161,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xl font-bold\",\n                                                                                    children: \"即将推出\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                                    lineNumber: 162,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm opacity-80\",\n                                                                                    children: \"更多权益\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                                    lineNumber: 163,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                            lineNumber: 160,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 159,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center space-x-2 mt-6\",\n                                                        children: [\n                                                            0,\n                                                            1,\n                                                            2\n                                                        ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setCurrentSlide(index),\n                                                                className: `w-3 h-3 rounded-full transition-all ${currentSlide === index ? 'bg-purple-500' : 'bg-slate-600'}`\n                                                            }, index, false, {\n                                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full opacity-20 animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full opacity-20 animate-bounce delay-1000\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-24 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30\",\n                                style: {\n                                    backgroundImage: 'url(/images/features-background.webp)',\n                                    backgroundBlendMode: 'multiply'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-slate-900/50 to-slate-900/80\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-white mb-4\",\n                                        children: \"为什么选择 AkashaDao Pass？\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-300 max-w-3xl mx-auto\",\n                                        children: \"不仅仅是NFT，更是通往Web3未来的钥匙\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-4\",\n                                                children: \"无限制内容访问\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"解锁所有付费内容、研究报告和独家分析，随时随地获取最新的Web3资讯和深度见解。\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-4\",\n                                                children: \"社区治理权\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"参与社区重要决策投票，影响AkashaDao的发展方向，真正成为社区的主人。\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-4\",\n                                                children: \"VC网络连接\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"直接接触顶级投资人和创业者，获得投资机会和商业合作的第一手信息。\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-4\",\n                                                children: \"专属活动\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"参加线下聚会、专属Workshop和高端社交活动，与行业精英面对面交流。\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-violet-500 to-purple-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-4\",\n                                                children: \"早期投资机会\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"优先获得优质项目的早期投资机会，参与种子轮和私募轮投资。\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-pink-500 to-rose-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-4\",\n                                                children: \"空投福利\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"定期获得合作项目的代币空投和NFT白名单，享受持续的被动收益。\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"benefits\",\n                className: \"py-24 bg-slate-800/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-white mb-4\",\n                                    children: \"选择适合你的通行证\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-slate-300 max-w-3xl mx-auto\",\n                                    children: \"不同层级，不同权益，总有一款适合你\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-slate-800/50 backdrop-blur-sm rounded-3xl p-8 border border-slate-700 group-hover:border-orange-500/50 transition-all\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold text-white\",\n                                                                    children: \"Whitelist Pass\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-orange-400 font-medium\",\n                                                                    children: \"早期支持者专属\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-white\",\n                                                                    children: [\n                                                                        _lib_web3__WEBPACK_IMPORTED_MODULE_4__.PRICES.WHITELIST,\n                                                                        \" ETH\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"限量发售\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nft_NFTPreview__WEBPACK_IMPORTED_MODULE_3__.NFTPreview, {\n                                                        tier: \"whitelist\",\n                                                        className: \"max-w-xs mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-orange-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"无限制内容阅读权限\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-orange-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"社区治理投票权\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-orange-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"专属线下活动邀请\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-orange-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"VC网络连接机会\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-orange-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"优先获得新项目信息\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-orange-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"专属Discord频道访问\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/mint\",\n                                                    className: \"block w-full py-4 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black font-bold text-center rounded-xl transition-all transform hover:scale-105\",\n                                                    children: \"立即铸造\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-slate-800/50 backdrop-blur-sm rounded-3xl p-8 border border-slate-700 group-hover:border-blue-500/50 transition-all\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold text-white\",\n                                                                    children: \"Student Pass\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-400 font-medium\",\n                                                                    children: \"学生专享优惠\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-white\",\n                                                                    children: [\n                                                                        _lib_web3__WEBPACK_IMPORTED_MODULE_4__.PRICES.STUDENT,\n                                                                        \" ETH\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"学生认证\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nft_NFTPreview__WEBPACK_IMPORTED_MODULE_3__.NFTPreview, {\n                                                        tier: \"student\",\n                                                        className: \"max-w-xs mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-blue-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"无限制内容阅读权限\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-blue-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"学习资源优先访问\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-blue-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"导师一对一指导机会\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-blue-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"实习和工作推荐\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-blue-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"学生专属活动\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-blue-400 mr-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"技能认证和证书\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/mint\",\n                                                    className: \"block w-full py-4 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-bold text-center rounded-xl transition-all transform hover:scale-105\",\n                                                    children: \"立即铸造\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"roadmap\",\n                className: \"py-24 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-25\",\n                                style: {\n                                    backgroundImage: 'url(/images/roadmap-background.webp)',\n                                    backgroundBlendMode: 'overlay'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-slate-900/70 to-slate-900/50\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-white mb-4\",\n                                        children: \"发展路线图\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-300 max-w-3xl mx-auto\",\n                                        children: \"我们的愿景和未来规划\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-purple-500 to-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 pr-8 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-white mb-2\",\n                                                                    children: \"Q1-Q2 2024 - 社区建设\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-300 mb-4\",\n                                                                    children: \"启动AkashaDao社区，发布NFT通行证，建立核心用户群体\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-end\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-sm\",\n                                                                        children: \"已完成\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-500 rounded-full border-4 border-slate-900\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 pl-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 pr-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-500 rounded-full border-4 border-slate-900\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 pl-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-white mb-2\",\n                                                                    children: \"Q3-Q4 2024 - 内容平台\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-300 mb-4\",\n                                                                    children: \"推出内容平台，提供高质量的Web3研究报告和分析\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-sm\",\n                                                                        children: \"已完成\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 499,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 pr-8 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-white mb-2\",\n                                                                    children: \"Q1-Q2 2025 - VC网络\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-300 mb-4\",\n                                                                    children: \"建立VC合作网络，为社区成员提供投资机会和项目对接\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-end\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-sm\",\n                                                                        children: \"已完成\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-500 rounded-full border-4 border-slate-900\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 pl-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 pr-8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 rounded-full border-4 border-slate-900\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 pl-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-white mb-2\",\n                                                                    children: \"Q3-Q4 2025 - 生态扩展\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-300 mb-4\",\n                                                                    children: \"推出DeFi产品，建立完整的Web3生态系统\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm\",\n                                                                        children: \"进行中\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                        lineNumber: 533,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"community\",\n                className: \"py-24 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20\",\n                                style: {\n                                    backgroundImage: 'url(/images/community-background.webp)',\n                                    backgroundBlendMode: 'screen'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-slate-800/40\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-white mb-4\",\n                                        children: \"加入我们的社区\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-300 max-w-3xl mx-auto\",\n                                        children: \"与全球Web3爱好者一起探索未来\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-4 gap-8 mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-white mb-2\",\n                                                children: \"3000+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-slate-400\",\n                                                children: \"社区成员\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-white mb-2\",\n                                                children: \"500+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-slate-400\",\n                                                children: \"NFT持有者\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-white mb-2\",\n                                                children: \"50+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-slate-400\",\n                                                children: \"合作伙伴\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-white mb-2\",\n                                                children: \"100+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-slate-400\",\n                                                children: \"专业内容\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-purple-500/50 transition-all text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-2\",\n                                                children: \"Discord\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"加入我们的Discord服务器，与社区成员实时交流\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-blue-500/50 transition-all text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-2\",\n                                                children: \"Twitter\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"关注我们的Twitter，获取最新动态和行业资讯\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 hover:border-green-500/50 transition-all text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-white\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-2\",\n                                                children: \"Medium\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-300\",\n                                                children: \"阅读我们的深度文章和技术分析\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-24 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-6\",\n                                children: \"准备好加入AkashaDao了吗？\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-slate-300 mb-8 max-w-2xl mx-auto\",\n                                children: \"立即铸造你的专属NFT通行证，开启Web3世界的无限可能\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/mint\",\n                                        className: \"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-bold text-lg transition-all transform hover:scale-105 shadow-2xl\",\n                                        children: [\n                                            \"立即铸造 NFT\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"ml-2 w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#community\",\n                                        className: \"inline-flex items-center justify-center px-8 py-4 border-2 border-slate-600 hover:border-purple-500 text-white rounded-xl font-bold text-lg transition-all hover:bg-purple-500/10\",\n                                        children: \"加入社区\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                        lineNumber: 621,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 619,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-slate-900 border-t border-slate-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: \"A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"AkashaDao\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-purple-300\",\n                                                            children: \"Community Pass\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-400 max-w-md mb-6\",\n                                            children: \"AkashaDao是一个专注于Web3教育和社区建设的去中心化自治组织， 通过NFT通行证为成员提供独家内容、投资机会和网络连接。\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"w-10 h-10 bg-slate-800 hover:bg-purple-600 rounded-lg flex items-center justify-center transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-slate-400 hover:text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"w-10 h-10 bg-slate-800 hover:bg-purple-600 rounded-lg flex items-center justify-center transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-slate-400 hover:text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"w-10 h-10 bg-slate-800 hover:bg-purple-600 rounded-lg flex items-center justify-center transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-slate-400 hover:text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-bold mb-4\",\n                                            children: \"快速链接\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#features\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"特性\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#benefits\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"权益\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#roadmap\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"路线图\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#community\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"社区\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/mint\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"铸造NFT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-bold mb-4\",\n                                            children: \"联系我们\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"Discord社区\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"Telegram群组\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-slate-400 hover:text-white transition-colors\",\n                                                        children: \"技术支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-slate-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-400 text-sm\",\n                                    children: \"\\xa9 2025 AkashaDao. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-6 mt-4 md:mt-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-slate-400 hover:text-white text-sm transition-colors\",\n                                            children: \"隐私政策\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-slate-400 hover:text-white text-sm transition-colors\",\n                                            children: \"服务条款\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-slate-400 hover:text-white text-sm transition-colors\",\n                                            children: \"Cookie政策\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                            lineNumber: 710,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n                lineNumber: 649,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/app/page.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/nft/NFTPreview.tsx":
/*!*******************************************!*\
  !*** ./src/components/nft/NFTPreview.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NFTPreview: () => (/* binding */ NFTPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/../node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ NFTPreview auto */ \n\nfunction NFTPreview({ tier, className = '' }) {\n    const isWhitelist = tier === 'whitelist';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative aspect-square rounded-2xl overflow-hidden ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: isWhitelist ? '/images/akasha-nft-whitelist.png' : '/images/akasha-nft-student.png',\n                    alt: `AkashaDao ${tier} NFT`,\n                    fill: true,\n                    className: \"object-cover\",\n                    priority: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 ${isWhitelist ? 'bg-gradient-to-br from-yellow-400/20 via-orange-500/20 to-red-500/20' : 'bg-gradient-to-br from-blue-400/20 via-purple-500/20 to-cyan-500/20'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent opacity-50 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute inset-0 rounded-2xl transition-all duration-300 hover:shadow-2xl ${isWhitelist ? 'hover:shadow-orange-500/50' : 'hover:shadow-blue-500/50'}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/nft/NFTPreview.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/nft/NFTPreview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/Web3Provider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/Web3Provider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Web3Provider: () => (/* binding */ Web3Provider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(ssr)/../node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(ssr)/../node_modules/@rainbow-me/rainbowkit/dist/index.js\");\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(ssr)/../node_modules/@rainbow-me/rainbowkit/dist/chunk-RZWDCITT.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _lib_web3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/web3 */ \"(ssr)/./src/lib/web3.ts\");\n/* harmony import */ var _rainbow_me_rainbowkit_styles_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @rainbow-me/rainbowkit/styles.css */ \"(ssr)/../node_modules/@rainbow-me/rainbowkit/dist/index.css\");\n/* __next_internal_client_entry_do_not_use__ Web3Provider auto */ \n\n\n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 1000 * 60 * 5,\n            gcTime: 1000 * 60 * 10\n        }\n    }\n});\nfunction Web3Provider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_4__.WagmiProvider, {\n        config: _lib_web3__WEBPACK_IMPORTED_MODULE_1__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n            client: queryClient,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_6__.RainbowKitProvider, {\n                theme: (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_7__.darkTheme)({\n                    accentColor: '#7b3f00',\n                    accentColorForeground: 'white',\n                    borderRadius: 'medium',\n                    fontStack: 'system',\n                    overlayBlur: 'small'\n                }),\n                appInfo: {\n                    appName: 'AkashaDao Community Pass',\n                    learnMoreUrl: 'https://akasha-dao.com'\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/providers/Web3Provider.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/providers/Web3Provider.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/providers/Web3Provider.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/Web3Provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Logo.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Logo.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Logo: () => (/* binding */ Logo),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Logo = ({ size = 'md', showText = true, showSubtitle = false, href = '/', className = '' })=>{\n    const sizeClasses = {\n        sm: {\n            icon: 'w-8 h-8',\n            text: 'text-lg',\n            subtitle: 'text-xs'\n        },\n        md: {\n            icon: 'w-10 h-10',\n            text: 'text-xl',\n            subtitle: 'text-xs'\n        },\n        lg: {\n            icon: 'w-12 h-12',\n            text: 'text-2xl',\n            subtitle: 'text-sm'\n        }\n    };\n    const LogoContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center space-x-3 ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${sizeClasses[size].icon} bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-white font-bold ${size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-xl' : 'text-lg'}`,\n                        children: \"A\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 7\n                }, undefined),\n                showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: `${sizeClasses[size].text} font-bold text-white leading-tight`,\n                            children: \"AkashaDao\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined),\n                        showSubtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `${sizeClasses[size].subtitle} text-purple-300 leading-tight`,\n                            children: \"Community Pass\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n            lineNumber: 38,\n            columnNumber: 5\n        }, undefined);\n    if (href) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n            href: href,\n            className: \"hover:opacity-80 transition-opacity\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoContent, {}, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LogoContent, {}, void 0, false, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/ui/Logo.tsx\",\n        lineNumber: 70,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Logo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/wallet/WalletButton.tsx":
/*!************************************************!*\
  !*** ./src/components/wallet/WalletButton.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletButton: () => (/* binding */ WalletButton),\n/* harmony export */   WalletStatus: () => (/* binding */ WalletStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(ssr)/../node_modules/@rainbow-me/rainbowkit/dist/index.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useAccount.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(ssr)/../node_modules/wagmi/dist/esm/hooks/useDisconnect.js\");\n/* harmony import */ var _lib_web3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/web3 */ \"(ssr)/./src/lib/web3.ts\");\n/* __next_internal_client_entry_do_not_use__ WalletButton,WalletStatus auto */ \n\n\n\nfunction WalletButton() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_2__.ConnectButton.Custom, {\n        children: ({ account, chain, openAccountModal, openChainModal, openConnectModal, authenticationStatus, mounted })=>{\n            // 注意：如果你的应用使用SSR，确保组件已挂载\n            const ready = mounted && authenticationStatus !== 'loading';\n            const connected = ready && account && chain && (!authenticationStatus || authenticationStatus === 'authenticated');\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...!ready && {\n                    'aria-hidden': true,\n                    'style': {\n                        opacity: 0,\n                        pointerEvents: 'none',\n                        userSelect: 'none'\n                    }\n                },\n                children: (()=>{\n                    if (!connected) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: openConnectModal,\n                            type: \"button\",\n                            className: \"bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                            children: \"连接钱包\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 19\n                        }, this);\n                    }\n                    if (chain.unsupported) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: openChainModal,\n                            type: \"button\",\n                            className: \"bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg\",\n                            children: \"网络错误\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 19\n                        }, this);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: openChainModal,\n                                type: \"button\",\n                                className: \"bg-gray-800 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center gap-2\",\n                                children: [\n                                    chain.hasIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: chain.iconBackground,\n                                            width: 20,\n                                            height: 20,\n                                            borderRadius: 999,\n                                            overflow: 'hidden'\n                                        },\n                                        children: chain.iconUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            alt: chain.name ?? 'Chain icon',\n                                            src: chain.iconUrl,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 27\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 23\n                                    }, this),\n                                    (0,_lib_web3__WEBPACK_IMPORTED_MODULE_1__.getNetworkName)(chain.id)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 19\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: openAccountModal,\n                                type: \"button\",\n                                className: \"bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                children: (0,_lib_web3__WEBPACK_IMPORTED_MODULE_1__.truncateAddress)(account.address)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 17\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                lineNumber: 29,\n                columnNumber: 11\n            }, this);\n        }\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n// 简化版钱包状态组件\nfunction WalletStatus() {\n    const { address, isConnected } = (0,wagmi__WEBPACK_IMPORTED_MODULE_3__.useAccount)();\n    const { disconnect } = (0,wagmi__WEBPACK_IMPORTED_MODULE_4__.useDisconnect)();\n    if (!isConnected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-gray-500 text-sm\",\n            children: \"请连接钱包以继续\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between bg-gray-50 dark:bg-gray-800 p-4 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: \"已连接地址\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-mono text-sm\",\n                        children: (0,_lib_web3__WEBPACK_IMPORTED_MODULE_1__.truncateAddress)(address)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>disconnect(),\n                className: \"text-red-600 hover:text-red-700 text-sm font-medium\",\n                children: \"断开连接\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/keith/akas/frontend/src/components/wallet/WalletButton.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/wallet/WalletButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/web3.ts":
/*!*************************!*\
  !*** ./src/lib/web3.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACCESS_CONTROL_ABI: () => (/* binding */ ACCESS_CONTROL_ABI),\n/* harmony export */   AKASHA_NFT_ABI: () => (/* binding */ AKASHA_NFT_ABI),\n/* harmony export */   AccessType: () => (/* binding */ AccessType),\n/* harmony export */   CONTRACT_ADDRESSES: () => (/* binding */ CONTRACT_ADDRESSES),\n/* harmony export */   MERKLE_GENERATOR_ABI: () => (/* binding */ MERKLE_GENERATOR_ABI),\n/* harmony export */   NFTTier: () => (/* binding */ NFTTier),\n/* harmony export */   PRICES: () => (/* binding */ PRICES),\n/* harmony export */   SUPPLY_LIMITS: () => (/* binding */ SUPPLY_LIMITS),\n/* harmony export */   SUPPORTED_CHAINS: () => (/* binding */ SUPPORTED_CHAINS),\n/* harmony export */   Web3Error: () => (/* binding */ Web3Error),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   formatEther: () => (/* binding */ formatEther),\n/* harmony export */   getNetworkName: () => (/* binding */ getNetworkName),\n/* harmony export */   isSupportedChain: () => (/* binding */ isSupportedChain),\n/* harmony export */   parseEther: () => (/* binding */ parseEther),\n/* harmony export */   truncateAddress: () => (/* binding */ truncateAddress)\n/* harmony export */ });\n/* harmony import */ var _rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @rainbow-me/rainbowkit */ \"(ssr)/../node_modules/@rainbow-me/rainbowkit/dist/index.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/../node_modules/viem/_esm/chains/definitions/hardhat.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/../node_modules/viem/_esm/chains/definitions/sepolia.js\");\n\n\n// 合约地址配置\nconst CONTRACT_ADDRESSES = {\n    // 在部署后更新这些地址\n    AKASHA_NFT:  false || '',\n    ACCESS_CONTROL:  false || '',\n    MERKLE_GENERATOR:  false || ''\n};\n// 支持的网络\nconst SUPPORTED_CHAINS = [\n    wagmi_chains__WEBPACK_IMPORTED_MODULE_0__.hardhat,\n    wagmi_chains__WEBPACK_IMPORTED_MODULE_1__.sepolia,\n    ... false ? 0 : []\n];\n// Wagmi配置\nconst config = (0,_rainbow_me_rainbowkit__WEBPACK_IMPORTED_MODULE_2__.getDefaultConfig)({\n    appName: 'AkashaDao Community Pass',\n    projectId: \"your_walletconnect_project_id_here\" || 0,\n    chains: SUPPORTED_CHAINS,\n    ssr: true\n});\n// NFT层级枚举\nvar NFTTier = /*#__PURE__*/ function(NFTTier) {\n    NFTTier[NFTTier[\"WHITELIST\"] = 0] = \"WHITELIST\";\n    NFTTier[NFTTier[\"STUDENT\"] = 1] = \"STUDENT\";\n    return NFTTier;\n}({});\n// 访问权限类型\nvar AccessType = /*#__PURE__*/ function(AccessType) {\n    AccessType[AccessType[\"UNLIMITED_READING\"] = 0] = \"UNLIMITED_READING\";\n    AccessType[AccessType[\"VOTING_RIGHTS\"] = 1] = \"VOTING_RIGHTS\";\n    AccessType[AccessType[\"EXCLUSIVE_EVENTS\"] = 2] = \"EXCLUSIVE_EVENTS\";\n    AccessType[AccessType[\"VC_NETWORKING\"] = 3] = \"VC_NETWORKING\";\n    return AccessType;\n}({});\n// 价格配置\nconst PRICES = {\n    WHITELIST: '0.05',\n    PUBLIC: '0.08',\n    STUDENT: '0.03'\n};\n// 供应量配置\nconst SUPPLY_LIMITS = {\n    WHITELIST_TOTAL: 100,\n    WHITELIST_PERCENTAGE: 80,\n    LOTTERY_PERCENTAGE: 20,\n    STUDENT_TOTAL: 500\n};\n// 合约ABI片段（主要函数）\nconst AKASHA_NFT_ABI = [\n    // 查询函数\n    'function name() view returns (string)',\n    'function symbol() view returns (string)',\n    'function totalSupply() view returns (uint256)',\n    'function balanceOf(address owner) view returns (uint256)',\n    'function ownerOf(uint256 tokenId) view returns (address)',\n    'function tokenURI(uint256 tokenId) view returns (string)',\n    'function getNFTInfo(uint256 tokenId) view returns (tuple(uint8 tier, uint256 mintTime, bool hasVotingRights, bool hasUnlimitedAccess))',\n    // 价格查询\n    'function whitelistPrice() view returns (uint256)',\n    'function publicPrice() view returns (uint256)',\n    'function studentPrice() view returns (uint256)',\n    // 供应量查询\n    'function whitelistMinted() view returns (uint256)',\n    'function studentMinted() view returns (uint256)',\n    'function remainingWhitelistSupply() view returns (uint256)',\n    'function remainingLotterySupply() view returns (uint256)',\n    'function remainingStudentSupply() view returns (uint256)',\n    // 销售状态查询\n    'function whitelistSaleActive() view returns (bool)',\n    'function lotterySaleActive() view returns (bool)',\n    'function studentSaleActive() view returns (bool)',\n    // Mint函数\n    'function whitelistMint(bytes32[] calldata merkleProof) payable',\n    'function lotteryMint(bytes32[] calldata merkleProof) payable',\n    'function studentMint(bytes32[] calldata merkleProof) payable',\n    // 权限查询\n    'function hasVotingRights(uint256 tokenId) view returns (bool)',\n    'function hasUnlimitedAccess(uint256 tokenId) view returns (bool)',\n    // 事件\n    'event WhitelistMint(address indexed user, uint256 indexed tokenId)',\n    'event LotteryMint(address indexed user, uint256 indexed tokenId)',\n    'event StudentMint(address indexed user, uint256 indexed tokenId)'\n];\nconst ACCESS_CONTROL_ABI = [\n    // 访问权限验证\n    'function hasAccess(address user, uint8 accessType) view returns (bool)',\n    'function isWhitelistMember(address user) view returns (bool)',\n    'function isStudentMember(address user) view returns (bool)',\n    // 活动管理\n    'function createEvent(string memory name, string memory description, uint256 startTime, uint256 endTime, bool requiresWhitelist)',\n    'function participateInEvent(uint256 eventId)',\n    'function eventCounter() view returns (uint256)',\n    // 提案和投票\n    'function createProposal(string memory title, string memory description, uint256 votingPeriod)',\n    'function vote(uint256 proposalId, bool support)',\n    'function getProposalResults(uint256 proposalId) view returns (uint256 forVotes, uint256 againstVotes)',\n    'function proposalCounter() view returns (uint256)',\n    // 访问时间更新\n    'function updateAccessTime(address user)',\n    'function lastAccessTime(address user) view returns (uint256)',\n    // 事件\n    'event EventCreated(uint256 indexed eventId, string name)',\n    'event EventParticipation(uint256 indexed eventId, address indexed user)',\n    'event ProposalCreated(uint256 indexed proposalId, string title)',\n    'event VoteCast(uint256 indexed proposalId, address indexed voter, bool support)'\n];\nconst MERKLE_GENERATOR_ABI = [\n    // Merkle Root管理\n    'function getMerkleRoot(string memory listType) view returns (bytes32)',\n    'function setMerkleRoot(string memory listType, bytes32 root)',\n    // 地址验证\n    'function verifyAddress(string memory listType, address user, bytes32[] calldata merkleProof) view returns (bool)',\n    'function verifyAddresses(string memory listType, address[] calldata users, bytes32[][] calldata merkleProofs) view returns (bool[])',\n    // 验证状态\n    'function isVerified(string memory listType, address user) view returns (bool)',\n    'function markAsVerified(string memory listType, address user, bytes32[] calldata merkleProof)',\n    // 工具函数\n    'function generateLeaf(address user) pure returns (bytes32)',\n    'function generateLeaves(address[] calldata users) pure returns (bytes32[])',\n    // 事件\n    'event MerkleRootSet(string indexed listType, bytes32 root)',\n    'event AddressVerified(string indexed listType, address indexed user)'\n];\n// 错误处理工具\nclass Web3Error extends Error {\n    constructor(message, code){\n        super(message), this.code = code;\n        this.name = 'Web3Error';\n    }\n}\n// 格式化ETH数量\nfunction formatEther(value) {\n    return (Number(value) / 1e18).toFixed(4);\n}\n// 解析ETH数量\nfunction parseEther(value) {\n    return BigInt(Math.floor(parseFloat(value) * 1e18));\n}\n// 截断地址显示\nfunction truncateAddress(address) {\n    if (!address) return '';\n    return `${address.slice(0, 6)}...${address.slice(-4)}`;\n}\n// 获取网络名称\nfunction getNetworkName(chainId) {\n    switch(chainId){\n        case 1:\n            return 'Mainnet';\n        case 11155111:\n            return 'Sepolia';\n        case 31337:\n            return 'Hardhat';\n        default:\n            return 'Unknown';\n    }\n}\n// 检查是否为支持的网络\nfunction isSupportedChain(chainId) {\n    return SUPPORTED_CHAINS.some((chain)=>chain.id === chainId);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/web3.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@rainbow-me","vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/viem","vendor-chunks/ox","vendor-chunks/@opentelemetry","vendor-chunks/abitype","vendor-chunks/@wagmi","vendor-chunks/@tanstack","vendor-chunks/ua-parser-js","vendor-chunks/qr","vendor-chunks/wagmi","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/zustand","vendor-chunks/@vanilla-extract","vendor-chunks/cuer","vendor-chunks/eventemitter3","vendor-chunks/use-sync-external-store","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/mipd","vendor-chunks/clsx","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();