globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/providers/Web3Provider.tsx":{"*":{"id":"(ssr)/./src/components/providers/Web3Provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/keith/akas/frontend/src/components/providers/Web3Provider.tsx":{"id":"(app-pages-browser)/./src/components/providers/Web3Provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/../node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/../node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/keith/akas/frontend/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/keith/akas/frontend/src/app/page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/keith/akas/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/keith/akas/frontend/src/":[],"/Users/<USER>/keith/akas/frontend/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/keith/akas/frontend/src/app/page":[],"/Users/<USER>/keith/akas/frontend/src/app/_not-found/page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/components/providers/Web3Provider.tsx":{"*":{"id":"(rsc)/./src/components/providers/Web3Provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"(app-pages-browser)/../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}}}