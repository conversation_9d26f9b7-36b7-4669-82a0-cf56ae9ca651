/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/whitelist/route";
exports.ids = ["app/api/whitelist/route"];
exports.modules = {

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwhitelist%2Froute&page=%2Fapi%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwhitelist%2Froute.ts&appDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwhitelist%2Froute&page=%2Fapi%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwhitelist%2Froute.ts&appDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_joe_keith_akas_frontend_src_app_api_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/whitelist/route.ts */ \"(rsc)/./src/app/api/whitelist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/whitelist/route\",\n        pathname: \"/api/whitelist\",\n        filename: \"route\",\n        bundlePath: \"app/api/whitelist/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/keith/akas/frontend/src/app/api/whitelist/route.ts\",\n    nextConfigOutput,\n    userland: _Users_joe_keith_akas_frontend_src_app_api_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwhitelist%2Froute&page=%2Fapi%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwhitelist%2Froute.ts&appDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/whitelist/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/whitelist/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../node_modules/next/dist/api/server.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(rsc)/../node_modules/viem/_esm/utils/hash/keccak256.js\");\n/* harmony import */ var merkletreejs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! merkletreejs */ \"(rsc)/../node_modules/merkletreejs/dist/index.js\");\n/* harmony import */ var merkletreejs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(merkletreejs__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// 模拟白名单地址（实际应用中应该从数据库获取）\nconst WHITELIST_ADDRESSES = [\n    '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266',\n    '0x70997970C51812dc3A010C7d01b50e0d17dc79C8',\n    '0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC',\n    '0x90F79bf6EB2c4f870365E785982E1f101E93b906',\n    '0x15d34AAf54267DB7D7c367839AAf71A00a2C6A65'\n];\nconst STUDENT_ADDRESSES = [\n    '0x9965507D1a55bcC2695C58ba16FB37d819B0A4dc',\n    '0x976EA74026E726554dB657fA54763abd0C3a0aa9',\n    '0x14dC79964da2C08b23698B3D3cc7Ca32193d9955',\n    '0x23618e81E3f5cdF7f54C3d65f7FBc0aBf5B21E8f',\n    '0xa0Ee7A142d267C1f36714E4a8F75612F20a79720'\n];\n// 生成Merkle Tree\nfunction generateMerkleTree(addresses) {\n    const leaves = addresses.map((addr)=>(0,viem__WEBPACK_IMPORTED_MODULE_2__.keccak256)(addr));\n    return new merkletreejs__WEBPACK_IMPORTED_MODULE_1__.MerkleTree(leaves, viem__WEBPACK_IMPORTED_MODULE_2__.keccak256, {\n        sortPairs: true\n    });\n}\n// 获取Merkle Proof\nfunction getMerkleProof(addresses, targetAddress) {\n    const tree = generateMerkleTree(addresses);\n    const leaf = (0,viem__WEBPACK_IMPORTED_MODULE_2__.keccak256)(targetAddress);\n    return tree.getHexProof(leaf);\n}\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const address = searchParams.get('address');\n    const type = searchParams.get('type'); // 'whitelist' or 'student'\n    if (!address) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '地址参数缺失'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        let isEligible = false;\n        let merkleProof = [];\n        let merkleRoot = '';\n        if (type === 'whitelist') {\n            isEligible = WHITELIST_ADDRESSES.includes(address.toLowerCase());\n            if (isEligible) {\n                merkleProof = getMerkleProof(WHITELIST_ADDRESSES, address);\n                const tree = generateMerkleTree(WHITELIST_ADDRESSES);\n                merkleRoot = tree.getHexRoot();\n            }\n        } else if (type === 'student') {\n            isEligible = STUDENT_ADDRESSES.includes(address.toLowerCase());\n            if (isEligible) {\n                merkleProof = getMerkleProof(STUDENT_ADDRESSES, address);\n                const tree = generateMerkleTree(STUDENT_ADDRESSES);\n                merkleRoot = tree.getHexRoot();\n            }\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无效的类型参数'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            address,\n            type,\n            isEligible,\n            merkleProof,\n            merkleRoot\n        });\n    } catch (error) {\n        console.error('Whitelist verification error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// 获取所有白名单和学生名单的Merkle Root\nasync function POST() {\n    try {\n        const whitelistTree = generateMerkleTree(WHITELIST_ADDRESSES);\n        const studentTree = generateMerkleTree(STUDENT_ADDRESSES);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            whitelist: {\n                addresses: WHITELIST_ADDRESSES,\n                merkleRoot: whitelistTree.getHexRoot(),\n                totalCount: WHITELIST_ADDRESSES.length\n            },\n            student: {\n                addresses: STUDENT_ADDRESSES,\n                merkleRoot: studentTree.getHexRoot(),\n                totalCount: STUDENT_ADDRESSES.length\n            }\n        });\n    } catch (error) {\n        console.error('Merkle tree generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/whitelist/route.ts\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/viem","vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/crypto-js","vendor-chunks/merkletreejs","vendor-chunks/treeify","vendor-chunks/buffer-reverse"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwhitelist%2Froute&page=%2Fapi%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwhitelist%2Froute.ts&appDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjoe%2Fkeith%2Fakas%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();