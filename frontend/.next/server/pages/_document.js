"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nconst _excluded = [\"strategy\", \"src\", \"children\", \"dangerouslySetInnerHTML\"],\n      _excluded2 = [\"strategy\"],\n      _excluded3 = [\"crossOrigin\", \"nonce\"],\n      _excluded4 = [\"strategy\", \"children\", \"dangerouslySetInnerHTML\", \"src\"];\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\n\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\n\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\n\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\n\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\n\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\n\nvar _htmlContext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\n\nclass Document extends _react.default.Component {\n  /**\n  * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n  * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n  */\n  static getInitialProps(ctx) {\n    return ctx.defaultGetInitialProps(ctx);\n  }\n\n  render() {\n    return /*#__PURE__*/_react.default.createElement(Html, null, /*#__PURE__*/_react.default.createElement(Head, null), /*#__PURE__*/_react.default.createElement(\"body\", null, /*#__PURE__*/_react.default.createElement(Main, null), /*#__PURE__*/_react.default.createElement(NextScript, null)));\n  }\n\n}\n\nexports[\"default\"] = Document;\n\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\nfunction _getRequireWildcardCache() {\n  if (typeof WeakMap !== \"function\") return null;\n  var cache = new WeakMap();\n\n  _getRequireWildcardCache = function () {\n    return cache;\n  };\n\n  return cache;\n}\n\nfunction _interopRequireWildcard(obj) {\n  if (obj && obj.__esModule) {\n    return obj;\n  }\n\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n\n  var cache = _getRequireWildcardCache();\n\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n  for (var key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n\n  newObj.default = obj;\n\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n\n  return newObj;\n}\n\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n  const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, \"/_app\");\n  const pageFiles =  true && inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n  return {\n    sharedFiles,\n    pageFiles,\n    allFiles: [...new Set([...sharedFiles, ...pageFiles])]\n  };\n}\n\nfunction getPolyfillScripts(context, props) {\n  // polyfills.js has to be rendered as nomodule without async\n  // It also has to be the first script to load\n  const {\n    assetPrefix,\n    buildManifest,\n    devOnlyCacheBusterQueryString,\n    disableOptimizedLoading,\n    crossOrigin\n  } = context;\n  return buildManifest.polyfillFiles.filter(polyfill => polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map(polyfill => /*#__PURE__*/_react.default.createElement(\"script\", {\n    key: polyfill,\n    defer: !disableOptimizedLoading,\n    nonce: props.nonce,\n    crossOrigin: props.crossOrigin || crossOrigin,\n    noModule: true,\n    src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n  }));\n}\n\nfunction hasComponentProps(child) {\n  return !!child && !!child.props;\n}\n\nfunction AmpStyles({\n  styles\n}) {\n  if (!styles) return null; // try to parse styles from fragment for backwards compat\n\n  const curStyles = Array.isArray(styles) ? styles : [];\n\n  if ( // @ts-ignore Property 'props' does not exist on type ReactElement\n  styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n  Array.isArray(styles.props.children)) {\n    const hasStyles = el => {\n      var ref, ref1;\n      return el == null ? void 0 : (ref = el.props) == null ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref1.__html;\n    }; // @ts-ignore Property 'props' does not exist on type ReactElement\n\n\n    styles.props.children.forEach(child => {\n      if (Array.isArray(child)) {\n        child.forEach(el => hasStyles(el) && curStyles.push(el));\n      } else if (hasStyles(child)) {\n        curStyles.push(child);\n      }\n    });\n  }\n  /* Add custom styles before AMP styles to prevent accidental overrides */\n\n\n  return /*#__PURE__*/_react.default.createElement(\"style\", {\n    \"amp-custom\": \"\",\n    dangerouslySetInnerHTML: {\n      __html: curStyles.map(style => style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n    }\n  });\n}\n\nfunction getDynamicChunks(context, props, files) {\n  const {\n    dynamicImports,\n    assetPrefix,\n    isDevelopment,\n    devOnlyCacheBusterQueryString,\n    disableOptimizedLoading,\n    crossOrigin\n  } = context;\n  return dynamicImports.map(file => {\n    if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n    return /*#__PURE__*/_react.default.createElement(\"script\", {\n      async: !isDevelopment && disableOptimizedLoading,\n      defer: !disableOptimizedLoading,\n      key: file,\n      src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n      nonce: props.nonce,\n      crossOrigin: props.crossOrigin || crossOrigin\n    });\n  });\n}\n\nfunction getScripts(context, props, files) {\n  var ref;\n  const {\n    assetPrefix,\n    buildManifest,\n    isDevelopment,\n    devOnlyCacheBusterQueryString,\n    disableOptimizedLoading,\n    crossOrigin\n  } = context;\n  const normalScripts = files.allFiles.filter(file => file.endsWith(\".js\"));\n  const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) == null ? void 0 : ref.filter(file => file.endsWith(\".js\"));\n  return [...normalScripts, ...lowPriorityScripts].map(file => {\n    return /*#__PURE__*/_react.default.createElement(\"script\", {\n      key: file,\n      src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n      nonce: props.nonce,\n      async: !isDevelopment && disableOptimizedLoading,\n      defer: !disableOptimizedLoading,\n      crossOrigin: props.crossOrigin || crossOrigin\n    });\n  });\n}\n\nfunction getPreNextWorkerScripts(context, props) {\n  const {\n    assetPrefix,\n    scriptLoader,\n    crossOrigin,\n    nextScriptWorkers\n  } = context; // disable `nextScriptWorkers` in edge runtime\n\n  if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n\n  try {\n    let {\n      partytownSnippet\n    } = require(\"@builder.io/partytown/integration\");\n\n    const children = Array.isArray(props.children) ? props.children : [props.children]; // Check to see if the user has defined their own Partytown configuration\n\n    const userDefinedConfig = children.find(child => {\n      var ref, ref2;\n      return hasComponentProps(child) && (child == null ? void 0 : (ref = child.props) == null ? void 0 : (ref2 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref2.__html.length) && \"data-partytown-config\" in child.props;\n    });\n    return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/_react.default.createElement(\"script\", {\n      \"data-partytown-config\": \"\",\n      dangerouslySetInnerHTML: {\n        __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n      }\n    }), /*#__PURE__*/_react.default.createElement(\"script\", {\n      \"data-partytown\": \"\",\n      dangerouslySetInnerHTML: {\n        __html: partytownSnippet()\n      }\n    }), (scriptLoader.worker || []).map((file, index) => {\n      const {\n        strategy,\n        src,\n        children: scriptChildren,\n        dangerouslySetInnerHTML\n      } = file,\n            scriptProps = _objectWithoutProperties(file, _excluded);\n\n      let srcProps = {};\n\n      if (src) {\n        // Use external src if provided\n        srcProps.src = src;\n      } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n        // Embed inline script if provided with dangerouslySetInnerHTML\n        srcProps.dangerouslySetInnerHTML = {\n          __html: dangerouslySetInnerHTML.__html\n        };\n      } else if (scriptChildren) {\n        // Embed inline script if provided with children\n        srcProps.dangerouslySetInnerHTML = {\n          __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n        };\n      } else {\n        throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n      }\n\n      return /*#__PURE__*/_react.default.createElement(\"script\", Object.assign({}, srcProps, scriptProps, {\n        type: \"text/partytown\",\n        key: src || index,\n        nonce: props.nonce,\n        \"data-nscript\": \"worker\",\n        crossOrigin: props.crossOrigin || crossOrigin\n      }));\n    }));\n  } catch (err) {\n    if ((0, _isError).default(err) && err.code !== \"MODULE_NOT_FOUND\") {\n      console.warn(`Warning: ${err.message}`);\n    }\n\n    return null;\n  }\n}\n\nfunction getPreNextScripts(context, props) {\n  const {\n    scriptLoader,\n    disableOptimizedLoading,\n    crossOrigin\n  } = context;\n  const webWorkerScripts = getPreNextWorkerScripts(context, props);\n  const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter(script => script.src).map((file, index) => {\n    const {\n      strategy\n    } = file,\n          scriptProps = _objectWithoutProperties(file, _excluded2);\n\n    var _defer;\n\n    return /*#__PURE__*/_react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n      key: scriptProps.src || index,\n      defer: (_defer = scriptProps.defer) != null ? _defer : !disableOptimizedLoading,\n      nonce: props.nonce,\n      \"data-nscript\": \"beforeInteractive\",\n      crossOrigin: props.crossOrigin || crossOrigin\n    }));\n  });\n  return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\n\nfunction getHeadHTMLProps(props) {\n  const {\n    crossOrigin,\n    nonce\n  } = props,\n        restProps = _objectWithoutProperties(props, _excluded3); // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n\n\n  const headProps = restProps;\n  return headProps;\n}\n\nfunction getAmpPath(ampPath, asPath) {\n  return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\n\nclass Head extends _react.default.Component {\n  getCssLinks(files) {\n    const {\n      assetPrefix,\n      devOnlyCacheBusterQueryString,\n      dynamicImports,\n      crossOrigin,\n      optimizeCss,\n      optimizeFonts\n    } = this.context;\n    const cssFiles = files.allFiles.filter(f => f.endsWith(\".css\"));\n    const sharedFiles = new Set(files.sharedFiles); // Unmanaged files are CSS files that will be handled directly by the\n    // webpack runtime (`mini-css-extract-plugin`).\n\n    let unmangedFiles = new Set([]);\n    let dynamicCssFiles = Array.from(new Set(dynamicImports.filter(file => file.endsWith(\".css\"))));\n\n    if (dynamicCssFiles.length) {\n      const existing = new Set(cssFiles);\n      dynamicCssFiles = dynamicCssFiles.filter(f => !(existing.has(f) || sharedFiles.has(f)));\n      unmangedFiles = new Set(dynamicCssFiles);\n      cssFiles.push(...dynamicCssFiles);\n    }\n\n    let cssLinkElements = [];\n    cssFiles.forEach(file => {\n      const isSharedFile = sharedFiles.has(file);\n\n      if (!optimizeCss) {\n        cssLinkElements.push( /*#__PURE__*/_react.default.createElement(\"link\", {\n          key: `${file}-preload`,\n          nonce: this.props.nonce,\n          rel: \"preload\",\n          href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n          as: \"style\",\n          crossOrigin: this.props.crossOrigin || crossOrigin\n        }));\n      }\n\n      const isUnmanagedFile = unmangedFiles.has(file);\n      cssLinkElements.push( /*#__PURE__*/_react.default.createElement(\"link\", {\n        key: file,\n        nonce: this.props.nonce,\n        rel: \"stylesheet\",\n        href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n        crossOrigin: this.props.crossOrigin || crossOrigin,\n        \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n        \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n      }));\n    });\n\n    if (false) {}\n\n    return cssLinkElements.length === 0 ? null : cssLinkElements;\n  }\n\n  getPreloadDynamicChunks() {\n    const {\n      dynamicImports,\n      assetPrefix,\n      devOnlyCacheBusterQueryString,\n      crossOrigin\n    } = this.context;\n    return dynamicImports.map(file => {\n      if (!file.endsWith(\".js\")) {\n        return null;\n      }\n\n      return /*#__PURE__*/_react.default.createElement(\"link\", {\n        rel: \"preload\",\n        key: file,\n        href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n        as: \"script\",\n        nonce: this.props.nonce,\n        crossOrigin: this.props.crossOrigin || crossOrigin\n      });\n    }) // Filter out nulled scripts\n    .filter(Boolean);\n  }\n\n  getPreloadMainLinks(files) {\n    const {\n      assetPrefix,\n      devOnlyCacheBusterQueryString,\n      scriptLoader,\n      crossOrigin\n    } = this.context;\n    const preloadFiles = files.allFiles.filter(file => {\n      return file.endsWith(\".js\");\n    });\n    return [...(scriptLoader.beforeInteractive || []).map(file => /*#__PURE__*/_react.default.createElement(\"link\", {\n      key: file.src,\n      nonce: this.props.nonce,\n      rel: \"preload\",\n      href: file.src,\n      as: \"script\",\n      crossOrigin: this.props.crossOrigin || crossOrigin\n    })), ...preloadFiles.map(file => /*#__PURE__*/_react.default.createElement(\"link\", {\n      key: file,\n      nonce: this.props.nonce,\n      rel: \"preload\",\n      href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n      as: \"script\",\n      crossOrigin: this.props.crossOrigin || crossOrigin\n    }))];\n  }\n\n  getBeforeInteractiveInlineScripts() {\n    const {\n      scriptLoader\n    } = this.context;\n    const {\n      nonce,\n      crossOrigin\n    } = this.props;\n    return (scriptLoader.beforeInteractive || []).filter(script => !script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index) => {\n      const {\n        strategy,\n        children,\n        dangerouslySetInnerHTML,\n        src\n      } = file,\n            scriptProps = _objectWithoutProperties(file, _excluded4);\n\n      let html = \"\";\n\n      if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n        html = dangerouslySetInnerHTML.__html;\n      } else if (children) {\n        html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n      }\n\n      return /*#__PURE__*/_react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n        dangerouslySetInnerHTML: {\n          __html: html\n        },\n        key: scriptProps.id || index,\n        nonce: nonce,\n        \"data-nscript\": \"beforeInteractive\",\n        crossOrigin: crossOrigin || undefined\n      }));\n    });\n  }\n\n  getDynamicChunks(files) {\n    return getDynamicChunks(this.context, this.props, files);\n  }\n\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props);\n  }\n\n  getScripts(files) {\n    return getScripts(this.context, this.props, files);\n  }\n\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props);\n  }\n\n  makeStylesheetInert(node) {\n    return _react.default.Children.map(node, c => {\n      var ref5, ref3;\n\n      if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (ref5 = c.props) == null ? void 0 : ref5.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({\n        url\n      }) => {\n        var ref, ref4;\n        return c == null ? void 0 : (ref = c.props) == null ? void 0 : (ref4 = ref.href) == null ? void 0 : ref4.startsWith(url);\n      })) {\n        const newProps = _objectSpread(_objectSpread({}, c.props || {}), {}, {\n          \"data-href\": c.props.href,\n          href: undefined\n        });\n\n        return /*#__PURE__*/_react.default.cloneElement(c, newProps);\n      } else if (c == null ? void 0 : (ref3 = c.props) == null ? void 0 : ref3.children) {\n        const newProps = _objectSpread(_objectSpread({}, c.props || {}), {}, {\n          children: this.makeStylesheetInert(c.props.children)\n        });\n\n        return /*#__PURE__*/_react.default.cloneElement(c, newProps);\n      }\n\n      return c;\n    }).filter(Boolean);\n  }\n\n  render() {\n    const {\n      styles,\n      ampPath,\n      inAmpMode,\n      hybridAmp,\n      canonicalBase,\n      __NEXT_DATA__,\n      dangerousAsPath,\n      headTags,\n      unstable_runtimeJS,\n      unstable_JsPreload,\n      disableOptimizedLoading,\n      optimizeCss,\n      optimizeFonts\n    } = this.context;\n    const disableRuntimeJS = unstable_runtimeJS === false;\n    const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n    this.context.docComponentsRendered.Head = true;\n    let {\n      head\n    } = this.context;\n    let cssPreloads = [];\n    let otherHeadElements = [];\n\n    if (head) {\n      head.forEach(c => {\n        if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n          cssPreloads.push(c);\n        } else {\n          c && otherHeadElements.push(c);\n        }\n      });\n      head = cssPreloads.concat(otherHeadElements);\n    }\n\n    let children = _react.default.Children.toArray(this.props.children).filter(Boolean); // show a warning if Head contains <title> (only in development)\n\n\n    if (true) {\n      children = _react.default.Children.map(children, child => {\n        var ref;\n        const isReactHelmet = child == null ? void 0 : (ref = child.props) == null ? void 0 : ref[\"data-react-helmet\"];\n\n        if (!isReactHelmet) {\n          var ref6;\n\n          if ((child == null ? void 0 : child.type) === \"title\") {\n            console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n          } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (ref6 = child.props) == null ? void 0 : ref6.name) === \"viewport\") {\n            console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n          }\n        }\n\n        return child;\n      });\n      if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n    }\n\n    if (false) {}\n\n    let hasAmphtmlRel = false;\n    let hasCanonicalRel = false; // show warning and remove conflicting amp head tags\n\n    head = _react.default.Children.map(head || [], child => {\n      if (!child) return child;\n      const {\n        type,\n        props\n      } = child;\n\n      if ( true && inAmpMode) {\n        let badProp = \"\";\n\n        if (type === \"meta\" && props.name === \"viewport\") {\n          badProp = 'name=\"viewport\"';\n        } else if (type === \"link\" && props.rel === \"canonical\") {\n          hasCanonicalRel = true;\n        } else if (type === \"script\") {\n          // only block if\n          // 1. it has a src and isn't pointing to ampproject's CDN\n          // 2. it is using dangerouslySetInnerHTML without a type or\n          // a type of text/javascript\n          if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n            badProp = \"<script\";\n            Object.keys(props).forEach(prop => {\n              badProp += ` ${prop}=\"${props[prop]}\"`;\n            });\n            badProp += \"/>\";\n          }\n        }\n\n        if (badProp) {\n          console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n          return null;\n        }\n      } else {\n        // non-amp mode\n        if (type === \"link\" && props.rel === \"amphtml\") {\n          hasAmphtmlRel = true;\n        }\n      }\n\n      return child;\n    });\n    const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n\n    var _nonce, _nonce1;\n\n    return /*#__PURE__*/_react.default.createElement(\"head\", Object.assign({}, getHeadHTMLProps(this.props)), this.context.isDevelopment && /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/_react.default.createElement(\"style\", {\n      \"data-next-hide-fouc\": true,\n      \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n      dangerouslySetInnerHTML: {\n        __html: `body{display:none}`\n      }\n    }), /*#__PURE__*/_react.default.createElement(\"noscript\", {\n      \"data-next-hide-fouc\": true,\n      \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n    }, /*#__PURE__*/_react.default.createElement(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: `body{display:block}`\n      }\n    }))), head, /*#__PURE__*/_react.default.createElement(\"meta\", {\n      name: \"next-head-count\",\n      content: _react.default.Children.count(head || []).toString()\n    }), children, optimizeFonts && /*#__PURE__*/_react.default.createElement(\"meta\", {\n      name: \"next-font-preconnect\"\n    }),  true && inAmpMode && /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/_react.default.createElement(\"meta\", {\n      name: \"viewport\",\n      content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n    }), !hasCanonicalRel && /*#__PURE__*/_react.default.createElement(\"link\", {\n      rel: \"canonical\",\n      href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n    }), /*#__PURE__*/_react.default.createElement(\"link\", {\n      rel: \"preload\",\n      as: \"script\",\n      href: \"https://cdn.ampproject.org/v0.js\"\n    }), /*#__PURE__*/_react.default.createElement(AmpStyles, {\n      styles: styles\n    }), /*#__PURE__*/_react.default.createElement(\"style\", {\n      \"amp-boilerplate\": \"\",\n      dangerouslySetInnerHTML: {\n        __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n      }\n    }), /*#__PURE__*/_react.default.createElement(\"noscript\", null, /*#__PURE__*/_react.default.createElement(\"style\", {\n      \"amp-boilerplate\": \"\",\n      dangerouslySetInnerHTML: {\n        __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n      }\n    })), /*#__PURE__*/_react.default.createElement(\"script\", {\n      async: true,\n      src: \"https://cdn.ampproject.org/v0.js\"\n    })), !( true && inAmpMode) && /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/_react.default.createElement(\"link\", {\n      rel: \"amphtml\",\n      href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n    }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/_react.default.createElement(\"noscript\", {\n      \"data-n-css\": (_nonce = this.props.nonce) != null ? _nonce : \"\"\n    }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/_react.default.createElement(\"noscript\", {\n      \"data-n-css\": (_nonce1 = this.props.nonce) != null ? _nonce1 : \"\"\n    }), this.context.isDevelopment && // this element is used to mount development styles so the\n    // ordering matches production\n    // (by default, style-loader injects at the bottom of <head />)\n\n    /*#__PURE__*/\n    _react.default.createElement(\"noscript\", {\n      id: \"__next_css__DO_NOT_USE__\"\n    }), styles || null), /*#__PURE__*/_react.default.createElement(_react.default.Fragment, {}, ...(headTags || [])));\n  }\n\n}\n\n_defineProperty(Head, \"contextType\", _htmlContext.HtmlContext);\n\nexports.Head = Head;\n\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n  var ref10, ref7, ref8, ref9;\n  if (!props.children) return;\n  const scriptLoaderItems = [];\n  const children = Array.isArray(props.children) ? props.children : [props.children];\n  const headChildren = (ref10 = children.find(child => child.type === Head)) == null ? void 0 : (ref7 = ref10.props) == null ? void 0 : ref7.children;\n  const bodyChildren = (ref8 = children.find(child => child.type === \"body\")) == null ? void 0 : (ref9 = ref8.props) == null ? void 0 : ref9.children; // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n\n  const combinedChildren = [...(Array.isArray(headChildren) ? headChildren : [headChildren]), ...(Array.isArray(bodyChildren) ? bodyChildren : [bodyChildren])];\n\n  _react.default.Children.forEach(combinedChildren, child => {\n    var ref;\n    if (!child) return; // When using the `next/script` component, register it in script loader.\n\n    if ((ref = child.type) == null ? void 0 : ref.__nextScript) {\n      if (child.props.strategy === \"beforeInteractive\") {\n        scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([_objectSpread({}, child.props)]);\n        return;\n      } else if ([\"lazyOnload\", \"afterInteractive\", \"worker\"].includes(child.props.strategy)) {\n        scriptLoaderItems.push(child.props);\n        return;\n      }\n    }\n  });\n\n  __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\n\nclass NextScript extends _react.default.Component {\n  getDynamicChunks(files) {\n    return getDynamicChunks(this.context, this.props, files);\n  }\n\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props);\n  }\n\n  getScripts(files) {\n    return getScripts(this.context, this.props, files);\n  }\n\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props);\n  }\n\n  static getInlineScriptSource(context) {\n    const {\n      __NEXT_DATA__,\n      largePageDataBytes\n    } = context;\n\n    try {\n      const data = JSON.stringify(__NEXT_DATA__);\n      const bytes =  false ? 0 : Buffer.from(data).byteLength;\n\n      const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n\n      if (largePageDataBytes && bytes > largePageDataBytes) {\n        console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n      }\n\n      return (0, _htmlescape).htmlEscapeJsonString(data);\n    } catch (err) {\n      if ((0, _isError).default(err) && err.message.indexOf(\"circular structure\") !== -1) {\n        throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n      }\n\n      throw err;\n    }\n  }\n\n  render() {\n    const {\n      assetPrefix,\n      inAmpMode,\n      buildManifest,\n      unstable_runtimeJS,\n      docComponentsRendered,\n      devOnlyCacheBusterQueryString,\n      disableOptimizedLoading,\n      crossOrigin\n    } = this.context;\n    const disableRuntimeJS = unstable_runtimeJS === false;\n    docComponentsRendered.NextScript = true;\n\n    if ( true && inAmpMode) {\n      if (false) {}\n\n      const ampDevFiles = [...buildManifest.devFiles, ...buildManifest.polyfillFiles, ...buildManifest.ampDevFiles];\n      return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/_react.default.createElement(\"script\", {\n        id: \"__NEXT_DATA__\",\n        type: \"application/json\",\n        nonce: this.props.nonce,\n        crossOrigin: this.props.crossOrigin || crossOrigin,\n        dangerouslySetInnerHTML: {\n          __html: NextScript.getInlineScriptSource(this.context)\n        },\n        \"data-ampdevmode\": true\n      }), ampDevFiles.map(file => /*#__PURE__*/_react.default.createElement(\"script\", {\n        key: file,\n        src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n        nonce: this.props.nonce,\n        crossOrigin: this.props.crossOrigin || crossOrigin,\n        \"data-ampdevmode\": true\n      })));\n    }\n\n    if (true) {\n      if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n    }\n\n    const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n    return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map(file => /*#__PURE__*/_react.default.createElement(\"script\", {\n      key: file,\n      src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n      nonce: this.props.nonce,\n      crossOrigin: this.props.crossOrigin || crossOrigin\n    })) : null, disableRuntimeJS ? null : /*#__PURE__*/_react.default.createElement(\"script\", {\n      id: \"__NEXT_DATA__\",\n      type: \"application/json\",\n      nonce: this.props.nonce,\n      crossOrigin: this.props.crossOrigin || crossOrigin,\n      dangerouslySetInnerHTML: {\n        __html: NextScript.getInlineScriptSource(this.context)\n      }\n    }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n  }\n\n}\n\n_defineProperty(NextScript, \"contextType\", _htmlContext.HtmlContext);\n\nexports.NextScript = NextScript;\n\nfunction Html(props) {\n  const {\n    inAmpMode,\n    docComponentsRendered,\n    locale,\n    scriptLoader,\n    __NEXT_DATA__\n  } = (0, _react).useContext(_htmlContext.HtmlContext);\n  docComponentsRendered.Html = true;\n  handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n  return /*#__PURE__*/_react.default.createElement(\"html\", Object.assign({}, props, {\n    lang: props.lang || locale || undefined,\n    amp:  true && inAmpMode ? \"\" : undefined,\n    \"data-ampdevmode\":  true && inAmpMode && true ? \"\" : undefined\n  }));\n}\n\nfunction Main() {\n  const {\n    docComponentsRendered\n  } = (0, _react).useContext(_htmlContext.HtmlContext);\n  docComponentsRendered.Main = true; // @ts-ignore\n\n  return /*#__PURE__*/_react.default.createElement(\"next-js-internal-body-render-target\", null);\n} // Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\n\n\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n  return /*#__PURE__*/_react.default.createElement(Html, null, /*#__PURE__*/_react.default.createElement(Head, null), /*#__PURE__*/_react.default.createElement(\"body\", null, /*#__PURE__*/_react.default.createElement(Main, null), /*#__PURE__*/_react.default.createElement(NextScript, null)));\n};\n\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./node_modules/next/dist/pages/_document.js"));
module.exports = __webpack_exports__;

})();