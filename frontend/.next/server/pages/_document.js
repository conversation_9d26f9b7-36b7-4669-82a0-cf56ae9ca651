"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Head: function() {\n        return Head;\n    },\n    Html: function() {\n        return Html;\n    },\n    Main: function() {\n        return Main;\n    },\n    NextScript: function() {\n        return NextScript;\n    },\n    /**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */ default: function() {\n        return Document;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nconst _getpagefiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nconst _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nconst _iserror = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _htmlcontextsharedruntime = __webpack_require__(/*! ../shared/lib/html-context.shared-runtime */ \"../shared/lib/html-context.shared-runtime\");\nconst _encodeuripath = __webpack_require__(/*! ../shared/lib/encode-uri-path */ \"../shared/lib/encode-uri-path\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\n/** Set of pages that have triggered a large data warning on production mode. */ const largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getpagefiles.getPageFiles)(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getpagefiles.getPageFiles)(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , assetQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(polyfill)}${assetQueryString}`\n        }, polyfill));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var _el_props_dangerouslySetInnerHTML, _el_props;\n            return el == null ? void 0 : (_el_props = el.props) == null ? void 0 : (_el_props_dangerouslySetInnerHTML = _el_props.dangerouslySetInnerHTML) == null ? void 0 : _el_props_dangerouslySetInnerHTML.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , assetQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        }, file);\n    });\n}\nfunction getScripts(context, props, files) {\n    var _buildManifest_lowPriorityFiles;\n    const { assetPrefix , buildManifest , isDevelopment , assetQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (_buildManifest_lowPriorityFiles = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest_lowPriorityFiles.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        }, file);\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix , scriptLoader , crossOrigin , nextScriptWorkers  } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet  } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var _child_props_dangerouslySetInnerHTML, _child_props;\n            return hasComponentProps(child) && (child == null ? void 0 : (_child_props = child.props) == null ? void 0 : (_child_props_dangerouslySetInnerHTML = _child_props.dangerouslySetInnerHTML) == null ? void 0 : _child_props_dangerouslySetInnerHTML.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                !userDefinedConfig && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    \"data-partytown-config\": \"\",\n                    dangerouslySetInnerHTML: {\n                        __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n                    }\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    \"data-partytown\": \"\",\n                    dangerouslySetInnerHTML: {\n                        __html: partytownSnippet()\n                    }\n                }),\n                (scriptLoader.worker || []).map((file, index)=>{\n                    const { strategy , src , children: scriptChildren , dangerouslySetInnerHTML , ...scriptProps } = file;\n                    let srcProps = {};\n                    if (src) {\n                        // Use external src if provided\n                        srcProps.src = src;\n                    } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                        // Embed inline script if provided with dangerouslySetInnerHTML\n                        srcProps.dangerouslySetInnerHTML = {\n                            __html: dangerouslySetInnerHTML.__html\n                        };\n                    } else if (scriptChildren) {\n                        // Embed inline script if provided with children\n                        srcProps.dangerouslySetInnerHTML = {\n                            __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                        };\n                    } else {\n                        throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n                    }\n                    return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n                        ...srcProps,\n                        ...scriptProps,\n                        type: \"text/partytown\",\n                        key: src || index,\n                        nonce: props.nonce,\n                        \"data-nscript\": \"worker\",\n                        crossOrigin: props.crossOrigin || crossOrigin\n                    });\n                })\n            ]\n        });\n    } catch (err) {\n        if ((0, _iserror.default)(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n            ...scriptProps,\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            webWorkerScripts,\n            beforeInteractiveScripts\n        ]\n    });\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin , nonce , ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nfunction getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix = \"\") {\n    if (!nextFontManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = nextFontManifest.pages[\"/_app\"];\n    const pageFontsEntry = nextFontManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = Array.from(new Set([\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ]));\n    // If no font files should preload but there's an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            \"data-next-font\": nextFontManifest.pagesUsingSizeAdjust ? \"size-adjust\" : \"\",\n            rel: \"preconnect\",\n            href: \"/\",\n            crossOrigin: \"anonymous\"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(fontFile)}`,\n                as: \"font\",\n                type: `font/${ext}`,\n                crossOrigin: \"anonymous\",\n                \"data-next-font\": fontFile.includes(\"-s\") ? \"size-adjust\" : \"\"\n            }, fontFile);\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix , assetQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts  } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, `${file}-preload`));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }, file));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , assetQueryString , crossOrigin  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            }, file);\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix , assetQueryString , scriptLoader , crossOrigin  } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, file.src)),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, file))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader  } = this.context;\n        const { nonce , crossOrigin  } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy , children , dangerouslySetInnerHTML , src , ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n                ...scriptProps,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            });\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var _c_props, _c_props1;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (_c_props = c.props) == null ? void 0 : _c_props.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>{\n                var _c_props_href, _c_props;\n                return c == null ? void 0 : (_c_props = c.props) == null ? void 0 : (_c_props_href = _c_props.href) == null ? void 0 : _c_props_href.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (_c_props1 = c.props) == null ? void 0 : _c_props1.children) {\n                const newProps1 = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps1);\n            }\n            return c;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , optimizeCss , optimizeFonts , assetPrefix , nextFontManifest  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                let metaTag;\n                if (this.context.strictNextHead) {\n                    metaTag = /*#__PURE__*/ _react.default.createElement(\"meta\", {\n                        name: \"next-head\",\n                        content: \"1\"\n                    });\n                }\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    metaTag && cssPreloads.push(metaTag);\n                    cssPreloads.push(c);\n                } else {\n                    if (c) {\n                        if (metaTag && (c.type !== \"meta\" || !c.props[\"charSet\"])) {\n                            otherHeadElements.push(metaTag);\n                        }\n                        otherHeadElements.push(c);\n                    }\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var _child_props;\n                const isReactHelmet = child == null ? void 0 : (_child_props = child.props) == null ? void 0 : _child_props[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var _child_props1;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (_child_props1 = child.props) == null ? void 0 : _child_props1.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const nextFontLinkTags = getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix);\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"head\", {\n            ...getHeadHTMLProps(this.props),\n            children: [\n                this.context.isDevelopment && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            \"data-next-hide-fouc\": true,\n                            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n                            dangerouslySetInnerHTML: {\n                                __html: `body{display:none}`\n                            }\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-next-hide-fouc\": true,\n                            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: `body{display:block}`\n                                }\n                            })\n                        })\n                    ]\n                }),\n                head,\n                this.context.strictNextHead ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                    name: \"next-head-count\",\n                    content: _react.default.Children.count(head || []).toString()\n                }),\n                children,\n                optimizeFonts && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                    name: \"next-font-preconnect\"\n                }),\n                nextFontLinkTags.preconnect,\n                nextFontLinkTags.preload,\n                 true && inAmpMode && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n                        }),\n                        !hasCanonicalRel && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"canonical\",\n                            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"preload\",\n                            as: \"script\",\n                            href: \"https://cdn.ampproject.org/v0.js\"\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AmpStyles, {\n                            styles: styles\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            \"amp-boilerplate\": \"\",\n                            dangerouslySetInnerHTML: {\n                                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n                            }\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                                \"amp-boilerplate\": \"\",\n                                dangerouslySetInnerHTML: {\n                                    __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n                                }\n                            })\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                            async: true,\n                            src: \"https://cdn.ampproject.org/v0.js\"\n                        })\n                    ]\n                }),\n                !( true && inAmpMode) && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"amphtml\",\n                            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n                        }),\n                        this.getBeforeInteractiveInlineScripts(),\n                        !optimizeCss && this.getCssLinks(files),\n                        !optimizeCss && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-n-css\": this.props.nonce ?? \"\"\n                        }),\n                        !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(),\n                        !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files),\n                        optimizeCss && this.getCssLinks(files),\n                        optimizeCss && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-n-css\": this.props.nonce ?? \"\"\n                        }),\n                        this.context.isDevelopment && // this element is used to mount development styles so the\n                        // ordering matches production\n                        // (by default, style-loader injects at the bottom of <head />)\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            id: \"__next_css__DO_NOT_USE__\"\n                        }),\n                        styles || null\n                    ]\n                }),\n                /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || [])\n            ]\n        });\n    }\n}\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var _children_find_props, _children_find, _children_find_props1, _children_find1;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (_children_find = children.find((child)=>child.type === Head)) == null ? void 0 : (_children_find_props = _children_find.props) == null ? void 0 : _children_find_props.children;\n    const bodyChildren = (_children_find1 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (_children_find_props1 = _children_find1.props) == null ? void 0 : _children_find_props1.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var _child_type;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((_child_type = child.type) == null ? void 0 : _child_type.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__ , largePageDataBytes  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n                return (0, _htmlescape.htmlEscapeJsonString)(data);\n            }\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                if (false) {}\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape.htmlEscapeJsonString)(data);\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , assetQueryString , disableOptimizedLoading , crossOrigin  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    disableRuntimeJS ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                        id: \"__NEXT_DATA__\",\n                        type: \"application/json\",\n                        nonce: this.props.nonce,\n                        crossOrigin: this.props.crossOrigin || crossOrigin,\n                        dangerouslySetInnerHTML: {\n                            __html: NextScript.getInlineScriptSource(this.context)\n                        },\n                        \"data-ampdevmode\": true\n                    }),\n                    ampDevFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                            nonce: this.props.nonce,\n                            crossOrigin: this.props.crossOrigin || crossOrigin,\n                            \"data-ampdevmode\": true\n                        }, file))\n                ]\n            });\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                        src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                        nonce: this.props.nonce,\n                        crossOrigin: this.props.crossOrigin || crossOrigin\n                    }, file)) : null,\n                disableRuntimeJS ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    id: \"__NEXT_DATA__\",\n                    type: \"application/json\",\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    dangerouslySetInnerHTML: {\n                        __html: NextScript.getInlineScriptSource(this.context)\n                    }\n                }),\n                disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(),\n                disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(),\n                disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files),\n                disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)\n            ]\n        });\n    }\n}\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale , scriptLoader , __NEXT_DATA__  } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"html\", {\n        ...props,\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    });\n}\nfunction Main() {\n    const { docComponentsRendered  } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"next-js-internal-body-render-target\", {});\n}\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(Html, {\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {}),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(Main, {}),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(NextScript, {})\n                    ]\n                })\n            ]\n        });\n    }\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(Html, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(Main, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(NextScript, {})\n                ]\n            })\n        ]\n    });\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return isError;\n    },\n    getProperError: function() {\n        return getProperError;\n    }\n});\nconst _isplainobject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isplainobject.isPlainObject)(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return prettyBytes;\n    }\n}));\nconst UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/encode-uri-path":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/encode-uri-path.js" ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/encode-uri-path.js");

/***/ }),

/***/ "../shared/lib/html-context.shared-runtime":
/*!**********************************************************************!*\
  !*** external "next/dist/shared/lib/html-context.shared-runtime.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/html-context.shared-runtime.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./node_modules/next/dist/pages/_document.js"));
module.exports = __webpack_exports__;

})();