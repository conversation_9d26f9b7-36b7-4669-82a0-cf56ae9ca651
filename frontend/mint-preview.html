<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AkashaDao - NFT铸造页面预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen relative overflow-hidden">
    <!-- AI生成的背景 -->
    <div class="absolute inset-0">
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40" 
             style="background-image: url('public/images/mint-background.webp'); background-blend-mode: overlay;"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
        
        <!-- 装饰元素 -->
        <div class="absolute top-20 right-10 w-32 h-32 opacity-20 animate-float" 
             style="background-image: url('public/images/nft-card-decoration.webp'); background-size: contain; background-repeat: no-repeat;"></div>
        <div class="absolute bottom-20 left-10 w-24 h-24 opacity-15 animate-pulse" 
             style="background-image: url('public/images/geometric-decoration.webp'); background-size: contain; background-repeat: no-repeat; transform: rotate(45deg);"></div>
        
        <!-- 侧边装饰 -->
        <div class="absolute left-0 top-1/2 transform -translate-y-1/2 w-48 h-64 opacity-10" 
             style="background-image: url('public/images/mint-sidebar-decoration.webp'); background-size: contain; background-repeat: no-repeat;"></div>
    </div>

    <!-- Header -->
    <header class="relative z-10 flex justify-between items-center p-6 bg-slate-800/30 backdrop-blur-md border-b border-slate-700/50">
        <!-- 统一的Logo设计 -->
        <a href="homepage-preview.html" class="hover:opacity-80 transition-opacity">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105">
                    <span class="text-white font-bold text-lg">A</span>
                </div>
                <div class="flex flex-col">
                    <h1 class="text-xl font-bold text-white leading-tight">AkashaDao</h1>
                </div>
            </div>
        </a>
        
        <button class="px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-medium transition-all transform hover:scale-105">
            连接钱包
        </button>
    </header>

    <!-- Main Content -->
    <div class="relative z-10 container mx-auto px-6 py-12">
        <div class="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <!-- Left Side - NFT Preview -->
            <div class="space-y-6">
                <!-- NFT预览卡片 -->
                <div class="relative">
                    <div class="aspect-square bg-gradient-to-br from-orange-400 to-yellow-500 rounded-3xl shadow-2xl overflow-hidden relative">
                        <!-- NFT内容 -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="w-32 h-32 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                                    <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
                                </div>
                                <div class="bg-black/30 backdrop-blur-sm rounded-lg p-4 mx-4">
                                    <div class="text-lg font-bold">AkashaDao Pass</div>
                                    <div class="text-sm opacity-80">Whitelist Access</div>
                                </div>
                            </div>
                        </div>
                        <!-- WHITELIST标签 -->
                        <div class="absolute top-4 right-4 bg-amber-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                            WHITELIST
                        </div>
                    </div>
                </div>

                <!-- Benefits Section -->
                <div class="bg-slate-800/30 backdrop-blur-md rounded-2xl p-6 border border-slate-700/50 shadow-2xl hover:shadow-purple-500/10 transition-all duration-300">
                    <h3 class="text-lg font-bold text-white mb-4">Whitelist Holder Benefits</h3>
                    <div class="space-y-3">
                        <div class="flex items-center text-sm text-slate-300">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            投票权和治理参与
                        </div>
                        <div class="flex items-center text-sm text-slate-300">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            无限制内容访问权限
                        </div>
                        <div class="flex items-center text-sm text-slate-300">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            优先客户支持
                        </div>
                        <div class="flex items-center text-sm text-slate-300">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            独家活动和空投
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Mint Interface -->
            <div class="space-y-6">
                <div class="bg-slate-800/30 backdrop-blur-md rounded-2xl p-8 border border-slate-700/50 shadow-2xl hover:shadow-purple-500/10 transition-all duration-300">
                    <h2 class="text-2xl font-bold text-white mb-6">AKASHA DAO PASS</h2>
                    
                    <!-- Tier Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-slate-300 mb-3">Status:</label>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r from-yellow-500 to-orange-500 text-black">
                                Whitelist
                            </button>
                            <button class="px-4 py-2 rounded-lg text-sm font-medium bg-slate-700 text-slate-300 hover:bg-slate-600 transition-colors">
                                Student
                            </button>
                        </div>
                    </div>

                    <!-- Price Display -->
                    <div class="mb-6 p-4 bg-slate-700/50 rounded-xl">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-slate-300">价格:</span>
                            <span class="text-xl font-bold text-white">0.05 ETH</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">数量:</span>
                            <div class="flex items-center space-x-2">
                                <button class="w-8 h-8 bg-slate-600 hover:bg-slate-500 rounded-lg flex items-center justify-center text-white transition-colors">-</button>
                                <span class="text-white font-medium px-4">1</span>
                                <button class="w-8 h-8 bg-slate-600 hover:bg-slate-500 rounded-lg flex items-center justify-center text-white transition-colors">+</button>
                            </div>
                        </div>
                    </div>

                    <!-- Mint Button -->
                    <button class="w-full py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-bold text-lg transition-all transform hover:scale-105 shadow-lg hover:shadow-purple-500/25">
                        铸造 NFT
                    </button>

                    <!-- Status Info -->
                    <div class="mt-4 text-center text-sm text-slate-400">
                        <p>剩余数量: 450 / 500</p>
                        <p>您的白名单状态: ✅ 已验证</p>
                    </div>
                </div>

                <!-- Phase Information -->
                <div class="bg-slate-800/30 backdrop-blur-md rounded-2xl p-6 border border-slate-700/50 shadow-2xl hover:shadow-purple-500/10 transition-all duration-300">
                    <h3 class="text-lg font-bold text-white mb-4">铸造阶段</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-300">Phase 1 (Whitelist):</span>
                            <span class="text-sm text-green-400 font-medium">进行中</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-300">Phase 2 (Student):</span>
                            <span class="text-sm text-yellow-400 font-medium">即将开始</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-300">Phase 3 (Public):</span>
                            <span class="text-sm text-slate-500 font-medium">待定</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回首页按钮 -->
    <div class="fixed bottom-6 right-6 z-20">
        <a href="homepage-preview.html" class="inline-flex items-center px-4 py-2 bg-slate-800/80 backdrop-blur-sm border border-slate-600 hover:border-purple-500 text-white rounded-lg transition-all hover:bg-purple-500/10">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回首页
        </a>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 数量调节
            const minusBtn = document.querySelector('button:has-text("-")');
            const plusBtn = document.querySelector('button:has-text("+")');
            
            // 悬停效果
            const cards = document.querySelectorAll('.bg-slate-800\\/30');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
