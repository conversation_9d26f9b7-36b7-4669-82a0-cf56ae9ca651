{"version": 3, "file": "toBlobSidecars.d.ts", "sourceRoot": "", "sources": ["../../../utils/blob/toBlobSidecars.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAA;AAC1D,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,oBAAoB,CAAA;AAC7C,OAAO,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,qBAAqB,CAAA;AACzD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAA;AACjD,OAAO,EACL,KAAK,2BAA2B,EAEjC,MAAM,yBAAyB,CAAA;AAChC,OAAO,EAAiB,KAAK,sBAAsB,EAAE,MAAM,oBAAoB,CAAA;AAC/E,OAAO,EAAE,KAAK,gBAAgB,EAAW,MAAM,cAAc,CAAA;AAE7D,KAAK,EAAE,GAAG,KAAK,GAAG,OAAO,CAAA;AAEzB,MAAM,MAAM,wBAAwB,CAClC,IAAI,SAAS,GAAG,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,EACpD,KAAK,SAAS,SAAS,GAAG,EAAE,GAAG,SAAS,SAAS,EAAE,GAAG,SAAS,GAAG,SAAS,EAC3E,EAAE,SAAS,EAAE,GACT,CAAC,KAAK,SAAS,SAAS,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC,GAC9C,CAAC,KAAK,SAAS,SAAS,SAAS,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,EAE1D,UAAU,GACN,CAAC,KAAK,SAAS,SAAS,GAAG,EAAE,GAAG,SAAS,GAAG,EAAE,GAAG,KAAK,CAAC,GACvD,CAAC,KAAK,SAAS,SAAS,SAAS,EAAE,GAAG,SAAS,SAAS,EAAE,GAAG,KAAK,CAAC,IACrE;IACF,mBAAmB;IACnB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,SAAS,CAAA;CACzB,GAAG,KAAK,CACL;IACE,oCAAoC;IACpC,IAAI,EAAE,IAAI,GAAG,GAAG,GAAG,SAAS,CAAA;IAC5B,0BAA0B;IAC1B,GAAG,EAAE,GAAG,CAAA;CACT,GACD;IACE,aAAa;IACb,KAAK,EAAE,KAAK,GAAG,SAAS,GAAG,EAAE,GAAG,SAAS,SAAS,EAAE,CAAA;IACpD,gCAAgC;IAChC,WAAW,EAAE,UAAU,GAAG,SAAS,GAAG,EAAE,GAAG,SAAS,SAAS,EAAE,CAAA;IAC/D,2BAA2B;IAC3B,MAAM,EAAE,UAAU,GAAG,SAAS,GAAG,EAAE,GAAG,SAAS,SAAS,EAAE,CAAA;CAC3D,CACJ,CAAA;AAED,MAAM,MAAM,wBAAwB,CAAC,EAAE,SAAS,EAAE,IAC9C,CAAC,EAAE,SAAS,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,GACtD,CAAC,EAAE,SAAS,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;AAElD,MAAM,MAAM,uBAAuB,GAC/B,2BAA2B,GAC3B,gBAAgB,GAChB,sBAAsB,GACtB,SAAS,CAAA;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,cAAc,CAC5B,KAAK,CAAC,IAAI,SAAS,GAAG,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,EAC1D,KAAK,CAAC,KAAK,SACP,SAAS,GAAG,EAAE,GACd,SAAS,SAAS,EAAE,GACpB,SAAS,GAAG,SAAS,EACzB,EAAE,SAAS,EAAE,GACT,CAAC,IAAI,SAAS,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,GAClC,CAAC,IAAI,SAAS,SAAS,GAAG,OAAO,GAAG,KAAK,CAAC,GAC1C,CAAC,KAAK,SAAS,SAAS,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC,GAC9C,CAAC,KAAK,SAAS,SAAS,SAAS,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,EAE1D,UAAU,EAAE,wBAAwB,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,GACpD,wBAAwB,CAAC,EAAE,CAAC,CAiB9B"}