{"version": 3, "file": "transaction.d.ts", "sourceRoot": "", "sources": ["../../errors/transaction.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAA;AACnD,OAAO,KAAK,EAAE,yBAAyB,EAAE,MAAM,sCAAsC,CAAA;AACrF,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAA;AACjD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AAC9C,OAAO,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAA;AACjD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAI9D,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AAErC,wBAAgB,WAAW,CACzB,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,KAAK,GAAG,OAAO,CAAC,UAY7E;AAED,MAAM,MAAM,oBAAoB,GAAG,gBAAgB,GAAG;IACpD,IAAI,EAAE,kBAAkB,CAAA;CACzB,CAAA;AACD,qBAAa,gBAAiB,SAAQ,SAAS;;CAU9C;AAED,MAAM,MAAM,uBAAuB,GAAG,mBAAmB,GAAG;IAC1D,IAAI,EAAE,qBAAqB,CAAA;CAC5B,CAAA;AACD,qBAAa,mBAAoB,SAAQ,SAAS;gBACpC,EAAE,CAAC,EAAE,EAAE;QAAE,CAAC,EAAE,MAAM,CAAA;KAAE;CAKjC;AAED,MAAM,MAAM,uCAAuC,GACjD,mCAAmC,GAAG;IACpC,IAAI,EAAE,qCAAqC,CAAA;CAC5C,CAAA;AACH,qBAAa,mCAAoC,SAAQ,SAAS;gBACpD,EAAE,WAAW,EAAE,EAAE;QAAE,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;KAAE;CAmBtE;AAED,MAAM,MAAM,yCAAyC,GACnD,qCAAqC,GAAG;IACtC,IAAI,EAAE,uCAAuC,CAAA;CAC9C,CAAA;AACH,qBAAa,qCAAsC,SAAQ,SAAS;IAClE,cAAc,EAAE,GAAG,CAAA;gBAEP,EAAE,cAAc,EAAE,EAAE;QAAE,cAAc,EAAE,GAAG,CAAA;KAAE;CAOxD;AAED,MAAM,MAAM,qCAAqC,GAC/C,iCAAiC,GAAG;IAClC,IAAI,EAAE,mCAAmC,CAAA;CAC1C,CAAA;AACH,qBAAa,iCAAkC,SAAQ,SAAS;IAC9D,qBAAqB,EAAE,GAAG,CAAA;IAC1B,IAAI,EAAE,eAAe,CAAA;gBAET,EACV,UAAU,EACV,qBAAqB,EACrB,IAAI,GACL,EAAE;QACD,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnC,qBAAqB,EAAE,GAAG,CAAA;QAC1B,IAAI,EAAE,eAAe,CAAA;KACtB;CAeF;AAED,MAAM,MAAM,8BAA8B,GAAG,0BAA0B,GAAG;IACxE,IAAI,EAAE,4BAA4B,CAAA;CACnC,CAAA;AACD,qBAAa,0BAA2B,SAAQ,SAAS;gBAC3C,EAAE,UAAU,EAAE,EAAE;QAAE,UAAU,EAAE,GAAG,CAAA;KAAE;CAQhD;AAED,MAAM,MAAM,6BAA6B,GAAG,yBAAyB,GAAG;IACtE,IAAI,EAAE,2BAA2B,CAAA;CAClC,CAAA;AACD,qBAAa,yBAA0B,SAAQ,SAAS;IAC7C,KAAK,EAAE,SAAS,CAAA;gBAGvB,KAAK,EAAE,SAAS,EAChB,EACE,OAAO,EACP,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,GAAG,EACH,QAAQ,EACR,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,EAAE,EACF,KAAK,GACN,EAAE,IAAI,CAAC,yBAAyB,EAAE,SAAS,GAAG,OAAO,CAAC,GAAG;QACxD,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;QACvB,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;QACzB,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC9B;CAkCJ;AAED,MAAM,MAAM,4BAA4B,GAAG,wBAAwB,GAAG;IACpE,IAAI,EAAE,0BAA0B,CAAA;CACjC,CAAA;AACD,qBAAa,wBAAyB,SAAQ,SAAS;gBACzC,EACV,SAAS,EACT,WAAW,EACX,QAAQ,EACR,IAAI,EACJ,KAAK,GACN,EAAE;QACD,SAAS,CAAC,EAAE,IAAI,GAAG,SAAS,CAAA;QAC5B,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAChC,QAAQ,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAA;QAC/B,IAAI,CAAC,EAAE,IAAI,GAAG,SAAS,CAAA;QACvB,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC3B;CAaF;AAED,MAAM,MAAM,mCAAmC,GAC7C,+BAA+B,GAAG;IAChC,IAAI,EAAE,iCAAiC,CAAA;CACxC,CAAA;AACH,qBAAa,+BAAgC,SAAQ,SAAS;gBAChD,EAAE,IAAI,EAAE,EAAE;QAAE,IAAI,EAAE,IAAI,CAAA;KAAE;CAQrC;AAED,MAAM,MAAM,yCAAyC,GACnD,qCAAqC,GAAG;IACtC,IAAI,EAAE,uCAAuC,CAAA;CAC9C,CAAA;AACH,qBAAa,qCAAsC,SAAQ,SAAS;gBACtD,EAAE,IAAI,EAAE,EAAE;QAAE,IAAI,EAAE,IAAI,CAAA;KAAE;CAMrC"}