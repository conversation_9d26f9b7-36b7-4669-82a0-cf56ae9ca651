{"version": 3, "file": "contract.d.ts", "sourceRoot": "", "sources": ["../../errors/contract.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,SAAS,CAAA;AAG3C,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAA;AAE/D,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AAC9C,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAA;AAC3C,OAAO,EACL,KAAK,2BAA2B,EAEjC,MAAM,mCAAmC,CAAA;AAQ1C,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AAKrC,MAAM,MAAM,sBAAsB,GAAG,kBAAkB,GAAG;IACxD,IAAI,EAAE,oBAAoB,CAAA;CAC3B,CAAA;AACD,qBAAa,kBAAmB,SAAQ,SAAS;IACtC,KAAK,EAAE,SAAS,CAAA;gBAGvB,KAAK,EAAE,SAAS,EAChB,EACE,OAAO,EAAE,QAAQ,EACjB,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,GAAG,EACH,QAAQ,EACR,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,EAAE,EACF,KAAK,EACL,aAAa,GACd,EAAE,cAAc,GAAG;QAClB,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;QACzB,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC9B;CAsCJ;AAED,MAAM,MAAM,kCAAkC,GAC5C,8BAA8B,GAAG;IAC/B,IAAI,EAAE,gCAAgC,CAAA;CACvC,CAAA;AACH,qBAAa,8BAA+B,SAAQ,SAAS;IAC3D,GAAG,EAAE,GAAG,CAAA;IACR,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,SAAS,CAAA;IACnB,KAAK,EAAE,SAAS,CAAA;IACzB,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACrC,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAClC,YAAY,EAAE,MAAM,CAAA;IACpB,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;gBAG1B,KAAK,EAAE,SAAS,EAChB,EACE,GAAG,EACH,IAAI,EACJ,eAAe,EACf,QAAQ,EACR,YAAY,EACZ,MAAM,GACP,EAAE;QACD,GAAG,EAAE,GAAG,CAAA;QACR,IAAI,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;QACtB,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QACrC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC7B,YAAY,EAAE,MAAM,CAAA;QACpB,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KAC7B;CAgDJ;AAED,MAAM,MAAM,iCAAiC,GAC3C,6BAA6B,GAAG;IAC9B,IAAI,EAAE,+BAA+B,CAAA;CACtC,CAAA;AACH,qBAAa,6BAA8B,SAAQ,SAAS;IAC1D,IAAI,CAAC,EAAE,2BAA2B,GAAG,SAAS,CAAA;IAC9C,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;IACrB,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC3B,SAAS,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;gBAEf,EACV,GAAG,EACH,IAAI,EACJ,YAAY,EACZ,OAAO,GACR,EAAE;QACD,GAAG,EAAE,GAAG,CAAA;QACR,IAAI,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;QACtB,YAAY,EAAE,MAAM,CAAA;QACpB,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC7B;CAyEF;AAED,MAAM,MAAM,iCAAiC,GAC3C,6BAA6B,GAAG;IAC9B,IAAI,EAAE,+BAA+B,CAAA;CACtC,CAAA;AACH,qBAAa,6BAA8B,SAAQ,SAAS;gBAC9C,EAAE,YAAY,EAAE,EAAE;QAAE,YAAY,EAAE,MAAM,CAAA;KAAE;CAWvD;AAED,MAAM,MAAM,uCAAuC,GACjD,mCAAmC,GAAG;IACpC,IAAI,EAAE,qCAAqC,CAAA;CAC5C,CAAA;AACH,qBAAa,mCAAoC,SAAQ,SAAS;gBACpD,EAAE,OAAO,EAAE,EAAE;QAAE,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KAAE;CAe3D;AAED,MAAM,MAAM,oBAAoB,GAAG,gBAAgB,GAAG;IACpD,IAAI,EAAE,kBAAkB,CAAA;CACzB,CAAA;AACD,qBAAa,gBAAiB,SAAQ,SAAS;IAC7C,IAAI,SAAI;IAER,IAAI,CAAC,EAAE,GAAG,GAAG;QAAE,IAAI,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;KAAE,GAAG,SAAS,CAAA;gBAEvC,EACV,IAAI,EACJ,OAAO,GACR,EAAE;QACD,IAAI,CAAC,EAAE,GAAG,GAAG;YAAE,IAAI,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;SAAE,GAAG,SAAS,CAAA;QACnD,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC7B;CAIF"}