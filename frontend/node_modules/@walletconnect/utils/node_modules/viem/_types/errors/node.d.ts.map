{"version": 3, "file": "node.d.ts", "sourceRoot": "", "sources": ["../../errors/node.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AAErC;;;;;;;;GAQG;AACH,MAAM,MAAM,0BAA0B,GAAG,sBAAsB,GAAG;IAChE,IAAI,EAAE,CAAC,CAAA;IACP,IAAI,EAAE,wBAAwB,CAAA;CAC/B,CAAA;AACD,qBAAa,sBAAuB,SAAQ,SAAS;IACnD,MAAM,CAAC,IAAI,SAAI;IACf,MAAM,CAAC,WAAW,SAAuB;gBAE7B,EACV,KAAK,EACL,OAAO,GACR,GAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAO;CAcxE;AAED,MAAM,MAAM,sBAAsB,GAAG,kBAAkB,GAAG;IACxD,IAAI,EAAE,oBAAoB,CAAA;CAC3B,CAAA;AACD,qBAAa,kBAAmB,SAAQ,SAAS;IAC/C,MAAM,CAAC,WAAW,SACmD;gBACzD,EACV,KAAK,EACL,YAAY,GACb,GAAE;QACD,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAA;QAC7B,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC7B;CAWP;AAED,MAAM,MAAM,qBAAqB,GAAG,iBAAiB,GAAG;IACtD,IAAI,EAAE,mBAAmB,CAAA;CAC1B,CAAA;AACD,qBAAa,iBAAkB,SAAQ,SAAS;IAC9C,MAAM,CAAC,WAAW,SACmF;gBACzF,EACV,KAAK,EACL,YAAY,GACb,GAAE;QACD,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAA;QAC7B,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC7B;CAWP;AAED,MAAM,MAAM,qBAAqB,GAAG,iBAAiB,GAAG;IACtD,IAAI,EAAE,mBAAmB,CAAA;CAC1B,CAAA;AACD,qBAAa,iBAAkB,SAAQ,SAAS;IAC9C,MAAM,CAAC,WAAW,SAAmB;gBACzB,EACV,KAAK,EACL,KAAK,GACN,GAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAO;CAQtE;AAED,MAAM,MAAM,oBAAoB,GAAG,gBAAgB,GAAG;IACpD,IAAI,EAAE,kBAAkB,CAAA;CACzB,CAAA;AACD,qBAAa,gBAAiB,SAAQ,SAAS;IAC7C,MAAM,CAAC,WAAW,SAC0C;gBAChD,EACV,KAAK,EACL,KAAK,GACN,GAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAO;CAWtE;AAED,MAAM,MAAM,sBAAsB,GAAG,kBAAkB,GAAG;IACxD,IAAI,EAAE,oBAAoB,CAAA;CAC3B,CAAA;AACD,qBAAa,kBAAmB,SAAQ,SAAS;IAC/C,MAAM,CAAC,WAAW,SAAwB;gBAC9B,EACV,KAAK,EACL,KAAK,GACN,GAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAO;CAQtE;AAED,MAAM,MAAM,0BAA0B,GAAG,sBAAsB,GAAG;IAChE,IAAI,EAAE,wBAAwB,CAAA;CAC/B,CAAA;AACD,qBAAa,sBAAuB,SAAQ,SAAS;IACnD,MAAM,CAAC,WAAW,SAC+C;gBACrD,EAAE,KAAK,EAAE,GAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAA;KAAO;CAqB9D;AAED,MAAM,MAAM,4BAA4B,GAAG,wBAAwB,GAAG;IACpE,IAAI,EAAE,0BAA0B,CAAA;CACjC,CAAA;AACD,qBAAa,wBAAyB,SAAQ,SAAS;IACrD,MAAM,CAAC,WAAW,SAA6C;gBACnD,EACV,KAAK,EACL,GAAG,GACJ,GAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAO;CAWpE;AAED,MAAM,MAAM,2BAA2B,GAAG,uBAAuB,GAAG;IAClE,IAAI,EAAE,yBAAyB,CAAA;CAChC,CAAA;AACD,qBAAa,uBAAwB,SAAQ,SAAS;IACpD,MAAM,CAAC,WAAW,SAA0B;gBAChC,EACV,KAAK,EACL,GAAG,GACJ,GAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAO;CAWpE;AAED,MAAM,MAAM,oCAAoC,GAC9C,gCAAgC,GAAG;IACjC,IAAI,EAAE,kCAAkC,CAAA;CACzC,CAAA;AACH,qBAAa,gCAAiC,SAAQ,SAAS;IAC7D,MAAM,CAAC,WAAW,SAA+B;gBACrC,EAAE,KAAK,EAAE,EAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAA;KAAE;CAMzD;AAED,MAAM,MAAM,uBAAuB,GAAG,mBAAmB,GAAG;IAC1D,IAAI,EAAE,qBAAqB,CAAA;CAC5B,CAAA;AACD,qBAAa,mBAAoB,SAAQ,SAAS;IAChD,MAAM,CAAC,WAAW,SAC8D;gBACpE,EACV,KAAK,EACL,oBAAoB,EACpB,YAAY,GACb,GAAE;QACD,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAA;QAC7B,oBAAoB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACzC,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC7B;CAiBP;AAED,MAAM,MAAM,oBAAoB,GAAG,gBAAgB,GAAG;IACpD,IAAI,EAAE,kBAAkB,CAAA;CACzB,CAAA;AACD,qBAAa,gBAAiB,SAAQ,SAAS;gBACjC,EAAE,KAAK,EAAE,EAAE;QAAE,KAAK,CAAC,EAAE,SAAS,GAAG,SAAS,CAAA;KAAE;CAMzD"}