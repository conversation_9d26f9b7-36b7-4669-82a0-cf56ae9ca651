{"version": 3, "file": "request.d.ts", "sourceRoot": "", "sources": ["../../errors/request.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AAGrC,MAAM,MAAM,oBAAoB,GAAG,gBAAgB,GAAG;IACpD,IAAI,EAAE,kBAAkB,CAAA;CACzB,CAAA;AACD,qBAAa,gBAAiB,SAAQ,SAAS;IAC7C,IAAI,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,GAAG;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,EAAE,GAAG,SAAS,CAAA;IACxE,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAC7B,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC3B,GAAG,EAAE,MAAM,CAAA;gBAEC,EACV,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,GACJ,EAAE;QACD,IAAI,CAAC,EAAE;YAAE,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,GAAG;YAAE,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,EAAE,GAAG,SAAS,CAAA;QACxE,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;QACzB,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC5B,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC7B,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC3B,GAAG,EAAE,MAAM,CAAA;KACZ;CAgBF;AAED,MAAM,MAAM,yBAAyB,GAAG,qBAAqB,GAAG;IAC9D,IAAI,EAAE,uBAAuB,CAAA;CAC9B,CAAA;AACD,qBAAa,qBAAsB,SAAQ,SAAS;gBACtC,EACV,IAAI,EACJ,KAAK,EACL,OAAO,EACP,GAAG,GACJ,EAAE;QACD,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,GAAG,SAAS,CAAA;QAC7C,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;QACzB,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC5B,GAAG,EAAE,MAAM,CAAA;KACZ;CAWF;AAED,MAAM,MAAM,mBAAmB,GAAG,eAAe,GAAG;IAClD,IAAI,EAAE,iBAAiB,CAAA;CACxB,CAAA;AACD,qBAAa,eAAgB,SAAQ,SAAS;IAC5C,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,CAAC,EAAE,OAAO,CAAA;gBAEF,EACV,IAAI,EACJ,KAAK,EACL,GAAG,GACJ,EAAE;QACD,IAAI,EAAE;YAAE,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,GAAG;YAAE,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,EAAE,CAAA;QAC3D,KAAK,EAAE;YAAE,IAAI,EAAE,MAAM,CAAC;YAAC,IAAI,CAAC,EAAE,OAAO,CAAC;YAAC,OAAO,EAAE,MAAM,CAAA;SAAE,CAAA;QACxD,GAAG,EAAE,MAAM,CAAA;KACZ;CAUF;AAED,MAAM,MAAM,qBAAqB,GAAG,iBAAiB,GAAG;IACtD,IAAI,EAAE,mBAAmB,CAAA;CAC1B,CAAA;AACD,qBAAa,iBAAkB,SAAQ,SAAS;gBAClC,EACV,GAAG,GACJ,GAAE;QACD,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KACpB;CAMP;AAED,MAAM,MAAM,gBAAgB,GAAG,YAAY,GAAG;IAC5C,IAAI,EAAE,cAAc,CAAA;CACrB,CAAA;AACD,qBAAa,YAAa,SAAQ,SAAS;gBAC7B,EACV,IAAI,EACJ,GAAG,GACJ,EAAE;QACD,IAAI,EAAE;YAAE,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,GAAG;YAAE,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,EAAE,CAAA;QAC3D,GAAG,EAAE,MAAM,CAAA;KACZ;CAOF"}