{"version": 3, "file": "rpc.d.ts", "sourceRoot": "", "sources": ["../../errors/rpc.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAA;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AAKrC,MAAM,MAAM,YAAY,GACpB,CAAC,CAAC,GACF,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,GACN,CAAC,KAAK,CAAA;AAEV,KAAK,eAAe,CAAC,IAAI,SAAS,MAAM,GAAG,YAAY,IAAI;IACzD,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,SAAS,CAAA;IACvC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC7B,YAAY,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAA;IACnC,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACzB,YAAY,EAAE,MAAM,CAAA;CACrB,CAAA;AAED;;;;GAIG;AACH,MAAM,MAAM,YAAY,GAAG,QAAQ,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAA;AAC1D,qBAAa,QAAQ,CAAC,KAAK,SAAS,MAAM,GAAG,YAAY,CAAE,SAAQ,SAAS;IAC1E,IAAI,EAAE,KAAK,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAA;gBAGzB,KAAK,EAAE,KAAK,EACZ,EACE,IAAI,EACJ,QAAQ,EACR,YAAY,EACZ,IAAI,EACJ,YAAY,GACb,EAAE,eAAe,CAAC,KAAK,CAAC;CAc5B;AAED,MAAM,MAAM,oBAAoB,GAC5B,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,GACJ,IAAI,CAAA;AAER;;;;GAIG;AACH,MAAM,MAAM,oBAAoB,GAAG,gBAAgB,GAAG;IACpD,IAAI,EAAE,kBAAkB,CAAA;CACzB,CAAA;AACD,qBAAa,gBAAgB,CAC3B,CAAC,GAAG,SAAS,CACb,SAAQ,QAAQ,CAAC,oBAAoB,CAAC;IACtC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAA;gBAGlB,KAAK,EAAE,KAAK,EACZ,OAAO,EAAE,QAAQ,CACf,eAAe,CAAC,oBAAoB,CAAC,GAAG;QACtC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAA;KACrB,CACF;CAMJ;AAED;;;;GAIG;AACH,MAAM,MAAM,iBAAiB,GAAG,aAAa,GAAG;IAC9C,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,eAAe,CAAA;CACtB,CAAA;AACD,qBAAa,aAAc,SAAQ,QAAQ;IACzC,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAQzB;AAED;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,sBAAsB,GAAG;IAChE,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,wBAAwB,CAAA;CAC/B,CAAA;AACD,qBAAa,sBAAuB,SAAQ,QAAQ;IAClD,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,sBAAsB,GAAG;IAChE,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,wBAAwB,CAAA;CAC/B,CAAA;AACD,qBAAa,sBAAuB,SAAQ,QAAQ;IAClD,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,GAAE;QAAE,MAAM,CAAC,EAAE,MAAM,CAAA;KAAO;CAO/D;AAED;;;;GAIG;AACH,MAAM,MAAM,yBAAyB,GAAG,qBAAqB,GAAG;IAC9D,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,uBAAuB,CAAA;CAC9B,CAAA;AACD,qBAAa,qBAAsB,SAAQ,QAAQ;IACjD,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAUzB;AAED;;;;GAIG;AACH,MAAM,MAAM,oBAAoB,GAAG,gBAAgB,GAAG;IACpD,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,kBAAkB,CAAA;CACzB,CAAA;AACD,qBAAa,gBAAiB,SAAQ,QAAQ;IAC5C,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,wBAAwB,GAAG,oBAAoB,GAAG;IAC5D,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,sBAAsB,CAAA;CAC7B,CAAA;AACD,qBAAa,oBAAqB,SAAQ,QAAQ;IAChD,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAUzB;AAED;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,wBAAwB,GAAG;IACpE,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,0BAA0B,CAAA;CACjC,CAAA;AACD,qBAAa,wBAAyB,SAAQ,QAAQ;IAC3C,IAAI,SAA6B;IAC1C,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,2BAA2B,GAAG;IAC1E,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,6BAA6B,CAAA;CACpC,CAAA;AACD,qBAAa,2BAA4B,SAAQ,QAAQ;IACvD,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,2BAA2B,GAAG;IAC1E,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,6BAA6B,CAAA;CACpC,CAAA;AACD,qBAAa,2BAA4B,SAAQ,QAAQ;IACvD,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,0BAA0B,GAAG;IACxE,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,4BAA4B,CAAA;CACnC,CAAA;AACD,qBAAa,0BAA2B,SAAQ,QAAQ;IACtD,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,GAAE;QAAE,MAAM,CAAC,EAAE,MAAM,CAAA;KAAO;CAO/D;AAED;;;;GAIG;AACH,MAAM,MAAM,yBAAyB,GAAG,qBAAqB,GAAG;IAC9D,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,uBAAuB,CAAA;CAC9B,CAAA;AACD,qBAAa,qBAAsB,SAAQ,QAAQ;IACjD,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,kCAAkC,GAC5C,8BAA8B,GAAG;IAC/B,IAAI,EAAE,CAAC,KAAK,CAAA;IACZ,IAAI,EAAE,gCAAgC,CAAA;CACvC,CAAA;AACH,qBAAa,8BAA+B,SAAQ,QAAQ;IAC1D,MAAM,CAAC,IAAI,SAAkB;gBAEjB,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,wBAAwB,GAAG;IACpE,IAAI,EAAE,IAAI,CAAA;IACV,IAAI,EAAE,0BAA0B,CAAA;CACjC,CAAA;AACD,qBAAa,wBAAyB,SAAQ,gBAAgB;IAC5D,MAAM,CAAC,IAAI,OAAgB;gBAEf,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,6BAA6B,GAAG,yBAAyB,GAAG;IACtE,IAAI,EAAE,IAAI,CAAA;IACV,IAAI,EAAE,2BAA2B,CAAA;CAClC,CAAA;AACD,qBAAa,yBAA0B,SAAQ,gBAAgB;IAC7D,MAAM,CAAC,IAAI,OAAgB;gBAEf,KAAK,EAAE,KAAK;CAQzB;AAED;;;;GAIG;AACH,MAAM,MAAM,kCAAkC,GAC5C,8BAA8B,GAAG;IAC/B,IAAI,EAAE,IAAI,CAAA;IACV,IAAI,EAAE,gCAAgC,CAAA;CACvC,CAAA;AACH,qBAAa,8BAA+B,SAAQ,gBAAgB;IAClE,MAAM,CAAC,IAAI,OAAgB;gBAEf,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,GAAE;QAAE,MAAM,CAAC,EAAE,MAAM,CAAA;KAAO;CAO/D;AAED;;;;GAIG;AACH,MAAM,MAAM,6BAA6B,GAAG,yBAAyB,GAAG;IACtE,IAAI,EAAE,IAAI,CAAA;IACV,IAAI,EAAE,2BAA2B,CAAA;CAClC,CAAA;AACD,qBAAa,yBAA0B,SAAQ,gBAAgB;IAC7D,MAAM,CAAC,IAAI,OAAgB;gBAEf,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,sBAAsB,GAAG;IAChE,IAAI,EAAE,IAAI,CAAA;IACV,IAAI,EAAE,wBAAwB,CAAA;CAC/B,CAAA;AACD,qBAAa,sBAAuB,SAAQ,gBAAgB;IAC1D,MAAM,CAAC,IAAI,OAAgB;gBAEf,KAAK,EAAE,KAAK;CAOzB;AAED;;;;GAIG;AACH,MAAM,MAAM,oBAAoB,GAAG,gBAAgB,GAAG;IACpD,IAAI,EAAE,IAAI,CAAA;IACV,IAAI,EAAE,kBAAkB,CAAA;CACzB,CAAA;AACD,qBAAa,gBAAiB,SAAQ,gBAAgB;IACpD,MAAM,CAAC,IAAI,OAAgB;gBAEf,KAAK,EAAE,KAAK;CAOzB;AAED;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG,eAAe,GAAG;IAClD,IAAI,EAAE,iBAAiB,CAAA;CACxB,CAAA;AACD,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,KAAK,EAAE,KAAK;CAMzB"}