{"version": 3, "file": "fallback.d.ts", "sourceRoot": "", "sources": ["../../../clients/transports/fallback.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAA;AAGjD,OAAO,EACL,KAAK,wBAAwB,EAC7B,KAAK,SAAS,EACd,KAAK,eAAe,EAErB,MAAM,sBAAsB,CAAA;AAE7B,MAAM,MAAM,YAAY,GAAG,CACzB,IAAI,EAAE;IACJ,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,OAAO,EAAE,CAAA;IACjB,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,CAAA;CACjC,GAAG,CACA;IACE,KAAK,CAAC,EAAE,SAAS,CAAA;IACjB,QAAQ,EAAE,OAAO,CAAA;IACjB,MAAM,EAAE,SAAS,CAAA;CAClB,GACD;IACE,KAAK,EAAE,KAAK,CAAA;IACZ,QAAQ,CAAC,EAAE,SAAS,CAAA;IACpB,MAAM,EAAE,OAAO,CAAA;CAChB,CACJ,KACE,IAAI,CAAA;AAET,KAAK,WAAW,GAAG;IACjB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC7B;;OAEG;IACH,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE;QAAE,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,CAAA;KAAE,KACpD,OAAO,CAAC,OAAO,CAAC,GAChB,SAAS,CAAA;IACb;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAChC;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC5B;;OAEG;IACH,OAAO,CAAC,EACJ;QACE;;;WAGG;QACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC5B;;;WAGG;QACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC/B,GACD,SAAS,CAAA;CACd,CAAA;AAED,MAAM,MAAM,uBAAuB,GAAG;IACpC,yCAAyC;IACzC,GAAG,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,SAAS,CAAA;IACxC,0CAA0C;IAC1C,IAAI,CAAC,EAAE,eAAe,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;IAC1C,iDAAiD;IACjD,IAAI,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,SAAS,CAAA;IACxC,wCAAwC;IACxC,UAAU,CAAC,EAAE,eAAe,CAAC,YAAY,CAAC,GAAG,SAAS,CAAA;IACtD,8CAA8C;IAC9C,UAAU,CAAC,EAAE,eAAe,CAAC,YAAY,CAAC,GAAG,SAAS,CAAA;CACvD,CAAA;AAED,MAAM,MAAM,iBAAiB,CAC3B,UAAU,SAAS,SAAS,SAAS,EAAE,GAAG,SAAS,SAAS,EAAE,IAC5D,SAAS,CACX,UAAU,EACV;IACE,UAAU,EAAE,CAAC,EAAE,EAAE,YAAY,KAAK,IAAI,CAAA;IACtC,UAAU,EAAE;SACT,GAAG,IAAI,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KACvD,CAAA;CACF,CACF,CAAA;AAED,MAAM,MAAM,0BAA0B,GAAG,wBAAwB,GAAG,SAAS,CAAA;AAE7E,wBAAgB,QAAQ,CAAC,KAAK,CAAC,UAAU,SAAS,SAAS,SAAS,EAAE,EACpE,WAAW,EAAE,UAAU,EACvB,MAAM,GAAE,uBAA4B,GACnC,iBAAiB,CAAC,UAAU,CAAC,CAiG/B;AAeD,gBAAgB;AAChB,wBAAgB,cAAc,CAAC,EAC7B,KAAK,EACL,QAAgB,EAChB,YAAY,EACZ,IAAI,EACJ,WAAgB,EAChB,OAAe,EACf,UAAU,EACV,OAAY,GACb,EAAE;IACD,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;IACzB,QAAQ,EAAE,WAAW,CAAC,UAAU,CAAC,CAAA;IACjC,YAAY,EAAE,CAAC,UAAU,EAAE,SAAS,SAAS,EAAE,KAAK,IAAI,CAAA;IACxD,IAAI,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;IACtC,WAAW,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,GAAG,SAAS,CAAA;IACpD,OAAO,CAAC,EAAE,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;IAC5C,UAAU,EAAE,SAAS,SAAS,EAAE,CAAA;IAChC,OAAO,CAAC,EAAE,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;CAC7C,QA0EA"}