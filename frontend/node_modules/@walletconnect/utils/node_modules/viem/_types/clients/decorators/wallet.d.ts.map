{"version": 3, "file": "wallet.d.ts", "sourceRoot": "", "sources": ["../../../clients/decorators/wallet.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,SAAS,CAAA;AAEtD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAA;AACtD,OAAO,EACL,KAAK,oBAAoB,EAE1B,MAAM,oCAAoC,CAAA;AAC3C,OAAO,EACL,KAAK,kBAAkB,EAExB,MAAM,kCAAkC,CAAA;AACzC,OAAO,EACL,KAAK,wBAAwB,EAC7B,KAAK,wBAAwB,EAE9B,MAAM,wCAAwC,CAAA;AAC/C,OAAO,EACL,KAAK,sBAAsB,EAE5B,MAAM,sCAAsC,CAAA;AAC7C,OAAO,EACL,KAAK,wBAAwB,EAE9B,MAAM,wCAAwC,CAAA;AAC/C,OAAO,EACL,KAAK,mCAAmC,EACxC,KAAK,gCAAgC,EACrC,KAAK,mCAAmC,EAEzC,MAAM,mDAAmD,CAAA;AAC1D,OAAO,EACL,KAAK,0BAA0B,EAEhC,MAAM,0CAA0C,CAAA;AACjD,OAAO,EACL,KAAK,4BAA4B,EACjC,KAAK,4BAA4B,EAElC,MAAM,4CAA4C,CAAA;AACnD,OAAO,EACL,KAAK,4BAA4B,EACjC,KAAK,4BAA4B,EAElC,MAAM,4CAA4C,CAAA;AACnD,OAAO,EACL,KAAK,yBAAyB,EAC9B,KAAK,sBAAsB,EAC3B,KAAK,yBAAyB,EAE/B,MAAM,yCAAyC,CAAA;AAChD,OAAO,EACL,KAAK,qBAAqB,EAC1B,KAAK,qBAAqB,EAE3B,MAAM,qCAAqC,CAAA;AAC5C,OAAO,EACL,KAAK,yBAAyB,EAC9B,KAAK,sBAAsB,EAC3B,KAAK,yBAAyB,EAE/B,MAAM,yCAAyC,CAAA;AAChD,OAAO,EACL,KAAK,uBAAuB,EAC5B,KAAK,uBAAuB,EAE7B,MAAM,uCAAuC,CAAA;AAC9C,OAAO,EACL,KAAK,qBAAqB,EAE3B,MAAM,qCAAqC,CAAA;AAC5C,OAAO,EACL,KAAK,oBAAoB,EACzB,KAAK,oBAAoB,EAE1B,MAAM,oCAAoC,CAAA;AAC3C,OAAO,EACL,KAAK,uBAAuB,EAC5B,KAAK,uBAAuB,EAE7B,MAAM,uCAAuC,CAAA;AAC9C,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAA;AACjD,OAAO,KAAK,EACV,oBAAoB,EACpB,oBAAoB,EACrB,MAAM,yBAAyB,CAAA;AAChC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAA;AAEjE,MAAM,MAAM,aAAa,CACvB,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,IACvD;IACF;;;;;;;;;;;;;;;;OAgBG;IACH,QAAQ,EAAE,CAAC,IAAI,EAAE,kBAAkB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA;IACrD;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,cAAc,EAAE,CACd,KAAK,CAAC,GAAG,SAAS,GAAG,GAAG,SAAS,OAAO,EAAE,EAC1C,aAAa,SAAS,KAAK,GAAG,SAAS,EAEvC,IAAI,EAAE,wBAAwB,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,CAAC,KAC/D,OAAO,CAAC,wBAAwB,CAAC,CAAA;IACtC;;;;;;;;;;;;;;;;;OAiBG;IACH,YAAY,EAAE,MAAM,OAAO,CAAC,sBAAsB,CAAC,CAAA;IACnD;;;;;;;;;;;;;;;;;;OAkBG;IACH,UAAU,EAAE,MAAM,OAAO,CAAC,oBAAoB,CAAC,CAAA;IAC/C;;;;;;;;;;;;;;;;;OAiBG;IACH,cAAc,EAAE,MAAM,OAAO,CAAC,wBAAwB,CAAC,CAAA;IACvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACH,yBAAyB,EAAE,CACzB,KAAK,CAAC,OAAO,SAAS,gCAAgC,CACpD,KAAK,EACL,aAAa,CACd,EACD,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EACnD,eAAe,SAAS,OAAO,GAAG,OAAO,GAAG,SAAS,GAAG,SAAS,EAEjE,IAAI,EAAE,mCAAmC,CACvC,KAAK,EACL,OAAO,EACP,aAAa,EACb,eAAe,EACf,OAAO,CACR,KACE,OAAO,CACV,mCAAmC,CACjC,KAAK,EACL,OAAO,EACP,aAAa,EACb,eAAe,EACf,OAAO,CACR,CACF,CAAA;IACD;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,gBAAgB,EAAE,MAAM,OAAO,CAAC,0BAA0B,CAAC,CAAA;IAC3D;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,kBAAkB,EAAE,CAClB,IAAI,EAAE,4BAA4B,KAC/B,OAAO,CAAC,4BAA4B,CAAC,CAAA;IAC1C;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,kBAAkB,EAAE,CAClB,IAAI,EAAE,4BAA4B,KAC/B,OAAO,CAAC,4BAA4B,CAAC,CAAA;IAC1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAyCG;IACH,eAAe,EAAE,CACf,KAAK,CAAC,OAAO,SAAS,sBAAsB,CAAC,KAAK,EAAE,aAAa,CAAC,EAClE,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAEnD,IAAI,EAAE,yBAAyB,CAAC,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,KACpE,OAAO,CAAC,yBAAyB,CAAC,CAAA;IACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA0CG;IACH,WAAW,EAAE,CACX,IAAI,EAAE,qBAAqB,CAAC,OAAO,CAAC,KACjC,OAAO,CAAC,qBAAqB,CAAC,CAAA;IACnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA0CG;IACH,eAAe,EAAE,CACf,aAAa,SAAS,KAAK,GAAG,SAAS,EACvC,KAAK,CAAC,OAAO,SAAS,sBAAsB,CAC1C,KAAK,EACL,aAAa,CACd,GAAG,sBAAsB,CAAC,KAAK,EAAE,aAAa,CAAC,EAEhD,IAAI,EAAE,yBAAyB,CAAC,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,KACpE,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAA;IAChD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+FG;IACH,aAAa,EAAE,CACb,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,EAC9D,WAAW,SAAS,MAAM,EAE1B,IAAI,EAAE,uBAAuB,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,KAC3D,OAAO,CAAC,uBAAuB,CAAC,CAAA;IACrC;;;;;;;;;;;;;;;;;OAiBG;IACH,WAAW,EAAE,CAAC,IAAI,EAAE,qBAAqB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3D;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,UAAU,EAAE,CAAC,IAAI,EAAE,oBAAoB,KAAK,OAAO,CAAC,oBAAoB,CAAC,CAAA;IACzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8CG;IACH,aAAa,EAAE,CACb,KAAK,CAAC,GAAG,SAAS,GAAG,GAAG,SAAS,OAAO,EAAE,EAC1C,YAAY,SAAS,oBAAoB,CAAC,GAAG,EAAE,SAAS,GAAG,YAAY,CAAC,EACxE,IAAI,SAAS,oBAAoB,CAC/B,GAAG,EACH,SAAS,GAAG,YAAY,EACxB,YAAY,CACb,EACD,aAAa,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAEnD,IAAI,EAAE,uBAAuB,CAC3B,GAAG,EACH,YAAY,EACZ,IAAI,EACJ,KAAK,EACL,OAAO,EACP,aAAa,CACd,KACE,OAAO,CAAC,uBAAuB,CAAC,CAAA;CACtC,CAAA;AAED,wBAAgB,aAAa,CAC3B,SAAS,SAAS,SAAS,EAC3B,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,EACzD,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAoB1E"}