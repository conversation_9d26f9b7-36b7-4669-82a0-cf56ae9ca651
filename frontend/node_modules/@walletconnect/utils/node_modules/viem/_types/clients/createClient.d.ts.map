{"version": 3, "file": "createClient.d.ts", "sourceRoot": "", "sources": ["../../clients/createClient.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,SAAS,CAAA;AAEtC,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAA;AAC1D,OAAO,EACL,KAAK,qBAAqB,EAE3B,MAAM,mCAAmC,CAAA;AAC1C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAA;AACnD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAA;AAClD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AAC9C,OAAO,KAAK,EACV,gBAAgB,EAChB,cAAc,EACd,SAAS,EACV,MAAM,qBAAqB,CAAA;AAC5B,OAAO,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAA;AAC/D,OAAO,KAAK,EACV,qBAAqB,EACrB,qBAAqB,EACtB,MAAM,kBAAkB,CAAA;AAEzB,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAA;AAC3D,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAA;AAC3D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAA;AAEhE,MAAM,MAAM,YAAY,CACtB,SAAS,SAAS,SAAS,GAAG,SAAS,EACvC,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,gBAAgB,SAAS,OAAO,GAAG,OAAO,GAAG,SAAS,GAClD,OAAO,GACP,OAAO,GACP,SAAS,EACb,SAAS,SAAS,SAAS,GAAG,SAAS,GAAG,SAAS,IACjD;IACF,+GAA+G;IAC/G,OAAO,CAAC,EAAE,gBAAgB,GAAG,OAAO,GAAG,OAAO,GAAG,SAAS,CAAA;IAC1D,gCAAgC;IAChC,KAAK,CAAC,EACF;QACE,yDAAyD;QACzD,SAAS,CAAC,EAAE,OAAO,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,SAAS,CAAA;KAClE,GACD,SAAS,CAAA;IACb;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC9B;;;OAGG;IACH,QAAQ,CAAC,EACL;QACE;;;WAGG;QACH,OAAO,CAAC,EAAE,CACR,UAAU,EAAE,qBAAqB,KAC9B,OAAO,CAAC,qBAAqB,CAAC,CAAA;KACpC,GACD,KAAK,GACL,SAAS,CAAA;IACb,4BAA4B;IAC5B,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,KAAK,CAAA;IACjC,4BAA4B;IAC5B,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACxB,6BAA6B;IAC7B,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACzB;;;OAGG;IACH,eAAe,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACpC;;OAEG;IACH,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,CAAA;IACjC,wBAAwB;IACxB,SAAS,EAAE,SAAS,CAAA;IACpB,0BAA0B;IAC1B,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC1B,CAAA;AAMD,KAAK,0BAA0B,CAC7B,SAAS,SAAS,SAAS,GAAG,SAAS,EACvC,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,IACvD,IAAI,CACN,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,EACtC,MAAM,GACN,2BAA2B,GAC3B,mBAAmB,GACnB,qBAAqB,GACrB,aAAa,GACb,UAAU,GACV,gBAAgB,GAChB,YAAY,GACZ,mBAAmB,GACnB,YAAY,GACZ,kBAAkB,GAClB,aAAa,GACb,SAAS,GACT,gBAAgB,GAChB,qBAAqB,GACrB,uBAAuB,GACvB,2BAA2B,GAC3B,cAAc,GACd,oBAAoB,GACpB,kBAAkB,GAClB,iBAAiB,GACjB,kBAAkB,GAClB,oBAAoB,CACvB,GACC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,iBAAiB,GAAG,eAAe,CAAC,CAAA;AAI1E,MAAM,MAAM,MAAM,CAChB,SAAS,SAAS,SAAS,GAAG,SAAS,EACvC,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,EACzD,SAAS,SAAS,SAAS,GAAG,SAAS,GAAG,SAAS,EACnD,QAAQ,SAAS,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG,SAAS,IAC1D,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,GACnD,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,GAAG;IACjD,MAAM,EAAE,CACN,KAAK,CAAC,MAAM,SAAS,QAAQ,GAC3B,YAAY,CAAC,0BAA0B,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,EAErE,EAAE,EAAE,CACF,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,KAC3D,MAAM,KACR,MAAM,CACT,SAAS,EACT,KAAK,EACL,OAAO,EACP,SAAS,EACT,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,CACpE,CAAA;CACF,CAAA;AAEH,KAAK,WAAW,CACd,SAAS,SAAS,SAAS,GAAG,SAAS,EACvC,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,EACnD,OAAO,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,EACzD,SAAS,SAAS,SAAS,GAAG,SAAS,GAAG,SAAS,IACjD;IACF,iCAAiC;IACjC,OAAO,EAAE,OAAO,CAAA;IAChB,gCAAgC;IAChC,KAAK,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,SAAS,CAAA;IACzC,2DAA2D;IAC3D,SAAS,EAAE,MAAM,CAAA;IACjB,0EAA0E;IAC1E,QAAQ,CAAC,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,SAAS,CAAA;IAC/C,4BAA4B;IAC5B,KAAK,EAAE,KAAK,CAAA;IACZ,4BAA4B;IAC5B,GAAG,EAAE,MAAM,CAAA;IACX,6BAA6B;IAC7B,IAAI,EAAE,MAAM,CAAA;IACZ,8FAA8F;IAC9F,eAAe,EAAE,MAAM,CAAA;IACvB,4DAA4D;IAC5D,OAAO,EAAE,gBAAgB,CACvB,SAAS,SAAS,SAAS,GAAG,cAAc,GAAG,SAAS,CACzD,CAAA;IACD,wBAAwB;IACxB,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAA;IAC3E,0BAA0B;IAC1B,IAAI,EAAE,MAAM,CAAA;IACZ,kCAAkC;IAClC,GAAG,EAAE,MAAM,CAAA;CACZ,CAAA;AAED,KAAK,QAAQ,GAAG,QAAQ,CAEtB;KAAG,CAAC,IAAI,MAAM,WAAW,CAAC,CAAC,EAAE,SAAS;CAAE,GAAG;IACzC,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;CACvB,CACF,CAAA;AAED,MAAM,MAAM,qBAAqB,GAAG;IAClC,0EAA0E;IAC1E,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC9B,oFAAoF;IACpF,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC1B,CAAA;AAED,MAAM,MAAM,qBAAqB,GAAG,qBAAqB,GAAG,SAAS,CAAA;AAErE,wBAAgB,YAAY,CAC1B,SAAS,SAAS,SAAS,EAC3B,KAAK,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAC3C,gBAAgB,SAAS,OAAO,GAAG,OAAO,GAAG,SAAS,GAAG,SAAS,EAClE,SAAS,SAAS,SAAS,GAAG,SAAS,GAAG,SAAS,EAEnD,UAAU,EAAE,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,gBAAgB,EAAE,SAAS,CAAC,GACtE,QAAQ,CACT,MAAM,CACJ,SAAS,EACT,KAAK,EACL,gBAAgB,SAAS,OAAO,GAC5B,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,GAC1C,gBAAgB,EACpB,SAAS,CACV,CACF,CAAA;AAmDD;;;GAGG;AACH,wBAAgB,SAAS,CAAC,SAAS,SAAS,SAAS,KAAK,SAAS,CAElE"}