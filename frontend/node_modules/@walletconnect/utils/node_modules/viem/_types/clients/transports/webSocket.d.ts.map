{"version": 3, "file": "webSocket.d.ts", "sourceRoot": "", "sources": ["../../../clients/transports/webSocket.ts"], "names": [], "mappings": "AACA,OAAO,EAEL,KAAK,oBAAoB,EAC1B,MAAM,2BAA2B,CAAA;AAClC,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,qBAAqB,CAAA;AAC/C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAA;AAErD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAA;AAChE,OAAO,EACL,KAAK,4BAA4B,EAElC,MAAM,8BAA8B,CAAA;AACrC,OAAO,EACL,KAAK,wBAAwB,EAC7B,KAAK,SAAS,EACd,KAAK,eAAe,EAErB,MAAM,sBAAsB,CAAA;AAE7B,KAAK,qCAAqC,GAAG;IAC3C,MAAM,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,CAAA;IACnC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,CAAC,GAAG,SAAS,CAAA;CAC7C,CAAA;AAED,KAAK,qCAAqC,GAAG;IAC3C,cAAc,EAAE,IAAI,CAAA;IACpB,WAAW,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAA;CACjD,CAAA;AAED,KAAK,2BAA2B,GAAG;IACjC,SAAS,CACP,IAAI,EAAE,qCAAqC,GAAG;QAC5C;;;WAGG;QACH,MAAM,EAAE,CAAC,UAAU,CAAC,CAAA;KACrB,GACA,OAAO,CAAC,qCAAqC,CAAC,CAAA;CAClD,CAAA;AAED,MAAM,MAAM,wBAAwB,GAAG;IACrC;;;OAGG;IACH,SAAS,CAAC,EAAE,4BAA4B,CAAC,WAAW,CAAC,GAAG,SAAS,CAAA;IACjE,0CAA0C;IAC1C,GAAG,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,SAAS,CAAA;IACxC,iEAAiE;IACjE,OAAO,CAAC,EAAE,eAAe,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;IAChD,2CAA2C;IAC3C,IAAI,CAAC,EAAE,eAAe,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;IAC1C;;;OAGG;IACH,SAAS,CAAC,EAAE,4BAA4B,CAAC,WAAW,CAAC,GAAG,SAAS,CAAA;IACjE,wCAAwC;IACxC,UAAU,CAAC,EAAE,eAAe,CAAC,YAAY,CAAC,GAAG,SAAS,CAAA;IACtD,8CAA8C;IAC9C,UAAU,CAAC,EAAE,eAAe,CAAC,YAAY,CAAC,GAAG,SAAS,CAAA;IACtD,wEAAwE;IACxE,OAAO,CAAC,EAAE,eAAe,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;CACjD,CAAA;AAED,MAAM,MAAM,kBAAkB,GAAG,SAAS,CACxC,WAAW,EACX;IACE;;OAEG;IACH,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,CAAA;IAC/B,YAAY,IAAI,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAA;IACnD,SAAS,EAAE,2BAA2B,CAAC,WAAW,CAAC,CAAA;CACpD,CACF,CAAA;AAED,MAAM,MAAM,2BAA2B,GACnC,wBAAwB,GACxB,oBAAoB,GACpB,SAAS,CAAA;AAEb;;GAEG;AACH,wBAAgB,SAAS;AACvB,uEAAuE;AACvE,GAAG,CAAC,EAAE,MAAM,EACZ,MAAM,GAAE,wBAA6B,GACpC,kBAAkB,CAiGpB"}