/** The number of bytes in a BLS scalar field element. */
export declare const bytesPerFieldElement = 32;
/** The number of field elements in a blob. */
export declare const fieldElementsPerBlob = 4096;
/** The number of bytes in a blob. */
export declare const bytesPerBlob: number;
/** Blob bytes limit per transaction. */
export declare const maxBytesPerTransaction: number;
//# sourceMappingURL=blob.d.ts.map