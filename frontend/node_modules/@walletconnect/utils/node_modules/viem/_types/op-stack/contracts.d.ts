/**
 * Predeploy contracts for OP Stack.
 * @see https://github.com/ethereum-optimism/optimism/blob/develop/specs/predeploys.md
 */
export declare const contracts: {
    readonly gasPriceOracle: {
        readonly address: "******************************************";
    };
    readonly l1Block: {
        readonly address: "******************************************";
    };
    readonly l2CrossDomainMessenger: {
        readonly address: "******************************************";
    };
    readonly l2Erc721Bridge: {
        readonly address: "******************************************";
    };
    readonly l2StandardBridge: {
        readonly address: "******************************************";
    };
    readonly l2ToL1MessagePasser: {
        readonly address: "******************************************";
    };
};
//# sourceMappingURL=contracts.d.ts.map