{"version": 3, "file": "transaction.d.ts", "sourceRoot": "", "sources": ["../../../op-stack/types/transaction.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAC1D,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,KAAK,EACV,KAAK,EACL,QAAQ,EACR,qBAAqB,EACrB,cAAc,IAAI,eAAe,EAClC,MAAM,oBAAoB,CAAA;AAC3B,OAAO,KAAK,EACV,eAAe,EACf,kBAAkB,EAClB,uBAAuB,EACvB,2BAA2B,EAC3B,qBAAqB,EACrB,eAAe,EACf,WAAW,IAAI,YAAY,EAC5B,MAAM,4BAA4B,CAAA;AACnC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAA;AAEjD,KAAK,cAAc,CAAC,OAAO,SAAS,OAAO,GAAG,OAAO,IACnD,eAAe,CAAC,OAAO,CAAC,GAAG;IACzB,UAAU,CAAC,EAAE,SAAS,CAAA;IACtB,IAAI,CAAC,EAAE,SAAS,CAAA;IAChB,UAAU,CAAC,EAAE,SAAS,CAAA;CACvB,CAAA;AAEH,MAAM,MAAM,4BAA4B,CAAC,OAAO,SAAS,OAAO,GAAG,OAAO,IACxE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,GACxD,gBAAgB,CAAC,QAAQ,CAAC,GAAG;IAC3B,UAAU,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAChC,IAAI,CAAC,EAAE,GAAG,GAAG,SAAS,CAAA;IACtB,UAAU,EAAE,GAAG,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;CACb,CAAA;AACL,MAAM,MAAM,qBAAqB,CAAC,OAAO,SAAS,OAAO,GAAG,OAAO,IAAI,KAAK,CAC1E,cAAc,CAAC,OAAO,CAAC,GAAG,4BAA4B,CAAC,OAAO,CAAC,CAChE,CAAA;AAED,MAAM,MAAM,qCAAqC,GAAG;IAClD,UAAU,EAAE,GAAG,GAAG,IAAI,CAAA;IACtB,SAAS,EAAE,GAAG,GAAG,IAAI,CAAA;IACrB,KAAK,EAAE,GAAG,GAAG,IAAI,CAAA;IACjB,WAAW,EAAE,GAAG,MAAM,EAAE,GAAG,IAAI,CAAA;CAChC,CAAA;AACD,MAAM,MAAM,4BAA4B,GAAG,qBAAqB,GAC9D,qCAAqC,CAAA;AAEvC,KAAK,WAAW,CAAC,OAAO,SAAS,OAAO,GAAG,OAAO,IAAI,YAAY,CAChE,MAAM,EACN,MAAM,EACN,OAAO,CACR,GAAG;IACF,UAAU,CAAC,EAAE,SAAS,CAAA;IACtB,IAAI,CAAC,EAAE,SAAS,CAAA;IAChB,UAAU,CAAC,EAAE,SAAS,CAAA;CACvB,CAAA;AAED,MAAM,MAAM,yBAAyB,CAAC,OAAO,SAAS,OAAO,GAAG,OAAO,IACrE,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,GACtC,gBAAgB,GAAG;IACjB,UAAU,CAAC,EAAE,OAAO,CAAA;IACpB,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACzB,UAAU,EAAE,GAAG,CAAA;IACf,IAAI,EAAE,SAAS,CAAA;CAChB,CAAA;AAEL,MAAM,MAAM,kBAAkB,CAAC,OAAO,SAAS,OAAO,GAAG,OAAO,IAC5D,WAAW,CAAC,OAAO,CAAC,GACpB,yBAAyB,CAAC,OAAO,CAAC,CAAA;AAEtC,MAAM,MAAM,kCAAkC,GAAG;IAC/C,UAAU,EAAE,MAAM,GAAG,IAAI,CAAA;IACzB,SAAS,EAAE,MAAM,GAAG,IAAI,CAAA;IACxB,KAAK,EAAE,MAAM,GAAG,IAAI,CAAA;IACpB,WAAW,EAAE,MAAM,GAAG,IAAI,CAAA;CAC3B,CAAA;AACD,MAAM,MAAM,yBAAyB,GAAG,kBAAkB,GACxD,kCAAkC,CAAA;AAEpC,MAAM,MAAM,8BAA8B,GAAG,KAAK,CAChD,8BAA8B,GAAG,uBAAuB,CACzD,CAAA;AAED,MAAM,MAAM,4BAA4B,CACtC,IAAI,SAAS,sBAAsB,GAAG,sBAAsB,IAC1D,IAAI,SAAS,SAAS,GACtB,4BAA4B,GAC5B,qBAAqB,CAAC,IAAI,CAAC,CAAA;AAE/B,MAAM,MAAM,sBAAsB,GAAG,eAAe,GAAG,SAAS,CAAA;AAEhE,MAAM,MAAM,8BAA8B,CACxC,QAAQ,GAAG,MAAM,EACjB,KAAK,GAAG,MAAM,IACZ,IAAI,CACN,2BAA2B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAC5C,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAC1B,GAAG;IACF,IAAI,EAAE,GAAG,CAAA;IACT,UAAU,CAAC,EAAE,OAAO,CAAA;IACpB,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACzB,UAAU,EAAE,GAAG,CAAA;IACf,IAAI,EAAE,SAAS,CAAA;CAChB,CAAA;AAED,MAAM,MAAM,4BAA4B,GAAG,OAAO,MAAM,EAAE,CAAA"}