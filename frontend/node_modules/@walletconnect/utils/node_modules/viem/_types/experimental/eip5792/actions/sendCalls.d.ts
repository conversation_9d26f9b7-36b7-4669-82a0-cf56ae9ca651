import type { Narrow } from 'abitype';
import type { Client } from '../../../clients/createClient.js';
import type { Transport } from '../../../clients/transports/createTransport.js';
import type { ErrorType } from '../../../errors/utils.js';
import type { Account, GetAccountParameter } from '../../../types/account.js';
import type { Calls } from '../../../types/calls.js';
import type { Chain, DeriveChain } from '../../../types/chain.js';
import type { WalletCapabilities, WalletSendCallsParameters } from '../../../types/eip1193.js';
import type { RequestErrorType } from '../../../utils/buildRequest.js';
export type SendCallsParameters<chain extends Chain | undefined = Chain | undefined, account extends Account | undefined = Account | undefined, chainOverride extends Chain | undefined = Chain | undefined, calls extends readonly unknown[] = readonly unknown[], _chain extends Chain | undefined = DeriveChain<chain, chainOverride>> = {
    chain?: chainOverride | Chain | undefined;
    calls: Calls<Narrow<calls>>;
    capabilities?: WalletSendCallsParameters<WalletCapabilities>[number]['capabilities'] | undefined;
    version?: WalletSendCallsParameters[number]['version'] | undefined;
} & GetAccountParameter<account>;
export type SendCallsReturnType = string;
export type SendCallsErrorType = RequestErrorType | ErrorType;
/**
 * Requests the connected wallet to send a batch of calls.
 *
 * - Docs: https://viem.sh/experimental/eip5792/sendCalls
 * - JSON-RPC Methods: [`wallet_sendCalls`](https://eips.ethereum.org/EIPS/eip-5792)
 *
 * @param client - Client to use
 * @returns Transaction identifier. {@link SendCallsReturnType}
 *
 * @example
 * import { createWalletClient, custom } from 'viem'
 * import { mainnet } from 'viem/chains'
 * import { sendCalls } from 'viem/wallet'
 *
 * const client = createWalletClient({
 *   chain: mainnet,
 *   transport: custom(window.ethereum),
 * })
 * const id = await sendCalls(client, {
 *   account: '******************************************',
 *   calls: [
 *     {
 *       data: '0xdeadbeef',
 *       to: '******************************************',
 *     },
 *     {
 *       to: '******************************************',
 *       value: 69420n,
 *     },
 *   ],
 * })
 */
export declare function sendCalls<const calls extends readonly unknown[], chain extends Chain | undefined, account extends Account | undefined = undefined, chainOverride extends Chain | undefined = undefined>(client: Client<Transport, chain, account>, parameters: SendCallsParameters<chain, account, chainOverride, calls>): Promise<SendCallsReturnType>;
//# sourceMappingURL=sendCalls.d.ts.map