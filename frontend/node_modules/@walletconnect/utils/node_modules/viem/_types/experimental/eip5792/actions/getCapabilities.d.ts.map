{"version": 3, "file": "getCapabilities.d.ts", "sourceRoot": "", "sources": ["../../../../experimental/eip5792/actions/getCapabilities.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,SAAS,CAAA;AAGtC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,kCAAkC,CAAA;AAC9D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gDAAgD,CAAA;AAE/E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AACzD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAA;AACxD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,KAAK,EACV,kBAAkB,EAClB,wBAAwB,EACzB,MAAM,2BAA2B,CAAA;AAClC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAA;AACvD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAA;AAEtE,MAAM,MAAM,yBAAyB,GAAG;IACtC,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,SAAS,CAAA;CACxC,CAAA;AAED,MAAM,MAAM,yBAAyB,GAAG,QAAQ,CAC9C,wBAAwB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CACrD,CAAA;AAED,MAAM,MAAM,wBAAwB,GAAG,gBAAgB,GAAG,SAAS,CAAA;AAEnE;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAsB,eAAe,CAAC,KAAK,SAAS,KAAK,GAAG,SAAS,EACnE,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,EAChC,UAAU,GAAE,yBAA8B,GACzC,OAAO,CAAC,yBAAyB,CAAC,CAkBpC"}