export declare const bitlayerTestnet: {
    blockExplorers: {
        readonly default: {
            readonly name: "bitlayer testnet scan";
            readonly url: "https://testnet.btrscan.com";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 4135671;
        };
    };
    id: 200810;
    name: "Bitlayer Testnet";
    nativeCurrency: {
        readonly name: "BTC";
        readonly symbol: "BTC";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://testnet-rpc.bitlayer.org"];
            readonly webSocket: readonly ["wss://testnet-ws.bitlayer.org"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=bitlayerTestnet.d.ts.map