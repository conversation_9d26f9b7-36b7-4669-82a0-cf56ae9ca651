export declare const goerli: {
    blockExplorers: {
        readonly default: {
            readonly name: "Etherscan";
            readonly url: "https://goerli.etherscan.io";
            readonly apiUrl: "https://api-goerli.etherscan.io/api";
        };
    };
    contracts: {
        readonly ensRegistry: {
            readonly address: "******************************************";
        };
        readonly ensUniversalResolver: {
            readonly address: "******************************************";
            readonly blockCreated: 10339206;
        };
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 6507670;
        };
    };
    id: 5;
    name: "<PERSON><PERSON><PERSON>";
    nativeCurrency: {
        readonly name: "<PERSON><PERSON><PERSON>ther";
        readonly symbol: "ETH";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.ankr.com/eth_goerli"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=goerli.d.ts.map