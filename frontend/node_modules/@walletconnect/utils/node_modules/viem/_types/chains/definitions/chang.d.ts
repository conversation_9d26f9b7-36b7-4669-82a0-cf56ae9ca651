export declare const chang: {
    blockExplorers: {
        readonly default: {
            readonly name: "Chang Chain explorer";
            readonly url: "https://cthscan.com";
        };
    };
    contracts?: import("../index.js").Prettify<{
        [key: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
    } & {
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    }> | undefined;
    id: 5858;
    name: "Chang Chain Foundation Mainnet";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "CTH";
        readonly symbol: "CTH";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.cthscan.com"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=chang.d.ts.map