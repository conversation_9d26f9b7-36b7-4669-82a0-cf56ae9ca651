export declare const birdlayer: {
    blockExplorers: {
        readonly default: {
            readonly name: "BirdLayer Explorer";
            readonly url: "https://scan.birdlayer.xyz";
        };
    };
    contracts?: import("../index.js").Prettify<{
        [key: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
    } & {
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    }> | undefined;
    id: 53456;
    name: "<PERSON><PERSON>ay<PERSON>";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "E<PERSON>";
        readonly symbol: "ETH";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.birdlayer.xyz", "https://rpc1.birdlayer.xyz"];
            readonly webSocket: readonly ["wss://rpc.birdlayer.xyz/ws"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=birdlayer.d.ts.map