export declare const canto: {
    blockExplorers: {
        readonly default: {
            readonly name: "Tuber.Build (Blockscout)";
            readonly url: "https://tuber.build";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "0xca11bde05977b3631167028862be2a173976ca11";
            readonly blockCreated: 2905789;
        };
    };
    id: 7700;
    name: "Canto";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "Canto";
        readonly symbol: "CANTO";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://canto.gravitychain.io"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=canto.d.ts.map