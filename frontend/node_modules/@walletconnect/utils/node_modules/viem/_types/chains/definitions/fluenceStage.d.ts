export declare const fluenceStage: {
    blockExplorers: {
        readonly default: {
            readonly name: "Blockscout";
            readonly url: "https://blockscout.stage.fluence.dev";
            readonly apiUrl: "https://blockscout.stage.fluence.dev/api";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "0xcA11bde05977b3631167028862bE2a173976CA11";
            readonly blockCreated: 83227;
        };
    };
    id: 123420000220;
    name: "Fluence Stage";
    nativeCurrency: {
        readonly name: "tFLT";
        readonly symbol: "tFLT";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.stage.fluence.dev"];
            readonly webSocket: readonly ["wss://ws.stage.fluence.dev"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=fluenceStage.d.ts.map