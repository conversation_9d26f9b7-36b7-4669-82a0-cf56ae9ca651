export declare const lyra: {
    blockExplorers: {
        readonly default: {
            readonly name: "Lyra Explorer";
            readonly url: "https://explorer.lyra.finance";
            readonly apiUrl: "https://explorer.lyra.finance/api/v2";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 1935198;
        };
    };
    id: 957;
    name: "Lyra Chain";
    nativeCurrency: {
        readonly name: "<PERSON><PERSON>";
        readonly symbol: "ETH";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.lyra.finance"];
        };
    };
    sourceId?: number | undefined;
    testnet?: boolean | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=lyra.d.ts.map