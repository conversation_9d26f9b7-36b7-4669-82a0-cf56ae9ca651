export declare const lightlinkPhoenix: {
    blockExplorers: {
        readonly default: {
            readonly name: "LightLink Phoenix Explorer";
            readonly url: "https://phoenix.lightlink.io";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 125499184;
        };
    };
    id: 1890;
    name: "LightLink Phoenix Mainnet";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "Ether";
        readonly symbol: "ETH";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://replicator.phoenix.lightlink.io/rpc/v1"];
        };
    };
    sourceId?: number | undefined;
    testnet: false;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
    readonly network: "lightlink-phoenix";
};
//# sourceMappingURL=lightlinkPhoenix.d.ts.map