{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../errors/base.ts"], "names": [], "mappings": ";;;AAqBA,wCAEC;AAvBD,6CAAsC;AAOtC,IAAI,WAAW,GAAgB;IAC7B,UAAU,EAAE,CAAC,EACX,WAAW,EACX,QAAQ,GAAG,EAAE,EACb,QAAQ,GACY,EAAE,EAAE,CACxB,QAAQ;QACN,CAAC,CAAC,GAAG,WAAW,IAAI,iBAAiB,GAAG,QAAQ,GAC5C,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,CAAC,CAAC,EAC9B,EAAE;QACJ,CAAC,CAAC,SAAS;IACf,OAAO,EAAE,QAAQ,oBAAO,EAAE;CAC3B,CAAA;AAED,SAAgB,cAAc,CAAC,MAAmB;IAChD,WAAW,GAAG,MAAM,CAAA;AACtB,CAAC;AAaD,MAAa,SAAU,SAAQ,KAAK;IASlC,YAAY,YAAoB,EAAE,OAA4B,EAAE;QAC9D,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE;YACpB,IAAI,IAAI,CAAC,KAAK,YAAY,SAAS;gBAAE,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAA;YAC9D,IAAI,IAAI,CAAC,KAAK,EAAE,OAAO;gBAAE,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAA;YAClD,OAAO,IAAI,CAAC,OAAQ,CAAA;QACtB,CAAC,CAAC,EAAE,CAAA;QACJ,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE;YACrB,IAAI,IAAI,CAAC,KAAK,YAAY,SAAS;gBACjC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAA;YAC7C,OAAO,IAAI,CAAC,QAAQ,CAAA;QACtB,CAAC,CAAC,EAAE,CAAA;QACJ,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAA;QAE/D,MAAM,OAAO,GAAG;YACd,YAAY,IAAI,oBAAoB;YACpC,EAAE;YACF,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxD,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3C,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SACpE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEZ,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QA9BhE;;;;;WAAe;QACf;;;;;WAA6B;QAC7B;;;;;WAAmC;QACnC;;;;;WAAoB;QACpB;;;;;WAAe;QAEN;;;;mBAAO,WAAW;WAAA;QA0BzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAA;QACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAA;QAClC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,OAAO,GAAG,oBAAO,CAAA;IACxB,CAAC;IAID,IAAI,CAAC,EAAQ;QACX,OAAO,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IACvB,CAAC;CACF;AA9CD,8BA8CC;AAED,SAAS,IAAI,CACX,GAAY,EACZ,EAA4C;IAE5C,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC;QAAE,OAAO,GAAG,CAAA;IACzB,IACE,GAAG;QACH,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAO,IAAI,GAAG;QACd,GAAG,CAAC,KAAK,KAAK,SAAS;QAEvB,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;IAC5B,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAA;AACxB,CAAC"}