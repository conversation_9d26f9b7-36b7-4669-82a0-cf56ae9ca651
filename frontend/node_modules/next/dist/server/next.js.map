{"version": 3, "sources": ["../../server/next.ts"], "names": ["log", "ServerImpl", "getServerImpl", "undefined", "Promise", "resolve", "require", "default", "NextServer", "constructor", "options", "hostname", "port", "getRequestHandler", "req", "res", "parsedUrl", "requestHandler", "getServerRequestHandler", "getUpgradeHandler", "socket", "head", "server", "getServer", "handleUpgrade", "apply", "setAssetPrefix", "assetPrefix", "preparedAssetPrefix", "logError", "args", "render", "renderToHTML", "renderError", "renderErrorToHTML", "render404", "serveStatic", "prepare", "close", "createServer", "dev", "DevServer", "ServerImplementation", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_SERVER", "dir", "conf", "serverPromise", "setTimeout", "then", "reqHandlerPromise", "bind", "Error", "process", "env", "NODE_ENV", "includes", "warn", "NON_STANDARD_NODE_ENV", "console", "shouldUseReactRoot", "__NEXT_REACT_ROOT", "module", "exports"], "mappings": "AAAA;;;;;QAIO,uBAAuB;AAElBA,IAAAA,GAAG,mCAAM,qBAAqB,EAA3B;AACQ,IAAA,OAAU,kCAAV,UAAU,EAAA;AACT,IAAA,KAAM,WAAN,MAAM,CAAA;AACQ,IAAA,UAAkB,WAAlB,kBAAkB,CAAA;AACf,IAAA,WAAyB,WAAzB,yBAAyB,CAAA;AAI/B,IAAA,MAAS,WAAT,SAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5C,IAAIC,UAAU,AAAe;AAE7B,MAAMC,aAAa,GAAG,UAAY;IAChC,IAAID,UAAU,KAAKE,SAAS,EAC1BF,UAAU,GAAG,CAAC,MAAMG,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAACC,OAAO;IACxE,OAAON,UAAU,CAAA;CAClB;AAYM,MAAMO,UAAU;IAOrBC,YAAYC,OAA0B,CAAE;QACtC,IAAI,CAACA,OAAO,GAAGA,OAAO;KACvB;IAED,IAAIC,QAAQ,GAAG;QACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAA;KAC7B;IAED,IAAIC,IAAI,GAAG;QACT,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI,CAAA;KACzB;IAEDC,iBAAiB,GAAmB;QAClC,OAAO,OACLC,GAAoB,EACpBC,GAAmB,EACnBC,SAA8B,GAC3B;YACH,MAAMC,cAAc,GAAG,MAAM,IAAI,CAACC,uBAAuB,EAAE;YAC3D,OAAOD,cAAc,CAACH,GAAG,EAAEC,GAAG,EAAEC,SAAS,CAAC,CAAA;SAC3C,CAAA;KACF;IAEDG,iBAAiB,GAAG;QAClB,OAAO,OAAOL,GAAoB,EAAEM,MAAW,EAAEC,IAAS,GAAK;YAC7D,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;YACrC,mDAAmD;YACnD,uBAAuB;YACvB,OAAOD,MAAM,CAACE,aAAa,CAACC,KAAK,CAACH,MAAM,EAAE;gBAACR,GAAG;gBAAEM,MAAM;gBAAEC,IAAI;aAAC,CAAC,CAAA;SAC/D,CAAA;KACF;IAEDK,cAAc,CAACC,WAAmB,EAAE;QAClC,IAAI,IAAI,CAACL,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACI,cAAc,CAACC,WAAW,CAAC;SACxC,MAAM;YACL,IAAI,CAACC,mBAAmB,GAAGD,WAAW;SACvC;KACF;IAEDE,QAAQ,CAAC,GAAGC,IAAI,AAAgC,EAAE;QAChD,IAAI,IAAI,CAACR,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACO,QAAQ,IAAIC,IAAI,CAAC;SAC9B;KACF;IAED,MAAMC,MAAM,CAAC,GAAGD,IAAI,AAA8B,EAAE;QAClD,MAAMR,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;QACrC,OAAOD,MAAM,CAACS,MAAM,IAAID,IAAI,CAAC,CAAA;KAC9B;IAED,MAAME,YAAY,CAAC,GAAGF,IAAI,AAAoC,EAAE;QAC9D,MAAMR,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;QACrC,OAAOD,MAAM,CAACU,YAAY,IAAIF,IAAI,CAAC,CAAA;KACpC;IAED,MAAMG,WAAW,CAAC,GAAGH,IAAI,AAAmC,EAAE;QAC5D,MAAMR,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;QACrC,OAAOD,MAAM,CAACW,WAAW,IAAIH,IAAI,CAAC,CAAA;KACnC;IAED,MAAMI,iBAAiB,CAAC,GAAGJ,IAAI,AAAyC,EAAE;QACxE,MAAMR,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;QACrC,OAAOD,MAAM,CAACY,iBAAiB,IAAIJ,IAAI,CAAC,CAAA;KACzC;IAED,MAAMK,SAAS,CAAC,GAAGL,IAAI,AAAiC,EAAE;QACxD,MAAMR,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;QACrC,OAAOD,MAAM,CAACa,SAAS,IAAIL,IAAI,CAAC,CAAA;KACjC;IAED,MAAMM,WAAW,CAAC,GAAGN,IAAI,AAAmC,EAAE;QAC5D,MAAMR,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;QACrC,OAAOD,MAAM,CAACc,WAAW,IAAIN,IAAI,CAAC,CAAA;KACnC;IAED,MAAMO,OAAO,GAAG;QACd,MAAMf,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;QACrC,OAAOD,MAAM,CAACe,OAAO,EAAE,CAAA;KACxB;IAED,MAAMC,KAAK,GAAG;QACZ,MAAMhB,MAAM,GAAG,MAAM,IAAI,CAACC,SAAS,EAAE;QACrC,OAAO,AAACD,MAAM,CAASgB,KAAK,EAAE,CAAA;KAC/B;IAED,MAAcC,YAAY,CAAC7B,OAAyB,EAAmB;QACrE,IAAIA,OAAO,CAAC8B,GAAG,EAAE;YACf,MAAMC,SAAS,GAAGnC,OAAO,CAAC,uBAAuB,CAAC,CAACC,OAAO;YAC1D,OAAO,IAAIkC,SAAS,CAAC/B,OAAO,CAAC,CAAA;SAC9B;QACD,MAAMgC,oBAAoB,GAAG,MAAMxC,aAAa,EAAE;QAClD,OAAO,IAAIwC,oBAAoB,CAAChC,OAAO,CAAC,CAAA;KACzC;IAED,MAAciC,UAAU,GAAG;QACzB,OAAOA,CAAAA,GAAAA,OAAU,AAIhB,CAAA,QAJgB,CACf,IAAI,CAACjC,OAAO,CAAC8B,GAAG,GAAGI,WAAwB,yBAAA,GAAGC,WAAuB,wBAAA,EACrExC,CAAAA,GAAAA,KAAO,AAAyB,CAAA,QAAzB,CAAC,IAAI,CAACK,OAAO,CAACoC,GAAG,IAAI,GAAG,CAAC,EAChC,IAAI,CAACpC,OAAO,CAACqC,IAAI,CAClB,CAAA;KACF;IAED,MAAcxB,SAAS,GAAG;QACxB,IAAI,CAAC,IAAI,CAACyB,aAAa,EAAE;YACvBC,UAAU,CAAC/C,aAAa,EAAE,EAAE,CAAC;YAC7B,IAAI,CAAC8C,aAAa,GAAG,IAAI,CAACL,UAAU,EAAE,CAACO,IAAI,CAAC,OAAOH,IAAI,GAAK;gBAC1D,IAAI,CAACzB,MAAM,GAAG,MAAM,IAAI,CAACiB,YAAY,CAAC;oBACpC,GAAG,IAAI,CAAC7B,OAAO;oBACfqC,IAAI;iBACL,CAAC;gBACF,IAAI,IAAI,CAACnB,mBAAmB,EAAE;oBAC5B,IAAI,CAACN,MAAM,CAACI,cAAc,CAAC,IAAI,CAACE,mBAAmB,CAAC;iBACrD;gBACD,OAAO,IAAI,CAACN,MAAM,CAAA;aACnB,CAAC;SACH;QACD,OAAO,IAAI,CAAC0B,aAAa,CAAA;KAC1B;IAED,MAAc9B,uBAAuB,GAAG;QACtC,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAACiC,iBAAiB,EAAE;YAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAAC5B,SAAS,EAAE,CAAC2B,IAAI,CAAC,CAAC5B,MAAM,GACpDA,MAAM,CAACT,iBAAiB,EAAE,CAACuC,IAAI,CAAC9B,MAAM,CAAC,CACxC;SACF;QACD,OAAO,IAAI,CAAC6B,iBAAiB,CAAA;KAC9B;CACF;QAxIY3C,UAAU,GAAVA,UAAU;AA0IvB,yDAAyD;AACzD,SAAS+B,YAAY,CAAC7B,OAA0B,EAAc;IAC5D,IAAIA,OAAO,IAAI,IAAI,EAAE;QACnB,MAAM,IAAI2C,KAAK,CACb,wGAAwG,CACzG,CAAA;KACF;IAED,IACE,CAAC,CAAC,kBAAkB,IAAI3C,OAAO,CAAC,IAChC4C,OAAO,CAACC,GAAG,CAACC,QAAQ,IACpB,CAAC;QAAC,YAAY;QAAE,aAAa;QAAE,MAAM;KAAC,CAACC,QAAQ,CAACH,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC,EACrE;QACAxD,GAAG,CAAC0D,IAAI,CAACC,UAAqB,sBAAA,CAAC;KAChC;IAED,IAAIjD,OAAO,CAAC8B,GAAG,IAAI,OAAO9B,OAAO,CAAC8B,GAAG,KAAK,SAAS,EAAE;QACnDoB,OAAO,CAACF,IAAI,CACV,oIAAoI,CACrI;KACF;IAED,IAAIG,MAAkB,mBAAA,EAAE;QACpBP,OAAO,CAACC,GAAG,CAASO,iBAAiB,GAAG,MAAM;KACjD;IAED,OAAO,IAAItD,UAAU,CAACE,OAAO,CAAC,CAAA;CAC/B;AAED,qCAAqC;AACrCqD,MAAM,CAACC,OAAO,GAAGzB,YAAY;AAC7ByB,OAAO,GAAGD,MAAM,CAACC,OAAO;eAGTzB,YAAY"}