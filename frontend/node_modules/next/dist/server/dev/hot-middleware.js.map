{"version": 3, "sources": ["../../../server/dev/hot-middleware.ts"], "names": ["isMiddlewareStats", "stats", "key", "compilation", "entrypoints", "keys", "isMiddlewareFilename", "stats<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON>", "all", "errors", "hash", "warnings", "EventStream", "constructor", "clients", "Set", "everyClient", "fn", "client", "close", "clear", "handler", "add", "addEventListener", "delete", "publish", "payload", "send", "JSON", "stringify", "WebpackHotMiddleware", "compilers", "eventStream", "clientLatestStats", "middlewareLatestStats", "serverLatestStats", "closed", "hooks", "invalid", "tap", "onClientInvalid", "done", "onClientDone", "onServerInvalid", "onServerDone", "onEdgeServerDone", "onEdgeServerInvalid", "hasErrors", "action", "statsResult", "ts", "Date", "now", "publishStats", "onHMR", "latestStats", "filter", "nonNullable", "sort", "statsA", "statsB", "middlewareStats"], "mappings": "AAuBA;;;;AAEqC,IAAA,MAAmB,WAAnB,mBAAmB,CAAA;AAC5B,IAAA,YAAwB,WAAxB,wBAAwB,CAAA;AAEpD,SAASA,iBAAiB,CAACC,KAAoB,EAAE;IAC/C,KAAK,MAAMC,GAAG,IAAID,KAAK,CAACE,WAAW,CAACC,WAAW,CAACC,IAAI,EAAE,CAAE;QACtD,IAAIC,CAAAA,GAAAA,MAAoB,AAAK,CAAA,qBAAL,CAACJ,GAAG,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAA;SACZ;KACF;IAED,OAAO,KAAK,CAAA;CACb;AAED,SAASK,WAAW,CAACN,KAA4B,EAAE;IACjD,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE,CAAA;IACrB,OAAOA,KAAK,CAACO,MAAM,CAAC;QAClBC,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,IAAI;KACf,CAAC,CAAA;CACH;AAED,MAAMC,WAAW;IAEfC,aAAc;QACZ,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,EAAE;KACzB;IAEDC,WAAW,CAACC,EAAwB,EAAE;QACpC,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACJ,OAAO,CAAE;YACjCG,EAAE,CAACC,MAAM,CAAC;SACX;KACF;IAEDC,KAAK,GAAG;QACN,IAAI,CAACH,WAAW,CAAC,CAACE,MAAM,GAAK;YAC3BA,MAAM,CAACC,KAAK,EAAE;SACf,CAAC;QACF,IAAI,CAACL,OAAO,CAACM,KAAK,EAAE;KACrB;IAEDC,OAAO,CAACH,MAAU,EAAE;QAClB,IAAI,CAACJ,OAAO,CAACQ,GAAG,CAACJ,MAAM,CAAC;QACxBA,MAAM,CAACK,gBAAgB,CAAC,OAAO,EAAE,IAAM;YACrC,IAAI,CAACT,OAAO,CAACU,MAAM,CAACN,MAAM,CAAC;SAC5B,CAAC;KACH;IAEDO,OAAO,CAACC,OAAY,EAAE;QACpB,IAAI,CAACV,WAAW,CAAC,CAACE,MAAM,GAAK;YAC3BA,MAAM,CAACS,IAAI,CAACC,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC,CAAC;SACrC,CAAC;KACH;CACF;AAEM,MAAMI,oBAAoB;IAO/BjB,YAAYkB,SAA6B,CAAE;QACzC,IAAI,CAACC,WAAW,GAAG,IAAIpB,WAAW,EAAE;QACpC,IAAI,CAACqB,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,qBAAqB,GAAG,IAAI;QACjC,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,MAAM,GAAG,KAAK;QAEnBL,SAAS,CAAC,CAAC,CAAC,CAACM,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,wBAAwB,EACxB,IAAI,CAACC,eAAe,CACrB;QACDT,SAAS,CAAC,CAAC,CAAC,CAACM,KAAK,CAACI,IAAI,CAACF,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACG,YAAY,CAAC;QACxEX,SAAS,CAAC,CAAC,CAAC,CAACM,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,wBAAwB,EACxB,IAAI,CAACI,eAAe,CACrB;QACDZ,SAAS,CAAC,CAAC,CAAC,CAACM,KAAK,CAACI,IAAI,CAACF,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACK,YAAY,CAAC;QACxEb,SAAS,CAAC,CAAC,CAAC,CAACM,KAAK,CAACI,IAAI,CAACF,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACM,gBAAgB,CAAC;QAC5Ed,SAAS,CAAC,CAAC,CAAC,CAACM,KAAK,CAACC,OAAO,CAACC,GAAG,CAC5B,wBAAwB,EACxB,IAAI,CAACO,mBAAmB,CACzB;KACF;IAEDN,eAAe,GAAG,IAAM;YACH,GAAsB;QAAzC,IAAI,IAAI,CAACJ,MAAM,IAAI,CAAA,CAAA,GAAsB,GAAtB,IAAI,CAACD,iBAAiB,SAAO,GAA7B,KAAA,CAA6B,GAA7B,GAAsB,CAAEnC,KAAK,CAAC+C,SAAS,EAAE,CAAA,EAAE,OAAM;QACpE,IAAI,CAACf,WAAW,CAACP,OAAO,CAAC;YAAEuB,MAAM,EAAE,UAAU;SAAE,CAAC;KACjD,CAAA;IAEDN,YAAY,GAAG,CAACO,WAA0B,GAAK;YAE1B,GAAsB;QADzC,IAAI,CAAChB,iBAAiB,GAAG;YAAEiB,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE;YAAEpD,KAAK,EAAEiD,WAAW;SAAE;QAC/D,IAAI,IAAI,CAACb,MAAM,IAAI,CAAA,CAAA,GAAsB,GAAtB,IAAI,CAACD,iBAAiB,SAAO,GAA7B,KAAA,CAA6B,GAA7B,GAAsB,CAAEnC,KAAK,CAAC+C,SAAS,EAAE,CAAA,EAAE,OAAM;QACpE,IAAI,CAACM,YAAY,CAAC,OAAO,EAAEJ,WAAW,CAAC;KACxC,CAAA;IAEDN,eAAe,GAAG,IAAM;YACjB,GAAsB,EAEvB,IAAsB;QAF1B,IAAI,CAAC,CAAA,CAAA,GAAsB,GAAtB,IAAI,CAACR,iBAAiB,SAAO,GAA7B,KAAA,CAA6B,GAA7B,GAAsB,CAAEnC,KAAK,CAAC+C,SAAS,EAAE,CAAA,EAAE,OAAM;QACtD,IAAI,CAACZ,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAAA,IAAsB,GAAtB,IAAI,CAACF,iBAAiB,SAAO,GAA7B,KAAA,CAA6B,GAA7B,IAAsB,CAAEjC,KAAK,EAAE;YACjC,IAAI,CAACqD,YAAY,CAAC,OAAO,EAAE,IAAI,CAACpB,iBAAiB,CAACjC,KAAK,CAAC;SACzD;KACF,CAAA;IAED4C,YAAY,GAAG,CAACK,WAA0B,GAAK;QAC7C,IAAI,IAAI,CAACb,MAAM,EAAE,OAAM;QACvB,IAAIa,WAAW,CAACF,SAAS,EAAE,EAAE;YAC3B,IAAI,CAACZ,iBAAiB,GAAG;gBAAEe,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE;gBAAEpD,KAAK,EAAEiD,WAAW;aAAE;YAC/D,IAAI,CAACI,YAAY,CAAC,OAAO,EAAEJ,WAAW,CAAC;SACxC;KACF,CAAA;IAEDH,mBAAmB,GAAG,IAAM;YACrB,GAA0B,EAE3B,IAAsB;QAF1B,IAAI,CAAC,CAAA,CAAA,GAA0B,GAA1B,IAAI,CAACZ,qBAAqB,SAAO,GAAjC,KAAA,CAAiC,GAAjC,GAA0B,CAAElC,KAAK,CAAC+C,SAAS,EAAE,CAAA,EAAE,OAAM;QAC1D,IAAI,CAACb,qBAAqB,GAAG,IAAI;QACjC,IAAI,CAAA,IAAsB,GAAtB,IAAI,CAACD,iBAAiB,SAAO,GAA7B,KAAA,CAA6B,GAA7B,IAAsB,CAAEjC,KAAK,EAAE;YACjC,IAAI,CAACqD,YAAY,CAAC,OAAO,EAAE,IAAI,CAACpB,iBAAiB,CAACjC,KAAK,CAAC;SACzD;KACF,CAAA;IAED6C,gBAAgB,GAAG,CAACI,WAA0B,GAAK;QACjD,IAAI,CAAClD,iBAAiB,CAACkD,WAAW,CAAC,EAAE;YACnC,IAAI,CAACL,YAAY,CAACK,WAAW,CAAC;YAC9B,OAAM;SACP;QAED,IAAIA,WAAW,CAACF,SAAS,EAAE,EAAE;YAC3B,IAAI,CAACb,qBAAqB,GAAG;gBAAEgB,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE;gBAAEpD,KAAK,EAAEiD,WAAW;aAAE;YACnE,IAAI,CAACI,YAAY,CAAC,OAAO,EAAEJ,WAAW,CAAC;SACxC;KACF,CAAA;IAED;;;;;KAKG,CACHK,KAAK,GAAG,CAACpC,MAAU,GAAK;QACtB,IAAI,IAAI,CAACkB,MAAM,EAAE,OAAM;QACvB,IAAI,CAACJ,WAAW,CAACX,OAAO,CAACH,MAAM,CAAC;QAEhC,MAAM,CAACqC,WAAW,CAAC,GAAG;YAAC,IAAI,CAACtB,iBAAiB;YAAE,IAAI,CAACE,iBAAiB;SAAC,CACnEqB,MAAM,CAACC,YAAW,YAAA,CAAC,CACnBC,IAAI,CAAC,CAACC,MAAM,EAAEC,MAAM,GAAKA,MAAM,CAACV,EAAE,GAAGS,MAAM,CAACT,EAAE,CAAC;QAElD,IAAIK,WAAW,QAAO,GAAlBA,KAAAA,CAAkB,GAAlBA,WAAW,CAAEvD,KAAK,EAAE;gBAEc,GAA0B;YAD9D,MAAMA,KAAK,GAAGM,WAAW,CAACiD,WAAW,CAACvD,KAAK,CAAC;YAC5C,MAAM6D,eAAe,GAAGvD,WAAW,CAAC,CAAA,GAA0B,GAA1B,IAAI,CAAC4B,qBAAqB,SAAO,GAAjC,KAAA,CAAiC,GAAjC,GAA0B,CAAElC,KAAK,CAAC;YACtE,IAAI,CAACgC,WAAW,CAACP,OAAO,CAAC;gBACvBuB,MAAM,EAAE,MAAM;gBACdtC,IAAI,EAAEV,KAAK,CAACU,IAAI;gBAChBD,MAAM,EAAE;uBAAKT,KAAK,CAACS,MAAM,IAAI,EAAE;uBAAOoD,eAAe,CAACpD,MAAM,IAAI,EAAE;iBAAE;gBACpEE,QAAQ,EAAE;uBACJX,KAAK,CAACW,QAAQ,IAAI,EAAE;uBACpBkD,eAAe,CAAClD,QAAQ,IAAI,EAAE;iBACnC;aACF,CAAC;SACH;KACF,CAAA;IAED0C,YAAY,GAAG,CAACL,MAAc,EAAEC,WAA0B,GAAK;QAC7D,MAAMjD,KAAK,GAAGiD,WAAW,CAAC1C,MAAM,CAAC;YAC/BC,GAAG,EAAE,KAAK;YACVE,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,IAAI;YACdF,MAAM,EAAE,IAAI;SACb,CAAC;QAEF,IAAI,CAACuB,WAAW,CAACP,OAAO,CAAC;YACvBuB,MAAM,EAAEA,MAAM;YACdtC,IAAI,EAAEV,KAAK,CAACU,IAAI;YAChBC,QAAQ,EAAEX,KAAK,CAACW,QAAQ,IAAI,EAAE;YAC9BF,MAAM,EAAET,KAAK,CAACS,MAAM,IAAI,EAAE;SAC3B,CAAC;KACH,CAAA;IAEDgB,OAAO,GAAG,CAACC,OAAY,GAAK;QAC1B,IAAI,IAAI,CAACU,MAAM,EAAE,OAAM;QACvB,IAAI,CAACJ,WAAW,CAACP,OAAO,CAACC,OAAO,CAAC;KAClC,CAAA;IACDP,KAAK,GAAG,IAAM;QACZ,IAAI,IAAI,CAACiB,MAAM,EAAE,OAAM;QACvB,0EAA0E;QAC1E,sEAAsE;QACtE,IAAI,CAACA,MAAM,GAAG,IAAI;QAClB,IAAI,CAACJ,WAAW,CAACb,KAAK,EAAE;KACzB,CAAA;CACF;QAtIYW,oBAAoB,GAApBA,oBAAoB"}