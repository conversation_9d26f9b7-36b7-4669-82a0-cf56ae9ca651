{"version": 3, "sources": ["../../server/font-utils.ts"], "names": ["getFontDefinitionFromNetwork", "getFontDefinitionFromManifest", "getFontOverrideCss", "Log", "googleFontsMetrics", "require", "https", "CHROME_UA", "IE_UA", "isGoogleFont", "url", "startsWith", "GOOGLE_FONT_PROVIDER", "getFontForUA", "UA", "Promise", "resolve", "reject", "rawData", "get", "headers", "res", "on", "chunk", "toString", "e", "result", "warn", "manifest", "find", "font", "content", "parseGoogleFontName", "css", "regex", "matches", "matchAll", "fontNames", "Set", "fontFamily", "replace", "add", "calculateOverrideCSS", "fontMetrics", "fontName", "toLowerCase", "trim", "font<PERSON>ey", "category", "ascentOverride", "descentOverride", "lineGapOverride", "fallbackFont", "DEFAULT_SERIF_FONT", "DEFAULT_SANS_SERIF_FONT", "ascent", "toFixed", "descent", "lineGap", "fontCss", "reduce", "cssStr", "console", "log"], "mappings": "AAAA;;;;QAkDsBA,4BAA4B,GAA5BA,4BAA4B;QAuBlCC,6BAA6B,GAA7BA,6BAA6B;QAiD7BC,kBAAkB,GAAlBA,kBAAkB;AA1HtBC,IAAAA,GAAG,mCAAM,qBAAqB,EAA3B;AAKR,IAAA,UAAyB,WAAzB,yBAAyB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAChC,MAAMC,kBAAkB,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAChE,MAAMC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAE9B,MAAME,SAAS,GACb,0HAA0H;AAC5H,MAAMC,KAAK,GAAG,gEAAgE;AAS9E,SAASC,YAAY,CAACC,GAAW,EAAW;IAC1C,OAAOA,GAAG,CAACC,UAAU,CAACC,UAAoB,qBAAA,CAAC,CAAA;CAC5C;AAED,SAASC,YAAY,CAACH,GAAW,EAAEI,EAAU,EAAmB;IAC9D,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,GAAK;QACtC,IAAIC,OAAO,GAAQ,EAAE;QACrBZ,KAAK,CACFa,GAAG,CACFT,GAAG,EACH;YACEU,OAAO,EAAE;gBACP,YAAY,EAAEN,EAAE;aACjB;SACF,EACD,CAACO,GAAQ,GAAK;YACZA,GAAG,CAACC,EAAE,CAAC,MAAM,EAAE,CAACC,KAAU,GAAK;gBAC7BL,OAAO,IAAIK,KAAK;aACjB,CAAC;YACFF,GAAG,CAACC,EAAE,CAAC,KAAK,EAAE,IAAM;gBAClBN,OAAO,CAACE,OAAO,CAACM,QAAQ,CAAC,MAAM,CAAC,CAAC;aAClC,CAAC;SACH,CACF,CACAF,EAAE,CAAC,OAAO,EAAE,CAACG,CAAQ,GAAK;YACzBR,MAAM,CAACQ,CAAC,CAAC;SACV,CAAC;KACL,CAAC,CAAA;CACH;AAEM,eAAezB,4BAA4B,CAChDU,GAAW,EACM;IACjB,IAAIgB,MAAM,GAAG,EAAE;IACf;;;KAGG,CACH,IAAI;QACF,IAAIjB,YAAY,CAACC,GAAG,CAAC,EAAE;YACrBgB,MAAM,IAAI,MAAMb,YAAY,CAACH,GAAG,EAAEF,KAAK,CAAC;SACzC;QACDkB,MAAM,IAAI,MAAMb,YAAY,CAACH,GAAG,EAAEH,SAAS,CAAC;KAC7C,CAAC,OAAOkB,CAAC,EAAE;QACVtB,GAAG,CAACwB,IAAI,CACN,CAAC,sCAAsC,EAAEjB,GAAG,CAAC,+BAA+B,CAAC,CAC9E;QACD,OAAO,EAAE,CAAA;KACV;IAED,OAAOgB,MAAM,CAAA;CACd;AAEM,SAASzB,6BAA6B,CAC3CS,GAAW,EACXkB,QAAsB,EACd;QAENA,GAKE;IANJ,OACEA,CAAAA,CAAAA,GAKE,GALFA,QAAQ,CAACC,IAAI,CAAC,CAACC,IAAI,GAAK;QACtB,IAAIA,IAAI,IAAIA,IAAI,CAACpB,GAAG,KAAKA,GAAG,EAAE;YAC5B,OAAO,IAAI,CAAA;SACZ;QACD,OAAO,KAAK,CAAA;KACb,CAAC,SAAS,GALXkB,KAAAA,CAKW,GALXA,GAKE,CAAEG,OAAO,CAAA,IAAI,EAAE,CAClB;CACF;AAED,SAASC,mBAAmB,CAACC,GAAW,EAAiB;IACvD,MAAMC,KAAK,0BAA0B;IACrC,MAAMC,OAAO,GAAGF,GAAG,CAACG,QAAQ,CAACF,KAAK,CAAC;IACnC,MAAMG,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,KAAK,IAAIR,IAAI,IAAIK,OAAO,CAAE;QACxB,MAAMI,UAAU,GAAGT,IAAI,CAAC,CAAC,CAAC,CAACU,OAAO,iBAAiB,EAAE,CAAC;QACtDH,SAAS,CAACI,GAAG,CAACF,UAAU,CAAC;KAC1B;IAED,OAAO;WAAIF,SAAS;KAAC,CAAA;CACtB;AAED,SAASK,oBAAoB,CAACZ,IAAY,EAAEa,WAAgB,EAAE;IAC5D,MAAMC,QAAQ,GAAGd,IAAI,CAACe,WAAW,EAAE,CAACC,IAAI,EAAE,CAACN,OAAO,OAAO,GAAG,CAAC;IAC7D,MAAMO,OAAO,GAAGjB,IAAI,CAACe,WAAW,EAAE,CAACC,IAAI,EAAE,CAACN,OAAO,OAAO,EAAE,CAAC;IAC3D,MAAM,EAAEQ,QAAQ,CAAA,EAAEC,cAAc,CAAA,EAAEC,eAAe,CAAA,EAAEC,eAAe,CAAA,EAAE,GAClER,WAAW,CAACI,OAAO,CAAC;IACtB,MAAMK,YAAY,GAChBJ,QAAQ,KAAK,OAAO,GAAGK,UAAkB,mBAAA,GAAGC,UAAuB,wBAAA;IACrE,MAAMC,MAAM,GAAG,CAACN,cAAc,GAAG,GAAG,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC;IAChD,MAAMC,OAAO,GAAG,CAACP,eAAe,GAAG,GAAG,CAAC,CAACM,OAAO,CAAC,CAAC,CAAC;IAClD,MAAME,OAAO,GAAG,CAACP,eAAe,GAAG,GAAG,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC;IAElD,OAAO,CAAC;;oBAEU,EAAEZ,QAAQ,CAAC;uBACR,EAAEW,MAAM,CAAC;wBACR,EAAEE,OAAO,CAAC;yBACT,EAAEC,OAAO,CAAC;kBACjB,EAAEN,YAAY,CAAC;;EAE/B,CAAC,CAAA;CACF;AAEM,SAASlD,kBAAkB,CAACQ,GAAW,EAAEuB,GAAW,EAAE;IAC3D,IAAI,CAACxB,YAAY,CAACC,GAAG,CAAC,EAAE;QACtB,OAAO,EAAE,CAAA;KACV;IAED,IAAI;QACF,MAAM2B,SAAS,GAAGL,mBAAmB,CAACC,GAAG,CAAC;QAC1C,MAAMU,WAAW,GAAGvC,kBAAkB;QAEtC,MAAMuD,OAAO,GAAGtB,SAAS,CAACuB,MAAM,CAAC,CAACC,MAAM,EAAEjB,QAAQ,GAAK;YACrDiB,MAAM,IAAInB,oBAAoB,CAACE,QAAQ,EAAED,WAAW,CAAC;YACrD,OAAOkB,MAAM,CAAA;SACd,EAAE,EAAE,CAAC;QAEN,OAAOF,OAAO,CAAA;KACf,CAAC,OAAOlC,CAAC,EAAE;QACVqC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEtC,CAAC,CAAC;QACvD,OAAO,EAAE,CAAA;KACV;CACF"}