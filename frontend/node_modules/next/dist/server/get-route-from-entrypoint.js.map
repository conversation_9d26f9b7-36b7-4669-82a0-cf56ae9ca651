{"version": 3, "sources": ["../../server/get-route-from-entrypoint.ts"], "names": ["getRouteFromEntrypoint", "entryFile", "app", "pagePath", "matchBundle", "SERVER_ROUTE_NAME_REGEX", "getAppRouteFromEntrypoint", "BROWSER_ROUTE_NAME_REGEX"], "mappings": "AAAA;;;;kBASwBA,sBAAsB;AATR,IAAA,0BAAiC,kCAAjC,iCAAiC,EAAA;AAC/C,IAAA,YAAgB,kCAAhB,gBAAgB,EAAA;AAQzB,SAASA,sBAAsB,CAC5CC,SAAiB,EACjBC,GAAa,EACE;IACf,IAAIC,QAAQ,GAAGC,CAAAA,GAAAA,YAAW,AAAoC,CAAA,QAApC,CAACC,uBAAuB,EAAEJ,SAAS,CAAC;IAE9D,IAAIE,QAAQ,EAAE;QACZ,OAAOA,QAAQ,CAAA;KAChB;IAED,IAAID,GAAG,EAAE;QACPC,QAAQ,GAAGG,CAAAA,GAAAA,0BAAyB,AAAW,CAAA,QAAX,CAACL,SAAS,CAAC;QAC/C,IAAIE,QAAQ,EAAE,OAAOA,QAAQ,CAAA;KAC9B;IAED,+EAA+E;IAC/E,OAAOC,CAAAA,GAAAA,YAAW,AAAqC,CAAA,QAArC,CAACG,wBAAwB,EAAEN,SAAS,CAAC,CAAA;CACxD;;;;;;AAvBD,0BAA0B;AAC1B,MAAMI,uBAAuB,qBAAqB;AAElD,iCAAiC;AACjC,MAAME,wBAAwB,gCAAgC"}