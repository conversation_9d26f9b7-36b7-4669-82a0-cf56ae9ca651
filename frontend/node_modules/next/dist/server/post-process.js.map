{"version": 3, "sources": ["../../server/post-process.ts"], "names": ["optimizeAmp", "getFontDefinitionFromManifest", "parse", "process", "env", "NEXT_RUNTIME", "require", "default", "middlewareRegistry", "registerPostProcessor", "name", "middleware", "condition", "push", "processHTML", "html", "data", "options", "root", "document", "callMiddleWare", "inspectData", "inspect", "mutate", "i", "length", "FontOptimizerMiddleware", "originalDom", "getFontDefinition", "fontDefinitions", "querySelectorAll", "filter", "tag", "getAttribute", "hasAttribute", "OPTIMIZED_FONT_PROVIDERS", "some", "url", "dataHref", "startsWith", "for<PERSON>ach", "element", "nonce", "markup", "result", "preconnectUrls", "Set", "fontDef", "fallBackLinkTag", "indexOf", "fontContent", "replace", "nonceStr", "escapedUrl", "fontRegex", "RegExp", "provider", "find", "p", "add", "preconnect", "preconnectTag", "postProcessHTML", "pathname", "content", "renderOpts", "inAmpMode", "hybridAmp", "postProcessors", "ampOptimizerConfig", "ampSkipValidation", "ampValidator", "optimizeFonts", "fontManifest", "optimizeCss", "Critters", "cssOptimizer", "ssrMode", "reduceInlineStyles", "path", "distDir", "publicPath", "assetPrefix", "preload", "fonts", "nonNullable", "postProcessor", "__NEXT_OPTIMIZE_FONTS"], "mappings": "AAAA;;;;;AAGyC,IAAA,UAAyB,WAAzB,yBAAyB,CAAA;AACtC,IAAA,YAAqB,WAArB,qBAAqB,CAAA;AAEjD,IAAIA,WAAW,AAAqD;AACpE,IAAIC,6BAA6B,AAEpB;AACb,IAAIC,KAAK,AAA4D;AAErE,IAAIC,OAAO,CAACC,GAAG,CAACC,YAAY,KAAK,MAAM,EAAE;IACvCL,WAAW,GAAGM,OAAO,CAAC,gBAAgB,CAAC,CAACC,OAAO;IAC/CN,6BAA6B,GAC3BK,OAAO,CAAC,cAAc,CAAC,CAACL,6BAA6B;IACvDC,KAAK,GAAG,AACNI,OAAO,CAAC,qCAAqC,CAAC,CAC9CJ,KAAK;CACR;AAoBD,MAAMM,kBAAkB,GAA+B,EAAE;AAEzD,SAASC,qBAAqB,CAC5BC,IAAY,EACZC,UAAiC,EACjCC,SAAoD,EACpD;IACAJ,kBAAkB,CAACK,IAAI,CAAC;QAAEH,IAAI;QAAEC,UAAU;QAAEC,SAAS,EAAEA,SAAS,IAAI,IAAI;KAAE,CAAC;CAC5E;AAED,eAAeE,WAAW,CACxBC,IAAY,EACZC,IAAmB,EACnBC,OAA2B,EACV;IACjB,+DAA+D;IAC/D,IAAI,CAACT,kBAAkB,CAAC,CAAC,CAAC,EAAE;QAC1B,OAAOO,IAAI,CAAA;KACZ;IACD,MAAMG,IAAI,GAAgBhB,KAAK,CAACa,IAAI,CAAC;IACrC,IAAII,QAAQ,GAAGJ,IAAI;IAEnB,8DAA8D;IAC9D,eAAeK,cAAc,CAACT,UAAiC,EAAE;QAC/D,yBAAyB;QACzB,MAAMU,WAAW,GAAGV,UAAU,CAACW,OAAO,CAACJ,IAAI,EAAEF,IAAI,CAAC;QAClDG,QAAQ,GAAG,MAAMR,UAAU,CAACY,MAAM,CAACJ,QAAQ,EAAEE,WAAW,EAAEL,IAAI,CAAC;QAC/D,6BAA6B;QAC7B,wCAAwC;QACxC,gEAAgE;QAChE,gDAAgD;QAChD,IAAI;QACJ,OAAM;KACP;IAED,IAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,kBAAkB,CAACiB,MAAM,EAAED,CAAC,EAAE,CAAE;QAClD,IAAIb,UAAU,GAAGH,kBAAkB,CAACgB,CAAC,CAAC;QACtC,IAAI,CAACb,UAAU,CAACC,SAAS,IAAID,UAAU,CAACC,SAAS,CAACK,OAAO,CAAC,EAAE;YAC1D,MAAMG,cAAc,CAACZ,kBAAkB,CAACgB,CAAC,CAAC,CAACb,UAAU,CAAC;SACvD;KACF;IAED,OAAOQ,QAAQ,CAAA;CAChB;AAED,MAAMO,uBAAuB;IAC3BJ,OAAO,CAACK,WAAwB,EAAEV,OAAsB,EAAE;QACxD,IAAI,CAACA,OAAO,CAACW,iBAAiB,EAAE;YAC9B,OAAM;SACP;QACD,MAAMC,eAAe,GAA6B,EAAE;QACpD,gDAAgD;QAChDF,WAAW,CACRG,gBAAgB,CAAC,MAAM,CAAC,CACxBC,MAAM,CACL,CAACC,GAAgB,GACfA,GAAG,CAACC,YAAY,CAAC,KAAK,CAAC,KAAK,YAAY,IACxCD,GAAG,CAACE,YAAY,CAAC,WAAW,CAAC,IAC7BC,UAAwB,yBAAA,CAACC,IAAI,CAAC,CAAC,EAAEC,GAAG,CAAA,EAAE,GAAK;gBACzC,MAAMC,QAAQ,GAAGN,GAAG,CAACC,YAAY,CAAC,WAAW,CAAC;gBAC9C,OAAOK,QAAQ,GAAGA,QAAQ,CAACC,UAAU,CAACF,GAAG,CAAC,GAAG,KAAK,CAAA;aACnD,CAAC,CACL,CACAG,OAAO,CAAC,CAACC,OAAoB,GAAK;YACjC,MAAMJ,GAAG,GAAGI,OAAO,CAACR,YAAY,CAAC,WAAW,CAAC;YAC7C,MAAMS,KAAK,GAAGD,OAAO,CAACR,YAAY,CAAC,OAAO,CAAC;YAE3C,IAAII,GAAG,EAAE;gBACPR,eAAe,CAAChB,IAAI,CAAC;oBAACwB,GAAG;oBAAEK,KAAK;iBAAC,CAAC;aACnC;SACF,CAAC;QAEJ,OAAOb,eAAe,CAAA;KACvB;IACDN,MAAM,GAAG,OACPoB,MAAc,EACdd,eAA2B,EAC3BZ,OAAsB,GACnB;QACH,IAAI2B,MAAM,GAAGD,MAAM;QACnB,IAAIE,cAAc,GAAG,IAAIC,GAAG,EAAU;QAEtC,IAAI,CAAC7B,OAAO,CAACW,iBAAiB,EAAE;YAC9B,OAAOe,MAAM,CAAA;SACd;QAEDd,eAAe,CAACW,OAAO,CAAC,CAACO,OAAO,GAAK;YACnC,MAAM,CAACV,GAAG,EAAEK,KAAK,CAAC,GAAGK,OAAO;YAC5B,MAAMC,eAAe,GAAG,CAAC,6BAA6B,EAAEX,GAAG,CAAC,GAAG,CAAC;YAChE,IACEO,MAAM,CAACK,OAAO,CAAC,CAAC,kBAAkB,EAAEZ,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IACjDO,MAAM,CAACK,OAAO,CAACD,eAAe,CAAC,GAAG,CAAC,CAAC,EACpC;gBACA,oEAAoE;gBACpE,OAAM;aACP;YACD,MAAME,WAAW,GAAGjC,OAAO,CAACW,iBAAiB,GACzCX,OAAO,CAACW,iBAAiB,CAACS,GAAG,CAAW,GACxC,IAAI;YACR,IAAI,CAACa,WAAW,EAAE;gBAChB;;WAEG,CACHN,MAAM,GAAGA,MAAM,CAACO,OAAO,CAAC,SAAS,EAAE,CAAC,EAAEH,eAAe,CAAC,OAAO,CAAC,CAAC;aAChE,MAAM;gBACL,MAAMI,QAAQ,GAAGV,KAAK,GAAG,CAAC,QAAQ,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;gBACjDE,MAAM,GAAGA,MAAM,CAACO,OAAO,CACrB,SAAS,EACT,CAAC,kBAAkB,EAAEd,GAAG,CAAC,CAAC,EAAEe,QAAQ,CAAC,CAAC,EAAEF,WAAW,CAAC,eAAe,CAAC,CACrE;gBAED,wBAAwB;gBACxB,MAAMG,UAAU,GAAGhB,GAAG,CACnBc,OAAO,OAAO,OAAO,CAAC,CACtBA,OAAO,wBAAwB,MAAM,CAAC;gBACzC,MAAMG,SAAS,GAAG,IAAIC,MAAM,CAC1B,CAAC,qBAAqB,EAAEF,UAAU,CAAC,QAAQ,CAAC,CAC7C;gBACDT,MAAM,GAAGA,MAAM,CAACO,OAAO,CAACG,SAAS,EAAE,EAAE,CAAC;gBAEtC,MAAME,QAAQ,GAAGrB,UAAwB,yBAAA,CAACsB,IAAI,CAAC,CAACC,CAAC,GAC/CrB,GAAG,CAACE,UAAU,CAACmB,CAAC,CAACrB,GAAG,CAAC,CACtB;gBAED,IAAImB,QAAQ,EAAE;oBACZX,cAAc,CAACc,GAAG,CAACH,QAAQ,CAACI,UAAU,CAAC;iBACxC;aACF;SACF,CAAC;QAEF,IAAIC,aAAa,GAAG,EAAE;QACtBhB,cAAc,CAACL,OAAO,CAAC,CAACH,GAAG,GAAK;YAC9BwB,aAAa,IAAI,CAAC,6BAA6B,EAAExB,GAAG,CAAC,gBAAgB,CAAC;SACvE,CAAC;QAEFO,MAAM,GAAGA,MAAM,CAACO,OAAO,CACrB,qCAAqC,EACrCU,aAAa,CACd;QAED,OAAOjB,MAAM,CAAA;KACd,CAAA;CACF;AAED,eAAekB,eAAe,CAC5BC,QAAgB,EAChBC,OAAe,EACfC,UAAsB,EACtB,EAAEC,SAAS,CAAA,EAAEC,SAAS,CAAA,EAA8C,EACpE;IACA,MAAMC,cAAc,GAA6C;QAC/DjE,OAAO,CAACC,GAAG,CAACC,YAAY,KAAK,MAAM,IAAI6D,SAAS,GAC5C,OAAOnD,IAAY,GAAK;YACtBA,IAAI,GAAG,MAAMf,WAAW,CAAEe,IAAI,EAAEkD,UAAU,CAACI,kBAAkB,CAAC;YAC9D,IAAI,CAACJ,UAAU,CAACK,iBAAiB,IAAIL,UAAU,CAACM,YAAY,EAAE;gBAC5D,MAAMN,UAAU,CAACM,YAAY,CAACxD,IAAI,EAAEgD,QAAQ,CAAC;aAC9C;YACD,OAAOhD,IAAI,CAAA;SACZ,GACD,IAAI;QACRZ,OAAO,CAACC,GAAG,CAACC,YAAY,KAAK,MAAM,IAAI4D,UAAU,CAACO,aAAa,GAC3D,OAAOzD,IAAY,GAAK;YACtB,MAAMa,iBAAiB,GAAG,CAACS,GAAW,GAAa;gBACjD,IAAI4B,UAAU,CAACQ,YAAY,EAAE;oBAC3B,OAAOxE,6BAA6B,CAClCoC,GAAG,EACH4B,UAAU,CAACQ,YAAY,CACxB,CAAA;iBACF;gBACD,OAAO,EAAE,CAAA;aACV;YACD,OAAO,MAAM3D,WAAW,CACtBC,IAAI,EACJ;gBAAEa,iBAAiB;aAAE,EACrB;gBACE4C,aAAa,EAAEP,UAAU,CAACO,aAAa;aACxC,CACF,CAAA;SACF,GACD,IAAI;QACRrE,OAAO,CAACC,GAAG,CAACC,YAAY,KAAK,MAAM,IAAI4D,UAAU,CAACS,WAAW,GACzD,OAAO3D,IAAY,GAAK;YACtB,6DAA6D;YAC7D,MAAM4D,QAAQ,GAAGrE,OAAO,CAAC,UAAU,CAAC;YACpC,MAAMsE,YAAY,GAAG,IAAID,QAAQ,CAAC;gBAChCE,OAAO,EAAE,IAAI;gBACbC,kBAAkB,EAAE,KAAK;gBACzBC,IAAI,EAAEd,UAAU,CAACe,OAAO;gBACxBC,UAAU,EAAE,CAAC,EAAEhB,UAAU,CAACiB,WAAW,CAAC,OAAO,CAAC;gBAC9CC,OAAO,EAAE,OAAO;gBAChBC,KAAK,EAAE,KAAK;gBACZ,GAAGnB,UAAU,CAACS,WAAW;aAC1B,CAAC;YACF,OAAO,MAAME,YAAY,CAACzE,OAAO,CAACY,IAAI,CAAC,CAAA;SACxC,GACD,IAAI;QACRmD,SAAS,IAAIC,SAAS,GAClB,OAAOpD,IAAY,GAAK;YACtB,OAAOA,IAAI,CAACoC,OAAO,gBAAgB,QAAQ,CAAC,CAAA;SAC7C,GACD,IAAI;KACT,CAACpB,MAAM,CAACsD,YAAW,YAAA,CAAC;IAErB,KAAK,MAAMC,aAAa,IAAIlB,cAAc,CAAE;QAC1C,IAAIkB,aAAa,EAAE;YACjBtB,OAAO,GAAG,MAAMsB,aAAa,CAACtB,OAAO,CAAC;SACvC;KACF;IACD,OAAOA,OAAO,CAAA;CACf;AAED,iBAAiB;AACjBvD,qBAAqB,CACnB,cAAc,EACd,IAAIiB,uBAAuB,EAAE,EAC7B,sFAAsF;AACtF,aAAa;AACb,CAACT,OAAO,GAAKA,OAAO,CAACuD,aAAa,IAAIrE,OAAO,CAACC,GAAG,CAACmF,qBAAqB,CACxE;QAEQzB,eAAe,GAAfA,eAAe"}