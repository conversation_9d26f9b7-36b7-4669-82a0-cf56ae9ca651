{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "names": ["ImageError", "ImageOptimizerCache", "detectContentType", "fetchExternalImage", "fetchInternalImage", "getHash", "getImageSize", "getMaxAge", "imageOptimizer", "optimizeImage", "sendResponse", "AVIF", "WEBP", "PNG", "JPEG", "GIF", "SVG", "ICO", "CACHE_VERSION", "ANIMATABLE_TYPES", "VECTOR_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "sharp", "require", "process", "env", "NEXT_SHARP_PATH", "concurrency", "divisor", "NODE_ENV", "Math", "floor", "max", "cpus", "length", "e", "showSharpMissingWarning", "getSupportedMimeType", "options", "accept", "mimeType", "mediaType", "includes", "items", "hash", "createHash", "item", "update", "String", "digest", "replace", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "etag", "filename", "join", "promises", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "every", "b", "i", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "url", "w", "q", "href", "Log", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "hrefParsed", "URL", "toString", "_error", "protocol", "hasMatch", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "revalidateAfter", "curRevalidate", "isStale", "_", "set", "revalidate", "Error", "err", "error", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "age", "endsWith", "slice", "n", "contentType", "height", "nextConfigOutput", "optimizedBuffer", "transformer", "sequentialRead", "rotate", "resize", "undefined", "withoutEnlargement", "avif", "avifQuality", "chromaSubsampling", "webp", "png", "jpeg", "progressive", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "getOrientation", "operations", "Orientation", "RIGHT_TOP", "type", "numRotations", "BOTTOM_RIGHT", "LEFT_BOTTOM", "processBuffer", "res", "fetch", "ok", "status", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "cacheControl", "_req", "_res", "handleRequest", "mocked", "createRequestResponseMocks", "method", "socket", "nodeUrl", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageUpstream", "paramsResult", "upstreamBuffer", "upstreamType", "dangerouslyAllowSVG", "isAnimated", "getExtension", "output", "getMetadata", "meta", "opts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getImageBlurSvg", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "sendEtagResponse", "finished", "contentDisposition", "contentDispositionType", "contentSecurityPolicy", "getContentType", "result", "byteLength", "end", "metadata", "decodeBuffer", "imageSizeOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IA8XaA,UAAU;eAAVA;;IAnOAC,mBAAmB;eAAnBA;;IArCGC,iBAAiB;eAAjBA;;IAwaMC,kBAAkB;eAAlBA;;IAkBAC,kBAAkB;eAAlBA;;IA3dNC,OAAO;eAAPA;;IAutBMC,YAAY;eAAZA;;IA7YNC,SAAS;eAATA;;IA2LMC,cAAc;eAAdA;;IA5KAC,aAAa;eAAbA;;IA8VNC,YAAY;eAAZA;;;wBA5wBW;oBACF;oBACJ;wBAEK;2EACK;gCACa;kEACpB;mEACD;sBACF;4DAC4B;8BAEjB;oCAEP;6BAEkB;6BAWV;6BACY;6DACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIrB,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACP;IAAMC;IAAKE;CAAI;AACzC,MAAMK,eAAe;IAACJ;CAAI;AAC1B,MAAMK,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,IAAI;IACFA,QAAQC,QAAQC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAC/C,IAAIJ,SAASA,MAAMK,WAAW,KAAK,GAAG;QACpC,2DAA2D;QAC3D,8DAA8D;QAC9D,0DAA0D;QAC1D,MAAMC,UAAUJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,gBAAgB,IAAI;QAC7DP,MAAMK,WAAW,CAACG,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACC,IAAAA,QAAI,IAAGC,MAAM,GAAGN,SAAS;IACjE;AACF,EAAE,OAAOO,GAAG;AACV,iEAAiE;AACnE;AAEA,IAAIC,0BAA0BZ,QAAQC,GAAG,CAACI,QAAQ,KAAK;AAmBvD,SAASQ,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWC,IAAAA,iBAAS,EAACF,QAAQD;IACnC,OAAOC,OAAOG,QAAQ,CAACF,YAAYA,WAAW;AAChD;AAEO,SAASpC,QAAQuC,KAAmC;IACzD,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;IACxB,KAAK,IAAIC,QAAQH,MAAO;QACtB,IAAI,OAAOG,SAAS,UAAUF,KAAKG,MAAM,CAACC,OAAOF;aAC5C;YACHF,KAAKG,MAAM,CAACD;QACd;IACF;IACA,qDAAqD;IACrD,OAAOF,KAAKK,MAAM,CAAC,UAAUC,OAAO,CAAC,OAAO;AAC9C;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAY;IAEZ,MAAMC,WAAWC,IAAAA,UAAI,EAACP,KAAK,CAAC,EAAEE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAEJ,UAAU,CAAC;IAEvE,MAAMO,YAAQ,CAACC,EAAE,CAACT,KAAK;QAAEU,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMJ,YAAQ,CAACK,KAAK,CAACb,KAAK;QAAEU,WAAW;IAAK;IAC5C,MAAMF,YAAQ,CAACM,SAAS,CAACR,UAAUF;AACrC;AAOO,SAASvD,kBAAkBuD,MAAc;IAC9C,IAAI;QAAC;QAAM;QAAM;KAAK,CAACW,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACvD,OAAOvD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACsD,KAAK,CACpD,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAE1B;QACA,OAAOxD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACuD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOtD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAACqD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAOzD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACwD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACnE,OAAOrD;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACoD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAO1D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOpD;IACT;IACA,OAAO;AACT;AAEO,MAAMhB;IAIX,OAAOsE,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD;QARvB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGb;QACtB,IAAIc;QAEJ,IAAIP,QAAQ7C,MAAM,GAAG,GAAG;YACtBqD,KAAIC,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACL,KAAK;YACR,OAAO;gBAAEM,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACR,MAAM;YAC7B,OAAO;gBAAEM,cAAc;YAAqC;QAC9D;QAEA,IAAIN,IAAIjD,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAEuD,cAAc;YAA8B;QACvD;QAEA,IAAIN,IAAIS,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAIN,IAAIS,UAAU,CAAC,iBAAiB;YAClC,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIV,IAAIS,UAAU,CAAC,MAAM;YACvBN,OAAOH;YACPU,aAAa;QACf,OAAO;YACL,IAAIC;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIZ;gBACrBG,OAAOQ,WAAWE,QAAQ;gBAC1BH,aAAa;YACf,EAAE,OAAOI,QAAQ;gBACf,OAAO;oBAAER,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAAC/C,QAAQ,CAACoD,WAAWI,QAAQ,GAAG;gBACtD,OAAO;oBAAET,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAACU,IAAAA,4BAAQ,EAACpB,SAASG,gBAAgBY,aAAa;gBAClD,OAAO;oBAAEL,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACL,GAAG;YACN,OAAO;gBAAEK,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACP,IAAI;YAC3B,OAAO;gBAAEK,cAAc;YAA2C;QACpE;QAEA,IAAI,CAACJ,GAAG;YACN,OAAO;gBAAEI,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACN,IAAI;YAC3B,OAAO;gBAAEI,cAAc;YAA6C;QACtE;QAEA,MAAMW,QAAQC,SAASjB,GAAG;QAE1B,IAAIgB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLX,cAAc;YAChB;QACF;QAEA,MAAMc,QAAQ;eAAK1B,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACT6B,MAAMC,IAAI,CAACpF;QACb;QAEA,MAAMqF,cACJF,MAAM7D,QAAQ,CAAC0D,UAAW1B,SAAS0B,SAAShF;QAE9C,IAAI,CAACqF,aAAa;YAChB,OAAO;gBACLhB,cAAc,CAAC,yBAAyB,EAAEW,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAAShB;QAEzB,IAAIiB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLjB,cACE;YACJ;QACF;QAEA,MAAMjD,WAAWH,qBAAqB4C,WAAW,EAAE,EAAEV,IAAIoC,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAWzB,IAAIS,UAAU,CAC7B,CAAC,EAAEnB,WAAWoC,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACLvB;YACAiB;YACAV;YACAe;YACAR;YACAM;YACAlE;YACAwC;QACF;IACF;IAEA,OAAO8B,YAAY,EACjBxB,IAAI,EACJc,KAAK,EACLM,OAAO,EACPlE,QAAQ,EAMT,EAAU;QACT,OAAOpC,QAAQ;YAACa;YAAeqE;YAAMc;YAAOM;YAASlE;SAAS;IAChE;IAEAuE,YAAY,EACVC,OAAO,EACPvC,UAAU,EAIX,CAAE;QACD,IAAI,CAACwC,QAAQ,GAAGtD,IAAAA,UAAI,EAACqD,SAAS,SAAS;QACvC,IAAI,CAACvC,UAAU,GAAGA;IACpB;IAEA,MAAMyC,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAWtD,IAAAA,UAAI,EAAC,IAAI,CAACsD,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMxD,YAAQ,CAACyD,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAYjE,MAAMJ,UAAU,GAAGmE,KAAKG,KAAK,CAAC,KAAK;gBAChE,MAAMnE,SAAS,MAAMI,YAAQ,CAACgE,QAAQ,CAACjE,IAAAA,UAAI,EAACsD,UAAUO;gBACtD,MAAMjE,WAAWsE,OAAOH;gBACxB,MAAMpE,SAASuE,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAM;wBACNtE;wBACAD;wBACAH;oBACF;oBACA2E,iBACElG,KAAKE,GAAG,CAACsB,QAAQ,IAAI,CAACmB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3DuC,KAAKD,GAAG;oBACVW,eAAe3E;oBACf4E,SAASZ,MAAM/D;gBACjB;YACF;QACF,EAAE,OAAO4E,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMC,IACJjB,QAAgB,EAChBW,KAAmC,EACnC,EACEO,UAAU,EAGX,EACD;QACA,IAAIP,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,SAAS;YAC3B,MAAM,IAAIO,MAAM;QAClB;QAEA,IAAI,OAAOD,eAAe,UAAU;YAClC,MAAM,IAAIC,MAAM;QAClB;QACA,MAAM/E,WACJzB,KAAKE,GAAG,CAACqG,YAAY,IAAI,CAAC5D,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/DuC,KAAKD,GAAG;QAEV,IAAI;YACF,MAAMnE,gBACJQ,IAAAA,UAAI,EAAC,IAAI,CAACsD,QAAQ,EAAEE,WACpBW,MAAMzE,SAAS,EACfgF,YACA9E,UACAuE,MAAMtE,MAAM,EACZsE,MAAMrE,IAAI;QAEd,EAAE,OAAO8E,KAAK;YACZhD,KAAIiD,KAAK,CAAC,CAAC,+BAA+B,EAAErB,SAAS,CAAC,EAAEoB;QAC1D;IACF;AACF;AACO,MAAMxI,mBAAmBuI;IAG9BvB,YAAY0B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIjB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACqB,KAAKlB,MAAM,GAAGiB,UAAUE,IAAI,GAAGtB,KAAK,CAAC,KAAK;QAC/CqB,MAAMA,IAAIE,WAAW;QACrB,IAAIpB,OAAO;YACTA,QAAQA,MAAMoB,WAAW;QAC3B;QACAL,IAAIT,GAAG,CAACY,KAAKlB;IACf;IACA,OAAOe;AACT;AAEO,SAASvI,UAAUsI,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIM,MAAMN,IAAI3B,GAAG,CAAC,eAAe2B,IAAI3B,GAAG,CAAC,cAAc;QACvD,IAAIiC,IAAIvD,UAAU,CAAC,QAAQuD,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIjD,SAAS8C,KAAK;QACxB,IAAI,CAAC7C,MAAMgD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AAEO,eAAe9I,cAAc,EAClCgD,MAAM,EACN+F,WAAW,EACX7C,OAAO,EACPN,KAAK,EACLoD,MAAM,EACNC,gBAAgB,EAQjB;IACC,IAAIC,kBAAkBlG;IACtB,IAAIlC,OAAO;QACT,mCAAmC;QACnC,MAAMqI,cAAcrI,MAAMkC,QAAQ;YAChCoG,gBAAgB;QAClB;QAEAD,YAAYE,MAAM;QAElB,IAAIL,QAAQ;YACVG,YAAYG,MAAM,CAAC1D,OAAOoD;QAC5B,OAAO;YACLG,YAAYG,MAAM,CAAC1D,OAAO2D,WAAW;gBACnCC,oBAAoB;YACtB;QACF;QAEA,IAAIT,gBAAgB7I,MAAM;YACxB,IAAIiJ,YAAYM,IAAI,EAAE;gBACpB,MAAMC,cAAcxD,UAAU;gBAC9BiD,YAAYM,IAAI,CAAC;oBACfvD,SAAS5E,KAAKE,GAAG,CAACkI,aAAa;oBAC/BC,mBAAmB;gBACrB;YACF,OAAO;gBACL5E,KAAIC,QAAQ,CACV,CAAC,wIAAwI,CAAC,GACxI;gBAEJmE,YAAYS,IAAI,CAAC;oBAAE1D;gBAAQ;YAC7B;QACF,OAAO,IAAI6C,gBAAgB5I,MAAM;YAC/BgJ,YAAYS,IAAI,CAAC;gBAAE1D;YAAQ;QAC7B,OAAO,IAAI6C,gBAAgB3I,KAAK;YAC9B+I,YAAYU,GAAG,CAAC;gBAAE3D;YAAQ;QAC5B,OAAO,IAAI6C,gBAAgB1I,MAAM;YAC/B8I,YAAYW,IAAI,CAAC;gBAAE5D;gBAAS6D,aAAa;YAAK;QAChD;QAEAb,kBAAkB,MAAMC,YAAYa,QAAQ;IAC5C,iCAAiC;IACnC,OAAO;QACL,IAAIpI,2BAA2BqH,qBAAqB,cAAc;YAChElE,KAAIiD,KAAK,CACP,CAAC,0LAA0L,CAAC;YAE9L,MAAM,IAAIzI,WAAW,KAAK;QAC5B;QACA,wCAAwC;QACxC,IAAIqC,yBAAyB;YAC3BmD,KAAIC,QAAQ,CACV,CAAC,wLAAwL,CAAC,GACxL;YAEJpD,0BAA0B;QAC5B;QAEA,qCAAqC;QACrC,MAAMqI,cAAc,MAAMC,IAAAA,8BAAc,EAAClH;QAEzC,MAAMmH,aAA0B,EAAE;QAElC,IAAIF,gBAAgBG,2BAAW,CAACC,SAAS,EAAE;YACzCF,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACI,YAAY,EAAE;YACnDL,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACK,WAAW,EAAE;YAClDN,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO;QACL,kCAAkC;QAClC,6DAA6D;QAC7D,+BAA+B;QACjC;QAEA,IAAIvB,QAAQ;YACVmB,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAU1E;gBAAOoD;YAAO;QAClD,OAAO;YACLmB,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAU1E;YAAM;QAC1C;QAEA,MAAM,EAAE8E,aAAa,EAAE,GACrB3J,QAAQ;QAEV,IAAIgI,gBAAgB7I,MAAM;YACxBgJ,kBAAkB,MAAMwB,cAAc1H,QAAQmH,YAAY,QAAQjE;QACpE,OAAO,IAAI6C,gBAAgB5I,MAAM;YAC/B+I,kBAAkB,MAAMwB,cAAc1H,QAAQmH,YAAY,QAAQjE;QACpE,OAAO,IAAI6C,gBAAgB3I,KAAK;YAC9B8I,kBAAkB,MAAMwB,cAAc1H,QAAQmH,YAAY,OAAOjE;QACnE,OAAO,IAAI6C,gBAAgB1I,MAAM;YAC/B6I,kBAAkB,MAAMwB,cAAc1H,QAAQmH,YAAY,QAAQjE;QACpE;IACF;IAEA,OAAOgD;AACT;AAEO,eAAexJ,mBAAmBoF,IAAY;IACnD,MAAM6F,MAAM,MAAMC,MAAM9F;IAExB,IAAI,CAAC6F,IAAIE,EAAE,EAAE;QACX9F,KAAIiD,KAAK,CAAC,sCAAsClD,MAAM6F,IAAIG,MAAM;QAChE,MAAM,IAAIvL,WACRoL,IAAIG,MAAM,EACV;IAEJ;IAEA,MAAM9H,SAAS+H,OAAOC,IAAI,CAAC,MAAML,IAAIM,WAAW;IAChD,MAAMlC,cAAc4B,IAAIxE,OAAO,CAACO,GAAG,CAAC;IACpC,MAAMwE,eAAeP,IAAIxE,OAAO,CAACO,GAAG,CAAC;IAErC,OAAO;QAAE1D;QAAQ+F;QAAamC;IAAa;AAC7C;AAEO,eAAevL,mBACpBmF,IAAY,EACZqG,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxC5G,KAAKG;YACL0G,QAAQL,KAAKK,MAAM,IAAI;YACvBrF,SAASgF,KAAKhF,OAAO;YACrBsF,QAAQN,KAAKM,MAAM;QACrB;QAEA,MAAMJ,cAAcC,OAAOvH,GAAG,EAAEuH,OAAOX,GAAG,EAAEe,YAAO,CAACC,KAAK,CAAC7G,MAAM;QAChE,MAAMwG,OAAOX,GAAG,CAACiB,WAAW;QAE5B,IAAI,CAACN,OAAOX,GAAG,CAAC1C,UAAU,EAAE;YAC1BlD,KAAIiD,KAAK,CAAC,6BAA6BlD,MAAMwG,OAAOX,GAAG,CAAC1C,UAAU;YAClE,MAAM,IAAI1I,WACR+L,OAAOX,GAAG,CAAC1C,UAAU,EACrB;QAEJ;QAEA,MAAMjF,SAAS+H,OAAOc,MAAM,CAACP,OAAOX,GAAG,CAACmB,OAAO;QAC/C,MAAM/C,cAAcuC,OAAOX,GAAG,CAACoB,SAAS,CAAC;QACzC,MAAMb,eAAeI,OAAOX,GAAG,CAACoB,SAAS,CAAC;QAC1C,OAAO;YAAE/I;YAAQ+F;YAAamC;QAAa;IAC7C,EAAE,OAAOnD,KAAK;QACZhD,KAAIiD,KAAK,CAAC,sCAAsClD,MAAMiD;QACtD,MAAM,IAAIxI,WACR,KACA;IAEJ;AACF;AAEO,eAAeQ,eACpBiM,aAA4B,EAC5BC,YAGC,EACDhI,UAMC,EACDC,KAA0B;QAOxB8H;IALF,MAAM,EAAElH,IAAI,EAAEoB,OAAO,EAAEN,KAAK,EAAE5D,QAAQ,EAAE,GAAGiK;IAC3C,MAAMC,iBAAiBF,cAAchJ,MAAM;IAC3C,MAAMF,SAAShD,UAAUkM,cAAcd,YAAY;IACnD,MAAMiB,eACJ1M,kBAAkByM,qBAClBF,6BAAAA,cAAcjD,WAAW,qBAAzBiD,2BAA2BtD,WAAW,GAAGD,IAAI;IAE/C,IAAI0D,cAAc;QAChB,IACEA,aAAa/G,UAAU,CAAC,gBACxB,CAACnB,WAAWG,MAAM,CAACgI,mBAAmB,EACtC;YACArH,KAAIiD,KAAK,CACP,CAAC,wBAAwB,EAAElD,KAAK,YAAY,EAAEqH,aAAa,qCAAqC,CAAC;YAEnG,MAAM,IAAI5M,WACR,KACA;QAEJ;QAEA,IAAImB,iBAAiBwB,QAAQ,CAACiK,iBAAiBE,IAAAA,mBAAU,EAACH,iBAAiB;YACzEnH,KAAIC,QAAQ,CACV,CAAC,wBAAwB,EAAEF,KAAK,8GAA8G,CAAC;YAEjJ,OAAO;gBAAE9B,QAAQkJ;gBAAgBnD,aAAaoD;gBAAcrJ;YAAO;QACrE;QACA,IAAInC,aAAauB,QAAQ,CAACiK,eAAe;YACvC,wEAAwE;YACxE,6DAA6D;YAC7D,4EAA4E;YAC5E,OAAO;gBAAEnJ,QAAQkJ;gBAAgBnD,aAAaoD;gBAAcrJ;YAAO;QACrE;QACA,IAAI,CAACqJ,aAAa/G,UAAU,CAAC,aAAa+G,aAAajK,QAAQ,CAAC,MAAM;YACpE6C,KAAIiD,KAAK,CACP,kDACAlD,MACA,YACAqH;YAEF,MAAM,IAAI5M,WAAW,KAAK;QAC5B;IACF;IAEA,IAAIwJ;IAEJ,IAAI/G,UAAU;QACZ+G,cAAc/G;IAChB,OAAO,IACLmK,CAAAA,gCAAAA,aAAc/G,UAAU,CAAC,cACzBkH,IAAAA,yBAAY,EAACH,iBACbA,iBAAiBhM,QACjBgM,iBAAiBjM,MACjB;QACA6I,cAAcoD;IAChB,OAAO;QACLpD,cAAc1I;IAChB;IACA,IAAI;QACF,IAAI6I,kBAAkB,MAAMlJ,cAAc;YACxCgD,QAAQkJ;YACRnD;YACA7C;YACAN;YACAqD,kBAAkBhF,WAAWsI,MAAM;QACrC;QACA,IAAIrD,iBAAiB;YACnB,IAAIhF,SAAS0B,SAAShF,iBAAiBsF,YAAYrF,cAAc;gBAC/D,MAAM,EAAE2L,WAAW,EAAE,GACnBzL,QAAQ;gBACV,8EAA8E;gBAC9E,gFAAgF;gBAChF,qFAAqF;gBACrF,MAAM0L,OAAO,MAAMD,YAAYtD;gBAC/B,MAAMwD,OAAO;oBACXC,WAAWF,KAAK7G,KAAK;oBACrBgH,YAAYH,KAAKzD,MAAM;oBACvB6D,aAAa,CAAC,KAAK,EAAE9D,YAAY,QAAQ,EAAEG,gBAAgB1D,QAAQ,CACjE,UACA,CAAC;gBACL;gBACA0D,kBAAkB6B,OAAOC,IAAI,CAAC8B,SAASC,IAAAA,6BAAe,EAACL;gBACvD3D,cAAc;YAChB;YACA,OAAO;gBACL/F,QAAQkG;gBACRH;gBACAjG,QAAQxB,KAAKE,GAAG,CAACsB,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC5D;QACF,OAAO;YACL,MAAM,IAAIjF,WAAW,KAAK;QAC5B;IACF,EAAE,OAAOyI,OAAO;QACd,IAAIkE,kBAAkBC,cAAc;YAClC,yDAAyD;YACzD,OAAO;gBACLnJ,QAAQkJ;gBACRnD,aAAaoD;gBACbrJ,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC3C;QACF,OAAO;YACL,MAAM,IAAIjF,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAASyN,yBACPrI,GAAW,EACXoE,WAA0B;IAE1B,MAAM,CAACkE,sBAAsB,GAAGtI,IAAIwC,KAAK,CAAC,KAAK;IAC/C,MAAM+F,wBAAwBD,sBAAsB9F,KAAK,CAAC,KAAKgG,GAAG;IAClE,IAAI,CAACpE,eAAe,CAACmE,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsB/F,KAAK,CAAC,KAAK;IACpD,MAAMtE,YAAYyJ,IAAAA,yBAAY,EAACvD;IAC/B,OAAO,CAAC,EAAEqE,SAAS,CAAC,EAAEvK,UAAU,CAAC;AACnC;AAEA,SAASwK,mBACPtJ,GAAoB,EACpB4G,GAAmB,EACnBhG,GAAW,EACX1B,IAAY,EACZ8F,WAA0B,EAC1B3C,QAAiB,EACjBkH,MAAoB,EACpBC,YAAiC,EACjCzK,MAAc,EACdoB,KAAc;IAEdyG,IAAI6C,SAAS,CAAC,QAAQ;IACtB7C,IAAI6C,SAAS,CACX,iBACApH,WACI,yCACA,CAAC,gBAAgB,EAAElC,QAAQ,IAAIpB,OAAO,iBAAiB,CAAC;IAE9D,IAAI2K,IAAAA,6BAAgB,EAAC1J,KAAK4G,KAAK1H,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAEyK,UAAU;QAAK;IAC1B;IACA,IAAI3E,aAAa;QACf4B,IAAI6C,SAAS,CAAC,gBAAgBzE;IAChC;IAEA,MAAMqE,WAAWJ,yBAAyBrI,KAAKoE;IAC/C4B,IAAI6C,SAAS,CACX,uBACAG,IAAAA,2BAAkB,EAACP,UAAU;QAAE9C,MAAMiD,aAAaK,sBAAsB;IAAC;IAG3EjD,IAAI6C,SAAS,CAAC,2BAA2BD,aAAaM,qBAAqB;IAC3ElD,IAAI6C,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEI,UAAU;IAAM;AAC3B;AAEO,SAASzN,aACd8D,GAAoB,EACpB4G,GAAmB,EACnBhG,GAAW,EACX9B,SAAiB,EACjBG,MAAc,EACdoD,QAAiB,EACjBkH,MAAoB,EACpBC,YAAiC,EACjCzK,MAAc,EACdoB,KAAc;IAEd,MAAM6E,cAAc+E,IAAAA,2BAAc,EAACjL;IACnC,MAAMI,OAAOrD,QAAQ;QAACoD;KAAO;IAC7B,MAAM+K,SAASV,mBACbtJ,KACA4G,KACAhG,KACA1B,MACA8F,aACA3C,UACAkH,QACAC,cACAzK,QACAoB;IAEF,IAAI,CAAC6J,OAAOL,QAAQ,EAAE;QACpB/C,IAAI6C,SAAS,CAAC,kBAAkBzC,OAAOiD,UAAU,CAAChL;QAClD2H,IAAIsD,GAAG,CAACjL;IACV;AACF;AAEO,eAAenD,aACpBmD,MAAc,EACd,8BAA8B;AAC9BH,SAA2C;IAK3C,qDAAqD;IACrD,0DAA0D;IAC1D,IAAIA,cAAc,QAAQ;QACxB,IAAI/B,OAAO;YACT,MAAMqI,cAAcrI,MAAMkC;YAC1B,MAAM,EAAE4C,KAAK,EAAEoD,MAAM,EAAE,GAAG,MAAMG,YAAY+E,QAAQ;YACpD,OAAO;gBAAEtI;gBAAOoD;YAAO;QACzB,OAAO;YACL,MAAM,EAAEmF,YAAY,EAAE,GACpBpN,QAAQ;YACV,MAAM,EAAE6E,KAAK,EAAEoD,MAAM,EAAE,GAAG,MAAMmF,aAAanL;YAC7C,OAAO;gBAAE4C;gBAAOoD;YAAO;QACzB;IACF;IAEA,MAAM,EAAEpD,KAAK,EAAEoD,MAAM,EAAE,GAAGoF,IAAAA,kBAAW,EAACpL;IACtC,OAAO;QAAE4C;QAAOoD;IAAO;AACzB"}