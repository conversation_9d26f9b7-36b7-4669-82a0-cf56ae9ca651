{"version": 3, "sources": ["../../server/serve-static.ts"], "names": ["serveStatic", "getContentType", "getExtension", "send", "mime", "define", "req", "res", "path", "Promise", "resolve", "reject", "on", "err", "Error", "code", "pipe", "extWithoutDot", "getType", "lookup", "contentType", "extension"], "mappings": "AAAA;;;;QASgBA,WAAW,GAAXA,WAAW;QAmBXC,cAAc,GAAdA,cAAc;QAUdC,YAAY,GAAZA,YAAY;AArCX,IAAA,KAAyB,kCAAzB,yBAAyB,EAAA;;;;;;AAE1C,gGAAgG;AAChG,0FAA0F;AAC1FC,KAAI,QAAA,CAACC,IAAI,CAACC,MAAM,CAAC;IACf,YAAY,EAAE;QAAC,MAAM;KAAC;CACvB,CAAC;AAEK,SAASL,WAAW,CACzBM,GAAoB,EACpBC,GAAmB,EACnBC,IAAY,EACG;IACf,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,GAAK;QACtCR,CAAAA,GAAAA,KAAI,AAAW,CAAA,QAAX,CAACG,GAAG,EAAEE,IAAI,CAAC,CACZI,EAAE,CAAC,WAAW,EAAE,IAAM;YACrB,yCAAyC;YACzC,MAAMC,GAAG,GAAQ,IAAIC,KAAK,CAAC,qBAAqB,CAAC;YACjDD,GAAG,CAACE,IAAI,GAAG,QAAQ;YACnBJ,MAAM,CAACE,GAAG,CAAC;SACZ,CAAC,CACDD,EAAE,CAAC,OAAO,EAAED,MAAM,CAAC,CACnBK,IAAI,CAACT,GAAG,CAAC,CACTK,EAAE,CAAC,QAAQ,EAAEF,OAAO,CAAC;KACzB,CAAC,CAAA;CACH;AAEM,SAAST,cAAc,CAACgB,aAAqB,EAAiB;IACnE,MAAM,EAAEb,IAAI,CAAA,EAAE,GAAGD,KAAI,QAAA;IACrB,IAAI,SAAS,IAAIC,IAAI,EAAE;QACrB,MAAM;QACN,OAAOA,IAAI,CAACc,OAAO,CAACD,aAAa,CAAC,CAAA;KACnC;IACD,MAAM;IACN,OAAO,AAACb,IAAI,CAASe,MAAM,CAACF,aAAa,CAAC,CAAA;CAC3C;AAEM,SAASf,YAAY,CAACkB,WAAmB,EAAiB;IAC/D,MAAM,EAAEhB,IAAI,CAAA,EAAE,GAAGD,KAAI,QAAA;IACrB,IAAI,cAAc,IAAIC,IAAI,EAAE;QAC1B,MAAM;QACN,OAAOA,IAAI,CAACF,YAAY,CAACkB,WAAW,CAAC,CAAA;KACtC;IACD,MAAM;IACN,OAAO,AAAChB,IAAI,CAASiB,SAAS,CAACD,WAAW,CAAC,CAAA;CAC5C"}