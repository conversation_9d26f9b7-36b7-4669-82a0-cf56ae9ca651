{"version": 3, "sources": ["../../server/accept-header.ts"], "names": ["acceptLanguage", "parse", "raw", "preferences", "options", "lowers", "Map", "header", "replace", "pos", "preference", "lower", "toLowerCase", "set", "orig", "prefixMatch", "parts", "split", "pop", "length", "joined", "join", "has", "selections", "map", "Set", "i", "part", "params", "Error", "type", "token", "selection", "q", "pref", "get", "add", "key", "value", "score", "parseFloat", "Number", "isFinite", "push", "sort", "a", "b", "undefined", "values", "preferred"], "mappings": "AAAA;;;;QAiIgBA,cAAc,GAAdA,cAAc;AArH9B,SAASC,KAAK,CACZC,GAAW,EACXC,WAAiC,EACjCC,OAAgB,EAChB;IACA,MAAMC,MAAM,GAAG,IAAIC,GAAG,EAAyC;IAC/D,MAAMC,MAAM,GAAGL,GAAG,CAACM,OAAO,WAAW,EAAE,CAAC;IAExC,IAAIL,WAAW,EAAE;QACf,IAAIM,GAAG,GAAG,CAAC;QACX,KAAK,MAAMC,UAAU,IAAIP,WAAW,CAAE;YACpC,MAAMQ,KAAK,GAAGD,UAAU,CAACE,WAAW,EAAE;YACtCP,MAAM,CAACQ,GAAG,CAACF,KAAK,EAAE;gBAAEG,IAAI,EAAEJ,UAAU;gBAAED,GAAG,EAAEA,GAAG,EAAE;aAAE,CAAC;YACnD,IAAIL,OAAO,CAACW,WAAW,EAAE;gBACvB,MAAMC,KAAK,GAAGL,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC;gBAC9B,MAAQD,KAAK,CAACE,GAAG,EAAE,EAAEF,KAAK,CAACG,MAAM,GAAG,CAAC,CAAG;oBACtC,MAAMC,MAAM,GAAGJ,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC;oBAC9B,IAAI,CAAChB,MAAM,CAACiB,GAAG,CAACF,MAAM,CAAC,EAAE;wBACvBf,MAAM,CAACQ,GAAG,CAACO,MAAM,EAAE;4BAAEN,IAAI,EAAEJ,UAAU;4BAAED,GAAG,EAAEA,GAAG,EAAE;yBAAE,CAAC;qBACrD;iBACF;aACF;SACF;KACF;IAED,MAAMO,KAAK,GAAGT,MAAM,CAACU,KAAK,CAAC,GAAG,CAAC;IAC/B,MAAMM,UAAU,GAAgB,EAAE;IAClC,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAU;IAE7B,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACG,MAAM,EAAE,EAAEO,CAAC,CAAE;QACrC,MAAMC,IAAI,GAAGX,KAAK,CAACU,CAAC,CAAC;QACrB,IAAI,CAACC,IAAI,EAAE;YACT,SAAQ;SACT;QAED,MAAMC,MAAM,GAAGD,IAAI,CAACV,KAAK,CAAC,GAAG,CAAC;QAC9B,IAAIW,MAAM,CAACT,MAAM,GAAG,CAAC,EAAE;YACrB,MAAM,IAAIU,KAAK,CAAC,CAAC,QAAQ,EAAEzB,OAAO,CAAC0B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;SAClD;QAED,IAAIC,KAAK,GAAGH,MAAM,CAAC,CAAC,CAAC,CAAChB,WAAW,EAAE;QACnC,IAAI,CAACmB,KAAK,EAAE;YACV,MAAM,IAAIF,KAAK,CAAC,CAAC,QAAQ,EAAEzB,OAAO,CAAC0B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;SAClD;QAED,MAAME,SAAS,GAAc;YAAED,KAAK;YAAEtB,GAAG,EAAEiB,CAAC;YAAEO,CAAC,EAAE,CAAC;SAAE;QACpD,IAAI9B,WAAW,IAAIE,MAAM,CAACiB,GAAG,CAACS,KAAK,CAAC,EAAE;YACpCC,SAAS,CAACE,IAAI,GAAG7B,MAAM,CAAC8B,GAAG,CAACJ,KAAK,CAAC,CAAEtB,GAAG;SACxC;QAEDe,GAAG,CAACY,GAAG,CAACJ,SAAS,CAACD,KAAK,CAAC;QAExB,IAAIH,MAAM,CAACT,MAAM,KAAK,CAAC,EAAE;YACvB,MAAMc,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC;YACnB,MAAM,CAACS,GAAG,EAAEC,KAAK,CAAC,GAAGL,CAAC,CAAChB,KAAK,CAAC,GAAG,CAAC;YAEjC,IAAI,CAACqB,KAAK,IAAKD,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,GAAG,AAAC,EAAE;gBAC1C,MAAM,IAAIR,KAAK,CAAC,CAAC,QAAQ,EAAEzB,OAAO,CAAC0B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;aAClD;YAED,MAAMS,KAAK,GAAGC,UAAU,CAACF,KAAK,CAAC;YAC/B,IAAIC,KAAK,KAAK,CAAC,EAAE;gBACf,SAAQ;aACT;YAED,IAAIE,MAAM,CAACC,QAAQ,CAACH,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,KAAK,EAAE;gBAC1DP,SAAS,CAACC,CAAC,GAAGM,KAAK;aACpB;SACF;QAEDhB,UAAU,CAACoB,IAAI,CAACX,SAAS,CAAC;KAC3B;IAEDT,UAAU,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAK;QACxB,IAAIA,CAAC,CAACb,CAAC,KAAKY,CAAC,CAACZ,CAAC,EAAE;YACf,OAAOa,CAAC,CAACb,CAAC,GAAGY,CAAC,CAACZ,CAAC,CAAA;SACjB;QAED,IAAIa,CAAC,CAACZ,IAAI,KAAKW,CAAC,CAACX,IAAI,EAAE;YACrB,IAAIW,CAAC,CAACX,IAAI,KAAKa,SAAS,EAAE;gBACxB,OAAO,CAAC,CAAA;aACT;YAED,IAAID,CAAC,CAACZ,IAAI,KAAKa,SAAS,EAAE;gBACxB,OAAO,CAAC,CAAC,CAAA;aACV;YAED,OAAOF,CAAC,CAACX,IAAI,GAAGY,CAAC,CAACZ,IAAI,CAAA;SACvB;QAED,OAAOW,CAAC,CAACpC,GAAG,GAAGqC,CAAC,CAACrC,GAAG,CAAA;KACrB,CAAC;IAEF,MAAMuC,MAAM,GAAGzB,UAAU,CAACC,GAAG,CAAC,CAACQ,SAAS,GAAKA,SAAS,CAACD,KAAK,CAAC;IAC7D,IAAI,CAAC5B,WAAW,IAAI,CAACA,WAAW,CAACgB,MAAM,EAAE;QACvC,OAAO6B,MAAM,CAAA;KACd;IAED,MAAMC,SAAS,GAAa,EAAE;IAC9B,KAAK,MAAMjB,UAAS,IAAIgB,MAAM,CAAE;QAC9B,IAAIhB,UAAS,KAAK,GAAG,EAAE;YACrB,KAAK,MAAM,CAACtB,UAAU,EAAE4B,KAAK,CAAC,IAAIjC,MAAM,CAAE;gBACxC,IAAI,CAACmB,GAAG,CAACF,GAAG,CAACZ,UAAU,CAAC,EAAE;oBACxBuC,SAAS,CAACN,IAAI,CAACL,KAAK,CAACxB,IAAI,CAAC;iBAC3B;aACF;SACF,MAAM;YACL,MAAMH,KAAK,GAAGqB,UAAS,CAACpB,WAAW,EAAE;YACrC,IAAIP,MAAM,CAACiB,GAAG,CAACX,KAAK,CAAC,EAAE;gBACrBsC,SAAS,CAACN,IAAI,CAACtC,MAAM,CAAC8B,GAAG,CAACxB,KAAK,CAAC,CAAEG,IAAI,CAAC;aACxC;SACF;KACF;IAED,OAAOmC,SAAS,CAAA;CACjB;AAEM,SAASjD,cAAc,CAACO,MAAM,GAAG,EAAE,EAAEJ,WAAsB,EAAE;IAClE,OACEF,KAAK,CAACM,MAAM,EAAEJ,WAAW,EAAE;QACzB2B,IAAI,EAAE,iBAAiB;QACvBf,WAAW,EAAE,IAAI;KAClB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CACZ;CACF"}