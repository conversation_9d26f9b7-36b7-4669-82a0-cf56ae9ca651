{"version": 3, "sources": ["../../server/render-result.ts"], "names": ["RenderResult", "constructor", "response", "contentType", "_result", "_contentType", "toUnchunkedString", "Error", "pipe", "res", "flush", "reader", "<PERSON><PERSON><PERSON><PERSON>", "fatalError", "done", "value", "read", "end", "write", "err", "destroy", "isDynamic", "fromStatic", "empty"], "mappings": "AAAA;;;;;AAIe,MAAMA,YAAY;IAI/BC,YACEC,QAA6C,EAC7C,EAAEC,WAAW,CAAA,EAAuC,GAAG,EAAE,CACzD;QACA,IAAI,CAACC,OAAO,GAAGF,QAAQ;QACvB,IAAI,CAACG,YAAY,GAAGF,WAAW;KAChC;IAEDA,WAAW,GAAsB;QAC/B,OAAO,IAAI,CAACE,YAAY,CAAA;KACzB;IAEDC,iBAAiB,GAAW;QAC1B,IAAI,OAAO,IAAI,CAACF,OAAO,KAAK,QAAQ,EAAE;YACpC,MAAM,IAAIG,KAAK,CACb,4EAA4E,CAC7E,CAAA;SACF;QACD,OAAO,IAAI,CAACH,OAAO,CAAA;KACpB;IAEDI,IAAI,CAACC,GAAmB,EAAiB;QACvC,IAAI,OAAO,IAAI,CAACL,OAAO,KAAK,QAAQ,EAAE;YACpC,MAAM,IAAIG,KAAK,CACb,uEAAuE,CACxE,CAAA;SACF;QACD,MAAML,QAAQ,GAAG,IAAI,CAACE,OAAO;QAC7B,MAAMM,KAAK,GACT,OAAO,AAACD,GAAG,CAASC,KAAK,KAAK,UAAU,GACpC,IAAM,AAACD,GAAG,CAASC,KAAK,EAAE,GAC1B,IAAM,EAAE;QAEd,OAAO,CAAC,UAAY;YAClB,MAAMC,MAAM,GAAGT,QAAQ,CAACU,SAAS,EAAE;YACnC,IAAIC,UAAU,GAAG,KAAK;YAEtB,IAAI;gBACF,MAAO,IAAI,CAAE;oBACX,MAAM,EAAEC,IAAI,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAG,MAAMJ,MAAM,CAACK,IAAI,EAAE;oBAE3C,IAAIF,IAAI,EAAE;wBACRL,GAAG,CAACQ,GAAG,EAAE;wBACT,OAAM;qBACP;oBAEDJ,UAAU,GAAG,IAAI;oBACjBJ,GAAG,CAACS,KAAK,CAACH,KAAK,CAAC;oBAChBL,KAAK,EAAE;iBACR;aACF,CAAC,OAAOS,GAAG,EAAE;gBACZ,IAAIN,UAAU,EAAE;oBACdJ,GAAG,CAACW,OAAO,CAACD,GAAG,CAAQ;iBACxB;gBACD,MAAMA,GAAG,CAAA;aACV;SACF,CAAC,EAAE,CAAA;KACL;IAEDE,SAAS,GAAY;QACnB,OAAO,OAAO,IAAI,CAACjB,OAAO,KAAK,QAAQ,CAAA;KACxC;IAED,OAAOkB,UAAU,CAACP,KAAa,EAAgB;QAC7C,OAAO,IAAIf,YAAY,CAACe,KAAK,CAAC,CAAA;KAC/B;IAED,OAAOQ,KAAK,GAAGvB,YAAY,CAACsB,UAAU,CAAC,EAAE,CAAC,CAAA;CAC3C;kBAxEoBtB,YAAY"}