{"version": 3, "sources": ["../../server/htmlescape.ts"], "names": ["htmlEscapeJsonString", "ESCAPE_LOOKUP", "ESCAPE_REGEX", "str", "replace", "match"], "mappings": "AAGA;;;;QAUgBA,oBAAoB,GAApBA,oBAAoB;;AAbpC,iEAAiE;AACjE,uGAAuG;AAEvG,MAAMC,aAAa,GAAgC;IACjD,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,QAAQ,EAAE,SAAS;IACnB,QAAQ,EAAE,SAAS;CACpB;AAEM,MAAMC,YAAY,uBAAuB;QAAnCA,YAAY,GAAZA,YAAY;AAElB,SAASF,oBAAoB,CAACG,GAAW,EAAU;IACxD,OAAOA,GAAG,CAACC,OAAO,CAACF,YAAY,EAAE,CAACG,KAAK,GAAKJ,aAAa,CAACI,KAAK,CAAC,CAAC,CAAA;CAClE"}