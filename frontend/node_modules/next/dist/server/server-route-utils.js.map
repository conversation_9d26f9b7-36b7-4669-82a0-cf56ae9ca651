{"version": 3, "sources": ["../../server/server-route-utils.ts"], "names": ["getCustomRoute", "params", "rule", "type", "restrictedRedirectPaths", "match", "getPathMatch", "source", "strict", "removeUnnamedP<PERSON>ms", "regexModifier", "internal", "regex", "modifyRouteRegex", "undefined", "name", "fn", "_req", "_res", "_params", "_parsedUrl", "finished", "createHeaderRoute", "headerRoute", "matchesBasePath", "matchesLocale", "matchesLocaleAPIRoutes", "matchesTrailingSlash", "has", "res", "hasParams", "Object", "keys", "length", "header", "headers", "key", "value", "compileNonPath", "<PERSON><PERSON><PERSON><PERSON>", "stringifyQuery", "req", "query", "initialQuery", "getRequestMeta", "initialQueryValues", "values", "stringifyQs", "encodeURIComponent", "some", "initialQueryVal", "Array", "isArray", "includes", "createRedirectRoute", "redirectRoute", "statusCode", "parsedUrl", "parsedDestination", "prepareDestination", "appendParamsToQuery", "destination", "search", "updatedDestination", "formatUrl", "startsWith", "normalizeRepeatedSlashes", "redirect", "getRedirectStatus", "body", "send"], "mappings": "AACA;;;;QAoCgBA,cAAc,GAAdA,cAAc;;AA1BsB,IAAA,eAAwB,WAAxB,wBAAwB,CAAA;AAC/C,IAAA,UAAuC,WAAvC,uCAAuC,CAAA;AAI7D,IAAA,mBAAgD,WAAhD,gDAAgD,CAAA;AACxB,IAAA,YAAgB,WAAhB,gBAAgB,CAAA;AACN,IAAA,YAAa,WAAb,aAAa,CAAA;AAClB,IAAA,IAAK,WAAL,KAAK,CAAA;AACA,IAAA,MAAqB,WAArB,qBAAqB,CAAA;AAiBvD,SAASA,cAAc,CAACC,MAI9B,EAA4D;IAC3D,MAAM,EAAEC,IAAI,CAAA,EAAEC,IAAI,CAAA,EAAEC,uBAAuB,CAAA,EAAE,GAAGH,MAAM;IACtD,MAAMI,KAAK,GAAGC,CAAAA,GAAAA,UAAY,AAUxB,CAAA,aAVwB,CAACJ,IAAI,CAACK,MAAM,EAAE;QACtCC,MAAM,EAAE,IAAI;QACZC,mBAAmB,EAAE,IAAI;QACzBC,aAAa,EAAE,CAAC,AAACR,IAAI,CAASS,QAAQ,GAClC,CAACC,KAAa,GACZC,CAAAA,GAAAA,eAAgB,AAGf,CAAA,iBAHe,CACdD,KAAK,EACLT,IAAI,KAAK,UAAU,GAAGC,uBAAuB,GAAGU,SAAS,CAC1D,GACHA,SAAS;KACd,CAAC;IAEF,OAAO;QACL,GAAGZ,IAAI;QACPC,IAAI;QACJE,KAAK;QACLU,IAAI,EAAEZ,IAAI;QACVa,EAAE,EAAE,OAAOC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,GAAK,CAAC;gBAAEC,QAAQ,EAAE,KAAK;aAAE,CAAC;KACrE,CAAA;CACF;AAEM,MAAMC,iBAAiB,GAAG,CAAC,EAChCpB,IAAI,CAAA,EACJE,uBAAuB,CAAA,EAIxB,GAAY;IACX,MAAMmB,WAAW,GAAGvB,cAAc,CAAC;QACjCG,IAAI,EAAE,QAAQ;QACdD,IAAI;QACJE,uBAAuB;KACxB,CAAC;IACF,OAAO;QACLC,KAAK,EAAEkB,WAAW,CAAClB,KAAK;QACxBmB,eAAe,EAAE,IAAI;QACrBC,aAAa,EAAE,IAAI;QACnBC,sBAAsB,EAAE,IAAI;QAC5BC,oBAAoB,EAAE,IAAI;QAC1BC,GAAG,EAAEL,WAAW,CAACK,GAAG;QACpBzB,IAAI,EAAEoB,WAAW,CAACpB,IAAI;QACtBY,IAAI,EAAE,CAAC,EAAEQ,WAAW,CAACpB,IAAI,CAAC,CAAC,EAAEoB,WAAW,CAAChB,MAAM,CAAC,aAAa,CAAC;QAC9DS,EAAE,EAAE,OAAOC,IAAI,EAAEY,GAAG,EAAE5B,MAAM,EAAEmB,UAAU,GAAK;YAC3C,MAAMU,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC/B,MAAM,CAAC,CAACgC,MAAM,GAAG,CAAC;YAChD,KAAK,MAAMC,MAAM,IAAIX,WAAW,CAACY,OAAO,CAAE;gBACxC,IAAI,EAAEC,GAAG,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAGH,MAAM;gBAC3B,IAAIJ,SAAS,EAAE;oBACbM,GAAG,GAAGE,CAAAA,GAAAA,mBAAc,AAAa,CAAA,eAAb,CAACF,GAAG,EAAEnC,MAAM,CAAC;oBACjCoC,KAAK,GAAGC,CAAAA,GAAAA,mBAAc,AAAe,CAAA,eAAf,CAACD,KAAK,EAAEpC,MAAM,CAAC;iBACtC;gBACD4B,GAAG,CAACU,SAAS,CAACH,GAAG,EAAEC,KAAK,CAAC;aAC1B;YACD,OAAO;gBAAEhB,QAAQ,EAAE,KAAK;aAAE,CAAA;SAC3B;KACF,CAAA;CACF;QAlCYC,iBAAiB,GAAjBA,iBAAiB;AAuCvB,MAAMkB,cAAc,GAAG,CAACC,GAAoB,EAAEC,KAAqB,GAAK;IAC7E,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,YAAc,AAA0B,CAAA,eAA1B,CAACH,GAAG,EAAE,mBAAmB,CAAC,IAAI,EAAE;IACnE,MAAMI,kBAAkB,GACtBd,MAAM,CAACe,MAAM,CAACH,YAAY,CAAC;IAE7B,OAAOI,CAAAA,GAAAA,YAAW,AAiBhB,CAAA,UAjBgB,CAACL,KAAK,EAAE5B,SAAS,EAAEA,SAAS,EAAE;QAC9CkC,kBAAkB,EAACX,KAAK,EAAE;YACxB,IACEA,KAAK,IAAIM,YAAY,IACrBE,kBAAkB,CAACI,IAAI,CAAC,CAACC,eAAkC,GAAK;gBAC9D,0EAA0E;gBAC1E,OAAOC,KAAK,CAACC,OAAO,CAACF,eAAe,CAAC,GACjCA,eAAe,CAACG,QAAQ,CAAChB,KAAK,CAAC,GAC/Ba,eAAe,KAAKb,KAAK,CAAA;aAC9B,CAAC,EACF;gBACA,4CAA4C;gBAC5C,OAAOW,kBAAkB,CAACX,KAAK,CAAC,CAAA;aACjC;YAED,OAAOA,KAAK,CAAA;SACb;KACF,CAAC,CAAA;CACH;QAvBYG,cAAc,GAAdA,cAAc;AAyBpB,MAAMc,mBAAmB,GAAG,CAAC,EAClCpD,IAAI,CAAA,EACJE,uBAAuB,CAAA,EAIxB,GAAY;IACX,MAAMmD,aAAa,GAAGvD,cAAc,CAAC;QACnCG,IAAI,EAAE,UAAU;QAChBD,IAAI;QACJE,uBAAuB;KACxB,CAAC;IACF,OAAO;QACLO,QAAQ,EAAE4C,aAAa,CAAC5C,QAAQ;QAChCR,IAAI,EAAEoD,aAAa,CAACpD,IAAI;QACxBE,KAAK,EAAEkD,aAAa,CAAClD,KAAK;QAC1BmB,eAAe,EAAE,IAAI;QACrBC,aAAa,EAAE8B,aAAa,CAAC5C,QAAQ,GAAGG,SAAS,GAAG,IAAI;QACxDY,sBAAsB,EAAE,IAAI;QAC5BC,oBAAoB,EAAE,IAAI;QAC1BC,GAAG,EAAE2B,aAAa,CAAC3B,GAAG;QACtB4B,UAAU,EAAED,aAAa,CAACC,UAAU;QACpCzC,IAAI,EAAE,CAAC,eAAe,EAAEwC,aAAa,CAAChD,MAAM,CAAC,CAAC;QAC9CS,EAAE,EAAE,OAAOyB,GAAG,EAAEZ,GAAG,EAAE5B,MAAM,EAAEwD,SAAS,GAAK;YACzC,MAAM,EAAEC,iBAAiB,CAAA,EAAE,GAAGC,CAAAA,GAAAA,mBAAkB,AAK9C,CAAA,mBAL8C,CAAC;gBAC/CC,mBAAmB,EAAE,KAAK;gBAC1BC,WAAW,EAAEN,aAAa,CAACM,WAAW;gBACtC5D,MAAM,EAAEA,MAAM;gBACdyC,KAAK,EAAEe,SAAS,CAACf,KAAK;aACvB,CAAC;YAEF,MAAM,EAAEA,KAAK,CAAA,EAAE,GAAGgB,iBAAiB;YACnC,OAAO,AAACA,iBAAiB,CAAShB,KAAK;YAEvCgB,iBAAiB,CAACI,MAAM,GAAGtB,cAAc,CAACC,GAAG,EAAEC,KAAK,CAAC;YAErD,IAAIqB,kBAAkB,GAAGC,CAAAA,GAAAA,IAAS,AAAmB,CAAA,OAAnB,CAACN,iBAAiB,CAAC;YAErD,IAAIK,kBAAkB,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;gBACtCF,kBAAkB,GAAGG,CAAAA,GAAAA,MAAwB,AAAoB,CAAA,yBAApB,CAACH,kBAAkB,CAAC;aAClE;YAEDlC,GAAG,CACAsC,QAAQ,CAACJ,kBAAkB,EAAEK,CAAAA,GAAAA,eAAiB,AAAe,CAAA,kBAAf,CAACb,aAAa,CAAC,CAAC,CAC9Dc,IAAI,CAACN,kBAAkB,CAAC,CACxBO,IAAI,EAAE;YAET,OAAO;gBACLjD,QAAQ,EAAE,IAAI;aACf,CAAA;SACF;KACF,CAAA;CACF;QApDYiC,mBAAmB,GAAnBA,mBAAmB"}