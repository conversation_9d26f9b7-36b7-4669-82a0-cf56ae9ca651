{"version": 3, "sources": ["../../server/request-meta.ts"], "names": ["getRequestMeta", "setRequestMeta", "addRequestMeta", "getNextInternalQuery", "NEXT_REQUEST_META", "Symbol", "req", "key", "meta", "request", "value", "query", "keysToInclude", "nextInternalQuery"], "mappings": "AACA;;;;QAmCgBA,cAAc,GAAdA,cAAc;QAQdC,cAAc,GAAdA,cAAc;QAKdC,cAAc,GAAdA,cAAc;QA8BdC,oBAAoB,GAApBA,oBAAoB;;AAxE7B,MAAMC,iBAAiB,GAAGC,MAAM,CAAC,iBAAiB,CAAC;QAA7CD,iBAAiB,GAAjBA,iBAAiB;AA6BvB,SAASJ,cAAc,CAC5BM,GAAwB,EACxBC,GAAO,EACuB;IAC9B,MAAMC,IAAI,GAAGF,GAAG,CAACF,iBAAiB,CAAC,IAAI,EAAE;IACzC,OAAO,OAAOG,GAAG,KAAK,QAAQ,GAAGC,IAAI,CAACD,GAAG,CAAC,GAAGC,IAAI,CAAA;CAClD;AAEM,SAASP,cAAc,CAACK,GAAwB,EAAEE,IAAiB,EAAE;IAC1EF,GAAG,CAACF,iBAAiB,CAAC,GAAGI,IAAI;IAC7B,OAAOR,cAAc,CAACM,GAAG,CAAC,CAAA;CAC3B;AAEM,SAASJ,cAAc,CAC5BO,OAA4B,EAC5BF,GAAM,EACNG,KAAqB,EACrB;IACA,MAAMF,IAAI,GAAGR,cAAc,CAACS,OAAO,CAAC;IACpCD,IAAI,CAACD,GAAG,CAAC,GAAGG,KAAK;IACjB,OAAOT,cAAc,CAACQ,OAAO,EAAED,IAAI,CAAC,CAAA;CACrC;AAsBM,SAASL,oBAAoB,CAClCQ,KAAyB,EACN;IACnB,MAAMC,aAAa,GAAgC;QACjD,qBAAqB;QACrB,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,uBAAuB;QACvB,eAAe;KAChB;IACD,MAAMC,iBAAiB,GAAsB,EAAE;IAE/C,KAAK,MAAMN,GAAG,IAAIK,aAAa,CAAE;QAC/B,IAAIL,GAAG,IAAII,KAAK,EAAE;YAChB,2CAA2C;YAC3CE,iBAAiB,CAACN,GAAG,CAAC,GAAGI,KAAK,CAACJ,GAAG,CAAC;SACpC;KACF;IAED,OAAOM,iBAAiB,CAAA;CACzB"}