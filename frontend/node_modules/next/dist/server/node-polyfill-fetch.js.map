{"version": 3, "sources": ["../../server/node-polyfill-fetch.js"], "names": ["global", "fetch", "agent", "protocol", "__NEXT_HTTP_AGENT", "__NEXT_HTTPS_AGENT", "fetchWithAgent", "url", "opts", "rest", "Headers", "Request", "Response"], "mappings": "AAAA;AAIO,IAAA,UAA+B,mCAA/B,+BAA+B,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtC,8CAA8C;AAC9C,IAAI,CAACA,MAAM,CAACC,KAAK,EAAE;IACjB,MAAMC,KAAK,GAAG,CAAC,EAAEC,QAAQ,CAAA,EAAE,GACzBA,QAAQ,KAAK,OAAO,GAAGH,MAAM,CAACI,iBAAiB,GAAGJ,MAAM,CAACK,kBAAkB;IAC7E,MAAMC,cAAc,GAAG,CAACC,GAAG,EAAEC,IAAI,EAAKC,GAAAA,IAAI,GAAK;QAC7C,IAAI,CAACD,IAAI,EAAE;YACTA,IAAI,GAAG;gBAAEN,KAAK;aAAE;SACjB,MAAM,IAAI,CAACM,IAAI,CAACN,KAAK,EAAE;YACtBM,IAAI,CAACN,KAAK,GAAGA,KAAK;SACnB;QACD,OAAOD,CAAAA,GAAAA,UAAK,AAAoB,CAAA,QAApB,CAACM,GAAG,EAAEC,IAAI,KAAKC,IAAI,CAAC,CAAA;KACjC;IACDT,MAAM,CAACC,KAAK,GAAGK,cAAc;IAC7BN,MAAM,CAACU,OAAO,GAAGA,UAAO,QAAA;IACxBV,MAAM,CAACW,OAAO,GAAGA,UAAO,QAAA;IACxBX,MAAM,CAACY,QAAQ,GAAGA,UAAQ,SAAA;CAC3B"}