{"version": 3, "sources": ["../../../server/api-utils/index.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sendStatusCode", "redirect", "checkIsManualRevalidate", "clearPreviewData", "sendError", "setLazyProp", "headers", "parse<PERSON><PERSON><PERSON>", "header", "cookie", "parse", "parseCookieFn", "require", "Array", "isArray", "join", "res", "statusCode", "statusOrUrl", "url", "Error", "writeHead", "Location", "write", "end", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "req", "previewProps", "isManualRevalidate", "previewModeId", "revalidateOnlyGenerated", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "SYMBOL_PREVIEW_DATA", "Symbol", "SYMBOL_CLEARED_COOKIES", "options", "serialize", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "expires", "Date", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "undefined", "Object", "defineProperty", "value", "enumerable", "ApiError", "constructor", "message", "statusMessage", "prop", "getter", "opts", "configurable", "optsReset", "writable", "get", "set"], "mappings": "AAAA;;;;QAmBgBA,eAAe,GAAfA,eAAe;QAoBfC,cAAc,GAAdA,cAAc;QAcdC,QAAQ,GAARA,QAAQ;QAwBRC,uBAAuB,GAAvBA,uBAAuB;QAuBvBC,gBAAgB,GAAhBA,gBAAgB;QAwEhBC,SAAS,GAATA,SAAS;QAoBTC,WAAW,GAAXA,WAAW;;AA7KpB,SAASN,eAAe,CAACO,OAE/B,EAA+B;IAC9B,OAAO,SAASC,WAAW,GAA0B;QACnD,MAAMC,MAAM,GAAkCF,OAAO,CAACG,MAAM;QAE5D,IAAI,CAACD,MAAM,EAAE;YACX,OAAO,EAAE,CAAA;SACV;QAED,MAAM,EAAEE,KAAK,EAAEC,aAAa,CAAA,EAAE,GAAGC,OAAO,CAAC,2BAA2B,CAAC;QACrE,OAAOD,aAAa,CAACE,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,GAAGA,MAAM,CAACO,IAAI,CAAC,GAAG,CAAC,GAAGP,MAAM,CAAC,CAAA;KACxE,CAAA;CACF;AAOM,SAASR,cAAc,CAC5BgB,GAAoB,EACpBC,UAAkB,EACI;IACtBD,GAAG,CAACC,UAAU,GAAGA,UAAU;IAC3B,OAAOD,GAAG,CAAA;CACX;AAQM,SAASf,QAAQ,CACtBe,GAAoB,EACpBE,WAA4B,EAC5BC,GAAY,EACU;IACtB,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;QACnCC,GAAG,GAAGD,WAAW;QACjBA,WAAW,GAAG,GAAG;KAClB;IACD,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;QAC9D,MAAM,IAAIC,KAAK,CACb,CAAC,qKAAqK,CAAC,CACxK,CAAA;KACF;IACDJ,GAAG,CAACK,SAAS,CAACH,WAAW,EAAE;QAAEI,QAAQ,EAAEH,GAAG;KAAE,CAAC;IAC7CH,GAAG,CAACO,KAAK,CAACJ,GAAG,CAAC;IACdH,GAAG,CAACQ,GAAG,EAAE;IACT,OAAOR,GAAG,CAAA;CACX;AAEM,MAAMS,2BAA2B,GAAG,wBAAwB;QAAtDA,2BAA2B,GAA3BA,2BAA2B;AACjC,MAAMC,0CAA0C,GACrD,qCAAqC;QAD1BA,0CAA0C,GAA1CA,0CAA0C;AAGhD,SAASxB,uBAAuB,CACrCyB,GAAsC,EACtCC,YAA+B,EAI/B;IACA,OAAO;QACLC,kBAAkB,EAChBF,GAAG,CAACrB,OAAO,CAACmB,2BAA2B,CAAC,KAAKG,YAAY,CAACE,aAAa;QACzEC,uBAAuB,EACrB,CAAC,CAACJ,GAAG,CAACrB,OAAO,CAACoB,0CAA0C,CAAC;KAC5D,CAAA;CACF;AAEM,MAAMM,4BAA4B,GAAG,CAAC,kBAAkB,CAAC;QAAnDA,4BAA4B,GAA5BA,4BAA4B;AAClC,MAAMC,0BAA0B,GAAG,CAAC,mBAAmB,CAAC;QAAlDA,0BAA0B,GAA1BA,0BAA0B;AAEhC,MAAMC,sBAAsB,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;QAAxCA,sBAAsB,GAAtBA,sBAAsB;AAE5B,MAAMC,mBAAmB,GAAGC,MAAM,CAACH,0BAA0B,CAAC;QAAxDE,mBAAmB,GAAnBA,mBAAmB;AACzB,MAAME,sBAAsB,GAAGD,MAAM,CAACJ,4BAA4B,CAAC;QAA7DK,sBAAsB,GAAtBA,sBAAsB;AAE5B,SAASlC,gBAAgB,CAC9Ba,GAAuB,EACvBsB,OAEC,GAAG,EAAE,EACc;IACpB,IAAID,sBAAsB,IAAIrB,GAAG,EAAE;QACjC,OAAOA,GAAG,CAAA;KACX;IAED,MAAM,EAAEuB,SAAS,CAAA,EAAE,GACjB3B,OAAO,CAAC,2BAA2B,CAAC,AAA2B;IACjE,MAAM4B,QAAQ,GAAGxB,GAAG,CAACyB,SAAS,CAAC,YAAY,CAAC;IAC5CzB,GAAG,CAAC0B,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAOF,QAAQ,KAAK,QAAQ,GAC5B;YAACA,QAAQ;SAAC,GACV3B,KAAK,CAACC,OAAO,CAAC0B,QAAQ,CAAC,GACvBA,QAAQ,GACR,EAAE;QACND,SAAS,CAACP,4BAA4B,EAAE,EAAE,EAAE;YAC1C,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEW,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC;YACpBC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAAG,MAAM,GAAG,KAAK;YACjEC,MAAM,EAAEH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;YAC9CE,IAAI,EAAE,GAAG;YACT,GAAIb,OAAO,CAACa,IAAI,KAAKC,SAAS,GACzB;gBAAED,IAAI,EAAEb,OAAO,CAACa,IAAI;aAAE,GACvBC,SAAS;SACd,CAAC;QACFb,SAAS,CAACN,0BAA0B,EAAE,EAAE,EAAE;YACxC,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEU,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC;YACpBC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAAG,MAAM,GAAG,KAAK;YACjEC,MAAM,EAAEH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;YAC9CE,IAAI,EAAE,GAAG;YACT,GAAIb,OAAO,CAACa,IAAI,KAAKC,SAAS,GACzB;gBAAED,IAAI,EAAEb,OAAO,CAACa,IAAI;aAAE,GACvBC,SAAS;SACd,CAAC;KACH,CAAC;IAEFC,MAAM,CAACC,cAAc,CAACtC,GAAG,EAAEqB,sBAAsB,EAAE;QACjDkB,KAAK,EAAE,IAAI;QACXC,UAAU,EAAE,KAAK;KAClB,CAAC;IACF,OAAOxC,GAAG,CAAA;CACX;AAKM,MAAMyC,QAAQ,SAASrC,KAAK;IAGjCsC,YAAYzC,UAAkB,EAAE0C,OAAe,CAAE;QAC/C,KAAK,CAACA,OAAO,CAAC;QACd,IAAI,CAAC1C,UAAU,GAAGA,UAAU;KAC7B;CACF;QAPYwC,QAAQ,GAARA,QAAQ;AAed,SAASrD,SAAS,CACvBY,GAAoB,EACpBC,UAAkB,EAClB0C,OAAe,EACT;IACN3C,GAAG,CAACC,UAAU,GAAGA,UAAU;IAC3BD,GAAG,CAAC4C,aAAa,GAAGD,OAAO;IAC3B3C,GAAG,CAACQ,GAAG,CAACmC,OAAO,CAAC;CACjB;AAYM,SAAStD,WAAW,CACzB,EAAEsB,GAAG,CAAA,EAAa,EAClBkC,IAAY,EACZC,MAAe,EACT;IACN,MAAMC,IAAI,GAAG;QAAEC,YAAY,EAAE,IAAI;QAAER,UAAU,EAAE,IAAI;KAAE;IACrD,MAAMS,SAAS,GAAG;QAAE,GAAGF,IAAI;QAAEG,QAAQ,EAAE,IAAI;KAAE;IAE7Cb,MAAM,CAACC,cAAc,CAAC3B,GAAG,EAAEkC,IAAI,EAAE;QAC/B,GAAGE,IAAI;QACPI,GAAG,EAAE,IAAM;YACT,MAAMZ,KAAK,GAAGO,MAAM,EAAE;YACtB,8DAA8D;YAC9DT,MAAM,CAACC,cAAc,CAAC3B,GAAG,EAAEkC,IAAI,EAAE;gBAAE,GAAGI,SAAS;gBAAEV,KAAK;aAAE,CAAC;YACzD,OAAOA,KAAK,CAAA;SACb;QACDa,GAAG,EAAE,CAACb,KAAK,GAAK;YACdF,MAAM,CAACC,cAAc,CAAC3B,GAAG,EAAEkC,IAAI,EAAE;gBAAE,GAAGI,SAAS;gBAAEV,KAAK;aAAE,CAAC;SAC1D;KACF,CAAC;CACH"}