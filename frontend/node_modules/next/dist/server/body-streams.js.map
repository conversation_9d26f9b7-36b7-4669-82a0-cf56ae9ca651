{"version": 3, "sources": ["../../server/body-streams.ts"], "names": ["requestToBodyStream", "bodyStreamToNodeStream", "getClonableBody", "context", "stream", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "close", "err", "error", "bodyStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "Readable", "from", "done", "value", "read", "replaceRequestBody", "base", "key", "v", "bind", "readable", "buffered", "endPromise", "Promise", "resolve", "reject", "catch", "finalize", "res", "cloneBodyStream", "input", "p1", "PassThrough", "p2", "push"], "mappings": "AAAA;;;;QAGgBA,mBAAmB,GAAnBA,mBAAmB;QAanBC,sBAAsB,GAAtBA,sBAAsB;QAqCtBC,eAAe,GAAfA,eAAe;AApDO,IAAA,OAAQ,WAAR,QAAQ,CAAA;AAEvC,SAASF,mBAAmB,CACjCG,OAAkD,EAClDC,MAAgB,EAChB;IACA,OAAO,IAAID,OAAO,CAACE,cAAc,CAAC;QAChCC,KAAK,EAACC,UAAU,EAAE;YAChBH,MAAM,CAACI,EAAE,CAAC,MAAM,EAAE,CAACC,KAAK,GAAKF,UAAU,CAACG,OAAO,CAACD,KAAK,CAAC,CAAC;YACvDL,MAAM,CAACI,EAAE,CAAC,KAAK,EAAE,IAAMD,UAAU,CAACI,KAAK,EAAE,CAAC;YAC1CP,MAAM,CAACI,EAAE,CAAC,OAAO,EAAE,CAACI,GAAG,GAAKL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC,CAAC;SACnD;KACF,CAAC,CAAA;CACH;AAEM,SAASX,sBAAsB,CACpCa,UAAsC,EAC5B;IACV,MAAMC,MAAM,GAAGD,UAAU,CAACE,SAAS,EAAE;IACrC,OAAOC,OAAQ,SAAA,CAACC,IAAI,CAClB,AAAC,kBAAmB;QAClB,MAAO,IAAI,CAAE;YACX,MAAM,EAAEC,IAAI,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAG,MAAML,MAAM,CAACM,IAAI,EAAE;YAC3C,IAAIF,IAAI,EAAE;gBACR,OAAM;aACP;YACD,MAAMC,KAAK;SACZ;KACF,EAAG,CACL,CAAA;CACF;AAED,SAASE,kBAAkB,CACzBC,IAAO,EACPnB,MAAgB,EACb;IACH,IAAK,MAAMoB,GAAG,IAAIpB,MAAM,CAAE;QACxB,IAAIqB,CAAC,GAAGrB,MAAM,CAACoB,GAAG,CAAmB,AAAO;QAC5C,IAAI,OAAOC,CAAC,KAAK,UAAU,EAAE;YAC3BA,CAAC,GAAGA,CAAC,CAACC,IAAI,CAACH,IAAI,CAAC;SACjB;QACDA,IAAI,CAACC,GAAG,CAAY,GAAGC,CAAC;KACzB;IAED,OAAOF,IAAI,CAAA;CACZ;AAOM,SAASrB,eAAe,CAC7ByB,QAAW,EACG;IACd,IAAIC,QAAQ,GAAoB,IAAI;IAEpC,MAAMC,UAAU,GAAG,IAAIC,OAAO,CAC5B,CAACC,OAAO,EAAEC,MAAM,GAAK;QACnBL,QAAQ,CAACnB,EAAE,CAAC,KAAK,EAAEuB,OAAO,CAAC;QAC3BJ,QAAQ,CAACnB,EAAE,CAAC,OAAO,EAAEwB,MAAM,CAAC;KAC7B,CACF,CAACC,KAAK,CAAC,CAACpB,KAAK,GAAK;QACjB,OAAO;YAAEA,KAAK;SAAE,CAAA;KACjB,CAAC;IAEF,OAAO;QACL;;;;OAIG,CACH,MAAMqB,QAAQ,IAAkB;YAC9B,IAAIN,QAAQ,EAAE;gBACZ,MAAMO,GAAG,GAAG,MAAMN,UAAU;gBAE5B,IAAIM,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACtB,KAAK,EAAE;oBAC/C,MAAMsB,GAAG,CAACtB,KAAK,CAAA;iBAChB;gBACDS,kBAAkB,CAACK,QAAQ,EAAEC,QAAQ,CAAC;gBACtCA,QAAQ,GAAGD,QAAQ;aACpB;SACF;QACD;;;OAGG,CACHS,eAAe,IAAG;YAChB,MAAMC,KAAK,GAAGT,QAAQ,WAARA,QAAQ,GAAID,QAAQ;YAClC,MAAMW,EAAE,GAAG,IAAIC,OAAW,YAAA,EAAE;YAC5B,MAAMC,EAAE,GAAG,IAAID,OAAW,YAAA,EAAE;YAC5BF,KAAK,CAAC7B,EAAE,CAAC,MAAM,EAAE,CAACC,KAAK,GAAK;gBAC1B6B,EAAE,CAACG,IAAI,CAAChC,KAAK,CAAC;gBACd+B,EAAE,CAACC,IAAI,CAAChC,KAAK,CAAC;aACf,CAAC;YACF4B,KAAK,CAAC7B,EAAE,CAAC,KAAK,EAAE,IAAM;gBACpB8B,EAAE,CAACG,IAAI,CAAC,IAAI,CAAC;gBACbD,EAAE,CAACC,IAAI,CAAC,IAAI,CAAC;aACd,CAAC;YACFb,QAAQ,GAAGY,EAAE;YACb,OAAOF,EAAE,CAAA;SACV;KACF,CAAA;CACF"}