{"version": 3, "sources": ["../../server/utils.ts"], "names": ["isBlockedPage", "cleanAmpPath", "isTargetLikeServerless", "pathname", "BLOCKED_PAGES", "includes", "match", "replace", "target", "isServerless", "isServerlessTrace", "shouldUseReactRoot", "parseInt", "React", "version"], "mappings": "AAAA;;;;QAGgBA,aAAa,GAAbA,aAAa;QAIbC,YAAY,GAAZA,YAAY;QAWZC,sBAAsB,GAAtBA,sBAAsB;;AAlBpB,IAAA,MAAO,kCAAP,OAAO,EAAA;AACK,IAAA,UAAyB,WAAzB,yBAAyB,CAAA;;;;;;AAEhD,SAASF,aAAa,CAACG,QAAgB,EAAW;IACvD,OAAOC,UAAa,cAAA,CAACC,QAAQ,CAACF,QAAQ,CAAC,CAAA;CACxC;AAEM,SAASF,YAAY,CAACE,QAAgB,EAAU;IACrD,IAAIA,QAAQ,CAACG,KAAK,wBAAwB,EAAE;QAC1CH,QAAQ,GAAGA,QAAQ,CAACI,OAAO,2BAA2B,GAAG,CAAC;KAC3D;IACD,IAAIJ,QAAQ,CAACG,KAAK,uBAAuB,EAAE;QACzCH,QAAQ,GAAGA,QAAQ,CAACI,OAAO,wBAAwB,EAAE,CAAC;KACvD;IACDJ,QAAQ,GAAGA,QAAQ,CAACI,OAAO,QAAQ,EAAE,CAAC;IACtC,OAAOJ,QAAQ,CAAA;CAChB;AAEM,SAASD,sBAAsB,CAACM,MAAc,EAAE;IACrD,MAAMC,YAAY,GAAGD,MAAM,KAAK,YAAY;IAC5C,MAAME,iBAAiB,GAAGF,MAAM,KAAK,+BAA+B;IACpE,OAAOC,YAAY,IAAIC,iBAAiB,CAAA;CACzC;AAGM,MAAMC,kBAAkB,GAAGC,QAAQ,CAACC,MAAK,QAAA,CAACC,OAAO,CAAC,IAAI,EAAE;QAAlDH,kBAAkB,GAAlBA,kBAAkB"}