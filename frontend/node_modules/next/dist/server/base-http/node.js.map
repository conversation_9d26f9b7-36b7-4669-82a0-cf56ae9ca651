{"version": 3, "sources": ["../../../server/base-http/node.ts"], "names": ["NEXT_REQUEST_META", "NodeNextRequest", "BaseNextRequest", "originalRequest", "_req", "url", "cookies", "value", "constructor", "method", "toUpperCase", "headers", "parseBody", "limit", "NodeNextResponse", "BaseNextResponse", "originalResponse", "SYMBOL_CLEARED_COOKIES", "_res", "textBody", "undefined", "sent", "finished", "headersSent", "statusCode", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "name", "getHeader<PERSON><PERSON>ues", "values", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "map", "toString", "<PERSON><PERSON><PERSON><PERSON>", "join", "append<PERSON><PERSON>er", "currentV<PERSON>ues", "includes", "body", "send", "end"], "mappings": "AAAA;;;;AAG8D,IAAA,SAAc,WAAd,cAAc,CAAA;AAClD,IAAA,KAAmB,WAAnB,mBAAmB,CAAA;AACE,IAAA,YAAiB,WAAjB,iBAAiB,CAAA;AAEd,IAAA,MAAS,WAAT,SAAS,CAAA;IAUxDA,kBAAiB,GAAjBA,YAAiB,kBAAA;AAHb,MAAMC,eAAe,SAASC,MAAe,gBAAA;IAKlD,IAAIC,eAAe,GAAG;QACpB,qFAAqF;QACrF,+BAA+B;QAC/B,IAAI,CAACC,IAAI,CAACJ,YAAiB,kBAAA,CAAC,GAAG,IAAI,CAACA,YAAiB,kBAAA,CAAC;QACtD,IAAI,CAACI,IAAI,CAACC,GAAG,GAAG,IAAI,CAACA,GAAG;QACxB,IAAI,CAACD,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO;QAChC,OAAO,IAAI,CAACF,IAAI,CAAA;KACjB;IAED,IAAID,eAAe,CAACI,KAAU,EAAE;QAC9B,IAAI,CAACH,IAAI,GAAGG,KAAK;KAClB;IAEDC,YAAoBJ,IAAS,CAAE;QAC7B,KAAK,CAACA,IAAI,CAACK,MAAM,CAAEC,WAAW,EAAE,EAAEN,IAAI,CAACC,GAAG,EAAGD,IAAI,CAAC;aADhCA,IAAS,GAATA,IAAS;aAjBtBO,OAAO,GAAG,IAAI,CAACP,IAAI,CAACO,OAAO;YAElC,CAACX,kBAAiB,CAAC,GAAgB,EAAE;KAiBpC;IAED,MAAMY,SAAS,CAACC,KAAsB,EAAgB;QACpD,OAAOD,CAAAA,GAAAA,KAAS,AAAkB,CAAA,UAAlB,CAAC,IAAI,CAACR,IAAI,EAAES,KAAK,CAAC,CAAA;KACnC;CACF;QAzBYZ,eAAe,GAAfA,eAAe;AA2BrB,MAAMa,gBAAgB,SAASC,MAAgB,iBAAA;IAKpD,IAAIC,gBAAgB,GAAG;QACrB,IAAIC,SAAsB,uBAAA,IAAI,IAAI,EAAE;YAClC,IAAI,CAACC,IAAI,CAACD,SAAsB,uBAAA,CAAC,GAAG,IAAI,CAACA,SAAsB,uBAAA,CAAC;SACjE;QAED,OAAO,IAAI,CAACC,IAAI,CAAA;KACjB;IAEDV,YACUU,IAA6D,CACrE;QACA,KAAK,CAACA,IAAI,CAAC;aAFHA,IAA6D,GAA7DA,IAA6D;aAb/DC,QAAQ,GAAuBC,SAAS;KAgB/C;IAED,IAAIC,IAAI,GAAG;QACT,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,IAAI,CAACJ,IAAI,CAACK,WAAW,CAAA;KACnD;IAED,IAAIC,UAAU,GAAG;QACf,OAAO,IAAI,CAACN,IAAI,CAACM,UAAU,CAAA;KAC5B;IAED,IAAIA,UAAU,CAACjB,KAAa,EAAE;QAC5B,IAAI,CAACW,IAAI,CAACM,UAAU,GAAGjB,KAAK;KAC7B;IAED,IAAIkB,aAAa,GAAG;QAClB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa,CAAA;KAC/B;IAED,IAAIA,aAAa,CAAClB,KAAa,EAAE;QAC/B,IAAI,CAACW,IAAI,CAACO,aAAa,GAAGlB,KAAK;KAChC;IAEDmB,SAAS,CAACC,IAAY,EAAEpB,KAAwB,EAAQ;QACtD,IAAI,CAACW,IAAI,CAACQ,SAAS,CAACC,IAAI,EAAEpB,KAAK,CAAC;QAChC,OAAO,IAAI,CAAA;KACZ;IAEDqB,eAAe,CAACD,IAAY,EAAwB;QAClD,MAAME,MAAM,GAAG,IAAI,CAACX,IAAI,CAACY,SAAS,CAACH,IAAI,CAAC;QAExC,IAAIE,MAAM,KAAKT,SAAS,EAAE,OAAOA,SAAS,CAAA;QAE1C,OAAO,CAACW,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,GAAG;YAACA,MAAM;SAAC,CAAC,CAACI,GAAG,CAAC,CAAC1B,KAAK,GAC3DA,KAAK,CAAC2B,QAAQ,EAAE,CACjB,CAAA;KACF;IAEDC,SAAS,CAACR,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACT,IAAI,CAACiB,SAAS,CAACR,IAAI,CAAC,CAAA;KACjC;IAEDG,SAAS,CAACH,IAAY,EAAsB;QAC1C,MAAME,MAAM,GAAG,IAAI,CAACD,eAAe,CAACD,IAAI,CAAC;QACzC,OAAOI,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACO,IAAI,CAAC,GAAG,CAAC,GAAGhB,SAAS,CAAA;KAC5D;IAEDiB,YAAY,CAACV,IAAY,EAAEpB,KAAa,EAAQ;YACxB,GAA0B;QAAhD,MAAM+B,aAAa,GAAG,CAAA,GAA0B,GAA1B,IAAI,CAACV,eAAe,CAACD,IAAI,CAAC,YAA1B,GAA0B,GAAI,EAAE;QAEtD,IAAI,CAACW,aAAa,CAACC,QAAQ,CAAChC,KAAK,CAAC,EAAE;YAClC,IAAI,CAACW,IAAI,CAACQ,SAAS,CAACC,IAAI,EAAE;mBAAIW,aAAa;gBAAE/B,KAAK;aAAC,CAAC;SACrD;QAED,OAAO,IAAI,CAAA;KACZ;IAEDiC,IAAI,CAACjC,KAAa,EAAE;QAClB,IAAI,CAACY,QAAQ,GAAGZ,KAAK;QACrB,OAAO,IAAI,CAAA;KACZ;IAEDkC,IAAI,GAAG;QACL,IAAI,CAACvB,IAAI,CAACwB,GAAG,CAAC,IAAI,CAACvB,QAAQ,CAAC;KAC7B;CACF;QAjFYL,gBAAgB,GAAhBA,gBAAgB"}