{"version": 3, "sources": ["../../../server/base-http/web.ts"], "names": ["WebNextRequest", "BaseNextRequest", "constructor", "request", "url", "URL", "method", "href", "slice", "origin", "length", "clone", "body", "headers", "name", "value", "entries", "parseBody", "_limit", "Error", "WebNextResponse", "BaseNextResponse", "sent", "_sent", "transformStream", "TransformStream", "writable", "Headers", "textBody", "undefined", "sendPromise", "Promise", "resolve", "sendResolve", "response", "then", "Response", "readable", "status", "statusCode", "statusText", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "delete", "val", "Array", "isArray", "append", "getHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON>", "split", "map", "v", "trimStart", "get", "<PERSON><PERSON><PERSON><PERSON>", "has", "append<PERSON><PERSON>er", "send", "toResponse"], "mappings": "AAAA;;;;AAEkD,IAAA,MAAS,WAAT,SAAS,CAAA;AAEpD,MAAMA,cAAc,SAASC,MAAe,gBAAA;IAIjDC,YAAYC,OAAgB,CAAE;QAC5B,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACF,OAAO,CAACC,GAAG,CAAC;QAEhC,KAAK,CACHD,OAAO,CAACG,MAAM,EACdF,GAAG,CAACG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAACK,MAAM,CAACC,MAAM,CAAC,EACjCP,OAAO,CAACQ,KAAK,EAAE,CAACC,IAAI,CACrB;QACD,IAAI,CAACT,OAAO,GAAGA,OAAO;QAEtB,IAAI,CAACU,OAAO,GAAG,EAAE;QACjB,KAAK,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,IAAIZ,OAAO,CAACU,OAAO,CAACG,OAAO,EAAE,CAAE;YACrD,IAAI,CAACH,OAAO,CAACC,IAAI,CAAC,GAAGC,KAAK;SAC3B;KACF;IAED,MAAME,SAAS,CAACC,MAAuB,EAAgB;QACrD,MAAM,IAAIC,KAAK,CAAC,iDAAiD,CAAC,CAAA;KACnE;CACF;QAvBYnB,cAAc,GAAdA,cAAc;AAyBpB,MAAMoB,eAAe,SAASC,MAAgB,iBAAA;IAoBnD,IAAIC,IAAI,GAAG;QACT,OAAO,IAAI,CAACC,KAAK,CAAA;KAClB;IAEDrB,YAAmBsB,eAAe,GAAG,IAAIC,eAAe,EAAE,CAAE;QAC1D,KAAK,CAACD,eAAe,CAACE,QAAQ,CAAC;aADdF,eAAe,GAAfA,eAAe;aAvB1BX,OAAO,GAAG,IAAIc,OAAO,EAAE;aACvBC,QAAQ,GAAuBC,SAAS;aACxCN,KAAK,GAAG,KAAK;aAEbO,WAAW,GAAG,IAAIC,OAAO,CAAO,CAACC,OAAO,GAAK;YACnD,IAAI,CAACC,WAAW,GAAGD,OAAO;SAC3B,CAAC;aAEME,QAAQ,GAAG,IAAI,CAACJ,WAAW,CAACK,IAAI,CAAC,IAAM;gBACzB,SAAa;YAAjC,OAAO,IAAIC,QAAQ,CAAC,CAAA,SAAa,GAAb,IAAI,CAACR,QAAQ,YAAb,SAAa,GAAI,IAAI,CAACJ,eAAe,CAACa,QAAQ,EAAE;gBAClExB,OAAO,EAAE,IAAI,CAACA,OAAO;gBACrByB,MAAM,EAAE,IAAI,CAACC,UAAU;gBACvBC,UAAU,EAAE,IAAI,CAACC,aAAa;aAC/B,CAAC,CAAA;SACH,CAAC;KAWD;IAEDC,SAAS,CAAC5B,IAAY,EAAEC,KAAwB,EAAQ;QACtD,IAAI,CAACF,OAAO,CAAC8B,MAAM,CAAC7B,IAAI,CAAC;QACzB,KAAK,MAAM8B,GAAG,IAAIC,KAAK,CAACC,OAAO,CAAC/B,KAAK,CAAC,GAAGA,KAAK,GAAG;YAACA,KAAK;SAAC,CAAE;YACxD,IAAI,CAACF,OAAO,CAACkC,MAAM,CAACjC,IAAI,EAAE8B,GAAG,CAAC;SAC/B;QACD,OAAO,IAAI,CAAA;KACZ;IAEDI,eAAe,CAAClC,IAAY,EAAwB;YAE3C,GAAoB;QAD3B,uEAAuE;QACvE,OAAO,CAAA,GAAoB,GAApB,IAAI,CAACmC,SAAS,CAACnC,IAAI,CAAC,SAClB,GADF,KAAA,CACE,GADF,GAAoB,CACvBoC,KAAK,CAAC,GAAG,CAAC,CACXC,GAAG,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,SAAS,EAAE,CAAC,CAAA;KAC7B;IAEDJ,SAAS,CAACnC,IAAY,EAAsB;YACnC,GAAsB;QAA7B,OAAO,CAAA,GAAsB,GAAtB,IAAI,CAACD,OAAO,CAACyC,GAAG,CAACxC,IAAI,CAAC,YAAtB,GAAsB,GAAIe,SAAS,CAAA;KAC3C;IAED0B,SAAS,CAACzC,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACD,OAAO,CAAC2C,GAAG,CAAC1C,IAAI,CAAC,CAAA;KAC9B;IAED2C,YAAY,CAAC3C,IAAY,EAAEC,KAAa,EAAQ;QAC9C,IAAI,CAACF,OAAO,CAACkC,MAAM,CAACjC,IAAI,EAAEC,KAAK,CAAC;QAChC,OAAO,IAAI,CAAA;KACZ;IAEDH,IAAI,CAACG,KAAa,EAAE;QAClB,IAAI,CAACa,QAAQ,GAAGb,KAAK;QACrB,OAAO,IAAI,CAAA;KACZ;IAED2C,IAAI,GAAG;YACL,IAAI,AAAY,EAAhB,GAAgB;QAAhB,CAAA,GAAgB,GAAhB,CAAA,IAAI,GAAJ,IAAI,EAACzB,WAAW,SAAI,GAApB,KAAA,CAAoB,GAApB,GAAgB,CAAhB,IAAoB,CAApB,IAAI,CAAgB,CAAA;QACpB,IAAI,CAACV,KAAK,GAAG,IAAI;KAClB;IAEDoC,UAAU,GAAG;QACX,OAAO,IAAI,CAACzB,QAAQ,CAAA;KACrB;CACF;QArEYd,eAAe,GAAfA,eAAe"}