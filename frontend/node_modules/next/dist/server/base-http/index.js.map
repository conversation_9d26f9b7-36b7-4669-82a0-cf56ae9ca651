{"version": 3, "sources": ["../../../server/base-http/index.ts"], "names": ["BaseNextRequest", "constructor", "method", "url", "body", "cookies", "_cookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "BaseNextResponse", "destination", "redirect", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "PERMANENT_REDIRECT_STATUS"], "mappings": "AAAA;;;;AAG0C,IAAA,UAA4B,WAA5B,4BAA4B,CAAA;AACf,IAAA,SAAc,WAAd,cAAc,CAAA;AAQ9D,MAAeA,eAAe;IAInCC,YAAmBC,MAAc,EAASC,GAAW,EAASC,IAAU,CAAE;aAAvDF,MAAc,GAAdA,MAAc;aAASC,GAAW,GAAXA,GAAW;aAASC,IAAU,GAAVA,IAAU;KAAI;IAI5E,qDAAqD;IAErD,IAAWC,OAAO,GAAG;QACnB,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI,CAACA,QAAQ,CAAA;QACvC,OAAQ,IAAI,CAACA,QAAQ,GAAGC,CAAAA,GAAAA,SAAe,AAAc,CAAA,gBAAd,CAAC,IAAI,CAACC,OAAO,CAAC,EAAE,CAAC;KACzD;CACF;QAdqBR,eAAe,GAAfA,eAAe;AAgB9B,MAAeS,gBAAgB;IAKpCR,YAAmBS,WAAwB,CAAE;aAA1BA,WAAwB,GAAxBA,WAAwB;KAAI;IA4B/C,qDAAqD;IAErDC,QAAQ,CAACD,WAAmB,EAAEE,UAAkB,EAAE;QAChD,IAAI,CAACC,SAAS,CAAC,UAAU,EAAEH,WAAW,CAAC;QACvC,IAAI,CAACE,UAAU,GAAGA,UAAU;QAE5B,0DAA0D;QAC1D,qCAAqC;QACrC,IAAIA,UAAU,KAAKE,UAAyB,0BAAA,EAAE;YAC5C,IAAI,CAACD,SAAS,CAAC,SAAS,EAAE,CAAC,MAAM,EAAEH,WAAW,CAAC,CAAC,CAAC;SAClD;QACD,OAAO,IAAI,CAAA;KACZ;CACF;QA9CqBD,gBAAgB,GAAhBA,gBAAgB"}