{"version": 3, "sources": ["../../server/config-shared.ts"], "names": ["normalizeConfig", "isServerRuntime", "validateConfig", "defaultConfig", "env", "webpack", "webpackDevMiddleware", "eslint", "ignoreDuringBuilds", "typescript", "ignoreBuildErrors", "tsconfigPath", "distDir", "cleanDistDir", "assetPrefix", "config<PERSON><PERSON><PERSON>", "useFileSystemPublicRoutes", "generateBuildId", "generateEtags", "pageExtensions", "target", "poweredByHeader", "compress", "analyticsId", "process", "VERCEL_ANALYTICS_ID", "images", "imageConfigDefault", "devIndicators", "buildActivity", "buildActivityPosition", "onDemandEntries", "maxInactiveAge", "pagesBufferLength", "amp", "canonicalBase", "basePath", "sassOptions", "trailingSlash", "i18n", "productionBrowserSourceMaps", "optimizeFonts", "webpack5", "undefined", "excludeDefaultMomentLocales", "serverRuntimeConfig", "publicRuntimeConfig", "reactStrictMode", "httpAgentOptions", "keepAlive", "outputFileTracing", "staticPageGenerationTimeout", "swcMinify", "output", "NEXT_PRIVATE_STANDALONE", "experimental", "optimisticClientCache", "runtime", "manualClientBasePath", "legacyBrowsers", "browsersListForSwc", "newNextLinkBehavior", "cpus", "Math", "max", "Number", "CIRCLE_NODE_TOTAL", "os", "length", "sharedPool", "profiling", "isrFlushToDisk", "workerThreads", "pageEnv", "proxyTimeout", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "externalDir", "disableOptimizedLoading", "gzipSize", "swcFileReading", "craCompat", "esmExternals", "appDir", "isrMemoryCacheSize", "incremental<PERSON>ache<PERSON>andlerPath", "serverComponents", "fullySpecified", "outputFileTracingRoot", "NEXT_PRIVATE_OUTPUT_TRACE_ROOT", "swcTraceProfiling", "forceSwcTransforms", "swcPlugins", "swcMinifyDebugOptions", "largePageDataBytes", "disablePostcssPresetEnv", "urlImports", "modularizeImports", "adjustFontFallbacks", "phase", "config", "value", "userConfig", "configValida<PERSON>", "require", "errors"], "mappings": "AAAA;;;;QA+kBsBA,eAAe,GAAfA,eAAe;QAQrBC,eAAe,GAAfA,eAAe;QAMfC,cAAc,GAAdA,cAAc;;AA7lBf,IAAA,GAAI,kCAAJ,IAAI,EAAA;AAOZ,IAAA,YAA4B,WAA5B,4BAA4B,CAAA;;;;;;AAke5B,MAAMC,aAAa,GAAe;IACvCC,GAAG,EAAE,EAAE;IACPC,OAAO,EAAE,IAAI;IACbC,oBAAoB,EAAE,IAAI;IAC1BC,MAAM,EAAE;QACNC,kBAAkB,EAAE,KAAK;KAC1B;IACDC,UAAU,EAAE;QACVC,iBAAiB,EAAE,KAAK;QACxBC,YAAY,EAAE,eAAe;KAC9B;IACDC,OAAO,EAAE,OAAO;IAChBC,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,SAAS;IACvBC,yBAAyB,EAAE,IAAI;IAC/BC,eAAe,EAAE,IAAM,IAAI;IAC3BC,aAAa,EAAE,IAAI;IACnBC,cAAc,EAAE;QAAC,KAAK;QAAE,IAAI;QAAE,KAAK;QAAE,IAAI;KAAC;IAC1CC,MAAM,EAAE,QAAQ;IAChBC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAEC,OAAO,CAACpB,GAAG,CAACqB,mBAAmB,IAAI,EAAE;IAClDC,MAAM,EAAEC,YAAkB,mBAAA;IAC1BC,aAAa,EAAE;QACbC,aAAa,EAAE,IAAI;QACnBC,qBAAqB,EAAE,cAAc;KACtC;IACDC,eAAe,EAAE;QACfC,cAAc,EAAE,EAAE,GAAG,IAAI;QACzBC,iBAAiB,EAAE,CAAC;KACrB;IACDC,GAAG,EAAE;QACHC,aAAa,EAAE,EAAE;KAClB;IACDC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,KAAK;IACpBC,IAAI,EAAE,IAAI;IACVC,2BAA2B,EAAE,KAAK;IAClCC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAEC,SAAS;IACnBC,2BAA2B,EAAE,IAAI;IACjCC,mBAAmB,EAAE,EAAE;IACvBC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE;QAChBC,SAAS,EAAE,IAAI;KAChB;IACDC,iBAAiB,EAAE,IAAI;IACvBC,2BAA2B,EAAE,EAAE;IAC/BC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,CAAC,CAAC7B,OAAO,CAACpB,GAAG,CAACkD,uBAAuB,GAAG,YAAY,GAAGX,SAAS;IACxEY,YAAY,EAAE;QACZC,qBAAqB,EAAE,IAAI;QAC3BC,OAAO,EAAEd,SAAS;QAClBe,oBAAoB,EAAE,KAAK;QAC3B,+DAA+D;QAC/DC,cAAc,EAAE,IAAI;QACpBC,kBAAkB,EAAE,KAAK;QACzB,+DAA+D;QAC/DC,mBAAmB,EAAE,KAAK;QAC1BC,IAAI,EAAEC,IAAI,CAACC,GAAG,CACZ,CAAC,EACD,CAACC,MAAM,CAACzC,OAAO,CAACpB,GAAG,CAAC8D,iBAAiB,CAAC,IACpC,CAACC,GAAE,QAAA,CAACL,IAAI,EAAE,IAAI;YAAEM,MAAM,EAAE,CAAC;SAAE,CAAC,CAACA,MAAM,CAAC,GAAG,CAAC,CAC3C;QACDC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,KAAK;QAChBC,cAAc,EAAE,IAAI;QACpBC,aAAa,EAAE,KAAK;QACpBC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE/B,SAAS;QACvBgC,WAAW,EAAE,KAAK;QAClBC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,WAAW,EAAE,KAAK;QAClBC,uBAAuB,EAAE,KAAK;QAC9BC,QAAQ,EAAE,IAAI;QACdC,cAAc,EAAE,IAAI;QACpBC,SAAS,EAAE,KAAK;QAChBC,YAAY,EAAE,IAAI;QAClBC,MAAM,EAAE,KAAK;QACb,wBAAwB;QACxBC,kBAAkB,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;QACpCC,2BAA2B,EAAE3C,SAAS;QACtC4C,gBAAgB,EAAE,KAAK;QACvBC,cAAc,EAAE,KAAK;QACrBC,qBAAqB,EAAEjE,OAAO,CAACpB,GAAG,CAACsF,8BAA8B,IAAI,EAAE;QACvEC,iBAAiB,EAAE,KAAK;QACxBC,kBAAkB,EAAE,KAAK;QACzBC,UAAU,EAAElD,SAAS;QACrBmD,qBAAqB,EAAEnD,SAAS;QAChCoD,kBAAkB,EAAE,GAAG,GAAG,IAAI;QAC9BC,uBAAuB,EAAErD,SAAS;QAClCT,GAAG,EAAES,SAAS;QACdsD,UAAU,EAAEtD,SAAS;QACrBuD,iBAAiB,EAAEvD,SAAS;QAC5BwD,mBAAmB,EAAE,KAAK;KAC3B;CACF;QApGYhG,aAAa,GAAbA,aAAa;AAsGnB,eAAeH,eAAe,CAACoG,KAAa,EAAEC,MAAW,EAAE;IAChE,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;QAChCA,MAAM,GAAGA,MAAM,CAACD,KAAK,EAAE;YAAEjG,aAAa;SAAE,CAAC;KAC1C;IACD,gFAAgF;IAChF,OAAO,MAAMkG,MAAM,CAAA;CACpB;AAEM,SAASpG,eAAe,CAACqG,KAAc,EAA0B;IACtE,OACEA,KAAK,KAAK3D,SAAS,IAAI2D,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,mBAAmB,CAC3E;CACF;AAEM,SAASpG,cAAc,CAACqG,UAAsB,EAEnD;IACA,MAAMC,eAAe,GAAGC,OAAO,CAAC,mCAAmC,CAAC;IACpED,eAAe,CAACD,UAAU,CAAC;IAC3B,OAAO;QACLG,MAAM,EAAEF,eAAe,CAACE,MAAM;KAC/B,CAAA;CACF"}