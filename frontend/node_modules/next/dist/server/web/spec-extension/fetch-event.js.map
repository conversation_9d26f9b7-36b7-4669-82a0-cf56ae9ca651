{"version": 3, "sources": ["../../../../server/web/spec-extension/fetch-event.ts"], "names": ["responseSymbol", "Symbol", "passThroughSymbol", "waitUntilSymbol", "FetchEvent", "constructor", "_request", "respondWith", "response", "Promise", "resolve", "passThroughOnException", "waitUntil", "promise", "push", "NextFetchEvent", "params", "request", "sourcePage", "page", "PageSignatureError"], "mappings": "AAAA;;;;;AAAmC,IAAA,MAAU,WAAV,UAAU,CAAA;AAG7C,MAAMA,cAAc,GAAGC,MAAM,CAAC,UAAU,CAAC;AACzC,MAAMC,iBAAiB,GAAGD,MAAM,CAAC,aAAa,CAAC;AACxC,MAAME,eAAe,GAAGF,MAAM,CAAC,WAAW,CAAC;QAArCE,eAAe,GAAfA,eAAe;AAE5B,MAAMC,UAAU;IACL,CAACD,eAAe,CAAC,GAAmB,EAAE,CAAC;IAEhD,CAACD,iBAAiB,CAAC,GAAG,KAAK,CAAA;IAE3B,qEAAqE;IACrEG,YAAYC,QAAiB,CAAE,EAAE;IAEjCC,WAAW,CAACC,QAAsC,EAAQ;QACxD,IAAI,CAAC,IAAI,CAACR,cAAc,CAAC,EAAE;YACzB,IAAI,CAACA,cAAc,CAAC,GAAGS,OAAO,CAACC,OAAO,CAACF,QAAQ,CAAC;SACjD;KACF;IAEDG,sBAAsB,GAAS;QAC7B,IAAI,CAACT,iBAAiB,CAAC,GAAG,IAAI;KAC/B;IAEDU,SAAS,CAACC,OAAqB,EAAQ;QACrC,IAAI,CAACV,eAAe,CAAC,CAACW,IAAI,CAACD,OAAO,CAAC;KACpC;CACF;AAEM,MAAME,cAAc,SAASX,UAAU;IAG5CC,YAAYW,MAA8C,CAAE;QAC1D,KAAK,CAACA,MAAM,CAACC,OAAO,CAAC;QACrB,IAAI,CAACC,UAAU,GAAGF,MAAM,CAACG,IAAI;KAC9B;IAED;;;;KAIG,CACH,IAAIF,OAAO,GAAG;QACZ,MAAM,IAAIG,MAAkB,mBAAA,CAAC;YAC3BD,IAAI,EAAE,IAAI,CAACD,UAAU;SACtB,CAAC,CAAA;KACH;IAED;;;;KAIG,CACHX,WAAW,GAAG;QACZ,MAAM,IAAIa,MAAkB,mBAAA,CAAC;YAC3BD,IAAI,EAAE,IAAI,CAACD,UAAU;SACtB,CAAC,CAAA;KACH;CACF;QA7BYH,cAAc,GAAdA,cAAc"}