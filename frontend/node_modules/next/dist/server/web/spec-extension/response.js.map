{"version": 3, "sources": ["../../../../server/web/spec-extension/response.ts"], "names": ["INTERNALS", "Symbol", "REDIRECTS", "Set", "NextResponse", "Response", "constructor", "body", "init", "cookies", "NextCookies", "url", "NextURL", "headers", "toNodeHeaders", "nextConfig", "undefined", "for", "bodyUsed", "Object", "fromEntries", "ok", "redirected", "status", "statusText", "type", "json", "response", "redirect", "has", "RangeError", "initObj", "Headers", "set", "validateURL", "rewrite", "destination", "next"], "mappings": "AAAA;;;;AACwB,IAAA,QAAa,WAAb,aAAa,CAAA;AACM,IAAA,MAAU,WAAV,UAAU,CAAA;AAEzB,IAAA,QAAW,WAAX,WAAW,CAAA;AAEvC,MAAMA,SAAS,GAAGC,MAAM,CAAC,mBAAmB,CAAC;AAC7C,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC;AAAC,OAAG;AAAE,OAAG;AAAE,OAAG;AAAE,OAAG;AAAE,OAAG;CAAC,CAAC;AAE7C,MAAMC,YAAY,SAASC,QAAQ;IAMxCC,YAAYC,IAAsB,EAAEC,IAAkB,GAAG,EAAE,CAAE;QAC3D,KAAK,CAACD,IAAI,EAAEC,IAAI,CAAC;QAEjB,IAAI,CAACR,SAAS,CAAC,GAAG;YAChBS,OAAO,EAAE,IAAIC,QAAW,YAAA,CAAC,IAAI,CAAC;YAC9BC,GAAG,EAAEH,IAAI,CAACG,GAAG,GACT,IAAIC,QAAO,QAAA,CAACJ,IAAI,CAACG,GAAG,EAAE;gBACpBE,OAAO,EAAEC,CAAAA,GAAAA,MAAa,AAAc,CAAA,cAAd,CAAC,IAAI,CAACD,OAAO,CAAC;gBACpCE,UAAU,EAAEP,IAAI,CAACO,UAAU;aAC5B,CAAC,GACFC,SAAS;SACd;KACF;IAED,CAACf,MAAM,CAACgB,GAAG,CAAC,6BAA6B,CAAC,CAAC,GAAG;QAC5C,OAAO;YACLR,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBE,GAAG,EAAE,IAAI,CAACA,GAAG;YACb,mCAAmC;YACnCJ,IAAI,EAAE,IAAI,CAACA,IAAI;YACfW,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBL,OAAO,EAAEM,MAAM,CAACC,WAAW,CAAC,IAAI,CAACP,OAAO,CAAC;YACzCQ,EAAE,EAAE,IAAI,CAACA,EAAE;YACXC,UAAU,EAAE,IAAI,CAACA,UAAU;YAC3BC,MAAM,EAAE,IAAI,CAACA,MAAM;YACnBC,UAAU,EAAE,IAAI,CAACA,UAAU;YAC3BC,IAAI,EAAE,IAAI,CAACA,IAAI;SAChB,CAAA;KACF;IAED,IAAWhB,OAAO,GAAG;QACnB,OAAO,IAAI,CAACT,SAAS,CAAC,CAACS,OAAO,CAAA;KAC/B;IAED,OAAOiB,IAAI,CAACnB,IAAS,EAAEC,IAAmB,EAAgB;QACxD,8EAA8E;QAC9E,MAAMmB,QAAQ,GAAatB,QAAQ,CAACqB,IAAI,CAACnB,IAAI,EAAEC,IAAI,CAAC;QACpD,OAAO,IAAIJ,YAAY,CAACuB,QAAQ,CAACpB,IAAI,EAAEoB,QAAQ,CAAC,CAAA;KACjD;IAED,OAAOC,QAAQ,CAACjB,GAA2B,EAAEH,IAA4B,EAAE;YACxBA,GAAY;QAA7D,MAAMe,MAAM,GAAG,OAAOf,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,CAAAA,GAAY,GAAZA,IAAI,QAAQ,GAAZA,KAAAA,CAAY,GAAZA,IAAI,CAAEe,MAAM,YAAZf,GAAY,GAAI,GAAG;QACpE,IAAI,CAACN,SAAS,CAAC2B,GAAG,CAACN,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAIO,UAAU,CAClB,iEAAiE,CAClE,CAAA;SACF;QACD,MAAMC,OAAO,GAAG,OAAOvB,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG,EAAE;QACpD,MAAMK,OAAO,GAAG,IAAImB,OAAO,CAACD,OAAO,QAAS,GAAhBA,KAAAA,CAAgB,GAAhBA,OAAO,CAAElB,OAAO,CAAC;QAC7CA,OAAO,CAACoB,GAAG,CAAC,UAAU,EAAEC,CAAAA,GAAAA,MAAW,AAAK,CAAA,YAAL,CAACvB,GAAG,CAAC,CAAC;QAEzC,OAAO,IAAIP,YAAY,CAAC,IAAI,EAAE;YAC5B,GAAG2B,OAAO;YACVlB,OAAO;YACPU,MAAM;SACP,CAAC,CAAA;KACH;IAED,OAAOY,OAAO,CAACC,WAAmC,EAAE5B,IAAmB,EAAE;QACvE,MAAMK,OAAO,GAAG,IAAImB,OAAO,CAACxB,IAAI,QAAS,GAAbA,KAAAA,CAAa,GAAbA,IAAI,CAAEK,OAAO,CAAC;QAC1CA,OAAO,CAACoB,GAAG,CAAC,sBAAsB,EAAEC,CAAAA,GAAAA,MAAW,AAAa,CAAA,YAAb,CAACE,WAAW,CAAC,CAAC;QAC7D,OAAO,IAAIhC,YAAY,CAAC,IAAI,EAAE;YAAE,GAAGI,IAAI;YAAEK,OAAO;SAAE,CAAC,CAAA;KACpD;IAED,OAAOwB,IAAI,CAAC7B,IAAmB,EAAE;QAC/B,MAAMK,OAAO,GAAG,IAAImB,OAAO,CAACxB,IAAI,QAAS,GAAbA,KAAAA,CAAa,GAAbA,IAAI,CAAEK,OAAO,CAAC;QAC1CA,OAAO,CAACoB,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC;QACrC,OAAO,IAAI7B,YAAY,CAAC,IAAI,EAAE;YAAE,GAAGI,IAAI;YAAEK,OAAO;SAAE,CAAC,CAAA;KACpD;CACF;QA3EYT,YAAY,GAAZA,YAAY"}