{"version": 3, "sources": ["../../../../server/web/spec-extension/cookies.ts"], "names": ["normalizeCookieOptions", "options", "Object", "assign", "maxAge", "expires", "Date", "now", "path", "serializeValue", "value", "JSON", "stringify", "String", "serializeExpiredCookie", "key", "cookie", "serialize", "deserializeCookie", "input", "headers", "get", "undefined", "split", "serializeCookie", "join", "Cookies", "Map", "constructor", "parsedInput", "parse", "entries", "set", "Symbol", "for", "fromEntries", "NextCookies", "response", "args", "getWithOptions", "raw", "isAlreadyAdded", "has", "<PERSON><PERSON><PERSON><PERSON>", "Error", "<PERSON><PERSON><PERSON><PERSON>", "filter", "startsWith", "append", "delete", "isDeleted", "<PERSON><PERSON><PERSON><PERSON>", "clear", "expiredCookies", "Array", "from", "keys", "map"], "mappings": "AAAA;;;;AAEmB,IAAA,OAA2B,kCAA3B,2BAA2B,EAAA;;;;;;AAO9C,MAAMA,sBAAsB,GAAG,CAACC,OAA+B,GAAK;IAClEA,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEF,OAAO,CAAC;IAEpC,IAAIA,OAAO,CAACG,MAAM,EAAE;QAClBH,OAAO,CAACI,OAAO,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAGN,OAAO,CAACG,MAAM,GAAG,IAAI,CAAC;KAC/D;IAED,IAAIH,OAAO,CAACO,IAAI,IAAI,IAAI,EAAE;QACxBP,OAAO,CAACO,IAAI,GAAG,GAAG;KACnB;IAED,OAAOP,OAAO,CAAA;CACf;AAED,MAAMQ,cAAc,GAAG,CAACC,KAAc,GACpC,OAAOA,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE,EAAEC,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC,CAAC,CAAC,GAAGG,MAAM,CAACH,KAAK,CAAC;AAE1E,MAAMI,sBAAsB,GAAG,CAC7BC,GAAW,EACXd,OAA+B,GAAG,EAAE,GAEpCe,OAAM,QAAA,CAACC,SAAS,CAACF,GAAG,EAAE,EAAE,EAAE;QACxBV,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC;QACpBE,IAAI,EAAE,GAAG;QACT,GAAGP,OAAO;KACX,CAAC;AAEJ,MAAMiB,iBAAiB,GAAG,CAACC,KAAyB,GAAe;IACjE,MAAMT,KAAK,GAAGS,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IAC7C,OAAOX,KAAK,KAAKY,SAAS,IAAIZ,KAAK,KAAK,IAAI,GAAGA,KAAK,CAACa,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;CACtE;AAED,MAAMC,eAAe,GAAG,CAACL,KAAe,GAAKA,KAAK,CAACM,IAAI,CAAC,IAAI,CAAC;AAEtD,MAAMC,OAAO,SAASC,GAAG;IAC9BC,YAAYT,KAAqB,CAAE;QACjC,MAAMU,WAAW,GAAG,OAAOV,KAAK,KAAK,QAAQ,GAAGH,OAAM,QAAA,CAACc,KAAK,CAACX,KAAK,CAAC,GAAG,EAAE;QACxE,KAAK,CAACjB,MAAM,CAAC6B,OAAO,CAACF,WAAW,CAAC,CAAC;KACnC;IACDG,GAAG,CAACjB,GAAW,EAAEL,KAAc,EAAET,OAA+B,GAAG,EAAE,EAAE;QACrE,OAAO,KAAK,CAAC+B,GAAG,CACdjB,GAAG,EACHC,OAAM,QAAA,CAACC,SAAS,CACdF,GAAG,EACHN,cAAc,CAACC,KAAK,CAAC,EACrBV,sBAAsB,CAACC,OAAO,CAAC,CAChC,CACF,CAAA;KACF;IACD,CAACgC,MAAM,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAAC,GAAG;QAC5C,OAAOhC,MAAM,CAACiC,WAAW,CAAC,IAAI,CAACJ,OAAO,EAAE,CAAC,CAAA;KAC1C;CACF;QAlBYL,OAAO,GAAPA,OAAO;AAoBb,MAAMU,WAAW,SAASV,OAAO;IAGtCE,YAAYS,QAA4B,CAAE;QACxC,KAAK,CAACA,QAAQ,CAACjB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,CAACgB,QAAQ,GAAGA,QAAQ;KACzB;IACDhB,GAAG,GAAG,CAAC,GAAGiB,IAAI,AAA4B,GAAK;QAC7C,OAAO,IAAI,CAACC,cAAc,IAAID,IAAI,CAAC,CAAC5B,KAAK,CAAA;KAC1C,CAAA;IACD6B,cAAc,GAAG,CACf,GAAGD,IAAI,AAA4B,GACV;QACzB,MAAME,GAAG,GAAG,KAAK,CAACnB,GAAG,IAAIiB,IAAI,CAAC;QAC9B,IAAI,OAAOE,GAAG,KAAK,QAAQ,EAAE,OAAO;YAAE9B,KAAK,EAAE8B,GAAG;YAAEvC,OAAO,EAAE,EAAE;SAAE,CAAA;QAC/D,MAAM,EAAE,CAACqC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE5B,KAAK,CAAA,EAAE,GAAGT,OAAO,EAAE,GAAGe,OAAM,QAAA,CAACc,KAAK,CAACU,GAAG,CAAC;QAC1D,OAAO;YAAE9B,KAAK;YAAET,OAAO;SAAE,CAAA;KAC1B,CAAA;IACD+B,GAAG,GAAG,CAAC,GAAGM,IAAI,AAA4B,GAAK;QAC7C,MAAMG,cAAc,GAAG,KAAK,CAACC,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC;QAEzC,KAAK,CAACN,GAAG,IAAIM,IAAI,CAAC;QAClB,MAAMK,aAAa,GAAG,KAAK,CAACtB,GAAG,CAACiB,IAAI,CAAC,CAAC,CAAC,CAAC;QAExC,IAAI,OAAOK,aAAa,KAAK,QAAQ,EAAE;YACrC,MAAM,IAAIC,KAAK,CACb,CAAC,yCAAyC,EAAEjC,IAAI,CAACC,SAAS,CAAC0B,IAAI,CAAC,CAAC,CAAC,CACnE,CAAA;SACF;QAED,IAAIG,cAAc,EAAE;YAClB,MAAMI,SAAS,GAAGrB,eAAe,CAC/BN,iBAAiB,CAAC,IAAI,CAACmB,QAAQ,CAAC,CAACS,MAAM,CACrC,CAACpC,KAAK,GAAK,CAACA,KAAK,CAACqC,UAAU,CAAC,CAAC,EAAET,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC5C,CACF;YAED,IAAIO,SAAS,EAAE;gBACb,IAAI,CAACR,QAAQ,CAACjB,OAAO,CAACY,GAAG,CACvB,YAAY,EACZ;oBAACW,aAAa;oBAAEE,SAAS;iBAAC,CAACpB,IAAI,CAAC,IAAI,CAAC,CACtC;aACF,MAAM;gBACL,IAAI,CAACY,QAAQ,CAACjB,OAAO,CAACY,GAAG,CAAC,YAAY,EAAEW,aAAa,CAAC;aACvD;SACF,MAAM;YACL,IAAI,CAACN,QAAQ,CAACjB,OAAO,CAAC4B,MAAM,CAAC,YAAY,EAAEL,aAAa,CAAC;SAC1D;QAED,OAAO,IAAI,CAAA;KACZ,CAAA;IACDM,MAAM,GAAG,CAAClC,GAAW,EAAEd,OAA+B,GAAG,EAAE,GAAK;QAC9D,MAAMiD,SAAS,GAAG,KAAK,CAACD,MAAM,CAAClC,GAAG,CAAC;QAEnC,IAAImC,SAAS,EAAE;YACb,MAAML,SAAS,GAAGrB,eAAe,CAC/BN,iBAAiB,CAAC,IAAI,CAACmB,QAAQ,CAAC,CAACS,MAAM,CACrC,CAACpC,KAAK,GAAK,CAACA,KAAK,CAACqC,UAAU,CAAC,CAAC,EAAEhC,GAAG,CAAC,CAAC,CAAC,CAAC,CACxC,CACF;YACD,MAAMoC,aAAa,GAAGrC,sBAAsB,CAACC,GAAG,EAAEd,OAAO,CAAC;YAC1D,IAAI,CAACoC,QAAQ,CAACjB,OAAO,CAACY,GAAG,CACvB,YAAY,EACZ;gBAACmB,aAAa;gBAAEN,SAAS;aAAC,CAACpB,IAAI,CAAC,IAAI,CAAC,CACtC;SACF;QAED,OAAOyB,SAAS,CAAA;KACjB,CAAA;IACDE,KAAK,GAAG,CAACnD,OAA+B,GAAG,EAAE,GAAK;QAChD,MAAMoD,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC,KAAK,CAACC,IAAI,EAAE,CAAC,CAC5CC,GAAG,CAAC,CAAC1C,GAAG,GAAKD,sBAAsB,CAACC,GAAG,EAAEd,OAAO,CAAC,CAAC,CAClDwB,IAAI,CAAC,IAAI,CAAC;QAEb,IAAI4B,cAAc,EAAE,IAAI,CAAChB,QAAQ,CAACjB,OAAO,CAACY,GAAG,CAAC,YAAY,EAAEqB,cAAc,CAAC;QAC3E,OAAO,KAAK,CAACD,KAAK,EAAE,CAAA;KACrB,CAAA;CACF;QA7EYhB,WAAW,GAAXA,WAAW"}