{"version": 3, "sources": ["../../../server/web/utils.ts"], "names": ["fromNodeHeaders", "splitCookiesString", "toNodeHeaders", "validateURL", "object", "headers", "Headers", "key", "value", "Object", "entries", "values", "Array", "isArray", "v", "undefined", "append", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "length", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "result", "toLowerCase", "url", "String", "URL", "error", "Error", "cause"], "mappings": "AAAA;;;;QAEgBA,eAAe,GAAfA,eAAe;QAuBfC,kBAAkB,GAAlBA,kBAAkB;QAkElBC,aAAa,GAAbA,aAAa;QAgBbC,WAAW,GAAXA,WAAW;AAzGpB,SAASH,eAAe,CAACI,MAAmB,EAAW;IAC5D,MAAMC,OAAO,GAAG,IAAIC,OAAO,EAAE;IAC7B,KAAK,IAAI,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,MAAM,CAAC,CAAE;QAC/C,MAAMO,MAAM,GAAGC,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,GAAGA,KAAK,GAAG;YAACA,KAAK;SAAC;QACrD,KAAK,IAAIM,CAAC,IAAIH,MAAM,CAAE;YACpB,IAAIG,CAAC,KAAKC,SAAS,EAAE;gBACnBV,OAAO,CAACW,MAAM,CAACT,GAAG,EAAEO,CAAC,CAAC;aACvB;SACF;KACF;IACD,OAAOT,OAAO,CAAA;CACf;AAYM,SAASJ,kBAAkB,CAACgB,aAAqB,EAAE;IACxD,IAAIC,cAAc,GAAG,EAAE;IACvB,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,KAAK;IACT,IAAIC,EAAE;IACN,IAAIC,SAAS;IACb,IAAIC,SAAS;IACb,IAAIC,qBAAqB;IAEzB,SAASC,cAAc,GAAG;QACxB,MAAON,GAAG,GAAGF,aAAa,CAACS,MAAM,IAAI,KAAKC,IAAI,CAACV,aAAa,CAACW,MAAM,CAACT,GAAG,CAAC,CAAC,CAAE;YACzEA,GAAG,IAAI,CAAC;SACT;QACD,OAAOA,GAAG,GAAGF,aAAa,CAACS,MAAM,CAAA;KAClC;IAED,SAASG,cAAc,GAAG;QACxBR,EAAE,GAAGJ,aAAa,CAACW,MAAM,CAACT,GAAG,CAAC;QAE9B,OAAOE,EAAE,KAAK,GAAG,IAAIA,EAAE,KAAK,GAAG,IAAIA,EAAE,KAAK,GAAG,CAAA;KAC9C;IAED,MAAOF,GAAG,GAAGF,aAAa,CAACS,MAAM,CAAE;QACjCN,KAAK,GAAGD,GAAG;QACXK,qBAAqB,GAAG,KAAK;QAE7B,MAAOC,cAAc,EAAE,CAAE;YACvBJ,EAAE,GAAGJ,aAAa,CAACW,MAAM,CAACT,GAAG,CAAC;YAC9B,IAAIE,EAAE,KAAK,GAAG,EAAE;gBACd,uEAAuE;gBACvEC,SAAS,GAAGH,GAAG;gBACfA,GAAG,IAAI,CAAC;gBAERM,cAAc,EAAE;gBAChBF,SAAS,GAAGJ,GAAG;gBAEf,MAAOA,GAAG,GAAGF,aAAa,CAACS,MAAM,IAAIG,cAAc,EAAE,CAAE;oBACrDV,GAAG,IAAI,CAAC;iBACT;gBAED,8BAA8B;gBAC9B,IAAIA,GAAG,GAAGF,aAAa,CAACS,MAAM,IAAIT,aAAa,CAACW,MAAM,CAACT,GAAG,CAAC,KAAK,GAAG,EAAE;oBACnE,6BAA6B;oBAC7BK,qBAAqB,GAAG,IAAI;oBAC5B,2DAA2D;oBAC3DL,GAAG,GAAGI,SAAS;oBACfL,cAAc,CAACY,IAAI,CAACb,aAAa,CAACc,SAAS,CAACX,KAAK,EAAEE,SAAS,CAAC,CAAC;oBAC9DF,KAAK,GAAGD,GAAG;iBACZ,MAAM;oBACL,uCAAuC;oBACvC,8BAA8B;oBAC9BA,GAAG,GAAGG,SAAS,GAAG,CAAC;iBACpB;aACF,MAAM;gBACLH,GAAG,IAAI,CAAC;aACT;SACF;QAED,IAAI,CAACK,qBAAqB,IAAIL,GAAG,IAAIF,aAAa,CAACS,MAAM,EAAE;YACzDR,cAAc,CAACY,IAAI,CAACb,aAAa,CAACc,SAAS,CAACX,KAAK,EAAEH,aAAa,CAACS,MAAM,CAAC,CAAC;SAC1E;KACF;IAED,OAAOR,cAAc,CAAA;CACtB;AAEM,SAAShB,aAAa,CAACG,OAAiB,EAAe;IAC5D,MAAM2B,MAAM,GAAgB,EAAE;IAC9B,IAAI3B,OAAO,EAAE;QACX,KAAK,MAAM,CAACE,GAAG,EAAEC,KAAK,CAAC,IAAIH,OAAO,CAACK,OAAO,EAAE,CAAE;YAC5CsB,MAAM,CAACzB,GAAG,CAAC,GAAGC,KAAK;YACnB,IAAID,GAAG,CAAC0B,WAAW,EAAE,KAAK,YAAY,EAAE;gBACtCD,MAAM,CAACzB,GAAG,CAAC,GAAGN,kBAAkB,CAACO,KAAK,CAAC;aACxC;SACF;KACF;IACD,OAAOwB,MAAM,CAAA;CACd;AAKM,SAAS7B,WAAW,CAAC+B,GAAiB,EAAU;IACrD,IAAI;QACF,OAAOC,MAAM,CAAC,IAAIC,GAAG,CAACD,MAAM,CAACD,GAAG,CAAC,CAAC,CAAC,CAAA;KACpC,CAAC,OAAOG,KAAK,EAAO;QACnB,MAAM,IAAIC,KAAK,CACb,CAAC,4GAA4G,CAAC,EAC9G;YAAEC,KAAK,EAAEF,KAAK;SAAE,CACjB,CAAA;KACF;CACF"}