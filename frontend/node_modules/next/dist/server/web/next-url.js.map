{"version": 3, "sources": ["../../../server/web/next-url.ts"], "names": ["FLIGHT_PARAMETERS", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "url", "base", "URL", "String", "replace", "parseFlightParameters", "searchParams", "flightSearchParameters", "flightSearchParametersUpdated", "name", "value", "get", "undefined", "Internal", "Symbol", "NextURL", "constructor", "input", "baseOrOpts", "opts", "options", "basePath", "analyzeUrl", "pathnameInfo", "getNextPathnameInfo", "pathname", "nextConfig", "parseData", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "domainLocale", "detectDomainLocale", "i18n", "domains", "getHostname", "headers", "defaultLocale", "buildId", "locale", "trailingSlash", "formatPathname", "formatNextPathnameInfo", "forceLocale", "formatSearch", "search", "URLSearchParams", "set", "params", "toString", "flightSearchParams", "delete", "locales", "includes", "TypeError", "host", "hostname", "port", "protocol", "href", "origin", "hash", "password", "username", "startsWith", "toJSON", "for", "clone"], "mappings": "AAAA;;;;AACmC,IAAA,mBAA4C,WAA5C,4CAA4C,CAAA;AACxC,IAAA,uBAAyD,WAAzD,yDAAyD,CAAA;AACpE,IAAA,YAA+B,WAA/B,+BAA+B,CAAA;AACvB,IAAA,oBAAsD,WAAtD,sDAAsD,CAAA;AAa1F,MAAMA,iBAAiB,GAAG;IACxB,YAAY;IACZ,8BAA8B;IAC9B,qBAAqB;CACtB,AAAS;AAEV,MAAMC,wBAAwB,yFAC0D;AAExF,SAASC,QAAQ,CAACC,GAAiB,EAAEC,IAAmB,EAAE;IACxD,OAAO,IAAIC,GAAG,CACZC,MAAM,CAACH,GAAG,CAAC,CAACI,OAAO,CAACN,wBAAwB,EAAE,WAAW,CAAC,EAC1DG,IAAI,IAAIE,MAAM,CAACF,IAAI,CAAC,CAACG,OAAO,CAACN,wBAAwB,EAAE,WAAW,CAAC,CACpE,CAAA;CACF;AAED,SAASO,qBAAqB,CAC5BC,YAA6B,EACO;IACpC,IAAIC,sBAAsB,GAA2B,EAAE;IACvD,IAAIC,6BAA6B,GAAG,KAAK;IACzC,KAAK,MAAMC,IAAI,IAAIZ,iBAAiB,CAAE;QACpC,MAAMa,KAAK,GAAGJ,YAAY,CAACK,GAAG,CAACF,IAAI,CAAC;QACpC,IAAIC,KAAK,KAAK,IAAI,EAAE;YAClB,SAAQ;SACT;QAEDH,sBAAsB,CAACE,IAAI,CAAC,GAAGC,KAAK;QACpCF,6BAA6B,GAAG,IAAI;KACrC;IAED,IAAI,CAACA,6BAA6B,EAAE;QAClC,OAAOI,SAAS,CAAA;KACjB;IAED,OAAOL,sBAAsB,CAAA;CAC9B;AAED,MAAMM,QAAQ,GAAGC,MAAM,CAAC,iBAAiB,CAAC;AAEnC,MAAMC,OAAO;IAelBC,YACEC,KAAmB,EACnBC,UAAmC,EACnCC,IAAc,CACd;QACA,IAAIlB,IAAI,AAA0B;QAClC,IAAImB,OAAO,AAAS;QAEpB,IACE,AAAC,OAAOF,UAAU,KAAK,QAAQ,IAAI,UAAU,IAAIA,UAAU,IAC3D,OAAOA,UAAU,KAAK,QAAQ,EAC9B;YACAjB,IAAI,GAAGiB,UAAU;YACjBE,OAAO,GAAGD,IAAI,IAAI,EAAE;SACrB,MAAM;YACLC,OAAO,GAAGD,IAAI,IAAID,UAAU,IAAI,EAAE;SACnC;QAED,IAAI,CAACL,QAAQ,CAAC,GAAG;YACfb,GAAG,EAAED,QAAQ,CAACkB,KAAK,EAAEhB,IAAI,WAAJA,IAAI,GAAImB,OAAO,CAACnB,IAAI,CAAC;YAC1CmB,OAAO,EAAEA,OAAO;YAChBC,QAAQ,EAAE,EAAE;SACb;QAED,IAAI,CAACC,UAAU,EAAE;KAClB;IAED,AAAQA,UAAU,GAAG;YAOjB,GAAiC,QAKjC,IAA2B,EAC3B,IAAiC;QAZnC,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,oBAAmB,AAGtC,CAAA,oBAHsC,CAAC,IAAI,CAACX,QAAQ,CAAC,CAACb,GAAG,CAACyB,QAAQ,EAAE;YACpEC,UAAU,EAAE,IAAI,CAACb,QAAQ,CAAC,CAACO,OAAO,CAACM,UAAU;YAC7CC,SAAS,EAAE,CAACC,OAAO,CAACC,GAAG,CAACC,kCAAkC;SAC3D,CAAC;QAEF,IAAI,CAACjB,QAAQ,CAAC,CAACkB,YAAY,GAAGC,CAAAA,GAAAA,mBAAkB,AAG/C,CAAA,mBAH+C,CAC9C,CAAA,GAAiC,GAAjC,IAAI,CAACnB,QAAQ,CAAC,CAACO,OAAO,CAACM,UAAU,SAAM,GAAvC,KAAA,CAAuC,GAAvC,QAAA,GAAiC,CAAEO,IAAI,SAAA,GAAvC,KAAA,CAAuC,QAAEC,OAAO,AAAT,EACvCC,CAAAA,GAAAA,YAAW,AAAoD,CAAA,YAApD,CAAC,IAAI,CAACtB,QAAQ,CAAC,CAACb,GAAG,EAAE,IAAI,CAACa,QAAQ,CAAC,CAACO,OAAO,CAACgB,OAAO,CAAC,CAChE;QAED,MAAMC,aAAa,GACjB,CAAA,CAAA,IAA2B,GAA3B,IAAI,CAACxB,QAAQ,CAAC,CAACkB,YAAY,SAAe,GAA1C,KAAA,CAA0C,GAA1C,IAA2B,CAAEM,aAAa,CAAA,IAC1C,CAAA,CAAA,IAAiC,GAAjC,IAAI,CAACxB,QAAQ,CAAC,CAACO,OAAO,CAACM,UAAU,SAAM,GAAvC,KAAA,CAAuC,GAAvC,QAAA,IAAiC,CAAEO,IAAI,SAAA,GAAvC,KAAA,CAAuC,QAAEI,aAAa,AAAf,CAAA,AAAe;QAExD,IAAI,CAACxB,QAAQ,CAAC,CAACb,GAAG,CAACyB,QAAQ,GAAGF,YAAY,CAACE,QAAQ;QACnD,IAAI,CAACZ,QAAQ,CAAC,CAACwB,aAAa,GAAGA,aAAa;YAClBd,SAAqB;QAA/C,IAAI,CAACV,QAAQ,CAAC,CAACQ,QAAQ,GAAGE,CAAAA,SAAqB,GAArBA,YAAY,CAACF,QAAQ,YAArBE,SAAqB,GAAI,EAAE;QACrD,IAAI,CAACV,QAAQ,CAAC,CAACyB,OAAO,GAAGf,YAAY,CAACe,OAAO;YACrBf,OAAmB;QAA3C,IAAI,CAACV,QAAQ,CAAC,CAAC0B,MAAM,GAAGhB,CAAAA,OAAmB,GAAnBA,YAAY,CAACgB,MAAM,YAAnBhB,OAAmB,GAAIc,aAAa;QAC5D,IAAI,CAACxB,QAAQ,CAAC,CAAC2B,aAAa,GAAGjB,YAAY,CAACiB,aAAa;QACzD,IAAI,CAAC3B,QAAQ,CAAC,CAACN,sBAAsB,GAAGF,qBAAqB,CAC3D,IAAI,CAACQ,QAAQ,CAAC,CAACb,GAAG,CAACM,YAAY,CAChC;KACF;IAED,AAAQmC,cAAc,GAAG;QACvB,OAAOC,CAAAA,GAAAA,uBAAsB,AAS3B,CAAA,uBAT2B,CAAC;YAC5BrB,QAAQ,EAAE,IAAI,CAACR,QAAQ,CAAC,CAACQ,QAAQ;YACjCiB,OAAO,EAAE,IAAI,CAACzB,QAAQ,CAAC,CAACyB,OAAO;YAC/BD,aAAa,EAAE,CAAC,IAAI,CAACxB,QAAQ,CAAC,CAACO,OAAO,CAACuB,WAAW,GAC9C,IAAI,CAAC9B,QAAQ,CAAC,CAACwB,aAAa,GAC5BzB,SAAS;YACb2B,MAAM,EAAE,IAAI,CAAC1B,QAAQ,CAAC,CAAC0B,MAAM;YAC7Bd,QAAQ,EAAE,IAAI,CAACZ,QAAQ,CAAC,CAACb,GAAG,CAACyB,QAAQ;YACrCe,aAAa,EAAE,IAAI,CAAC3B,QAAQ,CAAC,CAAC2B,aAAa;SAC5C,CAAC,CAAA;KACH;IAED,AAAQI,YAAY,GAAG;QACrB,MAAMrC,sBAAsB,GAAG,IAAI,CAACM,QAAQ,CAAC,CAACN,sBAAsB;QACpE,mEAAmE;QACnE,6FAA6F;QAC7F,IAAI,CAACA,sBAAsB,EAAE;YAC3B,OAAO,IAAI,CAACM,QAAQ,CAAC,CAACb,GAAG,CAAC6C,MAAM,CAAA;SACjC;QAED,wFAAwF;QACxF,MAAMvC,YAAY,GAAG,IAAIwC,eAAe,CAAC,IAAI,CAACjC,QAAQ,CAAC,CAACb,GAAG,CAACM,YAAY,CAAC;QACzE,+EAA+E;QAC/E,IAAK,MAAMG,IAAI,IAAIF,sBAAsB,CAAE;YACzCD,YAAY,CAACyC,GAAG,CAACtC,IAAI,EAAEF,sBAAsB,CAACE,IAAI,CAAC,CAAC;SACrD;QAED,MAAMuC,MAAM,GAAG1C,YAAY,CAAC2C,QAAQ,EAAE;QACtC,OAAOD,MAAM,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAA;KACzC;IAED,IAAWV,OAAO,GAAG;QACnB,OAAO,IAAI,CAACzB,QAAQ,CAAC,CAACyB,OAAO,CAAA;KAC9B;IAED,IAAWA,OAAO,CAACA,OAA2B,EAAE;QAC9C,IAAI,CAACzB,QAAQ,CAAC,CAACyB,OAAO,GAAGA,OAAO;KACjC;IAED,IAAW/B,sBAAsB,GAAG;QAClC,OAAO,IAAI,CAACM,QAAQ,CAAC,CAACN,sBAAsB,CAAA;KAC7C;IAED,IAAWA,sBAAsB,CAC/B2C,kBAAsD,EACtD;QACA,IAAIA,kBAAkB,EAAE;YACtB,KAAK,MAAMzC,IAAI,IAAIZ,iBAAiB,CAAE;gBACpC,0CAA0C;gBAC1C,IAAIqD,kBAAkB,CAACzC,IAAI,CAAC,EAAE;oBAC5B,IAAI,CAACI,QAAQ,CAAC,CAACb,GAAG,CAACM,YAAY,CAACyC,GAAG,CAACtC,IAAI,EAAEyC,kBAAkB,CAACzC,IAAI,CAAC,CAAC;iBACpE,MAAM;oBACL,4EAA4E;oBAC5E,IAAI,CAACI,QAAQ,CAAC,CAACb,GAAG,CAACM,YAAY,CAAC6C,MAAM,CAAC1C,IAAI,CAAC;iBAC7C;aACF;SACF,MAAM;YACL,KAAK,MAAMA,IAAI,IAAIZ,iBAAiB,CAAE;gBACpC,IAAI,CAACgB,QAAQ,CAAC,CAACb,GAAG,CAACM,YAAY,CAAC6C,MAAM,CAAC1C,IAAI,CAAC;aAC7C;SACF;QAED,IAAI,CAACI,QAAQ,CAAC,CAACN,sBAAsB,GAAG2C,kBAAkB;KAC3D;IAED,IAAWX,MAAM,GAAG;YACX,OAAqB;QAA5B,OAAO,CAAA,OAAqB,GAArB,IAAI,CAAC1B,QAAQ,CAAC,CAAC0B,MAAM,YAArB,OAAqB,GAAI,EAAE,CAAA;KACnC;IAED,IAAWA,MAAM,CAACA,MAAc,EAAE;YAG7B,GAAiC;QAFpC,IACE,CAAC,IAAI,CAAC1B,QAAQ,CAAC,CAAC0B,MAAM,IACtB,CAAC,CAAA,CAAA,GAAiC,GAAjC,IAAI,CAAC1B,QAAQ,CAAC,CAACO,OAAO,CAACM,UAAU,SAAM,GAAvC,KAAA,CAAuC,GAAvC,QAAA,GAAiC,CAAEO,IAAI,SAAA,GAAvC,KAAA,CAAuC,GAAvC,KAAyCmB,OAAO,CAACC,QAAQ,CAACd,MAAM,CAAC,CAAA,EAClE;YACA,MAAM,IAAIe,SAAS,CACjB,CAAC,8CAA8C,EAAEf,MAAM,CAAC,CAAC,CAAC,CAC3D,CAAA;SACF;QAED,IAAI,CAAC1B,QAAQ,CAAC,CAAC0B,MAAM,GAAGA,MAAM;KAC/B;IAED,IAAIF,aAAa,GAAG;QAClB,OAAO,IAAI,CAACxB,QAAQ,CAAC,CAACwB,aAAa,CAAA;KACpC;IAED,IAAIN,YAAY,GAAG;QACjB,OAAO,IAAI,CAAClB,QAAQ,CAAC,CAACkB,YAAY,CAAA;KACnC;IAED,IAAIzB,YAAY,GAAG;QACjB,OAAO,IAAI,CAACO,QAAQ,CAAC,CAACb,GAAG,CAACM,YAAY,CAAA;KACvC;IAED,IAAIiD,IAAI,GAAG;QACT,OAAO,IAAI,CAAC1C,QAAQ,CAAC,CAACb,GAAG,CAACuD,IAAI,CAAA;KAC/B;IAED,IAAIA,IAAI,CAAC7C,KAAa,EAAE;QACtB,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAACuD,IAAI,GAAG7C,KAAK;KAChC;IAED,IAAI8C,QAAQ,GAAG;QACb,OAAO,IAAI,CAAC3C,QAAQ,CAAC,CAACb,GAAG,CAACwD,QAAQ,CAAA;KACnC;IAED,IAAIA,QAAQ,CAAC9C,KAAa,EAAE;QAC1B,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAACwD,QAAQ,GAAG9C,KAAK;KACpC;IAED,IAAI+C,IAAI,GAAG;QACT,OAAO,IAAI,CAAC5C,QAAQ,CAAC,CAACb,GAAG,CAACyD,IAAI,CAAA;KAC/B;IAED,IAAIA,IAAI,CAAC/C,KAAa,EAAE;QACtB,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAACyD,IAAI,GAAG/C,KAAK;KAChC;IAED,IAAIgD,QAAQ,GAAG;QACb,OAAO,IAAI,CAAC7C,QAAQ,CAAC,CAACb,GAAG,CAAC0D,QAAQ,CAAA;KACnC;IAED,IAAIA,QAAQ,CAAChD,KAAa,EAAE;QAC1B,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAAC0D,QAAQ,GAAGhD,KAAK;KACpC;IAED,IAAIiD,IAAI,GAAG;QACT,MAAMlC,QAAQ,GAAG,IAAI,CAACgB,cAAc,EAAE;QACtC,MAAMI,MAAM,GAAG,IAAI,CAACD,YAAY,EAAE;QAClC,OAAO,CAAC,EAAE,IAAI,CAACc,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACH,IAAI,CAAC,EAAE9B,QAAQ,CAAC,EAAEoB,MAAM,CAAC,CAAC,CAAA;KAC5D;IAED,IAAIc,IAAI,CAAC3D,GAAW,EAAE;QACpB,IAAI,CAACa,QAAQ,CAAC,CAACb,GAAG,GAAGD,QAAQ,CAACC,GAAG,CAAC;QAClC,IAAI,CAACsB,UAAU,EAAE;KAClB;IAED,IAAIsC,MAAM,GAAG;QACX,OAAO,IAAI,CAAC/C,QAAQ,CAAC,CAACb,GAAG,CAAC4D,MAAM,CAAA;KACjC;IAED,IAAInC,QAAQ,GAAG;QACb,OAAO,IAAI,CAACZ,QAAQ,CAAC,CAACb,GAAG,CAACyB,QAAQ,CAAA;KACnC;IAED,IAAIA,QAAQ,CAACf,KAAa,EAAE;QAC1B,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAACyB,QAAQ,GAAGf,KAAK;KACpC;IAED,IAAImD,IAAI,GAAG;QACT,OAAO,IAAI,CAAChD,QAAQ,CAAC,CAACb,GAAG,CAAC6D,IAAI,CAAA;KAC/B;IAED,IAAIA,IAAI,CAACnD,KAAa,EAAE;QACtB,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAAC6D,IAAI,GAAGnD,KAAK;KAChC;IAED,IAAImC,MAAM,GAAG;QACX,OAAO,IAAI,CAAChC,QAAQ,CAAC,CAACb,GAAG,CAAC6C,MAAM,CAAA;KACjC;IAED,IAAIA,MAAM,CAACnC,KAAa,EAAE;QACxB,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAAC6C,MAAM,GAAGnC,KAAK;KAClC;IAED,IAAIoD,QAAQ,GAAG;QACb,OAAO,IAAI,CAACjD,QAAQ,CAAC,CAACb,GAAG,CAAC8D,QAAQ,CAAA;KACnC;IAED,IAAIA,QAAQ,CAACpD,KAAa,EAAE;QAC1B,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAAC8D,QAAQ,GAAGpD,KAAK;KACpC;IAED,IAAIqD,QAAQ,GAAG;QACb,OAAO,IAAI,CAAClD,QAAQ,CAAC,CAACb,GAAG,CAAC+D,QAAQ,CAAA;KACnC;IAED,IAAIA,QAAQ,CAACrD,KAAa,EAAE;QAC1B,IAAI,CAACG,QAAQ,CAAC,CAACb,GAAG,CAAC+D,QAAQ,GAAGrD,KAAK;KACpC;IAED,IAAIW,QAAQ,GAAG;QACb,OAAO,IAAI,CAACR,QAAQ,CAAC,CAACQ,QAAQ,CAAA;KAC/B;IAED,IAAIA,QAAQ,CAACX,KAAa,EAAE;QAC1B,IAAI,CAACG,QAAQ,CAAC,CAACQ,QAAQ,GAAGX,KAAK,CAACsD,UAAU,CAAC,GAAG,CAAC,GAAGtD,KAAK,GAAG,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC;KACtE;IAEDuC,QAAQ,GAAG;QACT,OAAO,IAAI,CAACU,IAAI,CAAA;KACjB;IAEDM,MAAM,GAAG;QACP,OAAO,IAAI,CAACN,IAAI,CAAA;KACjB;IAED,CAAC7C,MAAM,CAACoD,GAAG,CAAC,6BAA6B,CAAC,CAAC,GAAG;QAC5C,OAAO;YACLP,IAAI,EAAE,IAAI,CAACA,IAAI;YACfC,MAAM,EAAE,IAAI,CAACA,MAAM;YACnBF,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBK,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBD,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBP,IAAI,EAAE,IAAI,CAACA,IAAI;YACfC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBC,IAAI,EAAE,IAAI,CAACA,IAAI;YACfhC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBoB,MAAM,EAAE,IAAI,CAACA,MAAM;YACnBvC,YAAY,EAAE,IAAI,CAACA,YAAY;YAC/BuD,IAAI,EAAE,IAAI,CAACA,IAAI;SAChB,CAAA;KACF;IAEDM,KAAK,GAAG;QACN,OAAO,IAAIpD,OAAO,CAACZ,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAACU,QAAQ,CAAC,CAACO,OAAO,CAAC,CAAA;KACzD;CACF;QA9RYL,OAAO,GAAPA,OAAO"}