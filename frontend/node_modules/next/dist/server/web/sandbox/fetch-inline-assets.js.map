{"version": 3, "sources": ["../../../../server/web/sandbox/fetch-inline-assets.ts"], "names": ["fetchInlineAsset", "options", "inputString", "String", "input", "startsWith", "hash", "replace", "asset", "assets", "find", "x", "name", "filePath", "resolve", "distDir", "fileIsReadable", "fs", "access", "then", "readStream", "createReadStream", "context", "Response", "requestToBodyStream"], "mappings": "AAAA;;;;QAUsBA,gBAAgB,GAAhBA,gBAAgB;AATW,IAAA,GAAI,WAAJ,IAAI,CAAA;AACjB,IAAA,YAAoB,WAApB,oBAAoB,CAAA;AAChC,IAAA,KAAM,WAAN,MAAM,CAAA;AAOvB,eAAeA,gBAAgB,CAACC,OAKtC,EAAiC;QAOlBA,GAAc;IAN5B,MAAMC,WAAW,GAAGC,MAAM,CAACF,OAAO,CAACG,KAAK,CAAC;IACzC,IAAI,CAACF,WAAW,CAACG,UAAU,CAAC,OAAO,CAAC,EAAE;QACpC,OAAM;KACP;IAED,MAAMC,IAAI,GAAGJ,WAAW,CAACK,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAC7C,MAAMC,KAAK,GAAGP,CAAAA,GAAc,GAAdA,OAAO,CAACQ,MAAM,SAAM,GAApBR,KAAAA,CAAoB,GAApBA,GAAc,CAAES,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,IAAI,KAAKN,IAAI,CAAC;IAC1D,IAAI,CAACE,KAAK,EAAE;QACV,OAAM;KACP;IAED,MAAMK,QAAQ,GAAGC,CAAAA,GAAAA,KAAO,AAAiC,CAAA,QAAjC,CAACb,OAAO,CAACc,OAAO,EAAEP,KAAK,CAACK,QAAQ,CAAC;IACzD,MAAMG,cAAc,GAAG,MAAMC,GAAE,SAAA,CAACC,MAAM,CAACL,QAAQ,CAAC,CAACM,IAAI,CACnD,IAAM,IAAI,EACV,IAAM,KAAK,CACZ;IAED,IAAIH,cAAc,EAAE;QAClB,MAAMI,UAAU,GAAGC,CAAAA,GAAAA,GAAgB,AAAU,CAAA,iBAAV,CAACR,QAAQ,CAAC;QAC7C,OAAO,IAAIZ,OAAO,CAACqB,OAAO,CAACC,QAAQ,CACjCC,CAAAA,GAAAA,YAAmB,AAA6B,CAAA,oBAA7B,CAACvB,OAAO,CAACqB,OAAO,EAAEF,UAAU,CAAC,CACjD,CAAA;KACF;CACF"}