{"version": 3, "sources": ["../../../../server/web/sandbox/context.ts"], "names": ["clearModuleContext", "getModuleContext", "WEBPACK_HASH_REGEX", "moduleContexts", "Map", "path", "content", "key", "cache", "prev", "paths", "get", "replace", "toString", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "fs", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "keys", "pairs", "process", "env", "Object", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "error", "Error", "decorateServerError", "COMPILER_NAMES", "edgeServer", "createProcessPolyfill", "options", "processPolyfill", "overridenValue", "defineProperty", "undefined", "set", "value", "enumerable", "addStub", "context", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "createModuleContext", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "EdgeRuntime", "codeGeneration", "NODE_ENV", "strings", "extend", "__next_eval__", "fn", "has", "warning", "getServerError", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "assetResponse", "fetchInlineAsset", "assets", "distDir", "headers", "Headers", "prevs", "split", "concat", "moduleName", "join", "url", "pick", "String", "__Request", "Request", "constructor", "validateURL", "__redirect", "Response", "redirect", "bind", "args", "EDGE_UNSUPPORTED_NODE_APIS", "assign", "decorateUnhandledError", "addEventListener", "pendingModuleCaches", "getModuleContextShared", "deferredModuleContext", "moduleContext", "useCache", "evaluateInContext", "filepath", "readFileSync"], "mappings": "AAAA;;;;QAqCgBA,kBAAkB,GAAlBA,kBAAkB;QA2RZC,gBAAgB,GAAhBA,gBAAgB;AA5T/B,IAAA,WAA4D,WAA5D,4DAA4D,CAAA;AAI5D,IAAA,UAA+B,WAA/B,+BAA+B,CAAA;AACV,IAAA,YAAiC,WAAjC,iCAAiC,CAAA;AAChB,IAAA,GAAI,WAAJ,IAAI,CAAA;AACrB,IAAA,MAAU,WAAV,UAAU,CAAA;AACjB,IAAA,KAAmB,WAAnB,mBAAmB,CAAA;AACP,IAAA,kBAAuB,WAAvB,uBAAuB,CAAA;AAGxD,MAAMC,kBAAkB,qEAC4C;AAQpE;;;;GAIG,CACH,MAAMC,cAAc,GAAG,IAAIC,GAAG,EAAyB;AAOhD,SAASJ,kBAAkB,CAACK,IAAY,EAAEC,OAAwB,EAAE;IACzE,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIL,cAAc,CAAE;YAC5BK,GAAsB;QAAnC,MAAMC,IAAI,GAAGD,CAAAA,GAAsB,GAAtBA,KAAK,QAAO,GAAZA,KAAAA,CAAY,GAAZA,KAAK,CAAEE,KAAK,CAACC,GAAG,CAACN,IAAI,CAAC,SAAS,GAA/BG,KAAAA,CAA+B,GAA/BA,GAAsB,CAAEI,OAAO,CAACV,kBAAkB,EAAE,EAAE,CAAC;QACpE,IACE,OAAOO,IAAI,KAAK,WAAW,IAC3BA,IAAI,KAAKH,OAAO,CAACO,QAAQ,EAAE,CAACD,OAAO,CAACV,kBAAkB,EAAE,EAAE,CAAC,EAC3D;YACAC,cAAc,CAACW,MAAM,CAACP,GAAG,CAAC;SAC3B;KACF;CACF;AAED,eAAeQ,QAAQ,CACrBC,IAAoB,EACyB;IAC7C,MAAMC,OAAO,GAAuC,EAAE;IAEtD,MAAMC,OAAO,CAACC,GAAG,CACfH,IAAI,CAACI,GAAG,CAAC,OAAOC,OAAO,GAAK;QAC1B,MAAMC,MAAM,GAAG,MAAMC,WAAW,CAACC,OAAO,CACtC,MAAMC,GAAE,SAAA,CAACC,QAAQ,CAACL,OAAO,CAACM,QAAQ,CAAC,CACpC;QACDV,OAAO,CAACI,OAAO,CAACO,IAAI,CAAC,GAAGN,MAAM;KAC/B,CAAC,CACH;IAED,OAAOL,OAAO,CAAA;CACf;AAED,SAASY,6BAA6B,CACpCC,IAAc,EACsB;IACpC,MAAMC,KAAK,GAAGD,IAAI,CAACV,GAAG,CAAC,CAACb,GAAG,GAAK;YAACA,GAAG;YAAEyB,OAAO,CAACC,GAAG,CAAC1B,GAAG,CAAC;SAAC,CAAC;IACxD,MAAM0B,GAAG,GAAGC,MAAM,CAACC,WAAW,CAACJ,KAAK,CAAC;IACrCE,GAAG,CAACG,YAAY,GAAG,MAAM;IACzB,OAAOH,GAAG,CAAA;CACX;AAED,SAASI,wBAAwB,CAACT,IAAY,EAAE;IAC9C,MAAMU,KAAK,GACT,IAAIC,KAAK,CAAC,CAAC,uBAAuB,EAAEX,IAAI,CAAC;8DACiB,CAAC,CAAC;IAC9DY,CAAAA,GAAAA,WAAmB,AAAkC,CAAA,oBAAlC,CAACF,KAAK,EAAEG,UAAc,eAAA,CAACC,UAAU,CAAC;IACrD,MAAMJ,KAAK,CAAA;CACZ;AAED,SAASK,qBAAqB,CAACC,OAA0C,EAAE;IACzE,MAAMX,GAAG,GAAGJ,6BAA6B,CAACe,OAAO,CAACX,GAAG,CAAC;IAEtD,MAAMY,eAAe,GAAG;QAAEZ,GAAG;KAAE;IAC/B,MAAMa,cAAc,GAAwB,EAAE;IAC9C,KAAK,MAAMvC,GAAG,IAAI2B,MAAM,CAACJ,IAAI,CAACE,OAAO,CAAC,CAAE;QACtC,IAAIzB,GAAG,KAAK,KAAK,EAAE,SAAQ;QAC3B2B,MAAM,CAACa,cAAc,CAACF,eAAe,EAAEtC,GAAG,EAAE;YAC1CI,GAAG,IAAG;gBACJ,IAAImC,cAAc,CAACvC,GAAG,CAAC,EAAE;oBACvB,OAAOuC,cAAc,CAACvC,GAAG,CAAC,CAAA;iBAC3B;gBACD,IAAI,OAAO,AAACyB,OAAO,AAAQ,CAACzB,GAAG,CAAC,KAAK,UAAU,EAAE;oBAC/C,OAAO,IAAM8B,wBAAwB,CAAC,CAAC,QAAQ,EAAE9B,GAAG,CAAC,CAAC,CAAC,CAAA;iBACxD;gBACD,OAAOyC,SAAS,CAAA;aACjB;YACDC,GAAG,EAACC,KAAK,EAAE;gBACTJ,cAAc,CAACvC,GAAG,CAAC,GAAG2C,KAAK;aAC5B;YACDC,UAAU,EAAE,KAAK;SAClB,CAAC;KACH;IACD,OAAON,eAAe,CAAA;CACvB;AAED,SAASO,OAAO,CAACC,OAA+B,EAAEzB,IAAY,EAAE;IAC9DM,MAAM,CAACa,cAAc,CAACM,OAAO,EAAEzB,IAAI,EAAE;QACnCjB,GAAG,IAAG;YACJ,OAAO,WAAY;gBACjB0B,wBAAwB,CAACT,IAAI,CAAC;aAC/B,CAAA;SACF;QACDuB,UAAU,EAAE,KAAK;KAClB,CAAC;CACH;AAED,SAASG,yBAAyB,CAACC,OAAoB,EAAE;IACvD,MAAMC,gBAAgB,GAAGD,OAAO,CAACE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;IAClD,OAAO,CAACnB,KAAU,GAAK;QACrB,IAAIA,KAAK,YAAYkB,gBAAgB,EAAE;YACrChB,CAAAA,GAAAA,WAAmB,AAAkC,CAAA,oBAAlC,CAACF,KAAK,EAAEG,UAAc,eAAA,CAACC,UAAU,CAAC;SACtD;KACF,CAAA;CACF;AAED;;;GAGG,CACH,eAAegB,mBAAmB,CAACd,OAA6B,EAAE;IAChE,MAAMe,WAAW,GAAG,IAAIC,GAAG,EAAU;IACrC,MAAMC,kBAAkB,GAAG,IAAID,GAAG,EAAU;QAChBhB,KAA8B;IAA1D,MAAM5B,IAAI,GAAG,MAAMD,QAAQ,CAAC6B,CAAAA,KAA8B,GAA9BA,OAAO,CAACkB,iBAAiB,CAAC9C,IAAI,YAA9B4B,KAA8B,GAAI,EAAE,CAAC;IACjE,MAAMW,OAAO,GAAG,IAAIQ,YAAW,YAAA,CAAC;QAC9BC,cAAc,EACZhC,OAAO,CAACC,GAAG,CAACgC,QAAQ,KAAK,YAAY,GACjC;YAAEC,OAAO,EAAE,IAAI;YAAElD,IAAI,EAAE,IAAI;SAAE,GAC7BgC,SAAS;QACfmB,MAAM,EAAE,CAACd,OAAO,GAAK;YACnBA,OAAO,CAACrB,OAAO,GAAGW,qBAAqB,CAACC,OAAO,CAAC;YAEhDS,OAAO,CAACe,aAAa,GAAG,SAASA,aAAa,CAACC,EAAY,EAAE;gBAC3D,MAAM9D,GAAG,GAAG8D,EAAE,CAACxD,QAAQ,EAAE;gBACzB,IAAI,CAAC8C,WAAW,CAACW,GAAG,CAAC/D,GAAG,CAAC,EAAE;oBACzB,MAAMgE,OAAO,GAAGC,CAAAA,GAAAA,WAAc,AAM7B,CAAA,eAN6B,CAC5B,IAAIjC,KAAK,CACP,CAAC;yEAC0D,CAAC,CAC7D,EACDE,UAAc,eAAA,CAACC,UAAU,CAC1B;oBACD6B,OAAO,CAAC3C,IAAI,GAAG,8BAA8B;oBAC7CW,KAAK,CAACkC,iBAAiB,CAACF,OAAO,EAAEH,aAAa,CAAC;oBAC/CT,WAAW,CAACe,GAAG,CAACnE,GAAG,CAAC;oBACpBqC,OAAO,CAAC+B,SAAS,CAACJ,OAAO,CAAC;iBAC3B;gBACD,OAAOF,EAAE,EAAE,CAAA;aACZ;YAEDhB,OAAO,CAACuB,4BAA4B,GAClC,SAASA,4BAA4B,CAACP,EAAY,EAAE;gBAClD,MAAM9D,GAAG,GAAG8D,EAAE,CAACxD,QAAQ,EAAE;gBACzB,IAAI,CAACgD,kBAAkB,CAACS,GAAG,CAAC/D,GAAG,CAAC,EAAE;oBAChC,MAAMgE,OAAO,GAAGC,CAAAA,GAAAA,WAAc,AAI7B,CAAA,eAJ6B,CAC5B,IAAIjC,KAAK,CAAC,CAAC;yEACgD,CAAC,CAAC,EAC7DE,UAAc,eAAA,CAACC,UAAU,CAC1B;oBACD6B,OAAO,CAAC3C,IAAI,GAAG,kCAAkC;oBACjDW,KAAK,CAACkC,iBAAiB,CAACF,OAAO,EAAEK,4BAA4B,CAAC;oBAC9Df,kBAAkB,CAACa,GAAG,CAACnE,GAAG,CAAC;oBAC3BqC,OAAO,CAAC+B,SAAS,CAACJ,OAAO,CAAC;iBAC3B;gBACD,OAAOF,EAAE,EAAE,CAAA;aACZ;YAEHhB,OAAO,CAACwB,gCAAgC,GACtC,eAAeA,gCAAgC,CAACR,EAAY,EAAE;gBAC5D,MAAMS,MAAM,GAAG,MAAMT,EAAE,EAAE;gBAEzB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,8JAA8J;gBAC9J,MAAMU,sBAAsB,GAAGD,MAAM,CAACE,cAAc,CAAC,QAAQ,CAAC;gBAE9D,MAAMzE,GAAG,GAAG8D,EAAE,CAACxD,QAAQ,EAAE;gBACzB,IAAIkE,sBAAsB,IAAI,CAAClB,kBAAkB,CAACS,GAAG,CAAC/D,GAAG,CAAC,EAAE;oBAC1D,MAAMgE,OAAO,GAAGC,CAAAA,GAAAA,WAAc,AAI7B,CAAA,eAJ6B,CAC5B,IAAIjC,KAAK,CAAC,CAAC;yEACgD,CAAC,CAAC,EAC7DE,UAAc,eAAA,CAACC,UAAU,CAC1B;oBACD6B,OAAO,CAAC3C,IAAI,GAAG,kCAAkC;oBACjDW,KAAK,CAACkC,iBAAiB,CAACF,OAAO,EAAEM,gCAAgC,CAAC;oBAClEhB,kBAAkB,CAACa,GAAG,CAACnE,GAAG,CAAC;oBAC3BqC,OAAO,CAAC+B,SAAS,CAACJ,OAAO,CAAC;iBAC3B;gBACD,OAAOO,MAAM,CAAA;aACd;YAEH,MAAMG,OAAO,GAAG5B,OAAO,CAAC6B,KAAK;YAC7B7B,OAAO,CAAC6B,KAAK,GAAG,OAAOC,KAAK,EAAEC,IAAI,GAAG,EAAE,GAAK;oBAaxCA,GAA2C;gBAZ7C,MAAMC,aAAa,GAAG,MAAMC,CAAAA,GAAAA,kBAAgB,AAK1C,CAAA,iBAL0C,CAAC;oBAC3CH,KAAK;oBACLI,MAAM,EAAE3C,OAAO,CAACkB,iBAAiB,CAACyB,MAAM;oBACxCC,OAAO,EAAE5C,OAAO,CAAC4C,OAAO;oBACxBnC,OAAO;iBACR,CAAC;gBACF,IAAIgC,aAAa,EAAE;oBACjB,OAAOA,aAAa,CAAA;iBACrB;oBAE0BD,QAAY;gBAAvCA,IAAI,CAACK,OAAO,GAAG,IAAIC,OAAO,CAACN,CAAAA,QAAY,GAAZA,IAAI,CAACK,OAAO,YAAZL,QAAY,GAAI,EAAE,CAAC;gBAC9C,MAAMO,KAAK,GACTP,CAAAA,CAAAA,GAA2C,GAA3CA,IAAI,CAACK,OAAO,CAAC9E,GAAG,CAAC,CAAC,uBAAuB,CAAC,CAAC,SAAO,GAAlDyE,KAAAA,CAAkD,GAAlDA,GAA2C,CAAEQ,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE;gBAC/D,MAAM1C,KAAK,GAAGyC,KAAK,CAACE,MAAM,CAACjD,OAAO,CAACkD,UAAU,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;gBACxDX,IAAI,CAACK,OAAO,CAACxC,GAAG,CAAC,yBAAyB,EAAEC,KAAK,CAAC;gBAElD,IAAI,CAACkC,IAAI,CAACK,OAAO,CAACnB,GAAG,CAAC,YAAY,CAAC,EAAE;oBACnCc,IAAI,CAACK,OAAO,CAACxC,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC;iBACrD;gBAED,IAAI,OAAOkC,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAIA,KAAK,EAAE;oBAC/C,OAAOF,OAAO,CAACE,KAAK,CAACa,GAAG,EAAE;wBACxB,GAAGC,CAAAA,GAAAA,KAAI,AAYL,CAAA,KAZK,CAACd,KAAK,EAAE;4BACb,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;4BACX,WAAW;4BACX,MAAM;4BACN,UAAU;4BACV,UAAU;4BACV,gBAAgB;4BAChB,QAAQ;yBACT,CAAC;wBACF,GAAGC,IAAI;wBACPK,OAAO,EAAE;4BACP,GAAGvD,MAAM,CAACC,WAAW,CAACgD,KAAK,CAACM,OAAO,CAAC;4BACpC,GAAGvD,MAAM,CAACC,WAAW,CAACiD,IAAI,CAACK,OAAO,CAAC;yBACpC;qBACF,CAAC,CAAA;iBACH;gBAED,OAAOR,OAAO,CAACiB,MAAM,CAACf,KAAK,CAAC,EAAEC,IAAI,CAAC,CAAA;aACpC;YAED,MAAMe,SAAS,GAAG9C,OAAO,CAAC+C,OAAO;YACjC/C,OAAO,CAAC+C,OAAO,GAAG,cAAcD,SAAS;gBACvCE,YAAYlB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMY,GAAG,GACP,OAAOb,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAIA,KAAK,GACvCA,KAAK,CAACa,GAAG,GACTE,MAAM,CAACf,KAAK,CAAC;oBACnBmB,CAAAA,GAAAA,MAAW,AAAK,CAAA,YAAL,CAACN,GAAG,CAAC;oBAChB,KAAK,CAACA,GAAG,EAAEZ,IAAI,CAAC;iBACjB;aACF;YAED,MAAMmB,UAAU,GAAGlD,OAAO,CAACmD,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACrD,OAAO,CAACmD,QAAQ,CAAC;YACnEnD,OAAO,CAACmD,QAAQ,CAACC,QAAQ,GAAG,CAAIE,GAAAA,IAAI,GAAK;gBACvCL,CAAAA,GAAAA,MAAW,AAAS,CAAA,YAAT,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAOJ,UAAU,IAAII,IAAI,CAAC,CAAA;aAC3B;YAED,KAAK,MAAM/E,IAAI,IAAIgF,UAA0B,2BAAA,CAAE;gBAC7CxD,OAAO,CAACC,OAAO,EAAEzB,IAAI,CAAC;aACvB;YAEDM,MAAM,CAAC2E,MAAM,CAACxD,OAAO,EAAErC,IAAI,CAAC;YAE5B,OAAOqC,OAAO,CAAA;SACf;KACF,CAAC;IAEF,MAAMyD,sBAAsB,GAAGxD,yBAAyB,CAACC,OAAO,CAAC;IACjEA,OAAO,CAACF,OAAO,CAAC0D,gBAAgB,CAAC,oBAAoB,EAAED,sBAAsB,CAAC;IAC9EvD,OAAO,CAACF,OAAO,CAAC0D,gBAAgB,CAAC,OAAO,EAAED,sBAAsB,CAAC;IAEjE,OAAO;QACLvD,OAAO;QACP7C,KAAK,EAAE,IAAIN,GAAG,EAAkB;QAChCuD,WAAW,EAAE,IAAIC,GAAG,EAAU;KAC/B,CAAA;CACF;AAWD,MAAMoD,mBAAmB,GAAG,IAAI5G,GAAG,EAAkC;AAErE,SAAS6G,sBAAsB,CAACrE,OAA6B,EAAE;IAC7D,IAAIsE,qBAAqB,GAAGF,mBAAmB,CAACrG,GAAG,CAACiC,OAAO,CAACkD,UAAU,CAAC;IACvE,IAAI,CAACoB,qBAAqB,EAAE;QAC1BA,qBAAqB,GAAGxD,mBAAmB,CAACd,OAAO,CAAC;QACpDoE,mBAAmB,CAAC/D,GAAG,CAACL,OAAO,CAACkD,UAAU,EAAEoB,qBAAqB,CAAC;KACnE;IACD,OAAOA,qBAAqB,CAAA;CAC7B;AAQM,eAAejH,gBAAgB,CAAC2C,OAA6B,EAKjE;IACD,IAAIuE,aAAa,GAAGvE,OAAO,CAACwE,QAAQ,GAChCjH,cAAc,CAACQ,GAAG,CAACiC,OAAO,CAACkD,UAAU,CAAC,GACtC,MAAMmB,sBAAsB,CAACrE,OAAO,CAAC;IAEzC,IAAI,CAACuE,aAAa,EAAE;QAClBA,aAAa,GAAG,MAAMzD,mBAAmB,CAACd,OAAO,CAAC;QAClDzC,cAAc,CAAC8C,GAAG,CAACL,OAAO,CAACkD,UAAU,EAAEqB,aAAa,CAAC;KACtD;IAED,MAAME,iBAAiB,GAAG,CAACC,QAAgB,GAAK;QAC9C,IAAI,CAACH,aAAa,CAAEzG,KAAK,CAAC4D,GAAG,CAACgD,QAAQ,CAAC,EAAE;YACvC,MAAMhH,OAAO,GAAGiH,CAAAA,GAAAA,GAAY,AAAmB,CAAA,aAAnB,CAACD,QAAQ,EAAE,OAAO,CAAC;YAC/C,IAAI;gBACFH,aAAa,QAAS,GAAtBA,KAAAA,CAAsB,GAAtBA,aAAa,CAAE5D,OAAO,CAACE,QAAQ,CAACnD,OAAO,CAAC;gBACxC6G,aAAa,CAAEzG,KAAK,CAACuC,GAAG,CAACqE,QAAQ,EAAEhH,OAAO,CAAC;aAC5C,CAAC,OAAOgC,KAAK,EAAE;gBACd,IAAIM,OAAO,CAACwE,QAAQ,EAAE;oBACpBD,aAAa,QAAO,GAApBA,KAAAA,CAAoB,GAApBA,aAAa,CAAEzG,KAAK,CAACI,MAAM,CAAC8B,OAAO,CAACkD,UAAU,CAAC;iBAChD;gBACD,MAAMxD,KAAK,CAAA;aACZ;SACF;KACF;IAED,OAAO;QAAE,GAAG6E,aAAa;QAAEE,iBAAiB;KAAE,CAAA;CAC/C"}