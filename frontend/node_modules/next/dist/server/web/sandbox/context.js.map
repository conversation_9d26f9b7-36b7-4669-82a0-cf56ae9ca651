{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "names": ["clearAllModuleContexts", "clearModuleContext", "getModuleContext", "requestStore", "getServerError", "decorateServerError", "process", "env", "NODE_ENV", "middleware", "require", "error", "_", "__", "moduleContexts", "Map", "pendingModuleCaches", "intervalsManager", "removeAll", "timeouts<PERSON><PERSON><PERSON>", "clear", "path", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "fs", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "pairs", "Object", "keys", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "Error", "COMPILER_NAMES", "edgeServer", "createProcessPolyfill", "processPolyfill", "overridenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "pick", "BufferImplementation", "EventsImplementation", "AsyncHooksImplementation", "AssertImplementation", "UtilImplementation", "entries", "AsyncLocalStorage", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "EdgeRuntime", "codeGeneration", "strings", "extend", "id", "TypeError", "__next_log_error__", "err", "onError", "__next_eval__", "fn", "toString", "warning", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "fetchInlineAsset", "assets", "distDir", "headers", "Headers", "store", "getStore", "prevs", "split", "concat", "moduleName", "join", "response", "url", "String", "catch", "message", "stack", "__Request", "Request", "constructor", "validateURL", "next", "__redirect", "Response", "redirect", "bind", "args", "EDGE_UNSUPPORTED_NODE_APIS", "assign", "performance", "setInterval", "clearInterval", "interval", "remove", "setTimeout", "clearTimeout", "timeout", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "getModuleContextShared", "deferredModuleContext", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "readFileSync", "runInContext", "filename"], "mappings": ";;;;;;;;;;;;;;;;;IAuDsBA,sBAAsB;eAAtBA;;IAeAC,kBAAkB;eAAlBA;;IAoaAC,gBAAgB;eAAhBA;;IA/PTC,YAAY;eAAZA;;;6BArOqB;2BAI3B;6BACqB;oBACiB;uBACjB;sBACP;mCACY;oBACJ;mEACI;mEACA;mEACA;iEACF;wEACM;kCACa;;;;;;AAQlD,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,aAAaC,QAAQ;IAC3BN,iBAAiBK,WAAWL,cAAc;IAC1CC,sBACEK,QAAQ,oCAAoCL,mBAAmB;AACnE,OAAO;IACLD,iBAAiB,CAACO,OAAcC,IAAcD;IAC9CN,sBAAsB,CAACO,GAAUC,MAAgB;AACnD;AAEA;;;;CAIC,GACD,MAAMC,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAKzB,eAAef;IACpBiB,kCAAgB,CAACC,SAAS;IAC1BC,iCAAe,CAACD,SAAS;IACzBJ,eAAeM,KAAK;IACpBJ,oBAAoBI,KAAK;AAC3B;AAUO,eAAenB,mBAAmBoB,IAAY;IACnDJ,kCAAgB,CAACC,SAAS;IAC1BC,iCAAe,CAACD,SAAS;IAEzB,MAAMI,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACN,OAAO;YAC1BI,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIV,eAAgB;QACzCQ,cAAcC,KAAKC,OAAOV;IAC5B;IACA,KAAK,MAAM,CAACS,KAAKC,MAAM,IAAIR,oBAAqB;QAC9CM,cAAcC,KAAK,MAAMC,OAAOR;IAClC;AACF;AAEA,eAAea,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,UAAS,MAAMC,YAAYC,OAAO,CACtC,MAAMC,YAAE,CAACC,QAAQ,CAACL,QAAQM,QAAQ;QAEpCV,OAAO,CAACI,QAAQO,IAAI,CAAC,GAAGN;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASY;IACP,MAAMC,QAAQC,OAAOC,IAAI,CAACxC,QAAQC,GAAG,EAAE2B,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKjB,QAAQC,GAAG,CAACgB,IAAI;SAAC;IAC3E,MAAMhB,MAAMsC,OAAOE,WAAW,CAACH;IAC/BrC,IAAIyC,YAAY,GAAG;IACnB,OAAOzC;AACT;AAEA,SAAS0C,yBAAyBP,IAAY;IAC5C,MAAM/B,QACJ,IAAIuC,MAAM,CAAC,uBAAuB,EAAER,KAAK;8DACiB,CAAC;IAC7DrC,oBAAoBM,OAAOwC,yBAAc,CAACC,UAAU;IACpD,MAAMzC;AACR;AAEA,SAAS0C;IACP,MAAMC,kBAAkB;QAAE/C,KAAKoC;IAAgC;IAC/D,MAAMY,iBAAsC,CAAC;IAC7C,KAAK,MAAMhC,OAAOsB,OAAOC,IAAI,CAACxC,SAAU;QACtC,IAAIiB,QAAQ,OAAO;QACnBsB,OAAOW,cAAc,CAACF,iBAAiB/B,KAAK;YAC1CkC;gBACE,IAAIF,cAAc,CAAChC,IAAI,KAAKmC,WAAW;oBACrC,OAAOH,cAAc,CAAChC,IAAI;gBAC5B;gBACA,IAAI,OAAO,AAACjB,OAAe,CAACiB,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAM0B,yBAAyB,CAAC,QAAQ,EAAE1B,IAAI,CAAC;gBACxD;gBACA,OAAOmC;YACT;YACAC,KAAIC,KAAK;gBACPL,cAAc,CAAChC,IAAI,GAAGqC;YACxB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQrC,OAA+B,EAAEiB,IAAY;IAC5DG,OAAOW,cAAc,CAAC/B,SAASiB,MAAM;QACnCe;YACE,OAAO;gBACLR,yBAAyBP;YAC3B;QACF;QACAmB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACvD;QACN,IAAIA,iBAAiBsD,kBAAkB;YACrC5D,oBAAoBM,OAAOwC,yBAAc,CAACC,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/C5D,oBAAoB+D,SAASC,MAAM,EAAElB,yBAAc,CAACC,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAeC,IAAAA,UAAI,EAACC,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeD,IAAAA,UAAI,EAACE,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBF,IAAAA,UAAI,EAACG,wBAAwB,EAAE;YACjD;YACA;SACD;QACD,eAAeH,IAAAA,UAAI,EAACI,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaJ,IAAAA,UAAI,EAACK,iBAAkB,EAAE;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAI9D,IAAI8B,OAAOiC,OAAO,CAACP;AAChC,CAAA;AAEO,MAAMpE,eAAe,IAAI4E,8BAAiB;AAIjD;;;CAGC,GACD,eAAeC,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAMrD,OAAO,MAAMD,SAASoD,QAAQI,iBAAiB,CAACvD,IAAI,IAAI,EAAE;IAChE,MAAMkC,UAAU,IAAIsB,wBAAW,CAAC;QAC9BC,gBACEjF,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB;YAAEgF,SAAS;YAAM1D,MAAM;QAAK,IAC5B4B;QACN+B,QAAQ,CAAChE;YACPA,QAAQnB,OAAO,GAAG+C;YAElBR,OAAOW,cAAc,CAAC/B,SAAS,WAAW;gBACxCoC,YAAY;gBACZD,OAAO,CAAC8B;oBACN,MAAM9B,QAAQU,gBAAgBb,GAAG,CAACiC;oBAClC,IAAI,CAAC9B,OAAO;wBACV,MAAM+B,UAAU,8BAA8BD;oBAChD;oBACA,OAAO9B;gBACT;YACF;YAEA,IAAItD,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzCiB,QAAQmE,kBAAkB,GAAG,SAAUC,GAAY;oBACjDZ,QAAQa,OAAO,CAACD;gBAClB;YACF;YAEApE,QAAQsE,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAMzE,MAAMyE,GAAGC,QAAQ;gBACvB,IAAI,CAACf,YAAYvD,GAAG,CAACJ,MAAM;oBACzB,MAAM2E,UAAU9F,eACd,IAAI8C,MACF,CAAC;yEAC0D,CAAC,GAE9DC,yBAAc,CAACC,UAAU;oBAE3B8C,QAAQxD,IAAI,GAAG;oBACfQ,MAAMiD,iBAAiB,CAACD,SAASH;oBACjCb,YAAYkB,GAAG,CAAC7E;oBAChB0D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEAvE,QAAQ6E,4BAA4B,GAClC,SAASA,6BAA6BN,EAAY;gBAChD,MAAMzE,MAAMyE,GAAGC,QAAQ;gBACvB,IAAI,CAACb,mBAAmBzD,GAAG,CAACJ,MAAM;oBAChC,MAAM2E,UAAU9F,eACd,IAAI8C,MAAM,CAAC;yEACgD,CAAC,GAC5DC,yBAAc,CAACC,UAAU;oBAE3B8C,QAAQxD,IAAI,GAAG;oBACfQ,MAAMiD,iBAAiB,CAACD,SAASI;oBACjClB,mBAAmBgB,GAAG,CAAC7E;oBACvB0D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEFvE,QAAQ8E,gCAAgC,GACtC,eAAeA,iCAAiCP,EAAY;gBAC1D,MAAMQ,SAAS,MAAMR;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMS,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAMnF,MAAMyE,GAAGC,QAAQ;gBACvB,IAAIQ,0BAA0B,CAACrB,mBAAmBzD,GAAG,CAACJ,MAAM;oBAC1D,MAAM2E,UAAU9F,eACd,IAAI8C,MAAM,CAAC;yEACgD,CAAC,GAC5DC,yBAAc,CAACC,UAAU;oBAE3B8C,QAAQxD,IAAI,GAAG;oBACfQ,MAAMiD,iBAAiB,CAACD,SAASK;oBACjCnB,mBAAmBgB,GAAG,CAAC7E;oBACvB0D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOM;YACT;YAEF,MAAMG,UAAUlF,QAAQmF,KAAK;YAC7BnF,QAAQmF,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;oBA2BnCA;gBA1BF,MAAMC,eAAe,IAAI7D,MAAM;gBAC/B,MAAM8D,gBAAgB,MAAMC,IAAAA,mCAAgB,EAAC;oBAC3CJ;oBACAK,QAAQjC,QAAQI,iBAAiB,CAAC6B,MAAM;oBACxCC,SAASlC,QAAQkC,OAAO;oBACxB1F;gBACF;gBACA,IAAIuF,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKM,OAAO,GAAG,IAAIC,QAAQP,KAAKM,OAAO,IAAI,CAAC;gBAE5C,sEAAsE;gBACtE,MAAME,QAAQnH,aAAaoH,QAAQ;gBACnC,IACED,CAAAA,yBAAAA,MAAOF,OAAO,CAACzF,GAAG,CAAC,+BACnB,CAACmF,KAAKM,OAAO,CAACzF,GAAG,CAAC,4BAClB;oBACAmF,KAAKM,OAAO,CAACzD,GAAG,CACd,2BACA2D,MAAMF,OAAO,CAAC3D,GAAG,CAAC,8BAA8B;gBAEpD;gBAEA,MAAM+D,QACJV,EAAAA,oBAAAA,KAAKM,OAAO,CAAC3D,GAAG,CAAC,CAAC,uBAAuB,CAAC,sBAA1CqD,kBAA6CW,KAAK,CAAC,SAAQ,EAAE;gBAC/D,MAAM7D,QAAQ4D,MAAME,MAAM,CAACzC,QAAQ0C,UAAU,EAAEC,IAAI,CAAC;gBACpDd,KAAKM,OAAO,CAACzD,GAAG,CAAC,2BAA2BC;gBAE5C,IAAI,CAACkD,KAAKM,OAAO,CAACzF,GAAG,CAAC,eAAe;oBACnCmF,KAAKM,OAAO,CAACzD,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAMkE,WACJ,OAAOhB,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMiB,GAAG,EAAE;oBACjB,GAAGtD,IAAAA,UAAI,EAACqC,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPM,SAAS;wBACP,GAAGvE,OAAOE,WAAW,CAAC8D,MAAMO,OAAO,CAAC;wBACpC,GAAGvE,OAAOE,WAAW,CAAC+D,KAAKM,OAAO,CAAC;oBACrC;gBACF,KACAT,QAAQoB,OAAOlB,QAAQC;gBAE7B,OAAO,MAAMe,SAASG,KAAK,CAAC,CAACnC;oBAC3BkB,aAAakB,OAAO,GAAGpC,IAAIoC,OAAO;oBAClCpC,IAAIqC,KAAK,GAAGnB,aAAamB,KAAK;oBAC9B,MAAMrC;gBACR;YACF;YAEA,MAAMsC,YAAY1G,QAAQ2G,OAAO;YACjC3G,QAAQ2G,OAAO,GAAG,cAAcD;gBAE9BE,YAAYxB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMgB,MACJ,OAAOjB,UAAU,YAAY,SAASA,QAClCA,MAAMiB,GAAG,GACTC,OAAOlB;oBACbyB,IAAAA,kBAAW,EAACR;oBACZ,KAAK,CAACA,KAAKhB;oBACX,IAAI,CAACyB,IAAI,GAAGzB,wBAAAA,KAAMyB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAa/G,QAAQgH,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAClH,QAAQgH,QAAQ;YAClEhH,QAAQgH,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BN,IAAAA,kBAAW,EAACM,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAMlG,QAAQmG,qCAA0B,CAAE;gBAC7C/E,QAAQrC,SAASiB;YACnB;YAEAG,OAAOiG,MAAM,CAACrH,SAASK;YAEvBL,QAAQsH,WAAW,GAAGA;YAEtBtH,QAAQsD,iBAAiB,GAAGA,8BAAiB;YAE7C,+DAA+D;YAC/DtD,QAAQuH,WAAW,GAAG,CAAC,GAAGJ,OACxB3H,kCAAgB,CAACmF,GAAG,CAACwC;YAEvB,+DAA+D;YAC/DnH,QAAQwH,aAAa,GAAG,CAACC,WACvBjI,kCAAgB,CAACkI,MAAM,CAACD;YAE1B,+DAA+D;YAC/DzH,QAAQ2H,UAAU,GAAG,CAAC,GAAGR,OACvBzH,iCAAe,CAACiF,GAAG,CAACwC;YAEtB,+DAA+D;YAC/DnH,QAAQ4H,YAAY,GAAG,CAACC,UACtBnI,iCAAe,CAACgI,MAAM,CAACG;YAEzB,OAAO7H;QACT;IACF;IAEA,MAAM8H,yBAAyBxF,0BAA0BC;IACzDA,QAAQvC,OAAO,CAAC+H,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6BtF,8BAA8BH;IACjEA,QAAQvC,OAAO,CAAC+H,gBAAgB,CAC9B,sBACAC;IAGF,OAAO;QACLzF;QACAtC,OAAO,IAAIX;QACXmE,aAAa,IAAIC;IACnB;AACF;AAWA,SAASuE,uBAAuBzE,OAA6B;IAC3D,IAAI0E,wBAAwB3I,oBAAoByC,GAAG,CAACwB,QAAQ0C,UAAU;IACtE,IAAI,CAACgC,uBAAuB;QAC1BA,wBAAwB3E,oBAAoBC;QAC5CjE,oBAAoB2C,GAAG,CAACsB,QAAQ0C,UAAU,EAAEgC;IAC9C;IACA,OAAOA;AACT;AAQO,eAAezJ,iBAAiB+E,OAA6B;IAMlE,IAAI2E;IAIJ,IAAI3E,QAAQ4E,QAAQ,EAAE;QACpBD,oBACE9I,eAAe2C,GAAG,CAACwB,QAAQ0C,UAAU,KACpC,MAAM+B,uBAAuBzE;IAClC;IAEA,IAAI,CAAC2E,mBAAmB;QACtBA,oBAAoB,MAAM5E,oBAAoBC;QAC9CnE,eAAe6C,GAAG,CAACsB,QAAQ0C,UAAU,EAAEiC;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAcpI,KAAK,CAACC,GAAG,CAACqI,WAAW;YACtC,MAAMC,UAAUC,IAAAA,gBAAY,EAACF,UAAU;YACvC,IAAI;gBACFG,IAAAA,gBAAY,EAACF,SAASH,cAAc9F,OAAO,CAACvC,OAAO,EAAE;oBACnD2I,UAAUJ;gBACZ;gBACAF,cAAcpI,KAAK,CAACiC,GAAG,CAACqG,UAAUC;YACpC,EAAE,OAAOtJ,OAAO;gBACd,IAAIsE,QAAQ4E,QAAQ,EAAE;oBACpBC,iCAAAA,cAAepI,KAAK,CAACE,MAAM,CAACoI;gBAC9B;gBACA,MAAMrJ;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAGmJ,aAAa;QAAEC;IAAkB;AAC/C"}