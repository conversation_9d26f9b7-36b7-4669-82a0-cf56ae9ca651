{"version": 3, "sources": ["../../../../server/web/sandbox/sandbox.ts"], "names": ["ErrorSource", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "params", "then", "result", "waitUntil", "catch", "error", "getServerError", "getRuntimeContext", "runtime", "evaluateInContext", "getModuleContext", "moduleName", "name", "onWarning", "useCache", "env", "edgeFunctionEntry", "distDir", "<PERSON><PERSON><PERSON><PERSON>", "paths", "run", "subreq", "request", "headers", "subrequests", "split", "includes", "Promise", "resolve", "response", "context", "Response", "edgeFunction", "_ENTRIES", "default", "cloned", "method", "body", "cloneBodyStream", "undefined", "requestToBodyStream", "headerName", "delete", "finalize"], "mappings": "AAAA;;;;;AAC+B,IAAA,WAA4D,WAA5D,4DAA4D,CAAA;AAC1D,IAAA,QAAW,WAAX,WAAW,CAAA;AAER,IAAA,YAAoB,WAApB,oBAAoB,CAAA;AAGjD,MAAMA,WAAW,GAAGC,MAAM,CAAC,cAAc,CAAC;QAApCD,WAAW,GAAXA,WAAW;AAExB,MAAME,iBAAiB,GAAG;IACxB,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;CACpB;AAaD;;;GAGG,CACH,SAASC,gBAAgB,CAACC,EAAY,EAAY;IAChD,OAAO,CAACC,MAAM;QACZD,OAAAA,EAAE,CAACC,MAAM,CAAC,CACPC,IAAI,CAAC,CAACC,MAAM;gBAEAA,GAAiB;YAFZ,OAAC;gBACjB,GAAGA,MAAM;gBACTC,SAAS,EAAED,MAAM,QAAW,GAAjBA,KAAAA,CAAiB,GAAjBA,CAAAA,GAAiB,GAAjBA,MAAM,CAAEC,SAAS,SAAA,GAAjBD,KAAAA,CAAiB,GAAjBA,GAAiB,CAAEE,KAAK,CAAC,CAACC,KAAK,GAAK;oBAC7C,mGAAmG;oBACnG,MAAMC,CAAAA,GAAAA,WAAc,AAAsB,CAAA,eAAtB,CAACD,KAAK,EAAE,aAAa,CAAC,CAAA;iBAC3C,CAAC;aACH,CAAC;SAAA,CAAC,CACFD,KAAK,CAAC,CAACC,KAAK,GAAK;YAChB,+CAA+C;YAC/C,MAAMC,CAAAA,GAAAA,WAAc,AAAsB,CAAA,eAAtB,CAACD,KAAK,EAAE,aAAa,CAAC,CAAA;SAC3C,CAAC,CAAA;KAAA,CAAA;CACP;AAEM,MAAME,iBAAiB,GAAG,OAAOP,MAQvC,GAAgC;QAGlBA,UAAgB;IAF7B,MAAM,EAAEQ,OAAO,CAAA,EAAEC,iBAAiB,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,QAAgB,AAO3D,CAAA,iBAP2D,CAAC;QAC5DC,UAAU,EAAEX,MAAM,CAACY,IAAI;QACvBC,SAAS,EAAEb,CAAAA,UAAgB,GAAhBA,MAAM,CAACa,SAAS,YAAhBb,UAAgB,GAAK,IAAM,EAAE,AAAC;QACzCc,QAAQ,EAAEd,MAAM,CAACc,QAAQ,KAAK,KAAK;QACnCC,GAAG,EAAEf,MAAM,CAACe,GAAG;QACfC,iBAAiB,EAAEhB,MAAM,CAACgB,iBAAiB;QAC3CC,OAAO,EAAEjB,MAAM,CAACiB,OAAO;KACxB,CAAC;IAEF,KAAK,MAAMC,SAAS,IAAIlB,MAAM,CAACmB,KAAK,CAAE;QACpCV,iBAAiB,CAACS,SAAS,CAAC;KAC7B;IACD,OAAOV,OAAO,CAAA;CACf;QAtBYD,iBAAiB,GAAjBA,iBAAiB;AAwBvB,MAAMa,GAAG,GAAGtB,gBAAgB,CAAC,OAAOE,MAAM,GAAK;QAqBhDA,GAAmB;IApBvB,MAAMQ,OAAO,GAAG,MAAMD,iBAAiB,CAACP,MAAM,CAAC;IAC/C,MAAMqB,MAAM,GAAGrB,MAAM,CAACsB,OAAO,CAACC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAChE,MAAMC,WAAW,GAAG,OAAOH,MAAM,KAAK,QAAQ,GAAGA,MAAM,CAACI,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;IACvE,IAAID,WAAW,CAACE,QAAQ,CAAC1B,MAAM,CAACY,IAAI,CAAC,EAAE;QACrC,OAAO;YACLT,SAAS,EAAEwB,OAAO,CAACC,OAAO,EAAE;YAC5BC,QAAQ,EAAE,IAAIrB,OAAO,CAACsB,OAAO,CAACC,QAAQ,CAAC,IAAI,EAAE;gBAC3CR,OAAO,EAAE;oBACP,mBAAmB,EAAE,GAAG;iBACzB;aACF,CAAC;SACH,CAAA;KACF;IAED,MAAMS,YAAY,GAGhBxB,OAAO,CAACsB,OAAO,CAACG,QAAQ,CAAC,CAAC,WAAW,EAAEjC,MAAM,CAACY,IAAI,CAAC,CAAC,CAAC,CAACsB,OAAO;IAE/D,MAAMC,MAAM,GAAG,CAAC;QAAC,MAAM;QAAE,KAAK;KAAC,CAACT,QAAQ,CAAC1B,MAAM,CAACsB,OAAO,CAACc,MAAM,CAAC,GAC3DpC,CAAAA,GAAmB,GAAnBA,MAAM,CAACsB,OAAO,CAACe,IAAI,SAAiB,GAApCrC,KAAAA,CAAoC,GAApCA,GAAmB,CAAEsC,eAAe,EAAE,GACtCC,SAAS;IAEb,IAAI;QACF,MAAMrC,MAAM,GAAG,MAAM8B,YAAY,CAAC;YAChCV,OAAO,EAAE;gBACP,GAAGtB,MAAM,CAACsB,OAAO;gBACjBe,IAAI,EAAEF,MAAM,IAAIK,CAAAA,GAAAA,YAAmB,AAAyB,CAAA,oBAAzB,CAAChC,OAAO,CAACsB,OAAO,EAAEK,MAAM,CAAC;aAC7D;SACF,CAAC;QACF,KAAK,MAAMM,UAAU,IAAI5C,iBAAiB,CAAE;YAC1CK,MAAM,CAAC2B,QAAQ,CAACN,OAAO,CAACmB,MAAM,CAACD,UAAU,CAAC;SAC3C;QACD,OAAOvC,MAAM,CAAA;KACd,QAAS;YACFF,IAAmB;QAAzB,OAAMA,CAAAA,IAAmB,GAAnBA,MAAM,CAACsB,OAAO,CAACe,IAAI,SAAU,GAA7BrC,KAAAA,CAA6B,GAA7BA,IAAmB,CAAE2C,QAAQ,EAAE,CAAA;KACtC;CACF,CAAC;QAtCWvB,GAAG,GAAHA,GAAG"}