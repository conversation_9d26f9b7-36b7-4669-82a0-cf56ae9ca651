{"version": 3, "sources": ["../../server/crypto-utils.ts"], "names": ["encryptWithSecret", "decryptWithSecret", "CIPHER_ALGORITHM", "CIPHER_KEY_LENGTH", "CIPHER_IV_LENGTH", "CIPHER_TAG_LENGTH", "CIPHER_SALT_LENGTH", "PBKDF2_ITERATIONS", "secret", "data", "iv", "crypto", "randomBytes", "salt", "key", "pbkdf2Sync", "cipher", "createCipheriv", "encrypted", "<PERSON><PERSON><PERSON>", "concat", "update", "final", "tag", "getAuthTag", "toString", "encryptedData", "buffer", "from", "slice", "decipher", "createDecipheriv", "setAuthTag"], "mappings": "AAAA;;;;QAagBA,iBAAiB,GAAjBA,iBAAiB;QA+BjBC,iBAAiB,GAAjBA,iBAAiB;AA5Cd,IAAA,OAAQ,kCAAR,QAAQ,EAAA;;;;;;AAE3B,cAAc;AACd,qHAAqH;AAErH,MAAMC,gBAAgB,GAAG,CAAC,WAAW,CAAC,EACpCC,iBAAiB,GAAG,EAAE,EACtBC,gBAAgB,GAAG,EAAE,EACrBC,iBAAiB,GAAG,EAAE,EACtBC,kBAAkB,GAAG,EAAE;AAEzB,MAAMC,iBAAiB,GAAG,MAAO,CAAC,wCAAwC;AAAzC;AAE1B,SAASP,iBAAiB,CAACQ,MAAc,EAAEC,IAAY,EAAU;IACtE,MAAMC,EAAE,GAAGC,OAAM,QAAA,CAACC,WAAW,CAACR,gBAAgB,CAAC;IAC/C,MAAMS,IAAI,GAAGF,OAAM,QAAA,CAACC,WAAW,CAACN,kBAAkB,CAAC;IAEnD,qGAAqG;IACrG,MAAMQ,GAAG,GAAGH,OAAM,QAAA,CAACI,UAAU,CAC3BP,MAAM,EACNK,IAAI,EACJN,iBAAiB,EACjBJ,iBAAiB,EACjB,CAAC,MAAM,CAAC,CACT;IAED,MAAMa,MAAM,GAAGL,OAAM,QAAA,CAACM,cAAc,CAACf,gBAAgB,EAAEY,GAAG,EAAEJ,EAAE,CAAC;IAC/D,MAAMQ,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC;QAACJ,MAAM,CAACK,MAAM,CAACZ,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAAEO,MAAM,CAACM,KAAK,EAAE;KAAC,CAAC;IAE9E,8DAA8D;IAC9D,MAAMC,GAAG,GAAGP,MAAM,CAACQ,UAAU,EAAE;IAE/B,OAAOL,MAAM,CAACC,MAAM,CAAC;QACnB,uBAAuB;QACvB,mHAAmH;QACnH,+DAA+D;QAC/D,4EAA4E;QAC5EP,IAAI;QACJH,EAAE;QACFa,GAAG;QACHL,SAAS;KACV,CAAC,CAACO,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;CACnB;AAEM,SAASxB,iBAAiB,CAC/BO,MAAc,EACdkB,aAAqB,EACb;IACR,MAAMC,MAAM,GAAGR,MAAM,CAACS,IAAI,CAACF,aAAa,EAAE,CAAC,GAAG,CAAC,CAAC;IAEhD,MAAMb,IAAI,GAAGc,MAAM,CAACE,KAAK,CAAC,CAAC,EAAEvB,kBAAkB,CAAC;IAChD,MAAMI,EAAE,GAAGiB,MAAM,CAACE,KAAK,CACrBvB,kBAAkB,EAClBA,kBAAkB,GAAGF,gBAAgB,CACtC;IACD,MAAMmB,GAAG,GAAGI,MAAM,CAACE,KAAK,CACtBvB,kBAAkB,GAAGF,gBAAgB,EACrCE,kBAAkB,GAAGF,gBAAgB,GAAGC,iBAAiB,CAC1D;IACD,MAAMa,SAAS,GAAGS,MAAM,CAACE,KAAK,CAC5BvB,kBAAkB,GAAGF,gBAAgB,GAAGC,iBAAiB,CAC1D;IAED,qGAAqG;IACrG,MAAMS,GAAG,GAAGH,OAAM,QAAA,CAACI,UAAU,CAC3BP,MAAM,EACNK,IAAI,EACJN,iBAAiB,EACjBJ,iBAAiB,EACjB,CAAC,MAAM,CAAC,CACT;IAED,MAAM2B,QAAQ,GAAGnB,OAAM,QAAA,CAACoB,gBAAgB,CAAC7B,gBAAgB,EAAEY,GAAG,EAAEJ,EAAE,CAAC;IACnEoB,QAAQ,CAACE,UAAU,CAACT,GAAG,CAAC;IAExB,OAAOO,QAAQ,CAACT,MAAM,CAACH,SAAS,CAAC,GAAGY,QAAQ,CAACR,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;CAC3D"}