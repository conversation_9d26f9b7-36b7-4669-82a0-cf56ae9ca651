{"version": 3, "sources": ["../../server/require.ts"], "names": ["getPagePath", "requirePage", "requireFontManifest", "page", "distDir", "serverless", "dev", "locales", "appDirEnabled", "serverBuildPath", "join", "SERVERLESS_DIRECTORY", "SERVER_DIRECTORY", "appPathsManifest", "require", "APP_PATHS_MANIFEST", "pagesManifest", "PAGES_MANIFEST", "denormalizePagePath", "normalizePagePath", "err", "console", "error", "PageNotFoundError", "checkManifest", "manifest", "curPath", "manifestNoLocales", "key", "Object", "keys", "normalizeLocalePath", "pathname", "pagePath", "undefined", "endsWith", "promises", "readFile", "catch", "MissingStaticPage", "message", "fontManifest", "FONT_MANIFEST"], "mappings": "AAAA;;;;QAegBA,WAAW,GAAXA,WAAW;QA2DXC,WAAW,GAAXA,WAAW;QAsBXC,mBAAmB,GAAnBA,mBAAmB;AAhGV,IAAA,GAAI,WAAJ,IAAI,CAAA;AACR,IAAA,KAAM,WAAN,MAAM,CAAA;AAOpB,IAAA,UAAyB,WAAzB,yBAAyB,CAAA;AACI,IAAA,oBAA0C,WAA1C,0CAA0C,CAAA;AAC5C,IAAA,kBAA6C,WAA7C,6CAA6C,CAAA;AAC3C,IAAA,oBAA+C,WAA/C,+CAA+C,CAAA;AAE9B,IAAA,MAAqB,WAArB,qBAAqB,CAAA;AAEnE,SAASF,WAAW,CACzBG,IAAY,EACZC,OAAe,EACfC,UAAmB,EACnBC,GAAa,EACbC,OAAkB,EAClBC,aAAuB,EACf;IACR,MAAMC,eAAe,GAAGC,CAAAA,GAAAA,KAAI,AAG3B,CAAA,KAH2B,CAC1BN,OAAO,EACPC,UAAU,IAAI,CAACC,GAAG,GAAGK,UAAoB,qBAAA,GAAGC,UAAgB,iBAAA,CAC7D;IACD,IAAIC,gBAAgB,AAA2B;IAE/C,IAAIL,aAAa,EAAE;QACjBK,gBAAgB,GAAGC,OAAO,CAACJ,CAAAA,GAAAA,KAAI,AAAqC,CAAA,KAArC,CAACD,eAAe,EAAEM,UAAkB,mBAAA,CAAC,CAAC;KACtE;IACD,MAAMC,aAAa,GAAGF,OAAO,CAACJ,CAAAA,GAAAA,KAAI,AAGjC,CAAA,KAHiC,CAChCD,eAAe,EACfQ,UAAc,eAAA,CACf,CAAC,AAAiB;IAEnB,IAAI;QACFd,IAAI,GAAGe,CAAAA,GAAAA,oBAAmB,AAAyB,CAAA,oBAAzB,CAACC,CAAAA,GAAAA,kBAAiB,AAAM,CAAA,kBAAN,CAAChB,IAAI,CAAC,CAAC;KACpD,CAAC,OAAOiB,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;QAClB,MAAM,IAAIG,MAAiB,kBAAA,CAACpB,IAAI,CAAC,CAAA;KAClC;IAED,MAAMqB,aAAa,GAAG,CAACC,QAAuB,GAAK;QACjD,IAAIC,OAAO,GAAGD,QAAQ,CAACtB,IAAI,CAAC;QAE5B,IAAI,CAACsB,QAAQ,CAACC,OAAO,CAAC,IAAInB,OAAO,EAAE;YACjC,MAAMoB,iBAAiB,GAAyB,EAAE;YAElD,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAAC,CAAE;gBACvCE,iBAAiB,CAACI,CAAAA,GAAAA,oBAAmB,AAAc,CAAA,oBAAd,CAACH,GAAG,EAAErB,OAAO,CAAC,CAACyB,QAAQ,CAAC,GAC3DhB,aAAa,CAACY,GAAG,CAAC;aACrB;YACDF,OAAO,GAAGC,iBAAiB,CAACxB,IAAI,CAAC;SAClC;QACD,OAAOuB,OAAO,CAAA;KACf;IACD,IAAIO,QAAQ,AAAoB;IAEhC,IAAIpB,gBAAgB,EAAE;QACpBoB,QAAQ,GAAGT,aAAa,CAACX,gBAAgB,CAAC;KAC3C;IAED,IAAI,CAACoB,QAAQ,EAAE;QACbA,QAAQ,GAAGT,aAAa,CAACR,aAAa,CAAC;KACxC;IAED,IAAI,CAACiB,QAAQ,EAAE;QACb,MAAM,IAAIV,MAAiB,kBAAA,CAACpB,IAAI,CAAC,CAAA;KAClC;IACD,OAAOO,CAAAA,GAAAA,KAAI,AAA2B,CAAA,KAA3B,CAACD,eAAe,EAAEwB,QAAQ,CAAC,CAAA;CACvC;AAEM,SAAShC,WAAW,CACzBE,IAAY,EACZC,OAAe,EACfC,UAAmB,EACnBG,aAAuB,EAClB;IACL,MAAMyB,QAAQ,GAAGjC,WAAW,CAC1BG,IAAI,EACJC,OAAO,EACPC,UAAU,EACV,KAAK,EACL6B,SAAS,EACT1B,aAAa,CACd;IACD,IAAIyB,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC9B,OAAOC,GAAQ,SAAA,CAACC,QAAQ,CAACJ,QAAQ,EAAE,MAAM,CAAC,CAACK,KAAK,CAAC,CAAClB,GAAG,GAAK;YACxD,MAAM,IAAImB,MAAiB,kBAAA,CAACpC,IAAI,EAAEiB,GAAG,CAACoB,OAAO,CAAC,CAAA;SAC/C,CAAC,CAAA;KACH;IACD,OAAO1B,OAAO,CAACmB,QAAQ,CAAC,CAAA;CACzB;AAEM,SAAS/B,mBAAmB,CAACE,OAAe,EAAEC,UAAmB,EAAE;IACxE,MAAMI,eAAe,GAAGC,CAAAA,GAAAA,KAAI,AAG3B,CAAA,KAH2B,CAC1BN,OAAO,EACPC,UAAU,GAAGM,UAAoB,qBAAA,GAAGC,UAAgB,iBAAA,CACrD;IACD,MAAM6B,YAAY,GAAG3B,OAAO,CAACJ,CAAAA,GAAAA,KAAI,AAAgC,CAAA,KAAhC,CAACD,eAAe,EAAEiC,UAAa,cAAA,CAAC,CAAC;IAClE,OAAOD,YAAY,CAAA;CACpB"}