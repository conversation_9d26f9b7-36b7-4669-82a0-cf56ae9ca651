{"version": 3, "sources": ["../../../server/send-payload/revalidate-headers.ts"], "names": ["setRevalidateHeaders", "res", "options", "private", "stateful", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "revalidate", "Error"], "mappings": "AAAA;;;;QAIgBA,oBAAoB,GAApBA,oBAAoB;AAA7B,SAASA,oBAAoB,CAClCC,GAAsC,EACtCC,OAAuB,EACvB;IACA,IAAIA,OAAO,CAACC,OAAO,IAAID,OAAO,CAACE,QAAQ,EAAE;QACvC,IAAIF,OAAO,CAACC,OAAO,IAAI,CAACF,GAAG,CAACI,SAAS,CAAC,eAAe,CAAC,EAAE;YACtDJ,GAAG,CAACK,SAAS,CACX,eAAe,EACf,CAAC,uDAAuD,CAAC,CAC1D;SACF;KACF,MAAM,IAAI,OAAOJ,OAAO,CAACK,UAAU,KAAK,QAAQ,EAAE;QACjD,IAAIL,OAAO,CAACK,UAAU,GAAG,CAAC,EAAE;YAC1B,MAAM,IAAIC,KAAK,CACb,CAAC,oDAAoD,EAAEN,OAAO,CAACK,UAAU,CAAC,IAAI,CAAC,CAChF,CAAA;SACF;QAEDN,GAAG,CAACK,SAAS,CACX,eAAe,EACf,CAAC,SAAS,EAAEJ,OAAO,CAACK,UAAU,CAAC,wBAAwB,CAAC,CACzD;KACF,MAAM,IAAIL,OAAO,CAACK,UAAU,KAAK,KAAK,EAAE;QACvCN,GAAG,CAACK,SAAS,CAAC,eAAe,EAAE,CAAC,yCAAyC,CAAC,CAAC;KAC5E;CACF"}