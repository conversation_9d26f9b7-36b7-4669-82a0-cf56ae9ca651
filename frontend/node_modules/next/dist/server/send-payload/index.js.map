{"version": 3, "sources": ["../../../server/send-payload/index.ts"], "names": ["setRevalidateHeaders", "sendEtagResponse", "sendRenderResult", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "fresh", "headers", "statusCode", "end", "result", "type", "generateEtags", "poweredByHeader", "options", "isResSent", "payload", "isDynamic", "toUnchunkedString", "generateETag", "undefined", "resultContentType", "contentType", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipe"], "mappings": "AAAA;;;;+BAaSA,sBAAoB;;;eAApBA,kBAAoB,qBAAA;;;QAEbC,gBAAgB,GAAhBA,gBAAgB;QAwBVC,gBAAgB,GAAhBA,gBAAgB;AArCZ,IAAA,MAAwB,WAAxB,wBAAwB,CAAA;AACrB,IAAA,KAAa,WAAb,aAAa,CAAA;AACxB,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAEP,IAAA,kBAAsB,WAAtB,sBAAsB,CAAA;;;;;;AASpD,SAASD,gBAAgB,CAC9BE,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB,EACf;IACT,IAAIA,IAAI,EAAE;QACR;;;;;OAKG,CACHD,GAAG,CAACE,SAAS,CAAC,MAAM,EAAED,IAAI,CAAC;KAC5B;IAED,IAAIE,CAAAA,GAAAA,MAAK,AAAuB,CAAA,QAAvB,CAACJ,GAAG,CAACK,OAAO,EAAE;QAAEH,IAAI;KAAE,CAAC,EAAE;QAChCD,GAAG,CAACK,UAAU,GAAG,GAAG;QACpBL,GAAG,CAACM,GAAG,EAAE;QACT,OAAO,IAAI,CAAA;KACZ;IAED,OAAO,KAAK,CAAA;CACb;AAEM,eAAeR,gBAAgB,CAAC,EACrCC,GAAG,CAAA,EACHC,GAAG,CAAA,EACHO,MAAM,CAAA,EACNC,IAAI,CAAA,EACJC,aAAa,CAAA,EACbC,eAAe,CAAA,EACfC,OAAO,CAAA,EASR,EAAiB;IAChB,IAAIC,CAAAA,GAAAA,MAAS,AAAK,CAAA,UAAL,CAACZ,GAAG,CAAC,EAAE;QAClB,OAAM;KACP;IAED,IAAIU,eAAe,IAAIF,IAAI,KAAK,MAAM,EAAE;QACtCR,GAAG,CAACE,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC;KACzC;IAED,MAAMW,OAAO,GAAGN,MAAM,CAACO,SAAS,EAAE,GAAG,IAAI,GAAG,MAAMP,MAAM,CAACQ,iBAAiB,EAAE;IAE5E,IAAIF,OAAO,EAAE;QACX,MAAMZ,IAAI,GAAGQ,aAAa,GAAGO,CAAAA,GAAAA,KAAY,AAAS,CAAA,aAAT,CAACH,OAAO,CAAC,GAAGI,SAAS;QAC9D,IAAIpB,gBAAgB,CAACE,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC,EAAE;YACpC,OAAM;SACP;KACF;IAED,MAAMiB,iBAAiB,GAAGX,MAAM,CAACY,WAAW,EAAE;IAE9C,IAAI,CAACnB,GAAG,CAACoB,SAAS,CAAC,cAAc,CAAC,EAAE;QAClCpB,GAAG,CAACE,SAAS,CACX,cAAc,EACdgB,iBAAiB,GACbA,iBAAiB,GACjBV,IAAI,KAAK,MAAM,GACf,kBAAkB,GAClB,0BAA0B,CAC/B;KACF;IAED,IAAIK,OAAO,EAAE;QACXb,GAAG,CAACE,SAAS,CAAC,gBAAgB,EAAEmB,MAAM,CAACC,UAAU,CAACT,OAAO,CAAC,CAAC;KAC5D;IAED,IAAIF,OAAO,IAAI,IAAI,EAAE;QACnBf,CAAAA,GAAAA,kBAAoB,AAAc,CAAA,qBAAd,CAACI,GAAG,EAAEW,OAAO,CAAC;KACnC;IAED,IAAIZ,GAAG,CAACwB,MAAM,KAAK,MAAM,EAAE;QACzBvB,GAAG,CAACM,GAAG,CAAC,IAAI,CAAC;KACd,MAAM,IAAIO,OAAO,EAAE;QAClBb,GAAG,CAACM,GAAG,CAACO,OAAO,CAAC;KACjB,MAAM;QACL,MAAMN,MAAM,CAACiB,IAAI,CAACxB,GAAG,CAAC;KACvB;CACF"}