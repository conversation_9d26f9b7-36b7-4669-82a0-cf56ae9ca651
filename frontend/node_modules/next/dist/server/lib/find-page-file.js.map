{"version": 3, "sources": ["../../../server/lib/find-page-file.ts"], "names": ["findPageFile", "isLayoutsLeafPage", "isTrueCasePagePath", "pagePath", "pagesDir", "pageSegments", "normalize", "split", "sep", "filter", "Boolean", "segmentExistsPromises", "map", "segment", "i", "segmentParentDir", "join", "slice", "parentDirEntries", "promises", "readdir", "includes", "Promise", "all", "every", "normalizedPagePath", "pageExtensions", "isAppDir", "pagePaths", "getPagePaths", "existingPath", "others", "path", "filePath", "fileExists", "nonNullable", "length", "warn", "chalk", "cyan", "test"], "mappings": "AAAA;;;;QA4BsBA,YAAY,GAAZA,YAAY;QAyClBC,iBAAiB,GAAjBA,iBAAiB;AArEN,IAAA,WAAuB,WAAvB,uBAAuB,CAAA;AACrB,IAAA,aAA2C,WAA3C,2CAA2C,CAAA;AAC5C,IAAA,YAAwB,WAAxB,wBAAwB,CAAA;AACf,IAAA,KAAM,WAAN,MAAM,CAAA;AAClB,IAAA,GAAI,WAAJ,IAAI,CAAA;AACR,IAAA,IAAwB,WAAxB,wBAAwB,CAAA;AAC3B,IAAA,MAAiB,kCAAjB,iBAAiB,EAAA;;;;;;AAEnC,eAAeC,kBAAkB,CAACC,QAAgB,EAAEC,QAAgB,EAAE;IACpE,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,KAAS,AAAU,CAAA,UAAV,CAACH,QAAQ,CAAC,CAACI,KAAK,CAACC,KAAG,IAAA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IACnE,MAAMC,qBAAqB,GAAGN,YAAY,CAACO,GAAG,CAAC,OAAOC,OAAO,EAAEC,CAAC,GAAK;QACnE,MAAMC,gBAAgB,GAAGC,CAAAA,GAAAA,KAAI,AAAuC,CAAA,KAAvC,CAACZ,QAAQ,KAAKC,YAAY,CAACY,KAAK,CAAC,CAAC,EAAEH,CAAC,CAAC,CAAC;QACpE,MAAMI,gBAAgB,GAAG,MAAMC,GAAQ,SAAA,CAACC,OAAO,CAACL,gBAAgB,CAAC;QACjE,OAAOG,gBAAgB,CAACG,QAAQ,CAACR,OAAO,CAAC,CAAA;KAC1C,CAAC;IAEF,OAAO,CAAC,MAAMS,OAAO,CAACC,GAAG,CAACZ,qBAAqB,CAAC,CAAC,CAACa,KAAK,CAACd,OAAO,CAAC,CAAA;CACjE;AAWM,eAAeV,YAAY,CAChCI,QAAgB,EAChBqB,kBAA0B,EAC1BC,cAAwB,EACxBC,QAAiB,EACO;IACxB,MAAMC,SAAS,GAAGC,CAAAA,GAAAA,aAAY,AAA8C,CAAA,aAA9C,CAACJ,kBAAkB,EAAEC,cAAc,EAAEC,QAAQ,CAAC;IAC5E,MAAM,CAACG,YAAY,EAAE,GAAGC,MAAM,CAAC,GAAG,CAChC,MAAMT,OAAO,CAACC,GAAG,CACfK,SAAS,CAAChB,GAAG,CAAC,OAAOoB,IAAI,GAAK;QAC5B,MAAMC,QAAQ,GAAGjB,CAAAA,GAAAA,KAAI,AAAgB,CAAA,KAAhB,CAACZ,QAAQ,EAAE4B,IAAI,CAAC;QACrC,OAAO,AAAC,MAAME,CAAAA,GAAAA,WAAU,AAAU,CAAA,WAAV,CAACD,QAAQ,CAAC,GAAID,IAAI,GAAG,IAAI,CAAA;KAClD,CAAC,CACH,CACF,CAACvB,MAAM,CAAC0B,YAAW,YAAA,CAAC;IAErB,IAAI,CAACL,YAAY,EAAE;QACjB,OAAO,IAAI,CAAA;KACZ;IAED,IAAI,CAAE,MAAM5B,kBAAkB,CAAC4B,YAAY,EAAE1B,QAAQ,CAAC,AAAC,EAAE;QACvD,OAAO,IAAI,CAAA;KACZ;IAED,IAAI2B,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;QACrBC,CAAAA,GAAAA,IAAI,AAMH,CAAA,KANG,CACF,CAAC,yBAAyB,EAAEC,MAAK,QAAA,CAACC,IAAI,CACpCvB,CAAAA,GAAAA,KAAI,AAAuB,CAAA,KAAvB,CAAC,OAAO,EAAEc,YAAY,CAAC,CAC5B,CAAC,KAAK,EAAEQ,MAAK,QAAA,CAACC,IAAI,CACjBvB,CAAAA,GAAAA,KAAI,AAAoB,CAAA,KAApB,CAAC,OAAO,EAAEe,MAAM,CAAC,CAAC,CAAC,CAAC,CACzB,CAAC,iBAAiB,EAAEO,MAAK,QAAA,CAACC,IAAI,CAACd,kBAAkB,CAAC,CAAC,CAAC,CAAC,CACvD;KACF;IAED,OAAOK,YAAY,CAAA;CACpB;AAMM,SAAS7B,iBAAiB,CAACgC,QAAgB,EAAE;IAClD,OAAO,4CAA4CO,IAAI,CAACP,QAAQ,CAAC,CAAA;CAClE"}