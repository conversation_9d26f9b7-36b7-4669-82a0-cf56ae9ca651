{"version": 3, "sources": ["../../../server/lib/etag.ts"], "names": ["fnv1a52", "str", "len", "length", "i", "t0", "v0", "t1", "v1", "t2", "v2", "t3", "v3", "charCodeAt", "generateETag", "payload", "weak", "prefix", "toString"], "mappings": "AASA;;;;;AAAO,MAAMA,OAAO,GAAG,CAACC,GAAW,GAAK;IACtC,MAAMC,GAAG,GAAGD,GAAG,CAACE,MAAM;IACtB,IAAIC,CAAC,GAAG,CAAC,EACPC,EAAE,GAAG,CAAC,EACNC,EAAE,GAAG,MAAM,EACXC,EAAE,GAAG,CAAC,EACNC,EAAE,GAAG,MAAM,EACXC,EAAE,GAAG,CAAC,EACNC,EAAE,GAAG,MAAM,EACXC,EAAE,GAAG,CAAC,EACNC,EAAE,GAAG,MAAM;IAEb,MAAOR,CAAC,GAAGF,GAAG,CAAE;QACdI,EAAE,IAAIL,GAAG,CAACY,UAAU,CAACT,CAAC,EAAE,CAAC;QACzBC,EAAE,GAAGC,EAAE,GAAG,GAAG;QACbC,EAAE,GAAGC,EAAE,GAAG,GAAG;QACbC,EAAE,GAAGC,EAAE,GAAG,GAAG;QACbC,EAAE,GAAGC,EAAE,GAAG,GAAG;QACbH,EAAE,IAAIH,EAAE,IAAI,CAAC;QACbK,EAAE,IAAIH,EAAE,IAAI,CAAC;QACbD,EAAE,IAAIF,EAAE,KAAK,EAAE;QACfC,EAAE,GAAGD,EAAE,GAAG,KAAK;QACfI,EAAE,IAAIF,EAAE,KAAK,EAAE;QACfC,EAAE,GAAGD,EAAE,GAAG,KAAK;QACfK,EAAE,GAAG,AAACD,EAAE,GAAG,CAACF,EAAE,KAAK,EAAE,CAAC,GAAI,KAAK;QAC/BC,EAAE,GAAGD,EAAE,GAAG,KAAK;KAChB;IAED,OACE,CAACG,EAAE,GAAG,EAAE,CAAC,GAAG,eAAe,GAC3BF,EAAE,GAAG,UAAU,GACfF,EAAE,GAAG,KAAK,GACV,CAACF,EAAE,GAAIM,EAAE,IAAI,CAAC,AAAC,CAAC,CACjB;CACF;QAlCYZ,OAAO,GAAPA,OAAO;AAoCb,MAAMc,YAAY,GAAG,CAACC,OAAe,EAAEC,IAAI,GAAG,KAAK,GAAK;IAC7D,MAAMC,MAAM,GAAGD,IAAI,GAAG,KAAK,GAAG,GAAG;IACjC,OACEC,MAAM,GAAGjB,OAAO,CAACe,OAAO,CAAC,CAACG,QAAQ,CAAC,EAAE,CAAC,GAAGH,OAAO,CAACZ,MAAM,CAACe,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG,CAC3E;CACF;QALYJ,YAAY,GAAZA,YAAY"}