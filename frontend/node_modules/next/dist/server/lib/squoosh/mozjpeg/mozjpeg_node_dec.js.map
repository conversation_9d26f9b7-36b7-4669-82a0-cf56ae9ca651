{"version": 3, "sources": ["../../../../../server/lib/squoosh/mozjpeg/mozjpeg_node_dec.js"], "names": ["<PERSON><PERSON><PERSON>", "readyPromiseResolve", "readyPromiseReject", "Promise", "resolve", "reject", "moduleOverrides", "key", "hasOwnProperty", "arguments_", "thisProgram", "quit_", "status", "toThrow", "ENVIRONMENT_IS_WEB", "ENVIRONMENT_IS_WORKER", "ENVIRONMENT_IS_NODE", "scriptDirectory", "locateFile", "path", "read_", "readBinary", "nodeFS", "nodePath", "require", "dirname", "__dirname", "shell_read", "filename", "binary", "ret", "buffer", "Uint8Array", "assert", "process", "length", "replace", "slice", "out", "console", "log", "bind", "err", "warn", "tempRet0", "setTempRet0", "value", "wasmBinary", "noExitRuntime", "WebAssembly", "abort", "was<PERSON><PERSON><PERSON><PERSON>", "ABORT", "EXITSTATUS", "condition", "text", "UTF8Decoder", "TextDecoder", "UTF8ArrayToString", "heap", "idx", "maxBytesToRead", "endIdx", "endPtr", "decode", "subarray", "UTF8ToString", "ptr", "maxPtr", "end", "HEAPU8", "stringToUTF8Array", "str", "outIdx", "maxBytesToWrite", "startIdx", "i", "u", "charCodeAt", "u1", "stringToUTF8", "outPtr", "lengthBytesUTF8", "len", "UTF16Decoder", "UTF16ToString", "maxIdx", "HEAPU16", "codeUnit", "HEAP16", "String", "fromCharCode", "stringToUTF16", "undefined", "startPtr", "numCharsToWrite", "lengthBytesUTF16", "UTF32ToString", "utf32", "HEAP32", "ch", "stringToUTF32", "trailSurrogate", "lengthBytesUTF32", "writeAsciiToMemory", "dontAdd<PERSON>ull", "HEAP8", "alignUp", "x", "multiple", "HEAPU32", "HEAPF32", "HEAPF64", "updateGlobalBufferAndViews", "buf", "Int8Array", "Int16Array", "Int32Array", "Uint16Array", "Uint32Array", "Float32Array", "Float64Array", "INITIAL_MEMORY", "wasmTable", "__ATPRERUN__", "__ATINIT__", "__ATPOSTRUN__", "runtimeInitialized", "runtimeExited", "preRun", "addOnPreRun", "shift", "callRuntimeCallbacks", "initRuntime", "exitRuntime", "postRun", "addOnPostRun", "cb", "unshift", "addOnInit", "runDependencies", "runDependencyWatcher", "dependenciesFulfilled", "addRunDependency", "id", "removeRunDependency", "clearInterval", "callback", "what", "e", "RuntimeError", "dataURIPrefix", "isDataURI", "startsWith", "wasmBinaryFile", "Error", "getBinary", "file", "getBinaryPromise", "then", "createWasm", "info", "a", "asmLibraryArg", "receiveInstance", "instance", "module", "exports", "receiveInstantiationResult", "result", "instantiateArrayBuffer", "receiver", "instantiate", "reason", "instantiateAsync", "catch", "callbacks", "func", "arg", "get", "runtime<PERSON><PERSON><PERSON>ve<PERSON>ounter", "keepRuntimeAlive", "_atexit", "___cxa_thread_atexit", "a0", "a1", "__embind_register_bigint", "primitiveType", "name", "size", "minRange", "max<PERSON><PERSON><PERSON>", "getShiftFromSize", "TypeError", "embind_init_charCodes", "codes", "Array", "embind_charCodes", "readLatin1String", "c", "awaitingDependencies", "registeredTypes", "typeDependencies", "char_0", "char_9", "makeLegalFunctionName", "f", "createNamedFunction", "body", "Function", "extendError", "baseErrorType", "errorName", "errorClass", "message", "stack", "toString", "prototype", "Object", "create", "constructor", "BindingError", "throwBindingError", "InternalError", "throwInternalError", "whenDependentTypesAreResolved", "myTypes", "dependentTypes", "getTypeConverters", "for<PERSON>ach", "type", "onComplete", "typeConverters", "myTypeConverters", "registerType", "unregisteredTypes", "registered", "dt", "push", "rawType", "registeredInstance", "options", "ignoreDuplicateRegistrations", "__embind_register_bool", "trueValue", "falseValue", "fromWireType", "wt", "toWireType", "destructors", "o", "argPackAdvance", "readValueFromPointer", "pointer", "destructorFunction", "emval_free_list", "emval_handle_array", "__emval_decref", "handle", "refcount", "count_emval_handles", "count", "get_first_emval", "init_emval", "__emval_register", "pop", "simpleReadValueFromPointer", "__embind_register_emval", "rv", "_embind_repr", "v", "t", "floatReadValueFromPointer", "__embind_register_float", "new_", "argumentList", "dummy", "obj", "r", "apply", "runDestructors", "del", "craftInvokerFunction", "humanName", "argTypes", "classType", "cppInvokerFunc", "cppTargetFunc", "argCount", "isClassMethodFunc", "needsDestructorStack", "returns", "argsList", "argsListWired", "invokerFnBody", "dtorStack", "args1", "args2", "paramName", "invokerFunction", "ensureOverloadTable", "proto", "methodName", "overloadTable", "prevFunc", "arguments", "exposePublicSymbol", "numArguments", "heap32VectorToArray", "firstElement", "array", "replacePublicSymbol", "dynCallLegacy", "sig", "args", "concat", "call", "dynCall", "includes", "getDynCaller", "<PERSON><PERSON><PERSON><PERSON>", "embind__requireFunction", "signature", "rawFunction", "makeDynCaller", "fp", "UnboundTypeError", "getTypeName", "___getTypeName", "_free", "throwUnboundTypeError", "types", "unboundTypes", "seen", "visit", "map", "join", "__embind_register_function", "rawArgTypesAddr", "rawInvoker", "fn", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "integerReadValueFromPointer", "signed", "readS8FromPointer", "readU8FromPointer", "readS16FromPointer", "readU16FromPointer", "readS32FromPointer", "readU32FromPointer", "__embind_register_integer", "bitshift", "isUnsignedType", "__embind_register_memory_view", "dataTypeIndex", "typeMapping", "TA", "decodeMemoryView", "data", "__embind_register_std_string", "stdStringIsUTF8", "decodeStartPtr", "currentBytePtr", "maxRead", "stringSegment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "valueIsOfTypeString", "Uint8ClampedArray", "_malloc", "charCode", "__embind_register_std_wstring", "charSize", "decodeString", "encodeString", "getHeap", "lengthBytesUTF", "HEAP", "maxReadBytes", "__embind_register_void", "isVoid", "emval_symbols", "getStringOrSymbol", "address", "symbol", "emval_get_global", "globalThis", "__emval_get_global", "__emval_incref", "requireRegisteredType", "impl", "craftEmvalAllocator", "functionBody", "emval_newers", "<PERSON><PERSON><PERSON><PERSON>", "__emval_new", "newer", "_abort", "_emscripten_memcpy_big", "dest", "src", "num", "copyWithin", "emscripten_realloc_buffer", "grow", "byteLength", "_emscripten_resize_heap", "requestedSize", "oldSize", "maxHeapSize", "cutDown", "overGrownHeapSize", "Math", "min", "newSize", "max", "replacement", "ENV", "getExecutableName", "getEnvStrings", "strings", "lang", "navigator", "languages", "env", "USER", "LOGNAME", "PATH", "PWD", "HOME", "LANG", "_", "SYSCALLS", "mappings", "buffers", "printChar", "stream", "curr", "varargs", "getStr", "get64", "low", "high", "_environ_get", "__environ", "environ_buf", "bufSize", "string", "_environ_sizes_get", "penviron_count", "penviron_buf_size", "_exit", "exit", "_fd_close", "fd", "_fd_seek", "offset_low", "offset_high", "whence", "newOffset", "_fd_write", "iov", "iovcnt", "pnum", "j", "_setTempRet0", "val", "q", "m", "l", "b", "h", "g", "n", "d", "k", "s", "y", "w", "p", "asm", "___wasm_call_ctors", "___embind_register_native_and_builtin_types", "dyn<PERSON>all_jiji", "calledRun", "ExitStatus", "runCaller", "run", "doRun", "setTimeout", "implicit", "ready"], "mappings": "AACA;;;;;AADA,oBAAoB,CACpB,IAAIA,MAAM,GAAG,AAAC,WAAY;IACxB,OAAO,SAAUA,OAAM,EAAE;QACvBA,OAAM,GAAGA,OAAM,IAAI,EAAE;QAErB,IAAIA,OAAM,GAAG,OAAOA,OAAM,KAAK,WAAW,GAAGA,OAAM,GAAG,EAAE;QACxD,IAAIC,mBAAmB,EAAEC,kBAAkB;QAC3CF,OAAM,CAAC,OAAO,CAAC,GAAG,IAAIG,OAAO,CAAC,SAAUC,OAAO,EAAEC,MAAM,EAAE;YACvDJ,mBAAmB,GAAGG,OAAO;YAC7BF,kBAAkB,GAAGG,MAAM;SAC5B,CAAC;QACF,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIC,GAAG;QACP,IAAKA,GAAG,IAAIP,OAAM,CAAE;YAClB,IAAIA,OAAM,CAACQ,cAAc,CAACD,GAAG,CAAC,EAAE;gBAC9BD,eAAe,CAACC,GAAG,CAAC,GAAGP,OAAM,CAACO,GAAG,CAAC;aACnC;SACF;QACD,IAAIE,UAAU,GAAG,EAAE;QACnB,IAAIC,WAAW,GAAG,gBAAgB;QAClC,IAAIC,KAAK,GAAG,SAAUC,MAAM,EAAEC,OAAO,EAAE;YACrC,MAAMA,OAAO,CAAA;SACd;QACD,IAAIC,kBAAkB,GAAG,KAAK;QAC9B,IAAIC,qBAAqB,GAAG,KAAK;QACjC,IAAIC,mBAAmB,GAAG,IAAI;QAC9B,IAAIC,eAAe,GAAG,EAAE;QACxB,SAASC,UAAU,CAACC,IAAI,EAAE;YACxB,IAAInB,OAAM,CAAC,YAAY,CAAC,EAAE;gBACxB,OAAOA,OAAM,CAAC,YAAY,CAAC,CAACmB,IAAI,EAAEF,eAAe,CAAC,CAAA;aACnD;YACD,OAAOA,eAAe,GAAGE,IAAI,CAAA;SAC9B;QACD,IAAIC,KAAK,EAAEC,UAAU;QACrB,IAAIC,MAAM;QACV,IAAIC,QAAQ;QACZ,IAAIP,mBAAmB,EAAE;YACvB,IAAID,qBAAqB,EAAE;gBACzBE,eAAe,GAAGO,OAAO,CAAC,MAAM,CAAC,CAACC,OAAO,CAACR,eAAe,CAAC,GAAG,GAAG;aACjE,MAAM;gBACLA,eAAe,GAAGS,SAAS,GAAG,GAAG;aAClC;YACDN,KAAK,GAAG,SAASO,UAAU,CAACC,QAAQ,EAAEC,MAAM,EAAE;gBAC5C,IAAI,CAACP,MAAM,EAAEA,MAAM,GAAGE,OAAO,CAAC,IAAI,CAAC;gBACnC,IAAI,CAACD,QAAQ,EAAEA,QAAQ,GAAGC,OAAO,CAAC,MAAM,CAAC;gBACzCI,QAAQ,GAAGL,QAAQ,CAAC,WAAW,CAAC,CAACK,QAAQ,CAAC;gBAC1C,OAAON,MAAM,CAAC,cAAc,CAAC,CAACM,QAAQ,EAAEC,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC,CAAA;aAChE;YACDR,UAAU,GAAG,SAASA,UAAU,CAACO,QAAQ,EAAE;gBACzC,IAAIE,GAAG,GAAGV,KAAK,CAACQ,QAAQ,EAAE,IAAI,CAAC;gBAC/B,IAAI,CAACE,GAAG,CAACC,MAAM,EAAE;oBACfD,GAAG,GAAG,IAAIE,UAAU,CAACF,GAAG,CAAC;iBAC1B;gBACDG,MAAM,CAACH,GAAG,CAACC,MAAM,CAAC;gBAClB,OAAOD,GAAG,CAAA;aACX;YACD,IAAII,OAAO,CAAC,MAAM,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;gBAC9BzB,WAAW,GAAGwB,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACE,OAAO,QAAQ,GAAG,CAAC;aACrD;YACD3B,UAAU,GAAGyB,OAAO,CAAC,MAAM,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;YACrC1B,KAAK,GAAG,SAAUC,MAAM,EAAE;gBACxBsB,OAAO,CAAC,MAAM,CAAC,CAACtB,MAAM,CAAC;aACxB;YACDZ,OAAM,CAAC,SAAS,CAAC,GAAG,WAAY;gBAC9B,OAAO,4BAA4B,CAAA;aACpC;SACF,MAAM,EACN;QACD,IAAIsC,GAAG,GAAGtC,OAAM,CAAC,OAAO,CAAC,IAAIuC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACF,OAAO,CAAC;QACtD,IAAIG,IAAG,GAAG1C,OAAM,CAAC,UAAU,CAAC,IAAIuC,OAAO,CAACI,IAAI,CAACF,IAAI,CAACF,OAAO,CAAC;QAC1D,IAAKhC,GAAG,IAAID,eAAe,CAAE;YAC3B,IAAIA,eAAe,CAACE,cAAc,CAACD,GAAG,CAAC,EAAE;gBACvCP,OAAM,CAACO,GAAG,CAAC,GAAGD,eAAe,CAACC,GAAG,CAAC;aACnC;SACF;QACDD,eAAe,GAAG,IAAI;QACtB,IAAIN,OAAM,CAAC,WAAW,CAAC,EAAES,UAAU,GAAGT,OAAM,CAAC,WAAW,CAAC;QACzD,IAAIA,OAAM,CAAC,aAAa,CAAC,EAAEU,WAAW,GAAGV,OAAM,CAAC,aAAa,CAAC;QAC9D,IAAIA,OAAM,CAAC,MAAM,CAAC,EAAEW,KAAK,GAAGX,OAAM,CAAC,MAAM,CAAC;QAC1C,IAAI4C,QAAQ,GAAG,CAAC;QAChB,IAAIC,WAAW,GAAG,SAAUC,KAAK,EAAE;YACjCF,QAAQ,GAAGE,KAAK;SACjB;QACD,IAAIC,UAAU;QACd,IAAI/C,OAAM,CAAC,YAAY,CAAC,EAAE+C,UAAU,GAAG/C,OAAM,CAAC,YAAY,CAAC;QAC3D,IAAIgD,aAAa,GAAGhD,OAAM,CAAC,eAAe,CAAC,IAAI,IAAI;QACnD,IAAI,OAAOiD,WAAW,KAAK,QAAQ,EAAE;YACnCC,KAAK,CAAC,iCAAiC,CAAC;SACzC;QACD,IAAIC,UAAU;QACd,IAAIC,KAAK,GAAG,KAAK;QACjB,IAAIC,UAAU;QACd,SAASpB,MAAM,CAACqB,SAAS,EAAEC,IAAI,EAAE;YAC/B,IAAI,CAACD,SAAS,EAAE;gBACdJ,KAAK,CAAC,oBAAoB,GAAGK,IAAI,CAAC;aACnC;SACF;QACD,IAAIC,WAAW,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;QACzC,SAASC,iBAAiB,CAACC,IAAI,EAAEC,GAAG,EAAEC,cAAc,EAAE;YACpD,IAAIC,MAAM,GAAGF,GAAG,GAAGC,cAAc;YACjC,IAAIE,MAAM,GAAGH,GAAG;YAChB,MAAOD,IAAI,CAACI,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,IAAID,MAAM,CAAC,CAAE,EAAEC,MAAM;YACpD,OAAOP,WAAW,CAACQ,MAAM,CACvBL,IAAI,CAACM,QAAQ,GACTN,IAAI,CAACM,QAAQ,CAACL,GAAG,EAAEG,MAAM,CAAC,GAC1B,IAAI/B,UAAU,CAAC2B,IAAI,CAACtB,KAAK,CAACuB,GAAG,EAAEG,MAAM,CAAC,CAAC,CAC5C,CAAA;SACF;QACD,SAASG,YAAY,CAACC,GAAG,EAAEN,cAAc,EAAE;YACzC,IAAI,CAACM,GAAG,EAAE,OAAO,EAAE,CAAA;YACnB,IAAIC,MAAM,GAAGD,GAAG,GAAGN,cAAc;YACjC,IAAK,IAAIQ,GAAG,GAAGF,GAAG,EAAE,CAAC,CAACE,GAAG,IAAID,MAAM,CAAC,IAAIE,MAAM,CAACD,GAAG,CAAC,EAAI,EAAEA,GAAG;YAC5D,OAAOb,WAAW,CAACQ,MAAM,CAACM,MAAM,CAACL,QAAQ,CAACE,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAA;SACrD;QACD,SAASE,iBAAiB,CAACC,GAAG,EAAEb,IAAI,EAAEc,MAAM,EAAEC,eAAe,EAAE;YAC7D,IAAI,CAAC,CAACA,eAAe,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;YACpC,IAAIC,QAAQ,GAAGF,MAAM;YACrB,IAAIX,MAAM,GAAGW,MAAM,GAAGC,eAAe,GAAG,CAAC;YACzC,IAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC,IAAIC,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBACzB,IAAIC,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,EAAE;oBAC5B,IAAIE,EAAE,GAAGP,GAAG,CAACM,UAAU,CAAC,EAAEF,CAAC,CAAC;oBAC5BC,CAAC,GAAG,AAAC,KAAK,GAAG,CAAC,CAACA,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAKE,EAAE,GAAG,IAAI,AAAC;iBAC/C;gBACD,IAAIF,CAAC,IAAI,GAAG,EAAE;oBACZ,IAAIJ,MAAM,IAAIX,MAAM,EAAE,MAAK;oBAC3BH,IAAI,CAACc,MAAM,EAAE,CAAC,GAAGI,CAAC;iBACnB,MAAM,IAAIA,CAAC,IAAI,IAAI,EAAE;oBACpB,IAAIJ,MAAM,GAAG,CAAC,IAAIX,MAAM,EAAE,MAAK;oBAC/BH,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,IAAI,CAAC,AAAC;oBAC/BlB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,GAAG,EAAE,AAAC;iBAChC,MAAM,IAAIA,CAAC,IAAI,KAAK,EAAE;oBACrB,IAAIJ,MAAM,GAAG,CAAC,IAAIX,MAAM,EAAE,MAAK;oBAC/BH,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,IAAI,EAAE,AAAC;oBAChClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAI,AAACI,CAAC,IAAI,CAAC,GAAI,EAAE,AAAC;oBACtClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,GAAG,EAAE,AAAC;iBAChC,MAAM;oBACL,IAAIJ,MAAM,GAAG,CAAC,IAAIX,MAAM,EAAE,MAAK;oBAC/BH,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,IAAI,EAAE,AAAC;oBAChClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAI,AAACI,CAAC,IAAI,EAAE,GAAI,EAAE,AAAC;oBACvClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAI,AAACI,CAAC,IAAI,CAAC,GAAI,EAAE,AAAC;oBACtClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,GAAG,EAAE,AAAC;iBAChC;aACF;YACDlB,IAAI,CAACc,MAAM,CAAC,GAAG,CAAC;YAChB,OAAOA,MAAM,GAAGE,QAAQ,CAAA;SACzB;QACD,SAASK,YAAY,CAACR,GAAG,EAAES,MAAM,EAAEP,eAAe,EAAE;YAClD,OAAOH,iBAAiB,CAACC,GAAG,EAAEF,MAAM,EAAEW,MAAM,EAAEP,eAAe,CAAC,CAAA;SAC/D;QACD,SAASQ,eAAe,CAACV,GAAG,EAAE;YAC5B,IAAIW,GAAG,GAAG,CAAC;YACX,IAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC,IAAIC,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBACzB,IAAIC,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,EAC1BA,CAAC,GAAG,AAAC,KAAK,GAAG,CAAC,CAACA,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAKL,GAAG,CAACM,UAAU,CAAC,EAAEF,CAAC,CAAC,GAAG,IAAI,AAAC;gBACjE,IAAIC,CAAC,IAAI,GAAG,EAAE,EAAEM,GAAG;qBACd,IAAIN,CAAC,IAAI,IAAI,EAAEM,GAAG,IAAI,CAAC;qBACvB,IAAIN,CAAC,IAAI,KAAK,EAAEM,GAAG,IAAI,CAAC;qBACxBA,GAAG,IAAI,CAAC;aACd;YACD,OAAOA,GAAG,CAAA;SACX;QACD,IAAIC,YAAY,GAAG,IAAI3B,WAAW,CAAC,UAAU,CAAC;QAC9C,SAAS4B,aAAa,CAAClB,GAAG,EAAEN,cAAc,EAAE;YAC1C,IAAIE,MAAM,GAAGI,GAAG;YAChB,IAAIP,GAAG,GAAGG,MAAM,IAAI,CAAC;YACrB,IAAIuB,MAAM,GAAG1B,GAAG,GAAGC,cAAc,GAAG,CAAC;YACrC,MAAO,CAAC,CAACD,GAAG,IAAI0B,MAAM,CAAC,IAAIC,OAAO,CAAC3B,GAAG,CAAC,CAAE,EAAEA,GAAG;YAC9CG,MAAM,GAAGH,GAAG,IAAI,CAAC;YACjB,OAAOwB,YAAY,CAACpB,MAAM,CAACM,MAAM,CAACL,QAAQ,CAACE,GAAG,EAAEJ,MAAM,CAAC,CAAC,CAAA;YACxD,IAAIS,GAAG,GAAG,EAAE;YACZ,IAAK,IAAII,CAAC,GAAG,CAAC,EAAE,CAAC,CAACA,CAAC,IAAIf,cAAc,GAAG,CAAC,CAAC,EAAE,EAAEe,CAAC,CAAE;gBAC/C,IAAIY,QAAQ,GAAGC,MAAM,CAAC,AAACtB,GAAG,GAAGS,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;gBACzC,IAAIY,QAAQ,IAAI,CAAC,EAAE,MAAK;gBACxBhB,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAACH,QAAQ,CAAC;aACrC;YACD,OAAOhB,GAAG,CAAA;SACX;QACD,SAASoB,aAAa,CAACpB,GAAG,EAAES,MAAM,EAAEP,eAAe,EAAE;YACnD,IAAIA,eAAe,KAAKmB,SAAS,EAAE;gBACjCnB,eAAe,GAAG,UAAU;aAC7B;YACD,IAAIA,eAAe,GAAG,CAAC,EAAE,OAAO,CAAC,CAAA;YACjCA,eAAe,IAAI,CAAC;YACpB,IAAIoB,QAAQ,GAAGb,MAAM;YACrB,IAAIc,eAAe,GACjBrB,eAAe,GAAGF,GAAG,CAACrC,MAAM,GAAG,CAAC,GAAGuC,eAAe,GAAG,CAAC,GAAGF,GAAG,CAACrC,MAAM;YACrE,IAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,eAAe,EAAE,EAAEnB,CAAC,CAAE;gBACxC,IAAIY,QAAQ,GAAGhB,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBAChCa,MAAM,CAACR,MAAM,IAAI,CAAC,CAAC,GAAGO,QAAQ;gBAC9BP,MAAM,IAAI,CAAC;aACZ;YACDQ,MAAM,CAACR,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;YACvB,OAAOA,MAAM,GAAGa,QAAQ,CAAA;SACzB;QACD,SAASE,gBAAgB,CAACxB,GAAG,EAAE;YAC7B,OAAOA,GAAG,CAACrC,MAAM,GAAG,CAAC,CAAA;SACtB;QACD,SAAS8D,aAAa,CAAC9B,GAAG,EAAEN,cAAc,EAAE;YAC1C,IAAIe,CAAC,GAAG,CAAC;YACT,IAAIJ,GAAG,GAAG,EAAE;YACZ,MAAO,CAAC,CAACI,CAAC,IAAIf,cAAc,GAAG,CAAC,CAAC,CAAE;gBACjC,IAAIqC,KAAK,GAAGC,MAAM,CAAC,AAAChC,GAAG,GAAGS,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;gBACtC,IAAIsB,KAAK,IAAI,CAAC,EAAE,MAAK;gBACrB,EAAEtB,CAAC;gBACH,IAAIsB,KAAK,IAAI,KAAK,EAAE;oBAClB,IAAIE,EAAE,GAAGF,KAAK,GAAG,KAAK;oBACtB1B,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAAC,KAAK,GAAIS,EAAE,IAAI,EAAE,AAAC,EAAE,KAAK,GAAIA,EAAE,GAAG,IAAI,AAAC,CAAC;iBACpE,MAAM;oBACL5B,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAACO,KAAK,CAAC;iBAClC;aACF;YACD,OAAO1B,GAAG,CAAA;SACX;QACD,SAAS6B,aAAa,CAAC7B,GAAG,EAAES,MAAM,EAAEP,eAAe,EAAE;YACnD,IAAIA,eAAe,KAAKmB,SAAS,EAAE;gBACjCnB,eAAe,GAAG,UAAU;aAC7B;YACD,IAAIA,eAAe,GAAG,CAAC,EAAE,OAAO,CAAC,CAAA;YACjC,IAAIoB,QAAQ,GAAGb,MAAM;YACrB,IAAIlB,MAAM,GAAG+B,QAAQ,GAAGpB,eAAe,GAAG,CAAC;YAC3C,IAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC,IAAIY,QAAQ,GAAGhB,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBAChC,IAAIY,QAAQ,IAAI,KAAK,IAAIA,QAAQ,IAAI,KAAK,EAAE;oBAC1C,IAAIc,cAAc,GAAG9B,GAAG,CAACM,UAAU,CAAC,EAAEF,CAAC,CAAC;oBACxCY,QAAQ,GACN,AAAC,KAAK,GAAG,CAAC,CAACA,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAKc,cAAc,GAAG,IAAI,AAAC;iBAChE;gBACDH,MAAM,CAAClB,MAAM,IAAI,CAAC,CAAC,GAAGO,QAAQ;gBAC9BP,MAAM,IAAI,CAAC;gBACX,IAAIA,MAAM,GAAG,CAAC,GAAGlB,MAAM,EAAE,MAAK;aAC/B;YACDoC,MAAM,CAAClB,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;YACvB,OAAOA,MAAM,GAAGa,QAAQ,CAAA;SACzB;QACD,SAASS,gBAAgB,CAAC/B,GAAG,EAAE;YAC7B,IAAIW,GAAG,GAAG,CAAC;YACX,IAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC,IAAIY,QAAQ,GAAGhB,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBAChC,IAAIY,QAAQ,IAAI,KAAK,IAAIA,QAAQ,IAAI,KAAK,EAAE,EAAEZ,CAAC;gBAC/CO,GAAG,IAAI,CAAC;aACT;YACD,OAAOA,GAAG,CAAA;SACX;QACD,SAASqB,kBAAkB,CAAChC,GAAG,EAAEzC,MAAM,EAAE0E,WAAW,EAAE;YACpD,IAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC8B,KAAK,CAAC3E,CAAAA,MAAM,EAAE,CAAA,IAAI,CAAC,CAAC,GAAGyC,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;aACzC;YACD,IAAI,CAAC6B,WAAW,EAAEC,KAAK,CAAC3E,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;SACzC;QACD,SAAS4E,OAAO,CAACC,CAAC,EAAEC,QAAQ,EAAE;YAC5B,IAAID,CAAC,GAAGC,QAAQ,GAAG,CAAC,EAAE;gBACpBD,CAAC,IAAIC,QAAQ,GAAID,CAAC,GAAGC,QAAQ,AAAC;aAC/B;YACD,OAAOD,CAAC,CAAA;SACT;QACD,IAAI7E,OAAM,EACR2E,KAAK,EACLpC,MAAM,EACNmB,MAAM,EACNF,OAAO,EACPY,MAAM,EACNW,OAAO,EACPC,OAAO,EACPC,OAAO;QACT,SAASC,0BAA0B,CAACC,GAAG,EAAE;YACvCnF,OAAM,GAAGmF,GAAG;YACZlH,OAAM,CAAC,OAAO,CAAC,GAAG0G,KAAK,GAAG,IAAIS,SAAS,CAACD,GAAG,CAAC;YAC5ClH,OAAM,CAAC,QAAQ,CAAC,GAAGyF,MAAM,GAAG,IAAI2B,UAAU,CAACF,GAAG,CAAC;YAC/ClH,OAAM,CAAC,QAAQ,CAAC,GAAGmG,MAAM,GAAG,IAAIkB,UAAU,CAACH,GAAG,CAAC;YAC/ClH,OAAM,CAAC,QAAQ,CAAC,GAAGsE,MAAM,GAAG,IAAItC,UAAU,CAACkF,GAAG,CAAC;YAC/ClH,OAAM,CAAC,SAAS,CAAC,GAAGuF,OAAO,GAAG,IAAI+B,WAAW,CAACJ,GAAG,CAAC;YAClDlH,OAAM,CAAC,SAAS,CAAC,GAAG8G,OAAO,GAAG,IAAIS,WAAW,CAACL,GAAG,CAAC;YAClDlH,OAAM,CAAC,SAAS,CAAC,GAAG+G,OAAO,GAAG,IAAIS,YAAY,CAACN,GAAG,CAAC;YACnDlH,OAAM,CAAC,SAAS,CAAC,GAAGgH,OAAO,GAAG,IAAIS,YAAY,CAACP,GAAG,CAAC;SACpD;QACD,IAAIQ,cAAc,GAAG1H,OAAM,CAAC,gBAAgB,CAAC,IAAI,QAAQ;QACzD,IAAI2H,SAAS;QACb,IAAIC,YAAY,GAAG,EAAE;QACrB,IAAIC,UAAU,GAAG,EAAE;QACnB,IAAIC,aAAa,GAAG,EAAE;QACtB,IAAIC,kBAAkB,GAAG,KAAK;QAC9B,IAAIC,aAAa,GAAG,KAAK;QACzB,SAASC,MAAM,GAAG;YAChB,IAAIjI,OAAM,CAAC,QAAQ,CAAC,EAAE;gBACpB,IAAI,OAAOA,OAAM,CAAC,QAAQ,CAAC,IAAI,UAAU,EACvCA,OAAM,CAAC,QAAQ,CAAC,GAAG;oBAACA,OAAM,CAAC,QAAQ,CAAC;iBAAC;gBACvC,MAAOA,OAAM,CAAC,QAAQ,CAAC,CAACmC,MAAM,CAAE;oBAC9B+F,WAAW,CAAClI,OAAM,CAAC,QAAQ,CAAC,CAACmI,KAAK,EAAE,CAAC;iBACtC;aACF;YACDC,oBAAoB,CAACR,YAAY,CAAC;SACnC;QACD,SAASS,WAAW,GAAG;YACrBN,kBAAkB,GAAG,IAAI;YACzBK,oBAAoB,CAACP,UAAU,CAAC;SACjC;QACD,SAASS,WAAW,GAAG;YACrBN,aAAa,GAAG,IAAI;SACrB;QACD,SAASO,OAAO,GAAG;YACjB,IAAIvI,OAAM,CAAC,SAAS,CAAC,EAAE;gBACrB,IAAI,OAAOA,OAAM,CAAC,SAAS,CAAC,IAAI,UAAU,EACxCA,OAAM,CAAC,SAAS,CAAC,GAAG;oBAACA,OAAM,CAAC,SAAS,CAAC;iBAAC;gBACzC,MAAOA,OAAM,CAAC,SAAS,CAAC,CAACmC,MAAM,CAAE;oBAC/BqG,YAAY,CAACxI,OAAM,CAAC,SAAS,CAAC,CAACmI,KAAK,EAAE,CAAC;iBACxC;aACF;YACDC,oBAAoB,CAACN,aAAa,CAAC;SACpC;QACD,SAASI,WAAW,CAACO,EAAE,EAAE;YACvBb,YAAY,CAACc,OAAO,CAACD,EAAE,CAAC;SACzB;QACD,SAASE,SAAS,CAACF,EAAE,EAAE;YACrBZ,UAAU,CAACa,OAAO,CAACD,EAAE,CAAC;SACvB;QACD,SAASD,YAAY,CAACC,EAAE,EAAE;YACxBX,aAAa,CAACY,OAAO,CAACD,EAAE,CAAC;SAC1B;QACD,IAAIG,eAAe,GAAG,CAAC;QACvB,IAAIC,oBAAoB,GAAG,IAAI;QAC/B,IAAIC,qBAAqB,GAAG,IAAI;QAChC,SAASC,gBAAgB,CAACC,EAAE,EAAE;YAC5BJ,eAAe,EAAE;YACjB,IAAI5I,OAAM,CAAC,wBAAwB,CAAC,EAAE;gBACpCA,OAAM,CAAC,wBAAwB,CAAC,CAAC4I,eAAe,CAAC;aAClD;SACF;QACD,SAASK,mBAAmB,CAACD,EAAE,EAAE;YAC/BJ,eAAe,EAAE;YACjB,IAAI5I,OAAM,CAAC,wBAAwB,CAAC,EAAE;gBACpCA,OAAM,CAAC,wBAAwB,CAAC,CAAC4I,eAAe,CAAC;aAClD;YACD,IAAIA,eAAe,IAAI,CAAC,EAAE;gBACxB,IAAIC,oBAAoB,KAAK,IAAI,EAAE;oBACjCK,aAAa,CAACL,oBAAoB,CAAC;oBACnCA,oBAAoB,GAAG,IAAI;iBAC5B;gBACD,IAAIC,qBAAqB,EAAE;oBACzB,IAAIK,QAAQ,GAAGL,qBAAqB;oBACpCA,qBAAqB,GAAG,IAAI;oBAC5BK,QAAQ,EAAE;iBACX;aACF;SACF;QACDnJ,OAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE;QAC9BA,OAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE;QAC9B,SAASkD,KAAK,CAACkG,IAAI,EAAE;YACnB,IAAIpJ,OAAM,CAAC,SAAS,CAAC,EAAE;gBACrBA,OAAM,CAAC,SAAS,CAAC,CAACoJ,IAAI,CAAC;aACxB;YACDA,IAAI,IAAI,EAAE;YACV1G,IAAG,CAAC0G,IAAI,CAAC;YACThG,KAAK,GAAG,IAAI;YACZC,UAAU,GAAG,CAAC;YACd+F,IAAI,GAAG,QAAQ,GAAGA,IAAI,GAAG,8CAA8C;YACvE,IAAIC,CAAC,GAAG,IAAIpG,WAAW,CAACqG,YAAY,CAACF,IAAI,CAAC;YAC1ClJ,kBAAkB,CAACmJ,CAAC,CAAC;YACrB,MAAMA,CAAC,CAAA;SACR;QACD,IAAIE,aAAa,GAAG,uCAAuC;QAC3D,SAASC,SAAS,CAAC5H,QAAQ,EAAE;YAC3B,OAAOA,QAAQ,CAAC6H,UAAU,CAACF,aAAa,CAAC,CAAA;SAC1C;QACD,IAAIvJ,OAAM,CAAC,YAAY,CAAC,EAAE;YACxB,IAAI0J,cAAc,GAAG,uBAAuB;YAC5C,IAAI,CAACF,SAAS,CAACE,cAAc,CAAC,EAAE;gBAC9BA,cAAc,GAAGxI,UAAU,CAACwI,cAAc,CAAC;aAC5C;SACF,MAAM;YACL,MAAM,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAA;SAC7B;QACD,SAASC,SAAS,CAACC,IAAI,EAAE;YACvB,IAAI;gBACF,IAAIA,IAAI,IAAIH,cAAc,IAAI3G,UAAU,EAAE;oBACxC,OAAO,IAAIf,UAAU,CAACe,UAAU,CAAC,CAAA;iBAClC;gBACD,IAAI1B,UAAU,EAAE;oBACd,OAAOA,UAAU,CAACwI,IAAI,CAAC,CAAA;iBACxB,MAAM;oBACL,MAAM,iDAAiD,CAAA;iBACxD;aACF,CAAC,OAAOnH,GAAG,EAAE;gBACZQ,KAAK,CAACR,GAAG,CAAC;aACX;SACF;QACD,SAASoH,gBAAgB,GAAG;YAC1B,OAAO3J,OAAO,CAACC,OAAO,EAAE,CAAC2J,IAAI,CAAC,WAAY;gBACxC,OAAOH,SAAS,CAACF,cAAc,CAAC,CAAA;aACjC,CAAC,CAAA;SACH;QACD,SAASM,UAAU,GAAG;YACpB,IAAIC,IAAI,GAAG;gBAAEC,CAAC,EAAEC,aAAa;aAAE;YAC/B,SAASC,eAAe,CAACC,QAAQ,EAAEC,MAAM,EAAE;gBACzC,IAAIC,OAAO,GAAGF,QAAQ,CAACE,OAAO;gBAC9BvK,OAAM,CAAC,KAAK,CAAC,GAAGuK,OAAO;gBACvBpH,UAAU,GAAGnD,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;gBAC/BiH,0BAA0B,CAAC9D,UAAU,CAACpB,MAAM,CAAC;gBAC7C4F,SAAS,GAAG3H,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;gBAC9B2I,SAAS,CAAC3I,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC7BiJ,mBAAmB,CAAC,kBAAkB,CAAC;aACxC;YACDF,gBAAgB,CAAC,kBAAkB,CAAC;YACpC,SAASyB,0BAA0B,CAACC,MAAM,EAAE;gBAC1CL,eAAe,CAACK,MAAM,CAAC,UAAU,CAAC,CAAC;aACpC;YACD,SAASC,sBAAsB,CAACC,QAAQ,EAAE;gBACxC,OAAOb,gBAAgB,EAAE,CACtBC,IAAI,CAAC,SAAUlI,MAAM,EAAE;oBACtB,IAAI4I,MAAM,GAAGxH,WAAW,CAAC2H,WAAW,CAAC/I,MAAM,EAAEoI,IAAI,CAAC;oBAClD,OAAOQ,MAAM,CAAA;iBACd,CAAC,CACDV,IAAI,CAACY,QAAQ,EAAE,SAAUE,MAAM,EAAE;oBAChCnI,IAAG,CAAC,yCAAyC,GAAGmI,MAAM,CAAC;oBACvD3H,KAAK,CAAC2H,MAAM,CAAC;iBACd,CAAC,CAAA;aACL;YACD,SAASC,gBAAgB,GAAG;gBAC1B,OAAOJ,sBAAsB,CAACF,0BAA0B,CAAC,CAAA;aAC1D;YACD,IAAIxK,OAAM,CAAC,iBAAiB,CAAC,EAAE;gBAC7B,IAAI;oBACF,IAAIuK,QAAO,GAAGvK,OAAM,CAAC,iBAAiB,CAAC,CAACiK,IAAI,EAAEG,eAAe,CAAC;oBAC9D,OAAOG,QAAO,CAAA;iBACf,CAAC,OAAOlB,CAAC,EAAE;oBACV3G,IAAG,CAAC,qDAAqD,GAAG2G,CAAC,CAAC;oBAC9D,OAAO,KAAK,CAAA;iBACb;aACF;YACDyB,gBAAgB,EAAE,CAACC,KAAK,CAAC7K,kBAAkB,CAAC;YAC5C,OAAO,EAAE,CAAA;SACV;QACD,SAASkI,oBAAoB,CAAC4C,SAAS,EAAE;YACvC,MAAOA,SAAS,CAAC7I,MAAM,GAAG,CAAC,CAAE;gBAC3B,IAAIgH,QAAQ,GAAG6B,SAAS,CAAC7C,KAAK,EAAE;gBAChC,IAAI,OAAOgB,QAAQ,IAAI,UAAU,EAAE;oBACjCA,QAAQ,CAACnJ,OAAM,CAAC;oBAChB,SAAQ;iBACT;gBACD,IAAIiL,IAAI,GAAG9B,QAAQ,CAAC8B,IAAI;gBACxB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;oBAC5B,IAAI9B,QAAQ,CAAC+B,GAAG,KAAKrF,SAAS,EAAE;wBAC9B8B,SAAS,CAACwD,GAAG,CAACF,IAAI,CAAC,EAAE;qBACtB,MAAM;wBACLtD,SAAS,CAACwD,GAAG,CAACF,IAAI,CAAC,CAAC9B,QAAQ,CAAC+B,GAAG,CAAC;qBAClC;iBACF,MAAM;oBACLD,IAAI,CAAC9B,QAAQ,CAAC+B,GAAG,KAAKrF,SAAS,GAAG,IAAI,GAAGsD,QAAQ,CAAC+B,GAAG,CAAC;iBACvD;aACF;SACF;QACD,IAAIE,uBAAuB,GAAG,CAAC;QAC/B,SAASC,gBAAgB,GAAG;YAC1B,OAAOrI,aAAa,IAAIoI,uBAAuB,GAAG,CAAC,CAAA;SACpD;QACD,SAASE,OAAO,CAACL,IAAI,EAAEC,GAAG,EAAE,EAAE;QAC9B,SAASK,oBAAoB,CAACC,EAAE,EAAEC,EAAE,EAAE;YACpC,OAAOH,OAAO,CAACE,EAAE,EAAEC,EAAE,CAAC,CAAA;SACvB;QACD,SAASC,wBAAwB,CAC/BC,aAAa,EACbC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACR,EAAE;QACJ,SAASC,gBAAgB,CAACH,IAAI,EAAE;YAC9B,OAAQA,IAAI;gBACV,KAAK,CAAC;oBACJ,OAAO,CAAC,CAAA;gBACV,KAAK,CAAC;oBACJ,OAAO,CAAC,CAAA;gBACV,KAAK,CAAC;oBACJ,OAAO,CAAC,CAAA;gBACV,KAAK,CAAC;oBACJ,OAAO,CAAC,CAAA;gBACV;oBACE,MAAM,IAAII,SAAS,CAAC,qBAAqB,GAAGJ,IAAI,CAAC,CAAA;aACpD;SACF;QACD,SAASK,qBAAqB,GAAG;YAC/B,IAAIC,KAAK,GAAG,IAAIC,KAAK,CAAC,GAAG,CAAC;YAC1B,IAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,CAAE;gBAC5BuH,KAAK,CAACvH,CAAC,CAAC,GAAGc,MAAM,CAACC,YAAY,CAACf,CAAC,CAAC;aAClC;YACDyH,gBAAgB,GAAGF,KAAK;SACzB;QACD,IAAIE,gBAAgB,GAAGxG,SAAS;QAChC,SAASyG,gBAAgB,CAACnI,GAAG,EAAE;YAC7B,IAAIrC,GAAG,GAAG,EAAE;YACZ,IAAIyK,CAAC,GAAGpI,GAAG;YACX,MAAOG,MAAM,CAACiI,CAAC,CAAC,CAAE;gBAChBzK,GAAG,IAAIuK,gBAAgB,CAAC/H,MAAM,CAACiI,CAAC,EAAE,CAAC,CAAC;aACrC;YACD,OAAOzK,GAAG,CAAA;SACX;QACD,IAAI0K,oBAAoB,GAAG,EAAE;QAC7B,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIC,gBAAgB,GAAG,EAAE;QACzB,IAAIC,MAAM,GAAG,EAAE;QACf,IAAIC,MAAM,GAAG,EAAE;QACf,SAASC,qBAAqB,CAACjB,IAAI,EAAE;YACnC,IAAI/F,SAAS,KAAK+F,IAAI,EAAE;gBACtB,OAAO,UAAU,CAAA;aAClB;YACDA,IAAI,GAAGA,IAAI,CAACxJ,OAAO,mBAAmB,GAAG,CAAC;YAC1C,IAAI0K,CAAC,GAAGlB,IAAI,CAAC9G,UAAU,CAAC,CAAC,CAAC;YAC1B,IAAIgI,CAAC,IAAIH,MAAM,IAAIG,CAAC,IAAIF,MAAM,EAAE;gBAC9B,OAAO,GAAG,GAAGhB,IAAI,CAAA;aAClB,MAAM;gBACL,OAAOA,IAAI,CAAA;aACZ;SACF;QACD,SAASmB,mBAAmB,CAACnB,IAAI,EAAEoB,IAAI,EAAE;YACvCpB,IAAI,GAAGiB,qBAAqB,CAACjB,IAAI,CAAC;YAClC,OAAO,IAAIqB,QAAQ,CACjB,MAAM,EACN,kBAAkB,GAChBrB,IAAI,GACJ,QAAQ,GACR,mBAAmB,GACnB,2CAA2C,GAC3C,MAAM,CACT,CAACoB,IAAI,CAAC,CAAA;SACR;QACD,SAASE,WAAW,CAACC,aAAa,EAAEC,SAAS,EAAE;YAC7C,IAAIC,UAAU,GAAGN,mBAAmB,CAACK,SAAS,EAAE,SAAUE,OAAO,EAAE;gBACjE,IAAI,CAAC1B,IAAI,GAAGwB,SAAS;gBACrB,IAAI,CAACE,OAAO,GAAGA,OAAO;gBACtB,IAAIC,KAAK,GAAG,IAAI5D,KAAK,CAAC2D,OAAO,CAAC,CAACC,KAAK;gBACpC,IAAIA,KAAK,KAAK1H,SAAS,EAAE;oBACvB,IAAI,CAAC0H,KAAK,GACR,IAAI,CAACC,QAAQ,EAAE,GAAG,IAAI,GAAGD,KAAK,CAACnL,OAAO,uBAAuB,EAAE,CAAC;iBACnE;aACF,CAAC;YACFiL,UAAU,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACR,aAAa,CAACM,SAAS,CAAC;YAC7DJ,UAAU,CAACI,SAAS,CAACG,WAAW,GAAGP,UAAU;YAC7CA,UAAU,CAACI,SAAS,CAACD,QAAQ,GAAG,WAAY;gBAC1C,IAAI,IAAI,CAACF,OAAO,KAAKzH,SAAS,EAAE;oBAC9B,OAAO,IAAI,CAAC+F,IAAI,CAAA;iBACjB,MAAM;oBACL,OAAO,IAAI,CAACA,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC0B,OAAO,CAAA;iBACvC;aACF;YACD,OAAOD,UAAU,CAAA;SAClB;QACD,IAAIQ,YAAY,GAAGhI,SAAS;QAC5B,SAASiI,iBAAiB,CAACR,OAAO,EAAE;YAClC,MAAM,IAAIO,YAAY,CAACP,OAAO,CAAC,CAAA;SAChC;QACD,IAAIS,aAAa,GAAGlI,SAAS;QAC7B,SAASmI,kBAAkB,CAACV,OAAO,EAAE;YACnC,MAAM,IAAIS,aAAa,CAACT,OAAO,CAAC,CAAA;SACjC;QACD,SAASW,6BAA6B,CACpCC,OAAO,EACPC,cAAc,EACdC,iBAAiB,EACjB;YACAF,OAAO,CAACG,OAAO,CAAC,SAAUC,IAAI,EAAE;gBAC9B5B,gBAAgB,CAAC4B,IAAI,CAAC,GAAGH,cAAc;aACxC,CAAC;YACF,SAASI,UAAU,CAACC,cAAc,EAAE;gBAClC,IAAIC,gBAAgB,GAAGL,iBAAiB,CAACI,cAAc,CAAC;gBACxD,IAAIC,gBAAgB,CAACtM,MAAM,KAAK+L,OAAO,CAAC/L,MAAM,EAAE;oBAC9C6L,kBAAkB,CAAC,iCAAiC,CAAC;iBACtD;gBACD,IAAK,IAAIpJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsJ,OAAO,CAAC/L,MAAM,EAAE,EAAEyC,CAAC,CAAE;oBACvC8J,YAAY,CAACR,OAAO,CAACtJ,CAAC,CAAC,EAAE6J,gBAAgB,CAAC7J,CAAC,CAAC,CAAC;iBAC9C;aACF;YACD,IAAI4J,eAAc,GAAG,IAAIpC,KAAK,CAAC+B,cAAc,CAAChM,MAAM,CAAC;YACrD,IAAIwM,iBAAiB,GAAG,EAAE;YAC1B,IAAIC,UAAU,GAAG,CAAC;YAClBT,cAAc,CAACE,OAAO,CAAC,SAAUQ,EAAE,EAAEjK,CAAC,EAAE;gBACtC,IAAI6H,eAAe,CAACjM,cAAc,CAACqO,EAAE,CAAC,EAAE;oBACtCL,eAAc,CAAC5J,CAAC,CAAC,GAAG6H,eAAe,CAACoC,EAAE,CAAC;iBACxC,MAAM;oBACLF,iBAAiB,CAACG,IAAI,CAACD,EAAE,CAAC;oBAC1B,IAAI,CAACrC,oBAAoB,CAAChM,cAAc,CAACqO,EAAE,CAAC,EAAE;wBAC5CrC,oBAAoB,CAACqC,EAAE,CAAC,GAAG,EAAE;qBAC9B;oBACDrC,oBAAoB,CAACqC,EAAE,CAAC,CAACC,IAAI,CAAC,WAAY;wBACxCN,eAAc,CAAC5J,CAAC,CAAC,GAAG6H,eAAe,CAACoC,EAAE,CAAC;wBACvC,EAAED,UAAU;wBACZ,IAAIA,UAAU,KAAKD,iBAAiB,CAACxM,MAAM,EAAE;4BAC3CoM,UAAU,CAACC,eAAc,CAAC;yBAC3B;qBACF,CAAC;iBACH;aACF,CAAC;YACF,IAAI,CAAC,KAAKG,iBAAiB,CAACxM,MAAM,EAAE;gBAClCoM,UAAU,CAACC,eAAc,CAAC;aAC3B;SACF;QACD,SAASE,YAAY,CAACK,OAAO,EAAEC,kBAAkB,EAAEC,OAAO,EAAE;YAC1DA,OAAO,GAAGA,OAAO,IAAI,EAAE;YACvB,IAAI,CAAC,CAAC,gBAAgB,IAAID,kBAAkB,CAAC,EAAE;gBAC7C,MAAM,IAAI/C,SAAS,CACjB,yDAAyD,CAC1D,CAAA;aACF;YACD,IAAIL,IAAI,GAAGoD,kBAAkB,CAACpD,IAAI;YAClC,IAAI,CAACmD,OAAO,EAAE;gBACZjB,iBAAiB,CACf,QAAQ,GAAGlC,IAAI,GAAG,+CAA+C,CAClE;aACF;YACD,IAAIa,eAAe,CAACjM,cAAc,CAACuO,OAAO,CAAC,EAAE;gBAC3C,IAAIE,OAAO,CAACC,4BAA4B,EAAE;oBACxC,OAAM;iBACP,MAAM;oBACLpB,iBAAiB,CAAC,wBAAwB,GAAGlC,IAAI,GAAG,SAAS,CAAC;iBAC/D;aACF;YACDa,eAAe,CAACsC,OAAO,CAAC,GAAGC,kBAAkB;YAC7C,OAAOtC,gBAAgB,CAACqC,OAAO,CAAC;YAChC,IAAIvC,oBAAoB,CAAChM,cAAc,CAACuO,OAAO,CAAC,EAAE;gBAChD,IAAI/D,SAAS,GAAGwB,oBAAoB,CAACuC,OAAO,CAAC;gBAC7C,OAAOvC,oBAAoB,CAACuC,OAAO,CAAC;gBACpC/D,SAAS,CAACqD,OAAO,CAAC,SAAU5F,EAAE,EAAE;oBAC9BA,EAAE,EAAE;iBACL,CAAC;aACH;SACF;QACD,SAAS0G,sBAAsB,CAC7BJ,OAAO,EACPnD,IAAI,EACJC,IAAI,EACJuD,SAAS,EACTC,UAAU,EACV;YACA,IAAIlH,KAAK,GAAG6D,gBAAgB,CAACH,IAAI,CAAC;YAClCD,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7B8C,YAAY,CAACK,OAAO,EAAE;gBACpBnD,IAAI,EAAEA,IAAI;gBACV0D,YAAY,EAAE,SAAUC,EAAE,EAAE;oBAC1B,OAAO,CAAC,CAACA,EAAE,CAAA;iBACZ;gBACDC,UAAU,EAAE,SAAUC,WAAW,EAAEC,CAAC,EAAE;oBACpC,OAAOA,CAAC,GAAGN,SAAS,GAAGC,UAAU,CAAA;iBAClC;gBACDM,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAE,SAAUC,OAAO,EAAE;oBACvC,IAAIlM,IAAI;oBACR,IAAIkI,IAAI,KAAK,CAAC,EAAE;wBACdlI,IAAI,GAAG+C,KAAK;qBACb,MAAM,IAAImF,IAAI,KAAK,CAAC,EAAE;wBACrBlI,IAAI,GAAG8B,MAAM;qBACd,MAAM,IAAIoG,IAAI,KAAK,CAAC,EAAE;wBACrBlI,IAAI,GAAGwC,MAAM;qBACd,MAAM;wBACL,MAAM,IAAI8F,SAAS,CAAC,6BAA6B,GAAGL,IAAI,CAAC,CAAA;qBAC1D;oBACD,OAAO,IAAI,CAAC,cAAc,CAAC,CAACjI,IAAI,CAACkM,OAAO,IAAI1H,KAAK,CAAC,CAAC,CAAA;iBACpD;gBACD2H,kBAAkB,EAAE,IAAI;aACzB,CAAC;SACH;QACD,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIC,kBAAkB,GAAG;YACvB,EAAE;YACF;gBAAElN,KAAK,EAAE+C,SAAS;aAAE;YACpB;gBAAE/C,KAAK,EAAE,IAAI;aAAE;YACf;gBAAEA,KAAK,EAAE,IAAI;aAAE;YACf;gBAAEA,KAAK,EAAE,KAAK;aAAE;SACjB;QACD,SAASmN,cAAc,CAACC,MAAM,EAAE;YAC9B,IAAIA,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,EAAEF,kBAAkB,CAACE,MAAM,CAAC,CAACC,QAAQ,EAAE;gBAC7DH,kBAAkB,CAACE,MAAM,CAAC,GAAGrK,SAAS;gBACtCkK,eAAe,CAACjB,IAAI,CAACoB,MAAM,CAAC;aAC7B;SACF;QACD,SAASE,mBAAmB,GAAG;YAC7B,IAAIC,KAAK,GAAG,CAAC;YACb,IAAK,IAAIzL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoL,kBAAkB,CAAC7N,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBAClD,IAAIoL,kBAAkB,CAACpL,CAAC,CAAC,KAAKiB,SAAS,EAAE;oBACvC,EAAEwK,KAAK;iBACR;aACF;YACD,OAAOA,KAAK,CAAA;SACb;QACD,SAASC,eAAe,GAAG;YACzB,IAAK,IAAI1L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoL,kBAAkB,CAAC7N,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBAClD,IAAIoL,kBAAkB,CAACpL,CAAC,CAAC,KAAKiB,SAAS,EAAE;oBACvC,OAAOmK,kBAAkB,CAACpL,CAAC,CAAC,CAAA;iBAC7B;aACF;YACD,OAAO,IAAI,CAAA;SACZ;QACD,SAAS2L,UAAU,GAAG;YACpBvQ,OAAM,CAAC,qBAAqB,CAAC,GAAGoQ,mBAAmB;YACnDpQ,OAAM,CAAC,iBAAiB,CAAC,GAAGsQ,eAAe;SAC5C;QACD,SAASE,gBAAgB,CAAC1N,KAAK,EAAE;YAC/B,OAAQA,KAAK;gBACX,KAAK+C,SAAS;oBAAE;wBACd,OAAO,CAAC,CAAA;qBACT;gBACD,KAAK,IAAI;oBAAE;wBACT,OAAO,CAAC,CAAA;qBACT;gBACD,KAAK,IAAI;oBAAE;wBACT,OAAO,CAAC,CAAA;qBACT;gBACD,KAAK,KAAK;oBAAE;wBACV,OAAO,CAAC,CAAA;qBACT;gBACD;oBAAS;wBACP,IAAIqK,MAAM,GAAGH,eAAe,CAAC5N,MAAM,GAC/B4N,eAAe,CAACU,GAAG,EAAE,GACrBT,kBAAkB,CAAC7N,MAAM;wBAC7B6N,kBAAkB,CAACE,MAAM,CAAC,GAAG;4BAAEC,QAAQ,EAAE,CAAC;4BAAErN,KAAK,EAAEA,KAAK;yBAAE;wBAC1D,OAAOoN,MAAM,CAAA;qBACd;aACF;SACF;QACD,SAASQ,0BAA0B,CAACb,OAAO,EAAE;YAC3C,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC/I,OAAO,CAAC+I,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;SACnD;QACD,SAASc,uBAAuB,CAAC5B,OAAO,EAAEnD,IAAI,EAAE;YAC9CA,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7B8C,YAAY,CAACK,OAAO,EAAE;gBACpBnD,IAAI,EAAEA,IAAI;gBACV0D,YAAY,EAAE,SAAUY,MAAM,EAAE;oBAC9B,IAAIU,EAAE,GAAGZ,kBAAkB,CAACE,MAAM,CAAC,CAACpN,KAAK;oBACzCmN,cAAc,CAACC,MAAM,CAAC;oBACtB,OAAOU,EAAE,CAAA;iBACV;gBACDpB,UAAU,EAAE,SAAUC,WAAW,EAAE3M,KAAK,EAAE;oBACxC,OAAO0N,gBAAgB,CAAC1N,KAAK,CAAC,CAAA;iBAC/B;gBACD6M,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEc,0BAA0B;gBAChDZ,kBAAkB,EAAE,IAAI;aACzB,CAAC;SACH;QACD,SAASe,YAAY,CAACC,CAAC,EAAE;YACvB,IAAIA,CAAC,KAAK,IAAI,EAAE;gBACd,OAAO,MAAM,CAAA;aACd;YACD,IAAIC,CAAC,GAAG,OAAOD,CAAC;YAChB,IAAIC,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,OAAO,IAAIA,CAAC,KAAK,UAAU,EAAE;gBACvD,OAAOD,CAAC,CAACtD,QAAQ,EAAE,CAAA;aACpB,MAAM;gBACL,OAAO,EAAE,GAAGsD,CAAC,CAAA;aACd;SACF;QACD,SAASE,yBAAyB,CAACpF,IAAI,EAAEzD,KAAK,EAAE;YAC9C,OAAQA,KAAK;gBACX,KAAK,CAAC;oBACJ,OAAO,SAAU0H,OAAO,EAAE;wBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC9I,OAAO,CAAC8I,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;qBACnD,CAAA;gBACH,KAAK,CAAC;oBACJ,OAAO,SAAUA,OAAO,EAAE;wBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC7I,OAAO,CAAC6I,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;qBACnD,CAAA;gBACH;oBACE,MAAM,IAAI5D,SAAS,CAAC,sBAAsB,GAAGL,IAAI,CAAC,CAAA;aACrD;SACF;QACD,SAASqF,uBAAuB,CAAClC,OAAO,EAAEnD,IAAI,EAAEC,IAAI,EAAE;YACpD,IAAI1D,KAAK,GAAG6D,gBAAgB,CAACH,IAAI,CAAC;YAClCD,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7B8C,YAAY,CAACK,OAAO,EAAE;gBACpBnD,IAAI,EAAEA,IAAI;gBACV0D,YAAY,EAAE,SAAUxM,KAAK,EAAE;oBAC7B,OAAOA,KAAK,CAAA;iBACb;gBACD0M,UAAU,EAAE,SAAUC,WAAW,EAAE3M,KAAK,EAAE;oBACxC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;wBAC3D,MAAM,IAAImJ,SAAS,CACjB,kBAAkB,GAAG4E,YAAY,CAAC/N,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC8I,IAAI,CAC/D,CAAA;qBACF;oBACD,OAAO9I,KAAK,CAAA;iBACb;gBACD6M,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEoB,yBAAyB,CAACpF,IAAI,EAAEzD,KAAK,CAAC;gBAC5D2H,kBAAkB,EAAE,IAAI;aACzB,CAAC;SACH;QACD,SAASoB,IAAI,CAACtD,WAAW,EAAEuD,YAAY,EAAE;YACvC,IAAI,CAAC,CAACvD,WAAW,YAAYX,QAAQ,CAAC,EAAE;gBACtC,MAAM,IAAIhB,SAAS,CACjB,oCAAoC,GAClC,OAAO2B,WAAW,GAClB,0BAA0B,CAC7B,CAAA;aACF;YACD,IAAIwD,KAAK,GAAGrE,mBAAmB,CAC7Ba,WAAW,CAAChC,IAAI,IAAI,qBAAqB,EACzC,WAAY,EAAE,CACf;YACDwF,KAAK,CAAC3D,SAAS,GAAGG,WAAW,CAACH,SAAS;YACvC,IAAI4D,GAAG,GAAG,IAAID,KAAK,EAAE;YACrB,IAAIE,CAAC,GAAG1D,WAAW,CAAC2D,KAAK,CAACF,GAAG,EAAEF,YAAY,CAAC;YAC5C,OAAOG,CAAC,YAAY5D,MAAM,GAAG4D,CAAC,GAAGD,GAAG,CAAA;SACrC;QACD,SAASG,cAAc,CAAC/B,WAAW,EAAE;YACnC,MAAOA,WAAW,CAACtN,MAAM,CAAE;gBACzB,IAAIgC,GAAG,GAAGsL,WAAW,CAACgB,GAAG,EAAE;gBAC3B,IAAIgB,GAAG,GAAGhC,WAAW,CAACgB,GAAG,EAAE;gBAC3BgB,GAAG,CAACtN,GAAG,CAAC;aACT;SACF;QACD,SAASuN,oBAAoB,CAC3BC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,aAAa,EACb;YACA,IAAIC,QAAQ,GAAGJ,QAAQ,CAACzP,MAAM;YAC9B,IAAI6P,QAAQ,GAAG,CAAC,EAAE;gBAChBlE,iBAAiB,CACf,gFAAgF,CACjF;aACF;YACD,IAAImE,iBAAiB,GAAGL,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIC,SAAS,KAAK,IAAI;YAClE,IAAIK,oBAAoB,GAAG,KAAK;YAChC,IAAK,IAAItN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgN,QAAQ,CAACzP,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACxC,IACEgN,QAAQ,CAAChN,CAAC,CAAC,KAAK,IAAI,IACpBgN,QAAQ,CAAChN,CAAC,CAAC,CAACkL,kBAAkB,KAAKjK,SAAS,EAC5C;oBACAqM,oBAAoB,GAAG,IAAI;oBAC3B,MAAK;iBACN;aACF;YACD,IAAIC,OAAO,GAAGP,QAAQ,CAAC,CAAC,CAAC,CAAChG,IAAI,KAAK,MAAM;YACzC,IAAIwG,QAAQ,GAAG,EAAE;YACjB,IAAIC,aAAa,GAAG,EAAE;YACtB,IAAK,IAAIzN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoN,QAAQ,GAAG,CAAC,EAAE,EAAEpN,CAAC,CAAE;gBACrCwN,QAAQ,IAAI,CAACxN,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK,GAAGA,CAAC;gBAC7CyN,aAAa,IAAI,CAACzN,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAG,OAAO;aAC7D;YACD,IAAI0N,aAAa,GACf,kBAAkB,GAClBzF,qBAAqB,CAAC8E,SAAS,CAAC,GAChC,GAAG,GACHS,QAAQ,GACR,OAAO,GACP,2BAA2B,GAC3B,CAACJ,QAAQ,GAAG,CAAC,CAAC,GACd,OAAO,GACP,8BAA8B,GAC9BL,SAAS,GACT,4DAA4D,GAC5D,CAACK,QAAQ,GAAG,CAAC,CAAC,GACd,aAAa,GACb,KAAK;YACP,IAAIE,oBAAoB,EAAE;gBACxBI,aAAa,IAAI,yBAAyB;aAC3C;YACD,IAAIC,SAAS,GAAGL,oBAAoB,GAAG,aAAa,GAAG,MAAM;YAC7D,IAAIM,KAAK,GAAG;gBACV,mBAAmB;gBACnB,SAAS;gBACT,IAAI;gBACJ,gBAAgB;gBAChB,SAAS;gBACT,YAAY;aACb;YACD,IAAIC,KAAK,GAAG;gBACV3E,iBAAiB;gBACjBgE,cAAc;gBACdC,aAAa;gBACbP,cAAc;gBACdI,QAAQ,CAAC,CAAC,CAAC;gBACXA,QAAQ,CAAC,CAAC,CAAC;aACZ;YACD,IAAIK,iBAAiB,EAAE;gBACrBK,aAAa,IACX,wCAAwC,GAAGC,SAAS,GAAG,YAAY;aACtE;YACD,IAAK,IAAI3N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoN,QAAQ,GAAG,CAAC,EAAE,EAAEpN,CAAC,CAAE;gBACrC0N,aAAa,IACX,SAAS,GACT1N,CAAC,GACD,iBAAiB,GACjBA,CAAC,GACD,cAAc,GACd2N,SAAS,GACT,OAAO,GACP3N,CAAC,GACD,QAAQ,GACRgN,QAAQ,CAAChN,CAAC,GAAG,CAAC,CAAC,CAACgH,IAAI,GACpB,IAAI;gBACN4G,KAAK,CAAC1D,IAAI,CAAC,SAAS,GAAGlK,CAAC,CAAC;gBACzB6N,KAAK,CAAC3D,IAAI,CAAC8C,QAAQ,CAAChN,CAAC,GAAG,CAAC,CAAC,CAAC;aAC5B;YACD,IAAIqN,iBAAiB,EAAE;gBACrBI,aAAa,GACX,WAAW,GAAG,CAACA,aAAa,CAAClQ,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GAAGkQ,aAAa;aACvE;YACDC,aAAa,IACX,CAACH,OAAO,GAAG,WAAW,GAAG,EAAE,CAAC,GAC5B,YAAY,GACZ,CAACE,aAAa,CAAClQ,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GACtCkQ,aAAa,GACb,MAAM;YACR,IAAIH,oBAAoB,EAAE;gBACxBI,aAAa,IAAI,gCAAgC;aAClD,MAAM;gBACL,IAAK,IAAI1N,CAAC,GAAGqN,iBAAiB,GAAG,CAAC,GAAG,CAAC,EAAErN,CAAC,GAAGgN,QAAQ,CAACzP,MAAM,EAAE,EAAEyC,CAAC,CAAE;oBAChE,IAAI8N,SAAS,GAAG9N,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,KAAK,GAAG,CAACA,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;oBACjE,IAAIgN,QAAQ,CAAChN,CAAC,CAAC,CAACkL,kBAAkB,KAAK,IAAI,EAAE;wBAC3CwC,aAAa,IACXI,SAAS,GACT,QAAQ,GACRA,SAAS,GACT,QAAQ,GACRd,QAAQ,CAAChN,CAAC,CAAC,CAACgH,IAAI,GAChB,IAAI;wBACN4G,KAAK,CAAC1D,IAAI,CAAC4D,SAAS,GAAG,OAAO,CAAC;wBAC/BD,KAAK,CAAC3D,IAAI,CAAC8C,QAAQ,CAAChN,CAAC,CAAC,CAACkL,kBAAkB,CAAC;qBAC3C;iBACF;aACF;YACD,IAAIqC,OAAO,EAAE;gBACXG,aAAa,IACX,uCAAuC,GAAG,eAAe;aAC5D,MAAM,EACN;YACDA,aAAa,IAAI,KAAK;YACtBE,KAAK,CAAC1D,IAAI,CAACwD,aAAa,CAAC;YACzB,IAAIK,eAAe,GAAGzB,IAAI,CAACjE,QAAQ,EAAEuF,KAAK,CAAC,CAACjB,KAAK,CAAC,IAAI,EAAEkB,KAAK,CAAC;YAC9D,OAAOE,eAAe,CAAA;SACvB;QACD,SAASC,mBAAmB,CAACC,KAAK,EAAEC,UAAU,EAAEnB,SAAS,EAAE;YACzD,IAAI9L,SAAS,KAAKgN,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,EAAE;gBACjD,IAAIC,QAAQ,GAAGH,KAAK,CAACC,UAAU,CAAC;gBAChCD,KAAK,CAACC,UAAU,CAAC,GAAG,WAAY;oBAC9B,IACE,CAACD,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,CAACvS,cAAc,CAACyS,SAAS,CAAC9Q,MAAM,CAAC,EACjE;wBACA2L,iBAAiB,CACf,YAAY,GACV6D,SAAS,GACT,gDAAgD,GAChDsB,SAAS,CAAC9Q,MAAM,GAChB,sBAAsB,GACtB0Q,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,GAC/B,IAAI,CACP;qBACF;oBACD,OAAOF,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,CAACE,SAAS,CAAC9Q,MAAM,CAAC,CAACoP,KAAK,CAC5D,IAAI,EACJ0B,SAAS,CACV,CAAA;iBACF;gBACDJ,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,GAAG,EAAE;gBACpCF,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,CAACC,QAAQ,CAAChB,QAAQ,CAAC,GAAGgB,QAAQ;aAC9D;SACF;QACD,SAASE,kBAAkB,CAACtH,IAAI,EAAE9I,KAAK,EAAEqQ,YAAY,EAAE;YACrD,IAAInT,OAAM,CAACQ,cAAc,CAACoL,IAAI,CAAC,EAAE;gBAC/B,IACE/F,SAAS,KAAKsN,YAAY,IACzBtN,SAAS,KAAK7F,OAAM,CAAC4L,IAAI,CAAC,CAACmH,aAAa,IACvClN,SAAS,KAAK7F,OAAM,CAAC4L,IAAI,CAAC,CAACmH,aAAa,CAACI,YAAY,CAAC,AAAC,EACzD;oBACArF,iBAAiB,CAAC,+BAA+B,GAAGlC,IAAI,GAAG,SAAS,CAAC;iBACtE;gBACDgH,mBAAmB,CAAC5S,OAAM,EAAE4L,IAAI,EAAEA,IAAI,CAAC;gBACvC,IAAI5L,OAAM,CAACQ,cAAc,CAAC2S,YAAY,CAAC,EAAE;oBACvCrF,iBAAiB,CACf,sFAAsF,GACpFqF,YAAY,GACZ,IAAI,CACP;iBACF;gBACDnT,OAAM,CAAC4L,IAAI,CAAC,CAACmH,aAAa,CAACI,YAAY,CAAC,GAAGrQ,KAAK;aACjD,MAAM;gBACL9C,OAAM,CAAC4L,IAAI,CAAC,GAAG9I,KAAK;gBACpB,IAAI+C,SAAS,KAAKsN,YAAY,EAAE;oBAC9BnT,OAAM,CAAC4L,IAAI,CAAC,CAACuH,YAAY,GAAGA,YAAY;iBACzC;aACF;SACF;QACD,SAASC,mBAAmB,CAAC/C,KAAK,EAAEgD,YAAY,EAAE;YAChD,IAAIC,KAAK,GAAG,EAAE;YACd,IAAK,IAAI1O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyL,KAAK,EAAEzL,CAAC,EAAE,CAAE;gBAC9B0O,KAAK,CAACxE,IAAI,CAAC3I,MAAM,CAAC,CAACkN,YAAY,IAAI,CAAC,CAAC,GAAGzO,CAAC,CAAC,CAAC;aAC5C;YACD,OAAO0O,KAAK,CAAA;SACb;QACD,SAASC,mBAAmB,CAAC3H,IAAI,EAAE9I,KAAK,EAAEqQ,YAAY,EAAE;YACtD,IAAI,CAACnT,OAAM,CAACQ,cAAc,CAACoL,IAAI,CAAC,EAAE;gBAChCoC,kBAAkB,CAAC,qCAAqC,CAAC;aAC1D;YACD,IACEnI,SAAS,KAAK7F,OAAM,CAAC4L,IAAI,CAAC,CAACmH,aAAa,IACxClN,SAAS,KAAKsN,YAAY,EAC1B;gBACAnT,OAAM,CAAC4L,IAAI,CAAC,CAACmH,aAAa,CAACI,YAAY,CAAC,GAAGrQ,KAAK;aACjD,MAAM;gBACL9C,OAAM,CAAC4L,IAAI,CAAC,GAAG9I,KAAK;gBACpB9C,OAAM,CAAC4L,IAAI,CAAC,CAACoG,QAAQ,GAAGmB,YAAY;aACrC;SACF;QACD,SAASK,aAAa,CAACC,GAAG,EAAEtP,GAAG,EAAEuP,IAAI,EAAE;YACrC,IAAI5G,CAAC,GAAG9M,OAAM,CAAC,UAAU,GAAGyT,GAAG,CAAC;YAChC,OAAOC,IAAI,IAAIA,IAAI,CAACvR,MAAM,GACtB2K,CAAC,CAACyE,KAAK,CAAC,IAAI,EAAE;gBAACpN,GAAG;aAAC,CAACwP,MAAM,CAACD,IAAI,CAAC,CAAC,GACjC5G,CAAC,CAAC8G,IAAI,CAAC,IAAI,EAAEzP,GAAG,CAAC,CAAA;SACtB;QACD,SAAS0P,OAAO,CAACJ,GAAG,EAAEtP,GAAG,EAAEuP,IAAI,EAAE;YAC/B,IAAID,GAAG,CAACK,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACrB,OAAON,aAAa,CAACC,GAAG,EAAEtP,GAAG,EAAEuP,IAAI,CAAC,CAAA;aACrC;YACD,OAAO/L,SAAS,CAACwD,GAAG,CAAChH,GAAG,CAAC,CAACoN,KAAK,CAAC,IAAI,EAAEmC,IAAI,CAAC,CAAA;SAC5C;QACD,SAASK,YAAY,CAACN,GAAG,EAAEtP,GAAG,EAAE;YAC9B,IAAI6P,QAAQ,GAAG,EAAE;YACjB,OAAO,WAAY;gBACjBA,QAAQ,CAAC7R,MAAM,GAAG8Q,SAAS,CAAC9Q,MAAM;gBAClC,IAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,SAAS,CAAC9Q,MAAM,EAAEyC,CAAC,EAAE,CAAE;oBACzCoP,QAAQ,CAACpP,CAAC,CAAC,GAAGqO,SAAS,CAACrO,CAAC,CAAC;iBAC3B;gBACD,OAAOiP,OAAO,CAACJ,GAAG,EAAEtP,GAAG,EAAE6P,QAAQ,CAAC,CAAA;aACnC,CAAA;SACF;QACD,SAASC,uBAAuB,CAACC,SAAS,EAAEC,WAAW,EAAE;YACvDD,SAAS,GAAG5H,gBAAgB,CAAC4H,SAAS,CAAC;YACvC,SAASE,aAAa,GAAG;gBACvB,IAAIF,SAAS,CAACJ,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC3B,OAAOC,YAAY,CAACG,SAAS,EAAEC,WAAW,CAAC,CAAA;iBAC5C;gBACD,OAAOxM,SAAS,CAACwD,GAAG,CAACgJ,WAAW,CAAC,CAAA;aAClC;YACD,IAAIE,EAAE,GAAGD,aAAa,EAAE;YACxB,IAAI,OAAOC,EAAE,KAAK,UAAU,EAAE;gBAC5BvG,iBAAiB,CACf,0CAA0C,GACxCoG,SAAS,GACT,IAAI,GACJC,WAAW,CACd;aACF;YACD,OAAOE,EAAE,CAAA;SACV;QACD,IAAIC,gBAAgB,GAAGzO,SAAS;QAChC,SAAS0O,WAAW,CAACjG,IAAI,EAAE;YACzB,IAAInK,GAAG,GAAGqQ,cAAc,CAAClG,IAAI,CAAC;YAC9B,IAAIsC,EAAE,GAAGtE,gBAAgB,CAACnI,GAAG,CAAC;YAC9BsQ,KAAK,CAACtQ,GAAG,CAAC;YACV,OAAOyM,EAAE,CAAA;SACV;QACD,SAAS8D,qBAAqB,CAACpH,OAAO,EAAEqH,KAAK,EAAE;YAC7C,IAAIC,YAAY,GAAG,EAAE;YACrB,IAAIC,IAAI,GAAG,EAAE;YACb,SAASC,KAAK,CAACxG,IAAI,EAAE;gBACnB,IAAIuG,IAAI,CAACvG,IAAI,CAAC,EAAE;oBACd,OAAM;iBACP;gBACD,IAAI7B,eAAe,CAAC6B,IAAI,CAAC,EAAE;oBACzB,OAAM;iBACP;gBACD,IAAI5B,gBAAgB,CAAC4B,IAAI,CAAC,EAAE;oBAC1B5B,gBAAgB,CAAC4B,IAAI,CAAC,CAACD,OAAO,CAACyG,KAAK,CAAC;oBACrC,OAAM;iBACP;gBACDF,YAAY,CAAC9F,IAAI,CAACR,IAAI,CAAC;gBACvBuG,IAAI,CAACvG,IAAI,CAAC,GAAG,IAAI;aAClB;YACDqG,KAAK,CAACtG,OAAO,CAACyG,KAAK,CAAC;YACpB,MAAM,IAAIR,gBAAgB,CACxBhH,OAAO,GAAG,IAAI,GAAGsH,YAAY,CAACG,GAAG,CAACR,WAAW,CAAC,CAACS,IAAI,CAAC;gBAAC,IAAI;aAAC,CAAC,CAC5D,CAAA;SACF;QACD,SAASC,0BAA0B,CACjCrJ,IAAI,EACJoG,QAAQ,EACRkD,eAAe,EACfhB,SAAS,EACTiB,UAAU,EACVC,EAAE,EACF;YACA,IAAIxD,SAAQ,GAAGwB,mBAAmB,CAACpB,QAAQ,EAAEkD,eAAe,CAAC;YAC7DtJ,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7BuJ,UAAU,GAAGlB,uBAAuB,CAACC,SAAS,EAAEiB,UAAU,CAAC;YAC3DjC,kBAAkB,CAChBtH,IAAI,EACJ,WAAY;gBACV8I,qBAAqB,CACnB,cAAc,GAAG9I,IAAI,GAAG,uBAAuB,EAC/CgG,SAAQ,CACT;aACF,EACDI,QAAQ,GAAG,CAAC,CACb;YACD/D,6BAA6B,CAAC,EAAE,EAAE2D,SAAQ,EAAE,SAAUA,QAAQ,EAAE;gBAC9D,IAAIyD,gBAAgB,GAAG;oBAACzD,QAAQ,CAAC,CAAC,CAAC;oBAAE,IAAI;iBAAC,CAAC+B,MAAM,CAAC/B,QAAQ,CAACvP,KAAK,CAAC,CAAC,CAAC,CAAC;gBACpEkR,mBAAmB,CACjB3H,IAAI,EACJ8F,oBAAoB,CAAC9F,IAAI,EAAEyJ,gBAAgB,EAAE,IAAI,EAAEF,UAAU,EAAEC,EAAE,CAAC,EAClEpD,QAAQ,GAAG,CAAC,CACb;gBACD,OAAO,EAAE,CAAA;aACV,CAAC;SACH;QACD,SAASsD,2BAA2B,CAAC1J,IAAI,EAAEzD,KAAK,EAAEoN,MAAM,EAAE;YACxD,OAAQpN,KAAK;gBACX,KAAK,CAAC;oBACJ,OAAOoN,MAAM,GACT,SAASC,iBAAiB,CAAC3F,OAAO,EAAE;wBAClC,OAAOnJ,KAAK,CAACmJ,OAAO,CAAC,CAAA;qBACtB,GACD,SAAS4F,iBAAiB,CAAC5F,OAAO,EAAE;wBAClC,OAAOvL,MAAM,CAACuL,OAAO,CAAC,CAAA;qBACvB,CAAA;gBACP,KAAK,CAAC;oBACJ,OAAO0F,MAAM,GACT,SAASG,kBAAkB,CAAC7F,OAAO,EAAE;wBACnC,OAAOpK,MAAM,CAACoK,OAAO,IAAI,CAAC,CAAC,CAAA;qBAC5B,GACD,SAAS8F,kBAAkB,CAAC9F,OAAO,EAAE;wBACnC,OAAOtK,OAAO,CAACsK,OAAO,IAAI,CAAC,CAAC,CAAA;qBAC7B,CAAA;gBACP,KAAK,CAAC;oBACJ,OAAO0F,MAAM,GACT,SAASK,kBAAkB,CAAC/F,OAAO,EAAE;wBACnC,OAAO1J,MAAM,CAAC0J,OAAO,IAAI,CAAC,CAAC,CAAA;qBAC5B,GACD,SAASgG,kBAAkB,CAAChG,OAAO,EAAE;wBACnC,OAAO/I,OAAO,CAAC+I,OAAO,IAAI,CAAC,CAAC,CAAA;qBAC7B,CAAA;gBACP;oBACE,MAAM,IAAI5D,SAAS,CAAC,wBAAwB,GAAGL,IAAI,CAAC,CAAA;aACvD;SACF;QACD,SAASkK,yBAAyB,CAChCnK,aAAa,EACbC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACR;YACAH,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7B,IAAIG,QAAQ,KAAK,CAAC,CAAC,EAAE;gBACnBA,QAAQ,GAAG,UAAU;aACtB;YACD,IAAI5D,KAAK,GAAG6D,gBAAgB,CAACH,IAAI,CAAC;YAClC,IAAIyD,YAAY,GAAG,SAAUxM,KAAK,EAAE;gBAClC,OAAOA,KAAK,CAAA;aACb;YACD,IAAIgJ,QAAQ,KAAK,CAAC,EAAE;gBAClB,IAAIiK,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAGlK,IAAI;gBAC5ByD,YAAY,GAAG,SAAUxM,KAAK,EAAE;oBAC9B,OAAO,AAACA,KAAK,IAAIiT,QAAQ,KAAMA,QAAQ,CAAA;iBACxC;aACF;YACD,IAAIC,cAAc,GAAGpK,IAAI,CAACkI,QAAQ,CAAC,UAAU,CAAC;YAC9CpF,YAAY,CAAC/C,aAAa,EAAE;gBAC1BC,IAAI,EAAEA,IAAI;gBACV0D,YAAY,EAAEA,YAAY;gBAC1BE,UAAU,EAAE,SAAUC,WAAW,EAAE3M,KAAK,EAAE;oBACxC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;wBAC3D,MAAM,IAAImJ,SAAS,CACjB,kBAAkB,GAAG4E,YAAY,CAAC/N,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC8I,IAAI,CAC/D,CAAA;qBACF;oBACD,IAAI9I,KAAK,GAAGgJ,QAAQ,IAAIhJ,KAAK,GAAGiJ,QAAQ,EAAE;wBACxC,MAAM,IAAIE,SAAS,CACjB,oBAAoB,GAClB4E,YAAY,CAAC/N,KAAK,CAAC,GACnB,uDAAuD,GACvD8I,IAAI,GACJ,uCAAuC,GACvCE,QAAQ,GACR,IAAI,GACJC,QAAQ,GACR,IAAI,CACP,CAAA;qBACF;oBACD,OAAOiK,cAAc,GAAGlT,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAA;iBAChD;gBACD6M,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAE0F,2BAA2B,CAC/C1J,IAAI,EACJzD,KAAK,EACL2D,QAAQ,KAAK,CAAC,CACf;gBACDgE,kBAAkB,EAAE,IAAI;aACzB,CAAC;SACH;QACD,SAASmG,6BAA6B,CAAClH,OAAO,EAAEmH,aAAa,EAAEtK,IAAI,EAAE;YACnE,IAAIuK,WAAW,GAAG;gBAChBhP,SAAS;gBACTnF,UAAU;gBACVoF,UAAU;gBACVE,WAAW;gBACXD,UAAU;gBACVE,WAAW;gBACXC,YAAY;gBACZC,YAAY;aACb;YACD,IAAI2O,EAAE,GAAGD,WAAW,CAACD,aAAa,CAAC;YACnC,SAASG,gBAAgB,CAACnG,MAAM,EAAE;gBAChCA,MAAM,GAAGA,MAAM,IAAI,CAAC;gBACpB,IAAIvM,IAAI,GAAGmD,OAAO;gBAClB,IAAI+E,IAAI,GAAGlI,IAAI,CAACuM,MAAM,CAAC;gBACvB,IAAIoG,IAAI,GAAG3S,IAAI,CAACuM,MAAM,GAAG,CAAC,CAAC;gBAC3B,OAAO,IAAIkG,EAAE,CAACrU,OAAM,EAAEuU,IAAI,EAAEzK,IAAI,CAAC,CAAA;aAClC;YACDD,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7B8C,YAAY,CACVK,OAAO,EACP;gBACEnD,IAAI,EAAEA,IAAI;gBACV0D,YAAY,EAAE+G,gBAAgB;gBAC9B1G,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEyG,gBAAgB;aACvC,EACD;gBAAEnH,4BAA4B,EAAE,IAAI;aAAE,CACvC;SACF;QACD,SAASqH,4BAA4B,CAACxH,OAAO,EAAEnD,IAAI,EAAE;YACnDA,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7B,IAAI4K,eAAe,GAAG5K,IAAI,KAAK,aAAa;YAC5C8C,YAAY,CAACK,OAAO,EAAE;gBACpBnD,IAAI,EAAEA,IAAI;gBACV0D,YAAY,EAAE,SAAUxM,KAAK,EAAE;oBAC7B,IAAIX,MAAM,GAAG2E,OAAO,CAAChE,KAAK,IAAI,CAAC,CAAC;oBAChC,IAAI0B,GAAG;oBACP,IAAIgS,eAAe,EAAE;wBACnB,IAAIC,cAAc,GAAG3T,KAAK,GAAG,CAAC;wBAC9B,IAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;4BAChC,IAAI8R,cAAc,GAAG5T,KAAK,GAAG,CAAC,GAAG8B,CAAC;4BAClC,IAAIA,CAAC,IAAIzC,MAAM,IAAImC,MAAM,CAACoS,cAAc,CAAC,IAAI,CAAC,EAAE;gCAC9C,IAAIC,OAAO,GAAGD,cAAc,GAAGD,cAAc;gCAC7C,IAAIG,aAAa,GAAG1S,YAAY,CAACuS,cAAc,EAAEE,OAAO,CAAC;gCACzD,IAAInS,GAAG,KAAKqB,SAAS,EAAE;oCACrBrB,GAAG,GAAGoS,aAAa;iCACpB,MAAM;oCACLpS,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAAC,CAAC,CAAC;oCAC7BnB,GAAG,IAAIoS,aAAa;iCACrB;gCACDH,cAAc,GAAGC,cAAc,GAAG,CAAC;6BACpC;yBACF;qBACF,MAAM;wBACL,IAAIxM,CAAC,GAAG,IAAIkC,KAAK,CAACjK,MAAM,CAAC;wBACzB,IAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;4BAC/BsF,CAAC,CAACtF,CAAC,CAAC,GAAGc,MAAM,CAACC,YAAY,CAACrB,MAAM,CAACxB,KAAK,GAAG,CAAC,GAAG8B,CAAC,CAAC,CAAC;yBAClD;wBACDJ,GAAG,GAAG0F,CAAC,CAAC8K,IAAI,CAAC,EAAE,CAAC;qBACjB;oBACDP,KAAK,CAAC3R,KAAK,CAAC;oBACZ,OAAO0B,GAAG,CAAA;iBACX;gBACDgL,UAAU,EAAE,SAAUC,WAAW,EAAE3M,KAAK,EAAE;oBACxC,IAAIA,KAAK,YAAY+T,WAAW,EAAE;wBAChC/T,KAAK,GAAG,IAAId,UAAU,CAACc,KAAK,CAAC;qBAC9B;oBACD,IAAIgU,SAAS;oBACb,IAAIC,mBAAmB,GAAG,OAAOjU,KAAK,KAAK,QAAQ;oBACnD,IACE,CAAC,CACCiU,mBAAmB,IACnBjU,KAAK,YAAYd,UAAU,IAC3Bc,KAAK,YAAYkU,iBAAiB,IAClClU,KAAK,YAAYqE,SAAS,CAC3B,EACD;wBACA2G,iBAAiB,CAAC,uCAAuC,CAAC;qBAC3D;oBACD,IAAI0I,eAAe,IAAIO,mBAAmB,EAAE;wBAC1CD,SAAS,GAAG,WAAY;4BACtB,OAAO5R,eAAe,CAACpC,KAAK,CAAC,CAAA;yBAC9B;qBACF,MAAM;wBACLgU,SAAS,GAAG,WAAY;4BACtB,OAAOhU,KAAK,CAACX,MAAM,CAAA;yBACpB;qBACF;oBACD,IAAIA,MAAM,GAAG2U,SAAS,EAAE;oBACxB,IAAI3S,GAAG,GAAG8S,OAAO,CAAC,CAAC,GAAG9U,MAAM,GAAG,CAAC,CAAC;oBACjC2E,OAAO,CAAC3C,GAAG,IAAI,CAAC,CAAC,GAAGhC,MAAM;oBAC1B,IAAIqU,eAAe,IAAIO,mBAAmB,EAAE;wBAC1C/R,YAAY,CAAClC,KAAK,EAAEqB,GAAG,GAAG,CAAC,EAAEhC,MAAM,GAAG,CAAC,CAAC;qBACzC,MAAM;wBACL,IAAI4U,mBAAmB,EAAE;4BACvB,IAAK,IAAInS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gCAC/B,IAAIsS,QAAQ,GAAGpU,KAAK,CAACgC,UAAU,CAACF,CAAC,CAAC;gCAClC,IAAIsS,QAAQ,GAAG,GAAG,EAAE;oCAClBzC,KAAK,CAACtQ,GAAG,CAAC;oCACV2J,iBAAiB,CACf,wDAAwD,CACzD;iCACF;gCACDxJ,MAAM,CAACH,GAAG,GAAG,CAAC,GAAGS,CAAC,CAAC,GAAGsS,QAAQ;6BAC/B;yBACF,MAAM;4BACL,IAAK,IAAItS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gCAC/BN,MAAM,CAACH,GAAG,GAAG,CAAC,GAAGS,CAAC,CAAC,GAAG9B,KAAK,CAAC8B,CAAC,CAAC;6BAC/B;yBACF;qBACF;oBACD,IAAI6K,WAAW,KAAK,IAAI,EAAE;wBACxBA,WAAW,CAACX,IAAI,CAAC2F,KAAK,EAAEtQ,GAAG,CAAC;qBAC7B;oBACD,OAAOA,GAAG,CAAA;iBACX;gBACDwL,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEc,0BAA0B;gBAChDZ,kBAAkB,EAAE,SAAU3L,GAAG,EAAE;oBACjCsQ,KAAK,CAACtQ,GAAG,CAAC;iBACX;aACF,CAAC;SACH;QACD,SAASgT,6BAA6B,CAACpI,OAAO,EAAEqI,QAAQ,EAAExL,IAAI,EAAE;YAC9DA,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7B,IAAIyL,YAAY,EAAEC,YAAY,EAAEC,OAAO,EAAEC,cAAc,EAAErP,KAAK;YAC9D,IAAIiP,QAAQ,KAAK,CAAC,EAAE;gBAClBC,YAAY,GAAGhS,aAAa;gBAC5BiS,YAAY,GAAG1R,aAAa;gBAC5B4R,cAAc,GAAGxR,gBAAgB;gBACjCuR,OAAO,GAAG,WAAY;oBACpB,OAAOhS,OAAO,CAAA;iBACf;gBACD4C,KAAK,GAAG,CAAC;aACV,MAAM,IAAIiP,QAAQ,KAAK,CAAC,EAAE;gBACzBC,YAAY,GAAGpR,aAAa;gBAC5BqR,YAAY,GAAGjR,aAAa;gBAC5BmR,cAAc,GAAGjR,gBAAgB;gBACjCgR,OAAO,GAAG,WAAY;oBACpB,OAAOzQ,OAAO,CAAA;iBACf;gBACDqB,KAAK,GAAG,CAAC;aACV;YACDuG,YAAY,CAACK,OAAO,EAAE;gBACpBnD,IAAI,EAAEA,IAAI;gBACV0D,YAAY,EAAE,SAAUxM,KAAK,EAAE;oBAC7B,IAAIX,MAAM,GAAG2E,OAAO,CAAChE,KAAK,IAAI,CAAC,CAAC;oBAChC,IAAI2U,IAAI,GAAGF,OAAO,EAAE;oBACpB,IAAI/S,GAAG;oBACP,IAAIiS,cAAc,GAAG3T,KAAK,GAAG,CAAC;oBAC9B,IAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;wBAChC,IAAI8R,cAAc,GAAG5T,KAAK,GAAG,CAAC,GAAG8B,CAAC,GAAGwS,QAAQ;wBAC7C,IAAIxS,CAAC,IAAIzC,MAAM,IAAIsV,IAAI,CAACf,cAAc,IAAIvO,KAAK,CAAC,IAAI,CAAC,EAAE;4BACrD,IAAIuP,YAAY,GAAGhB,cAAc,GAAGD,cAAc;4BAClD,IAAIG,aAAa,GAAGS,YAAY,CAACZ,cAAc,EAAEiB,YAAY,CAAC;4BAC9D,IAAIlT,GAAG,KAAKqB,SAAS,EAAE;gCACrBrB,GAAG,GAAGoS,aAAa;6BACpB,MAAM;gCACLpS,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAAC,CAAC,CAAC;gCAC7BnB,GAAG,IAAIoS,aAAa;6BACrB;4BACDH,cAAc,GAAGC,cAAc,GAAGU,QAAQ;yBAC3C;qBACF;oBACD3C,KAAK,CAAC3R,KAAK,CAAC;oBACZ,OAAO0B,GAAG,CAAA;iBACX;gBACDgL,UAAU,EAAE,SAAUC,WAAW,EAAE3M,KAAK,EAAE;oBACxC,IAAI,CAAC,CAAC,OAAOA,KAAK,KAAK,QAAQ,CAAC,EAAE;wBAChCgL,iBAAiB,CACf,4CAA4C,GAAGlC,IAAI,CACpD;qBACF;oBACD,IAAIzJ,MAAM,GAAGqV,cAAc,CAAC1U,KAAK,CAAC;oBAClC,IAAIqB,GAAG,GAAG8S,OAAO,CAAC,CAAC,GAAG9U,MAAM,GAAGiV,QAAQ,CAAC;oBACxCtQ,OAAO,CAAC3C,GAAG,IAAI,CAAC,CAAC,GAAGhC,MAAM,IAAIgG,KAAK;oBACnCmP,YAAY,CAACxU,KAAK,EAAEqB,GAAG,GAAG,CAAC,EAAEhC,MAAM,GAAGiV,QAAQ,CAAC;oBAC/C,IAAI3H,WAAW,KAAK,IAAI,EAAE;wBACxBA,WAAW,CAACX,IAAI,CAAC2F,KAAK,EAAEtQ,GAAG,CAAC;qBAC7B;oBACD,OAAOA,GAAG,CAAA;iBACX;gBACDwL,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEc,0BAA0B;gBAChDZ,kBAAkB,EAAE,SAAU3L,GAAG,EAAE;oBACjCsQ,KAAK,CAACtQ,GAAG,CAAC;iBACX;aACF,CAAC;SACH;QACD,SAASwT,sBAAsB,CAAC5I,OAAO,EAAEnD,IAAI,EAAE;YAC7CA,IAAI,GAAGU,gBAAgB,CAACV,IAAI,CAAC;YAC7B8C,YAAY,CAACK,OAAO,EAAE;gBACpB6I,MAAM,EAAE,IAAI;gBACZhM,IAAI,EAAEA,IAAI;gBACV+D,cAAc,EAAE,CAAC;gBACjBL,YAAY,EAAE,WAAY;oBACxB,OAAOzJ,SAAS,CAAA;iBACjB;gBACD2J,UAAU,EAAE,SAAUC,WAAW,EAAEC,CAAC,EAAE;oBACpC,OAAO7J,SAAS,CAAA;iBACjB;aACF,CAAC;SACH;QACD,IAAIgS,aAAa,GAAG,EAAE;QACtB,SAASC,iBAAiB,CAACC,OAAO,EAAE;YAClC,IAAIC,MAAM,GAAGH,aAAa,CAACE,OAAO,CAAC;YACnC,IAAIC,MAAM,KAAKnS,SAAS,EAAE;gBACxB,OAAOyG,gBAAgB,CAACyL,OAAO,CAAC,CAAA;aACjC,MAAM;gBACL,OAAOC,MAAM,CAAA;aACd;SACF;QACD,SAASC,gBAAgB,GAAG;YAC1B,IAAI,OAAOC,UAAU,KAAK,QAAQ,EAAE;gBAClC,OAAOA,UAAU,CAAA;aAClB;YACD,OAAO,CAAC,WAAY;gBAClB,OAAOjL,QAAQ,CAAA;aAChB,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,CAAA;SACtB;QACD,SAASkL,kBAAkB,CAACvM,IAAI,EAAE;YAChC,IAAIA,IAAI,KAAK,CAAC,EAAE;gBACd,OAAO4E,gBAAgB,CAACyH,gBAAgB,EAAE,CAAC,CAAA;aAC5C,MAAM;gBACLrM,IAAI,GAAGkM,iBAAiB,CAAClM,IAAI,CAAC;gBAC9B,OAAO4E,gBAAgB,CAACyH,gBAAgB,EAAE,CAACrM,IAAI,CAAC,CAAC,CAAA;aAClD;SACF;QACD,SAASwM,cAAc,CAAClI,MAAM,EAAE;YAC9B,IAAIA,MAAM,GAAG,CAAC,EAAE;gBACdF,kBAAkB,CAACE,MAAM,CAAC,CAACC,QAAQ,IAAI,CAAC;aACzC;SACF;QACD,SAASkI,qBAAqB,CAACtJ,OAAO,EAAE4C,SAAS,EAAE;YACjD,IAAI2G,IAAI,GAAG7L,eAAe,CAACsC,OAAO,CAAC;YACnC,IAAIlJ,SAAS,KAAKyS,IAAI,EAAE;gBACtBxK,iBAAiB,CACf6D,SAAS,GAAG,oBAAoB,GAAG4C,WAAW,CAACxF,OAAO,CAAC,CACxD;aACF;YACD,OAAOuJ,IAAI,CAAA;SACZ;QACD,SAASC,mBAAmB,CAACvG,QAAQ,EAAE;YACrC,IAAII,QAAQ,GAAG,EAAE;YACjB,IAAK,IAAIxN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoN,QAAQ,EAAE,EAAEpN,CAAC,CAAE;gBACjCwN,QAAQ,IAAI,CAACxN,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK,GAAGA,CAAC;aAC9C;YACD,IAAI4T,YAAY,GACd,kCAAkC,GAClCxG,QAAQ,GACR,mCAAmC;YACrC,IAAK,IAAIpN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoN,QAAQ,EAAE,EAAEpN,CAAC,CAAE;gBACjC4T,YAAY,IACV,aAAa,GACb5T,CAAC,GACD,+DAA+D,GAC/DA,CAAC,GACD,gBAAgB,GAChBA,CAAC,GACD,OAAO,GACP,SAAS,GACTA,CAAC,GACD,YAAY,GACZA,CAAC,GACD,gCAAgC,GAChC,iBAAiB,GACjBA,CAAC,GACD,uBAAuB;aAC1B;YACD4T,YAAY,IACV,4BAA4B,GAC5BpG,QAAQ,GACR,MAAM,GACN,iCAAiC,GACjC,KAAK;YACP,OAAO,IAAInF,QAAQ,CACjB,uBAAuB,EACvB,QAAQ,EACR,kBAAkB,EAClBuL,YAAY,CACb,CAACH,qBAAqB,EAAErY,OAAM,EAAEwQ,gBAAgB,CAAC,CAAA;SACnD;QACD,IAAIiI,YAAY,GAAG,EAAE;QACrB,SAASC,aAAa,CAACxI,MAAM,EAAE;YAC7B,IAAI,CAACA,MAAM,EAAE;gBACXpC,iBAAiB,CAAC,mCAAmC,GAAGoC,MAAM,CAAC;aAChE;YACD,OAAOF,kBAAkB,CAACE,MAAM,CAAC,CAACpN,KAAK,CAAA;SACxC;QACD,SAAS6V,WAAW,CAACzI,MAAM,EAAE8B,QAAQ,EAAEJ,QAAQ,EAAE8B,IAAI,EAAE;YACrDxD,MAAM,GAAGwI,aAAa,CAACxI,MAAM,CAAC;YAC9B,IAAI0I,KAAK,GAAGH,YAAY,CAACzG,QAAQ,CAAC;YAClC,IAAI,CAAC4G,KAAK,EAAE;gBACVA,KAAK,GAAGL,mBAAmB,CAACvG,QAAQ,CAAC;gBACrCyG,YAAY,CAACzG,QAAQ,CAAC,GAAG4G,KAAK;aAC/B;YACD,OAAOA,KAAK,CAAC1I,MAAM,EAAE0B,QAAQ,EAAE8B,IAAI,CAAC,CAAA;SACrC;QACD,SAASmF,MAAM,GAAG;YAChB3V,KAAK,EAAE;SACR;QACD,SAAS4V,sBAAsB,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;YAC9C3U,MAAM,CAAC4U,UAAU,CAACH,IAAI,EAAEC,GAAG,EAAEA,GAAG,GAAGC,GAAG,CAAC;SACxC;QACD,SAASE,yBAAyB,CAACtN,IAAI,EAAE;YACvC,IAAI;gBACF1I,UAAU,CAACiW,IAAI,CAAC,AAACvN,IAAI,GAAG9J,OAAM,CAACsX,UAAU,GAAG,KAAK,KAAM,EAAE,CAAC;gBAC1DpS,0BAA0B,CAAC9D,UAAU,CAACpB,MAAM,CAAC;gBAC7C,OAAO,CAAC,CAAA;aACT,CAAC,OAAOsH,CAAC,EAAE,EAAE;SACf;QACD,SAASiQ,uBAAuB,CAACC,aAAa,EAAE;YAC9C,IAAIC,OAAO,GAAGlV,MAAM,CAACnC,MAAM;YAC3BoX,aAAa,GAAGA,aAAa,KAAK,CAAC;YACnC,IAAIE,WAAW,GAAG,UAAU;YAC5B,IAAIF,aAAa,GAAGE,WAAW,EAAE;gBAC/B,OAAO,KAAK,CAAA;aACb;YACD,IAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAI,CAAC,EAAEA,OAAO,IAAI,CAAC,CAAE;gBAChD,IAAIC,iBAAiB,GAAGH,OAAO,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGE,OAAO,CAAC;gBACrDC,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAC1BF,iBAAiB,EACjBJ,aAAa,GAAG,SAAS,CAC1B;gBACD,IAAIO,OAAO,GAAGF,IAAI,CAACC,GAAG,CACpBJ,WAAW,EACX9S,OAAO,CAACiT,IAAI,CAACG,GAAG,CAACR,aAAa,EAAEI,iBAAiB,CAAC,EAAE,KAAK,CAAC,CAC3D;gBACD,IAAIK,WAAW,GAAGb,yBAAyB,CAACW,OAAO,CAAC;gBACpD,IAAIE,WAAW,EAAE;oBACf,OAAO,IAAI,CAAA;iBACZ;aACF;YACD,OAAO,KAAK,CAAA;SACb;QACD,IAAIC,GAAG,GAAG,EAAE;QACZ,SAASC,iBAAiB,GAAG;YAC3B,OAAOxZ,WAAW,IAAI,gBAAgB,CAAA;SACvC;QACD,SAASyZ,aAAa,GAAG;YACvB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;gBAC1B,IAAIC,IAAI,GACN,CACE,AAAC,OAAOC,SAAS,KAAK,QAAQ,IAC5BA,SAAS,CAACC,SAAS,IACnBD,SAAS,CAACC,SAAS,CAAC,CAAC,CAAC,IACxB,GAAG,CACJ,CAACnY,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,QAAQ;gBAChC,IAAIoY,GAAG,GAAG;oBACRC,IAAI,EAAE,UAAU;oBAChBC,OAAO,EAAE,UAAU;oBACnBC,IAAI,EAAE,GAAG;oBACTC,GAAG,EAAE,GAAG;oBACRC,IAAI,EAAE,gBAAgB;oBACtBC,IAAI,EAAET,IAAI;oBACVU,CAAC,EAAEb,iBAAiB,EAAE;iBACvB;gBACD,IAAK,IAAItT,CAAC,IAAIqT,GAAG,CAAE;oBACjBO,GAAG,CAAC5T,CAAC,CAAC,GAAGqT,GAAG,CAACrT,CAAC,CAAC;iBAChB;gBACD,IAAIwT,OAAO,GAAG,EAAE;gBAChB,IAAK,IAAIxT,CAAC,IAAI4T,GAAG,CAAE;oBACjBJ,OAAO,CAACtL,IAAI,CAAClI,CAAC,GAAG,GAAG,GAAG4T,GAAG,CAAC5T,CAAC,CAAC,CAAC;iBAC/B;gBACDuT,aAAa,CAACC,OAAO,GAAGA,OAAO;aAChC;YACD,OAAOD,aAAa,CAACC,OAAO,CAAA;SAC7B;QACD,IAAIY,QAAQ,GAAG;YACbC,QAAQ,EAAE,EAAE;YACZC,OAAO,EAAE;gBAAC,IAAI;gBAAE,EAAE;gBAAE,EAAE;aAAC;YACvBC,SAAS,EAAE,SAAUC,MAAM,EAAEC,IAAI,EAAE;gBACjC,IAAItZ,MAAM,GAAGiZ,QAAQ,CAACE,OAAO,CAACE,MAAM,CAAC;gBACrC,IAAIC,IAAI,KAAK,CAAC,IAAIA,IAAI,KAAK,EAAE,EAAE;oBAC5B,CAACD,MAAM,KAAK,CAAC,GAAG9Y,GAAG,GAAGI,IAAG,CAAC,CAACgB,iBAAiB,CAAC3B,MAAM,EAAE,CAAC,CAAC,CAAC;oBACzDA,MAAM,CAACI,MAAM,GAAG,CAAC;iBAClB,MAAM;oBACLJ,MAAM,CAAC+M,IAAI,CAACuM,IAAI,CAAC;iBAClB;aACF;YACDC,OAAO,EAAEzV,SAAS;YAClBsF,GAAG,EAAE,WAAY;gBACf6P,QAAQ,CAACM,OAAO,IAAI,CAAC;gBACrB,IAAIxZ,GAAG,GAAGqE,MAAM,CAAC,AAAC6U,QAAQ,CAACM,OAAO,GAAG,CAAC,IAAK,CAAC,CAAC;gBAC7C,OAAOxZ,GAAG,CAAA;aACX;YACDyZ,MAAM,EAAE,SAAUpX,GAAG,EAAE;gBACrB,IAAIrC,GAAG,GAAGoC,YAAY,CAACC,GAAG,CAAC;gBAC3B,OAAOrC,GAAG,CAAA;aACX;YACD0Z,KAAK,EAAE,SAAUC,GAAG,EAAEC,IAAI,EAAE;gBAC1B,OAAOD,GAAG,CAAA;aACX;SACF;QACD,SAASE,YAAY,CAACC,SAAS,EAAEC,WAAW,EAAE;YAC5C,IAAIC,OAAO,GAAG,CAAC;YACf3B,aAAa,EAAE,CAAC9L,OAAO,CAAC,SAAU0N,MAAM,EAAEnX,CAAC,EAAE;gBAC3C,IAAIT,GAAG,GAAG0X,WAAW,GAAGC,OAAO;gBAC/B3V,MAAM,CAAC,AAACyV,SAAS,GAAGhX,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC,GAAGT,GAAG;gBACtCqC,kBAAkB,CAACuV,MAAM,EAAE5X,GAAG,CAAC;gBAC/B2X,OAAO,IAAIC,MAAM,CAAC5Z,MAAM,GAAG,CAAC;aAC7B,CAAC;YACF,OAAO,CAAC,CAAA;SACT;QACD,SAAS6Z,kBAAkB,CAACC,cAAc,EAAEC,iBAAiB,EAAE;YAC7D,IAAI9B,OAAO,GAAGD,aAAa,EAAE;YAC7BhU,MAAM,CAAC8V,cAAc,IAAI,CAAC,CAAC,GAAG7B,OAAO,CAACjY,MAAM;YAC5C,IAAI2Z,OAAO,GAAG,CAAC;YACf1B,OAAO,CAAC/L,OAAO,CAAC,SAAU0N,MAAM,EAAE;gBAChCD,OAAO,IAAIC,MAAM,CAAC5Z,MAAM,GAAG,CAAC;aAC7B,CAAC;YACFgE,MAAM,CAAC+V,iBAAiB,IAAI,CAAC,CAAC,GAAGJ,OAAO;YACxC,OAAO,CAAC,CAAA;SACT;QACD,SAASK,KAAK,CAACvb,MAAM,EAAE;YACrBwb,IAAI,CAACxb,MAAM,CAAC;SACb;QACD,SAASyb,SAAS,CAACC,EAAE,EAAE;YACrB,OAAO,CAAC,CAAA;SACT;QACD,SAASC,QAAQ,CAACD,EAAE,EAAEE,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAE,EAAE;QACpE,SAASC,SAAS,CAACN,EAAE,EAAEO,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAE;YACxC,IAAI9D,GAAG,GAAG,CAAC;YACX,IAAK,IAAIrU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkY,MAAM,EAAElY,CAAC,EAAE,CAAE;gBAC/B,IAAIT,GAAG,GAAGgC,MAAM,CAAC,AAAC0W,GAAG,GAAGjY,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;gBACpC,IAAIO,GAAG,GAAGgB,MAAM,CAAC,AAAC0W,GAAG,GAAG,CAACjY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC;gBAC1C,IAAK,IAAIoY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7X,GAAG,EAAE6X,CAAC,EAAE,CAAE;oBAC5BhC,QAAQ,CAACG,SAAS,CAACmB,EAAE,EAAEhY,MAAM,CAACH,GAAG,GAAG6Y,CAAC,CAAC,CAAC;iBACxC;gBACD/D,GAAG,IAAI9T,GAAG;aACX;YACDgB,MAAM,CAAC4W,IAAI,IAAI,CAAC,CAAC,GAAG9D,GAAG;YACvB,OAAO,CAAC,CAAA;SACT;QACD,SAASgE,YAAY,CAACC,GAAG,EAAE;YACzBra,WAAW,CAACqa,GAAG,CAAC;SACjB;QACDhR,qBAAqB,EAAE;QACvB2B,YAAY,GAAG7N,OAAM,CAAC,cAAc,CAAC,GAAGkN,WAAW,CAACvD,KAAK,EAAE,cAAc,CAAC;QAC1EoE,aAAa,GAAG/N,OAAM,CAAC,eAAe,CAAC,GAAGkN,WAAW,CACnDvD,KAAK,EACL,eAAe,CAChB;QACD4G,UAAU,EAAE;QACZ+D,gBAAgB,GAAGtU,OAAM,CAAC,kBAAkB,CAAC,GAAGkN,WAAW,CACzDvD,KAAK,EACL,kBAAkB,CACnB;QACD,IAAIQ,aAAa,GAAG;YAClBd,CAAC,EAAEkC,oBAAoB;YACvB4R,CAAC,EAAEzR,wBAAwB;YAC3B0R,CAAC,EAAEjO,sBAAsB;YACzBvI,CAAC,EAAE+J,uBAAuB;YAC1B0M,CAAC,EAAEpM,uBAAuB;YAC1BvB,CAAC,EAAEuF,0BAA0B;YAC7BqI,CAAC,EAAExH,yBAAyB;YAC5B5L,CAAC,EAAE+L,6BAA6B;YAChCsH,CAAC,EAAEhH,4BAA4B;YAC/BiH,CAAC,EAAErG,6BAA6B;YAChCsG,CAAC,EAAE9F,sBAAsB;YACzBpL,CAAC,EAAE0D,cAAc;YACjByN,CAAC,EAAEvF,kBAAkB;YACrBvT,CAAC,EAAEwT,cAAc;YACjB4E,CAAC,EAAErE,WAAW;YACdgF,CAAC,EAAE9E,MAAM;YACT+E,CAAC,EAAE9E,sBAAsB;YACzBhM,CAAC,EAAEwM,uBAAuB;YAC1BvI,CAAC,EAAE4K,YAAY;YACf9W,CAAC,EAAEmX,kBAAkB;YACrB6B,CAAC,EAAE1B,KAAK;YACR2B,CAAC,EAAEzB,SAAS;YACZ0B,CAAC,EAAExB,QAAQ;YACXzL,CAAC,EAAE8L,SAAS;YACZtL,CAAC,EAAE2L,YAAY;SAChB;QACD,IAAIe,GAAG,GAAGhU,UAAU,EAAE;QACtB,IAAIiU,kBAAkB,GAAIje,OAAM,CAAC,oBAAoB,CAAC,GAAG,WAAY;YACnE,OAAO,CAACie,kBAAkB,GAAGje,OAAM,CAAC,oBAAoB,CAAC,GACvDA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAACuR,KAAK,CAAC,IAAI,EAAE0B,SAAS,CAAC,CAAA;SAC7C,AAAC;QACF,IAAIgE,OAAO,GAAIjX,OAAM,CAAC,SAAS,CAAC,GAAG,WAAY;YAC7C,OAAO,CAACiX,OAAO,GAAGjX,OAAM,CAAC,SAAS,CAAC,GAAGA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAACuR,KAAK,CAC7D,IAAI,EACJ0B,SAAS,CACV,CAAA;SACF,AAAC;QACF,IAAIwB,KAAK,GAAIzU,OAAM,CAAC,OAAO,CAAC,GAAG,WAAY;YACzC,OAAO,CAACyU,KAAK,GAAGzU,OAAM,CAAC,OAAO,CAAC,GAAGA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAACuR,KAAK,CACzD,IAAI,EACJ0B,SAAS,CACV,CAAA;SACF,AAAC;QACF,IAAIuB,cAAc,GAAIxU,OAAM,CAAC,gBAAgB,CAAC,GAAG,WAAY;YAC3D,OAAO,CAACwU,cAAc,GAAGxU,OAAM,CAAC,gBAAgB,CAAC,GAC/CA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAACuR,KAAK,CAAC,IAAI,EAAE0B,SAAS,CAAC,CAAA;SAC7C,AAAC;QACF,IAAIiL,2CAA2C,GAAIle,OAAM,CACvD,6CAA6C,CAC9C,GAAG,WAAY;YACd,OAAO,CAACke,2CAA2C,GAAGle,OAAM,CAC1D,6CAA6C,CAC9C,GACCA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAACuR,KAAK,CAAC,IAAI,EAAE0B,SAAS,CAAC,CAAA;SAC7C,AAAC;QACF,IAAIkL,YAAY,GAAIne,OAAM,CAAC,cAAc,CAAC,GAAG,WAAY;YACvD,OAAO,CAACme,YAAY,GAAGne,OAAM,CAAC,cAAc,CAAC,GAAGA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAACuR,KAAK,CACvE,IAAI,EACJ0B,SAAS,CACV,CAAA;SACF,AAAC;QACF,IAAImL,SAAS;QACb,SAASC,UAAU,CAACzd,MAAM,EAAE;YAC1B,IAAI,CAACgL,IAAI,GAAG,YAAY;YACxB,IAAI,CAAC0B,OAAO,GAAG,+BAA+B,GAAG1M,MAAM,GAAG,GAAG;YAC7D,IAAI,CAACA,MAAM,GAAGA,MAAM;SACrB;QACDkI,qBAAqB,GAAG,SAASwV,SAAS,GAAG;YAC3C,IAAI,CAACF,SAAS,EAAEG,GAAG,EAAE;YACrB,IAAI,CAACH,SAAS,EAAEtV,qBAAqB,GAAGwV,SAAS;SAClD;QACD,SAASC,GAAG,CAAC7K,IAAI,EAAE;YACjBA,IAAI,GAAGA,IAAI,IAAIjT,UAAU;YACzB,IAAImI,eAAe,GAAG,CAAC,EAAE;gBACvB,OAAM;aACP;YACDX,MAAM,EAAE;YACR,IAAIW,eAAe,GAAG,CAAC,EAAE;gBACvB,OAAM;aACP;YACD,SAAS4V,KAAK,GAAG;gBACf,IAAIJ,SAAS,EAAE,OAAM;gBACrBA,SAAS,GAAG,IAAI;gBAChBpe,OAAM,CAAC,WAAW,CAAC,GAAG,IAAI;gBAC1B,IAAIoD,KAAK,EAAE,OAAM;gBACjBiF,WAAW,EAAE;gBACbpI,mBAAmB,CAACD,OAAM,CAAC;gBAC3B,IAAIA,OAAM,CAAC,sBAAsB,CAAC,EAAEA,OAAM,CAAC,sBAAsB,CAAC,EAAE;gBACpEuI,OAAO,EAAE;aACV;YACD,IAAIvI,OAAM,CAAC,WAAW,CAAC,EAAE;gBACvBA,OAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC;gBACjCye,UAAU,CAAC,WAAY;oBACrBA,UAAU,CAAC,WAAY;wBACrBze,OAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;qBACxB,EAAE,CAAC,CAAC;oBACLwe,KAAK,EAAE;iBACR,EAAE,CAAC,CAAC;aACN,MAAM;gBACLA,KAAK,EAAE;aACR;SACF;QACDxe,OAAM,CAAC,KAAK,CAAC,GAAGue,GAAG;QACnB,SAASnC,IAAI,CAACxb,MAAM,EAAE8d,QAAQ,EAAE;YAC9Brb,UAAU,GAAGzC,MAAM;YACnB,IAAI8d,QAAQ,IAAIrT,gBAAgB,EAAE,IAAIzK,MAAM,KAAK,CAAC,EAAE;gBAClD,OAAM;aACP;YACD,IAAIyK,gBAAgB,EAAE,EAAE,EACvB,MAAM;gBACL/C,WAAW,EAAE;gBACb,IAAItI,OAAM,CAAC,QAAQ,CAAC,EAAEA,OAAM,CAAC,QAAQ,CAAC,CAACY,MAAM,CAAC;gBAC9CwC,KAAK,GAAG,IAAI;aACb;YACDzC,KAAK,CAACC,MAAM,EAAE,IAAIyd,UAAU,CAACzd,MAAM,CAAC,CAAC;SACtC;QACD,IAAIZ,OAAM,CAAC,SAAS,CAAC,EAAE;YACrB,IAAI,OAAOA,OAAM,CAAC,SAAS,CAAC,IAAI,UAAU,EACxCA,OAAM,CAAC,SAAS,CAAC,GAAG;gBAACA,OAAM,CAAC,SAAS,CAAC;aAAC;YACzC,MAAOA,OAAM,CAAC,SAAS,CAAC,CAACmC,MAAM,GAAG,CAAC,CAAE;gBACnCnC,OAAM,CAAC,SAAS,CAAC,CAACyQ,GAAG,EAAE,EAAE;aAC1B;SACF;QACD8N,GAAG,EAAE;QAEL,OAAOve,OAAM,CAAC2e,KAAK,CAAA;KACpB,CAAA;CACF,EAAG;eACW3e,MAAM"}