{"version": 3, "sources": ["../../../../../server/lib/squoosh/mozjpeg/mozjpeg_node_enc.js"], "names": ["<PERSON><PERSON><PERSON>", "readyPromiseResolve", "readyPromiseReject", "Promise", "resolve", "reject", "moduleOverrides", "key", "hasOwnProperty", "arguments_", "thisProgram", "quit_", "status", "toThrow", "ENVIRONMENT_IS_WEB", "ENVIRONMENT_IS_WORKER", "ENVIRONMENT_IS_NODE", "scriptDirectory", "locateFile", "path", "read_", "readBinary", "nodeFS", "nodePath", "require", "dirname", "__dirname", "shell_read", "filename", "binary", "ret", "buffer", "Uint8Array", "assert", "process", "length", "replace", "slice", "out", "console", "log", "bind", "err", "warn", "tempRet0", "setTempRet0", "value", "wasmBinary", "noExitRuntime", "WebAssembly", "abort", "was<PERSON><PERSON><PERSON><PERSON>", "ABORT", "EXITSTATUS", "condition", "text", "UTF8Decoder", "TextDecoder", "UTF8ArrayToString", "heap", "idx", "maxBytesToRead", "endIdx", "endPtr", "decode", "subarray", "UTF8ToString", "ptr", "maxPtr", "end", "HEAPU8", "stringToUTF8Array", "str", "outIdx", "maxBytesToWrite", "startIdx", "i", "u", "charCodeAt", "u1", "stringToUTF8", "outPtr", "lengthBytesUTF8", "len", "UTF16Decoder", "UTF16ToString", "maxIdx", "HEAPU16", "codeUnit", "HEAP16", "String", "fromCharCode", "stringToUTF16", "undefined", "startPtr", "numCharsToWrite", "lengthBytesUTF16", "UTF32ToString", "utf32", "HEAP32", "ch", "stringToUTF32", "trailSurrogate", "lengthBytesUTF32", "writeAsciiToMemory", "dontAdd<PERSON>ull", "HEAP8", "alignUp", "x", "multiple", "HEAPU32", "HEAPF32", "HEAPF64", "updateGlobalBufferAndViews", "buf", "Int8Array", "Int16Array", "Int32Array", "Uint16Array", "Uint32Array", "Float32Array", "Float64Array", "INITIAL_MEMORY", "wasmTable", "__ATPRERUN__", "__ATINIT__", "__ATPOSTRUN__", "runtimeInitialized", "runtimeExited", "preRun", "addOnPreRun", "shift", "callRuntimeCallbacks", "initRuntime", "exitRuntime", "postRun", "addOnPostRun", "cb", "unshift", "addOnInit", "runDependencies", "runDependencyWatcher", "dependenciesFulfilled", "addRunDependency", "id", "removeRunDependency", "clearInterval", "callback", "what", "e", "RuntimeError", "dataURIPrefix", "isDataURI", "startsWith", "wasmBinaryFile", "Error", "getBinary", "file", "getBinaryPromise", "then", "createWasm", "info", "a", "asmLibraryArg", "receiveInstance", "instance", "module", "exports", "receiveInstantiationResult", "result", "instantiateArrayBuffer", "receiver", "instantiate", "reason", "instantiateAsync", "catch", "callbacks", "func", "arg", "get", "runtime<PERSON><PERSON><PERSON>ve<PERSON>ounter", "keepRuntimeAlive", "_atexit", "___cxa_thread_atexit", "a0", "a1", "structRegistrations", "runDestructors", "destructors", "pop", "del", "simpleReadValueFromPointer", "pointer", "awaitingDependencies", "registeredTypes", "typeDependencies", "char_0", "char_9", "makeLegalFunctionName", "name", "f", "createNamedFunction", "body", "Function", "extendError", "baseErrorType", "errorName", "errorClass", "message", "stack", "toString", "prototype", "Object", "create", "constructor", "InternalError", "throwInternalError", "whenDependentTypesAreResolved", "myTypes", "dependentTypes", "getTypeConverters", "for<PERSON>ach", "type", "onComplete", "typeConverters", "myTypeConverters", "registerType", "Array", "unregisteredTypes", "registered", "dt", "push", "__embind_finalize_value_object", "structType", "reg", "rawConstructor", "rawDestructor", "fieldRecords", "fields", "fieldTypes", "map", "field", "getterReturnType", "concat", "setterArgumentType", "fieldName", "getter", "getterContext", "setter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read", "write", "o", "fromWireType", "rv", "toWireType", "TypeError", "argPackAdvance", "readValueFromPointer", "destructorFunction", "__embind_register_bigint", "primitiveType", "size", "minRange", "max<PERSON><PERSON><PERSON>", "getShiftFromSize", "embind_init_charCodes", "codes", "embind_charCodes", "readLatin1String", "c", "BindingError", "throwBindingError", "rawType", "registeredInstance", "options", "ignoreDuplicateRegistrations", "__embind_register_bool", "trueValue", "falseValue", "wt", "emval_free_list", "emval_handle_array", "__emval_decref", "handle", "refcount", "count_emval_handles", "count", "get_first_emval", "init_emval", "__emval_register", "__embind_register_emval", "_embind_repr", "v", "t", "floatReadValueFromPointer", "__embind_register_float", "new_", "argumentList", "dummy", "obj", "r", "apply", "craftInvokerFunction", "humanName", "argTypes", "classType", "cppInvokerFunc", "cppTargetFunc", "argCount", "isClassMethodFunc", "needsDestructorStack", "returns", "argsList", "argsListWired", "invokerFnBody", "dtorStack", "args1", "args2", "paramName", "invokerFunction", "ensureOverloadTable", "proto", "methodName", "overloadTable", "prevFunc", "arguments", "exposePublicSymbol", "numArguments", "heap32VectorToArray", "firstElement", "array", "replacePublicSymbol", "dynCallLegacy", "sig", "args", "call", "dynCall", "includes", "getDynCaller", "<PERSON><PERSON><PERSON><PERSON>", "embind__requireFunction", "signature", "rawFunction", "makeDynCaller", "fp", "UnboundTypeError", "getTypeName", "___getTypeName", "_free", "throwUnboundTypeError", "types", "unboundTypes", "seen", "visit", "join", "__embind_register_function", "rawArgTypesAddr", "rawInvoker", "fn", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "integerReadValueFromPointer", "signed", "readS8FromPointer", "readU8FromPointer", "readS16FromPointer", "readU16FromPointer", "readS32FromPointer", "readU32FromPointer", "__embind_register_integer", "bitshift", "isUnsignedType", "__embind_register_memory_view", "dataTypeIndex", "typeMapping", "TA", "decodeMemoryView", "data", "__embind_register_std_string", "stdStringIsUTF8", "decodeStartPtr", "currentBytePtr", "maxRead", "stringSegment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "valueIsOfTypeString", "Uint8ClampedArray", "_malloc", "charCode", "__embind_register_std_wstring", "charSize", "decodeString", "encodeString", "getHeap", "lengthBytesUTF", "HEAP", "maxReadBytes", "__embind_register_value_object", "constructorSignature", "destructorSignature", "__embind_register_value_object_field", "getterSignature", "setterSignature", "__embind_register_void", "isVoid", "emval_symbols", "getStringOrSymbol", "address", "symbol", "emval_get_global", "globalThis", "__emval_get_global", "__emval_incref", "requireRegisteredType", "impl", "craftEmvalAllocator", "functionBody", "emval_newers", "<PERSON><PERSON><PERSON><PERSON>", "__emval_new", "newer", "_abort", "_emscripten_memcpy_big", "dest", "src", "num", "copyWithin", "emscripten_realloc_buffer", "grow", "byteLength", "_emscripten_resize_heap", "requestedSize", "oldSize", "maxHeapSize", "cutDown", "overGrownHeapSize", "Math", "min", "newSize", "max", "replacement", "ENV", "getExecutableName", "getEnvStrings", "strings", "lang", "navigator", "languages", "env", "USER", "LOGNAME", "PATH", "PWD", "HOME", "LANG", "_", "SYSCALLS", "mappings", "buffers", "printChar", "stream", "curr", "varargs", "getStr", "get64", "low", "high", "_environ_get", "__environ", "environ_buf", "bufSize", "string", "_environ_sizes_get", "penviron_count", "penviron_buf_size", "_exit", "exit", "_fd_close", "fd", "_fd_seek", "offset_low", "offset_high", "whence", "newOffset", "_fd_write", "iov", "iovcnt", "pnum", "j", "_setTempRet0", "val", "B", "l", "p", "y", "b", "m", "z", "g", "k", "n", "h", "d", "s", "A", "w", "q", "asm", "___wasm_call_ctors", "___embind_register_native_and_builtin_types", "dyn<PERSON>all_jiji", "calledRun", "ExitStatus", "runCaller", "run", "doRun", "setTimeout", "implicit", "ready"], "mappings": "AACA;;;;;AADA,oBAAoB,CACpB,IAAIA,MAAM,GAAG,AAAC,WAAY;IACxB,OAAO,SAAUA,OAAM,EAAE;QACvBA,OAAM,GAAGA,OAAM,IAAI,EAAE;QAErB,IAAIA,OAAM,GAAG,OAAOA,OAAM,KAAK,WAAW,GAAGA,OAAM,GAAG,EAAE;QACxD,IAAIC,mBAAmB,EAAEC,kBAAkB;QAC3CF,OAAM,CAAC,OAAO,CAAC,GAAG,IAAIG,OAAO,CAAC,SAAUC,OAAO,EAAEC,MAAM,EAAE;YACvDJ,mBAAmB,GAAGG,OAAO;YAC7BF,kBAAkB,GAAGG,MAAM;SAC5B,CAAC;QACF,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIC,GAAG;QACP,IAAKA,GAAG,IAAIP,OAAM,CAAE;YAClB,IAAIA,OAAM,CAACQ,cAAc,CAACD,GAAG,CAAC,EAAE;gBAC9BD,eAAe,CAACC,GAAG,CAAC,GAAGP,OAAM,CAACO,GAAG,CAAC;aACnC;SACF;QACD,IAAIE,UAAU,GAAG,EAAE;QACnB,IAAIC,WAAW,GAAG,gBAAgB;QAClC,IAAIC,KAAK,GAAG,SAAUC,MAAM,EAAEC,OAAO,EAAE;YACrC,MAAMA,OAAO,CAAA;SACd;QACD,IAAIC,kBAAkB,GAAG,KAAK;QAC9B,IAAIC,qBAAqB,GAAG,KAAK;QACjC,IAAIC,mBAAmB,GAAG,IAAI;QAC9B,IAAIC,eAAe,GAAG,EAAE;QACxB,SAASC,UAAU,CAACC,IAAI,EAAE;YACxB,IAAInB,OAAM,CAAC,YAAY,CAAC,EAAE;gBACxB,OAAOA,OAAM,CAAC,YAAY,CAAC,CAACmB,IAAI,EAAEF,eAAe,CAAC,CAAA;aACnD;YACD,OAAOA,eAAe,GAAGE,IAAI,CAAA;SAC9B;QACD,IAAIC,KAAK,EAAEC,UAAU;QACrB,IAAIC,MAAM;QACV,IAAIC,QAAQ;QACZ,IAAIP,mBAAmB,EAAE;YACvB,IAAID,qBAAqB,EAAE;gBACzBE,eAAe,GAAGO,OAAO,CAAC,MAAM,CAAC,CAACC,OAAO,CAACR,eAAe,CAAC,GAAG,GAAG;aACjE,MAAM;gBACLA,eAAe,GAAGS,SAAS,GAAG,GAAG;aAClC;YACDN,KAAK,GAAG,SAASO,UAAU,CAACC,QAAQ,EAAEC,MAAM,EAAE;gBAC5C,IAAI,CAACP,MAAM,EAAEA,MAAM,GAAGE,OAAO,CAAC,IAAI,CAAC;gBACnC,IAAI,CAACD,QAAQ,EAAEA,QAAQ,GAAGC,OAAO,CAAC,MAAM,CAAC;gBACzCI,QAAQ,GAAGL,QAAQ,CAAC,WAAW,CAAC,CAACK,QAAQ,CAAC;gBAC1C,OAAON,MAAM,CAAC,cAAc,CAAC,CAACM,QAAQ,EAAEC,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC,CAAA;aAChE;YACDR,UAAU,GAAG,SAASA,UAAU,CAACO,QAAQ,EAAE;gBACzC,IAAIE,GAAG,GAAGV,KAAK,CAACQ,QAAQ,EAAE,IAAI,CAAC;gBAC/B,IAAI,CAACE,GAAG,CAACC,MAAM,EAAE;oBACfD,GAAG,GAAG,IAAIE,UAAU,CAACF,GAAG,CAAC;iBAC1B;gBACDG,MAAM,CAACH,GAAG,CAACC,MAAM,CAAC;gBAClB,OAAOD,GAAG,CAAA;aACX;YACD,IAAII,OAAO,CAAC,MAAM,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;gBAC9BzB,WAAW,GAAGwB,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACE,OAAO,QAAQ,GAAG,CAAC;aACrD;YACD3B,UAAU,GAAGyB,OAAO,CAAC,MAAM,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;YACrC1B,KAAK,GAAG,SAAUC,MAAM,EAAE;gBACxBsB,OAAO,CAAC,MAAM,CAAC,CAACtB,MAAM,CAAC;aACxB;YACDZ,OAAM,CAAC,SAAS,CAAC,GAAG,WAAY;gBAC9B,OAAO,4BAA4B,CAAA;aACpC;SACF,MAAM,EACN;QACD,IAAIsC,GAAG,GAAGtC,OAAM,CAAC,OAAO,CAAC,IAAIuC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACF,OAAO,CAAC;QACtD,IAAIG,IAAG,GAAG1C,OAAM,CAAC,UAAU,CAAC,IAAIuC,OAAO,CAACI,IAAI,CAACF,IAAI,CAACF,OAAO,CAAC;QAC1D,IAAKhC,GAAG,IAAID,eAAe,CAAE;YAC3B,IAAIA,eAAe,CAACE,cAAc,CAACD,GAAG,CAAC,EAAE;gBACvCP,OAAM,CAACO,GAAG,CAAC,GAAGD,eAAe,CAACC,GAAG,CAAC;aACnC;SACF;QACDD,eAAe,GAAG,IAAI;QACtB,IAAIN,OAAM,CAAC,WAAW,CAAC,EAAES,UAAU,GAAGT,OAAM,CAAC,WAAW,CAAC;QACzD,IAAIA,OAAM,CAAC,aAAa,CAAC,EAAEU,WAAW,GAAGV,OAAM,CAAC,aAAa,CAAC;QAC9D,IAAIA,OAAM,CAAC,MAAM,CAAC,EAAEW,KAAK,GAAGX,OAAM,CAAC,MAAM,CAAC;QAC1C,IAAI4C,QAAQ,GAAG,CAAC;QAChB,IAAIC,WAAW,GAAG,SAAUC,KAAK,EAAE;YACjCF,QAAQ,GAAGE,KAAK;SACjB;QACD,IAAIC,UAAU;QACd,IAAI/C,OAAM,CAAC,YAAY,CAAC,EAAE+C,UAAU,GAAG/C,OAAM,CAAC,YAAY,CAAC;QAC3D,IAAIgD,aAAa,GAAGhD,OAAM,CAAC,eAAe,CAAC,IAAI,IAAI;QACnD,IAAI,OAAOiD,WAAW,KAAK,QAAQ,EAAE;YACnCC,KAAK,CAAC,iCAAiC,CAAC;SACzC;QACD,IAAIC,UAAU;QACd,IAAIC,KAAK,GAAG,KAAK;QACjB,IAAIC,UAAU;QACd,SAASpB,MAAM,CAACqB,SAAS,EAAEC,IAAI,EAAE;YAC/B,IAAI,CAACD,SAAS,EAAE;gBACdJ,KAAK,CAAC,oBAAoB,GAAGK,IAAI,CAAC;aACnC;SACF;QACD,IAAIC,WAAW,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;QACzC,SAASC,iBAAiB,CAACC,IAAI,EAAEC,GAAG,EAAEC,cAAc,EAAE;YACpD,IAAIC,MAAM,GAAGF,GAAG,GAAGC,cAAc;YACjC,IAAIE,MAAM,GAAGH,GAAG;YAChB,MAAOD,IAAI,CAACI,MAAM,CAAC,IAAI,CAAC,CAACA,MAAM,IAAID,MAAM,CAAC,CAAE,EAAEC,MAAM;YACpD,OAAOP,WAAW,CAACQ,MAAM,CACvBL,IAAI,CAACM,QAAQ,GACTN,IAAI,CAACM,QAAQ,CAACL,GAAG,EAAEG,MAAM,CAAC,GAC1B,IAAI/B,UAAU,CAAC2B,IAAI,CAACtB,KAAK,CAACuB,GAAG,EAAEG,MAAM,CAAC,CAAC,CAC5C,CAAA;SACF;QACD,SAASG,YAAY,CAACC,GAAG,EAAEN,cAAc,EAAE;YACzC,IAAI,CAACM,GAAG,EAAE,OAAO,EAAE,CAAA;YACnB,IAAIC,MAAM,GAAGD,GAAG,GAAGN,cAAc;YACjC,IAAK,IAAIQ,GAAG,GAAGF,GAAG,EAAE,CAAC,CAACE,GAAG,IAAID,MAAM,CAAC,IAAIE,MAAM,CAACD,GAAG,CAAC,EAAI,EAAEA,GAAG;YAC5D,OAAOb,WAAW,CAACQ,MAAM,CAACM,MAAM,CAACL,QAAQ,CAACE,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAA;SACrD;QACD,SAASE,iBAAiB,CAACC,GAAG,EAAEb,IAAI,EAAEc,MAAM,EAAEC,eAAe,EAAE;YAC7D,IAAI,CAAC,CAACA,eAAe,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;YACpC,IAAIC,QAAQ,GAAGF,MAAM;YACrB,IAAIX,MAAM,GAAGW,MAAM,GAAGC,eAAe,GAAG,CAAC;YACzC,IAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC,IAAIC,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBACzB,IAAIC,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,EAAE;oBAC5B,IAAIE,EAAE,GAAGP,GAAG,CAACM,UAAU,CAAC,EAAEF,CAAC,CAAC;oBAC5BC,CAAC,GAAG,AAAC,KAAK,GAAG,CAAC,CAACA,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAKE,EAAE,GAAG,IAAI,AAAC;iBAC/C;gBACD,IAAIF,CAAC,IAAI,GAAG,EAAE;oBACZ,IAAIJ,MAAM,IAAIX,MAAM,EAAE,MAAK;oBAC3BH,IAAI,CAACc,MAAM,EAAE,CAAC,GAAGI,CAAC;iBACnB,MAAM,IAAIA,CAAC,IAAI,IAAI,EAAE;oBACpB,IAAIJ,MAAM,GAAG,CAAC,IAAIX,MAAM,EAAE,MAAK;oBAC/BH,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,IAAI,CAAC,AAAC;oBAC/BlB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,GAAG,EAAE,AAAC;iBAChC,MAAM,IAAIA,CAAC,IAAI,KAAK,EAAE;oBACrB,IAAIJ,MAAM,GAAG,CAAC,IAAIX,MAAM,EAAE,MAAK;oBAC/BH,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,IAAI,EAAE,AAAC;oBAChClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAI,AAACI,CAAC,IAAI,CAAC,GAAI,EAAE,AAAC;oBACtClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,GAAG,EAAE,AAAC;iBAChC,MAAM;oBACL,IAAIJ,MAAM,GAAG,CAAC,IAAIX,MAAM,EAAE,MAAK;oBAC/BH,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,IAAI,EAAE,AAAC;oBAChClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAI,AAACI,CAAC,IAAI,EAAE,GAAI,EAAE,AAAC;oBACvClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAI,AAACI,CAAC,IAAI,CAAC,GAAI,EAAE,AAAC;oBACtClB,IAAI,CAACc,MAAM,EAAE,CAAC,GAAG,GAAG,GAAII,CAAC,GAAG,EAAE,AAAC;iBAChC;aACF;YACDlB,IAAI,CAACc,MAAM,CAAC,GAAG,CAAC;YAChB,OAAOA,MAAM,GAAGE,QAAQ,CAAA;SACzB;QACD,SAASK,YAAY,CAACR,GAAG,EAAES,MAAM,EAAEP,eAAe,EAAE;YAClD,OAAOH,iBAAiB,CAACC,GAAG,EAAEF,MAAM,EAAEW,MAAM,EAAEP,eAAe,CAAC,CAAA;SAC/D;QACD,SAASQ,eAAe,CAACV,GAAG,EAAE;YAC5B,IAAIW,GAAG,GAAG,CAAC;YACX,IAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC,IAAIC,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBACzB,IAAIC,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,EAC1BA,CAAC,GAAG,AAAC,KAAK,GAAG,CAAC,CAACA,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAKL,GAAG,CAACM,UAAU,CAAC,EAAEF,CAAC,CAAC,GAAG,IAAI,AAAC;gBACjE,IAAIC,CAAC,IAAI,GAAG,EAAE,EAAEM,GAAG;qBACd,IAAIN,CAAC,IAAI,IAAI,EAAEM,GAAG,IAAI,CAAC;qBACvB,IAAIN,CAAC,IAAI,KAAK,EAAEM,GAAG,IAAI,CAAC;qBACxBA,GAAG,IAAI,CAAC;aACd;YACD,OAAOA,GAAG,CAAA;SACX;QACD,IAAIC,YAAY,GAAG,IAAI3B,WAAW,CAAC,UAAU,CAAC;QAC9C,SAAS4B,aAAa,CAAClB,GAAG,EAAEN,cAAc,EAAE;YAC1C,IAAIE,MAAM,GAAGI,GAAG;YAChB,IAAIP,GAAG,GAAGG,MAAM,IAAI,CAAC;YACrB,IAAIuB,MAAM,GAAG1B,GAAG,GAAGC,cAAc,GAAG,CAAC;YACrC,MAAO,CAAC,CAACD,GAAG,IAAI0B,MAAM,CAAC,IAAIC,OAAO,CAAC3B,GAAG,CAAC,CAAE,EAAEA,GAAG;YAC9CG,MAAM,GAAGH,GAAG,IAAI,CAAC;YACjB,OAAOwB,YAAY,CAACpB,MAAM,CAACM,MAAM,CAACL,QAAQ,CAACE,GAAG,EAAEJ,MAAM,CAAC,CAAC,CAAA;YACxD,IAAIS,GAAG,GAAG,EAAE;YACZ,IAAK,IAAII,CAAC,GAAG,CAAC,EAAE,CAAC,CAACA,CAAC,IAAIf,cAAc,GAAG,CAAC,CAAC,EAAE,EAAEe,CAAC,CAAE;gBAC/C,IAAIY,QAAQ,GAAGC,MAAM,CAAC,AAACtB,GAAG,GAAGS,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;gBACzC,IAAIY,QAAQ,IAAI,CAAC,EAAE,MAAK;gBACxBhB,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAACH,QAAQ,CAAC;aACrC;YACD,OAAOhB,GAAG,CAAA;SACX;QACD,SAASoB,aAAa,CAACpB,GAAG,EAAES,MAAM,EAAEP,eAAe,EAAE;YACnD,IAAIA,eAAe,KAAKmB,SAAS,EAAE;gBACjCnB,eAAe,GAAG,UAAU;aAC7B;YACD,IAAIA,eAAe,GAAG,CAAC,EAAE,OAAO,CAAC,CAAA;YACjCA,eAAe,IAAI,CAAC;YACpB,IAAIoB,QAAQ,GAAGb,MAAM;YACrB,IAAIc,eAAe,GACjBrB,eAAe,GAAGF,GAAG,CAACrC,MAAM,GAAG,CAAC,GAAGuC,eAAe,GAAG,CAAC,GAAGF,GAAG,CAACrC,MAAM;YACrE,IAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,eAAe,EAAE,EAAEnB,CAAC,CAAE;gBACxC,IAAIY,QAAQ,GAAGhB,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBAChCa,MAAM,CAACR,MAAM,IAAI,CAAC,CAAC,GAAGO,QAAQ;gBAC9BP,MAAM,IAAI,CAAC;aACZ;YACDQ,MAAM,CAACR,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;YACvB,OAAOA,MAAM,GAAGa,QAAQ,CAAA;SACzB;QACD,SAASE,gBAAgB,CAACxB,GAAG,EAAE;YAC7B,OAAOA,GAAG,CAACrC,MAAM,GAAG,CAAC,CAAA;SACtB;QACD,SAAS8D,aAAa,CAAC9B,GAAG,EAAEN,cAAc,EAAE;YAC1C,IAAIe,CAAC,GAAG,CAAC;YACT,IAAIJ,GAAG,GAAG,EAAE;YACZ,MAAO,CAAC,CAACI,CAAC,IAAIf,cAAc,GAAG,CAAC,CAAC,CAAE;gBACjC,IAAIqC,KAAK,GAAGC,MAAM,CAAC,AAAChC,GAAG,GAAGS,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;gBACtC,IAAIsB,KAAK,IAAI,CAAC,EAAE,MAAK;gBACrB,EAAEtB,CAAC;gBACH,IAAIsB,KAAK,IAAI,KAAK,EAAE;oBAClB,IAAIE,EAAE,GAAGF,KAAK,GAAG,KAAK;oBACtB1B,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAAC,KAAK,GAAIS,EAAE,IAAI,EAAE,AAAC,EAAE,KAAK,GAAIA,EAAE,GAAG,IAAI,AAAC,CAAC;iBACpE,MAAM;oBACL5B,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAACO,KAAK,CAAC;iBAClC;aACF;YACD,OAAO1B,GAAG,CAAA;SACX;QACD,SAAS6B,aAAa,CAAC7B,GAAG,EAAES,MAAM,EAAEP,eAAe,EAAE;YACnD,IAAIA,eAAe,KAAKmB,SAAS,EAAE;gBACjCnB,eAAe,GAAG,UAAU;aAC7B;YACD,IAAIA,eAAe,GAAG,CAAC,EAAE,OAAO,CAAC,CAAA;YACjC,IAAIoB,QAAQ,GAAGb,MAAM;YACrB,IAAIlB,MAAM,GAAG+B,QAAQ,GAAGpB,eAAe,GAAG,CAAC;YAC3C,IAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC,IAAIY,QAAQ,GAAGhB,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBAChC,IAAIY,QAAQ,IAAI,KAAK,IAAIA,QAAQ,IAAI,KAAK,EAAE;oBAC1C,IAAIc,cAAc,GAAG9B,GAAG,CAACM,UAAU,CAAC,EAAEF,CAAC,CAAC;oBACxCY,QAAQ,GACN,AAAC,KAAK,GAAG,CAAC,CAACA,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAKc,cAAc,GAAG,IAAI,AAAC;iBAChE;gBACDH,MAAM,CAAClB,MAAM,IAAI,CAAC,CAAC,GAAGO,QAAQ;gBAC9BP,MAAM,IAAI,CAAC;gBACX,IAAIA,MAAM,GAAG,CAAC,GAAGlB,MAAM,EAAE,MAAK;aAC/B;YACDoC,MAAM,CAAClB,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;YACvB,OAAOA,MAAM,GAAGa,QAAQ,CAAA;SACzB;QACD,SAASS,gBAAgB,CAAC/B,GAAG,EAAE;YAC7B,IAAIW,GAAG,GAAG,CAAC;YACX,IAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC,IAAIY,QAAQ,GAAGhB,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;gBAChC,IAAIY,QAAQ,IAAI,KAAK,IAAIA,QAAQ,IAAI,KAAK,EAAE,EAAEZ,CAAC;gBAC/CO,GAAG,IAAI,CAAC;aACT;YACD,OAAOA,GAAG,CAAA;SACX;QACD,SAASqB,kBAAkB,CAAChC,GAAG,EAAEzC,MAAM,EAAE0E,WAAW,EAAE;YACpD,IAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACrC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACnC8B,KAAK,CAAC3E,CAAAA,MAAM,EAAE,CAAA,IAAI,CAAC,CAAC,GAAGyC,GAAG,CAACM,UAAU,CAACF,CAAC,CAAC;aACzC;YACD,IAAI,CAAC6B,WAAW,EAAEC,KAAK,CAAC3E,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;SACzC;QACD,SAAS4E,OAAO,CAACC,CAAC,EAAEC,QAAQ,EAAE;YAC5B,IAAID,CAAC,GAAGC,QAAQ,GAAG,CAAC,EAAE;gBACpBD,CAAC,IAAIC,QAAQ,GAAID,CAAC,GAAGC,QAAQ,AAAC;aAC/B;YACD,OAAOD,CAAC,CAAA;SACT;QACD,IAAI7E,OAAM,EACR2E,KAAK,EACLpC,MAAM,EACNmB,MAAM,EACNF,OAAO,EACPY,MAAM,EACNW,OAAO,EACPC,OAAO,EACPC,OAAO;QACT,SAASC,0BAA0B,CAACC,GAAG,EAAE;YACvCnF,OAAM,GAAGmF,GAAG;YACZlH,OAAM,CAAC,OAAO,CAAC,GAAG0G,KAAK,GAAG,IAAIS,SAAS,CAACD,GAAG,CAAC;YAC5ClH,OAAM,CAAC,QAAQ,CAAC,GAAGyF,MAAM,GAAG,IAAI2B,UAAU,CAACF,GAAG,CAAC;YAC/ClH,OAAM,CAAC,QAAQ,CAAC,GAAGmG,MAAM,GAAG,IAAIkB,UAAU,CAACH,GAAG,CAAC;YAC/ClH,OAAM,CAAC,QAAQ,CAAC,GAAGsE,MAAM,GAAG,IAAItC,UAAU,CAACkF,GAAG,CAAC;YAC/ClH,OAAM,CAAC,SAAS,CAAC,GAAGuF,OAAO,GAAG,IAAI+B,WAAW,CAACJ,GAAG,CAAC;YAClDlH,OAAM,CAAC,SAAS,CAAC,GAAG8G,OAAO,GAAG,IAAIS,WAAW,CAACL,GAAG,CAAC;YAClDlH,OAAM,CAAC,SAAS,CAAC,GAAG+G,OAAO,GAAG,IAAIS,YAAY,CAACN,GAAG,CAAC;YACnDlH,OAAM,CAAC,SAAS,CAAC,GAAGgH,OAAO,GAAG,IAAIS,YAAY,CAACP,GAAG,CAAC;SACpD;QACD,IAAIQ,cAAc,GAAG1H,OAAM,CAAC,gBAAgB,CAAC,IAAI,QAAQ;QACzD,IAAI2H,SAAS;QACb,IAAIC,YAAY,GAAG,EAAE;QACrB,IAAIC,UAAU,GAAG,EAAE;QACnB,IAAIC,aAAa,GAAG,EAAE;QACtB,IAAIC,kBAAkB,GAAG,KAAK;QAC9B,IAAIC,aAAa,GAAG,KAAK;QACzB,SAASC,MAAM,GAAG;YAChB,IAAIjI,OAAM,CAAC,QAAQ,CAAC,EAAE;gBACpB,IAAI,OAAOA,OAAM,CAAC,QAAQ,CAAC,IAAI,UAAU,EACvCA,OAAM,CAAC,QAAQ,CAAC,GAAG;oBAACA,OAAM,CAAC,QAAQ,CAAC;iBAAC;gBACvC,MAAOA,OAAM,CAAC,QAAQ,CAAC,CAACmC,MAAM,CAAE;oBAC9B+F,WAAW,CAAClI,OAAM,CAAC,QAAQ,CAAC,CAACmI,KAAK,EAAE,CAAC;iBACtC;aACF;YACDC,oBAAoB,CAACR,YAAY,CAAC;SACnC;QACD,SAASS,WAAW,GAAG;YACrBN,kBAAkB,GAAG,IAAI;YACzBK,oBAAoB,CAACP,UAAU,CAAC;SACjC;QACD,SAASS,WAAW,GAAG;YACrBN,aAAa,GAAG,IAAI;SACrB;QACD,SAASO,OAAO,GAAG;YACjB,IAAIvI,OAAM,CAAC,SAAS,CAAC,EAAE;gBACrB,IAAI,OAAOA,OAAM,CAAC,SAAS,CAAC,IAAI,UAAU,EACxCA,OAAM,CAAC,SAAS,CAAC,GAAG;oBAACA,OAAM,CAAC,SAAS,CAAC;iBAAC;gBACzC,MAAOA,OAAM,CAAC,SAAS,CAAC,CAACmC,MAAM,CAAE;oBAC/BqG,YAAY,CAACxI,OAAM,CAAC,SAAS,CAAC,CAACmI,KAAK,EAAE,CAAC;iBACxC;aACF;YACDC,oBAAoB,CAACN,aAAa,CAAC;SACpC;QACD,SAASI,WAAW,CAACO,EAAE,EAAE;YACvBb,YAAY,CAACc,OAAO,CAACD,EAAE,CAAC;SACzB;QACD,SAASE,SAAS,CAACF,EAAE,EAAE;YACrBZ,UAAU,CAACa,OAAO,CAACD,EAAE,CAAC;SACvB;QACD,SAASD,YAAY,CAACC,EAAE,EAAE;YACxBX,aAAa,CAACY,OAAO,CAACD,EAAE,CAAC;SAC1B;QACD,IAAIG,eAAe,GAAG,CAAC;QACvB,IAAIC,oBAAoB,GAAG,IAAI;QAC/B,IAAIC,qBAAqB,GAAG,IAAI;QAChC,SAASC,gBAAgB,CAACC,EAAE,EAAE;YAC5BJ,eAAe,EAAE;YACjB,IAAI5I,OAAM,CAAC,wBAAwB,CAAC,EAAE;gBACpCA,OAAM,CAAC,wBAAwB,CAAC,CAAC4I,eAAe,CAAC;aAClD;SACF;QACD,SAASK,mBAAmB,CAACD,EAAE,EAAE;YAC/BJ,eAAe,EAAE;YACjB,IAAI5I,OAAM,CAAC,wBAAwB,CAAC,EAAE;gBACpCA,OAAM,CAAC,wBAAwB,CAAC,CAAC4I,eAAe,CAAC;aAClD;YACD,IAAIA,eAAe,IAAI,CAAC,EAAE;gBACxB,IAAIC,oBAAoB,KAAK,IAAI,EAAE;oBACjCK,aAAa,CAACL,oBAAoB,CAAC;oBACnCA,oBAAoB,GAAG,IAAI;iBAC5B;gBACD,IAAIC,qBAAqB,EAAE;oBACzB,IAAIK,QAAQ,GAAGL,qBAAqB;oBACpCA,qBAAqB,GAAG,IAAI;oBAC5BK,QAAQ,EAAE;iBACX;aACF;SACF;QACDnJ,OAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE;QAC9BA,OAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE;QAC9B,SAASkD,KAAK,CAACkG,IAAI,EAAE;YACnB,IAAIpJ,OAAM,CAAC,SAAS,CAAC,EAAE;gBACrBA,OAAM,CAAC,SAAS,CAAC,CAACoJ,IAAI,CAAC;aACxB;YACDA,IAAI,IAAI,EAAE;YACV1G,IAAG,CAAC0G,IAAI,CAAC;YACThG,KAAK,GAAG,IAAI;YACZC,UAAU,GAAG,CAAC;YACd+F,IAAI,GAAG,QAAQ,GAAGA,IAAI,GAAG,8CAA8C;YACvE,IAAIC,CAAC,GAAG,IAAIpG,WAAW,CAACqG,YAAY,CAACF,IAAI,CAAC;YAC1ClJ,kBAAkB,CAACmJ,CAAC,CAAC;YACrB,MAAMA,CAAC,CAAA;SACR;QACD,IAAIE,aAAa,GAAG,uCAAuC;QAC3D,SAASC,SAAS,CAAC5H,QAAQ,EAAE;YAC3B,OAAOA,QAAQ,CAAC6H,UAAU,CAACF,aAAa,CAAC,CAAA;SAC1C;QACD,IAAIvJ,OAAM,CAAC,YAAY,CAAC,EAAE;YACxB,IAAI0J,cAAc,GAAG,uBAAuB;YAC5C,IAAI,CAACF,SAAS,CAACE,cAAc,CAAC,EAAE;gBAC9BA,cAAc,GAAGxI,UAAU,CAACwI,cAAc,CAAC;aAC5C;SACF,MAAM;YACL,MAAM,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAA;SAC7B;QACD,SAASC,SAAS,CAACC,IAAI,EAAE;YACvB,IAAI;gBACF,IAAIA,IAAI,IAAIH,cAAc,IAAI3G,UAAU,EAAE;oBACxC,OAAO,IAAIf,UAAU,CAACe,UAAU,CAAC,CAAA;iBAClC;gBACD,IAAI1B,UAAU,EAAE;oBACd,OAAOA,UAAU,CAACwI,IAAI,CAAC,CAAA;iBACxB,MAAM;oBACL,MAAM,iDAAiD,CAAA;iBACxD;aACF,CAAC,OAAOnH,GAAG,EAAE;gBACZQ,KAAK,CAACR,GAAG,CAAC;aACX;SACF;QACD,SAASoH,gBAAgB,GAAG;YAC1B,OAAO3J,OAAO,CAACC,OAAO,EAAE,CAAC2J,IAAI,CAAC,WAAY;gBACxC,OAAOH,SAAS,CAACF,cAAc,CAAC,CAAA;aACjC,CAAC,CAAA;SACH;QACD,SAASM,UAAU,GAAG;YACpB,IAAIC,IAAI,GAAG;gBAAEC,CAAC,EAAEC,aAAa;aAAE;YAC/B,SAASC,eAAe,CAACC,QAAQ,EAAEC,MAAM,EAAE;gBACzC,IAAIC,OAAO,GAAGF,QAAQ,CAACE,OAAO;gBAC9BvK,OAAM,CAAC,KAAK,CAAC,GAAGuK,OAAO;gBACvBpH,UAAU,GAAGnD,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;gBAC/BiH,0BAA0B,CAAC9D,UAAU,CAACpB,MAAM,CAAC;gBAC7C4F,SAAS,GAAG3H,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;gBAC9B2I,SAAS,CAAC3I,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC7BiJ,mBAAmB,CAAC,kBAAkB,CAAC;aACxC;YACDF,gBAAgB,CAAC,kBAAkB,CAAC;YACpC,SAASyB,0BAA0B,CAACC,MAAM,EAAE;gBAC1CL,eAAe,CAACK,MAAM,CAAC,UAAU,CAAC,CAAC;aACpC;YACD,SAASC,sBAAsB,CAACC,QAAQ,EAAE;gBACxC,OAAOb,gBAAgB,EAAE,CACtBC,IAAI,CAAC,SAAUlI,MAAM,EAAE;oBACtB,IAAI4I,MAAM,GAAGxH,WAAW,CAAC2H,WAAW,CAAC/I,MAAM,EAAEoI,IAAI,CAAC;oBAClD,OAAOQ,MAAM,CAAA;iBACd,CAAC,CACDV,IAAI,CAACY,QAAQ,EAAE,SAAUE,MAAM,EAAE;oBAChCnI,IAAG,CAAC,yCAAyC,GAAGmI,MAAM,CAAC;oBACvD3H,KAAK,CAAC2H,MAAM,CAAC;iBACd,CAAC,CAAA;aACL;YACD,SAASC,gBAAgB,GAAG;gBAC1B,OAAOJ,sBAAsB,CAACF,0BAA0B,CAAC,CAAA;aAC1D;YACD,IAAIxK,OAAM,CAAC,iBAAiB,CAAC,EAAE;gBAC7B,IAAI;oBACF,IAAIuK,QAAO,GAAGvK,OAAM,CAAC,iBAAiB,CAAC,CAACiK,IAAI,EAAEG,eAAe,CAAC;oBAC9D,OAAOG,QAAO,CAAA;iBACf,CAAC,OAAOlB,CAAC,EAAE;oBACV3G,IAAG,CAAC,qDAAqD,GAAG2G,CAAC,CAAC;oBAC9D,OAAO,KAAK,CAAA;iBACb;aACF;YACDyB,gBAAgB,EAAE,CAACC,KAAK,CAAC7K,kBAAkB,CAAC;YAC5C,OAAO,EAAE,CAAA;SACV;QACD,SAASkI,oBAAoB,CAAC4C,SAAS,EAAE;YACvC,MAAOA,SAAS,CAAC7I,MAAM,GAAG,CAAC,CAAE;gBAC3B,IAAIgH,QAAQ,GAAG6B,SAAS,CAAC7C,KAAK,EAAE;gBAChC,IAAI,OAAOgB,QAAQ,IAAI,UAAU,EAAE;oBACjCA,QAAQ,CAACnJ,OAAM,CAAC;oBAChB,SAAQ;iBACT;gBACD,IAAIiL,IAAI,GAAG9B,QAAQ,CAAC8B,IAAI;gBACxB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;oBAC5B,IAAI9B,QAAQ,CAAC+B,GAAG,KAAKrF,SAAS,EAAE;wBAC9B8B,SAAS,CAACwD,GAAG,CAACF,IAAI,CAAC,EAAE;qBACtB,MAAM;wBACLtD,SAAS,CAACwD,GAAG,CAACF,IAAI,CAAC,CAAC9B,QAAQ,CAAC+B,GAAG,CAAC;qBAClC;iBACF,MAAM;oBACLD,IAAI,CAAC9B,QAAQ,CAAC+B,GAAG,KAAKrF,SAAS,GAAG,IAAI,GAAGsD,QAAQ,CAAC+B,GAAG,CAAC;iBACvD;aACF;SACF;QACD,IAAIE,uBAAuB,GAAG,CAAC;QAC/B,SAASC,gBAAgB,GAAG;YAC1B,OAAOrI,aAAa,IAAIoI,uBAAuB,GAAG,CAAC,CAAA;SACpD;QACD,SAASE,OAAO,CAACL,IAAI,EAAEC,GAAG,EAAE,EAAE;QAC9B,SAASK,oBAAoB,CAACC,EAAE,EAAEC,EAAE,EAAE;YACpC,OAAOH,OAAO,CAACE,EAAE,EAAEC,EAAE,CAAC,CAAA;SACvB;QACD,IAAIC,mBAAmB,GAAG,EAAE;QAC5B,SAASC,cAAc,CAACC,WAAW,EAAE;YACnC,MAAOA,WAAW,CAACzJ,MAAM,CAAE;gBACzB,IAAIgC,GAAG,GAAGyH,WAAW,CAACC,GAAG,EAAE;gBAC3B,IAAIC,GAAG,GAAGF,WAAW,CAACC,GAAG,EAAE;gBAC3BC,GAAG,CAAC3H,GAAG,CAAC;aACT;SACF;QACD,SAAS4H,0BAA0B,CAACC,OAAO,EAAE;YAC3C,OAAO,IAAI,CAAC,cAAc,CAAC,CAAClF,OAAO,CAACkF,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;SACnD;QACD,IAAIC,oBAAoB,GAAG,EAAE;QAC7B,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIC,gBAAgB,GAAG,EAAE;QACzB,IAAIC,MAAM,GAAG,EAAE;QACf,IAAIC,MAAM,GAAG,EAAE;QACf,SAASC,qBAAqB,CAACC,IAAI,EAAE;YACnC,IAAI1G,SAAS,KAAK0G,IAAI,EAAE;gBACtB,OAAO,UAAU,CAAA;aAClB;YACDA,IAAI,GAAGA,IAAI,CAACnK,OAAO,mBAAmB,GAAG,CAAC;YAC1C,IAAIoK,CAAC,GAAGD,IAAI,CAACzH,UAAU,CAAC,CAAC,CAAC;YAC1B,IAAI0H,CAAC,IAAIJ,MAAM,IAAII,CAAC,IAAIH,MAAM,EAAE;gBAC9B,OAAO,GAAG,GAAGE,IAAI,CAAA;aAClB,MAAM;gBACL,OAAOA,IAAI,CAAA;aACZ;SACF;QACD,SAASE,mBAAmB,CAACF,IAAI,EAAEG,IAAI,EAAE;YACvCH,IAAI,GAAGD,qBAAqB,CAACC,IAAI,CAAC;YAClC,OAAO,IAAII,QAAQ,CACjB,MAAM,EACN,kBAAkB,GAChBJ,IAAI,GACJ,QAAQ,GACR,mBAAmB,GACnB,2CAA2C,GAC3C,MAAM,CACT,CAACG,IAAI,CAAC,CAAA;SACR;QACD,SAASE,WAAW,CAACC,aAAa,EAAEC,SAAS,EAAE;YAC7C,IAAIC,UAAU,GAAGN,mBAAmB,CAACK,SAAS,EAAE,SAAUE,OAAO,EAAE;gBACjE,IAAI,CAACT,IAAI,GAAGO,SAAS;gBACrB,IAAI,CAACE,OAAO,GAAGA,OAAO;gBACtB,IAAIC,KAAK,GAAG,IAAItD,KAAK,CAACqD,OAAO,CAAC,CAACC,KAAK;gBACpC,IAAIA,KAAK,KAAKpH,SAAS,EAAE;oBACvB,IAAI,CAACoH,KAAK,GACR,IAAI,CAACC,QAAQ,EAAE,GAAG,IAAI,GAAGD,KAAK,CAAC7K,OAAO,uBAAuB,EAAE,CAAC;iBACnE;aACF,CAAC;YACF2K,UAAU,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACR,aAAa,CAACM,SAAS,CAAC;YAC7DJ,UAAU,CAACI,SAAS,CAACG,WAAW,GAAGP,UAAU;YAC7CA,UAAU,CAACI,SAAS,CAACD,QAAQ,GAAG,WAAY;gBAC1C,IAAI,IAAI,CAACF,OAAO,KAAKnH,SAAS,EAAE;oBAC9B,OAAO,IAAI,CAAC0G,IAAI,CAAA;iBACjB,MAAM;oBACL,OAAO,IAAI,CAACA,IAAI,GAAG,IAAI,GAAG,IAAI,CAACS,OAAO,CAAA;iBACvC;aACF;YACD,OAAOD,UAAU,CAAA;SAClB;QACD,IAAIQ,aAAa,GAAG1H,SAAS;QAC7B,SAAS2H,kBAAkB,CAACR,OAAO,EAAE;YACnC,MAAM,IAAIO,aAAa,CAACP,OAAO,CAAC,CAAA;SACjC;QACD,SAASS,6BAA6B,CACpCC,OAAO,EACPC,cAAc,EACdC,iBAAiB,EACjB;YACAF,OAAO,CAACG,OAAO,CAAC,SAAUC,IAAI,EAAE;gBAC9B3B,gBAAgB,CAAC2B,IAAI,CAAC,GAAGH,cAAc;aACxC,CAAC;YACF,SAASI,UAAU,CAACC,cAAc,EAAE;gBAClC,IAAIC,gBAAgB,GAAGL,iBAAiB,CAACI,cAAc,CAAC;gBACxD,IAAIC,gBAAgB,CAAC9L,MAAM,KAAKuL,OAAO,CAACvL,MAAM,EAAE;oBAC9CqL,kBAAkB,CAAC,iCAAiC,CAAC;iBACtD;gBACD,IAAK,IAAI5I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8I,OAAO,CAACvL,MAAM,EAAE,EAAEyC,CAAC,CAAE;oBACvCsJ,YAAY,CAACR,OAAO,CAAC9I,CAAC,CAAC,EAAEqJ,gBAAgB,CAACrJ,CAAC,CAAC,CAAC;iBAC9C;aACF;YACD,IAAIoJ,eAAc,GAAG,IAAIG,KAAK,CAACR,cAAc,CAACxL,MAAM,CAAC;YACrD,IAAIiM,iBAAiB,GAAG,EAAE;YAC1B,IAAIC,UAAU,GAAG,CAAC;YAClBV,cAAc,CAACE,OAAO,CAAC,SAAUS,EAAE,EAAE1J,CAAC,EAAE;gBACtC,IAAIsH,eAAe,CAAC1L,cAAc,CAAC8N,EAAE,CAAC,EAAE;oBACtCN,eAAc,CAACpJ,CAAC,CAAC,GAAGsH,eAAe,CAACoC,EAAE,CAAC;iBACxC,MAAM;oBACLF,iBAAiB,CAACG,IAAI,CAACD,EAAE,CAAC;oBAC1B,IAAI,CAACrC,oBAAoB,CAACzL,cAAc,CAAC8N,EAAE,CAAC,EAAE;wBAC5CrC,oBAAoB,CAACqC,EAAE,CAAC,GAAG,EAAE;qBAC9B;oBACDrC,oBAAoB,CAACqC,EAAE,CAAC,CAACC,IAAI,CAAC,WAAY;wBACxCP,eAAc,CAACpJ,CAAC,CAAC,GAAGsH,eAAe,CAACoC,EAAE,CAAC;wBACvC,EAAED,UAAU;wBACZ,IAAIA,UAAU,KAAKD,iBAAiB,CAACjM,MAAM,EAAE;4BAC3C4L,UAAU,CAACC,eAAc,CAAC;yBAC3B;qBACF,CAAC;iBACH;aACF,CAAC;YACF,IAAI,CAAC,KAAKI,iBAAiB,CAACjM,MAAM,EAAE;gBAClC4L,UAAU,CAACC,eAAc,CAAC;aAC3B;SACF;QACD,SAASQ,8BAA8B,CAACC,UAAU,EAAE;YAClD,IAAIC,GAAG,GAAGhD,mBAAmB,CAAC+C,UAAU,CAAC;YACzC,OAAO/C,mBAAmB,CAAC+C,UAAU,CAAC;YACtC,IAAIE,cAAc,GAAGD,GAAG,CAACC,cAAc;YACvC,IAAIC,aAAa,GAAGF,GAAG,CAACE,aAAa;YACrC,IAAIC,YAAY,GAAGH,GAAG,CAACI,MAAM;YAC7B,IAAIC,WAAU,GAAGF,YAAY,CAC1BG,GAAG,CAAC,SAAUC,KAAK,EAAE;gBACpB,OAAOA,KAAK,CAACC,gBAAgB,CAAA;aAC9B,CAAC,CACDC,MAAM,CACLN,YAAY,CAACG,GAAG,CAAC,SAAUC,KAAK,EAAE;gBAChC,OAAOA,KAAK,CAACG,kBAAkB,CAAA;aAChC,CAAC,CACH;YACH3B,6BAA6B,CAC3B;gBAACgB,UAAU;aAAC,EACZM,WAAU,EACV,SAAUA,UAAU,EAAE;gBACpB,IAAID,MAAM,GAAG,EAAE;gBACfD,YAAY,CAAChB,OAAO,CAAC,SAAUoB,KAAK,EAAErK,CAAC,EAAE;oBACvC,IAAIyK,SAAS,GAAGJ,KAAK,CAACI,SAAS;oBAC/B,IAAIH,gBAAgB,GAAGH,UAAU,CAACnK,CAAC,CAAC;oBACpC,IAAI0K,MAAM,GAAGL,KAAK,CAACK,MAAM;oBACzB,IAAIC,aAAa,GAAGN,KAAK,CAACM,aAAa;oBACvC,IAAIH,kBAAkB,GAAGL,UAAU,CAACnK,CAAC,GAAGiK,YAAY,CAAC1M,MAAM,CAAC;oBAC5D,IAAIqN,MAAM,GAAGP,KAAK,CAACO,MAAM;oBACzB,IAAIC,aAAa,GAAGR,KAAK,CAACQ,aAAa;oBACvCX,MAAM,CAACO,SAAS,CAAC,GAAG;wBAClBK,IAAI,EAAE,SAAUvL,GAAG,EAAE;4BACnB,OAAO+K,gBAAgB,CAAC,cAAc,CAAC,CACrCI,MAAM,CAACC,aAAa,EAAEpL,GAAG,CAAC,CAC3B,CAAA;yBACF;wBACDwL,KAAK,EAAE,SAAUxL,GAAG,EAAEyL,CAAC,EAAE;4BACvB,IAAIhE,WAAW,GAAG,EAAE;4BACpB4D,MAAM,CACJC,aAAa,EACbtL,GAAG,EACHiL,kBAAkB,CAAC,YAAY,CAAC,CAACxD,WAAW,EAAEgE,CAAC,CAAC,CACjD;4BACDjE,cAAc,CAACC,WAAW,CAAC;yBAC5B;qBACF;iBACF,CAAC;gBACF,OAAO;oBACL;wBACEW,IAAI,EAAEmC,GAAG,CAACnC,IAAI;wBACdsD,YAAY,EAAE,SAAU1L,GAAG,EAAE;4BAC3B,IAAI2L,EAAE,GAAG,EAAE;4BACX,IAAK,IAAIlL,CAAC,IAAIkK,MAAM,CAAE;gCACpBgB,EAAE,CAAClL,CAAC,CAAC,GAAGkK,MAAM,CAAClK,CAAC,CAAC,CAAC8K,IAAI,CAACvL,GAAG,CAAC;6BAC5B;4BACDyK,aAAa,CAACzK,GAAG,CAAC;4BAClB,OAAO2L,EAAE,CAAA;yBACV;wBACDC,UAAU,EAAE,SAAUnE,WAAW,EAAEgE,CAAC,EAAE;4BACpC,IAAK,IAAIP,SAAS,IAAIP,MAAM,CAAE;gCAC5B,IAAI,CAAC,CAACO,SAAS,IAAIO,CAAC,CAAC,EAAE;oCACrB,MAAM,IAAII,SAAS,CAAC,mBAAmB,GAAGX,SAAS,GAAG,GAAG,CAAC,CAAA;iCAC3D;6BACF;4BACD,IAAIlL,GAAG,GAAGwK,cAAc,EAAE;4BAC1B,IAAKU,SAAS,IAAIP,MAAM,CAAE;gCACxBA,MAAM,CAACO,SAAS,CAAC,CAACM,KAAK,CAACxL,GAAG,EAAEyL,CAAC,CAACP,SAAS,CAAC,CAAC;6BAC3C;4BACD,IAAIzD,WAAW,KAAK,IAAI,EAAE;gCACxBA,WAAW,CAAC2C,IAAI,CAACK,aAAa,EAAEzK,GAAG,CAAC;6BACrC;4BACD,OAAOA,GAAG,CAAA;yBACX;wBACD8L,cAAc,EAAE,CAAC;wBACjBC,oBAAoB,EAAEnE,0BAA0B;wBAChDoE,kBAAkB,EAAEvB,aAAa;qBAClC;iBACF,CAAA;aACF,CACF;SACF;QACD,SAASwB,wBAAwB,CAC/BC,aAAa,EACb9D,IAAI,EACJ+D,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACR,EAAE;QACJ,SAASC,gBAAgB,CAACH,IAAI,EAAE;YAC9B,OAAQA,IAAI;gBACV,KAAK,CAAC;oBACJ,OAAO,CAAC,CAAA;gBACV,KAAK,CAAC;oBACJ,OAAO,CAAC,CAAA;gBACV,KAAK,CAAC;oBACJ,OAAO,CAAC,CAAA;gBACV,KAAK,CAAC;oBACJ,OAAO,CAAC,CAAA;gBACV;oBACE,MAAM,IAAIN,SAAS,CAAC,qBAAqB,GAAGM,IAAI,CAAC,CAAA;aACpD;SACF;QACD,SAASI,qBAAqB,GAAG;YAC/B,IAAIC,KAAK,GAAG,IAAIxC,KAAK,CAAC,GAAG,CAAC;YAC1B,IAAK,IAAIvJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,CAAE;gBAC5B+L,KAAK,CAAC/L,CAAC,CAAC,GAAGc,MAAM,CAACC,YAAY,CAACf,CAAC,CAAC;aAClC;YACDgM,gBAAgB,GAAGD,KAAK;SACzB;QACD,IAAIC,gBAAgB,GAAG/K,SAAS;QAChC,SAASgL,gBAAgB,CAAC1M,GAAG,EAAE;YAC7B,IAAIrC,GAAG,GAAG,EAAE;YACZ,IAAIgP,CAAC,GAAG3M,GAAG;YACX,MAAOG,MAAM,CAACwM,CAAC,CAAC,CAAE;gBAChBhP,GAAG,IAAI8O,gBAAgB,CAACtM,MAAM,CAACwM,CAAC,EAAE,CAAC,CAAC;aACrC;YACD,OAAOhP,GAAG,CAAA;SACX;QACD,IAAIiP,YAAY,GAAGlL,SAAS;QAC5B,SAASmL,iBAAiB,CAAChE,OAAO,EAAE;YAClC,MAAM,IAAI+D,YAAY,CAAC/D,OAAO,CAAC,CAAA;SAChC;QACD,SAASkB,YAAY,CAAC+C,OAAO,EAAEC,kBAAkB,EAAEC,OAAO,EAAE;YAC1DA,OAAO,GAAGA,OAAO,IAAI,EAAE;YACvB,IAAI,CAAC,CAAC,gBAAgB,IAAID,kBAAkB,CAAC,EAAE;gBAC7C,MAAM,IAAIlB,SAAS,CACjB,yDAAyD,CAC1D,CAAA;aACF;YACD,IAAIzD,IAAI,GAAG2E,kBAAkB,CAAC3E,IAAI;YAClC,IAAI,CAAC0E,OAAO,EAAE;gBACZD,iBAAiB,CACf,QAAQ,GAAGzE,IAAI,GAAG,+CAA+C,CAClE;aACF;YACD,IAAIL,eAAe,CAAC1L,cAAc,CAACyQ,OAAO,CAAC,EAAE;gBAC3C,IAAIE,OAAO,CAACC,4BAA4B,EAAE;oBACxC,OAAM;iBACP,MAAM;oBACLJ,iBAAiB,CAAC,wBAAwB,GAAGzE,IAAI,GAAG,SAAS,CAAC;iBAC/D;aACF;YACDL,eAAe,CAAC+E,OAAO,CAAC,GAAGC,kBAAkB;YAC7C,OAAO/E,gBAAgB,CAAC8E,OAAO,CAAC;YAChC,IAAIhF,oBAAoB,CAACzL,cAAc,CAACyQ,OAAO,CAAC,EAAE;gBAChD,IAAIjG,SAAS,GAAGiB,oBAAoB,CAACgF,OAAO,CAAC;gBAC7C,OAAOhF,oBAAoB,CAACgF,OAAO,CAAC;gBACpCjG,SAAS,CAAC6C,OAAO,CAAC,SAAUpF,EAAE,EAAE;oBAC9BA,EAAE,EAAE;iBACL,CAAC;aACH;SACF;QACD,SAAS4I,sBAAsB,CAC7BJ,OAAO,EACP1E,IAAI,EACJ+D,IAAI,EACJgB,SAAS,EACTC,UAAU,EACV;YACA,IAAIpJ,KAAK,GAAGsI,gBAAgB,CAACH,IAAI,CAAC;YAClC/D,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B2B,YAAY,CAAC+C,OAAO,EAAE;gBACpB1E,IAAI,EAAEA,IAAI;gBACVsD,YAAY,EAAE,SAAU2B,EAAE,EAAE;oBAC1B,OAAO,CAAC,CAACA,EAAE,CAAA;iBACZ;gBACDzB,UAAU,EAAE,SAAUnE,WAAW,EAAEgE,CAAC,EAAE;oBACpC,OAAOA,CAAC,GAAG0B,SAAS,GAAGC,UAAU,CAAA;iBAClC;gBACDtB,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAE,SAAUlE,OAAO,EAAE;oBACvC,IAAIrI,IAAI;oBACR,IAAI2M,IAAI,KAAK,CAAC,EAAE;wBACd3M,IAAI,GAAG+C,KAAK;qBACb,MAAM,IAAI4J,IAAI,KAAK,CAAC,EAAE;wBACrB3M,IAAI,GAAG8B,MAAM;qBACd,MAAM,IAAI6K,IAAI,KAAK,CAAC,EAAE;wBACrB3M,IAAI,GAAGwC,MAAM;qBACd,MAAM;wBACL,MAAM,IAAI6J,SAAS,CAAC,6BAA6B,GAAGzD,IAAI,CAAC,CAAA;qBAC1D;oBACD,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC5I,IAAI,CAACqI,OAAO,IAAI7D,KAAK,CAAC,CAAC,CAAA;iBACpD;gBACDgI,kBAAkB,EAAE,IAAI;aACzB,CAAC;SACH;QACD,IAAIsB,eAAe,GAAG,EAAE;QACxB,IAAIC,kBAAkB,GAAG;YACvB,EAAE;YACF;gBAAE5O,KAAK,EAAE+C,SAAS;aAAE;YACpB;gBAAE/C,KAAK,EAAE,IAAI;aAAE;YACf;gBAAEA,KAAK,EAAE,IAAI;aAAE;YACf;gBAAEA,KAAK,EAAE,KAAK;aAAE;SACjB;QACD,SAAS6O,cAAc,CAACC,MAAM,EAAE;YAC9B,IAAIA,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,EAAEF,kBAAkB,CAACE,MAAM,CAAC,CAACC,QAAQ,EAAE;gBAC7DH,kBAAkB,CAACE,MAAM,CAAC,GAAG/L,SAAS;gBACtC4L,eAAe,CAAClD,IAAI,CAACqD,MAAM,CAAC;aAC7B;SACF;QACD,SAASE,mBAAmB,GAAG;YAC7B,IAAIC,KAAK,GAAG,CAAC;YACb,IAAK,IAAInN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8M,kBAAkB,CAACvP,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBAClD,IAAI8M,kBAAkB,CAAC9M,CAAC,CAAC,KAAKiB,SAAS,EAAE;oBACvC,EAAEkM,KAAK;iBACR;aACF;YACD,OAAOA,KAAK,CAAA;SACb;QACD,SAASC,eAAe,GAAG;YACzB,IAAK,IAAIpN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8M,kBAAkB,CAACvP,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBAClD,IAAI8M,kBAAkB,CAAC9M,CAAC,CAAC,KAAKiB,SAAS,EAAE;oBACvC,OAAO6L,kBAAkB,CAAC9M,CAAC,CAAC,CAAA;iBAC7B;aACF;YACD,OAAO,IAAI,CAAA;SACZ;QACD,SAASqN,UAAU,GAAG;YACpBjS,OAAM,CAAC,qBAAqB,CAAC,GAAG8R,mBAAmB;YACnD9R,OAAM,CAAC,iBAAiB,CAAC,GAAGgS,eAAe;SAC5C;QACD,SAASE,gBAAgB,CAACpP,KAAK,EAAE;YAC/B,OAAQA,KAAK;gBACX,KAAK+C,SAAS;oBAAE;wBACd,OAAO,CAAC,CAAA;qBACT;gBACD,KAAK,IAAI;oBAAE;wBACT,OAAO,CAAC,CAAA;qBACT;gBACD,KAAK,IAAI;oBAAE;wBACT,OAAO,CAAC,CAAA;qBACT;gBACD,KAAK,KAAK;oBAAE;wBACV,OAAO,CAAC,CAAA;qBACT;gBACD;oBAAS;wBACP,IAAI+L,MAAM,GAAGH,eAAe,CAACtP,MAAM,GAC/BsP,eAAe,CAAC5F,GAAG,EAAE,GACrB6F,kBAAkB,CAACvP,MAAM;wBAC7BuP,kBAAkB,CAACE,MAAM,CAAC,GAAG;4BAAEC,QAAQ,EAAE,CAAC;4BAAE/O,KAAK,EAAEA,KAAK;yBAAE;wBAC1D,OAAO8O,MAAM,CAAA;qBACd;aACF;SACF;QACD,SAASO,uBAAuB,CAAClB,OAAO,EAAE1E,IAAI,EAAE;YAC9CA,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B2B,YAAY,CAAC+C,OAAO,EAAE;gBACpB1E,IAAI,EAAEA,IAAI;gBACVsD,YAAY,EAAE,SAAU+B,MAAM,EAAE;oBAC9B,IAAI9B,EAAE,GAAG4B,kBAAkB,CAACE,MAAM,CAAC,CAAC9O,KAAK;oBACzC6O,cAAc,CAACC,MAAM,CAAC;oBACtB,OAAO9B,EAAE,CAAA;iBACV;gBACDC,UAAU,EAAE,SAAUnE,WAAW,EAAE9I,KAAK,EAAE;oBACxC,OAAOoP,gBAAgB,CAACpP,KAAK,CAAC,CAAA;iBAC/B;gBACDmN,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEnE,0BAA0B;gBAChDoE,kBAAkB,EAAE,IAAI;aACzB,CAAC;SACH;QACD,SAASiC,YAAY,CAACC,CAAC,EAAE;YACvB,IAAIA,CAAC,KAAK,IAAI,EAAE;gBACd,OAAO,MAAM,CAAA;aACd;YACD,IAAIC,CAAC,GAAG,OAAOD,CAAC;YAChB,IAAIC,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,OAAO,IAAIA,CAAC,KAAK,UAAU,EAAE;gBACvD,OAAOD,CAAC,CAACnF,QAAQ,EAAE,CAAA;aACpB,MAAM;gBACL,OAAO,EAAE,GAAGmF,CAAC,CAAA;aACd;SACF;QACD,SAASE,yBAAyB,CAAChG,IAAI,EAAEpE,KAAK,EAAE;YAC9C,OAAQA,KAAK;gBACX,KAAK,CAAC;oBACJ,OAAO,SAAU6D,OAAO,EAAE;wBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,CAACjF,OAAO,CAACiF,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;qBACnD,CAAA;gBACH,KAAK,CAAC;oBACJ,OAAO,SAAUA,OAAO,EAAE;wBACxB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAChF,OAAO,CAACgF,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;qBACnD,CAAA;gBACH;oBACE,MAAM,IAAIgE,SAAS,CAAC,sBAAsB,GAAGzD,IAAI,CAAC,CAAA;aACrD;SACF;QACD,SAASiG,uBAAuB,CAACvB,OAAO,EAAE1E,IAAI,EAAE+D,IAAI,EAAE;YACpD,IAAInI,KAAK,GAAGsI,gBAAgB,CAACH,IAAI,CAAC;YAClC/D,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B2B,YAAY,CAAC+C,OAAO,EAAE;gBACpB1E,IAAI,EAAEA,IAAI;gBACVsD,YAAY,EAAE,SAAU/M,KAAK,EAAE;oBAC7B,OAAOA,KAAK,CAAA;iBACb;gBACDiN,UAAU,EAAE,SAAUnE,WAAW,EAAE9I,KAAK,EAAE;oBACxC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;wBAC3D,MAAM,IAAIkN,SAAS,CACjB,kBAAkB,GAAGoC,YAAY,CAACtP,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAACyJ,IAAI,CAC/D,CAAA;qBACF;oBACD,OAAOzJ,KAAK,CAAA;iBACb;gBACDmN,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEqC,yBAAyB,CAAChG,IAAI,EAAEpE,KAAK,CAAC;gBAC5DgI,kBAAkB,EAAE,IAAI;aACzB,CAAC;SACH;QACD,SAASsC,IAAI,CAACnF,WAAW,EAAEoF,YAAY,EAAE;YACvC,IAAI,CAAC,CAACpF,WAAW,YAAYX,QAAQ,CAAC,EAAE;gBACtC,MAAM,IAAIqD,SAAS,CACjB,oCAAoC,GAClC,OAAO1C,WAAW,GAClB,0BAA0B,CAC7B,CAAA;aACF;YACD,IAAIqF,KAAK,GAAGlG,mBAAmB,CAC7Ba,WAAW,CAACf,IAAI,IAAI,qBAAqB,EACzC,WAAY,EAAE,CACf;YACDoG,KAAK,CAACxF,SAAS,GAAGG,WAAW,CAACH,SAAS;YACvC,IAAIyF,GAAG,GAAG,IAAID,KAAK,EAAE;YACrB,IAAIE,CAAC,GAAGvF,WAAW,CAACwF,KAAK,CAACF,GAAG,EAAEF,YAAY,CAAC;YAC5C,OAAOG,CAAC,YAAYzF,MAAM,GAAGyF,CAAC,GAAGD,GAAG,CAAA;SACrC;QACD,SAASG,oBAAoB,CAC3BC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,aAAa,EACb;YACA,IAAIC,QAAQ,GAAGJ,QAAQ,CAAC9Q,MAAM;YAC9B,IAAIkR,QAAQ,GAAG,CAAC,EAAE;gBAChBrC,iBAAiB,CACf,gFAAgF,CACjF;aACF;YACD,IAAIsC,iBAAiB,GAAGL,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIC,SAAS,KAAK,IAAI;YAClE,IAAIK,oBAAoB,GAAG,KAAK;YAChC,IAAK,IAAI3O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,QAAQ,CAAC9Q,MAAM,EAAE,EAAEyC,CAAC,CAAE;gBACxC,IACEqO,QAAQ,CAACrO,CAAC,CAAC,KAAK,IAAI,IACpBqO,QAAQ,CAACrO,CAAC,CAAC,CAACuL,kBAAkB,KAAKtK,SAAS,EAC5C;oBACA0N,oBAAoB,GAAG,IAAI;oBAC3B,MAAK;iBACN;aACF;YACD,IAAIC,OAAO,GAAGP,QAAQ,CAAC,CAAC,CAAC,CAAC1G,IAAI,KAAK,MAAM;YACzC,IAAIkH,QAAQ,GAAG,EAAE;YACjB,IAAIC,aAAa,GAAG,EAAE;YACtB,IAAK,IAAI9O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyO,QAAQ,GAAG,CAAC,EAAE,EAAEzO,CAAC,CAAE;gBACrC6O,QAAQ,IAAI,CAAC7O,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK,GAAGA,CAAC;gBAC7C8O,aAAa,IAAI,CAAC9O,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAG,OAAO;aAC7D;YACD,IAAI+O,aAAa,GACf,kBAAkB,GAClBrH,qBAAqB,CAAC0G,SAAS,CAAC,GAChC,GAAG,GACHS,QAAQ,GACR,OAAO,GACP,2BAA2B,GAC3B,CAACJ,QAAQ,GAAG,CAAC,CAAC,GACd,OAAO,GACP,8BAA8B,GAC9BL,SAAS,GACT,4DAA4D,GAC5D,CAACK,QAAQ,GAAG,CAAC,CAAC,GACd,aAAa,GACb,KAAK;YACP,IAAIE,oBAAoB,EAAE;gBACxBI,aAAa,IAAI,yBAAyB;aAC3C;YACD,IAAIC,SAAS,GAAGL,oBAAoB,GAAG,aAAa,GAAG,MAAM;YAC7D,IAAIM,KAAK,GAAG;gBACV,mBAAmB;gBACnB,SAAS;gBACT,IAAI;gBACJ,gBAAgB;gBAChB,SAAS;gBACT,YAAY;aACb;YACD,IAAIC,KAAK,GAAG;gBACV9C,iBAAiB;gBACjBmC,cAAc;gBACdC,aAAa;gBACbzH,cAAc;gBACdsH,QAAQ,CAAC,CAAC,CAAC;gBACXA,QAAQ,CAAC,CAAC,CAAC;aACZ;YACD,IAAIK,iBAAiB,EAAE;gBACrBK,aAAa,IACX,wCAAwC,GAAGC,SAAS,GAAG,YAAY;aACtE;YACD,IAAK,IAAIhP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyO,QAAQ,GAAG,CAAC,EAAE,EAAEzO,CAAC,CAAE;gBACrC+O,aAAa,IACX,SAAS,GACT/O,CAAC,GACD,iBAAiB,GACjBA,CAAC,GACD,cAAc,GACdgP,SAAS,GACT,OAAO,GACPhP,CAAC,GACD,QAAQ,GACRqO,QAAQ,CAACrO,CAAC,GAAG,CAAC,CAAC,CAAC2H,IAAI,GACpB,IAAI;gBACNsH,KAAK,CAACtF,IAAI,CAAC,SAAS,GAAG3J,CAAC,CAAC;gBACzBkP,KAAK,CAACvF,IAAI,CAAC0E,QAAQ,CAACrO,CAAC,GAAG,CAAC,CAAC,CAAC;aAC5B;YACD,IAAI0O,iBAAiB,EAAE;gBACrBI,aAAa,GACX,WAAW,GAAG,CAACA,aAAa,CAACvR,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GAAGuR,aAAa;aACvE;YACDC,aAAa,IACX,CAACH,OAAO,GAAG,WAAW,GAAG,EAAE,CAAC,GAC5B,YAAY,GACZ,CAACE,aAAa,CAACvR,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GACtCuR,aAAa,GACb,MAAM;YACR,IAAIH,oBAAoB,EAAE;gBACxBI,aAAa,IAAI,gCAAgC;aAClD,MAAM;gBACL,IAAK,IAAI/O,CAAC,GAAG0O,iBAAiB,GAAG,CAAC,GAAG,CAAC,EAAE1O,CAAC,GAAGqO,QAAQ,CAAC9Q,MAAM,EAAE,EAAEyC,CAAC,CAAE;oBAChE,IAAImP,SAAS,GAAGnP,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,KAAK,GAAG,CAACA,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;oBACjE,IAAIqO,QAAQ,CAACrO,CAAC,CAAC,CAACuL,kBAAkB,KAAK,IAAI,EAAE;wBAC3CwD,aAAa,IACXI,SAAS,GACT,QAAQ,GACRA,SAAS,GACT,QAAQ,GACRd,QAAQ,CAACrO,CAAC,CAAC,CAAC2H,IAAI,GAChB,IAAI;wBACNsH,KAAK,CAACtF,IAAI,CAACwF,SAAS,GAAG,OAAO,CAAC;wBAC/BD,KAAK,CAACvF,IAAI,CAAC0E,QAAQ,CAACrO,CAAC,CAAC,CAACuL,kBAAkB,CAAC;qBAC3C;iBACF;aACF;YACD,IAAIqD,OAAO,EAAE;gBACXG,aAAa,IACX,uCAAuC,GAAG,eAAe;aAC5D,MAAM,EACN;YACDA,aAAa,IAAI,KAAK;YACtBE,KAAK,CAACtF,IAAI,CAACoF,aAAa,CAAC;YACzB,IAAIK,eAAe,GAAGvB,IAAI,CAAC9F,QAAQ,EAAEkH,KAAK,CAAC,CAACf,KAAK,CAAC,IAAI,EAAEgB,KAAK,CAAC;YAC9D,OAAOE,eAAe,CAAA;SACvB;QACD,SAASC,mBAAmB,CAACC,KAAK,EAAEC,UAAU,EAAEnB,SAAS,EAAE;YACzD,IAAInN,SAAS,KAAKqO,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,EAAE;gBACjD,IAAIC,QAAQ,GAAGH,KAAK,CAACC,UAAU,CAAC;gBAChCD,KAAK,CAACC,UAAU,CAAC,GAAG,WAAY;oBAC9B,IACE,CAACD,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,CAAC5T,cAAc,CAAC8T,SAAS,CAACnS,MAAM,CAAC,EACjE;wBACA6O,iBAAiB,CACf,YAAY,GACVgC,SAAS,GACT,gDAAgD,GAChDsB,SAAS,CAACnS,MAAM,GAChB,sBAAsB,GACtB+R,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,GAC/B,IAAI,CACP;qBACF;oBACD,OAAOF,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,CAACE,SAAS,CAACnS,MAAM,CAAC,CAAC2Q,KAAK,CAC5D,IAAI,EACJwB,SAAS,CACV,CAAA;iBACF;gBACDJ,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,GAAG,EAAE;gBACpCF,KAAK,CAACC,UAAU,CAAC,CAACC,aAAa,CAACC,QAAQ,CAAChB,QAAQ,CAAC,GAAGgB,QAAQ;aAC9D;SACF;QACD,SAASE,kBAAkB,CAAChI,IAAI,EAAEzJ,KAAK,EAAE0R,YAAY,EAAE;YACrD,IAAIxU,OAAM,CAACQ,cAAc,CAAC+L,IAAI,CAAC,EAAE;gBAC/B,IACE1G,SAAS,KAAK2O,YAAY,IACzB3O,SAAS,KAAK7F,OAAM,CAACuM,IAAI,CAAC,CAAC6H,aAAa,IACvCvO,SAAS,KAAK7F,OAAM,CAACuM,IAAI,CAAC,CAAC6H,aAAa,CAACI,YAAY,CAAC,AAAC,EACzD;oBACAxD,iBAAiB,CAAC,+BAA+B,GAAGzE,IAAI,GAAG,SAAS,CAAC;iBACtE;gBACD0H,mBAAmB,CAACjU,OAAM,EAAEuM,IAAI,EAAEA,IAAI,CAAC;gBACvC,IAAIvM,OAAM,CAACQ,cAAc,CAACgU,YAAY,CAAC,EAAE;oBACvCxD,iBAAiB,CACf,sFAAsF,GACpFwD,YAAY,GACZ,IAAI,CACP;iBACF;gBACDxU,OAAM,CAACuM,IAAI,CAAC,CAAC6H,aAAa,CAACI,YAAY,CAAC,GAAG1R,KAAK;aACjD,MAAM;gBACL9C,OAAM,CAACuM,IAAI,CAAC,GAAGzJ,KAAK;gBACpB,IAAI+C,SAAS,KAAK2O,YAAY,EAAE;oBAC9BxU,OAAM,CAACuM,IAAI,CAAC,CAACiI,YAAY,GAAGA,YAAY;iBACzC;aACF;SACF;QACD,SAASC,mBAAmB,CAAC1C,KAAK,EAAE2C,YAAY,EAAE;YAChD,IAAIC,KAAK,GAAG,EAAE;YACd,IAAK,IAAI/P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmN,KAAK,EAAEnN,CAAC,EAAE,CAAE;gBAC9B+P,KAAK,CAACpG,IAAI,CAACpI,MAAM,CAAC,CAACuO,YAAY,IAAI,CAAC,CAAC,GAAG9P,CAAC,CAAC,CAAC;aAC5C;YACD,OAAO+P,KAAK,CAAA;SACb;QACD,SAASC,mBAAmB,CAACrI,IAAI,EAAEzJ,KAAK,EAAE0R,YAAY,EAAE;YACtD,IAAI,CAACxU,OAAM,CAACQ,cAAc,CAAC+L,IAAI,CAAC,EAAE;gBAChCiB,kBAAkB,CAAC,qCAAqC,CAAC;aAC1D;YACD,IACE3H,SAAS,KAAK7F,OAAM,CAACuM,IAAI,CAAC,CAAC6H,aAAa,IACxCvO,SAAS,KAAK2O,YAAY,EAC1B;gBACAxU,OAAM,CAACuM,IAAI,CAAC,CAAC6H,aAAa,CAACI,YAAY,CAAC,GAAG1R,KAAK;aACjD,MAAM;gBACL9C,OAAM,CAACuM,IAAI,CAAC,GAAGzJ,KAAK;gBACpB9C,OAAM,CAACuM,IAAI,CAAC,CAAC8G,QAAQ,GAAGmB,YAAY;aACrC;SACF;QACD,SAASK,aAAa,CAACC,GAAG,EAAE3Q,GAAG,EAAE4Q,IAAI,EAAE;YACrC,IAAIvI,CAAC,GAAGxM,OAAM,CAAC,UAAU,GAAG8U,GAAG,CAAC;YAChC,OAAOC,IAAI,IAAIA,IAAI,CAAC5S,MAAM,GACtBqK,CAAC,CAACsG,KAAK,CAAC,IAAI,EAAE;gBAAC3O,GAAG;aAAC,CAACgL,MAAM,CAAC4F,IAAI,CAAC,CAAC,GACjCvI,CAAC,CAACwI,IAAI,CAAC,IAAI,EAAE7Q,GAAG,CAAC,CAAA;SACtB;QACD,SAAS8Q,OAAO,CAACH,GAAG,EAAE3Q,GAAG,EAAE4Q,IAAI,EAAE;YAC/B,IAAID,GAAG,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACrB,OAAOL,aAAa,CAACC,GAAG,EAAE3Q,GAAG,EAAE4Q,IAAI,CAAC,CAAA;aACrC;YACD,OAAOpN,SAAS,CAACwD,GAAG,CAAChH,GAAG,CAAC,CAAC2O,KAAK,CAAC,IAAI,EAAEiC,IAAI,CAAC,CAAA;SAC5C;QACD,SAASI,YAAY,CAACL,GAAG,EAAE3Q,GAAG,EAAE;YAC9B,IAAIiR,QAAQ,GAAG,EAAE;YACjB,OAAO,WAAY;gBACjBA,QAAQ,CAACjT,MAAM,GAAGmS,SAAS,CAACnS,MAAM;gBAClC,IAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0P,SAAS,CAACnS,MAAM,EAAEyC,CAAC,EAAE,CAAE;oBACzCwQ,QAAQ,CAACxQ,CAAC,CAAC,GAAG0P,SAAS,CAAC1P,CAAC,CAAC;iBAC3B;gBACD,OAAOqQ,OAAO,CAACH,GAAG,EAAE3Q,GAAG,EAAEiR,QAAQ,CAAC,CAAA;aACnC,CAAA;SACF;QACD,SAASC,uBAAuB,CAACC,SAAS,EAAEC,WAAW,EAAE;YACvDD,SAAS,GAAGzE,gBAAgB,CAACyE,SAAS,CAAC;YACvC,SAASE,aAAa,GAAG;gBACvB,IAAIF,SAAS,CAACJ,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC3B,OAAOC,YAAY,CAACG,SAAS,EAAEC,WAAW,CAAC,CAAA;iBAC5C;gBACD,OAAO5N,SAAS,CAACwD,GAAG,CAACoK,WAAW,CAAC,CAAA;aAClC;YACD,IAAIE,EAAE,GAAGD,aAAa,EAAE;YACxB,IAAI,OAAOC,EAAE,KAAK,UAAU,EAAE;gBAC5BzE,iBAAiB,CACf,0CAA0C,GACxCsE,SAAS,GACT,IAAI,GACJC,WAAW,CACd;aACF;YACD,OAAOE,EAAE,CAAA;SACV;QACD,IAAIC,gBAAgB,GAAG7P,SAAS;QAChC,SAAS8P,WAAW,CAAC7H,IAAI,EAAE;YACzB,IAAI3J,GAAG,GAAGyR,cAAc,CAAC9H,IAAI,CAAC;YAC9B,IAAIgC,EAAE,GAAGe,gBAAgB,CAAC1M,GAAG,CAAC;YAC9B0R,KAAK,CAAC1R,GAAG,CAAC;YACV,OAAO2L,EAAE,CAAA;SACV;QACD,SAASgG,qBAAqB,CAAC9I,OAAO,EAAE+I,KAAK,EAAE;YAC7C,IAAIC,YAAY,GAAG,EAAE;YACrB,IAAIC,IAAI,GAAG,EAAE;YACb,SAASC,KAAK,CAACpI,IAAI,EAAE;gBACnB,IAAImI,IAAI,CAACnI,IAAI,CAAC,EAAE;oBACd,OAAM;iBACP;gBACD,IAAI5B,eAAe,CAAC4B,IAAI,CAAC,EAAE;oBACzB,OAAM;iBACP;gBACD,IAAI3B,gBAAgB,CAAC2B,IAAI,CAAC,EAAE;oBAC1B3B,gBAAgB,CAAC2B,IAAI,CAAC,CAACD,OAAO,CAACqI,KAAK,CAAC;oBACrC,OAAM;iBACP;gBACDF,YAAY,CAACzH,IAAI,CAACT,IAAI,CAAC;gBACvBmI,IAAI,CAACnI,IAAI,CAAC,GAAG,IAAI;aAClB;YACDiI,KAAK,CAAClI,OAAO,CAACqI,KAAK,CAAC;YACpB,MAAM,IAAIR,gBAAgB,CACxB1I,OAAO,GAAG,IAAI,GAAGgJ,YAAY,CAAChH,GAAG,CAAC2G,WAAW,CAAC,CAACQ,IAAI,CAAC;gBAAC,IAAI;aAAC,CAAC,CAC5D,CAAA;SACF;QACD,SAASC,0BAA0B,CACjC7J,IAAI,EACJ8G,QAAQ,EACRgD,eAAe,EACff,SAAS,EACTgB,UAAU,EACVC,EAAE,EACF;YACA,IAAItD,SAAQ,GAAGwB,mBAAmB,CAACpB,QAAQ,EAAEgD,eAAe,CAAC;YAC7D9J,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B+J,UAAU,GAAGjB,uBAAuB,CAACC,SAAS,EAAEgB,UAAU,CAAC;YAC3D/B,kBAAkB,CAChBhI,IAAI,EACJ,WAAY;gBACVuJ,qBAAqB,CACnB,cAAc,GAAGvJ,IAAI,GAAG,uBAAuB,EAC/C0G,SAAQ,CACT;aACF,EACDI,QAAQ,GAAG,CAAC,CACb;YACD5F,6BAA6B,CAAC,EAAE,EAAEwF,SAAQ,EAAE,SAAUA,QAAQ,EAAE;gBAC9D,IAAIuD,gBAAgB,GAAG;oBAACvD,QAAQ,CAAC,CAAC,CAAC;oBAAE,IAAI;iBAAC,CAAC9D,MAAM,CAAC8D,QAAQ,CAAC5Q,KAAK,CAAC,CAAC,CAAC,CAAC;gBACpEuS,mBAAmB,CACjBrI,IAAI,EACJwG,oBAAoB,CAACxG,IAAI,EAAEiK,gBAAgB,EAAE,IAAI,EAAEF,UAAU,EAAEC,EAAE,CAAC,EAClElD,QAAQ,GAAG,CAAC,CACb;gBACD,OAAO,EAAE,CAAA;aACV,CAAC;SACH;QACD,SAASoD,2BAA2B,CAAClK,IAAI,EAAEpE,KAAK,EAAEuO,MAAM,EAAE;YACxD,OAAQvO,KAAK;gBACX,KAAK,CAAC;oBACJ,OAAOuO,MAAM,GACT,SAASC,iBAAiB,CAAC3K,OAAO,EAAE;wBAClC,OAAOtF,KAAK,CAACsF,OAAO,CAAC,CAAA;qBACtB,GACD,SAAS4K,iBAAiB,CAAC5K,OAAO,EAAE;wBAClC,OAAO1H,MAAM,CAAC0H,OAAO,CAAC,CAAA;qBACvB,CAAA;gBACP,KAAK,CAAC;oBACJ,OAAO0K,MAAM,GACT,SAASG,kBAAkB,CAAC7K,OAAO,EAAE;wBACnC,OAAOvG,MAAM,CAACuG,OAAO,IAAI,CAAC,CAAC,CAAA;qBAC5B,GACD,SAAS8K,kBAAkB,CAAC9K,OAAO,EAAE;wBACnC,OAAOzG,OAAO,CAACyG,OAAO,IAAI,CAAC,CAAC,CAAA;qBAC7B,CAAA;gBACP,KAAK,CAAC;oBACJ,OAAO0K,MAAM,GACT,SAASK,kBAAkB,CAAC/K,OAAO,EAAE;wBACnC,OAAO7F,MAAM,CAAC6F,OAAO,IAAI,CAAC,CAAC,CAAA;qBAC5B,GACD,SAASgL,kBAAkB,CAAChL,OAAO,EAAE;wBACnC,OAAOlF,OAAO,CAACkF,OAAO,IAAI,CAAC,CAAC,CAAA;qBAC7B,CAAA;gBACP;oBACE,MAAM,IAAIgE,SAAS,CAAC,wBAAwB,GAAGzD,IAAI,CAAC,CAAA;aACvD;SACF;QACD,SAAS0K,yBAAyB,CAChC5G,aAAa,EACb9D,IAAI,EACJ+D,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACR;YACAjE,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B,IAAIiE,QAAQ,KAAK,CAAC,CAAC,EAAE;gBACnBA,QAAQ,GAAG,UAAU;aACtB;YACD,IAAIrI,KAAK,GAAGsI,gBAAgB,CAACH,IAAI,CAAC;YAClC,IAAIT,YAAY,GAAG,SAAU/M,KAAK,EAAE;gBAClC,OAAOA,KAAK,CAAA;aACb;YACD,IAAIyN,QAAQ,KAAK,CAAC,EAAE;gBAClB,IAAI2G,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAG5G,IAAI;gBAC5BT,YAAY,GAAG,SAAU/M,KAAK,EAAE;oBAC9B,OAAO,AAACA,KAAK,IAAIoU,QAAQ,KAAMA,QAAQ,CAAA;iBACxC;aACF;YACD,IAAIC,cAAc,GAAG5K,IAAI,CAAC2I,QAAQ,CAAC,UAAU,CAAC;YAC9ChH,YAAY,CAACmC,aAAa,EAAE;gBAC1B9D,IAAI,EAAEA,IAAI;gBACVsD,YAAY,EAAEA,YAAY;gBAC1BE,UAAU,EAAE,SAAUnE,WAAW,EAAE9I,KAAK,EAAE;oBACxC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;wBAC3D,MAAM,IAAIkN,SAAS,CACjB,kBAAkB,GAAGoC,YAAY,CAACtP,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAACyJ,IAAI,CAC/D,CAAA;qBACF;oBACD,IAAIzJ,KAAK,GAAGyN,QAAQ,IAAIzN,KAAK,GAAG0N,QAAQ,EAAE;wBACxC,MAAM,IAAIR,SAAS,CACjB,oBAAoB,GAClBoC,YAAY,CAACtP,KAAK,CAAC,GACnB,uDAAuD,GACvDyJ,IAAI,GACJ,uCAAuC,GACvCgE,QAAQ,GACR,IAAI,GACJC,QAAQ,GACR,IAAI,CACP,CAAA;qBACF;oBACD,OAAO2G,cAAc,GAAGrU,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAA;iBAChD;gBACDmN,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEuG,2BAA2B,CAC/ClK,IAAI,EACJpE,KAAK,EACLoI,QAAQ,KAAK,CAAC,CACf;gBACDJ,kBAAkB,EAAE,IAAI;aACzB,CAAC;SACH;QACD,SAASiH,6BAA6B,CAACnG,OAAO,EAAEoG,aAAa,EAAE9K,IAAI,EAAE;YACnE,IAAI+K,WAAW,GAAG;gBAChBnQ,SAAS;gBACTnF,UAAU;gBACVoF,UAAU;gBACVE,WAAW;gBACXD,UAAU;gBACVE,WAAW;gBACXC,YAAY;gBACZC,YAAY;aACb;YACD,IAAI8P,EAAE,GAAGD,WAAW,CAACD,aAAa,CAAC;YACnC,SAASG,gBAAgB,CAAC5F,MAAM,EAAE;gBAChCA,MAAM,GAAGA,MAAM,IAAI,CAAC;gBACpB,IAAIjO,IAAI,GAAGmD,OAAO;gBAClB,IAAIwJ,IAAI,GAAG3M,IAAI,CAACiO,MAAM,CAAC;gBACvB,IAAI6F,IAAI,GAAG9T,IAAI,CAACiO,MAAM,GAAG,CAAC,CAAC;gBAC3B,OAAO,IAAI2F,EAAE,CAACxV,OAAM,EAAE0V,IAAI,EAAEnH,IAAI,CAAC,CAAA;aAClC;YACD/D,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B2B,YAAY,CACV+C,OAAO,EACP;gBACE1E,IAAI,EAAEA,IAAI;gBACVsD,YAAY,EAAE2H,gBAAgB;gBAC9BvH,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEsH,gBAAgB;aACvC,EACD;gBAAEpG,4BAA4B,EAAE,IAAI;aAAE,CACvC;SACF;QACD,SAASsG,4BAA4B,CAACzG,OAAO,EAAE1E,IAAI,EAAE;YACnDA,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B,IAAIoL,eAAe,GAAGpL,IAAI,KAAK,aAAa;YAC5C2B,YAAY,CAAC+C,OAAO,EAAE;gBACpB1E,IAAI,EAAEA,IAAI;gBACVsD,YAAY,EAAE,SAAU/M,KAAK,EAAE;oBAC7B,IAAIX,MAAM,GAAG2E,OAAO,CAAChE,KAAK,IAAI,CAAC,CAAC;oBAChC,IAAI0B,GAAG;oBACP,IAAImT,eAAe,EAAE;wBACnB,IAAIC,cAAc,GAAG9U,KAAK,GAAG,CAAC;wBAC9B,IAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;4BAChC,IAAIiT,cAAc,GAAG/U,KAAK,GAAG,CAAC,GAAG8B,CAAC;4BAClC,IAAIA,CAAC,IAAIzC,MAAM,IAAImC,MAAM,CAACuT,cAAc,CAAC,IAAI,CAAC,EAAE;gCAC9C,IAAIC,OAAO,GAAGD,cAAc,GAAGD,cAAc;gCAC7C,IAAIG,aAAa,GAAG7T,YAAY,CAAC0T,cAAc,EAAEE,OAAO,CAAC;gCACzD,IAAItT,GAAG,KAAKqB,SAAS,EAAE;oCACrBrB,GAAG,GAAGuT,aAAa;iCACpB,MAAM;oCACLvT,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAAC,CAAC,CAAC;oCAC7BnB,GAAG,IAAIuT,aAAa;iCACrB;gCACDH,cAAc,GAAGC,cAAc,GAAG,CAAC;6BACpC;yBACF;qBACF,MAAM;wBACL,IAAI3N,CAAC,GAAG,IAAIiE,KAAK,CAAChM,MAAM,CAAC;wBACzB,IAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;4BAC/BsF,CAAC,CAACtF,CAAC,CAAC,GAAGc,MAAM,CAACC,YAAY,CAACrB,MAAM,CAACxB,KAAK,GAAG,CAAC,GAAG8B,CAAC,CAAC,CAAC;yBAClD;wBACDJ,GAAG,GAAG0F,CAAC,CAACiM,IAAI,CAAC,EAAE,CAAC;qBACjB;oBACDN,KAAK,CAAC/S,KAAK,CAAC;oBACZ,OAAO0B,GAAG,CAAA;iBACX;gBACDuL,UAAU,EAAE,SAAUnE,WAAW,EAAE9I,KAAK,EAAE;oBACxC,IAAIA,KAAK,YAAYkV,WAAW,EAAE;wBAChClV,KAAK,GAAG,IAAId,UAAU,CAACc,KAAK,CAAC;qBAC9B;oBACD,IAAImV,SAAS;oBACb,IAAIC,mBAAmB,GAAG,OAAOpV,KAAK,KAAK,QAAQ;oBACnD,IACE,CAAC,CACCoV,mBAAmB,IACnBpV,KAAK,YAAYd,UAAU,IAC3Bc,KAAK,YAAYqV,iBAAiB,IAClCrV,KAAK,YAAYqE,SAAS,CAC3B,EACD;wBACA6J,iBAAiB,CAAC,uCAAuC,CAAC;qBAC3D;oBACD,IAAI2G,eAAe,IAAIO,mBAAmB,EAAE;wBAC1CD,SAAS,GAAG,WAAY;4BACtB,OAAO/S,eAAe,CAACpC,KAAK,CAAC,CAAA;yBAC9B;qBACF,MAAM;wBACLmV,SAAS,GAAG,WAAY;4BACtB,OAAOnV,KAAK,CAACX,MAAM,CAAA;yBACpB;qBACF;oBACD,IAAIA,MAAM,GAAG8V,SAAS,EAAE;oBACxB,IAAI9T,GAAG,GAAGiU,OAAO,CAAC,CAAC,GAAGjW,MAAM,GAAG,CAAC,CAAC;oBACjC2E,OAAO,CAAC3C,GAAG,IAAI,CAAC,CAAC,GAAGhC,MAAM;oBAC1B,IAAIwV,eAAe,IAAIO,mBAAmB,EAAE;wBAC1ClT,YAAY,CAAClC,KAAK,EAAEqB,GAAG,GAAG,CAAC,EAAEhC,MAAM,GAAG,CAAC,CAAC;qBACzC,MAAM;wBACL,IAAI+V,mBAAmB,EAAE;4BACvB,IAAK,IAAItT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gCAC/B,IAAIyT,QAAQ,GAAGvV,KAAK,CAACgC,UAAU,CAACF,CAAC,CAAC;gCAClC,IAAIyT,QAAQ,GAAG,GAAG,EAAE;oCAClBxC,KAAK,CAAC1R,GAAG,CAAC;oCACV6M,iBAAiB,CACf,wDAAwD,CACzD;iCACF;gCACD1M,MAAM,CAACH,GAAG,GAAG,CAAC,GAAGS,CAAC,CAAC,GAAGyT,QAAQ;6BAC/B;yBACF,MAAM;4BACL,IAAK,IAAIzT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;gCAC/BN,MAAM,CAACH,GAAG,GAAG,CAAC,GAAGS,CAAC,CAAC,GAAG9B,KAAK,CAAC8B,CAAC,CAAC;6BAC/B;yBACF;qBACF;oBACD,IAAIgH,WAAW,KAAK,IAAI,EAAE;wBACxBA,WAAW,CAAC2C,IAAI,CAACsH,KAAK,EAAE1R,GAAG,CAAC;qBAC7B;oBACD,OAAOA,GAAG,CAAA;iBACX;gBACD8L,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEnE,0BAA0B;gBAChDoE,kBAAkB,EAAE,SAAUhM,GAAG,EAAE;oBACjC0R,KAAK,CAAC1R,GAAG,CAAC;iBACX;aACF,CAAC;SACH;QACD,SAASmU,6BAA6B,CAACrH,OAAO,EAAEsH,QAAQ,EAAEhM,IAAI,EAAE;YAC9DA,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B,IAAIiM,YAAY,EAAEC,YAAY,EAAEC,OAAO,EAAEC,cAAc,EAAExQ,KAAK;YAC9D,IAAIoQ,QAAQ,KAAK,CAAC,EAAE;gBAClBC,YAAY,GAAGnT,aAAa;gBAC5BoT,YAAY,GAAG7S,aAAa;gBAC5B+S,cAAc,GAAG3S,gBAAgB;gBACjC0S,OAAO,GAAG,WAAY;oBACpB,OAAOnT,OAAO,CAAA;iBACf;gBACD4C,KAAK,GAAG,CAAC;aACV,MAAM,IAAIoQ,QAAQ,KAAK,CAAC,EAAE;gBACzBC,YAAY,GAAGvS,aAAa;gBAC5BwS,YAAY,GAAGpS,aAAa;gBAC5BsS,cAAc,GAAGpS,gBAAgB;gBACjCmS,OAAO,GAAG,WAAY;oBACpB,OAAO5R,OAAO,CAAA;iBACf;gBACDqB,KAAK,GAAG,CAAC;aACV;YACD+F,YAAY,CAAC+C,OAAO,EAAE;gBACpB1E,IAAI,EAAEA,IAAI;gBACVsD,YAAY,EAAE,SAAU/M,KAAK,EAAE;oBAC7B,IAAIX,MAAM,GAAG2E,OAAO,CAAChE,KAAK,IAAI,CAAC,CAAC;oBAChC,IAAI8V,IAAI,GAAGF,OAAO,EAAE;oBACpB,IAAIlU,GAAG;oBACP,IAAIoT,cAAc,GAAG9U,KAAK,GAAG,CAAC;oBAC9B,IAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIzC,MAAM,EAAE,EAAEyC,CAAC,CAAE;wBAChC,IAAIiT,cAAc,GAAG/U,KAAK,GAAG,CAAC,GAAG8B,CAAC,GAAG2T,QAAQ;wBAC7C,IAAI3T,CAAC,IAAIzC,MAAM,IAAIyW,IAAI,CAACf,cAAc,IAAI1P,KAAK,CAAC,IAAI,CAAC,EAAE;4BACrD,IAAI0Q,YAAY,GAAGhB,cAAc,GAAGD,cAAc;4BAClD,IAAIG,aAAa,GAAGS,YAAY,CAACZ,cAAc,EAAEiB,YAAY,CAAC;4BAC9D,IAAIrU,GAAG,KAAKqB,SAAS,EAAE;gCACrBrB,GAAG,GAAGuT,aAAa;6BACpB,MAAM;gCACLvT,GAAG,IAAIkB,MAAM,CAACC,YAAY,CAAC,CAAC,CAAC;gCAC7BnB,GAAG,IAAIuT,aAAa;6BACrB;4BACDH,cAAc,GAAGC,cAAc,GAAGU,QAAQ;yBAC3C;qBACF;oBACD1C,KAAK,CAAC/S,KAAK,CAAC;oBACZ,OAAO0B,GAAG,CAAA;iBACX;gBACDuL,UAAU,EAAE,SAAUnE,WAAW,EAAE9I,KAAK,EAAE;oBACxC,IAAI,CAAC,CAAC,OAAOA,KAAK,KAAK,QAAQ,CAAC,EAAE;wBAChCkO,iBAAiB,CACf,4CAA4C,GAAGzE,IAAI,CACpD;qBACF;oBACD,IAAIpK,MAAM,GAAGwW,cAAc,CAAC7V,KAAK,CAAC;oBAClC,IAAIqB,GAAG,GAAGiU,OAAO,CAAC,CAAC,GAAGjW,MAAM,GAAGoW,QAAQ,CAAC;oBACxCzR,OAAO,CAAC3C,GAAG,IAAI,CAAC,CAAC,GAAGhC,MAAM,IAAIgG,KAAK;oBACnCsQ,YAAY,CAAC3V,KAAK,EAAEqB,GAAG,GAAG,CAAC,EAAEhC,MAAM,GAAGoW,QAAQ,CAAC;oBAC/C,IAAI3M,WAAW,KAAK,IAAI,EAAE;wBACxBA,WAAW,CAAC2C,IAAI,CAACsH,KAAK,EAAE1R,GAAG,CAAC;qBAC7B;oBACD,OAAOA,GAAG,CAAA;iBACX;gBACD8L,cAAc,EAAE,CAAC;gBACjBC,oBAAoB,EAAEnE,0BAA0B;gBAChDoE,kBAAkB,EAAE,SAAUhM,GAAG,EAAE;oBACjC0R,KAAK,CAAC1R,GAAG,CAAC;iBACX;aACF,CAAC;SACH;QACD,SAAS2U,8BAA8B,CACrC7H,OAAO,EACP1E,IAAI,EACJwM,oBAAoB,EACpBpK,cAAc,EACdqK,mBAAmB,EACnBpK,aAAa,EACb;YACAlD,mBAAmB,CAACuF,OAAO,CAAC,GAAG;gBAC7B1E,IAAI,EAAEsE,gBAAgB,CAACtE,IAAI,CAAC;gBAC5BoC,cAAc,EAAE0G,uBAAuB,CACrC0D,oBAAoB,EACpBpK,cAAc,CACf;gBACDC,aAAa,EAAEyG,uBAAuB,CACpC2D,mBAAmB,EACnBpK,aAAa,CACd;gBACDE,MAAM,EAAE,EAAE;aACX;SACF;QACD,SAASmK,oCAAoC,CAC3CxK,UAAU,EACVY,SAAS,EACTH,gBAAgB,EAChBgK,eAAe,EACf5J,MAAM,EACNC,aAAa,EACbH,kBAAkB,EAClB+J,eAAe,EACf3J,MAAM,EACNC,aAAa,EACb;YACA/D,mBAAmB,CAAC+C,UAAU,CAAC,CAACK,MAAM,CAACP,IAAI,CAAC;gBAC1Cc,SAAS,EAAEwB,gBAAgB,CAACxB,SAAS,CAAC;gBACtCH,gBAAgB,EAAEA,gBAAgB;gBAClCI,MAAM,EAAE+F,uBAAuB,CAAC6D,eAAe,EAAE5J,MAAM,CAAC;gBACxDC,aAAa,EAAEA,aAAa;gBAC5BH,kBAAkB,EAAEA,kBAAkB;gBACtCI,MAAM,EAAE6F,uBAAuB,CAAC8D,eAAe,EAAE3J,MAAM,CAAC;gBACxDC,aAAa,EAAEA,aAAa;aAC7B,CAAC;SACH;QACD,SAAS2J,sBAAsB,CAACnI,OAAO,EAAE1E,IAAI,EAAE;YAC7CA,IAAI,GAAGsE,gBAAgB,CAACtE,IAAI,CAAC;YAC7B2B,YAAY,CAAC+C,OAAO,EAAE;gBACpBoI,MAAM,EAAE,IAAI;gBACZ9M,IAAI,EAAEA,IAAI;gBACV0D,cAAc,EAAE,CAAC;gBACjBJ,YAAY,EAAE,WAAY;oBACxB,OAAOhK,SAAS,CAAA;iBACjB;gBACDkK,UAAU,EAAE,SAAUnE,WAAW,EAAEgE,CAAC,EAAE;oBACpC,OAAO/J,SAAS,CAAA;iBACjB;aACF,CAAC;SACH;QACD,IAAIyT,aAAa,GAAG,EAAE;QACtB,SAASC,iBAAiB,CAACC,OAAO,EAAE;YAClC,IAAIC,MAAM,GAAGH,aAAa,CAACE,OAAO,CAAC;YACnC,IAAIC,MAAM,KAAK5T,SAAS,EAAE;gBACxB,OAAOgL,gBAAgB,CAAC2I,OAAO,CAAC,CAAA;aACjC,MAAM;gBACL,OAAOC,MAAM,CAAA;aACd;SACF;QACD,SAASC,gBAAgB,GAAG;YAC1B,IAAI,OAAOC,UAAU,KAAK,QAAQ,EAAE;gBAClC,OAAOA,UAAU,CAAA;aAClB;YACD,OAAO,CAAC,WAAY;gBAClB,OAAOhN,QAAQ,CAAA;aAChB,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,CAAA;SACtB;QACD,SAASiN,kBAAkB,CAACrN,IAAI,EAAE;YAChC,IAAIA,IAAI,KAAK,CAAC,EAAE;gBACd,OAAO2F,gBAAgB,CAACwH,gBAAgB,EAAE,CAAC,CAAA;aAC5C,MAAM;gBACLnN,IAAI,GAAGgN,iBAAiB,CAAChN,IAAI,CAAC;gBAC9B,OAAO2F,gBAAgB,CAACwH,gBAAgB,EAAE,CAACnN,IAAI,CAAC,CAAC,CAAA;aAClD;SACF;QACD,SAASsN,cAAc,CAACjI,MAAM,EAAE;YAC9B,IAAIA,MAAM,GAAG,CAAC,EAAE;gBACdF,kBAAkB,CAACE,MAAM,CAAC,CAACC,QAAQ,IAAI,CAAC;aACzC;SACF;QACD,SAASiI,qBAAqB,CAAC7I,OAAO,EAAE+B,SAAS,EAAE;YACjD,IAAI+G,IAAI,GAAG7N,eAAe,CAAC+E,OAAO,CAAC;YACnC,IAAIpL,SAAS,KAAKkU,IAAI,EAAE;gBACtB/I,iBAAiB,CACfgC,SAAS,GAAG,oBAAoB,GAAG2C,WAAW,CAAC1E,OAAO,CAAC,CACxD;aACF;YACD,OAAO8I,IAAI,CAAA;SACZ;QACD,SAASC,mBAAmB,CAAC3G,QAAQ,EAAE;YACrC,IAAII,QAAQ,GAAG,EAAE;YACjB,IAAK,IAAI7O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyO,QAAQ,EAAE,EAAEzO,CAAC,CAAE;gBACjC6O,QAAQ,IAAI,CAAC7O,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK,GAAGA,CAAC;aAC9C;YACD,IAAIqV,YAAY,GACd,kCAAkC,GAClC5G,QAAQ,GACR,mCAAmC;YACrC,IAAK,IAAIzO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyO,QAAQ,EAAE,EAAEzO,CAAC,CAAE;gBACjCqV,YAAY,IACV,aAAa,GACbrV,CAAC,GACD,+DAA+D,GAC/DA,CAAC,GACD,gBAAgB,GAChBA,CAAC,GACD,OAAO,GACP,SAAS,GACTA,CAAC,GACD,YAAY,GACZA,CAAC,GACD,gCAAgC,GAChC,iBAAiB,GACjBA,CAAC,GACD,uBAAuB;aAC1B;YACDqV,YAAY,IACV,4BAA4B,GAC5BxG,QAAQ,GACR,MAAM,GACN,iCAAiC,GACjC,KAAK;YACP,OAAO,IAAI9G,QAAQ,CACjB,uBAAuB,EACvB,QAAQ,EACR,kBAAkB,EAClBsN,YAAY,CACb,CAACH,qBAAqB,EAAE9Z,OAAM,EAAEkS,gBAAgB,CAAC,CAAA;SACnD;QACD,IAAIgI,YAAY,GAAG,EAAE;QACrB,SAASC,aAAa,CAACvI,MAAM,EAAE;YAC7B,IAAI,CAACA,MAAM,EAAE;gBACXZ,iBAAiB,CAAC,mCAAmC,GAAGY,MAAM,CAAC;aAChE;YACD,OAAOF,kBAAkB,CAACE,MAAM,CAAC,CAAC9O,KAAK,CAAA;SACxC;QACD,SAASsX,WAAW,CAACxI,MAAM,EAAEyB,QAAQ,EAAEJ,QAAQ,EAAE8B,IAAI,EAAE;YACrDnD,MAAM,GAAGuI,aAAa,CAACvI,MAAM,CAAC;YAC9B,IAAIyI,KAAK,GAAGH,YAAY,CAAC7G,QAAQ,CAAC;YAClC,IAAI,CAACgH,KAAK,EAAE;gBACVA,KAAK,GAAGL,mBAAmB,CAAC3G,QAAQ,CAAC;gBACrC6G,YAAY,CAAC7G,QAAQ,CAAC,GAAGgH,KAAK;aAC/B;YACD,OAAOA,KAAK,CAACzI,MAAM,EAAEqB,QAAQ,EAAE8B,IAAI,CAAC,CAAA;SACrC;QACD,SAASuF,MAAM,GAAG;YAChBpX,KAAK,EAAE;SACR;QACD,SAASqX,sBAAsB,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;YAC9CpW,MAAM,CAACqW,UAAU,CAACH,IAAI,EAAEC,GAAG,EAAEA,GAAG,GAAGC,GAAG,CAAC;SACxC;QACD,SAASE,yBAAyB,CAACtK,IAAI,EAAE;YACvC,IAAI;gBACFnN,UAAU,CAAC0X,IAAI,CAAC,AAACvK,IAAI,GAAGvO,OAAM,CAAC+Y,UAAU,GAAG,KAAK,KAAM,EAAE,CAAC;gBAC1D7T,0BAA0B,CAAC9D,UAAU,CAACpB,MAAM,CAAC;gBAC7C,OAAO,CAAC,CAAA;aACT,CAAC,OAAOsH,CAAC,EAAE,EAAE;SACf;QACD,SAAS0R,uBAAuB,CAACC,aAAa,EAAE;YAC9C,IAAIC,OAAO,GAAG3W,MAAM,CAACnC,MAAM;YAC3B6Y,aAAa,GAAGA,aAAa,KAAK,CAAC;YACnC,IAAIE,WAAW,GAAG,UAAU;YAC5B,IAAIF,aAAa,GAAGE,WAAW,EAAE;gBAC/B,OAAO,KAAK,CAAA;aACb;YACD,IAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAI,CAAC,EAAEA,OAAO,IAAI,CAAC,CAAE;gBAChD,IAAIC,iBAAiB,GAAGH,OAAO,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGE,OAAO,CAAC;gBACrDC,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAC1BF,iBAAiB,EACjBJ,aAAa,GAAG,SAAS,CAC1B;gBACD,IAAIO,OAAO,GAAGF,IAAI,CAACC,GAAG,CACpBJ,WAAW,EACXvU,OAAO,CAAC0U,IAAI,CAACG,GAAG,CAACR,aAAa,EAAEI,iBAAiB,CAAC,EAAE,KAAK,CAAC,CAC3D;gBACD,IAAIK,WAAW,GAAGb,yBAAyB,CAACW,OAAO,CAAC;gBACpD,IAAIE,WAAW,EAAE;oBACf,OAAO,IAAI,CAAA;iBACZ;aACF;YACD,OAAO,KAAK,CAAA;SACb;QACD,IAAIC,GAAG,GAAG,EAAE;QACZ,SAASC,iBAAiB,GAAG;YAC3B,OAAOjb,WAAW,IAAI,gBAAgB,CAAA;SACvC;QACD,SAASkb,aAAa,GAAG;YACvB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;gBAC1B,IAAIC,IAAI,GACN,CACE,AAAC,OAAOC,SAAS,KAAK,QAAQ,IAC5BA,SAAS,CAACC,SAAS,IACnBD,SAAS,CAACC,SAAS,CAAC,CAAC,CAAC,IACxB,GAAG,CACJ,CAAC5Z,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,QAAQ;gBAChC,IAAI6Z,GAAG,GAAG;oBACRC,IAAI,EAAE,UAAU;oBAChBC,OAAO,EAAE,UAAU;oBACnBC,IAAI,EAAE,GAAG;oBACTC,GAAG,EAAE,GAAG;oBACRC,IAAI,EAAE,gBAAgB;oBACtBC,IAAI,EAAET,IAAI;oBACVU,CAAC,EAAEb,iBAAiB,EAAE;iBACvB;gBACD,IAAK,IAAI/U,CAAC,IAAI8U,GAAG,CAAE;oBACjBO,GAAG,CAACrV,CAAC,CAAC,GAAG8U,GAAG,CAAC9U,CAAC,CAAC;iBAChB;gBACD,IAAIiV,OAAO,GAAG,EAAE;gBAChB,IAAK,IAAIjV,CAAC,IAAIqV,GAAG,CAAE;oBACjBJ,OAAO,CAACtN,IAAI,CAAC3H,CAAC,GAAG,GAAG,GAAGqV,GAAG,CAACrV,CAAC,CAAC,CAAC;iBAC/B;gBACDgV,aAAa,CAACC,OAAO,GAAGA,OAAO;aAChC;YACD,OAAOD,aAAa,CAACC,OAAO,CAAA;SAC7B;QACD,IAAIY,QAAQ,GAAG;YACbC,QAAQ,EAAE,EAAE;YACZC,OAAO,EAAE;gBAAC,IAAI;gBAAE,EAAE;gBAAE,EAAE;aAAC;YACvBC,SAAS,EAAE,SAAUC,MAAM,EAAEC,IAAI,EAAE;gBACjC,IAAI/a,MAAM,GAAG0a,QAAQ,CAACE,OAAO,CAACE,MAAM,CAAC;gBACrC,IAAIC,IAAI,KAAK,CAAC,IAAIA,IAAI,KAAK,EAAE,EAAE;oBAC5B,CAACD,MAAM,KAAK,CAAC,GAAGva,GAAG,GAAGI,IAAG,CAAC,CAACgB,iBAAiB,CAAC3B,MAAM,EAAE,CAAC,CAAC,CAAC;oBACzDA,MAAM,CAACI,MAAM,GAAG,CAAC;iBAClB,MAAM;oBACLJ,MAAM,CAACwM,IAAI,CAACuO,IAAI,CAAC;iBAClB;aACF;YACDC,OAAO,EAAElX,SAAS;YAClBsF,GAAG,EAAE,WAAY;gBACfsR,QAAQ,CAACM,OAAO,IAAI,CAAC;gBACrB,IAAIjb,GAAG,GAAGqE,MAAM,CAAC,AAACsW,QAAQ,CAACM,OAAO,GAAG,CAAC,IAAK,CAAC,CAAC;gBAC7C,OAAOjb,GAAG,CAAA;aACX;YACDkb,MAAM,EAAE,SAAU7Y,GAAG,EAAE;gBACrB,IAAIrC,GAAG,GAAGoC,YAAY,CAACC,GAAG,CAAC;gBAC3B,OAAOrC,GAAG,CAAA;aACX;YACDmb,KAAK,EAAE,SAAUC,GAAG,EAAEC,IAAI,EAAE;gBAC1B,OAAOD,GAAG,CAAA;aACX;SACF;QACD,SAASE,YAAY,CAACC,SAAS,EAAEC,WAAW,EAAE;YAC5C,IAAIC,OAAO,GAAG,CAAC;YACf3B,aAAa,EAAE,CAAC/N,OAAO,CAAC,SAAU2P,MAAM,EAAE5Y,CAAC,EAAE;gBAC3C,IAAIT,GAAG,GAAGmZ,WAAW,GAAGC,OAAO;gBAC/BpX,MAAM,CAAC,AAACkX,SAAS,GAAGzY,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC,GAAGT,GAAG;gBACtCqC,kBAAkB,CAACgX,MAAM,EAAErZ,GAAG,CAAC;gBAC/BoZ,OAAO,IAAIC,MAAM,CAACrb,MAAM,GAAG,CAAC;aAC7B,CAAC;YACF,OAAO,CAAC,CAAA;SACT;QACD,SAASsb,kBAAkB,CAACC,cAAc,EAAEC,iBAAiB,EAAE;YAC7D,IAAI9B,OAAO,GAAGD,aAAa,EAAE;YAC7BzV,MAAM,CAACuX,cAAc,IAAI,CAAC,CAAC,GAAG7B,OAAO,CAAC1Z,MAAM;YAC5C,IAAIob,OAAO,GAAG,CAAC;YACf1B,OAAO,CAAChO,OAAO,CAAC,SAAU2P,MAAM,EAAE;gBAChCD,OAAO,IAAIC,MAAM,CAACrb,MAAM,GAAG,CAAC;aAC7B,CAAC;YACFgE,MAAM,CAACwX,iBAAiB,IAAI,CAAC,CAAC,GAAGJ,OAAO;YACxC,OAAO,CAAC,CAAA;SACT;QACD,SAASK,KAAK,CAAChd,MAAM,EAAE;YACrBid,IAAI,CAACjd,MAAM,CAAC;SACb;QACD,SAASkd,SAAS,CAACC,EAAE,EAAE;YACrB,OAAO,CAAC,CAAA;SACT;QACD,SAASC,QAAQ,CAACD,EAAE,EAAEE,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAE,EAAE;QACpE,SAASC,SAAS,CAACN,EAAE,EAAEO,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAE;YACxC,IAAI9D,GAAG,GAAG,CAAC;YACX,IAAK,IAAI9V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2Z,MAAM,EAAE3Z,CAAC,EAAE,CAAE;gBAC/B,IAAIT,GAAG,GAAGgC,MAAM,CAAC,AAACmY,GAAG,GAAG1Z,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;gBACpC,IAAIO,GAAG,GAAGgB,MAAM,CAAC,AAACmY,GAAG,GAAG,CAAC1Z,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC;gBAC1C,IAAK,IAAI6Z,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtZ,GAAG,EAAEsZ,CAAC,EAAE,CAAE;oBAC5BhC,QAAQ,CAACG,SAAS,CAACmB,EAAE,EAAEzZ,MAAM,CAACH,GAAG,GAAGsa,CAAC,CAAC,CAAC;iBACxC;gBACD/D,GAAG,IAAIvV,GAAG;aACX;YACDgB,MAAM,CAACqY,IAAI,IAAI,CAAC,CAAC,GAAG9D,GAAG;YACvB,OAAO,CAAC,CAAA;SACT;QACD,SAASgE,YAAY,CAACC,GAAG,EAAE;YACzB9b,WAAW,CAAC8b,GAAG,CAAC;SACjB;QACDpR,aAAa,GAAGvN,OAAM,CAAC,eAAe,CAAC,GAAG4M,WAAW,CACnDjD,KAAK,EACL,eAAe,CAChB;QACD+G,qBAAqB,EAAE;QACvBK,YAAY,GAAG/Q,OAAM,CAAC,cAAc,CAAC,GAAG4M,WAAW,CAACjD,KAAK,EAAE,cAAc,CAAC;QAC1EsI,UAAU,EAAE;QACZyD,gBAAgB,GAAG1V,OAAM,CAAC,kBAAkB,CAAC,GAAG4M,WAAW,CACzDjD,KAAK,EACL,kBAAkB,CACnB;QACD,IAAIQ,aAAa,GAAG;YAClByU,CAAC,EAAErT,oBAAoB;YACvBsT,CAAC,EAAErQ,8BAA8B;YACjCsQ,CAAC,EAAE1O,wBAAwB;YAC3B2O,CAAC,EAAE1N,sBAAsB;YACzBzK,CAAC,EAAEuL,uBAAuB;YAC1BvN,CAAC,EAAE4N,uBAAuB;YAC1BhG,CAAC,EAAE4J,0BAA0B;YAC7BtF,CAAC,EAAEmG,yBAAyB;YAC5B+H,CAAC,EAAE5H,6BAA6B;YAChCqH,CAAC,EAAE/G,4BAA4B;YAC/BrO,CAAC,EAAEiP,6BAA6B;YAChC2G,CAAC,EAAEnG,8BAA8B;YACjC5O,CAAC,EAAE+O,oCAAoC;YACvCiG,CAAC,EAAE9F,sBAAsB;YACzB+F,CAAC,EAAExN,cAAc;YACjB9M,CAAC,EAAE+U,kBAAkB;YACrBwF,CAAC,EAAEvF,cAAc;YACjBwF,CAAC,EAAEjF,WAAW;YACdkF,CAAC,EAAEhF,MAAM;YACTzH,CAAC,EAAE0H,sBAAsB;YACzBgF,CAAC,EAAExE,uBAAuB;YAC1ByE,CAAC,EAAEpC,YAAY;YACf9K,CAAC,EAAEmL,kBAAkB;YACrBgC,CAAC,EAAE7B,KAAK;YACR8B,CAAC,EAAE5B,SAAS;YACZlO,CAAC,EAAEoO,QAAQ;YACX3L,CAAC,EAAEgM,SAAS;YACZsB,CAAC,EAAEjB,YAAY;SAChB;QACD,IAAIkB,GAAG,GAAG5V,UAAU,EAAE;QACtB,IAAI6V,kBAAkB,GAAI7f,OAAM,CAAC,oBAAoB,CAAC,GAAG,WAAY;YACnE,OAAO,CAAC6f,kBAAkB,GAAG7f,OAAM,CAAC,oBAAoB,CAAC,GACvDA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC8S,KAAK,CAAC,IAAI,EAAEwB,SAAS,CAAC,CAAA;SAC7C,AAAC;QACF,IAAI8D,OAAO,GAAIpY,OAAM,CAAC,SAAS,CAAC,GAAG,WAAY;YAC7C,OAAO,CAACoY,OAAO,GAAGpY,OAAM,CAAC,SAAS,CAAC,GAAGA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC8S,KAAK,CAC7D,IAAI,EACJwB,SAAS,CACV,CAAA;SACF,AAAC;QACF,IAAIuB,KAAK,GAAI7V,OAAM,CAAC,OAAO,CAAC,GAAG,WAAY;YACzC,OAAO,CAAC6V,KAAK,GAAG7V,OAAM,CAAC,OAAO,CAAC,GAAGA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC8S,KAAK,CACzD,IAAI,EACJwB,SAAS,CACV,CAAA;SACF,AAAC;QACF,IAAIsB,cAAc,GAAI5V,OAAM,CAAC,gBAAgB,CAAC,GAAG,WAAY;YAC3D,OAAO,CAAC4V,cAAc,GAAG5V,OAAM,CAAC,gBAAgB,CAAC,GAC/CA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC8S,KAAK,CAAC,IAAI,EAAEwB,SAAS,CAAC,CAAA;SAC7C,AAAC;QACF,IAAIwL,2CAA2C,GAAI9f,OAAM,CACvD,6CAA6C,CAC9C,GAAG,WAAY;YACd,OAAO,CAAC8f,2CAA2C,GAAG9f,OAAM,CAC1D,6CAA6C,CAC9C,GACCA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC8S,KAAK,CAAC,IAAI,EAAEwB,SAAS,CAAC,CAAA;SAC7C,AAAC;QACF,IAAIyL,YAAY,GAAI/f,OAAM,CAAC,cAAc,CAAC,GAAG,WAAY;YACvD,OAAO,CAAC+f,YAAY,GAAG/f,OAAM,CAAC,cAAc,CAAC,GAAGA,OAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC8S,KAAK,CACvE,IAAI,EACJwB,SAAS,CACV,CAAA;SACF,AAAC;QACF,IAAI0L,SAAS;QACb,SAASC,UAAU,CAACrf,MAAM,EAAE;YAC1B,IAAI,CAAC2L,IAAI,GAAG,YAAY;YACxB,IAAI,CAACS,OAAO,GAAG,+BAA+B,GAAGpM,MAAM,GAAG,GAAG;YAC7D,IAAI,CAACA,MAAM,GAAGA,MAAM;SACrB;QACDkI,qBAAqB,GAAG,SAASoX,SAAS,GAAG;YAC3C,IAAI,CAACF,SAAS,EAAEG,GAAG,EAAE;YACrB,IAAI,CAACH,SAAS,EAAElX,qBAAqB,GAAGoX,SAAS;SAClD;QACD,SAASC,GAAG,CAACpL,IAAI,EAAE;YACjBA,IAAI,GAAGA,IAAI,IAAItU,UAAU;YACzB,IAAImI,eAAe,GAAG,CAAC,EAAE;gBACvB,OAAM;aACP;YACDX,MAAM,EAAE;YACR,IAAIW,eAAe,GAAG,CAAC,EAAE;gBACvB,OAAM;aACP;YACD,SAASwX,KAAK,GAAG;gBACf,IAAIJ,SAAS,EAAE,OAAM;gBACrBA,SAAS,GAAG,IAAI;gBAChBhgB,OAAM,CAAC,WAAW,CAAC,GAAG,IAAI;gBAC1B,IAAIoD,KAAK,EAAE,OAAM;gBACjBiF,WAAW,EAAE;gBACbpI,mBAAmB,CAACD,OAAM,CAAC;gBAC3B,IAAIA,OAAM,CAAC,sBAAsB,CAAC,EAAEA,OAAM,CAAC,sBAAsB,CAAC,EAAE;gBACpEuI,OAAO,EAAE;aACV;YACD,IAAIvI,OAAM,CAAC,WAAW,CAAC,EAAE;gBACvBA,OAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC;gBACjCqgB,UAAU,CAAC,WAAY;oBACrBA,UAAU,CAAC,WAAY;wBACrBrgB,OAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;qBACxB,EAAE,CAAC,CAAC;oBACLogB,KAAK,EAAE;iBACR,EAAE,CAAC,CAAC;aACN,MAAM;gBACLA,KAAK,EAAE;aACR;SACF;QACDpgB,OAAM,CAAC,KAAK,CAAC,GAAGmgB,GAAG;QACnB,SAAStC,IAAI,CAACjd,MAAM,EAAE0f,QAAQ,EAAE;YAC9Bjd,UAAU,GAAGzC,MAAM;YACnB,IAAI0f,QAAQ,IAAIjV,gBAAgB,EAAE,IAAIzK,MAAM,KAAK,CAAC,EAAE;gBAClD,OAAM;aACP;YACD,IAAIyK,gBAAgB,EAAE,EAAE,EACvB,MAAM;gBACL/C,WAAW,EAAE;gBACb,IAAItI,OAAM,CAAC,QAAQ,CAAC,EAAEA,OAAM,CAAC,QAAQ,CAAC,CAACY,MAAM,CAAC;gBAC9CwC,KAAK,GAAG,IAAI;aACb;YACDzC,KAAK,CAACC,MAAM,EAAE,IAAIqf,UAAU,CAACrf,MAAM,CAAC,CAAC;SACtC;QACD,IAAIZ,OAAM,CAAC,SAAS,CAAC,EAAE;YACrB,IAAI,OAAOA,OAAM,CAAC,SAAS,CAAC,IAAI,UAAU,EACxCA,OAAM,CAAC,SAAS,CAAC,GAAG;gBAACA,OAAM,CAAC,SAAS,CAAC;aAAC;YACzC,MAAOA,OAAM,CAAC,SAAS,CAAC,CAACmC,MAAM,GAAG,CAAC,CAAE;gBACnCnC,OAAM,CAAC,SAAS,CAAC,CAAC6L,GAAG,EAAE,EAAE;aAC1B;SACF;QACDsU,GAAG,EAAE;QAEL,OAAOngB,OAAM,CAACugB,KAAK,CAAA;KACpB,CAAA;CACF,EAAG;eACWvgB,MAAM"}