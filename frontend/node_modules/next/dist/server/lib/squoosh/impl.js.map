{"version": 3, "sources": ["../../../../server/lib/squoosh/impl.ts"], "names": ["decodeBuffer", "rotate", "resize", "encodeJpeg", "encodeWebp", "encodeAvif", "encodePng", "FIXED_VERSION", "DELAY_MS", "_promise", "delayOnce", "ms", "Promise", "resolve", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "isAppleM1", "process", "arch", "platform", "semver", "lt", "version", "_buffer", "Object", "buffer", "<PERSON><PERSON><PERSON>", "from", "firstChunk", "slice", "firstChunkString", "Array", "map", "v", "String", "fromCodePoint", "join", "key", "entries", "supportedFormats", "find", "detectors", "some", "detector", "exec", "Error", "encoder", "mod", "dec", "rgba", "decode", "Uint8Array", "image", "numRotations", "ImageData", "m", "preprocessors", "instantiate", "data", "width", "height", "p", "defaultOptions", "quality", "e", "enc", "r", "encode", "defaultEncoderOptions", "val", "autoOptimize", "min", "cqLevel", "Math", "round"], "mappings": "AAAA;;;;QA+BsBA,YAAY,GAAZA,YAAY;QAoBZC,MAAM,GAANA,MAAM;QAgBNC,MAAM,GAANA,MAAM;QAaNC,UAAU,GAAVA,UAAU;QAgBVC,UAAU,GAAVA,UAAU;QAgBVC,UAAU,GAAVA,UAAU;QAmBVC,SAAS,GAATA,SAAS;AAnIZ,IAAA,OAA2B,kCAA3B,2BAA2B,EAAA;AACY,IAAA,OAAU,WAAV,UAAU,CAAA;AAC9C,IAAA,UAAc,kCAAd,cAAc,EAAA;;;;;;AAIpC,qCAAqC;AACrC,gDAAgD;AAChD,yDAAyD;AACzD,2HAA2H;AAC3H,MAAMC,aAAa,GAAG,QAAQ;AAC9B,MAAMC,QAAQ,GAAG,IAAI;AACrB,IAAIC,QAAQ,AAA2B;AAEvC,SAASC,SAAS,CAACC,EAAU,EAAiB;IAC5C,IAAI,CAACF,QAAQ,EAAE;QACbA,QAAQ,GAAG,IAAIG,OAAO,CAAC,CAACC,OAAO,GAAK;YAClCC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC;SACxB,CAAC;KACH;IACD,OAAOF,QAAQ,CAAA;CAChB;AAED,SAASM,UAAU,GAAkB;IACnC,MAAMC,SAAS,GAAGC,OAAO,CAACC,IAAI,KAAK,OAAO,IAAID,OAAO,CAACE,QAAQ,KAAK,QAAQ;IAC3E,IAAIH,SAAS,IAAII,OAAM,QAAA,CAACC,EAAE,CAACJ,OAAO,CAACK,OAAO,EAAEf,aAAa,CAAC,EAAE;QAC1D,OAAOG,SAAS,CAACF,QAAQ,CAAC,CAAA;KAC3B;IACD,OAAOI,OAAO,CAACC,OAAO,EAAE,CAAA;CACzB;AAEM,eAAeb,YAAY,CAChCuB,OAA4B,EACR;QAMRC,GAEX;IAPD,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC;IACnC,MAAMK,UAAU,GAAGH,MAAM,CAACI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACtC,MAAMC,gBAAgB,GAAGC,KAAK,CAACJ,IAAI,CAACC,UAAU,CAAC,CAC5CI,GAAG,CAAC,CAACC,CAAC,GAAKC,MAAM,CAACC,aAAa,CAACF,CAAC,CAAC,CAAC,CACnCG,IAAI,CAAC,EAAE,CAAC;IACX,MAAMC,GAAG,GAAGb,CAAAA,GAEX,GAFWA,MAAM,CAACc,OAAO,CAACC,OAAgB,OAAA,CAAC,CAACC,IAAI,CAAC,CAAC,GAAG,EAAEC,SAAS,CAAA,EAAE,CAAC,GAClEA,SAAS,CAACC,IAAI,CAAC,CAACC,QAAQ,GAAKA,QAAQ,CAACC,IAAI,CAACd,gBAAgB,CAAC,CAAC,CAC9D,SAAK,GAFMN,KAAAA,CAEN,GAFMA,GAEX,AAAE,CAAC,CAAC,CAAC,AAA0B;IAChC,IAAI,CAACa,GAAG,EAAE;QACR,MAAMQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAA;KAChD;IACD,MAAMC,OAAO,GAAGP,OAAgB,OAAA,CAACF,GAAG,CAAC;IACrC,MAAMU,GAAG,GAAG,MAAMD,OAAO,CAACE,GAAG,EAAE;IAC/B,MAAMC,IAAI,GAAGF,GAAG,CAACG,MAAM,CAAC,IAAIC,UAAU,CAAC1B,MAAM,CAAC,CAAC;IAC/C,OAAOwB,IAAI,CAAA;CACZ;AAEM,eAAehD,MAAM,CAC1BmD,KAAgB,EAChBC,YAAoB,EACA;IACpBD,KAAK,GAAGE,UAAS,QAAA,CAAC3B,IAAI,CAACyB,KAAK,CAAC;IAE7B,MAAMG,CAAC,GAAG,MAAMC,OAAa,cAAA,CAAC,QAAQ,CAAC,CAACC,WAAW,EAAE;IACrD,OAAO,MAAMF,CAAC,CAACH,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,KAAK,EAAEP,KAAK,CAACQ,MAAM,EAAE;QAAEP,YAAY;KAAE,CAAC,CAAA;CACxE;AAQM,eAAenD,MAAM,CAAC,EAAEkD,KAAK,CAAA,EAAEO,KAAK,CAAA,EAAEC,MAAM,CAAA,EAAc,EAAE;IACjER,KAAK,GAAGE,UAAS,QAAA,CAAC3B,IAAI,CAACyB,KAAK,CAAC;IAE7B,MAAMS,CAAC,GAAGL,OAAa,cAAA,CAAC,QAAQ,CAAC;IACjC,MAAMD,CAAC,GAAG,MAAMM,CAAC,CAACJ,WAAW,EAAE;IAC/B,MAAM1C,UAAU,EAAE;IAClB,OAAO,MAAMwC,CAAC,CAACH,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,KAAK,EAAEP,KAAK,CAACQ,MAAM,EAAE;QACpD,GAAGC,CAAC,CAACC,cAAc;QACnBH,KAAK;QACLC,MAAM;KACP,CAAC,CAAA;CACH;AAEM,eAAezD,UAAU,CAC9BiD,KAAgB,EAChB,EAAEW,OAAO,CAAA,EAAuB,EACF;IAC9BX,KAAK,GAAGE,UAAS,QAAA,CAAC3B,IAAI,CAACyB,KAAK,CAAC;IAE7B,MAAMY,CAAC,GAAGzB,OAAgB,OAAA,CAAC,SAAS,CAAC;IACrC,MAAMgB,CAAC,GAAG,MAAMS,CAAC,CAACC,GAAG,EAAE;IACvB,MAAMlD,UAAU,EAAE;IAClB,MAAMmD,CAAC,GAAG,MAAMX,CAAC,CAACY,MAAM,CAACf,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,KAAK,EAAEP,KAAK,CAACQ,MAAM,EAAE;QAC9D,GAAGI,CAAC,CAACI,qBAAqB;QAC1BL,OAAO;KACR,CAAC;IACF,OAAOrC,MAAM,CAACC,IAAI,CAACuC,CAAC,CAAC,CAAA;CACtB;AAEM,eAAe9D,UAAU,CAC9BgD,KAAgB,EAChB,EAAEW,OAAO,CAAA,EAAuB,EACF;IAC9BX,KAAK,GAAGE,UAAS,QAAA,CAAC3B,IAAI,CAACyB,KAAK,CAAC;IAE7B,MAAMY,CAAC,GAAGzB,OAAgB,OAAA,CAAC,MAAM,CAAC;IAClC,MAAMgB,CAAC,GAAG,MAAMS,CAAC,CAACC,GAAG,EAAE;IACvB,MAAMlD,UAAU,EAAE;IAClB,MAAMmD,CAAC,GAAG,MAAMX,CAAC,CAACY,MAAM,CAACf,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,KAAK,EAAEP,KAAK,CAACQ,MAAM,EAAE;QAC9D,GAAGI,CAAC,CAACI,qBAAqB;QAC1BL,OAAO;KACR,CAAC;IACF,OAAOrC,MAAM,CAACC,IAAI,CAACuC,CAAC,CAAC,CAAA;CACtB;AAEM,eAAe7D,UAAU,CAC9B+C,KAAgB,EAChB,EAAEW,OAAO,CAAA,EAAuB,EACF;IAC9BX,KAAK,GAAGE,UAAS,QAAA,CAAC3B,IAAI,CAACyB,KAAK,CAAC;IAE7B,MAAMY,CAAC,GAAGzB,OAAgB,OAAA,CAAC,MAAM,CAAC;IAClC,MAAMgB,CAAC,GAAG,MAAMS,CAAC,CAACC,GAAG,EAAE;IACvB,MAAMlD,UAAU,EAAE;IAClB,MAAMsD,GAAG,GAAGL,CAAC,CAACM,YAAY,CAACC,GAAG,IAAI,EAAE;IACpC,MAAML,CAAC,GAAG,MAAMX,CAAC,CAACY,MAAM,CAACf,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,KAAK,EAAEP,KAAK,CAACQ,MAAM,EAAE;QAC9D,GAAGI,CAAC,CAACI,qBAAqB;QAC1B,8DAA8D;QAC9D,qDAAqD;QACrDI,OAAO,EAAEC,IAAI,CAACC,KAAK,CAACL,GAAG,GAAG,AAACN,OAAO,GAAG,GAAG,GAAIM,GAAG,CAAC;KACjD,CAAC;IACF,OAAO3C,MAAM,CAACC,IAAI,CAACuC,CAAC,CAAC,CAAA;CACtB;AAEM,eAAe5D,SAAS,CAC7B8C,KAAgB,EACc;IAC9BA,KAAK,GAAGE,UAAS,QAAA,CAAC3B,IAAI,CAACyB,KAAK,CAAC;IAE7B,MAAMY,CAAC,GAAGzB,OAAgB,OAAA,CAAC,QAAQ,CAAC;IACpC,MAAMgB,CAAC,GAAG,MAAMS,CAAC,CAACC,GAAG,EAAE;IACvB,MAAMlD,UAAU,EAAE;IAClB,MAAMmD,CAAC,GAAG,MAAMX,CAAC,CAACY,MAAM,CAACf,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,KAAK,EAAEP,KAAK,CAACQ,MAAM,EAAE;QAC9D,GAAGI,CAAC,CAACI,qBAAqB;KAC3B,CAAC;IACF,OAAO1C,MAAM,CAACC,IAAI,CAACuC,CAAC,CAAC,CAAA;CACtB"}