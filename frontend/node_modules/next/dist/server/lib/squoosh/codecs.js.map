{"version": 3, "sources": ["../../../../server/lib/squoosh/codecs.ts"], "names": ["path", "pngEncDec", "oxipng", "resize", "mozEncWasm", "resolve", "__dirname", "mozDecWasm", "webpEncWasm", "webpDecWasm", "avifEncWasm", "avifDecWasm", "pngEncDecWasm", "pngEncDecInit", "default", "fsp", "readFile", "pathify", "oxipngWasm", "oxipngInit", "resizeWasm", "resizeInit", "rotateWasm", "global", "ImageData", "resizeNameToIndex", "name", "Error", "resizeWithAspect", "input_width", "input_height", "target_width", "target_height", "width", "height", "Math", "round", "preprocessors", "description", "instantiate", "buffer", "method", "premultiply", "linearRGB", "imageData", "cleanup", "defaultOptions", "fit<PERSON><PERSON><PERSON>", "rotate", "numRotations", "degrees", "sameDimensions", "size", "instance", "WebAssembly", "memory", "exports", "additionalPagesNeeded", "ceil", "byteLength", "grow", "view", "Uint8ClampedArray", "set", "slice", "codecs", "mozjpeg", "extension", "detectors", "dec", "instantiateEmscriptenWasm", "mozDec", "enc", "mozEnc", "defaultEncoderOptions", "quality", "baseline", "arithmetic", "progressive", "optimize_coding", "smoothing", "color_space", "quant_table", "trellis_multipass", "trellis_opt_zero", "trellis_opt_table", "trellis_loops", "auto_subsample", "chroma_subsample", "separate_chroma_quality", "chroma_quality", "autoOptimize", "option", "min", "max", "webp", "webpDec", "webpEnc", "target_size", "target_PSNR", "sns_strength", "filter_strength", "filter_sharpness", "filter_type", "partitions", "segments", "pass", "show_compressed", "preprocessing", "autofilter", "partition_limit", "alpha_compression", "alpha_filtering", "alpha_quality", "lossless", "exact", "image_hint", "emulate_jpeg_size", "thread_level", "low_memory", "near_lossless", "use_delta_palette", "use_sharp_yuv", "avif", "avifDec", "avifEnc", "cqLevel", "cqAlphaLevel", "denoiseLevel", "tileColsLog2", "tileRowsLog2", "speed", "subsample", "chromaDeltaQ", "sharpness", "tune", "decode", "encode", "opts", "simplePng", "Uint8Array", "optimise", "level"], "mappings": "AAAA;;;;;AAAgC,IAAA,GAAI,WAAJ,IAAI,CAAA;AACxBA,IAAAA,IAAI,mCAAM,MAAM,EAAZ;AACmC,IAAA,kBAAuB,WAAvB,uBAAuB,CAAA;AAqCvD,IAAA,iBAA+B,kCAA/B,+BAA+B,EAAA;AAG/B,IAAA,iBAA+B,kCAA/B,+BAA+B,EAAA;AAM9B,IAAA,cAAyB,kCAAzB,yBAAyB,EAAA;AAGzB,IAAA,cAAyB,kCAAzB,yBAAyB,EAAA;AAMzB,IAAA,cAAyB,kCAAzB,yBAAyB,EAAA;AAGzB,IAAA,cAAyB,kCAAzB,yBAAyB,EAAA;AAKjCC,IAAAA,SAAS,mCAAM,sBAAsB,EAA5B;AAOTC,IAAAA,MAAM,mCAAM,yBAAyB,EAA/B;AAMNC,IAAAA,MAAM,mCAAM,4BAA4B,EAAlC;AAQI,IAAA,UAAc,kCAAd,cAAc,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9CpC,MAAMC,UAAU,GAAGJ,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,iCAAiC,CAAC;AAG7E,MAAMC,UAAU,GAAGP,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,iCAAiC,CAAC;AAM7E,MAAME,WAAW,GAAGR,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,2BAA2B,CAAC;AAGxE,MAAMG,WAAW,GAAGT,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,2BAA2B,CAAC;AAMxE,MAAMI,WAAW,GAAGV,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,2BAA2B,CAAC;AAGxE,MAAMK,WAAW,GAAGX,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,2BAA2B,CAAC;AAKxE,MAAMM,aAAa,GAAGZ,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,2BAA2B,CAAC;AAC1E,MAAMO,aAAa,GAAG,IACpBZ,SAAS,CAACa,OAAO,CAACC,GAAG,SAAA,CAACC,QAAQ,CAACC,CAAAA,GAAAA,kBAAO,AAAe,CAAA,QAAf,CAACL,aAAa,CAAC,CAAC,CAAC;AAKzD,MAAMM,UAAU,GAAGlB,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,8BAA8B,CAAC;AAC1E,MAAMa,UAAU,GAAG,IAAMjB,MAAM,CAACY,OAAO,CAACC,GAAG,SAAA,CAACC,QAAQ,CAACC,CAAAA,GAAAA,kBAAO,AAAY,CAAA,QAAZ,CAACC,UAAU,CAAC,CAAC,CAAC;AAK1E,MAAME,UAAU,GAAGpB,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,iCAAiC,CAAC;AAC7E,MAAMe,UAAU,GAAG,IAAMlB,MAAM,CAACW,OAAO,CAACC,GAAG,SAAA,CAACC,QAAQ,CAACC,CAAAA,GAAAA,kBAAO,AAAY,CAAA,QAAZ,CAACG,UAAU,CAAC,CAAC,CAAC;AAE1E,SAAS;AACT,MAAME,UAAU,GAAGtB,IAAI,CAACK,OAAO,CAACC,SAAS,EAAE,sBAAsB,CAAC;AAIjE,AAACiB,MAAM,CAASC,SAAS,GAAGA,UAAS,QAAA;AAEtC,SAASC,iBAAiB,CACxBC,IAAqD,EACrD;IACA,OAAQA,IAAI;QACV,KAAK,UAAU;YACb,OAAO,CAAC,CAAA;QACV,KAAK,QAAQ;YACX,OAAO,CAAC,CAAA;QACV,KAAK,UAAU;YACb,OAAO,CAAC,CAAA;QACV,KAAK,UAAU;YACb,OAAO,CAAC,CAAA;QACV;YACE,MAAMC,KAAK,CAAC,CAAC,0BAA0B,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;KACpD;CACF;AAED,SAASE,gBAAgB,CAAC,EACxBC,WAAW,CAAA,EACXC,YAAY,CAAA,EACZC,YAAY,CAAA,EACZC,aAAa,CAAA,EACU,EAAqC;IAC5D,IAAI,CAACD,YAAY,IAAI,CAACC,aAAa,EAAE;QACnC,MAAML,KAAK,CAAC,wDAAwD,CAAC,CAAA;KACtE;IAED,IAAII,YAAY,IAAIC,aAAa,EAAE;QACjC,OAAO;YAAEC,KAAK,EAAEF,YAAY;YAAEG,MAAM,EAAEF,aAAa;SAAE,CAAA;KACtD;IAED,IAAI,CAACD,YAAY,EAAE;QACjB,OAAO;YACLE,KAAK,EAAEE,IAAI,CAACC,KAAK,CAAC,AAACP,WAAW,GAAGC,YAAY,GAAIE,aAAa,AAAC,CAAC;YAChEE,MAAM,EAAEF,aAAa;SACtB,CAAA;KACF;IAED,OAAO;QACLC,KAAK,EAAEF,YAAY;QACnBG,MAAM,EAAEC,IAAI,CAACC,KAAK,CAAC,AAACN,YAAY,GAAGD,WAAW,GAAIE,YAAY,CAAC;KAChE,CAAA;CACF;AAEM,MAAMM,aAAa,GAAG;IAC3BlC,MAAM,EAAE;QACNuB,IAAI,EAAE,QAAQ;QACdY,WAAW,EAAE,qCAAqC;QAClDC,WAAW,EAAE,UAAY;YACvB,MAAMlB,UAAU,EAAE;YAClB,OAAO,CACLmB,MAAkB,EAClBX,WAAmB,EACnBC,YAAoB,EACpB,EAAEG,KAAK,CAAA,EAAEC,MAAM,CAAA,EAAEO,MAAM,CAAA,EAAEC,WAAW,CAAA,EAAEC,SAAS,CAAA,EAAiB,GAC7D;gBACF,CAAC,EAAEV,KAAK,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAGN,gBAAgB,CAAC;oBACrCC,WAAW;oBACXC,YAAY;oBACZC,YAAY,EAAEE,KAAK;oBACnBD,aAAa,EAAEE,MAAM;iBACtB,CAAC,CAAC;gBACH,MAAMU,SAAS,GAAG,IAAIpB,UAAS,QAAA,CAC7BrB,MAAM,CAACA,MAAM,CACXqC,MAAM,EACNX,WAAW,EACXC,YAAY,EACZG,KAAK,EACLC,MAAM,EACNT,iBAAiB,CAACgB,MAAM,CAAC,EACzBC,WAAW,EACXC,SAAS,CACV,EACDV,KAAK,EACLC,MAAM,CACP;gBACD/B,MAAM,CAAC0C,OAAO,EAAE;gBAChB,OAAOD,SAAS,CAAA;aACjB,CAAA;SACF;QACDE,cAAc,EAAE;YACdL,MAAM,EAAE,UAAU;YAClBM,SAAS,EAAE,SAAS;YACpBL,WAAW,EAAE,IAAI;YACjBC,SAAS,EAAE,IAAI;SAChB;KACF;IACDK,MAAM,EAAE;QACNtB,IAAI,EAAE,QAAQ;QACdY,WAAW,EAAE,cAAc;QAC3BC,WAAW,EAAE,UAAY;YACvB,OAAO,OACLC,MAAkB,EAClBP,KAAa,EACbC,MAAc,EACd,EAAEe,YAAY,CAAA,EAAiB,GAC5B;gBACH,MAAMC,OAAO,GAAG,AAACD,YAAY,GAAG,EAAE,GAAI,GAAG;gBACzC,MAAME,cAAc,GAAGD,OAAO,KAAK,CAAC,IAAIA,OAAO,KAAK,GAAG;gBACvD,MAAME,IAAI,GAAGnB,KAAK,GAAGC,MAAM,GAAG,CAAC;gBAC/B,MAAMmB,QAAQ,GAAG,CACf,MAAMC,WAAW,CAACf,WAAW,CAAC,MAAMxB,GAAG,SAAA,CAACC,QAAQ,CAACC,CAAAA,GAAAA,kBAAO,AAAY,CAAA,QAAZ,CAACK,UAAU,CAAC,CAAC,CAAC,CACvE,CAAC+B,QAAQ,AAAwB;gBAClC,MAAM,EAAEE,MAAM,CAAA,EAAE,GAAGF,QAAQ,CAACG,OAAO;gBACnC,MAAMC,qBAAqB,GAAGtB,IAAI,CAACuB,IAAI,CACrC,CAACN,IAAI,GAAG,CAAC,GAAGG,MAAM,CAACf,MAAM,CAACmB,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CACxD;gBACD,IAAIF,qBAAqB,GAAG,CAAC,EAAE;oBAC7BF,MAAM,CAACK,IAAI,CAACH,qBAAqB,CAAC;iBACnC;gBACD,MAAMI,IAAI,GAAG,IAAIC,iBAAiB,CAACP,MAAM,CAACf,MAAM,CAAC;gBACjDqB,IAAI,CAACE,GAAG,CAACvB,MAAM,EAAE,CAAC,CAAC;gBACnBa,QAAQ,CAACG,OAAO,CAACR,MAAM,CAACf,KAAK,EAAEC,MAAM,EAAEgB,OAAO,CAAC;gBAC/C,OAAO,IAAI1B,UAAS,QAAA,CAClBqC,IAAI,CAACG,KAAK,CAACZ,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAClCD,cAAc,GAAGlB,KAAK,GAAGC,MAAM,EAC/BiB,cAAc,GAAGjB,MAAM,GAAGD,KAAK,CAChC,CAAA;aACF,CAAA;SACF;QACDa,cAAc,EAAE;YACdG,YAAY,EAAE,CAAC;SAChB;KACF;CACF,AAAS;QAhFGZ,aAAa,GAAbA,aAAa;AAkFnB,MAAM4B,MAAM,GAAG;IACpBC,OAAO,EAAE;QACPxC,IAAI,EAAE,SAAS;QACfyC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE;;SAAiB;QAC5BC,GAAG,EAAE,IACHC,CAAAA,GAAAA,kBAAyB,AAA2C,CAAA,0BAA3C,CAACC,iBAAM,QAAA,EAAyBhE,UAAU,CAAC;QACtEiE,GAAG,EAAE,IACHF,CAAAA,GAAAA,kBAAyB,AAGxB,CAAA,0BAHwB,CACvBG,iBAAM,QAAA,EACNrE,UAAU,CACX;QACHsE,qBAAqB,EAAE;YACrBC,OAAO,EAAE,EAAE;YACXC,QAAQ,EAAE,KAAK;YACfC,UAAU,EAAE,KAAK;YACjBC,WAAW,EAAE,IAAI;YACjBC,eAAe,EAAE,IAAI;YACrBC,SAAS,EAAE,CAAC;YACZC,WAAW,EAAE,CAAC,CAAC,SAAS,CAAV;YACdC,WAAW,EAAE,CAAC;YACdC,iBAAiB,EAAE,KAAK;YACxBC,gBAAgB,EAAE,KAAK;YACvBC,iBAAiB,EAAE,KAAK;YACxBC,aAAa,EAAE,CAAC;YAChBC,cAAc,EAAE,IAAI;YACpBC,gBAAgB,EAAE,CAAC;YACnBC,uBAAuB,EAAE,KAAK;YAC9BC,cAAc,EAAE,EAAE;SACnB;QACDC,YAAY,EAAE;YACZC,MAAM,EAAE,SAAS;YACjBC,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,GAAG;SACT;KACF;IACDC,IAAI,EAAE;QACJrE,IAAI,EAAE,MAAM;QACZyC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;;SAA0B;QACrCC,GAAG,EAAE,IACHC,CAAAA,GAAAA,kBAAyB,AAA6C,CAAA,0BAA7C,CAAC0B,cAAO,QAAA,EAAyBvF,WAAW,CAAC;QACxE+D,GAAG,EAAE,IACHF,CAAAA,GAAAA,kBAAyB,AAGxB,CAAA,0BAHwB,CACvB2B,cAAO,QAAA,EACPzF,WAAW,CACZ;QACHkE,qBAAqB,EAAE;YACrBC,OAAO,EAAE,EAAE;YACXuB,WAAW,EAAE,CAAC;YACdC,WAAW,EAAE,CAAC;YACd1D,MAAM,EAAE,CAAC;YACT2D,YAAY,EAAE,EAAE;YAChBC,eAAe,EAAE,EAAE;YACnBC,gBAAgB,EAAE,CAAC;YACnBC,WAAW,EAAE,CAAC;YACdC,UAAU,EAAE,CAAC;YACbC,QAAQ,EAAE,CAAC;YACXC,IAAI,EAAE,CAAC;YACPC,eAAe,EAAE,CAAC;YAClBC,aAAa,EAAE,CAAC;YAChBC,UAAU,EAAE,CAAC;YACbC,eAAe,EAAE,CAAC;YAClBC,iBAAiB,EAAE,CAAC;YACpBC,eAAe,EAAE,CAAC;YAClBC,aAAa,EAAE,GAAG;YAClBC,QAAQ,EAAE,CAAC;YACXC,KAAK,EAAE,CAAC;YACRC,UAAU,EAAE,CAAC;YACbC,iBAAiB,EAAE,CAAC;YACpBC,YAAY,EAAE,CAAC;YACfC,UAAU,EAAE,CAAC;YACbC,aAAa,EAAE,GAAG;YAClBC,iBAAiB,EAAE,CAAC;YACpBC,aAAa,EAAE,CAAC;SACjB;QACD/B,YAAY,EAAE;YACZC,MAAM,EAAE,SAAS;YACjBC,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,GAAG;SACT;KACF;IACD6B,IAAI,EAAE;QACJjG,IAAI,EAAE,MAAM;QACZyC,SAAS,EAAE,MAAM;QACjB,4CAA4C;QAC5CC,SAAS,EAAE;;SAA0C;QACrDC,GAAG,EAAE,IACHC,CAAAA,GAAAA,kBAAyB,AAA6C,CAAA,0BAA7C,CAACsD,cAAO,QAAA,EAAyBjH,WAAW,CAAC;QACxE6D,GAAG,EAAE,UAAY;YACf,OAAOF,CAAAA,GAAAA,kBAAyB,AAG/B,CAAA,0BAH+B,CAC9BuD,cAAO,QAAA,EACPnH,WAAW,CACZ,CAAA;SACF;QACDgE,qBAAqB,EAAE;YACrBoD,OAAO,EAAE,EAAE;YACXC,YAAY,EAAE,CAAC,CAAC;YAChBC,YAAY,EAAE,CAAC;YACfC,YAAY,EAAE,CAAC;YACfC,YAAY,EAAE,CAAC;YACfC,KAAK,EAAE,CAAC;YACRC,SAAS,EAAE,CAAC;YACZC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE,CAAC;YACZC,IAAI,EAAE,CAAC,CAAC,mBAAmB,CAApB;SACR;QACD5C,YAAY,EAAE;YACZC,MAAM,EAAE,SAAS;YACjBC,GAAG,EAAE,EAAE;YACPC,GAAG,EAAE,CAAC;SACP;KACF;IACD5F,MAAM,EAAE;QACNwB,IAAI,EAAE,QAAQ;QACdyC,SAAS,EAAE,KAAK;QAChB,4CAA4C;QAC5CC,SAAS,EAAE;;SAA4B;QACvCC,GAAG,EAAE,UAAY;YACf,MAAMxD,aAAa,EAAE;YACrB,OAAO;gBACL2H,MAAM,EAAE,CAAChG,MAA2B,GAAK;oBACvC,MAAMI,SAAS,GAAG3C,SAAS,CAACuI,MAAM,CAAChG,MAAM,CAAC;oBAC1CvC,SAAS,CAAC4C,OAAO,EAAE;oBACnB,OAAOD,SAAS,CAAA;iBACjB;aACF,CAAA;SACF;QACD4B,GAAG,EAAE,UAAY;YACf,MAAM3D,aAAa,EAAE;YACrB,MAAMM,UAAU,EAAE;YAClB,OAAO;gBACLsH,MAAM,EAAE,CACNjG,MAAuC,EACvCP,KAAa,EACbC,MAAc,EACdwG,IAAuB,GACpB;oBACH,MAAMC,SAAS,GAAG1I,SAAS,CAACwI,MAAM,CAChC,IAAIG,UAAU,CAACpG,MAAM,CAAC,EACtBP,KAAK,EACLC,MAAM,CACP;oBACD,MAAMU,SAAS,GAAG1C,MAAM,CAAC2I,QAAQ,CAACF,SAAS,EAAED,IAAI,CAACI,KAAK,EAAE,KAAK,CAAC;oBAC/D5I,MAAM,CAAC2C,OAAO,EAAE;oBAChB,OAAOD,SAAS,CAAA;iBACjB;aACF,CAAA;SACF;QACD8B,qBAAqB,EAAE;YACrBoE,KAAK,EAAE,CAAC;SACT;QACDnD,YAAY,EAAE;YACZC,MAAM,EAAE,OAAO;YACfC,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,CAAC;SACP;KACF;CACF,AAAS;QA9JG7B,MAAM,GAANA,MAAM"}