{"version": 3, "sources": ["../../../../../server/lib/squoosh/png/squoosh_png.js"], "names": ["encode", "decode", "cleanup", "wasm", "cachedTextDecoder", "TextDecoder", "ignoreBOM", "fatal", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "getStringFromWasm0", "ptr", "len", "subarray", "cachegetUint8ClampedMemory0", "getUint8ClampedMemory0", "Uint8ClampedArray", "getClampedArrayU8FromWasm0", "heap", "Array", "fill", "undefined", "push", "heap_next", "length", "addHeapObject", "obj", "idx", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "getArrayU8FromWasm0", "data", "width", "height", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "getObject", "dropObject", "takeObject", "ret", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "wbg", "__wbg_newwithownedu8clampedarrayandsh_787b2db8ea6bfd62", "arg0", "arg1", "arg2", "arg3", "v0", "ImageData", "__wbindgen_throw", "Error", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": "AAAA;;;;QAmFgBA,MAAM,GAANA,MAAM;QAmCNC,MAAM,GAANA,MAAM;QA2DNC,OAAO,GAAPA,OAAO;;AAjLvB,IAAIC,IAAI;AAER,IAAIC,iBAAiB,GAAG,IAAIC,WAAW,CAAC,OAAO,EAAE;IAC/CC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;CACZ,CAAC;AAEFH,iBAAiB,CAACH,MAAM,EAAE;AAE1B,IAAIO,oBAAoB,GAAG,IAAI;AAC/B,SAASC,eAAe,GAAG;IACzB,IACED,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACE,MAAM,KAAKP,IAAI,CAACQ,MAAM,CAACD,MAAM,EAClD;QACAF,oBAAoB,GAAG,IAAII,UAAU,CAACT,IAAI,CAACQ,MAAM,CAACD,MAAM,CAAC;KAC1D;IACD,OAAOF,oBAAoB,CAAA;CAC5B;AAED,SAASK,kBAAkB,CAACC,GAAG,EAAEC,GAAG,EAAE;IACpC,OAAOX,iBAAiB,CAACH,MAAM,CAACQ,eAAe,EAAE,CAACO,QAAQ,CAACF,GAAG,EAAEA,GAAG,GAAGC,GAAG,CAAC,CAAC,CAAA;CAC5E;AAED,IAAIE,2BAA2B,GAAG,IAAI;AACtC,SAASC,sBAAsB,GAAG;IAChC,IACED,2BAA2B,KAAK,IAAI,IACpCA,2BAA2B,CAACP,MAAM,KAAKP,IAAI,CAACQ,MAAM,CAACD,MAAM,EACzD;QACAO,2BAA2B,GAAG,IAAIE,iBAAiB,CAAChB,IAAI,CAACQ,MAAM,CAACD,MAAM,CAAC;KACxE;IACD,OAAOO,2BAA2B,CAAA;CACnC;AAED,SAASG,0BAA0B,CAACN,GAAG,EAAEC,GAAG,EAAE;IAC5C,OAAOG,sBAAsB,EAAE,CAACF,QAAQ,CAACF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,GAAGC,GAAG,CAAC,CAAA;CACjE;AAED,MAAMM,IAAI,GAAG,IAAIC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAACC,SAAS,CAAC;AAE1CH,IAAI,CAACI,IAAI,CAACD,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;AAEvC,IAAIE,SAAS,GAAGL,IAAI,CAACM,MAAM;AAE3B,SAASC,aAAa,CAACC,GAAG,EAAE;IAC1B,IAAIH,SAAS,KAAKL,IAAI,CAACM,MAAM,EAAEN,IAAI,CAACI,IAAI,CAACJ,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC;IACzD,MAAMG,GAAG,GAAGJ,SAAS;IACrBA,SAAS,GAAGL,IAAI,CAACS,GAAG,CAAC;IAErBT,IAAI,CAACS,GAAG,CAAC,GAAGD,GAAG;IACf,OAAOC,GAAG,CAAA;CACX;AAED,IAAIC,eAAe,GAAG,CAAC;AAEvB,SAASC,iBAAiB,CAACC,GAAG,EAAEC,MAAM,EAAE;IACtC,MAAMpB,GAAG,GAAGoB,MAAM,CAACD,GAAG,CAACN,MAAM,GAAG,CAAC,CAAC;IAClClB,eAAe,EAAE,CAAC0B,GAAG,CAACF,GAAG,EAAEnB,GAAG,GAAG,CAAC,CAAC;IACnCiB,eAAe,GAAGE,GAAG,CAACN,MAAM;IAC5B,OAAOb,GAAG,CAAA;CACX;AAED,IAAIsB,oBAAoB,GAAG,IAAI;AAC/B,SAASC,eAAe,GAAG;IACzB,IACED,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAAC1B,MAAM,KAAKP,IAAI,CAACQ,MAAM,CAACD,MAAM,EAClD;QACA0B,oBAAoB,GAAG,IAAIE,UAAU,CAACnC,IAAI,CAACQ,MAAM,CAACD,MAAM,CAAC;KAC1D;IACD,OAAO0B,oBAAoB,CAAA;CAC5B;AAED,SAASG,mBAAmB,CAACzB,GAAG,EAAEC,GAAG,EAAE;IACrC,OAAON,eAAe,EAAE,CAACO,QAAQ,CAACF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,GAAGC,GAAG,CAAC,CAAA;CAC1D;AAOM,SAASf,MAAM,CAACwC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC1C,IAAI;QACF,MAAMC,MAAM,GAAGxC,IAAI,CAACyC,+BAA+B,CAAC,CAAC,EAAE,CAAC;QACxD,IAAIC,IAAI,GAAGb,iBAAiB,CAACQ,IAAI,EAAErC,IAAI,CAAC2C,iBAAiB,CAAC;QAC1D,IAAIC,IAAI,GAAGhB,eAAe;QAC1B5B,IAAI,CAACH,MAAM,CAAC2C,MAAM,EAAEE,IAAI,EAAEE,IAAI,EAAEN,KAAK,EAAEC,MAAM,CAAC;QAC9C,IAAIM,EAAE,GAAGX,eAAe,EAAE,CAACM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAIM,EAAE,GAAGZ,eAAe,EAAE,CAACM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAIO,EAAE,GAAGX,mBAAmB,CAACS,EAAE,EAAEC,EAAE,CAAC,CAACE,KAAK,EAAE;QAC5ChD,IAAI,CAACiD,eAAe,CAACJ,EAAE,EAAEC,EAAE,GAAG,CAAC,CAAC;QAChC,OAAOC,EAAE,CAAA;KACV,QAAS;QACR/C,IAAI,CAACyC,+BAA+B,CAAC,EAAE,CAAC;KACzC;CACF;AAED,SAASS,SAAS,CAACvB,GAAG,EAAE;IACtB,OAAOT,IAAI,CAACS,GAAG,CAAC,CAAA;CACjB;AAED,SAASwB,UAAU,CAACxB,GAAG,EAAE;IACvB,IAAIA,GAAG,GAAG,EAAE,EAAE,OAAM;IACpBT,IAAI,CAACS,GAAG,CAAC,GAAGJ,SAAS;IACrBA,SAAS,GAAGI,GAAG;CAChB;AAED,SAASyB,UAAU,CAACzB,GAAG,EAAE;IACvB,MAAM0B,GAAG,GAAGH,SAAS,CAACvB,GAAG,CAAC;IAC1BwB,UAAU,CAACxB,GAAG,CAAC;IACf,OAAO0B,GAAG,CAAA;CACX;AAKM,SAASvD,MAAM,CAACuC,IAAI,EAAE;IAC3B,IAAIK,IAAI,GAAGb,iBAAiB,CAACQ,IAAI,EAAErC,IAAI,CAAC2C,iBAAiB,CAAC;IAC1D,IAAIC,IAAI,GAAGhB,eAAe;IAC1B,IAAIyB,GAAG,GAAGrD,IAAI,CAACF,MAAM,CAAC4C,IAAI,EAAEE,IAAI,CAAC;IACjC,OAAOQ,UAAU,CAACC,GAAG,CAAC,CAAA;CACvB;AAED,eAAeC,IAAI,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnC,IAAI,OAAOC,QAAQ,KAAK,UAAU,IAAIF,MAAM,YAAYE,QAAQ,EAAE;QAChE,IAAI,OAAOC,WAAW,CAACC,oBAAoB,KAAK,UAAU,EAAE;YAC1D,OAAO,MAAMD,WAAW,CAACC,oBAAoB,CAACJ,MAAM,EAAEC,OAAO,CAAC,CAAA;SAC/D;QAED,MAAMI,KAAK,GAAG,MAAML,MAAM,CAACM,WAAW,EAAE;QACxC,OAAO,MAAMH,WAAW,CAACI,WAAW,CAACF,KAAK,EAAEJ,OAAO,CAAC,CAAA;KACrD,MAAM;QACL,MAAMO,QAAQ,GAAG,MAAML,WAAW,CAACI,WAAW,CAACP,MAAM,EAAEC,OAAO,CAAC;QAE/D,IAAIO,QAAQ,YAAYL,WAAW,CAACM,QAAQ,EAAE;YAC5C,OAAO;gBAAED,QAAQ;gBAAER,MAAM;aAAE,CAAA;SAC5B,MAAM;YACL,OAAOQ,QAAQ,CAAA;SAChB;KACF;CACF;AAED,eAAeE,IAAI,CAACC,KAAK,EAAE;IACzB,MAAMV,OAAO,GAAG,EAAE;IAClBA,OAAO,CAACW,GAAG,GAAG,EAAE;IAChBX,OAAO,CAACW,GAAG,CAACC,sDAAsD,GAChE,SAAUC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;QAChC,IAAIC,EAAE,GAAGxD,0BAA0B,CAACoD,IAAI,EAAEC,IAAI,CAAC,CAACtB,KAAK,EAAE;QACvDhD,IAAI,CAACiD,eAAe,CAACoB,IAAI,EAAEC,IAAI,GAAG,CAAC,CAAC;QACpC,IAAIjB,GAAG,GAAG,IAAIqB,SAAS,CAACD,EAAE,EAAEF,IAAI,KAAK,CAAC,EAAEC,IAAI,KAAK,CAAC,CAAC;QACnD,OAAO/C,aAAa,CAAC4B,GAAG,CAAC,CAAA;KAC1B;IACHG,OAAO,CAACW,GAAG,CAACQ,gBAAgB,GAAG,SAAUN,IAAI,EAAEC,IAAI,EAAE;QACnD,MAAM,IAAIM,KAAK,CAAClE,kBAAkB,CAAC2D,IAAI,EAAEC,IAAI,CAAC,CAAC,CAAA;KAChD;IAED,IACE,OAAOJ,KAAK,KAAK,QAAQ,IACxB,OAAOW,OAAO,KAAK,UAAU,IAAIX,KAAK,YAAYW,OAAO,IACzD,OAAOC,GAAG,KAAK,UAAU,IAAIZ,KAAK,YAAYY,GAAG,AAAC,EACnD;QACAZ,KAAK,GAAGa,KAAK,CAACb,KAAK,CAAC;KACrB;IAED,MAAM,EAAEH,QAAQ,CAAA,EAAER,MAAM,CAAA,EAAE,GAAG,MAAMD,IAAI,CAAC,MAAMY,KAAK,EAAEV,OAAO,CAAC;IAE7DxD,IAAI,GAAG+D,QAAQ,CAACiB,OAAO;IACvBf,IAAI,CAACgB,sBAAsB,GAAG1B,MAAM;IAEpC,OAAOvD,IAAI,CAAA;CACZ;eAEciE,IAAI;;AAGZ,SAASlE,OAAO,GAAG;IACxBC,IAAI,GAAG,IAAI;IACXc,2BAA2B,GAAG,IAAI;IAClCT,oBAAoB,GAAG,IAAI;IAC3B4B,oBAAoB,GAAG,IAAI;CAC5B"}