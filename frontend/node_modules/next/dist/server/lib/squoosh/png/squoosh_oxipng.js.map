{"version": 3, "sources": ["../../../../../server/lib/squoosh/png/squoosh_oxipng.js"], "names": ["optimise", "cleanup", "wasm", "cachedTextDecoder", "TextDecoder", "ignoreBOM", "fatal", "decode", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "getStringFromWasm0", "ptr", "len", "subarray", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "length", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "getArrayU8FromWasm0", "data", "level", "interlace", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "wbg", "__wbindgen_throw", "arg0", "arg1", "Error", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": "AAAA;;;;QAqDgBA,QAAQ,GAARA,QAAQ;QA6DRC,OAAO,GAAPA,OAAO;;AAlHvB,IAAIC,IAAI;AAER,IAAIC,iBAAiB,GAAG,IAAIC,WAAW,CAAC,OAAO,EAAE;IAC/CC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;CACZ,CAAC;AAEFH,iBAAiB,CAACI,MAAM,EAAE;AAE1B,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,SAASC,eAAe,GAAG;IACzB,IACED,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACE,MAAM,KAAKR,IAAI,CAACS,MAAM,CAACD,MAAM,EAClD;QACAF,oBAAoB,GAAG,IAAII,UAAU,CAACV,IAAI,CAACS,MAAM,CAACD,MAAM,CAAC;KAC1D;IACD,OAAOF,oBAAoB,CAAA;CAC5B;AAED,SAASK,kBAAkB,CAACC,GAAG,EAAEC,GAAG,EAAE;IACpC,OAAOZ,iBAAiB,CAACI,MAAM,CAACE,eAAe,EAAE,CAACO,QAAQ,CAACF,GAAG,EAAEA,GAAG,GAAGC,GAAG,CAAC,CAAC,CAAA;CAC5E;AAED,IAAIE,eAAe,GAAG,CAAC;AAEvB,SAASC,iBAAiB,CAACC,GAAG,EAAEC,MAAM,EAAE;IACtC,MAAMN,GAAG,GAAGM,MAAM,CAACD,GAAG,CAACE,MAAM,GAAG,CAAC,CAAC;IAClCZ,eAAe,EAAE,CAACa,GAAG,CAACH,GAAG,EAAEL,GAAG,GAAG,CAAC,CAAC;IACnCG,eAAe,GAAGE,GAAG,CAACE,MAAM;IAC5B,OAAOP,GAAG,CAAA;CACX;AAED,IAAIS,oBAAoB,GAAG,IAAI;AAC/B,SAASC,eAAe,GAAG;IACzB,IACED,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACb,MAAM,KAAKR,IAAI,CAACS,MAAM,CAACD,MAAM,EAClD;QACAa,oBAAoB,GAAG,IAAIE,UAAU,CAACvB,IAAI,CAACS,MAAM,CAACD,MAAM,CAAC;KAC1D;IACD,OAAOa,oBAAoB,CAAA;CAC5B;AAED,SAASG,mBAAmB,CAACZ,GAAG,EAAEC,GAAG,EAAE;IACrC,OAAON,eAAe,EAAE,CAACO,QAAQ,CAACF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,GAAGC,GAAG,CAAC,CAAA;CAC1D;AAOM,SAASf,QAAQ,CAAC2B,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;IAC/C,IAAI;QACF,MAAMC,MAAM,GAAG5B,IAAI,CAAC6B,+BAA+B,CAAC,CAAC,EAAE,CAAC;QACxD,IAAIC,IAAI,GAAGd,iBAAiB,CAACS,IAAI,EAAEzB,IAAI,CAAC+B,iBAAiB,CAAC;QAC1D,IAAIC,IAAI,GAAGjB,eAAe;QAC1Bf,IAAI,CAACF,QAAQ,CAAC8B,MAAM,EAAEE,IAAI,EAAEE,IAAI,EAAEN,KAAK,EAAEC,SAAS,CAAC;QACnD,IAAIM,EAAE,GAAGX,eAAe,EAAE,CAACM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAIM,EAAE,GAAGZ,eAAe,EAAE,CAACM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAIO,EAAE,GAAGX,mBAAmB,CAACS,EAAE,EAAEC,EAAE,CAAC,CAACE,KAAK,EAAE;QAC5CpC,IAAI,CAACqC,eAAe,CAACJ,EAAE,EAAEC,EAAE,GAAG,CAAC,CAAC;QAChC,OAAOC,EAAE,CAAA;KACV,QAAS;QACRnC,IAAI,CAAC6B,+BAA+B,CAAC,EAAE,CAAC;KACzC;CACF;AAED,eAAeS,IAAI,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnC,IAAI,OAAOC,QAAQ,KAAK,UAAU,IAAIF,MAAM,YAAYE,QAAQ,EAAE;QAChE,IAAI,OAAOC,WAAW,CAACC,oBAAoB,KAAK,UAAU,EAAE;YAC1D,OAAO,MAAMD,WAAW,CAACC,oBAAoB,CAACJ,MAAM,EAAEC,OAAO,CAAC,CAAA;SAC/D;QAED,MAAMI,KAAK,GAAG,MAAML,MAAM,CAACM,WAAW,EAAE;QACxC,OAAO,MAAMH,WAAW,CAACI,WAAW,CAACF,KAAK,EAAEJ,OAAO,CAAC,CAAA;KACrD,MAAM;QACL,MAAMO,QAAQ,GAAG,MAAML,WAAW,CAACI,WAAW,CAACP,MAAM,EAAEC,OAAO,CAAC;QAE/D,IAAIO,QAAQ,YAAYL,WAAW,CAACM,QAAQ,EAAE;YAC5C,OAAO;gBAAED,QAAQ;gBAAER,MAAM;aAAE,CAAA;SAC5B,MAAM;YACL,OAAOQ,QAAQ,CAAA;SAChB;KACF;CACF;AAED,eAAeE,IAAI,CAACC,KAAK,EAAE;IACzB,MAAMV,OAAO,GAAG,EAAE;IAClBA,OAAO,CAACW,GAAG,GAAG,EAAE;IAChBX,OAAO,CAACW,GAAG,CAACC,gBAAgB,GAAG,SAAUC,IAAI,EAAEC,IAAI,EAAE;QACnD,MAAM,IAAIC,KAAK,CAAC5C,kBAAkB,CAAC0C,IAAI,EAAEC,IAAI,CAAC,CAAC,CAAA;KAChD;IAED,IACE,OAAOJ,KAAK,KAAK,QAAQ,IACxB,OAAOM,OAAO,KAAK,UAAU,IAAIN,KAAK,YAAYM,OAAO,IACzD,OAAOC,GAAG,KAAK,UAAU,IAAIP,KAAK,YAAYO,GAAG,AAAC,EACnD;QACAP,KAAK,GAAGQ,KAAK,CAACR,KAAK,CAAC;KACrB;IAED,MAAM,EAAEH,QAAQ,CAAA,EAAER,MAAM,CAAA,EAAE,GAAG,MAAMD,IAAI,CAAC,MAAMY,KAAK,EAAEV,OAAO,CAAC;IAE7DxC,IAAI,GAAG+C,QAAQ,CAACY,OAAO;IACvBV,IAAI,CAACW,sBAAsB,GAAGrB,MAAM;IAEpC,OAAOvC,IAAI,CAAA;CACZ;eAEciD,IAAI;;AAGZ,SAASlD,OAAO,GAAG;IACxBC,IAAI,GAAG,IAAI;IACXM,oBAAoB,GAAG,IAAI;IAC3Be,oBAAoB,GAAG,IAAI;CAC5B"}