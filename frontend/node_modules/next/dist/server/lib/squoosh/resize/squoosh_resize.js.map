{"version": 3, "sources": ["../../../../../server/lib/squoosh/resize/squoosh_resize.js"], "names": ["resize", "cleanup", "wasm", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "ptr", "length", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "cachegetUint8ClampedMemory0", "getUint8ClampedMemory0", "Uint8ClampedArray", "getClampedArrayU8FromWasm0", "len", "subarray", "input_image", "input_width", "input_height", "output_width", "output_height", "typ_idx", "premultiply", "color_space_conversion", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": "AAAA;;;;QA0DgBA,MAAM,GAANA,MAAM;QA6ENC,OAAO,GAAPA,OAAO;;AAvIvB,IAAIC,IAAI;AAER,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,SAASC,eAAe,GAAG;IACzB,IACED,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACE,MAAM,KAAKH,IAAI,CAACI,MAAM,CAACD,MAAM,EAClD;QACAF,oBAAoB,GAAG,IAAII,UAAU,CAACL,IAAI,CAACI,MAAM,CAACD,MAAM,CAAC;KAC1D;IACD,OAAOF,oBAAoB,CAAA;CAC5B;AAED,IAAIK,eAAe,GAAG,CAAC;AAEvB,SAASC,iBAAiB,CAACC,GAAG,EAAEC,MAAM,EAAE;IACtC,MAAMC,GAAG,GAAGD,MAAM,CAACD,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;IAClCT,eAAe,EAAE,CAACU,GAAG,CAACJ,GAAG,EAAEE,GAAG,GAAG,CAAC,CAAC;IACnCJ,eAAe,GAAGE,GAAG,CAACG,MAAM;IAC5B,OAAOD,GAAG,CAAA;CACX;AAED,IAAIG,oBAAoB,GAAG,IAAI;AAC/B,SAASC,eAAe,GAAG;IACzB,IACED,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACV,MAAM,KAAKH,IAAI,CAACI,MAAM,CAACD,MAAM,EAClD;QACAU,oBAAoB,GAAG,IAAIE,UAAU,CAACf,IAAI,CAACI,MAAM,CAACD,MAAM,CAAC;KAC1D;IACD,OAAOU,oBAAoB,CAAA;CAC5B;AAED,IAAIG,2BAA2B,GAAG,IAAI;AACtC,SAASC,sBAAsB,GAAG;IAChC,IACED,2BAA2B,KAAK,IAAI,IACpCA,2BAA2B,CAACb,MAAM,KAAKH,IAAI,CAACI,MAAM,CAACD,MAAM,EACzD;QACAa,2BAA2B,GAAG,IAAIE,iBAAiB,CAAClB,IAAI,CAACI,MAAM,CAACD,MAAM,CAAC;KACxE;IACD,OAAOa,2BAA2B,CAAA;CACnC;AAED,SAASG,0BAA0B,CAACT,GAAG,EAAEU,GAAG,EAAE;IAC5C,OAAOH,sBAAsB,EAAE,CAACI,QAAQ,CAACX,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,GAAGU,GAAG,CAAC,CAAA;CACjE;AAYM,SAAStB,MAAM,CACpBwB,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,OAAO,EACPC,WAAW,EACXC,sBAAsB,EACtB;IACA,IAAI;QACF,MAAMC,MAAM,GAAG9B,IAAI,CAAC+B,+BAA+B,CAAC,CAAC,EAAE,CAAC;QACxD,IAAIC,IAAI,GAAGzB,iBAAiB,CAACe,WAAW,EAAEtB,IAAI,CAACiC,iBAAiB,CAAC;QACjE,IAAIC,IAAI,GAAG5B,eAAe;QAC1BN,IAAI,CAACF,MAAM,CACTgC,MAAM,EACNE,IAAI,EACJE,IAAI,EACJX,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,OAAO,EACPC,WAAW,EACXC,sBAAsB,CACvB;QACD,IAAIM,EAAE,GAAGrB,eAAe,EAAE,CAACgB,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAIM,EAAE,GAAGtB,eAAe,EAAE,CAACgB,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAIO,EAAE,GAAGlB,0BAA0B,CAACgB,EAAE,EAAEC,EAAE,CAAC,CAACE,KAAK,EAAE;QACnDtC,IAAI,CAACuC,eAAe,CAACJ,EAAE,EAAEC,EAAE,GAAG,CAAC,CAAC;QAChC,OAAOC,EAAE,CAAA;KACV,QAAS;QACRrC,IAAI,CAAC+B,+BAA+B,CAAC,EAAE,CAAC;KACzC;CACF;AAED,eAAeS,IAAI,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnC,IAAI,OAAOC,QAAQ,KAAK,UAAU,IAAIF,MAAM,YAAYE,QAAQ,EAAE;QAChE,IAAI,OAAOC,WAAW,CAACC,oBAAoB,KAAK,UAAU,EAAE;YAC1D,OAAO,MAAMD,WAAW,CAACC,oBAAoB,CAACJ,MAAM,EAAEC,OAAO,CAAC,CAAA;SAC/D;QAED,MAAMI,KAAK,GAAG,MAAML,MAAM,CAACM,WAAW,EAAE;QACxC,OAAO,MAAMH,WAAW,CAACI,WAAW,CAACF,KAAK,EAAEJ,OAAO,CAAC,CAAA;KACrD,MAAM;QACL,MAAMO,QAAQ,GAAG,MAAML,WAAW,CAACI,WAAW,CAACP,MAAM,EAAEC,OAAO,CAAC;QAE/D,IAAIO,QAAQ,YAAYL,WAAW,CAACM,QAAQ,EAAE;YAC5C,OAAO;gBAAED,QAAQ;gBAAER,MAAM;aAAE,CAAA;SAC5B,MAAM;YACL,OAAOQ,QAAQ,CAAA;SAChB;KACF;CACF;AAED,eAAeE,IAAI,CAACC,KAAK,EAAE;IACzB,MAAMV,OAAO,GAAG,EAAE;IAElB,IACE,OAAOU,KAAK,KAAK,QAAQ,IACxB,OAAOC,OAAO,KAAK,UAAU,IAAID,KAAK,YAAYC,OAAO,IACzD,OAAOC,GAAG,KAAK,UAAU,IAAIF,KAAK,YAAYE,GAAG,AAAC,EACnD;QACAF,KAAK,GAAGG,KAAK,CAACH,KAAK,CAAC;KACrB;IAED,MAAM,EAAEH,QAAQ,CAAA,EAAER,MAAM,CAAA,EAAE,GAAG,MAAMD,IAAI,CAAC,MAAMY,KAAK,EAAEV,OAAO,CAAC;IAE7D1C,IAAI,GAAGiD,QAAQ,CAACO,OAAO;IACvBL,IAAI,CAACM,sBAAsB,GAAGhB,MAAM;IAEpC,OAAOzC,IAAI,CAAA;CACZ;eAEcmD,IAAI;;AAGZ,SAASpD,OAAO,GAAG;IACxBC,IAAI,GAAG,IAAI;IACXC,oBAAoB,GAAG,IAAI;IAC3BY,oBAAoB,GAAG,IAAI;CAC5B"}