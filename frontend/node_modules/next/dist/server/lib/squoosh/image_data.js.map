{"version": 3, "sources": ["../../../../server/lib/squoosh/image_data.ts"], "names": ["ImageData", "from", "input", "data", "_data", "width", "height", "Object", "prototype", "toString", "call", "<PERSON><PERSON><PERSON>", "values", "Uint8Array", "Uint8ClampedArray", "Error", "constructor"], "mappings": "AAAA;;;;;AAAe,MAAMA,SAAS;IAC5B,OAAOC,IAAI,CAACC,KAAgB,EAAa;QACvC,OAAO,IAAIF,SAAS,CAACE,KAAK,CAACC,IAAI,IAAID,KAAK,CAACE,KAAK,EAAEF,KAAK,CAACG,KAAK,EAAEH,KAAK,CAACI,MAAM,CAAC,CAAA;KAC3E;IAMD,IAAIH,IAAI,GAAW;QACjB,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACN,KAAK,CAAC,KAAK,iBAAiB,EAAE;YACpE,OAAOO,MAAM,CAACV,IAAI,CAACM,MAAM,CAACK,MAAM,CAAC,IAAI,CAACR,KAAK,CAAC,CAAC,CAAA;SAC9C;QACD,IACE,IAAI,CAACA,KAAK,YAAYO,MAAM,IAC5B,IAAI,CAACP,KAAK,YAAYS,UAAU,IAChC,IAAI,CAACT,KAAK,YAAYU,iBAAiB,EACvC;YACA,OAAOH,MAAM,CAACV,IAAI,CAAC,IAAI,CAACG,KAAK,CAAC,CAAA;SAC/B;QACD,MAAM,IAAIW,KAAK,CAAC,WAAW,CAAC,CAAA;KAC7B;IAEDC,YACEb,IAA6C,EAC7CE,KAAa,EACbC,MAAc,CACd;QACA,IAAI,CAACF,KAAK,GAAGD,IAAI;QACjB,IAAI,CAACE,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;KACrB;CACF;kBAhCoBN,SAAS"}