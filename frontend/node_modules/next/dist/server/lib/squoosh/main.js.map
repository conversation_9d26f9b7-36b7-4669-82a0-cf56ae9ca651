{"version": 3, "sources": ["../../../../server/lib/squoosh/main.ts"], "names": ["getMetadata", "processBuffer", "decodeBuffer", "path", "getWorker", "execOnce", "Worker", "resolve", "__dirname", "enableWorkerThreads", "numWorkers", "Math", "max", "min", "cpus", "length", "computeWorkerKey", "method", "buffer", "worker", "width", "height", "operations", "encoding", "quality", "imageData", "operation", "type", "rotate", "numRotations", "opt", "image", "resize", "<PERSON><PERSON><PERSON>", "from", "encodeJpeg", "encodeWebp", "avifQuality", "encodeAvif", "encodePng", "Error"], "mappings": "AAAA;;;;QA8BsBA,WAAW,GAAXA,WAAW;QAQXC,aAAa,GAAbA,aAAa;QAsDbC,YAAY,GAAZA,YAAY;AA5FX,IAAA,WAAgC,WAAhC,gCAAgC,CAAA;AAC3CC,IAAAA,IAAI,mCAAM,MAAM,EAAZ;AACS,IAAA,MAA2B,WAA3B,2BAA2B,CAAA;AAC/B,IAAA,GAAI,WAAJ,IAAI,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBzB,MAAMC,SAAS,GAAGC,CAAAA,GAAAA,MAAQ,AASzB,CAAA,SATyB,CACxB,IACE,IAAIC,WAAM,OAAA,CAACH,IAAI,CAACI,OAAO,CAACC,SAAS,EAAE,MAAM,CAAC,EAAE;QAC1CC,mBAAmB,EAAE,IAAI;QACzB,qEAAqE;QACrE,6BAA6B;QAC7BC,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACC,CAAAA,GAAAA,GAAI,AAAE,CAAA,KAAF,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACvDC,gBAAgB,EAAE,CAACC,MAAM,GAAKA,MAAM;KACrC,CAAC,CACL;AAEM,eAAejB,WAAW,CAC/BkB,MAAc,EAC8B;IAC5C,MAAMC,MAAM,GAA4Bf,SAAS,EAAE,AAAO;IAC1D,MAAM,EAAEgB,KAAK,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAG,MAAMF,MAAM,CAACjB,YAAY,CAACgB,MAAM,CAAC;IAC3D,OAAO;QAAEE,KAAK;QAAEC,MAAM;KAAE,CAAA;CACzB;AAEM,eAAepB,aAAa,CACjCiB,MAAc,EACdI,UAAuB,EACvBC,QAAkB,EAClBC,OAAe,EACE;IACjB,MAAML,MAAM,GAA4Bf,SAAS,EAAE,AAAO;IAE1D,IAAIqB,SAAS,GAAG,MAAMN,MAAM,CAACjB,YAAY,CAACgB,MAAM,CAAC;IACjD,KAAK,MAAMQ,SAAS,IAAIJ,UAAU,CAAE;QAClC,IAAII,SAAS,CAACC,IAAI,KAAK,QAAQ,EAAE;YAC/BF,SAAS,GAAG,MAAMN,MAAM,CAACS,MAAM,CAACH,SAAS,EAAEC,SAAS,CAACG,YAAY,CAAC;SACnE,MAAM,IAAIH,SAAS,CAACC,IAAI,KAAK,QAAQ,EAAE;YACtC,MAAMG,GAAG,GAAG;gBAAEC,KAAK,EAAEN,SAAS;gBAAEL,KAAK,EAAE,CAAC;gBAAEC,MAAM,EAAE,CAAC;aAAE;YACrD,IACEK,SAAS,CAACN,KAAK,IACfK,SAAS,CAACL,KAAK,IACfK,SAAS,CAACL,KAAK,GAAGM,SAAS,CAACN,KAAK,EACjC;gBACAU,GAAG,CAACV,KAAK,GAAGM,SAAS,CAACN,KAAK;aAC5B;YACD,IACEM,SAAS,CAACL,MAAM,IAChBI,SAAS,CAACJ,MAAM,IAChBI,SAAS,CAACJ,MAAM,GAAGK,SAAS,CAACL,MAAM,EACnC;gBACAS,GAAG,CAACT,MAAM,GAAGK,SAAS,CAACL,MAAM;aAC9B;YAED,IAAIS,GAAG,CAACV,KAAK,GAAG,CAAC,IAAIU,GAAG,CAACT,MAAM,GAAG,CAAC,EAAE;gBACnCI,SAAS,GAAG,MAAMN,MAAM,CAACa,MAAM,CAACF,GAAG,CAAC;aACrC;SACF;KACF;IAED,OAAQP,QAAQ;QACd,KAAK,MAAM;YACT,OAAOU,MAAM,CAACC,IAAI,CAAC,MAAMf,MAAM,CAACgB,UAAU,CAACV,SAAS,EAAE;gBAAED,OAAO;aAAE,CAAC,CAAC,CAAA;QACrE,KAAK,MAAM;YACT,OAAOS,MAAM,CAACC,IAAI,CAAC,MAAMf,MAAM,CAACiB,UAAU,CAACX,SAAS,EAAE;gBAAED,OAAO;aAAE,CAAC,CAAC,CAAA;QACrE,KAAK,MAAM;YACT,MAAMa,WAAW,GAAGb,OAAO,GAAG,EAAE;YAChC,OAAOS,MAAM,CAACC,IAAI,CAChB,MAAMf,MAAM,CAACmB,UAAU,CAACb,SAAS,EAAE;gBACjCD,OAAO,EAAEb,IAAI,CAACC,GAAG,CAACyB,WAAW,EAAE,CAAC,CAAC;aAClC,CAAC,CACH,CAAA;QACH,KAAK,KAAK;YACR,OAAOJ,MAAM,CAACC,IAAI,CAAC,MAAMf,MAAM,CAACoB,SAAS,CAACd,SAAS,CAAC,CAAC,CAAA;QACvD;YACE,MAAMe,KAAK,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAA;KAC7C;CACF;AAEM,eAAetC,YAAY,CAACgB,MAAc,EAAE;IACjD,MAAMC,MAAM,GAA4Bf,SAAS,EAAE,AAAO;IAC1D,MAAMqB,SAAS,GAAG,MAAMN,MAAM,CAACjB,YAAY,CAACgB,MAAM,CAAC;IACnD,OAAOO,SAAS,CAAA;CACjB"}