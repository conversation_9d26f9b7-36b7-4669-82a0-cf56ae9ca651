{"version": 3, "sources": ["../../../server/lib/start-server.ts"], "names": ["startServer", "opts", "requestHandler", "server", "http", "createServer", "req", "res", "keepAliveTimeout", "Promise", "resolve", "reject", "port", "retryCount", "on", "err", "allowRetry", "code", "warn", "listen", "hostname", "upgradeHandler", "dev", "socket", "upgrade", "addr", "address", "app", "next", "customServer", "httpServer", "getRequestHandler", "getUpgradeHandler"], "mappings": "AAAA;;;;QAUgBA,WAAW,GAAXA,WAAW;AATN,IAAA,IAAwB,WAAxB,wBAAwB,CAAA;AAC5B,IAAA,KAAM,kCAAN,MAAM,EAAA;AACN,IAAA,KAAS,kCAAT,SAAS,EAAA;;;;;;AAOnB,SAASA,WAAW,CAACC,IAAwB,EAAE;IACpD,IAAIC,cAAc,AAAgB;IAElC,MAAMC,MAAM,GAAGC,KAAI,QAAA,CAACC,YAAY,CAAC,CAACC,GAAG,EAAEC,GAAG,GAAK;QAC7C,OAAOL,cAAc,CAACI,GAAG,EAAEC,GAAG,CAAC,CAAA;KAChC,CAAC;IAEF,IAAIN,IAAI,CAACO,gBAAgB,EAAE;QACzBL,MAAM,CAACK,gBAAgB,GAAGP,IAAI,CAACO,gBAAgB;KAChD;IAED,OAAO,IAAIC,OAAO,CAAa,CAACC,OAAO,EAAEC,MAAM,GAAK;QAClD,IAAIC,IAAI,GAAGX,IAAI,CAACW,IAAI;QACpB,IAAIC,UAAU,GAAG,CAAC;QAElBV,MAAM,CAACW,EAAE,CAAC,OAAO,EAAE,CAACC,GAA0B,GAAK;YACjD,IACEH,IAAI,IACJX,IAAI,CAACe,UAAU,IACfD,GAAG,CAACE,IAAI,KAAK,YAAY,IACzBJ,UAAU,GAAG,EAAE,EACf;gBACAK,CAAAA,GAAAA,IAAI,AAAuD,CAAA,KAAvD,CAAC,CAAC,KAAK,EAAEN,IAAI,CAAC,mBAAmB,EAAEA,IAAI,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;gBAC3DA,IAAI,IAAI,CAAC;gBACTC,UAAU,IAAI,CAAC;gBACfV,MAAM,CAACgB,MAAM,CAACP,IAAI,EAAEX,IAAI,CAACmB,QAAQ,CAAC;aACnC,MAAM;gBACLT,MAAM,CAACI,GAAG,CAAC;aACZ;SACF,CAAC;QAEF,IAAIM,cAAc,AAAK;QAEvB,IAAI,CAACpB,IAAI,CAACqB,GAAG,EAAE;YACbnB,MAAM,CAACW,EAAE,CAAC,SAAS,EAAE,CAACR,GAAG,EAAEiB,MAAM,EAAEC,OAAO,GAAK;gBAC7CH,cAAc,CAACf,GAAG,EAAEiB,MAAM,EAAEC,OAAO,CAAC;aACrC,CAAC;SACH;QAEDrB,MAAM,CAACW,EAAE,CAAC,WAAW,EAAE,IAAM;YAC3B,MAAMW,IAAI,GAAGtB,MAAM,CAACuB,OAAO,EAAE;YAC7B,MAAMN,QAAQ,GACZ,CAACnB,IAAI,CAACmB,QAAQ,IAAInB,IAAI,CAACmB,QAAQ,KAAK,SAAS,GACzC,WAAW,GACXnB,IAAI,CAACmB,QAAQ;YAEnB,MAAMO,GAAG,GAAGC,CAAAA,GAAAA,KAAI,AAMd,CAAA,QANc,CAAC;gBACf,GAAG3B,IAAI;gBACPmB,QAAQ;gBACRS,YAAY,EAAE,KAAK;gBACnBC,UAAU,EAAE3B,MAAM;gBAClBS,IAAI,EAAEa,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,CAACb,IAAI,GAAGA,IAAI;aAC1D,CAAC;YAEFV,cAAc,GAAGyB,GAAG,CAACI,iBAAiB,EAAE;YACxCV,cAAc,GAAGM,GAAG,CAACK,iBAAiB,EAAE;YACxCtB,OAAO,CAACiB,GAAG,CAAC;SACb,CAAC;QAEFxB,MAAM,CAACgB,MAAM,CAACP,IAAI,EAAEX,IAAI,CAACmB,QAAQ,CAAC;KACnC,CAAC,CAAA;CACH"}