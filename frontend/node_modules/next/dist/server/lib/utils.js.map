{"version": 3, "sources": ["../../../server/lib/utils.ts"], "names": ["printAndExit", "getNodeOptionsWithoutInspect", "getPort", "message", "code", "console", "log", "error", "process", "exit", "NODE_INSPECT_RE", "env", "NODE_OPTIONS", "replace", "args", "parsed", "PORT", "parseInt", "Number", "isNaN"], "mappings": "AAAA;;;;QAEgBA,YAAY,GAAZA,YAAY;QAUZC,4BAA4B,GAA5BA,4BAA4B;QAK5BC,OAAO,GAAPA,OAAO;AAfhB,SAASF,YAAY,CAACG,OAAe,EAAEC,IAAI,GAAG,CAAC,EAAE;IACtD,IAAIA,IAAI,KAAK,CAAC,EAAE;QACdC,OAAO,CAACC,GAAG,CAACH,OAAO,CAAC;KACrB,MAAM;QACLE,OAAO,CAACE,KAAK,CAACJ,OAAO,CAAC;KACvB;IAEDK,OAAO,CAACC,IAAI,CAACL,IAAI,CAAC;CACnB;AAEM,SAASH,4BAA4B,GAAG;IAC7C,MAAMS,eAAe,iCAAiC;IACtD,OAAO,CAACF,OAAO,CAACG,GAAG,CAACC,YAAY,IAAI,EAAE,CAAC,CAACC,OAAO,CAACH,eAAe,EAAE,EAAE,CAAC,CAAA;CACrE;AAEM,SAASR,OAAO,CAACY,IAA0B,EAAU;IAC1D,IAAI,OAAOA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;QACtC,OAAOA,IAAI,CAAC,QAAQ,CAAC,CAAA;KACtB;IAED,MAAMC,MAAM,GAAGP,OAAO,CAACG,GAAG,CAACK,IAAI,IAAIC,QAAQ,CAACT,OAAO,CAACG,GAAG,CAACK,IAAI,EAAE,EAAE,CAAC;IACjE,IAAI,OAAOD,MAAM,KAAK,QAAQ,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,MAAM,CAAC,EAAE;QACvD,OAAOA,MAAM,CAAA;KACd;IAED,OAAO,IAAI,CAAA;CACZ"}