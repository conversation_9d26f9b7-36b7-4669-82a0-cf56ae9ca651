{"version": 3, "sources": ["../../../../server/lib/incremental-cache/file-system-cache.ts"], "names": ["FileSystemCache", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "appDir", "_appDir", "maxMemoryCacheSize", "memoryCache", "L<PERSON><PERSON><PERSON>", "max", "length", "value", "JSON", "kind", "stringify", "props", "Error", "html", "pageData", "get", "key", "data", "filePath", "htmlPath", "isAppPath", "getFsPath", "readFile", "parse", "mtime", "stat", "lastModified", "getTime", "set", "_", "Date", "now", "mkdir", "path", "dirname", "writeFile", "pathname", "join", "err"], "mappings": "AAAA;;;;;AAAqB,IAAA,SAA8B,kCAA9B,8BAA8B,EAAA;AAClC,IAAA,KAAqC,kCAArC,qCAAqC,EAAA;AAGvC,MAAMA,eAAe;IAOlCC,YAAYC,GAAwB,CAAE;QACpC,IAAI,CAACC,EAAE,GAAGD,GAAG,CAACC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,GAAG,CAACE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,GAAG,CAACG,aAAa;QACtC,IAAI,CAACC,MAAM,GAAG,CAAC,CAACJ,GAAG,CAACK,OAAO;QAE3B,IAAIL,GAAG,CAACM,kBAAkB,EAAE;YAC1B,IAAI,CAACC,WAAW,GAAG,IAAIC,SAAQ,QAAA,CAAC;gBAC9BC,GAAG,EAAET,GAAG,CAACM,kBAAkB;gBAC3BI,MAAM,EAAC,EAAEC,KAAK,CAAA,EAAE,EAAE;wBAUOC,GAA8B;oBATrD,IAAI,CAACD,KAAK,EAAE;wBACV,OAAO,EAAE,CAAA;qBACV,MAAM,IAAIA,KAAK,CAACE,IAAI,KAAK,UAAU,EAAE;wBACpC,OAAOD,IAAI,CAACE,SAAS,CAACH,KAAK,CAACI,KAAK,CAAC,CAACL,MAAM,CAAA;qBAC1C,MAAM,IAAIC,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;wBACjC,MAAM,IAAIG,KAAK,CAAC,iDAAiD,CAAC,CAAA;qBACnE;oBACD,wCAAwC;oBACxC,OACEL,KAAK,CAACM,IAAI,CAACP,MAAM,GAAG,CAACE,CAAAA,CAAAA,GAA8B,GAA9BA,IAAI,CAACE,SAAS,CAACH,KAAK,CAACO,QAAQ,CAAC,SAAQ,GAAtCN,KAAAA,CAAsC,GAAtCA,GAA8B,CAAEF,MAAM,CAAA,IAAI,CAAC,CAAC,CAClE;iBACF;aACF,CAAC;SACH;KACF;IAED,MAAaS,GAAG,CAACC,GAAW,EAAE;YACjB,GAAgB;QAA3B,IAAIC,IAAI,GAAG,CAAA,GAAgB,GAAhB,IAAI,CAACd,WAAW,SAAK,GAArB,KAAA,CAAqB,GAArB,GAAgB,CAAEY,GAAG,CAACC,GAAG,CAAC;QAErC,qCAAqC;QACrC,IAAI,CAACC,IAAI,EAAE;YACT,IAAI;oBA4BF,IAAgB;gBA3BhB,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,CAAA,EAAEC,SAAS,CAAA,EAAE,GAAG,MAAM,IAAI,CAACC,SAAS,CAC5D,CAAC,EAAEL,GAAG,CAAC,KAAK,CAAC,CACd;gBACD,MAAMH,IAAI,GAAG,MAAM,IAAI,CAAChB,EAAE,CAACyB,QAAQ,CAACH,QAAQ,CAAC;gBAC7C,MAAML,QAAQ,GAAGM,SAAS,GACtB,MAAM,IAAI,CAACvB,EAAE,CAACyB,QAAQ,CACpB,CACE,MAAM,IAAI,CAACD,SAAS,CAAC,CAAC,EAAEL,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CACzC,CAACE,QAAQ,CACX,GACDV,IAAI,CAACe,KAAK,CACR,MAAM,IAAI,CAAC1B,EAAE,CAACyB,QAAQ,CACpB,MAAM,CACJ,MAAM,IAAI,CAACD,SAAS,CAAC,CAAC,EAAEL,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAC3C,CAACE,QAAQ,CACX,CACF;gBACL,MAAM,EAAEM,KAAK,CAAA,EAAE,GAAG,MAAM,IAAI,CAAC3B,EAAE,CAAC4B,IAAI,CAACN,QAAQ,CAAC;gBAE9CF,IAAI,GAAG;oBACLS,YAAY,EAAEF,KAAK,CAACG,OAAO,EAAE;oBAC7BpB,KAAK,EAAE;wBACLE,IAAI,EAAE,MAAM;wBACZI,IAAI;wBACJC,QAAQ;qBACT;iBACF;gBACD,CAAA,IAAgB,GAAhB,IAAI,CAACX,WAAW,SAAK,GAArB,KAAA,CAAqB,GAArB,IAAgB,CAAEyB,GAAG,CAACZ,GAAG,EAAEC,IAAI,CAAC,CAAA;aACjC,CAAC,OAAOY,CAAC,EAAE;YACV,+BAA+B;aAChC;SACF;QACD,OAAOZ,IAAI,IAAI,IAAI,CAAA;KACpB;IAED,MAAaW,GAAG,CAACZ,GAAW,EAAEC,IAAgC,EAAE;YAG9D,GAAgB;QAFhB,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAE,OAAM;QAE7B,CAAA,GAAgB,GAAhB,IAAI,CAACK,WAAW,SAAK,GAArB,KAAA,CAAqB,GAArB,GAAgB,CAAEyB,GAAG,CAACZ,GAAG,EAAE;YACzBT,KAAK,EAAEU,IAAI;YACXS,YAAY,EAAEI,IAAI,CAACC,GAAG,EAAE;SACzB,CAAC,CAAA;QAEF,IAAId,CAAAA,IAAI,QAAM,GAAVA,KAAAA,CAAU,GAAVA,IAAI,CAAER,IAAI,CAAA,KAAK,MAAM,EAAE;YACzB,MAAMW,SAAS,GAAG,OAAOH,IAAI,CAACH,QAAQ,KAAK,QAAQ;YACnD,MAAM,EAAEI,QAAQ,EAAEC,QAAQ,CAAA,EAAE,GAAG,MAAM,IAAI,CAACE,SAAS,CACjD,CAAC,EAAEL,GAAG,CAAC,KAAK,CAAC,EACbI,SAAS,CACV;YACD,MAAM,IAAI,CAACvB,EAAE,CAACmC,KAAK,CAACC,KAAI,QAAA,CAACC,OAAO,CAACf,QAAQ,CAAC,CAAC;YAC3C,MAAM,IAAI,CAACtB,EAAE,CAACsC,SAAS,CAAChB,QAAQ,EAAEF,IAAI,CAACJ,IAAI,CAAC;YAE5C,MAAM,IAAI,CAAChB,EAAE,CAACsC,SAAS,CACrB,CACE,MAAM,IAAI,CAACd,SAAS,CAClB,CAAC,EAAEL,GAAG,CAAC,CAAC,EAAEI,SAAS,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,EACtCA,SAAS,CACV,CACF,CAACF,QAAQ,EACVE,SAAS,GAAGH,IAAI,CAACH,QAAQ,GAAGN,IAAI,CAACE,SAAS,CAACO,IAAI,CAACH,QAAQ,CAAC,CAC1D;SACF;KACF;IAED,MAAcO,SAAS,CACrBe,QAAgB,EAChBpC,MAAgB,EAIf;QACD,IAAIoB,SAAS,GAAG,KAAK;QACrB,IAAIF,QAAQ,GAAGe,KAAI,QAAA,CAACI,IAAI,CAAC,IAAI,CAACtC,aAAa,EAAE,OAAO,EAAEqC,QAAQ,CAAC;QAE/D,IAAI,CAAC,IAAI,CAACpC,MAAM,IAAIA,MAAM,KAAK,KAAK,EAClC,OAAO;YACLkB,QAAQ;YACRE,SAAS;SACV,CAAA;QACH,IAAI;YACF,MAAM,IAAI,CAACvB,EAAE,CAACyB,QAAQ,CAACJ,QAAQ,CAAC;YAChC,OAAO;gBACLA,QAAQ;gBACRE,SAAS;aACV,CAAA;SACF,CAAC,OAAOkB,GAAG,EAAE;YACZ,OAAO;gBACLpB,QAAQ,EAAEe,KAAI,QAAA,CAACI,IAAI,CAAC,IAAI,CAACtC,aAAa,EAAE,KAAK,EAAEqC,QAAQ,CAAC;gBACxDhB,SAAS,EAAE,IAAI;aAChB,CAAA;SACF;KACF;CACF;kBAnIoB1B,eAAe"}