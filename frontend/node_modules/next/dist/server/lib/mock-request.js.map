{"version": 3, "sources": ["../../../server/lib/mock-request.ts"], "names": ["mockRequest", "requestUrl", "requestHeaders", "requestMethod", "requestConnection", "resBuffers", "mockRes", "Stream", "Writable", "isStreamFinished", "Promise", "resolve", "reject", "on", "err", "write", "chunk", "push", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "from", "_write", "_encoding", "callback", "mockHeaders", "writeHead", "_status", "_headers", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "name", "toLowerCase", "getHeaders", "getHeaderNames", "keys", "<PERSON><PERSON><PERSON><PERSON>", "value", "removeHeader", "_implicitHeader", "connection", "finished", "statusCode", "mockReq", "Readable", "_read", "emit", "headers", "method", "url", "req", "res", "streamPromise"], "mappings": "AAAA;;;;QAEgBA,WAAW,GAAXA,WAAW;AAFR,IAAA,OAAQ,kCAAR,QAAQ,EAAA;;;;;;AAEpB,SAASA,WAAW,CACzBC,UAAkB,EAClBC,cAA6D,EAC7DC,aAAqB,EACrBC,iBAAuB,EACvB;IACA,MAAMC,UAAU,GAAa,EAAE;IAC/B,MAAMC,OAAO,GAAQ,IAAIC,OAAM,QAAA,CAACC,QAAQ,EAAE;IAE1C,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,CAAC,SAAUC,OAAO,EAAEC,MAAM,EAAE;QAC9DN,OAAO,CAACO,EAAE,CAAC,QAAQ,EAAE,IAAMF,OAAO,CAAC,IAAI,CAAC,CAAC;QACzCL,OAAO,CAACO,EAAE,CAAC,KAAK,EAAE,IAAMF,OAAO,CAAC,IAAI,CAAC,CAAC;QACtCL,OAAO,CAACO,EAAE,CAAC,OAAO,EAAE,CAACC,GAAQ,GAAKF,MAAM,CAACE,GAAG,CAAC,CAAC;KAC/C,CAAC;IAEFR,OAAO,CAACS,KAAK,GAAG,CAACC,KAAsB,GAAK;QAC1CX,UAAU,CAACY,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAGE,MAAM,CAACE,IAAI,CAACJ,KAAK,CAAC,CAAC;KACrE;IACDV,OAAO,CAACe,MAAM,GAAG,CACfL,KAAsB,EACtBM,SAAiB,EACjBC,QAAoB,GACjB;QACHjB,OAAO,CAACS,KAAK,CAACC,KAAK,CAAC;QACpB,kFAAkF;QAClF,wFAAwF;QACxF,uBAAuB;QACvB,6FAA6F;QAC7FO,QAAQ,EAAE;KACX;IAED,MAAMC,WAAW,GAAsC,EAAE;IAEzDlB,OAAO,CAACmB,SAAS,GAAG,CAACC,OAAY,EAAEC,QAAa,GAC9CC,MAAM,CAACC,MAAM,CAACL,WAAW,EAAEG,QAAQ,CAAC;IACtCrB,OAAO,CAACwB,SAAS,GAAG,CAACC,IAAY,GAAKP,WAAW,CAACO,IAAI,CAACC,WAAW,EAAE,CAAC;IACrE1B,OAAO,CAAC2B,UAAU,GAAG,IAAMT,WAAW;IACtClB,OAAO,CAAC4B,cAAc,GAAG,IAAMN,MAAM,CAACO,IAAI,CAACX,WAAW,CAAC;IACvDlB,OAAO,CAAC8B,SAAS,GAAG,CAACL,IAAY,EAAEM,KAAwB,GACxDb,WAAW,CAACO,IAAI,CAACC,WAAW,EAAE,CAAC,GAAGK,KAAK,AAAC;IAC3C/B,OAAO,CAACgC,YAAY,GAAG,CAACP,IAAY,GAAK;QACvC,OAAOP,WAAW,CAACO,IAAI,CAACC,WAAW,EAAE,CAAC;KACvC;IACD1B,OAAO,CAACiC,eAAe,GAAG,IAAM,EAAE;IAClCjC,OAAO,CAACkC,UAAU,GAAGpC,iBAAiB;IACtCE,OAAO,CAACmC,QAAQ,GAAG,KAAK;IACxBnC,OAAO,CAACoC,UAAU,GAAG,GAAG;IAExB,MAAMC,OAAO,GAAQ,IAAIpC,OAAM,QAAA,CAACqC,QAAQ,EAAE;IAE1CD,OAAO,CAACE,KAAK,GAAG,IAAM;QACpBF,OAAO,CAACG,IAAI,CAAC,KAAK,CAAC;QACnBH,OAAO,CAACG,IAAI,CAAC,OAAO,CAAC;QACrB,OAAO5B,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC,CAAA;KACvB;IAEDuB,OAAO,CAACI,OAAO,GAAG7C,cAAc;IAChCyC,OAAO,CAACK,MAAM,GAAG7C,aAAa;IAC9BwC,OAAO,CAACM,GAAG,GAAGhD,UAAU;IACxB0C,OAAO,CAACH,UAAU,GAAGpC,iBAAiB;IAEtC,OAAO;QACLC,UAAU;QACV6C,GAAG,EAAEP,OAAO;QACZQ,GAAG,EAAE7C,OAAO;QACZ8C,aAAa,EAAE3C,gBAAgB;KAChC,CAAA;CACF"}