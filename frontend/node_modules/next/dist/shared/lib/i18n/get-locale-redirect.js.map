{"version": 3, "sources": ["../../../../shared/lib/i18n/get-locale-redirect.ts"], "names": ["getLocaleRedirect", "getLocaleFromCookie", "i18n", "headers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nextLocale", "NEXT_LOCALE", "toLowerCase", "locales", "find", "locale", "undefined", "detectLocale", "domainLocale", "preferredLocale", "pathLocale", "defaultLocale", "getAcceptPreferredLocale", "Array", "isArray", "acceptLanguage", "err", "nextConfig", "urlParsed", "localeDetection", "denormalizePagePath", "pathname", "detectedLocale", "preferredDomain", "detectDomainLocale", "domains", "isPDomain", "domain", "isPLocale", "scheme", "http", "rlocale", "formatUrl", "basePath"], "mappings": "AAAA;;;;QAqEgBA,iBAAiB,GAAjBA,iBAAiB;;AAnEF,IAAA,aAA+B,WAA/B,+BAA+B,CAAA;AAC1B,IAAA,oBAAoC,WAApC,oCAAoC,CAAA;AACrC,IAAA,mBAAwB,WAAxB,wBAAwB,CAAA;AACjC,IAAA,UAA4B,WAA5B,4BAA4B,CAAA;AACtB,IAAA,SAA2B,WAA3B,2BAA2B,CAAA;AAe3D,SAASC,mBAAmB,CAC1BC,IAAgB,EAChBC,OAAyD,GAAG,EAAE,EAC9D;QACmBC,GAEhB;IAFH,MAAMC,UAAU,GAAGD,CAAAA,GAEhB,GAFgBA,CAAAA,GAAAA,SAAe,AAEjC,CAAA,gBAFiC,CAChCD,OAAO,IAAI,EAAE,CACd,EAAE,SAAa,GAFGC,KAAAA,CAEH,GAFGA,QAAAA,GAEhB,CAAEE,WAAW,SAAA,GAFGF,KAAAA,CAEH,GAFGA,KAEDG,WAAW,EAAE;IAC/B,OAAOF,UAAU,GACbH,IAAI,CAACM,OAAO,CAACC,IAAI,CAAC,CAACC,MAAM,GAAKL,UAAU,KAAKK,MAAM,CAACH,WAAW,EAAE,CAAC,GAClEI,SAAS,CAAA;CACd;AAED,SAASC,YAAY,CAAC,EACpBV,IAAI,CAAA,EACJC,OAAO,CAAA,EACPU,YAAY,CAAA,EACZC,eAAe,CAAA,EACfC,UAAU,CAAA,EAOX,EAAE;IACD,OACEA,UAAU,IACVF,CAAAA,YAAY,QAAe,GAA3BA,KAAAA,CAA2B,GAA3BA,YAAY,CAAEG,aAAa,CAAA,IAC3Bf,mBAAmB,CAACC,IAAI,EAAEC,OAAO,CAAC,IAClCW,eAAe,IACfZ,IAAI,CAACc,aAAa,CACnB;CACF;AAED,SAASC,wBAAwB,CAC/Bf,IAAgB,EAChBC,OAA0D,EAC1D;IACA,IACEA,CAAAA,OAAO,QAAqB,GAA5BA,KAAAA,CAA4B,GAA5BA,OAAO,AAAE,CAAC,iBAAiB,CAAC,CAAA,IAC5B,CAACe,KAAK,CAACC,OAAO,CAAChB,OAAO,CAAC,iBAAiB,CAAC,CAAC,EAC1C;QACA,IAAI;YACF,OAAOiB,CAAAA,GAAAA,aAAc,AAA0C,CAAA,eAA1C,CAACjB,OAAO,CAAC,iBAAiB,CAAC,EAAED,IAAI,CAACM,OAAO,CAAC,CAAA;SAChE,CAAC,OAAOa,GAAG,EAAE,EAAE;KACjB;CACF;AAEM,SAASrB,iBAAiB,CAAC,EAChCgB,aAAa,CAAA,EACbH,YAAY,CAAA,EACZE,UAAU,CAAA,EACVZ,OAAO,CAAA,EACPmB,UAAU,CAAA,EACVC,SAAS,CAAA,EACD,EAAE;IACV,IACED,UAAU,CAACpB,IAAI,IACfoB,UAAU,CAACpB,IAAI,CAACsB,eAAe,KAAK,KAAK,IACzCC,CAAAA,GAAAA,oBAAmB,AAAoB,CAAA,oBAApB,CAACF,SAAS,CAACG,QAAQ,CAAC,KAAK,GAAG,EAC/C;QACA,MAAMZ,eAAe,GAAGG,wBAAwB,CAACK,UAAU,CAACpB,IAAI,EAAEC,OAAO,CAAC;QAC1E,MAAMwB,cAAc,GAAGf,YAAY,CAAC;YAClCV,IAAI,EAAEoB,UAAU,CAACpB,IAAI;YACrBY,eAAe;YACfX,OAAO;YACPY,UAAU;YACVF,YAAY;SACb,CAAC;QAEF,MAAMe,eAAe,GAAGC,CAAAA,GAAAA,mBAAkB,AAIzC,CAAA,mBAJyC,CACxCP,UAAU,CAACpB,IAAI,CAAC4B,OAAO,EACvBnB,SAAS,EACTG,eAAe,CAChB;QAED,IAAID,YAAY,IAAIe,eAAe,EAAE;YACnC,MAAMG,SAAS,GAAGH,eAAe,CAACI,MAAM,KAAKnB,YAAY,CAACmB,MAAM;YAChE,MAAMC,SAAS,GAAGL,eAAe,CAACZ,aAAa,KAAKF,eAAe;YACnE,IAAI,CAACiB,SAAS,IAAI,CAACE,SAAS,EAAE;gBAC5B,MAAMC,MAAM,GAAG,CAAC,IAAI,EAAEN,eAAe,CAACO,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;gBACvD,MAAMC,OAAO,GAAGH,SAAS,GAAG,EAAE,GAAGnB,eAAe;gBAChD,OAAO,CAAC,EAAEoB,MAAM,CAAC,GAAG,EAAEN,eAAe,CAACI,MAAM,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAA;aAC1D;SACF;QAED,IAAIT,cAAc,CAACpB,WAAW,EAAE,KAAKS,aAAa,CAACT,WAAW,EAAE,EAAE;YAChE,OAAO8B,CAAAA,GAAAA,UAAS,AAGd,CAAA,UAHc,CAAC,aACZd,SAAS;gBACZG,QAAQ,EAAE,CAAC,EAAEJ,UAAU,CAACgB,QAAQ,IAAI,EAAE,CAAC,CAAC,EAAEX,cAAc,CAAC,CAAC;cAC3D,CAAC,CAAA;SACH;KACF;CACF"}