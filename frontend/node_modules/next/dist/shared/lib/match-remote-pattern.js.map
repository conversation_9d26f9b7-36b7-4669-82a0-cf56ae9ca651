{"version": 3, "sources": ["../../../shared/lib/match-remote-pattern.ts"], "names": ["matchRemotePattern", "hasMatch", "pattern", "url", "protocol", "undefined", "actualProto", "slice", "port", "hostname", "Error", "JSON", "stringify", "makeRe", "test", "pathname", "domains", "remotePatterns", "some", "domain", "p"], "mappings": "AAAA;;;;QAGgBA,kBAAkB,GAAlBA,kBAAkB;QA8BlBC,QAAQ,GAARA,QAAQ;AAhCD,IAAA,WAA+B,WAA/B,+BAA+B,CAAA;AAE/C,SAASD,kBAAkB,CAACE,OAAsB,EAAEC,GAAQ,EAAW;IAC5E,IAAID,OAAO,CAACE,QAAQ,KAAKC,SAAS,EAAE;QAClC,MAAMC,WAAW,GAAGH,GAAG,CAACC,QAAQ,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAIL,OAAO,CAACE,QAAQ,KAAKE,WAAW,EAAE;YACpC,OAAO,KAAK,CAAA;SACb;KACF;IACD,IAAIJ,OAAO,CAACM,IAAI,KAAKH,SAAS,EAAE;QAC9B,IAAIH,OAAO,CAACM,IAAI,KAAKL,GAAG,CAACK,IAAI,EAAE;YAC7B,OAAO,KAAK,CAAA;SACb;KACF;IAED,IAAIN,OAAO,CAACO,QAAQ,KAAKJ,SAAS,EAAE;QAClC,MAAM,IAAIK,KAAK,CACb,CAAC,0CAA0C,EAAEC,IAAI,CAACC,SAAS,CAACV,OAAO,CAAC,CAAC,CAAC,CACvE,CAAA;KACF,MAAM;QACL,IAAI,CAACW,CAAAA,GAAAA,WAAM,AAAkB,CAAA,OAAlB,CAACX,OAAO,CAACO,QAAQ,CAAC,CAACK,IAAI,CAACX,GAAG,CAACM,QAAQ,CAAC,EAAE;YAChD,OAAO,KAAK,CAAA;SACb;KACF;QAEWP,SAAgB;IAA5B,IAAI,CAACW,CAAAA,GAAAA,WAAM,AAA0B,CAAA,OAA1B,CAACX,CAAAA,SAAgB,GAAhBA,OAAO,CAACa,QAAQ,YAAhBb,SAAgB,GAAI,IAAI,CAAC,CAACY,IAAI,CAACX,GAAG,CAACY,QAAQ,CAAC,EAAE;QACxD,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;CACZ;AAEM,SAASd,QAAQ,CACtBe,OAAiB,EACjBC,cAA+B,EAC/Bd,GAAQ,EACC;IACT,OACEa,OAAO,CAACE,IAAI,CAAC,CAACC,MAAM,GAAKhB,GAAG,CAACM,QAAQ,KAAKU,MAAM,CAAC,IACjDF,cAAc,CAACC,IAAI,CAAC,CAACE,CAAC,GAAKpB,kBAAkB,CAACoB,CAAC,EAAEjB,GAAG,CAAC,CAAC,CACvD;CACF"}