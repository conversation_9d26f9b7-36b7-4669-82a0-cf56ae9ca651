{"version": 3, "sources": ["../../../src/shared/lib/match-remote-pattern.ts"], "names": ["hasMatch", "matchRemotePattern", "pattern", "url", "protocol", "undefined", "actualProto", "slice", "port", "hostname", "Error", "JSON", "stringify", "makeRe", "test", "pathname", "dot", "domains", "remotePatterns", "some", "domain", "p"], "mappings": ";;;;;;;;;;;;;;;IAiCgBA,QAAQ;eAARA;;IA9BAC,kBAAkB;eAAlBA;;;2BAFO;AAEhB,SAASA,mBAAmBC,OAAsB,EAAEC,GAAQ;IACjE,IAAID,QAAQE,QAAQ,KAAKC,WAAW;QAClC,MAAMC,cAAcH,IAAIC,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC;QAC3C,IAAIL,QAAQE,QAAQ,KAAKE,aAAa;YACpC,OAAO;QACT;IACF;IACA,IAAIJ,QAAQM,IAAI,KAAKH,WAAW;QAC9B,IAAIH,QAAQM,IAAI,KAAKL,IAAIK,IAAI,EAAE;YAC7B,OAAO;QACT;IACF;IAEA,IAAIN,QAAQO,QAAQ,KAAKJ,WAAW;QAClC,MAAM,IAAIK,MACR,AAAC,+CAA4CC,KAAKC,SAAS,CAACV;IAEhE,OAAO;QACL,IAAI,CAACW,IAAAA,iBAAM,EAACX,QAAQO,QAAQ,EAAEK,IAAI,CAACX,IAAIM,QAAQ,GAAG;YAChD,OAAO;QACT;IACF;QAEYP;IAAZ,IAAI,CAACW,IAAAA,iBAAM,EAACX,CAAAA,oBAAAA,QAAQa,QAAQ,YAAhBb,oBAAoB,MAAM;QAAEc,KAAK;IAAK,GAAGF,IAAI,CAACX,IAAIY,QAAQ,GAAG;QACvE,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAASf,SACdiB,OAAiB,EACjBC,cAA+B,EAC/Bf,GAAQ;IAER,OACEc,QAAQE,IAAI,CAAC,CAACC,SAAWjB,IAAIM,QAAQ,KAAKW,WAC1CF,eAAeC,IAAI,CAAC,CAACE,IAAMpB,mBAAmBoB,GAAGlB;AAErD"}