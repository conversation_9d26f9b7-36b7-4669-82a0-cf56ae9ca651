{"version": 3, "sources": ["../../../../../shared/lib/router/utils/add-path-suffix.ts"], "names": ["addPathSuffix", "path", "suffix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": "AAAA;;;;QAOgBA,aAAa,GAAbA,aAAa;AAPH,IAAA,UAAc,WAAd,cAAc,CAAA;AAOjC,SAASA,aAAa,CAACC,IAAY,EAAEC,MAAe,EAAE;IAC3D,IAAI,CAACD,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,IAAI,CAACD,MAAM,EAAE;QACpC,OAAOD,IAAI,CAAA;KACZ;IAED,MAAM,EAAEG,QAAQ,CAAA,EAAEC,KAAK,CAAA,EAAEC,IAAI,CAAA,EAAE,GAAGC,CAAAA,GAAAA,UAAS,AAAM,CAAA,UAAN,CAACN,IAAI,CAAC;IACjD,OAAO,CAAC,EAAEG,QAAQ,CAAC,EAAEF,MAAM,CAAC,EAAEG,KAAK,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAA;CAC7C"}