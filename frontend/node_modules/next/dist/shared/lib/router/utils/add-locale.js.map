{"version": 3, "sources": ["../../../../../shared/lib/router/utils/add-locale.ts"], "names": ["addLocale", "path", "locale", "defaultLocale", "ignorePrefix", "pathHasPrefix", "toLowerCase", "addPathPrefix"], "mappings": "AAAA;;;;QAQgBA,SAAS,GAATA,SAAS;AARK,IAAA,cAAmB,WAAnB,mBAAmB,CAAA;AACnB,IAAA,cAAmB,WAAnB,mBAAmB,CAAA;AAO1C,SAASA,SAAS,CACvBC,IAAY,EACZC,MAAuB,EACvBC,aAAsB,EACtBC,YAAsB,EACtB;IACA,IACEF,MAAM,IACNA,MAAM,KAAKC,aAAa,IACxB,CAACC,YAAY,IACV,CAACC,CAAAA,GAAAA,cAAa,AAAgD,CAAA,cAAhD,CAACJ,IAAI,CAACK,WAAW,EAAE,EAAE,CAAC,CAAC,EAAEJ,MAAM,CAACI,WAAW,EAAE,CAAC,CAAC,CAAC,IAC7D,CAACD,CAAAA,GAAAA,cAAa,AAA4B,CAAA,cAA5B,CAACJ,IAAI,CAACK,WAAW,EAAE,EAAE,MAAM,CAAC,AAAC,CAAC,EAChD;QACA,OAAOC,CAAAA,GAAAA,cAAa,AAAoB,CAAA,cAApB,CAACN,IAAI,EAAE,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAA;KACzC;IAED,OAAOD,IAAI,CAAA;CACZ"}