{"version": 3, "sources": ["../../../../../shared/lib/router/utils/resolve-rewrites.ts"], "names": ["resolveRewrites", "<PERSON><PERSON><PERSON>", "pages", "rewrites", "query", "resolveHref", "locales", "matchedPage", "externalDest", "parsedAs", "parseRelativeUrl", "fsPathname", "removeTrailingSlash", "normalizeLocalePath", "removeBasePath", "pathname", "resolvedHref", "handleRewrite", "rewrite", "matcher", "getPathMatch", "source", "process", "env", "__NEXT_TRAILING_SLASH", "removeUnnamedP<PERSON>ms", "strict", "params", "has", "missing", "hasParams", "matchHas", "headers", "host", "document", "location", "hostname", "cookies", "cookie", "split", "reduce", "acc", "item", "key", "value", "join", "Object", "assign", "destination", "destRes", "prepareDestination", "appendParamsToQuery", "parsedDestination", "newUrl", "includes", "finished", "i", "beforeFiles", "length", "afterFiles", "fallback"], "mappings": "AAAA;;;;kBASwBA,eAAe;AAPV,IAAA,UAAc,WAAd,cAAc,CAAA;AACE,IAAA,mBAAuB,WAAvB,uBAAuB,CAAA;AAChC,IAAA,oBAAyB,WAAzB,yBAAyB,CAAA;AACzB,IAAA,oBAAkC,WAAlC,kCAAkC,CAAA;AACvC,IAAA,eAAqC,WAArC,qCAAqC,CAAA;AACnC,IAAA,iBAAsB,WAAtB,sBAAsB,CAAA;AAExC,SAASA,eAAe,CACrCC,MAAc,EACdC,KAAe,EACfC,QAIC,EACDC,KAAqB,EACrBC,WAAqC,EACrCC,OAAkB,EAOlB;IACA,IAAIC,WAAW,GAAG,KAAK;IACvB,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIC,QAAQ,GAAGC,CAAAA,GAAAA,iBAAgB,AAAQ,CAAA,iBAAR,CAACT,MAAM,CAAC;IACvC,IAAIU,UAAU,GAAGC,CAAAA,GAAAA,oBAAmB,AAEnC,CAAA,oBAFmC,CAClCC,CAAAA,GAAAA,oBAAmB,AAA4C,CAAA,oBAA5C,CAACC,CAAAA,GAAAA,eAAc,AAAmB,CAAA,eAAnB,CAACL,QAAQ,CAACM,QAAQ,CAAC,EAAET,OAAO,CAAC,CAACS,QAAQ,CACzE;IACD,IAAIC,YAAY;IAEhB,MAAMC,aAAa,GAAG,CAACC,OAAgB,GAAK;QAC1C,MAAMC,OAAO,GAAGC,CAAAA,GAAAA,UAAY,AAM3B,CAAA,aAN2B,CAC1BF,OAAO,CAACG,MAAM,GAAG,CAACC,OAAO,CAACC,GAAG,CAACC,qBAAqB,GAAG,MAAM,GAAG,EAAE,CAAC,EAClE;YACEC,mBAAmB,EAAE,IAAI;YACzBC,MAAM,EAAE,IAAI;SACb,CACF;QAED,IAAIC,MAAM,GAAGR,OAAO,CAACV,QAAQ,CAACM,QAAQ,CAAC;QAEvC,IAAI,CAACG,OAAO,CAACU,GAAG,IAAIV,OAAO,CAACW,OAAO,CAAC,IAAIF,MAAM,EAAE;YAC9C,MAAMG,SAAS,GAAGC,CAAAA,GAAAA,mBAAQ,AAgBzB,CAAA,SAhByB,CACxB;gBACEC,OAAO,EAAE;oBACPC,IAAI,EAAEC,QAAQ,CAACC,QAAQ,CAACC,QAAQ;iBACjC;gBACDC,OAAO,EAAEH,QAAQ,CAACI,MAAM,CACrBC,KAAK,CAAC,IAAI,CAAC,CACXC,MAAM,CAAyB,CAACC,GAAG,EAAEC,IAAI,GAAK;oBAC7C,MAAM,CAACC,GAAG,EAAE,GAAGC,KAAK,CAAC,GAAGF,IAAI,CAACH,KAAK,CAAC,GAAG,CAAC;oBACvCE,GAAG,CAACE,GAAG,CAAC,GAAGC,KAAK,CAACC,IAAI,CAAC,GAAG,CAAC;oBAC1B,OAAOJ,GAAG,CAAA;iBACX,EAAE,EAAE,CAAC;aACT,EACDhC,QAAQ,CAACL,KAAK,EACdc,OAAO,CAACU,GAAG,EACXV,OAAO,CAACW,OAAO,CAChB;YAED,IAAIC,SAAS,EAAE;gBACbgB,MAAM,CAACC,MAAM,CAACpB,MAAM,EAAEG,SAAS,CAAC;aACjC,MAAM;gBACLH,MAAM,GAAG,KAAK;aACf;SACF;QAED,IAAIA,MAAM,EAAE;YACV,IAAI,CAACT,OAAO,CAAC8B,WAAW,EAAE;gBACxB,8DAA8D;gBAC9DxC,YAAY,GAAG,IAAI;gBACnB,OAAO,IAAI,CAAA;aACZ;YACD,MAAMyC,OAAO,GAAGC,CAAAA,GAAAA,mBAAkB,AAKhC,CAAA,mBALgC,CAAC;gBACjCC,mBAAmB,EAAE,IAAI;gBACzBH,WAAW,EAAE9B,OAAO,CAAC8B,WAAW;gBAChCrB,MAAM,EAAEA,MAAM;gBACdvB,KAAK,EAAEA,KAAK;aACb,CAAC;YACFK,QAAQ,GAAGwC,OAAO,CAACG,iBAAiB;YACpCnD,MAAM,GAAGgD,OAAO,CAACI,MAAM;YACvBP,MAAM,CAACC,MAAM,CAAC3C,KAAK,EAAE6C,OAAO,CAACG,iBAAiB,CAAChD,KAAK,CAAC;YAErDO,UAAU,GAAGC,CAAAA,GAAAA,oBAAmB,AAE/B,CAAA,oBAF+B,CAC9BC,CAAAA,GAAAA,oBAAmB,AAAiC,CAAA,oBAAjC,CAACC,CAAAA,GAAAA,eAAc,AAAQ,CAAA,eAAR,CAACb,MAAM,CAAC,EAAEK,OAAO,CAAC,CAACS,QAAQ,CAC9D;YAED,IAAIb,KAAK,CAACoD,QAAQ,CAAC3C,UAAU,CAAC,EAAE;gBAC9B,yDAAyD;gBACzD,yBAAyB;gBACzBJ,WAAW,GAAG,IAAI;gBAClBS,YAAY,GAAGL,UAAU;gBACzB,OAAO,IAAI,CAAA;aACZ;YAED,uEAAuE;YACvEK,YAAY,GAAGX,WAAW,CAACM,UAAU,CAAC;YAEtC,IAAIK,YAAY,KAAKf,MAAM,IAAIC,KAAK,CAACoD,QAAQ,CAACtC,YAAY,CAAC,EAAE;gBAC3DT,WAAW,GAAG,IAAI;gBAClB,OAAO,IAAI,CAAA;aACZ;SACF;KACF;IACD,IAAIgD,QAAQ,GAAG,KAAK;IAEpB,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,QAAQ,CAACsD,WAAW,CAACC,MAAM,EAAEF,CAAC,EAAE,CAAE;QACpD,mDAAmD;QACnD,8CAA8C;QAC9CvC,aAAa,CAACd,QAAQ,CAACsD,WAAW,CAACD,CAAC,CAAC,CAAC;KACvC;IACDjD,WAAW,GAAGL,KAAK,CAACoD,QAAQ,CAAC3C,UAAU,CAAC;IAExC,IAAI,CAACJ,WAAW,EAAE;QAChB,IAAI,CAACgD,QAAQ,EAAE;YACb,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,QAAQ,CAACwD,UAAU,CAACD,MAAM,EAAEF,CAAC,EAAE,CAAE;gBACnD,IAAIvC,aAAa,CAACd,QAAQ,CAACwD,UAAU,CAACH,CAAC,CAAC,CAAC,EAAE;oBACzCD,QAAQ,GAAG,IAAI;oBACf,MAAK;iBACN;aACF;SACF;QAED,0DAA0D;QAC1D,IAAI,CAACA,QAAQ,EAAE;YACbvC,YAAY,GAAGX,WAAW,CAACM,UAAU,CAAC;YACtCJ,WAAW,GAAGL,KAAK,CAACoD,QAAQ,CAACtC,YAAY,CAAC;YAC1CuC,QAAQ,GAAGhD,WAAW;SACvB;QAED,IAAI,CAACgD,QAAQ,EAAE;YACb,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,QAAQ,CAACyD,QAAQ,CAACF,MAAM,EAAEF,CAAC,EAAE,CAAE;gBACjD,IAAIvC,aAAa,CAACd,QAAQ,CAACyD,QAAQ,CAACJ,CAAC,CAAC,CAAC,EAAE;oBACvCD,QAAQ,GAAG,IAAI;oBACf,MAAK;iBACN;aACF;SACF;KACF;IAED,OAAO;QACLtD,MAAM;QACNQ,QAAQ;QACRF,WAAW;QACXS,YAAY;QACZR,YAAY;KACb,CAAA;CACF"}