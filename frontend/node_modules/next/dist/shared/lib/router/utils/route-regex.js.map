{"version": 3, "sources": ["../../../../../shared/lib/router/utils/route-regex.ts"], "names": ["getRouteRegex", "getNamedRouteRegex", "getNamedMiddlewareRegex", "parseParameter", "param", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getParametrizedRoute", "route", "segments", "removeTrailingSlash", "split", "groups", "groupIndex", "parameterizedRoute", "map", "segment", "pos", "escapeStringRegexp", "join", "normalizedRoute", "re", "RegExp", "buildGetSafeRouteKey", "routeKeyCharCode", "routeKeyCharLength", "routeKey", "i", "String", "fromCharCode", "getNamedParametrizedRoute", "getSafeRouteKey", "routeKeys", "namedParameterizedRoute", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "result", "namedRegex", "options", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": "AAAA;;;;QA0DgBA,aAAa,GAAbA,aAAa;QA8EbC,kBAAkB,GAAlBA,kBAAkB;QAalBC,uBAAuB,GAAvBA,uBAAuB;;AArJJ,IAAA,aAAqB,WAArB,qBAAqB,CAAA;AACpB,IAAA,oBAAyB,WAAzB,yBAAyB,CAAA;AAa7D;;;;;;GAMG,CACH,SAASC,cAAc,CAACC,KAAa,EAAE;IACrC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,UAAU,CAAC,GAAG,CAAC,IAAIF,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC;IAC7D,IAAIF,QAAQ,EAAE;QACZD,KAAK,GAAGA,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC3B;IACD,MAAMC,MAAM,GAAGL,KAAK,CAACE,UAAU,CAAC,KAAK,CAAC;IACtC,IAAIG,MAAM,EAAE;QACVL,KAAK,GAAGA,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC;KACvB;IACD,OAAO;QAAEE,GAAG,EAAEN,KAAK;QAAEK,MAAM;QAAEJ,QAAQ;KAAE,CAAA;CACxC;AAED,SAASM,oBAAoB,CAACC,KAAa,EAAE;IAC3C,MAAMC,QAAQ,GAAGC,CAAAA,GAAAA,oBAAmB,AAAO,CAAA,oBAAP,CAACF,KAAK,CAAC,CAACJ,KAAK,CAAC,CAAC,CAAC,CAACO,KAAK,CAAC,GAAG,CAAC;IAC/D,MAAMC,MAAM,GAAmC,EAAE;IACjD,IAAIC,UAAU,GAAG,CAAC;IAClB,OAAO;QACLC,kBAAkB,EAAEL,QAAQ,CACzBM,GAAG,CAAC,CAACC,OAAO,GAAK;YAChB,IAAIA,OAAO,CAACd,UAAU,CAAC,GAAG,CAAC,IAAIc,OAAO,CAACb,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACpD,MAAM,EAAEG,GAAG,CAAA,EAAEL,QAAQ,CAAA,EAAEI,MAAM,CAAA,EAAE,GAAGN,cAAc,CAACiB,OAAO,CAACZ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtEQ,MAAM,CAACN,GAAG,CAAC,GAAG;oBAAEW,GAAG,EAAEJ,UAAU,EAAE;oBAAER,MAAM;oBAAEJ,QAAQ;iBAAE;gBACrD,OAAOI,MAAM,GAAIJ,QAAQ,GAAG,aAAa,GAAG,QAAQ,GAAI,WAAW,CAAA;aACpE,MAAM;gBACL,OAAO,CAAC,CAAC,EAAEiB,CAAAA,GAAAA,aAAkB,AAAS,CAAA,mBAAT,CAACF,OAAO,CAAC,CAAC,CAAC,CAAA;aACzC;SACF,CAAC,CACDG,IAAI,CAAC,EAAE,CAAC;QACXP,MAAM;KACP,CAAA;CACF;AAOM,SAAShB,aAAa,CAACwB,eAAuB,EAAc;IACjE,MAAM,EAAEN,kBAAkB,CAAA,EAAEF,MAAM,CAAA,EAAE,GAAGL,oBAAoB,CAACa,eAAe,CAAC;IAC5E,OAAO;QACLC,EAAE,EAAE,IAAIC,MAAM,CAAC,CAAC,CAAC,EAAER,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC/CF,MAAM,EAAEA,MAAM;KACf,CAAA;CACF;AAED;;;GAGG,CACH,SAASW,oBAAoB,GAAG;IAC9B,IAAIC,gBAAgB,GAAG,EAAE;IACzB,IAAIC,kBAAkB,GAAG,CAAC;IAE1B,OAAO,IAAM;QACX,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,kBAAkB,EAAEE,CAAC,EAAE,CAAE;YAC3CD,QAAQ,IAAIE,MAAM,CAACC,YAAY,CAACL,gBAAgB,CAAC;YACjDA,gBAAgB,EAAE;YAElB,IAAIA,gBAAgB,GAAG,GAAG,EAAE;gBAC1BC,kBAAkB,EAAE;gBACpBD,gBAAgB,GAAG,EAAE;aACtB;SACF;QACD,OAAOE,QAAQ,CAAA;KAChB,CAAA;CACF;AAED,SAASI,yBAAyB,CAACtB,KAAa,EAAE;IAChD,MAAMC,QAAQ,GAAGC,CAAAA,GAAAA,oBAAmB,AAAO,CAAA,oBAAP,CAACF,KAAK,CAAC,CAACJ,KAAK,CAAC,CAAC,CAAC,CAACO,KAAK,CAAC,GAAG,CAAC;IAC/D,MAAMoB,eAAe,GAAGR,oBAAoB,EAAE;IAC9C,MAAMS,SAAS,GAAgC,EAAE;IACjD,OAAO;QACLC,uBAAuB,EAAExB,QAAQ,CAC9BM,GAAG,CAAC,CAACC,OAAO,GAAK;YAChB,IAAIA,OAAO,CAACd,UAAU,CAAC,GAAG,CAAC,IAAIc,OAAO,CAACb,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACpD,MAAM,EAAEG,GAAG,CAAA,EAAEL,QAAQ,CAAA,EAAEI,MAAM,CAAA,EAAE,GAAGN,cAAc,CAACiB,OAAO,CAACZ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtE,uDAAuD;gBACvD,kBAAkB;gBAClB,IAAI8B,UAAU,GAAG5B,GAAG,CAAC6B,OAAO,QAAQ,EAAE,CAAC;gBACvC,IAAIC,UAAU,GAAG,KAAK;gBAEtB,kEAAkE;gBAClE,WAAW;gBACX,IAAIF,UAAU,CAACG,MAAM,KAAK,CAAC,IAAIH,UAAU,CAACG,MAAM,GAAG,EAAE,EAAE;oBACrDD,UAAU,GAAG,IAAI;iBAClB;gBACD,IAAI,CAACE,KAAK,CAACC,QAAQ,CAACL,UAAU,CAAC9B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC5CgC,UAAU,GAAG,IAAI;iBAClB;gBAED,IAAIA,UAAU,EAAE;oBACdF,UAAU,GAAGH,eAAe,EAAE;iBAC/B;gBAEDC,SAAS,CAACE,UAAU,CAAC,GAAG5B,GAAG;gBAC3B,OAAOD,MAAM,GACTJ,QAAQ,GACN,CAAC,OAAO,EAAEiC,UAAU,CAAC,OAAO,CAAC,GAC7B,CAAC,IAAI,EAAEA,UAAU,CAAC,KAAK,CAAC,GAC1B,CAAC,IAAI,EAAEA,UAAU,CAAC,QAAQ,CAAC,CAAA;aAChC,MAAM;gBACL,OAAO,CAAC,CAAC,EAAEhB,CAAAA,GAAAA,aAAkB,AAAS,CAAA,mBAAT,CAACF,OAAO,CAAC,CAAC,CAAC,CAAA;aACzC;SACF,CAAC,CACDG,IAAI,CAAC,EAAE,CAAC;QACXa,SAAS;KACV,CAAA;CACF;AAOM,SAASnC,kBAAkB,CAACuB,eAAuB,EAAE;IAC1D,MAAMoB,MAAM,GAAGV,yBAAyB,CAACV,eAAe,CAAC;IACzD,OAAO,aACFxB,aAAa,CAACwB,eAAe,CAAC;QACjCqB,UAAU,EAAE,CAAC,CAAC,EAAED,MAAM,CAACP,uBAAuB,CAAC,OAAO,CAAC;QACvDD,SAAS,EAAEQ,MAAM,CAACR,SAAS;MAC5B,CAAA;CACF;AAMM,SAASlC,uBAAuB,CACrCsB,eAAuB,EACvBsB,OAEC,EACD;IACA,MAAM,EAAE5B,kBAAkB,CAAA,EAAE,GAAGP,oBAAoB,CAACa,eAAe,CAAC;IACpE,MAAM,EAAEuB,QAAQ,EAAG,IAAI,CAAA,EAAE,GAAGD,OAAO;IACnC,IAAI5B,kBAAkB,KAAK,GAAG,EAAE;QAC9B,IAAI8B,aAAa,GAAGD,QAAQ,GAAG,IAAI,GAAG,EAAE;QACxC,OAAO;YACLF,UAAU,EAAE,CAAC,EAAE,EAAEG,aAAa,CAAC,CAAC,CAAC;SAClC,CAAA;KACF;IAED,MAAM,EAAEX,uBAAuB,CAAA,EAAE,GAAGH,yBAAyB,CAACV,eAAe,CAAC;IAC9E,IAAIyB,oBAAoB,GAAGF,QAAQ,GAAG,YAAY,GAAG,EAAE;IACvD,OAAO;QACLF,UAAU,EAAE,CAAC,CAAC,EAAER,uBAAuB,CAAC,EAAEY,oBAAoB,CAAC,CAAC,CAAC;KAClE,CAAA;CACF"}