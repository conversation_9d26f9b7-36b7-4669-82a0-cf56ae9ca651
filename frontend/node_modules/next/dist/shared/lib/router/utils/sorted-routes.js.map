{"version": 3, "sources": ["../../../../../shared/lib/router/utils/sorted-routes.ts"], "names": ["getSortedRoutes", "UrlNode", "insert", "url<PERSON><PERSON>", "_insert", "split", "filter", "Boolean", "smoosh", "_smoosh", "prefix", "childrenPaths", "children", "keys", "sort", "slug<PERSON><PERSON>", "splice", "indexOf", "restSlugName", "optionalRestSlugName", "routes", "map", "c", "get", "reduce", "prev", "curr", "push", "placeholder", "r", "slice", "Error", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "length", "nextSegment", "startsWith", "endsWith", "segmentName", "isOptional", "substring", "handleSlug", "previousSlug", "nextSlug", "for<PERSON>ach", "slug", "replace", "has", "set", "Map", "normalizedPages", "root", "pagePath"], "mappings": "AAAA;;;;QAqMgBA,eAAe,GAAfA,eAAe;AArM/B,MAAMC,OAAO;IAOXC,MAAM,CAACC,OAAe,EAAQ;QAC5B,IAAI,CAACC,OAAO,CAACD,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC;KAC5D;IAEDC,MAAM,GAAa;QACjB,OAAO,IAAI,CAACC,OAAO,EAAE,CAAA;KACtB;IAED,AAAQA,OAAO,CAACC,MAAc,GAAG,GAAG,EAAY;QAC9C,MAAMC,aAAa,GAAG;eAAI,IAAI,CAACC,QAAQ,CAACC,IAAI,EAAE;SAAC,CAACC,IAAI,EAAE;QACtD,IAAI,IAAI,CAACC,QAAQ,KAAK,IAAI,EAAE;YAC1BJ,aAAa,CAACK,MAAM,CAACL,aAAa,CAACM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACrD;QACD,IAAI,IAAI,CAACC,YAAY,KAAK,IAAI,EAAE;YAC9BP,aAAa,CAACK,MAAM,CAACL,aAAa,CAACM,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SACxD;QACD,IAAI,IAAI,CAACE,oBAAoB,KAAK,IAAI,EAAE;YACtCR,aAAa,CAACK,MAAM,CAACL,aAAa,CAACM,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;SAC1D;QAED,MAAMG,MAAM,GAAGT,aAAa,CACzBU,GAAG,CAAC,CAACC,CAAC,GAAK,IAAI,CAACV,QAAQ,CAACW,GAAG,CAACD,CAAC,CAAC,CAAEb,OAAO,CAAC,CAAC,EAAEC,MAAM,CAAC,EAAEY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC3DE,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,GAAK;mBAAID,IAAI;mBAAKC,IAAI;aAAC,EAAE,EAAE,CAAC;QAEjD,IAAI,IAAI,CAACX,QAAQ,KAAK,IAAI,EAAE;YAC1BK,MAAM,CAACO,IAAI,IACN,IAAI,CAACf,QAAQ,CAACW,GAAG,CAAC,IAAI,CAAC,CAAEd,OAAO,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC,EAAE,IAAI,CAACK,QAAQ,CAAC,EAAE,CAAC,CAAC,CACpE;SACF;QAED,IAAI,CAAC,IAAI,CAACa,WAAW,EAAE;YACrB,MAAMC,CAAC,GAAGnB,MAAM,KAAK,GAAG,GAAG,GAAG,GAAGA,MAAM,CAACoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,IAAI,IAAI,CAACX,oBAAoB,IAAI,IAAI,EAAE;gBACrC,MAAM,IAAIY,KAAK,CACb,CAAC,oFAAoF,EAAEF,CAAC,CAAC,OAAO,EAAEA,CAAC,CAAC,KAAK,EAAE,IAAI,CAACV,oBAAoB,CAAC,KAAK,CAAC,CAC5I,CAAA;aACF;YAEDC,MAAM,CAACY,OAAO,CAACH,CAAC,CAAC;SAClB;QAED,IAAI,IAAI,CAACX,YAAY,KAAK,IAAI,EAAE;YAC9BE,MAAM,CAACO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,OAAO,CAAC,CACZd,OAAO,CAAC,CAAC,EAAEC,MAAM,CAAC,IAAI,EAAE,IAAI,CAACQ,YAAY,CAAC,EAAE,CAAC,CAAC,CAClD;SACF;QAED,IAAI,IAAI,CAACC,oBAAoB,KAAK,IAAI,EAAE;YACtCC,MAAM,CAACO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,SAAS,CAAC,CACdd,OAAO,CAAC,CAAC,EAAEC,MAAM,CAAC,KAAK,EAAE,IAAI,CAACS,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAC5D;SACF;QAED,OAAOC,MAAM,CAAA;KACd;IAED,AAAQhB,OAAO,CACb6B,QAAkB,EAClBC,SAAmB,EACnBC,UAAmB,EACb;QACN,IAAIF,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;YACzB,IAAI,CAACR,WAAW,GAAG,KAAK;YACxB,OAAM;SACP;QAED,IAAIO,UAAU,EAAE;YACd,MAAM,IAAIJ,KAAK,CAAC,CAAC,2CAA2C,CAAC,CAAC,CAAA;SAC/D;QAED,wCAAwC;QACxC,IAAIM,WAAW,GAAGJ,QAAQ,CAAC,CAAC,CAAC;QAE7B,6CAA6C;QAC7C,IAAII,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,IAAID,WAAW,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC5D,8CAA8C;YAC9C,IAAIC,WAAW,GAAGH,WAAW,CAACP,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAE1C,IAAIW,UAAU,GAAG,KAAK;YACtB,IAAID,WAAW,CAACF,UAAU,CAAC,GAAG,CAAC,IAAIE,WAAW,CAACD,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC5D,uDAAuD;gBACvDC,WAAW,GAAGA,WAAW,CAACV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtCW,UAAU,GAAG,IAAI;aAClB;YAED,IAAID,WAAW,CAACF,UAAU,CAAC,KAAK,CAAC,EAAE;gBACjC,wCAAwC;gBACxCE,WAAW,GAAGA,WAAW,CAACE,SAAS,CAAC,CAAC,CAAC;gBACtCP,UAAU,GAAG,IAAI;aAClB;YAED,IAAIK,WAAW,CAACF,UAAU,CAAC,GAAG,CAAC,IAAIE,WAAW,CAACD,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC5D,MAAM,IAAIR,KAAK,CACb,CAAC,yDAAyD,EAAES,WAAW,CAAC,GAAG,CAAC,CAC7E,CAAA;aACF;YAED,IAAIA,WAAW,CAACF,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC/B,MAAM,IAAIP,KAAK,CACb,CAAC,qDAAqD,EAAES,WAAW,CAAC,GAAG,CAAC,CACzE,CAAA;aACF;YAED,SAASG,UAAU,CAACC,YAA2B,EAAEC,QAAgB,EAAE;gBACjE,IAAID,YAAY,KAAK,IAAI,EAAE;oBACzB,6EAA6E;oBAC7E,iCAAiC;oBACjC,wBAAwB;oBACxB,sBAAsB;oBACtB,wFAAwF;oBACxF,IAAIA,YAAY,KAAKC,QAAQ,EAAE;wBAC7B,wHAAwH;wBACxH,MAAM,IAAId,KAAK,CACb,CAAC,gEAAgE,EAAEa,YAAY,CAAC,OAAO,EAAEC,QAAQ,CAAC,GAAG,CAAC,CACvG,CAAA;qBACF;iBACF;gBAEDX,SAAS,CAACY,OAAO,CAAC,CAACC,IAAI,GAAK;oBAC1B,IAAIA,IAAI,KAAKF,QAAQ,EAAE;wBACrB,MAAM,IAAId,KAAK,CACb,CAAC,oCAAoC,EAAEc,QAAQ,CAAC,qCAAqC,CAAC,CACvF,CAAA;qBACF;oBAED,IAAIE,IAAI,CAACC,OAAO,QAAQ,EAAE,CAAC,KAAKX,WAAW,CAACW,OAAO,QAAQ,EAAE,CAAC,EAAE;wBAC9D,MAAM,IAAIjB,KAAK,CACb,CAAC,gCAAgC,EAAEgB,IAAI,CAAC,OAAO,EAAEF,QAAQ,CAAC,8DAA8D,CAAC,CAC1H,CAAA;qBACF;iBACF,CAAC;gBAEFX,SAAS,CAACP,IAAI,CAACkB,QAAQ,CAAC;aACzB;YAED,IAAIV,UAAU,EAAE;gBACd,IAAIM,UAAU,EAAE;oBACd,IAAI,IAAI,CAACvB,YAAY,IAAI,IAAI,EAAE;wBAC7B,MAAM,IAAIa,KAAK,CACb,CAAC,qFAAqF,EAAE,IAAI,CAACb,YAAY,CAAC,QAAQ,EAAEe,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CACtI,CAAA;qBACF;oBAEDU,UAAU,CAAC,IAAI,CAACxB,oBAAoB,EAAEqB,WAAW,CAAC;oBAClD,6DAA6D;oBAC7D,IAAI,CAACrB,oBAAoB,GAAGqB,WAAW;oBACvC,oFAAoF;oBACpFH,WAAW,GAAG,SAAS;iBACxB,MAAM;oBACL,IAAI,IAAI,CAAClB,oBAAoB,IAAI,IAAI,EAAE;wBACrC,MAAM,IAAIY,KAAK,CACb,CAAC,sFAAsF,EAAE,IAAI,CAACZ,oBAAoB,CAAC,SAAS,EAAEc,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAC/I,CAAA;qBACF;oBAEDU,UAAU,CAAC,IAAI,CAACzB,YAAY,EAAEsB,WAAW,CAAC;oBAC1C,6DAA6D;oBAC7D,IAAI,CAACtB,YAAY,GAAGsB,WAAW;oBAC/B,kFAAkF;oBAClFH,WAAW,GAAG,OAAO;iBACtB;aACF,MAAM;gBACL,IAAII,UAAU,EAAE;oBACd,MAAM,IAAIV,KAAK,CACb,CAAC,kDAAkD,EAAEE,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CACtE,CAAA;iBACF;gBACDU,UAAU,CAAC,IAAI,CAAC5B,QAAQ,EAAEyB,WAAW,CAAC;gBACtC,6DAA6D;gBAC7D,IAAI,CAACzB,QAAQ,GAAGyB,WAAW;gBAC3B,+EAA+E;gBAC/EH,WAAW,GAAG,IAAI;aACnB;SACF;QAED,iFAAiF;QACjF,IAAI,CAAC,IAAI,CAACzB,QAAQ,CAACqC,GAAG,CAACZ,WAAW,CAAC,EAAE;YACnC,IAAI,CAACzB,QAAQ,CAACsC,GAAG,CAACb,WAAW,EAAE,IAAIpC,OAAO,EAAE,CAAC;SAC9C;QAED,IAAI,CAACW,QAAQ,CACVW,GAAG,CAACc,WAAW,CAAC,CAChBjC,OAAO,CAAC6B,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,EAAEI,SAAS,EAAEC,UAAU,CAAC;KACrD;;QAjMDP,KAAAA,WAAW,GAAY,IAAI,CAAA;QAC3BhB,KAAAA,QAAQ,GAAyB,IAAIuC,GAAG,EAAE,CAAA;QAC1CpC,KAAAA,QAAQ,GAAkB,IAAI,CAAA;QAC9BG,KAAAA,YAAY,GAAkB,IAAI,CAAA;QAClCC,KAAAA,oBAAoB,GAAkB,IAAI,CAAA;;CA8L3C;AAEM,SAASnB,eAAe,CAC7BoD,eAAsC,EAC5B;IACV,kFAAkF;IAClF,4EAA4E;IAC5E,2CAA2C;IAE3C,yEAAyE;IACzE,2BAA2B;IAC3B,oCAAoC;IACpC,8EAA8E;IAC9E,wEAAwE;IACxE,gHAAgH;IAChH,4EAA4E;IAC5E,MAAMC,IAAI,GAAG,IAAIpD,OAAO,EAAE;IAE1B,6FAA6F;IAC7FmD,eAAe,CAACN,OAAO,CAAC,CAACQ,QAAQ,GAAKD,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC;IAC5D,4GAA4G;IAC5G,OAAOD,IAAI,CAAC7C,MAAM,EAAE,CAAA;CACrB"}