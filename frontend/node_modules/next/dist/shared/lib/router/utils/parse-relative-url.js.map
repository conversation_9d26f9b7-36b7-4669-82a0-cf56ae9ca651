{"version": 3, "sources": ["../../../../../shared/lib/router/utils/parse-relative-url.ts"], "names": ["parseRelativeUrl", "url", "base", "globalBase", "URL", "window", "getLocationOrigin", "resolvedBase", "startsWith", "location", "href", "pathname", "searchParams", "search", "hash", "origin", "Error", "query", "searchParamsToUrlQuery", "slice", "length"], "mappings": "AAAA;;;;QAkBgBA,gBAAgB,GAAhBA,gBAAgB;AAjBE,IAAA,MAAa,WAAb,aAAa,CAAA;AACR,IAAA,YAAe,WAAf,eAAe,CAAA;AAgB/C,SAASA,gBAAgB,CAC9BC,GAAW,EACXC,IAAa,EACM;IACnB,MAAMC,UAAU,GAAG,IAAIC,GAAG,CACxB,OAAOC,MAAM,KAAK,WAAW,GAAG,UAAU,GAAGC,CAAAA,GAAAA,MAAiB,AAAE,CAAA,kBAAF,EAAE,CACjE;IAED,MAAMC,YAAY,GAAGL,IAAI,GACrB,IAAIE,GAAG,CAACF,IAAI,EAAEC,UAAU,CAAC,GACzBF,GAAG,CAACO,UAAU,CAAC,GAAG,CAAC,GACnB,IAAIJ,GAAG,CAAC,OAAOC,MAAM,KAAK,WAAW,GAAG,UAAU,GAAGA,MAAM,CAACI,QAAQ,CAACC,IAAI,CAAC,GAC1EP,UAAU;IAEd,MAAM,EAAEQ,QAAQ,CAAA,EAAEC,YAAY,CAAA,EAAEC,MAAM,CAAA,EAAEC,IAAI,CAAA,EAAEJ,IAAI,CAAA,EAAEK,MAAM,CAAA,EAAE,GAAG,IAAIX,GAAG,CACpEH,GAAG,EACHM,YAAY,CACb;IACD,IAAIQ,MAAM,KAAKZ,UAAU,CAACY,MAAM,EAAE;QAChC,MAAM,IAAIC,KAAK,CAAC,CAAC,iDAAiD,EAAEf,GAAG,CAAC,CAAC,CAAC,CAAA;KAC3E;IACD,OAAO;QACLU,QAAQ;QACRM,KAAK,EAAEC,CAAAA,GAAAA,YAAsB,AAAc,CAAA,uBAAd,CAACN,YAAY,CAAC;QAC3CC,MAAM;QACNC,IAAI;QACJJ,IAAI,EAAEA,IAAI,CAACS,KAAK,CAAChB,UAAU,CAACY,MAAM,CAACK,MAAM,CAAC;KAC3C,CAAA;CACF"}