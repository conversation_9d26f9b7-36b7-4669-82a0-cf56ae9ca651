{"version": 3, "sources": ["../../../../../shared/lib/router/utils/path-match.ts"], "names": ["getPathMatch", "path", "options", "keys", "regexp", "pathToRegexp", "delimiter", "sensitive", "strict", "matcher", "regexpToFunction", "regexModifier", "RegExp", "source", "flags", "pathname", "params", "res", "removeUnnamedP<PERSON>ms", "key", "name"], "mappings": "AAAA;;;;QA2BgBA,YAAY,GAAZA,YAAY;;AA1BC,IAAA,aAAmC,WAAnC,mCAAmC,CAAA;AA0BzD,SAASA,YAAY,CAACC,IAAY,EAAEC,OAAiB,EAAE;IAC5D,MAAMC,IAAI,GAAU,EAAE;IACtB,MAAMC,MAAM,GAAGC,CAAAA,GAAAA,aAAY,AAIzB,CAAA,aAJyB,CAACJ,IAAI,EAAEE,IAAI,EAAE;QACtCG,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,KAAK;QAChBC,MAAM,EAAEN,OAAO,QAAQ,GAAfA,KAAAA,CAAe,GAAfA,OAAO,CAAEM,MAAM;KACxB,CAAC;IAEF,MAAMC,OAAO,GAAGC,CAAAA,GAAAA,aAAgB,AAK/B,CAAA,iBAL+B,CAC9BR,CAAAA,OAAO,QAAe,GAAtBA,KAAAA,CAAsB,GAAtBA,OAAO,CAAES,aAAa,CAAA,GAClB,IAAIC,MAAM,CAACV,OAAO,CAACS,aAAa,CAACP,MAAM,CAACS,MAAM,CAAC,EAAET,MAAM,CAACU,KAAK,CAAC,GAC9DV,MAAM,EACVD,IAAI,CACL;IAED;;;;;KAKG,CACH,OAAO,CACLY,QAAwB,EACxBC,MAAY,GACE;QACd,MAAMC,GAAG,GAAGF,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAGN,OAAO,CAACM,QAAQ,CAAC;QACxD,IAAI,CAACE,GAAG,EAAE;YACR,OAAO,KAAK,CAAA;SACb;QAED;;;;OAIG,CACH,IAAIf,OAAO,QAAqB,GAA5BA,KAAAA,CAA4B,GAA5BA,OAAO,CAAEgB,mBAAmB,EAAE;YAChC,KAAK,MAAMC,GAAG,IAAIhB,IAAI,CAAE;gBACtB,IAAI,OAAOgB,GAAG,CAACC,IAAI,KAAK,QAAQ,EAAE;oBAChC,OAAO,AAACH,GAAG,CAACD,MAAM,AAAQ,CAACG,GAAG,CAACC,IAAI,CAAC;iBACrC;aACF;SACF;QAED,OAAO,aAAKJ,MAAM,EAAKC,GAAG,CAACD,MAAM,CAAE,CAAA;KACpC,CAAA;CACF"}