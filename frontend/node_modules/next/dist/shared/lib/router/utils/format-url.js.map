{"version": 3, "sources": ["../../../../../shared/lib/router/utils/format-url.ts"], "names": ["formatUrl", "formatWithValidation", "querystring", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "urlObjectKeys", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "console", "warn"], "mappings": "AAsBA;;;;QAMgBA,SAAS,GAATA,SAAS;QA6DTC,oBAAoB,GAApBA,oBAAoB;;;AAjExBC,IAAAA,WAAW,qCAAM,eAAe,EAArB;AAEvB,MAAMC,gBAAgB,2BAA2B;AAE1C,SAASH,SAAS,CAACI,MAAiB,EAAE;IAC3C,IAAI,EAAEC,IAAI,CAAA,EAAEC,QAAQ,CAAA,EAAE,GAAGF,MAAM;IAC/B,IAAIG,QAAQ,GAAGH,MAAM,CAACG,QAAQ,IAAI,EAAE;IACpC,IAAIC,QAAQ,GAAGJ,MAAM,CAACI,QAAQ,IAAI,EAAE;IACpC,IAAIC,IAAI,GAAGL,MAAM,CAACK,IAAI,IAAI,EAAE;IAC5B,IAAIC,KAAK,GAAGN,MAAM,CAACM,KAAK,IAAI,EAAE;IAC9B,IAAIC,IAAI,GAAmB,KAAK;IAEhCN,IAAI,GAAGA,IAAI,GAAGO,kBAAkB,CAACP,IAAI,CAAC,CAACQ,OAAO,SAAS,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;IAEtE,IAAIT,MAAM,CAACO,IAAI,EAAE;QACfA,IAAI,GAAGN,IAAI,GAAGD,MAAM,CAACO,IAAI;KAC1B,MAAM,IAAIL,QAAQ,EAAE;QACnBK,IAAI,GAAGN,IAAI,GAAG,CAAC,CAACC,QAAQ,CAACQ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAER,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC;QACnE,IAAIF,MAAM,CAACW,IAAI,EAAE;YACfJ,IAAI,IAAI,GAAG,GAAGP,MAAM,CAACW,IAAI;SAC1B;KACF;IAED,IAAIL,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACtCA,KAAK,GAAGM,MAAM,CAACd,WAAW,CAACe,sBAAsB,CAACP,KAAK,CAAmB,CAAC;KAC5E;IAED,IAAIQ,MAAM,GAAGd,MAAM,CAACc,MAAM,IAAKR,KAAK,IAAI,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,IAAK,EAAE;IAE1D,IAAIH,QAAQ,IAAI,CAACA,QAAQ,CAACY,QAAQ,CAAC,GAAG,CAAC,EAAEZ,QAAQ,IAAI,GAAG;IAExD,IACEH,MAAM,CAACgB,OAAO,IACb,CAAC,CAACb,QAAQ,IAAIJ,gBAAgB,CAACkB,IAAI,CAACd,QAAQ,CAAC,CAAC,IAAII,IAAI,KAAK,KAAK,AAAC,EAClE;QACAA,IAAI,GAAG,IAAI,GAAG,CAACA,IAAI,IAAI,EAAE,CAAC;QAC1B,IAAIH,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEA,QAAQ,GAAG,GAAG,GAAGA,QAAQ;KAC/D,MAAM,IAAI,CAACG,IAAI,EAAE;QAChBA,IAAI,GAAG,EAAE;KACV;IAED,IAAIF,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEA,IAAI,GAAG,GAAG,GAAGA,IAAI;IAC9C,IAAIS,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEA,MAAM,GAAG,GAAG,GAAGA,MAAM;IAEtDV,QAAQ,GAAGA,QAAQ,CAACK,OAAO,UAAUD,kBAAkB,CAAC;IACxDM,MAAM,GAAGA,MAAM,CAACL,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;IAEnC,OAAO,CAAC,EAAEN,QAAQ,CAAC,EAAEI,IAAI,CAAC,EAAEH,QAAQ,CAAC,EAAEU,MAAM,CAAC,EAAET,IAAI,CAAC,CAAC,CAAA;CACvD;AAEM,MAAMa,aAAa,GAAG;IAC3B,MAAM;IACN,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,UAAU;IACV,OAAO;IACP,QAAQ;IACR,SAAS;CACV;QAbYA,aAAa,GAAbA,aAAa;AAenB,SAASrB,oBAAoB,CAACsB,GAAc,EAAU;IAC3D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1C,IAAIH,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YAC3CI,MAAM,CAACC,IAAI,CAACL,GAAG,CAAC,CAACM,OAAO,CAAC,CAACC,GAAG,GAAK;gBAChC,IAAIR,aAAa,CAACR,OAAO,CAACgB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;oBACrCC,OAAO,CAACC,IAAI,CACV,CAAC,kDAAkD,EAAEF,GAAG,CAAC,CAAC,CAC3D;iBACF;aACF,CAAC;SACH;KACF;IAED,OAAO9B,SAAS,CAACuB,GAAG,CAAC,CAAA;CACtB"}