{"version": 3, "sources": ["../../../../../shared/lib/router/utils/parse-path.ts"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAKA;;;;QAAgBA,SAAS,GAATA,SAAS;AAAlB,SAASA,SAAS,CAACC,IAAY,EAAE;IACtC,MAAMC,SAAS,GAAGD,IAAI,CAACE,OAAO,CAAC,GAAG,CAAC;IACnC,MAAMC,UAAU,GAAGH,IAAI,CAACE,OAAO,CAAC,GAAG,CAAC;IACpC,MAAME,QAAQ,GAAGD,UAAU,GAAG,CAAC,CAAC,IAAI,CAACF,SAAS,GAAG,CAAC,IAAIE,UAAU,GAAGF,SAAS,CAAC;IAE7E,IAAIG,QAAQ,IAAIH,SAAS,GAAG,CAAC,CAAC,EAAE;QAC9B,OAAO;YACLI,QAAQ,EAAEL,IAAI,CAACM,SAAS,CAAC,CAAC,EAAEF,QAAQ,GAAGD,UAAU,GAAGF,SAAS,CAAC;YAC9DM,KAAK,EAAEH,QAAQ,GACXJ,IAAI,CAACM,SAAS,CAACH,UAAU,EAAEF,SAAS,GAAG,CAAC,CAAC,GAAGA,SAAS,GAAGO,SAAS,CAAC,GAClE,EAAE;YACNC,IAAI,EAAER,SAAS,GAAG,CAAC,CAAC,GAAGD,IAAI,CAACU,KAAK,CAACT,SAAS,CAAC,GAAG,EAAE;SAClD,CAAA;KACF;IAED,OAAO;QAAEI,QAAQ,EAAEL,IAAI;QAAEO,KAAK,EAAE,EAAE;QAAEE,IAAI,EAAE,EAAE;KAAE,CAAA;CAC/C"}