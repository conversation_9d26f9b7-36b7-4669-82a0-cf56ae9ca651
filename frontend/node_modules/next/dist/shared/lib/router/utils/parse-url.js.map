{"version": 3, "sources": ["../../../../../shared/lib/router/utils/parse-url.ts"], "names": ["parseUrl", "url", "startsWith", "parseRelativeUrl", "parsedURL", "URL", "hash", "hostname", "href", "pathname", "port", "protocol", "query", "searchParamsToUrlQuery", "searchParams", "search"], "mappings": "AAAA;;;;QAgBgBA,QAAQ,GAARA,QAAQ;AAde,IAAA,YAAe,WAAf,eAAe,CAAA;AACrB,IAAA,iBAAsB,WAAtB,sBAAsB,CAAA;AAahD,SAASA,QAAQ,CAACC,GAAW,EAAa;IAC/C,IAAIA,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;QACvB,OAAOC,CAAAA,GAAAA,iBAAgB,AAAK,CAAA,iBAAL,CAACF,GAAG,CAAC,CAAA;KAC7B;IAED,MAAMG,SAAS,GAAG,IAAIC,GAAG,CAACJ,GAAG,CAAC;IAC9B,OAAO;QACLK,IAAI,EAAEF,SAAS,CAACE,IAAI;QACpBC,QAAQ,EAAEH,SAAS,CAACG,QAAQ;QAC5BC,IAAI,EAAEJ,SAAS,CAACI,IAAI;QACpBC,QAAQ,EAAEL,SAAS,CAACK,QAAQ;QAC5BC,IAAI,EAAEN,SAAS,CAACM,IAAI;QACpBC,QAAQ,EAAEP,SAAS,CAACO,QAAQ;QAC5BC,KAAK,EAAEC,CAAAA,GAAAA,YAAsB,AAAwB,CAAA,uBAAxB,CAACT,SAAS,CAACU,YAAY,CAAC;QACrDC,MAAM,EAAEX,SAAS,CAACW,MAAM;KACzB,CAAA;CACF"}