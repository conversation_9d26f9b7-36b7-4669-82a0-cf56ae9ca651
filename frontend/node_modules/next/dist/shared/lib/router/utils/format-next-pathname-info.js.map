{"version": 3, "sources": ["../../../../../shared/lib/router/utils/format-next-pathname-info.ts"], "names": ["formatNextPathnameInfo", "info", "pathname", "addLocale", "locale", "buildId", "undefined", "defaultLocale", "ignorePrefix", "addPathSuffix", "addPathPrefix", "basePath", "trailingSlash", "endsWith", "removeTrailingSlash"], "mappings": "AAAA;;;;QAWgBA,sBAAsB,GAAtBA,sBAAsB;AAVF,IAAA,oBAAyB,WAAzB,yBAAyB,CAAA;AAC/B,IAAA,cAAmB,WAAnB,mBAAmB,CAAA;AACnB,IAAA,cAAmB,WAAnB,mBAAmB,CAAA;AACvB,IAAA,UAAc,WAAd,cAAc,CAAA;AAOjC,SAASA,sBAAsB,CAACC,IAAkB,EAAE;IACzD,IAAIC,QAAQ,GAAGC,CAAAA,GAAAA,UAAS,AAKvB,CAAA,UALuB,CACtBF,IAAI,CAACC,QAAQ,EACbD,IAAI,CAACG,MAAM,EACXH,IAAI,CAACI,OAAO,GAAGC,SAAS,GAAGL,IAAI,CAACM,aAAa,EAC7CN,IAAI,CAACO,YAAY,CAClB;IAED,IAAIP,IAAI,CAACI,OAAO,EAAE;QAChBH,QAAQ,GAAGO,CAAAA,GAAAA,cAAa,AAGvB,CAAA,cAHuB,CACtBC,CAAAA,GAAAA,cAAa,AAAyC,CAAA,cAAzC,CAACR,QAAQ,EAAE,CAAC,YAAY,EAAED,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,EACtDJ,IAAI,CAACC,QAAQ,KAAK,GAAG,GAAG,YAAY,GAAG,OAAO,CAC/C;KACF;IAEDA,QAAQ,GAAGQ,CAAAA,GAAAA,cAAa,AAAyB,CAAA,cAAzB,CAACR,QAAQ,EAAED,IAAI,CAACU,QAAQ,CAAC;IACjD,OAAOV,IAAI,CAACW,aAAa,GACrB,CAACX,IAAI,CAACI,OAAO,IAAI,CAACH,QAAQ,CAACW,QAAQ,CAAC,GAAG,CAAC,GACtCJ,CAAAA,GAAAA,cAAa,AAAe,CAAA,cAAf,CAACP,QAAQ,EAAE,GAAG,CAAC,GAC5BA,QAAQ,GACVY,CAAAA,GAAAA,oBAAmB,AAAU,CAAA,oBAAV,CAACZ,QAAQ,CAAC,CAAA;CAClC"}