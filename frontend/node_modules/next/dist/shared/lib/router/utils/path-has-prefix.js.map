{"version": 3, "sources": ["../../../../../shared/lib/router/utils/path-has-prefix.ts"], "names": ["pathHasPrefix", "path", "prefix", "pathname", "parsePath", "startsWith"], "mappings": "AAAA;;;;QASgBA,aAAa,GAAbA,aAAa;AATH,IAAA,UAAc,WAAd,cAAc,CAAA;AASjC,SAASA,aAAa,CAACC,IAAY,EAAEC,MAAc,EAAE;IAC1D,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO,KAAK,CAAA;KACb;IAED,MAAM,EAAEE,QAAQ,CAAA,EAAE,GAAGC,CAAAA,GAAAA,UAAS,AAAM,CAAA,UAAN,CAACH,IAAI,CAAC;IACpC,OAAOE,QAAQ,KAAKD,MAAM,IAAIC,QAAQ,CAACE,UAAU,CAACH,MAAM,GAAG,GAAG,CAAC,CAAA;CAChE"}