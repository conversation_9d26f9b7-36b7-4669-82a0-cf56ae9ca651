{"version": 3, "sources": ["../../../../../shared/lib/router/utils/compare-states.ts"], "names": ["compareRouterStates", "a", "b", "stateKeys", "Object", "keys", "length", "i", "key", "query<PERSON>eys", "query", "j", "query<PERSON><PERSON>", "hasOwnProperty"], "mappings": "AAAA;;;;QAEgBA,mBAAmB,GAAnBA,mBAAmB;AAA5B,SAASA,mBAAmB,CAACC,CAAkB,EAAEC,CAAkB,EAAE;IAC1E,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;IAChC,IAAIE,SAAS,CAACG,MAAM,KAAKF,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC,CAACI,MAAM,EAAE,OAAO,KAAK,CAAA;IAE5D,IAAK,IAAIC,CAAC,GAAGJ,SAAS,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAI;QACpC,MAAMC,GAAG,GAAGL,SAAS,CAACI,CAAC,CAAC;QACxB,IAAIC,GAAG,KAAK,OAAO,EAAE;YACnB,MAAMC,SAAS,GAAGL,MAAM,CAACC,IAAI,CAACJ,CAAC,CAACS,KAAK,CAAC;YACtC,IAAID,SAAS,CAACH,MAAM,KAAKF,MAAM,CAACC,IAAI,CAACH,CAAC,CAACQ,KAAK,CAAC,CAACJ,MAAM,EAAE;gBACpD,OAAO,KAAK,CAAA;aACb;YACD,IAAK,IAAIK,CAAC,GAAGF,SAAS,CAACH,MAAM,EAAEK,CAAC,EAAE,EAAI;gBACpC,MAAMC,QAAQ,GAAGH,SAAS,CAACE,CAAC,CAAC;gBAC7B,IACE,CAACT,CAAC,CAACQ,KAAK,CAACG,cAAc,CAACD,QAAQ,CAAC,IACjCX,CAAC,CAACS,KAAK,CAACE,QAAQ,CAAC,KAAKV,CAAC,CAACQ,KAAK,CAACE,QAAQ,CAAC,EACvC;oBACA,OAAO,KAAK,CAAA;iBACb;aACF;SACF,MAAM,IACL,CAACV,CAAC,CAACW,cAAc,CAACL,GAAG,CAAC,IACtBP,CAAC,CAACO,GAAG,CAA0B,KAAKN,CAAC,CAACM,GAAG,CAA0B,EACnE;YACA,OAAO,KAAK,CAAA;SACb;KACF;IAED,OAAO,IAAI,CAAA;CACZ"}