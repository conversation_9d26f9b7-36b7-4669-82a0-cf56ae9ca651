{"version": 3, "sources": ["../../../../../shared/lib/router/utils/add-path-prefix.ts"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": "AAAA;;;;QAMgBA,aAAa,GAAbA,aAAa;AANH,IAAA,UAAc,WAAd,cAAc,CAAA;AAMjC,SAASA,aAAa,CAACC,IAAY,EAAEC,MAAe,EAAE;IAC3D,IAAI,CAACD,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,IAAI,CAACD,MAAM,EAAE;QACpC,OAAOD,IAAI,CAAA;KACZ;IAED,MAAM,EAAEG,QAAQ,CAAA,EAAEC,KAAK,CAAA,EAAEC,IAAI,CAAA,EAAE,GAAGC,CAAAA,GAAAA,UAAS,AAAM,CAAA,UAAN,CAACN,IAAI,CAAC;IACjD,OAAO,CAAC,EAAEC,MAAM,CAAC,EAAEE,QAAQ,CAAC,EAAEC,KAAK,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAA;CAC7C"}