{"version": 3, "sources": ["../../../../../shared/lib/router/utils/get-next-pathname-info.ts"], "names": ["getNextPathnameInfo", "pathname", "options", "basePath", "i18n", "trailingSlash", "nextConfig", "info", "endsWith", "pathHasPrefix", "removePathPrefix", "parseData", "startsWith", "paths", "replace", "split", "buildId", "slice", "join", "pathLocale", "normalizeLocalePath", "locales", "locale", "detectedLocale"], "mappings": "AAAA;;;;QA6CgBA,mBAAmB,GAAnBA,mBAAmB;AA7CC,IAAA,oBAAkC,WAAlC,kCAAkC,CAAA;AACrC,IAAA,iBAAsB,WAAtB,sBAAsB,CAAA;AACzB,IAAA,cAAmB,WAAnB,mBAAmB,CAAA;AA2C1C,SAASA,mBAAmB,CAACC,QAAgB,EAAEC,OAAgB,EAAE;QAC5BA,WAAkB;IAA5D,MAAM,EAAEC,QAAQ,CAAA,EAAEC,IAAI,CAAA,EAAEC,aAAa,CAAA,EAAE,GAAGH,CAAAA,WAAkB,GAAlBA,OAAO,CAACI,UAAU,YAAlBJ,WAAkB,GAAI,EAAE;IAClE,MAAMK,IAAI,GAAqB;QAC7BN,QAAQ,EAAEA,QAAQ;QAClBI,aAAa,EAAEJ,QAAQ,KAAK,GAAG,GAAGA,QAAQ,CAACO,QAAQ,CAAC,GAAG,CAAC,GAAGH,aAAa;KACzE;IAED,IAAIF,QAAQ,IAAIM,CAAAA,GAAAA,cAAa,AAAyB,CAAA,cAAzB,CAACF,IAAI,CAACN,QAAQ,EAAEE,QAAQ,CAAC,EAAE;QACtDI,IAAI,CAACN,QAAQ,GAAGS,CAAAA,GAAAA,iBAAgB,AAAyB,CAAA,iBAAzB,CAACH,IAAI,CAACN,QAAQ,EAAEE,QAAQ,CAAC;QACzDI,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;KACzB;IAED,IACED,OAAO,CAACS,SAAS,KAAK,IAAI,IAC1BJ,IAAI,CAACN,QAAQ,CAACW,UAAU,CAAC,cAAc,CAAC,IACxCL,IAAI,CAACN,QAAQ,CAACO,QAAQ,CAAC,OAAO,CAAC,EAC/B;QACA,MAAMK,KAAK,GAAGN,IAAI,CAACN,QAAQ,CACxBa,OAAO,qBAAqB,EAAE,CAAC,CAC/BA,OAAO,YAAY,EAAE,CAAC,CACtBC,KAAK,CAAC,GAAG,CAAC;QAEb,MAAMC,OAAO,GAAGH,KAAK,CAAC,CAAC,CAAC;QACxBN,IAAI,CAACN,QAAQ,GAAGY,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,EAAEA,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;QAC3EX,IAAI,CAACS,OAAO,GAAGA,OAAO;KACvB;IAED,IAAIZ,IAAI,EAAE;QACR,MAAMe,UAAU,GAAGC,CAAAA,GAAAA,oBAAmB,AAA6B,CAAA,oBAA7B,CAACb,IAAI,CAACN,QAAQ,EAAEG,IAAI,CAACiB,OAAO,CAAC;QACnEd,IAAI,CAACe,MAAM,GAAGH,UAAU,QAAgB,GAA1BA,KAAAA,CAA0B,GAA1BA,UAAU,CAAEI,cAAc;QACxChB,IAAI,CAACN,QAAQ,GAAGkB,CAAAA,UAAU,QAAU,GAApBA,KAAAA,CAAoB,GAApBA,UAAU,CAAElB,QAAQ,CAAA,IAAIM,IAAI,CAACN,QAAQ;KACtD;IAED,OAAOM,IAAI,CAAA;CACZ"}