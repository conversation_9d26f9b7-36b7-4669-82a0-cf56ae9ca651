{"version": 3, "sources": ["../../../shared/lib/escape-regexp.ts"], "names": ["escapeStringRegexp", "reHasRegExp", "reReplaceRegExp", "str", "test", "replace"], "mappings": "AACA;;;;QAGgBA,kBAAkB,GAAlBA,kBAAkB;AAJlC,0EAA0E;AAC1E,MAAMC,WAAW,wBAAwB;AACzC,MAAMC,eAAe,yBAAyB;AAEvC,SAASF,kBAAkB,CAACG,GAAW,EAAE;IAC9C,+GAA+G;IAC/G,IAAIF,WAAW,CAACG,IAAI,CAACD,GAAG,CAAC,EAAE;QACzB,OAAOA,GAAG,CAACE,OAAO,CAACH,eAAe,EAAE,MAAM,CAAC,CAAA;KAC5C;IACD,OAAOC,GAAG,CAAA;CACX"}