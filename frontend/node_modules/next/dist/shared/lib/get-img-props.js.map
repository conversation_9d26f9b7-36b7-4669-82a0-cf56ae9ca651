{"version": 3, "sources": ["../../../src/shared/lib/get-img-props.ts"], "names": ["getImgProps", "VALID_LOADING_VALUES", "undefined", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "push", "length", "smallestRatio", "Math", "min", "widths", "filter", "s", "kind", "Set", "map", "w", "find", "p", "generateImgAttrs", "config", "unoptimized", "quality", "loader", "srcSet", "last", "i", "join", "_state", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "blurDataURL", "fetchPriority", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "c", "imageConfigDefault", "imageSizes", "sort", "a", "b", "Error", "isDefaultLoader", "customImageLoader", "obj", "_", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "startsWith", "endsWith", "dangerouslyAllowSVG", "qualityInt", "process", "env", "NODE_ENV", "output", "position", "isNaN", "includes", "String", "warnOnce", "VALID_BLUR_EXT", "urlStr", "url", "URL", "err", "pathname", "search", "<PERSON><PERSON><PERSON>", "legacyValue", "Object", "entries", "window", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "get", "observe", "type", "buffered", "console", "error", "imgStyle", "assign", "left", "top", "right", "bottom", "color", "backgroundImage", "getImageBlurSvg", "placeholder<PERSON><PERSON><PERSON>", "backgroundSize", "backgroundPosition", "backgroundRepeat", "imgAttributes", "fullUrl", "e", "location", "href", "set", "props", "decoding", "meta"], "mappings": ";;;;+BA2OgBA;;;eAAAA;;;0BA3OS;8BACO;6BACG;AA6EnC,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAkBzD,SAASC,gBACPC,GAAoC;IAEpC,OAAO,AAACA,IAAsBC,OAAO,KAAKH;AAC5C;AAEA,SAASI,kBACPF,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKF;AAC1C;AAEA,SAASK,eAAeH,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACdD,CAAAA,gBAAgBC,QACfE,kBAAkBF,IAAmB;AAE3C;AAEA,MAAMI,UAAU,IAAIC;AAIpB,IAAIC;AAEJ,SAASC,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,aAAa;QAC5B,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOC,OAAOC,QAAQ,CAACF,KAAKA,IAAIG;IAClC;IACA,IAAI,OAAOH,MAAM,YAAY,WAAWI,IAAI,CAACJ,IAAI;QAC/C,OAAOK,SAASL,GAAG;IACrB;IACA,OAAOG;AACT;AAEA,SAASG,UACP,KAAsC,EACtCC,KAAyB,EACzBC,KAAyB;IAFzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAIA,IAAIF,OAAO;QACT,yDAAyD;QACzD,MAAMG,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAaG,IAAI,CAACV,SAASQ,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAaI,MAAM,EAAE;YACvB,MAAMC,gBAAgBC,KAAKC,GAAG,IAAIP,gBAAgB;YAClD,OAAO;gBACLQ,QAAQV,SAASW,MAAM,CAAC,CAACC,IAAMA,KAAKb,WAAW,CAAC,EAAE,GAAGQ;gBACrDM,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQV;YAAUa,MAAM;QAAI;IACvC;IACA,IAAI,OAAOhB,UAAU,UAAU;QAC7B,OAAO;YAAEa,QAAQX;YAAac,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAII,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACjB;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACkB,GAAG,CACpC,CAACC,IAAMhB,SAASiB,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMhB,QAAQ,CAACA,SAASM,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAEI;QAAQG,MAAM;IAAI;AAC7B;AAkBA,SAASM,iBAAiB,KAQR;IARQ,IAAA,EACxBC,MAAM,EACNtC,GAAG,EACHuC,WAAW,EACXxB,KAAK,EACLyB,OAAO,EACPxB,KAAK,EACLyB,MAAM,EACU,GARQ;IASxB,IAAIF,aAAa;QACf,OAAO;YAAEvC;YAAK0C,QAAQ5C;YAAWkB,OAAOlB;QAAU;IACpD;IAEA,MAAM,EAAE8B,MAAM,EAAEG,IAAI,EAAE,GAAGjB,UAAUwB,QAAQvB,OAAOC;IAClD,MAAM2B,OAAOf,OAAOJ,MAAM,GAAG;IAE7B,OAAO;QACLR,OAAO,CAACA,SAASe,SAAS,MAAM,UAAUf;QAC1C0B,QAAQd,OACLK,GAAG,CACF,CAACC,GAAGU,IACF,AAAGH,OAAO;gBAAEH;gBAAQtC;gBAAKwC;gBAASzB,OAAOmB;YAAE,KAAG,MAC5CH,CAAAA,SAAS,MAAMG,IAAIU,IAAI,CAAA,IACtBb,MAENc,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD7C,KAAKyC,OAAO;YAAEH;YAAQtC;YAAKwC;YAASzB,OAAOa,MAAM,CAACe,KAAK;QAAC;IAC1D;AACF;AAKO,SAAS/C,YACd,KAwBa,EACbkD,MAKC;IA9BD,IAAA,EACE9C,GAAG,EACHgB,KAAK,EACLuB,cAAc,KAAK,EACnBQ,WAAW,KAAK,EAChBC,OAAO,EACPC,SAAS,EACTT,OAAO,EACPzB,KAAK,EACLmC,MAAM,EACNC,OAAO,KAAK,EACZC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,iBAAiB,EACjBC,cAAc,OAAO,EACrBC,WAAW,EACXC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACR,GAAGC,MACQ,GAxBb;IAwCA,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGtB;IAC9D,IAAIR;IACJ,IAAI+B,IAAIJ,WAAWK,+BAAkB;IACrC,IAAI,cAAcD,GAAG;QACnB/B,SAAS+B;IACX,OAAO;QACL,MAAMnD,WAAW;eAAImD,EAAEpD,WAAW;eAAKoD,EAAEE,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMzD,cAAcoD,EAAEpD,WAAW,CAACuD,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrDpC,SAAS;YAAE,GAAG+B,CAAC;YAAEnD;YAAUD;QAAY;IACzC;IAEA,IAAI,OAAOmD,kBAAkB,aAAa;QACxC,MAAM,IAAIO,MACR;IAEJ;IACA,IAAIlC,SAAgCuB,KAAKvB,MAAM,IAAI2B;IAEnD,sDAAsD;IACtD,OAAOJ,KAAKvB,MAAM;IAClB,OAAO,AAACuB,KAAatB,MAAM;IAE3B,6CAA6C;IAC7C,oDAAoD;IACpD,MAAMkC,kBAAkB,wBAAwBnC;IAEhD,IAAImC,iBAAiB;QACnB,IAAItC,OAAOG,MAAM,KAAK,UAAU;YAC9B,MAAM,IAAIkC,MACR,AAAC,qBAAkB3E,MAAI,gCACpB;QAEP;IACF,OAAO;QACL,8CAA8C;QAC9C,+CAA+C;QAC/C,iDAAiD;QACjD,MAAM6E,oBAAoBpC;QAC1BA,SAAS,CAACqC;YACR,MAAM,EAAExC,QAAQyC,CAAC,EAAE,GAAGC,MAAM,GAAGF;YAC/B,OAAOD,kBAAkBG;QAC3B;IACF;IAEA,IAAIrB,QAAQ;QACV,IAAIA,WAAW,QAAQ;YACrBR,OAAO;QACT;QACA,MAAM8B,gBAAoE;YACxEC,WAAW;gBAAEC,UAAU;gBAAQjC,QAAQ;YAAO;YAC9CkC,YAAY;gBAAErE,OAAO;gBAAQmC,QAAQ;YAAO;QAC9C;QACA,MAAMmC,gBAAoD;YACxDD,YAAY;YACZjC,MAAM;QACR;QACA,MAAMmC,cAAcL,aAAa,CAACtB,OAAO;QACzC,IAAI2B,aAAa;YACflC,QAAQ;gBAAE,GAAGA,KAAK;gBAAE,GAAGkC,WAAW;YAAC;QACrC;QACA,MAAMC,cAAcF,aAAa,CAAC1B,OAAO;QACzC,IAAI4B,eAAe,CAACvE,OAAO;YACzBA,QAAQuE;QACV;IACF;IAEA,IAAIC,YAAY;IAChB,IAAIC,WAAWlF,OAAOQ;IACtB,IAAI2E,YAAYnF,OAAO2C;IACvB,IAAIyC;IACJ,IAAIC;IACJ,IAAIzF,eAAeH,MAAM;QACvB,MAAM6F,kBAAkB9F,gBAAgBC,OAAOA,IAAIC,OAAO,GAAGD;QAE7D,IAAI,CAAC6F,gBAAgB7F,GAAG,EAAE;YACxB,MAAM,IAAI2E,MACR,AAAC,gJAA6ImB,KAAKC,SAAS,CAC1JF;QAGN;QACA,IAAI,CAACA,gBAAgB3C,MAAM,IAAI,CAAC2C,gBAAgB9E,KAAK,EAAE;YACrD,MAAM,IAAI4D,MACR,AAAC,6JAA0JmB,KAAKC,SAAS,CACvKF;QAGN;QAEAF,YAAYE,gBAAgBF,SAAS;QACrCC,aAAaC,gBAAgBD,UAAU;QACvCnC,cAAcA,eAAeoC,gBAAgBpC,WAAW;QACxD+B,YAAYK,gBAAgB7F,GAAG;QAE/B,IAAI,CAACmD,MAAM;YACT,IAAI,CAACsC,YAAY,CAACC,WAAW;gBAC3BD,WAAWI,gBAAgB9E,KAAK;gBAChC2E,YAAYG,gBAAgB3C,MAAM;YACpC,OAAO,IAAIuC,YAAY,CAACC,WAAW;gBACjC,MAAMM,QAAQP,WAAWI,gBAAgB9E,KAAK;gBAC9C2E,YAAYhE,KAAKuE,KAAK,CAACJ,gBAAgB3C,MAAM,GAAG8C;YAClD,OAAO,IAAI,CAACP,YAAYC,WAAW;gBACjC,MAAMM,QAAQN,YAAYG,gBAAgB3C,MAAM;gBAChDuC,WAAW/D,KAAKuE,KAAK,CAACJ,gBAAgB9E,KAAK,GAAGiF;YAChD;QACF;IACF;IACAhG,MAAM,OAAOA,QAAQ,WAAWA,MAAMwF;IAEtC,IAAIU,SACF,CAACnD,YAAaC,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAI,CAAChD,OAAOA,IAAImG,UAAU,CAAC,YAAYnG,IAAImG,UAAU,CAAC,UAAU;QAC9D,uEAAuE;QACvE5D,cAAc;QACd2D,SAAS;IACX;IACA,IAAI5D,OAAOC,WAAW,EAAE;QACtBA,cAAc;IAChB;IACA,IAAIqC,mBAAmB5E,IAAIoG,QAAQ,CAAC,WAAW,CAAC9D,OAAO+D,mBAAmB,EAAE;QAC1E,yDAAyD;QACzD,+CAA+C;QAC/C9D,cAAc;IAChB;IACA,IAAIQ,UAAU;QACZW,gBAAgB;IAClB;IAEA,MAAM4C,aAAa/F,OAAOiC;IAE1B,IAAI+D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAInE,OAAOoE,MAAM,KAAK,YAAY9B,mBAAmB,CAACrC,aAAa;YACjE,MAAM,IAAIoC,MACP;QAML;QACA,IAAI,CAAC3E,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CuC,cAAc;QAChB,OAAO;YACL,IAAIY,MAAM;gBACR,IAAIpC,OAAO;oBACT,MAAM,IAAI4D,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIkD,QAAQ;oBACV,MAAM,IAAIyB,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOuD,QAAQ,KAAIvD,MAAMuD,QAAQ,KAAK,YAAY;oBACpD,MAAM,IAAIhC,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOrC,KAAK,KAAIqC,MAAMrC,KAAK,KAAK,QAAQ;oBAC1C,MAAM,IAAI4D,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIoD,CAAAA,yBAAAA,MAAOF,MAAM,KAAIE,MAAMF,MAAM,KAAK,QAAQ;oBAC5C,MAAM,IAAIyB,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;YACF,OAAO;gBACL,IAAI,OAAOyF,aAAa,aAAa;oBACnC,MAAM,IAAId,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B,OAAO,IAAI4G,MAAMnB,WAAW;oBAC1B,MAAM,IAAId,MACR,AAAC,qBAAkB3E,MAAI,sFAAmFe,QAAM;gBAEpH;gBACA,IAAI,OAAO2E,cAAc,aAAa;oBACpC,MAAM,IAAIf,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B,OAAO,IAAI4G,MAAMlB,YAAY;oBAC3B,MAAM,IAAIf,MACR,AAAC,qBAAkB3E,MAAI,uFAAoFkD,SAAO;gBAEtH;YACF;QACF;QACA,IAAI,CAACrD,qBAAqBgH,QAAQ,CAAC7D,UAAU;YAC3C,MAAM,IAAI2B,MACR,AAAC,qBAAkB3E,MAAI,iDAA8CgD,UAAQ,wBAAqBnD,qBAAqBoC,GAAG,CACxH6E,QACAjE,IAAI,CAAC,OAAK;QAEhB;QACA,IAAIE,YAAYC,YAAY,QAAQ;YAClC,MAAM,IAAI2B,MACR,AAAC,qBAAkB3E,MAAI;QAE3B;QACA,IACEwD,gBAAgB,WAChBA,gBAAgB,UAChB,CAACA,YAAY2C,UAAU,CAAC,gBACxB;YACA,MAAM,IAAIxB,MACR,AAAC,qBAAkB3E,MAAI,2CAAwCwD,cAAY;QAE/E;QACA,IAAIA,gBAAgB,SAAS;YAC3B,IAAIiC,YAAYC,aAAaD,WAAWC,YAAY,MAAM;gBACxDqB,IAAAA,kBAAQ,EACN,AAAC,qBAAkB/G,MAAI;YAE3B;QACF;QACA,IAAIwD,gBAAgB,UAAU,CAACC,aAAa;YAC1C,MAAMuD,iBAAiB;gBAAC;gBAAQ;gBAAO;gBAAQ;aAAO,CAAC,iCAAiC;;YAExF,MAAM,IAAIrC,MACR,AAAC,qBAAkB3E,MAAI,6TAGkEgH,eAAenE,IAAI,CACxG,OACA;QAIR;QACA,IAAI,SAASmB,MAAM;YACjB+C,IAAAA,kBAAQ,EACN,AAAC,qBAAkB/G,MAAI;QAE3B;QAEA,IAAI,CAACuC,eAAe,CAACqC,iBAAiB;YACpC,MAAMqC,SAASxE,OAAO;gBACpBH;gBACAtC;gBACAe,OAAO0E,YAAY;gBACnBjD,SAAS8D,cAAc;YACzB;YACA,IAAIY;YACJ,IAAI;gBACFA,MAAM,IAAIC,IAAIF;YAChB,EAAE,OAAOG,KAAK,CAAC;YACf,IAAIH,WAAWjH,OAAQkH,OAAOA,IAAIG,QAAQ,KAAKrH,OAAO,CAACkH,IAAII,MAAM,EAAG;gBAClEP,IAAAA,kBAAQ,EACN,AAAC,qBAAkB/G,MAAI,4HACpB;YAEP;QACF;QAEA,IAAIuD,mBAAmB;YACrBwD,IAAAA,kBAAQ,EACN,AAAC,qBAAkB/G,MAAI;QAE3B;QAEA,KAAK,MAAM,CAACuH,WAAWC,YAAY,IAAIC,OAAOC,OAAO,CAAC;YACpD/D;YACAC;YACAC;YACAC;YACAC;QACF,GAAI;YACF,IAAIyD,aAAa;gBACfT,IAAAA,kBAAQ,EACN,AAAC,qBAAkB/G,MAAI,wBAAqBuH,YAAU,0CACnD;YAEP;QACF;QAEA,IACE,OAAOI,WAAW,eAClB,CAACrH,gBACDqH,OAAOC,mBAAmB,EAC1B;YACAtH,eAAe,IAAIsH,oBAAoB,CAACC;gBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;wBAE3BD;oBADf,0EAA0E;oBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgB9H,GAAG,KAAI;oBACtC,MAAMkI,WAAW9H,QAAQ+H,GAAG,CAACH;oBAC7B,IACEE,YACA,CAACA,SAASnF,QAAQ,IAClBmF,SAAS1E,WAAW,KAAK,WACzB,CAAC0E,SAASlI,GAAG,CAACmG,UAAU,CAAC,YACzB,CAAC+B,SAASlI,GAAG,CAACmG,UAAU,CAAC,UACzB;wBACA,iDAAiD;wBACjDY,IAAAA,kBAAQ,EACN,AAAC,qBAAkBmB,SAASlI,GAAG,GAAC,8HAC7B;oBAEP;gBACF;YACF;YACA,IAAI;gBACFM,aAAa8H,OAAO,CAAC;oBACnBC,MAAM;oBACNC,UAAU;gBACZ;YACF,EAAE,OAAOlB,KAAK;gBACZ,oCAAoC;gBACpCmB,QAAQC,KAAK,CAACpB;YAChB;QACF;IACF;IACA,MAAMqB,WAAWhB,OAAOiB,MAAM,CAC5BvF,OACI;QACEwD,UAAU;QACVzD,QAAQ;QACRnC,OAAO;QACP4H,MAAM;QACNC,KAAK;QACLC,OAAO;QACPC,QAAQ;QACRlF;QACAC;IACF,IACA,CAAC,GACLK,cAAc,CAAC,IAAI;QAAE6E,OAAO;IAAc,GAC1C3F;IAGF,MAAM4F,kBACJ,CAAC7E,gBAAgBX,gBAAgB,UAC7BA,gBAAgB,SACd,AAAC,2CAAwCyF,IAAAA,6BAAe,EAAC;QACvDxD;QACAC;QACAC;QACAC;QACAnC,aAAaA,eAAe;QAC5BG,WAAW6E,SAAS7E,SAAS;IAC/B,KAAG,OACH,AAAC,UAAOJ,cAAY,KAAI,uBAAuB;OACjD;IAEN,IAAI0F,mBAAmBF,kBACnB;QACEG,gBAAgBV,SAAS7E,SAAS,IAAI;QACtCwF,oBAAoBX,SAAS5E,cAAc,IAAI;QAC/CwF,kBAAkB;QAClBL;IACF,IACA,CAAC;IAEL,IAAIzC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IACEyC,iBAAiBF,eAAe,IAChCxF,gBAAgB,WAChBC,+BAAAA,YAAa0C,UAAU,CAAC,OACxB;YACA,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF+C,iBAAiBF,eAAe,GAAG,AAAC,UAAOvF,cAAY;QACzD;IACF;IAEA,MAAM6F,gBAAgBjH,iBAAiB;QACrCC;QACAtC;QACAuC;QACAxB,OAAO0E;QACPjD,SAAS8D;QACTtF;QACAyB;IACF;IAEA,IAAI8D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAOkB,WAAW,aAAa;YACjC,IAAI4B;YACJ,IAAI;gBACFA,UAAU,IAAIpC,IAAImC,cAActJ,GAAG;YACrC,EAAE,OAAOwJ,GAAG;gBACVD,UAAU,IAAIpC,IAAImC,cAActJ,GAAG,EAAE2H,OAAO8B,QAAQ,CAACC,IAAI;YAC3D;YACAtJ,QAAQuJ,GAAG,CAACJ,QAAQG,IAAI,EAAE;gBAAE1J;gBAAK+C;gBAAUS;YAAY;QACzD;IACF;IAEA,MAAMoG,QAAkB;QACtB,GAAG5F,IAAI;QACPhB,SAASkD,SAAS,SAASlD;QAC3BU;QACA3C,OAAO0E;QACPvC,QAAQwC;QACRmE,UAAU;QACV5G;QACAG,OAAO;YAAE,GAAGqF,QAAQ;YAAE,GAAGS,gBAAgB;QAAC;QAC1ClI,OAAOsI,cAActI,KAAK;QAC1B0B,QAAQ4G,cAAc5G,MAAM;QAC5B1C,KAAKqD,eAAeiG,cAActJ,GAAG;IACvC;IACA,MAAM8J,OAAO;QAAEvH;QAAaQ;QAAUS;QAAaL;IAAK;IACxD,OAAO;QAAEyG;QAAOE;IAAK;AACvB"}