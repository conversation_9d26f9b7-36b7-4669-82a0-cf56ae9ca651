{"version": 3, "sources": ["../../../../src/server/web/spec-extension/response.ts"], "names": ["NextURL", "toNodeOutgoingHttpHeaders", "validateURL", "ResponseCookies", "INTERNALS", "Symbol", "REDIRECTS", "Set", "handleMiddlewareField", "init", "headers", "request", "Headers", "Error", "keys", "key", "value", "set", "push", "join", "NextResponse", "Response", "constructor", "body", "cookies", "url", "nextConfig", "undefined", "for", "bodyUsed", "Object", "fromEntries", "ok", "redirected", "status", "statusText", "type", "json", "response", "redirect", "has", "RangeError", "initObj", "rewrite", "destination", "next"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAa;AACrC,SAASC,yBAAyB,EAAEC,WAAW,QAAQ,WAAU;AAEjE,SAASC,eAAe,QAAQ,YAAW;AAE3C,MAAMC,YAAYC,OAAO;AACzB,MAAMC,YAAY,IAAIC,IAAI;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAEnD,SAASC,sBACPC,IAAwC,EACxCC,OAAgB;QAEZD;IAAJ,IAAIA,yBAAAA,gBAAAA,KAAME,OAAO,qBAAbF,cAAeC,OAAO,EAAE;QAC1B,IAAI,CAAED,CAAAA,KAAKE,OAAO,CAACD,OAAO,YAAYE,OAAM,GAAI;YAC9C,MAAM,IAAIC,MAAM;QAClB;QAEA,MAAMC,OAAO,EAAE;QACf,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIP,KAAKE,OAAO,CAACD,OAAO,CAAE;YAC/CA,QAAQO,GAAG,CAAC,0BAA0BF,KAAKC;YAC3CF,KAAKI,IAAI,CAACH;QACZ;QAEAL,QAAQO,GAAG,CAAC,iCAAiCH,KAAKK,IAAI,CAAC;IACzD;AACF;AAEA;;;;CAIC,GACD,OAAO,MAAMC,qBAAqCC;IAOhDC,YAAYC,IAAsB,EAAEd,OAAqB,CAAC,CAAC,CAAE;QAC3D,KAAK,CAACc,MAAMd;QAEZ,IAAI,CAACL,UAAU,GAAG;YAChBoB,SAAS,IAAIrB,gBAAgB,IAAI,CAACO,OAAO;YACzCe,KAAKhB,KAAKgB,GAAG,GACT,IAAIzB,QAAQS,KAAKgB,GAAG,EAAE;gBACpBf,SAAST,0BAA0B,IAAI,CAACS,OAAO;gBAC/CgB,YAAYjB,KAAKiB,UAAU;YAC7B,KACAC;QACN;IACF;IAEA,CAACtB,OAAOuB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLJ,SAAS,IAAI,CAACA,OAAO;YACrBC,KAAK,IAAI,CAACA,GAAG;YACb,mCAAmC;YACnCF,MAAM,IAAI,CAACA,IAAI;YACfM,UAAU,IAAI,CAACA,QAAQ;YACvBnB,SAASoB,OAAOC,WAAW,CAAC,IAAI,CAACrB,OAAO;YACxCsB,IAAI,IAAI,CAACA,EAAE;YACXC,YAAY,IAAI,CAACA,UAAU;YAC3BC,QAAQ,IAAI,CAACA,MAAM;YACnBC,YAAY,IAAI,CAACA,UAAU;YAC3BC,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEA,IAAWZ,UAAU;QACnB,OAAO,IAAI,CAACpB,UAAU,CAACoB,OAAO;IAChC;IAEA,OAAOa,KACLd,IAAc,EACdd,IAAmB,EACK;QACxB,MAAM6B,WAAqBjB,SAASgB,IAAI,CAACd,MAAMd;QAC/C,OAAO,IAAIW,aAAakB,SAASf,IAAI,EAAEe;IACzC;IAEA,OAAOC,SAASd,GAA2B,EAAEhB,IAA4B,EAAE;QACzE,MAAMyB,SAAS,OAAOzB,SAAS,WAAWA,OAAOA,CAAAA,wBAAAA,KAAMyB,MAAM,KAAI;QACjE,IAAI,CAAC5B,UAAUkC,GAAG,CAACN,SAAS;YAC1B,MAAM,IAAIO,WACR;QAEJ;QACA,MAAMC,UAAU,OAAOjC,SAAS,WAAWA,OAAO,CAAC;QACnD,MAAMC,UAAU,IAAIE,QAAQ8B,2BAAAA,QAAShC,OAAO;QAC5CA,QAAQO,GAAG,CAAC,YAAYf,YAAYuB;QAEpC,OAAO,IAAIL,aAAa,MAAM;YAC5B,GAAGsB,OAAO;YACVhC;YACAwB;QACF;IACF;IAEA,OAAOS,QACLC,WAAmC,EACnCnC,IAA6B,EAC7B;QACA,MAAMC,UAAU,IAAIE,QAAQH,wBAAAA,KAAMC,OAAO;QACzCA,QAAQO,GAAG,CAAC,wBAAwBf,YAAY0C;QAEhDpC,sBAAsBC,MAAMC;QAC5B,OAAO,IAAIU,aAAa,MAAM;YAAE,GAAGX,IAAI;YAAEC;QAAQ;IACnD;IAEA,OAAOmC,KAAKpC,IAA6B,EAAE;QACzC,MAAMC,UAAU,IAAIE,QAAQH,wBAAAA,KAAMC,OAAO;QACzCA,QAAQO,GAAG,CAAC,qBAAqB;QAEjCT,sBAAsBC,MAAMC;QAC5B,OAAO,IAAIU,aAAa,MAAM;YAAE,GAAGX,IAAI;YAAEC;QAAQ;IACnD;AACF"}