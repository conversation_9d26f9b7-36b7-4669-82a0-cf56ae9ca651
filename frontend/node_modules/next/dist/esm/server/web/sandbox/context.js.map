{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "names": ["AsyncLocalStorage", "COMPILER_NAMES", "EDGE_UNSUPPORTED_NODE_APIS", "EdgeRuntime", "readFileSync", "promises", "fs", "validateURL", "pick", "fetchInlineAsset", "runInContext", "BufferImplementation", "EventsImplementation", "AssertImplementation", "UtilImplementation", "AsyncHooksImplementation", "intervalsManager", "timeouts<PERSON><PERSON><PERSON>", "getServerError", "decorateServerError", "process", "env", "NODE_ENV", "middleware", "require", "error", "_", "__", "moduleContexts", "Map", "pendingModuleCaches", "clearAllModuleContexts", "removeAll", "clear", "clearModuleContext", "path", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "pairs", "Object", "keys", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "Error", "edgeServer", "createProcessPolyfill", "processPolyfill", "overridenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "entries", "requestStore", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "codeGeneration", "strings", "extend", "id", "TypeError", "__next_log_error__", "err", "onError", "__next_eval__", "fn", "toString", "warning", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "assets", "distDir", "headers", "Headers", "store", "getStore", "prevs", "split", "concat", "moduleName", "join", "response", "url", "String", "catch", "message", "stack", "__Request", "Request", "constructor", "next", "__redirect", "Response", "redirect", "bind", "args", "assign", "performance", "setInterval", "clearInterval", "interval", "remove", "setTimeout", "clearTimeout", "timeout", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "getModuleContextShared", "deferredModuleContext", "getModuleContext", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "filename"], "mappings": "AAMA,SAASA,iBAAiB,QAAQ,cAAa;AAC/C,SACEC,cAAc,EACdC,0BAA0B,QACrB,gCAA+B;AACtC,SAASC,WAAW,QAAQ,kCAAiC;AAC7D,SAASC,YAAY,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACjD,SAASC,WAAW,QAAQ,WAAU;AACtC,SAASC,IAAI,QAAQ,oBAAmB;AACxC,SAASC,gBAAgB,QAAQ,wBAAuB;AACxD,SAASC,YAAY,QAAQ,KAAI;AACjC,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,0BAA0B,cAAa;AAC9C,OAAOC,wBAAwB,YAAW;AAC1C,OAAOC,8BAA8B,mBAAkB;AACvD,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,sBAAqB;AAQvE,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,aAAaC,QAAQ;IAC3BN,iBAAiBK,WAAWL,cAAc;IAC1CC,sBACEK,QAAQ,oCAAoCL,mBAAmB;AACnE,OAAO;IACLD,iBAAiB,CAACO,OAAcC,IAAcD;IAC9CN,sBAAsB,CAACO,GAAUC,MAAgB;AACnD;AAEA;;;;CAIC,GACD,MAAMC,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAEhC;;CAEC,GACD,OAAO,eAAeE;IACpBf,iBAAiBgB,SAAS;IAC1Bf,gBAAgBe,SAAS;IACzBJ,eAAeK,KAAK;IACpBH,oBAAoBG,KAAK;AAC3B;AAEA;;;;;;;CAOC,GACD,OAAO,eAAeC,mBAAmBC,IAAY;IACnDnB,iBAAiBgB,SAAS;IAC1Bf,gBAAgBe,SAAS;IAEzB,MAAMI,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACN,OAAO;YAC1BI,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIV,eAAgB;QACzCQ,cAAcC,KAAKC,OAAOV;IAC5B;IACA,KAAK,MAAM,CAACS,KAAKC,MAAM,IAAIR,oBAAqB;QAC9CM,cAAcC,KAAK,MAAMC,OAAOR;IAClC;AACF;AAEA,eAAea,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,SAAS,MAAMC,YAAYC,OAAO,CACtC,MAAM9C,GAAG+C,QAAQ,CAACJ,QAAQK,QAAQ;QAEpCT,OAAO,CAACI,QAAQM,IAAI,CAAC,GAAGL;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASW;IACP,MAAMC,QAAQC,OAAOC,IAAI,CAACvC,QAAQC,GAAG,EAAE2B,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKjB,QAAQC,GAAG,CAACgB,IAAI;SAAC;IAC3E,MAAMhB,MAAMqC,OAAOE,WAAW,CAACH;IAC/BpC,IAAIwC,YAAY,GAAG;IACnB,OAAOxC;AACT;AAEA,SAASyC,yBAAyBP,IAAY;IAC5C,MAAM9B,QACJ,IAAIsC,MAAM,CAAC,uBAAuB,EAAER,KAAK;8DACiB,CAAC;IAC7DpC,oBAAoBM,OAAOxB,eAAe+D,UAAU;IACpD,MAAMvC;AACR;AAEA,SAASwC;IACP,MAAMC,kBAAkB;QAAE7C,KAAKmC;IAAgC;IAC/D,MAAMW,iBAAsC,CAAC;IAC7C,KAAK,MAAM9B,OAAOqB,OAAOC,IAAI,CAACvC,SAAU;QACtC,IAAIiB,QAAQ,OAAO;QACnBqB,OAAOU,cAAc,CAACF,iBAAiB7B,KAAK;YAC1CgC;gBACE,IAAIF,cAAc,CAAC9B,IAAI,KAAKiC,WAAW;oBACrC,OAAOH,cAAc,CAAC9B,IAAI;gBAC5B;gBACA,IAAI,OAAO,AAACjB,OAAe,CAACiB,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAMyB,yBAAyB,CAAC,QAAQ,EAAEzB,IAAI,CAAC;gBACxD;gBACA,OAAOiC;YACT;YACAC,KAAIC,KAAK;gBACPL,cAAc,CAAC9B,IAAI,GAAGmC;YACxB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQnC,OAA+B,EAAEgB,IAAY;IAC5DG,OAAOU,cAAc,CAAC7B,SAASgB,MAAM;QACnCc;YACE,OAAO;gBACLP,yBAAyBP;YAC3B;QACF;QACAkB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACrD;QACN,IAAIA,iBAAiBoD,kBAAkB;YACrC1D,oBAAoBM,OAAOxB,eAAe+D,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/C1D,oBAAoB6D,SAASC,MAAM,EAAEhF,eAAe+D,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAe3E,KAAKG,sBAAsB;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeH,KAAKI,sBAAsB;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBJ,KAAKO,0BAA0B;YACjD;YACA;SACD;QACD,eAAeP,KAAKK,sBAAsB;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaL,KAAKM,oBAAoB;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAIe,IAAI6B,OAAO0B,OAAO,CAACD;AAChC,CAAA;AAEA,OAAO,MAAME,eAAe,IAAIrF,oBAE5B;AAEJ;;;CAGC,GACD,eAAesF,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAM7C,OAAO,MAAMD,SAAS4C,QAAQI,iBAAiB,CAAC/C,IAAI,IAAI,EAAE;IAChE,MAAMgC,UAAU,IAAIzE,YAAY;QAC9ByF,gBACExE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB;YAAEuE,SAAS;YAAMjD,MAAM;QAAK,IAC5B0B;QACNwB,QAAQ,CAACvD;YACPA,QAAQnB,OAAO,GAAG6C;YAElBP,OAAOU,cAAc,CAAC7B,SAAS,WAAW;gBACxCkC,YAAY;gBACZD,OAAO,CAACuB;oBACN,MAAMvB,QAAQU,gBAAgBb,GAAG,CAAC0B;oBAClC,IAAI,CAACvB,OAAO;wBACV,MAAMwB,UAAU,8BAA8BD;oBAChD;oBACA,OAAOvB;gBACT;YACF;YAEA,IAAIpD,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzCiB,QAAQ0D,kBAAkB,GAAG,SAAUC,GAAY;oBACjDX,QAAQY,OAAO,CAACD;gBAClB;YACF;YAEA3D,QAAQ6D,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAMhE,MAAMgE,GAAGC,QAAQ;gBACvB,IAAI,CAACd,YAAY/C,GAAG,CAACJ,MAAM;oBACzB,MAAMkE,UAAUrF,eACd,IAAI6C,MACF,CAAC;yEAC0D,CAAC,GAE9D9D,eAAe+D,UAAU;oBAE3BuC,QAAQhD,IAAI,GAAG;oBACfQ,MAAMyC,iBAAiB,CAACD,SAASH;oBACjCZ,YAAYiB,GAAG,CAACpE;oBAChBkD,QAAQmB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEA9D,QAAQoE,4BAA4B,GAClC,SAASA,6BAA6BN,EAAY;gBAChD,MAAMhE,MAAMgE,GAAGC,QAAQ;gBACvB,IAAI,CAACZ,mBAAmBjD,GAAG,CAACJ,MAAM;oBAChC,MAAMkE,UAAUrF,eACd,IAAI6C,MAAM,CAAC;yEACgD,CAAC,GAC5D9D,eAAe+D,UAAU;oBAE3BuC,QAAQhD,IAAI,GAAG;oBACfQ,MAAMyC,iBAAiB,CAACD,SAASI;oBACjCjB,mBAAmBe,GAAG,CAACpE;oBACvBkD,QAAQmB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEF9D,QAAQqE,gCAAgC,GACtC,eAAeA,iCAAiCP,EAAY;gBAC1D,MAAMQ,SAAS,MAAMR;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMS,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAM1E,MAAMgE,GAAGC,QAAQ;gBACvB,IAAIQ,0BAA0B,CAACpB,mBAAmBjD,GAAG,CAACJ,MAAM;oBAC1D,MAAMkE,UAAUrF,eACd,IAAI6C,MAAM,CAAC;yEACgD,CAAC,GAC5D9D,eAAe+D,UAAU;oBAE3BuC,QAAQhD,IAAI,GAAG;oBACfQ,MAAMyC,iBAAiB,CAACD,SAASK;oBACjClB,mBAAmBe,GAAG,CAACpE;oBACvBkD,QAAQmB,SAAS,CAACH;gBACpB;gBACA,OAAOM;YACT;YAEF,MAAMG,UAAUzE,QAAQ0E,KAAK;YAC7B1E,QAAQ0E,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;oBA2BnCA;gBA1BF,MAAMC,eAAe,IAAIrD,MAAM;gBAC/B,MAAMsD,gBAAgB,MAAM5G,iBAAiB;oBAC3CyG;oBACAI,QAAQ/B,QAAQI,iBAAiB,CAAC2B,MAAM;oBACxCC,SAAShC,QAAQgC,OAAO;oBACxBhF;gBACF;gBACA,IAAI8E,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKK,OAAO,GAAG,IAAIC,QAAQN,KAAKK,OAAO,IAAI,CAAC;gBAE5C,sEAAsE;gBACtE,MAAME,QAAQrC,aAAasC,QAAQ;gBACnC,IACED,CAAAA,yBAAAA,MAAOF,OAAO,CAAC/E,GAAG,CAAC,+BACnB,CAAC0E,KAAKK,OAAO,CAAC/E,GAAG,CAAC,4BAClB;oBACA0E,KAAKK,OAAO,CAACjD,GAAG,CACd,2BACAmD,MAAMF,OAAO,CAACnD,GAAG,CAAC,8BAA8B;gBAEpD;gBAEA,MAAMuD,QACJT,EAAAA,oBAAAA,KAAKK,OAAO,CAACnD,GAAG,CAAC,CAAC,uBAAuB,CAAC,sBAA1C8C,kBAA6CU,KAAK,CAAC,SAAQ,EAAE;gBAC/D,MAAMrD,QAAQoD,MAAME,MAAM,CAACvC,QAAQwC,UAAU,EAAEC,IAAI,CAAC;gBACpDb,KAAKK,OAAO,CAACjD,GAAG,CAAC,2BAA2BC;gBAE5C,IAAI,CAAC2C,KAAKK,OAAO,CAAC/E,GAAG,CAAC,eAAe;oBACnC0E,KAAKK,OAAO,CAACjD,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAM0D,WACJ,OAAOf,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMgB,GAAG,EAAE;oBACjB,GAAG1H,KAAK0G,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPK,SAAS;wBACP,GAAG9D,OAAOE,WAAW,CAACsD,MAAMM,OAAO,CAAC;wBACpC,GAAG9D,OAAOE,WAAW,CAACuD,KAAKK,OAAO,CAAC;oBACrC;gBACF,KACAR,QAAQmB,OAAOjB,QAAQC;gBAE7B,OAAO,MAAMc,SAASG,KAAK,CAAC,CAAClC;oBAC3BkB,aAAaiB,OAAO,GAAGnC,IAAImC,OAAO;oBAClCnC,IAAIoC,KAAK,GAAGlB,aAAakB,KAAK;oBAC9B,MAAMpC;gBACR;YACF;YAEA,MAAMqC,YAAYhG,QAAQiG,OAAO;YACjCjG,QAAQiG,OAAO,GAAG,cAAcD;gBAE9BE,YAAYvB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMe,MACJ,OAAOhB,UAAU,YAAY,SAASA,QAClCA,MAAMgB,GAAG,GACTC,OAAOjB;oBACb3G,YAAY2H;oBACZ,KAAK,CAACA,KAAKf;oBACX,IAAI,CAACuB,IAAI,GAAGvB,wBAAAA,KAAMuB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAapG,QAAQqG,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACvG,QAAQqG,QAAQ;YAClErG,QAAQqG,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BxI,YAAYwI,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAMxF,QAAQrD,2BAA4B;gBAC7CwE,QAAQnC,SAASgB;YACnB;YAEAG,OAAOsF,MAAM,CAACzG,SAASK;YAEvBL,QAAQ0G,WAAW,GAAGA;YAEtB1G,QAAQvC,iBAAiB,GAAGA;YAE5B,+DAA+D;YAC/DuC,QAAQ2G,WAAW,GAAG,CAAC,GAAGH,OACxB/H,iBAAiByF,GAAG,CAACsC;YAEvB,+DAA+D;YAC/DxG,QAAQ4G,aAAa,GAAG,CAACC,WACvBpI,iBAAiBqI,MAAM,CAACD;YAE1B,+DAA+D;YAC/D7G,QAAQ+G,UAAU,GAAG,CAAC,GAAGP,OACvB9H,gBAAgBwF,GAAG,CAACsC;YAEtB,+DAA+D;YAC/DxG,QAAQgH,YAAY,GAAG,CAACC,UACtBvI,gBAAgBoI,MAAM,CAACG;YAEzB,OAAOjH;QACT;IACF;IAEA,MAAMkH,yBAAyB9E,0BAA0BC;IACzDA,QAAQrC,OAAO,CAACmH,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6B5E,8BAA8BH;IACjEA,QAAQrC,OAAO,CAACmH,gBAAgB,CAC9B,sBACAC;IAGF,OAAO;QACL/E;QACApC,OAAO,IAAIX;QACX2D,aAAa,IAAIC;IACnB;AACF;AAWA,SAASmE,uBAAuBrE,OAA6B;IAC3D,IAAIsE,wBAAwB/H,oBAAoBuC,GAAG,CAACkB,QAAQwC,UAAU;IACtE,IAAI,CAAC8B,uBAAuB;QAC1BA,wBAAwBvE,oBAAoBC;QAC5CzD,oBAAoByC,GAAG,CAACgB,QAAQwC,UAAU,EAAE8B;IAC9C;IACA,OAAOA;AACT;AAEA;;;;;CAKC,GACD,OAAO,eAAeC,iBAAiBvE,OAA6B;IAMlE,IAAIwE;IAIJ,IAAIxE,QAAQyE,QAAQ,EAAE;QACpBD,oBACEnI,eAAeyC,GAAG,CAACkB,QAAQwC,UAAU,KACpC,MAAM6B,uBAAuBrE;IAClC;IAEA,IAAI,CAACwE,mBAAmB;QACtBA,oBAAoB,MAAMzE,oBAAoBC;QAC9C3D,eAAe2C,GAAG,CAACgB,QAAQwC,UAAU,EAAEgC;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAczH,KAAK,CAACC,GAAG,CAAC0H,WAAW;YACtC,MAAMC,UAAUhK,aAAa+J,UAAU;YACvC,IAAI;gBACFzJ,aAAa0J,SAASH,cAAcrF,OAAO,CAACrC,OAAO,EAAE;oBACnD8H,UAAUF;gBACZ;gBACAF,cAAczH,KAAK,CAAC+B,GAAG,CAAC4F,UAAUC;YACpC,EAAE,OAAO3I,OAAO;gBACd,IAAI8D,QAAQyE,QAAQ,EAAE;oBACpBC,iCAAAA,cAAezH,KAAK,CAACE,MAAM,CAACyH;gBAC9B;gBACA,MAAM1I;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAGwI,aAAa;QAAEC;IAAkB;AAC/C"}