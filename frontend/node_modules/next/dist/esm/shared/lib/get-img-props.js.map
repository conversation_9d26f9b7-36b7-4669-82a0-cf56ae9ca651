{"version": 3, "sources": ["../../../src/shared/lib/get-img-props.ts"], "names": ["warnOnce", "getImageBlurSvg", "imageConfigDefault", "VALID_LOADING_VALUES", "undefined", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "push", "length", "smallestRatio", "Math", "min", "widths", "filter", "s", "kind", "Set", "map", "w", "find", "p", "generateImgAttrs", "config", "unoptimized", "quality", "loader", "srcSet", "last", "i", "join", "getImgProps", "_state", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "blurDataURL", "fetchPriority", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "c", "imageSizes", "sort", "a", "b", "Error", "isDefaultLoader", "customImageLoader", "obj", "_", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "startsWith", "endsWith", "dangerouslyAllowSVG", "qualityInt", "process", "env", "NODE_ENV", "output", "position", "isNaN", "includes", "String", "VALID_BLUR_EXT", "urlStr", "url", "URL", "err", "pathname", "search", "<PERSON><PERSON><PERSON>", "legacyValue", "Object", "entries", "window", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "get", "observe", "type", "buffered", "console", "error", "imgStyle", "assign", "left", "top", "right", "bottom", "color", "backgroundImage", "placeholder<PERSON><PERSON><PERSON>", "backgroundSize", "backgroundPosition", "backgroundRepeat", "imgAttributes", "fullUrl", "e", "location", "href", "set", "props", "decoding", "meta"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAmB;AAC5C,SAASC,eAAe,QAAQ,mBAAkB;AAClD,SAASC,kBAAkB,QAAQ,iBAAgB;AA6EnD,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAkBzD,SAASC,gBACPC,GAAoC;IAEpC,OAAO,AAACA,IAAsBC,OAAO,KAAKH;AAC5C;AAEA,SAASI,kBACPF,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKF;AAC1C;AAEA,SAASK,eAAeH,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACdD,CAAAA,gBAAgBC,QACfE,kBAAkBF,IAAmB;AAE3C;AAEA,MAAMI,UAAU,IAAIC;AAIpB,IAAIC;AAEJ,SAASC,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,aAAa;QAC5B,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOC,OAAOC,QAAQ,CAACF,KAAKA,IAAIG;IAClC;IACA,IAAI,OAAOH,MAAM,YAAY,WAAWI,IAAI,CAACJ,IAAI;QAC/C,OAAOK,SAASL,GAAG;IACrB;IACA,OAAOG;AACT;AAEA,SAASG,UACP,KAAsC,EACtCC,KAAyB,EACzBC,KAAyB;IAFzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAIA,IAAIF,OAAO;QACT,yDAAyD;QACzD,MAAMG,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAaG,IAAI,CAACV,SAASQ,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAaI,MAAM,EAAE;YACvB,MAAMC,gBAAgBC,KAAKC,GAAG,IAAIP,gBAAgB;YAClD,OAAO;gBACLQ,QAAQV,SAASW,MAAM,CAAC,CAACC,IAAMA,KAAKb,WAAW,CAAC,EAAE,GAAGQ;gBACrDM,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQV;YAAUa,MAAM;QAAI;IACvC;IACA,IAAI,OAAOhB,UAAU,UAAU;QAC7B,OAAO;YAAEa,QAAQX;YAAac,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAII,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACjB;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACkB,GAAG,CACpC,CAACC,IAAMhB,SAASiB,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMhB,QAAQ,CAACA,SAASM,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAEI;QAAQG,MAAM;IAAI;AAC7B;AAkBA,SAASM,iBAAiB,KAQR;IARQ,IAAA,EACxBC,MAAM,EACNtC,GAAG,EACHuC,WAAW,EACXxB,KAAK,EACLyB,OAAO,EACPxB,KAAK,EACLyB,MAAM,EACU,GARQ;IASxB,IAAIF,aAAa;QACf,OAAO;YAAEvC;YAAK0C,QAAQ5C;YAAWkB,OAAOlB;QAAU;IACpD;IAEA,MAAM,EAAE8B,MAAM,EAAEG,IAAI,EAAE,GAAGjB,UAAUwB,QAAQvB,OAAOC;IAClD,MAAM2B,OAAOf,OAAOJ,MAAM,GAAG;IAE7B,OAAO;QACLR,OAAO,CAACA,SAASe,SAAS,MAAM,UAAUf;QAC1C0B,QAAQd,OACLK,GAAG,CACF,CAACC,GAAGU,IACF,AAAGH,OAAO;gBAAEH;gBAAQtC;gBAAKwC;gBAASzB,OAAOmB;YAAE,KAAG,MAC5CH,CAAAA,SAAS,MAAMG,IAAIU,IAAI,CAAA,IACtBb,MAENc,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD7C,KAAKyC,OAAO;YAAEH;YAAQtC;YAAKwC;YAASzB,OAAOa,MAAM,CAACe,KAAK;QAAC;IAC1D;AACF;AAEA;;CAEC,GACD,OAAO,SAASG,YACd,KAwBa,EACbC,MAKC;IA9BD,IAAA,EACE/C,GAAG,EACHgB,KAAK,EACLuB,cAAc,KAAK,EACnBS,WAAW,KAAK,EAChBC,OAAO,EACPC,SAAS,EACTV,OAAO,EACPzB,KAAK,EACLoC,MAAM,EACNC,OAAO,KAAK,EACZC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,iBAAiB,EACjBC,cAAc,OAAO,EACrBC,WAAW,EACXC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACR,GAAGC,MACQ,GAxBb;IAwCA,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGtB;IAC9D,IAAIT;IACJ,IAAIgC,IAAIJ,WAAWtE;IACnB,IAAI,cAAc0E,GAAG;QACnBhC,SAASgC;IACX,OAAO;QACL,MAAMpD,WAAW;eAAIoD,EAAErD,WAAW;eAAKqD,EAAEC,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMzD,cAAcqD,EAAErD,WAAW,CAACuD,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrDpC,SAAS;YAAE,GAAGgC,CAAC;YAAEpD;YAAUD;QAAY;IACzC;IAEA,IAAI,OAAOoD,kBAAkB,aAAa;QACxC,MAAM,IAAIM,MACR;IAEJ;IACA,IAAIlC,SAAgCwB,KAAKxB,MAAM,IAAI4B;IAEnD,sDAAsD;IACtD,OAAOJ,KAAKxB,MAAM;IAClB,OAAO,AAACwB,KAAavB,MAAM;IAE3B,6CAA6C;IAC7C,oDAAoD;IACpD,MAAMkC,kBAAkB,wBAAwBnC;IAEhD,IAAImC,iBAAiB;QACnB,IAAItC,OAAOG,MAAM,KAAK,UAAU;YAC9B,MAAM,IAAIkC,MACR,AAAC,qBAAkB3E,MAAI,gCACpB;QAEP;IACF,OAAO;QACL,8CAA8C;QAC9C,+CAA+C;QAC/C,iDAAiD;QACjD,MAAM6E,oBAAoBpC;QAC1BA,SAAS,CAACqC;YACR,MAAM,EAAExC,QAAQyC,CAAC,EAAE,GAAGC,MAAM,GAAGF;YAC/B,OAAOD,kBAAkBG;QAC3B;IACF;IAEA,IAAIpB,QAAQ;QACV,IAAIA,WAAW,QAAQ;YACrBR,OAAO;QACT;QACA,MAAM6B,gBAAoE;YACxEC,WAAW;gBAAEC,UAAU;gBAAQhC,QAAQ;YAAO;YAC9CiC,YAAY;gBAAErE,OAAO;gBAAQoC,QAAQ;YAAO;QAC9C;QACA,MAAMkC,gBAAoD;YACxDD,YAAY;YACZhC,MAAM;QACR;QACA,MAAMkC,cAAcL,aAAa,CAACrB,OAAO;QACzC,IAAI0B,aAAa;YACfjC,QAAQ;gBAAE,GAAGA,KAAK;gBAAE,GAAGiC,WAAW;YAAC;QACrC;QACA,MAAMC,cAAcF,aAAa,CAACzB,OAAO;QACzC,IAAI2B,eAAe,CAACvE,OAAO;YACzBA,QAAQuE;QACV;IACF;IAEA,IAAIC,YAAY;IAChB,IAAIC,WAAWlF,OAAOQ;IACtB,IAAI2E,YAAYnF,OAAO4C;IACvB,IAAIwC;IACJ,IAAIC;IACJ,IAAIzF,eAAeH,MAAM;QACvB,MAAM6F,kBAAkB9F,gBAAgBC,OAAOA,IAAIC,OAAO,GAAGD;QAE7D,IAAI,CAAC6F,gBAAgB7F,GAAG,EAAE;YACxB,MAAM,IAAI2E,MACR,AAAC,gJAA6ImB,KAAKC,SAAS,CAC1JF;QAGN;QACA,IAAI,CAACA,gBAAgB1C,MAAM,IAAI,CAAC0C,gBAAgB9E,KAAK,EAAE;YACrD,MAAM,IAAI4D,MACR,AAAC,6JAA0JmB,KAAKC,SAAS,CACvKF;QAGN;QAEAF,YAAYE,gBAAgBF,SAAS;QACrCC,aAAaC,gBAAgBD,UAAU;QACvClC,cAAcA,eAAemC,gBAAgBnC,WAAW;QACxD8B,YAAYK,gBAAgB7F,GAAG;QAE/B,IAAI,CAACoD,MAAM;YACT,IAAI,CAACqC,YAAY,CAACC,WAAW;gBAC3BD,WAAWI,gBAAgB9E,KAAK;gBAChC2E,YAAYG,gBAAgB1C,MAAM;YACpC,OAAO,IAAIsC,YAAY,CAACC,WAAW;gBACjC,MAAMM,QAAQP,WAAWI,gBAAgB9E,KAAK;gBAC9C2E,YAAYhE,KAAKuE,KAAK,CAACJ,gBAAgB1C,MAAM,GAAG6C;YAClD,OAAO,IAAI,CAACP,YAAYC,WAAW;gBACjC,MAAMM,QAAQN,YAAYG,gBAAgB1C,MAAM;gBAChDsC,WAAW/D,KAAKuE,KAAK,CAACJ,gBAAgB9E,KAAK,GAAGiF;YAChD;QACF;IACF;IACAhG,MAAM,OAAOA,QAAQ,WAAWA,MAAMwF;IAEtC,IAAIU,SACF,CAAClD,YAAaC,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAI,CAACjD,OAAOA,IAAImG,UAAU,CAAC,YAAYnG,IAAImG,UAAU,CAAC,UAAU;QAC9D,uEAAuE;QACvE5D,cAAc;QACd2D,SAAS;IACX;IACA,IAAI5D,OAAOC,WAAW,EAAE;QACtBA,cAAc;IAChB;IACA,IAAIqC,mBAAmB5E,IAAIoG,QAAQ,CAAC,WAAW,CAAC9D,OAAO+D,mBAAmB,EAAE;QAC1E,yDAAyD;QACzD,+CAA+C;QAC/C9D,cAAc;IAChB;IACA,IAAIS,UAAU;QACZW,gBAAgB;IAClB;IAEA,MAAM2C,aAAa/F,OAAOiC;IAE1B,IAAI+D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAInE,OAAOoE,MAAM,KAAK,YAAY9B,mBAAmB,CAACrC,aAAa;YACjE,MAAM,IAAIoC,MACP;QAML;QACA,IAAI,CAAC3E,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CuC,cAAc;QAChB,OAAO;YACL,IAAIa,MAAM;gBACR,IAAIrC,OAAO;oBACT,MAAM,IAAI4D,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAImD,QAAQ;oBACV,MAAM,IAAIwB,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIqD,CAAAA,yBAAAA,MAAOsD,QAAQ,KAAItD,MAAMsD,QAAQ,KAAK,YAAY;oBACpD,MAAM,IAAIhC,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIqD,CAAAA,yBAAAA,MAAOtC,KAAK,KAAIsC,MAAMtC,KAAK,KAAK,QAAQ;oBAC1C,MAAM,IAAI4D,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;gBACA,IAAIqD,CAAAA,yBAAAA,MAAOF,MAAM,KAAIE,MAAMF,MAAM,KAAK,QAAQ;oBAC5C,MAAM,IAAIwB,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B;YACF,OAAO;gBACL,IAAI,OAAOyF,aAAa,aAAa;oBACnC,MAAM,IAAId,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B,OAAO,IAAI4G,MAAMnB,WAAW;oBAC1B,MAAM,IAAId,MACR,AAAC,qBAAkB3E,MAAI,sFAAmFe,QAAM;gBAEpH;gBACA,IAAI,OAAO2E,cAAc,aAAa;oBACpC,MAAM,IAAIf,MACR,AAAC,qBAAkB3E,MAAI;gBAE3B,OAAO,IAAI4G,MAAMlB,YAAY;oBAC3B,MAAM,IAAIf,MACR,AAAC,qBAAkB3E,MAAI,uFAAoFmD,SAAO;gBAEtH;YACF;QACF;QACA,IAAI,CAACtD,qBAAqBgH,QAAQ,CAAC5D,UAAU;YAC3C,MAAM,IAAI0B,MACR,AAAC,qBAAkB3E,MAAI,iDAA8CiD,UAAQ,wBAAqBpD,qBAAqBoC,GAAG,CACxH6E,QACAjE,IAAI,CAAC,OAAK;QAEhB;QACA,IAAIG,YAAYC,YAAY,QAAQ;YAClC,MAAM,IAAI0B,MACR,AAAC,qBAAkB3E,MAAI;QAE3B;QACA,IACEyD,gBAAgB,WAChBA,gBAAgB,UAChB,CAACA,YAAY0C,UAAU,CAAC,gBACxB;YACA,MAAM,IAAIxB,MACR,AAAC,qBAAkB3E,MAAI,2CAAwCyD,cAAY;QAE/E;QACA,IAAIA,gBAAgB,SAAS;YAC3B,IAAIgC,YAAYC,aAAaD,WAAWC,YAAY,MAAM;gBACxDhG,SACE,AAAC,qBAAkBM,MAAI;YAE3B;QACF;QACA,IAAIyD,gBAAgB,UAAU,CAACC,aAAa;YAC1C,MAAMqD,iBAAiB;gBAAC;gBAAQ;gBAAO;gBAAQ;aAAO,CAAC,iCAAiC;;YAExF,MAAM,IAAIpC,MACR,AAAC,qBAAkB3E,MAAI,6TAGkE+G,eAAelE,IAAI,CACxG,OACA;QAIR;QACA,IAAI,SAASoB,MAAM;YACjBvE,SACE,AAAC,qBAAkBM,MAAI;QAE3B;QAEA,IAAI,CAACuC,eAAe,CAACqC,iBAAiB;YACpC,MAAMoC,SAASvE,OAAO;gBACpBH;gBACAtC;gBACAe,OAAO0E,YAAY;gBACnBjD,SAAS8D,cAAc;YACzB;YACA,IAAIW;YACJ,IAAI;gBACFA,MAAM,IAAIC,IAAIF;YAChB,EAAE,OAAOG,KAAK,CAAC;YACf,IAAIH,WAAWhH,OAAQiH,OAAOA,IAAIG,QAAQ,KAAKpH,OAAO,CAACiH,IAAII,MAAM,EAAG;gBAClE3H,SACE,AAAC,qBAAkBM,MAAI,4HACpB;YAEP;QACF;QAEA,IAAIwD,mBAAmB;YACrB9D,SACE,AAAC,qBAAkBM,MAAI;QAE3B;QAEA,KAAK,MAAM,CAACsH,WAAWC,YAAY,IAAIC,OAAOC,OAAO,CAAC;YACpD7D;YACAC;YACAC;YACAC;YACAC;QACF,GAAI;YACF,IAAIuD,aAAa;gBACf7H,SACE,AAAC,qBAAkBM,MAAI,wBAAqBsH,YAAU,0CACnD;YAEP;QACF;QAEA,IACE,OAAOI,WAAW,eAClB,CAACpH,gBACDoH,OAAOC,mBAAmB,EAC1B;YACArH,eAAe,IAAIqH,oBAAoB,CAACC;gBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;wBAE3BD;oBADf,0EAA0E;oBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgB7H,GAAG,KAAI;oBACtC,MAAMiI,WAAW7H,QAAQ8H,GAAG,CAACH;oBAC7B,IACEE,YACA,CAACA,SAASjF,QAAQ,IAClBiF,SAASxE,WAAW,KAAK,WACzB,CAACwE,SAASjI,GAAG,CAACmG,UAAU,CAAC,YACzB,CAAC8B,SAASjI,GAAG,CAACmG,UAAU,CAAC,UACzB;wBACA,iDAAiD;wBACjDzG,SACE,AAAC,qBAAkBuI,SAASjI,GAAG,GAAC,8HAC7B;oBAEP;gBACF;YACF;YACA,IAAI;gBACFM,aAAa6H,OAAO,CAAC;oBACnBC,MAAM;oBACNC,UAAU;gBACZ;YACF,EAAE,OAAOlB,KAAK;gBACZ,oCAAoC;gBACpCmB,QAAQC,KAAK,CAACpB;YAChB;QACF;IACF;IACA,MAAMqB,WAAWhB,OAAOiB,MAAM,CAC5BrF,OACI;QACEuD,UAAU;QACVxD,QAAQ;QACRpC,OAAO;QACP2H,MAAM;QACNC,KAAK;QACLC,OAAO;QACPC,QAAQ;QACRhF;QACAC;IACF,IACA,CAAC,GACLK,cAAc,CAAC,IAAI;QAAE2E,OAAO;IAAc,GAC1CzF;IAGF,MAAM0F,kBACJ,CAAC3E,gBAAgBX,gBAAgB,UAC7BA,gBAAgB,SACd,AAAC,2CAAwC9D,gBAAgB;QACvD8F;QACAC;QACAC;QACAC;QACAlC,aAAaA,eAAe;QAC5BG,WAAW2E,SAAS3E,SAAS;IAC/B,KAAG,OACH,AAAC,UAAOJ,cAAY,KAAI,uBAAuB;OACjD;IAEN,IAAIuF,mBAAmBD,kBACnB;QACEE,gBAAgBT,SAAS3E,SAAS,IAAI;QACtCqF,oBAAoBV,SAAS1E,cAAc,IAAI;QAC/CqF,kBAAkB;QAClBJ;IACF,IACA,CAAC;IAEL,IAAIxC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IACEuC,iBAAiBD,eAAe,IAChCtF,gBAAgB,WAChBC,+BAAAA,YAAayC,UAAU,CAAC,OACxB;YACA,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF6C,iBAAiBD,eAAe,GAAG,AAAC,UAAOrF,cAAY;QACzD;IACF;IAEA,MAAM0F,gBAAgB/G,iBAAiB;QACrCC;QACAtC;QACAuC;QACAxB,OAAO0E;QACPjD,SAAS8D;QACTtF;QACAyB;IACF;IAEA,IAAI8D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAOiB,WAAW,aAAa;YACjC,IAAI2B;YACJ,IAAI;gBACFA,UAAU,IAAInC,IAAIkC,cAAcpJ,GAAG;YACrC,EAAE,OAAOsJ,GAAG;gBACVD,UAAU,IAAInC,IAAIkC,cAAcpJ,GAAG,EAAE0H,OAAO6B,QAAQ,CAACC,IAAI;YAC3D;YACApJ,QAAQqJ,GAAG,CAACJ,QAAQG,IAAI,EAAE;gBAAExJ;gBAAKgD;gBAAUS;YAAY;QACzD;IACF;IAEA,MAAMiG,QAAkB;QACtB,GAAGzF,IAAI;QACPhB,SAASiD,SAAS,SAASjD;QAC3BU;QACA5C,OAAO0E;QACPtC,QAAQuC;QACRiE,UAAU;QACVzG;QACAG,OAAO;YAAE,GAAGmF,QAAQ;YAAE,GAAGQ,gBAAgB;QAAC;QAC1ChI,OAAOoI,cAAcpI,KAAK;QAC1B0B,QAAQ0G,cAAc1G,MAAM;QAC5B1C,KAAKsD,eAAe8F,cAAcpJ,GAAG;IACvC;IACA,MAAM4J,OAAO;QAAErH;QAAaS;QAAUS;QAAaL;IAAK;IACxD,OAAO;QAAEsG;QAAOE;IAAK;AACvB"}