{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/route-regex.ts"], "names": ["INTERCEPTION_ROUTE_MARKERS", "escapeStringRegexp", "removeTrailingSlash", "NEXT_QUERY_PARAM_PREFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "parseParameter", "param", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getParametrizedRoute", "route", "segments", "split", "groups", "groupIndex", "parameterizedRoute", "map", "segment", "markerMatch", "find", "m", "paramMatch<PERSON>", "match", "pos", "join", "getRouteRegex", "normalizedRoute", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "interceptionPrefix", "getNamedParametrizedRoute", "prefixRouteKeys", "namedParameterizedRoute", "hasInterceptionMarker", "some", "usedMarker", "undefined", "getNamedRouteRegex", "prefixRouteKey", "result", "namedRegex", "getNamedMiddlewareRegex", "options", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": "AAAA,SAASA,0BAA0B,QAAQ,wDAAuD;AAClG,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,mBAAmB,QAAQ,0BAAyB;AAE7D,MAAMC,0BAA0B;AAChC,MAAMC,kCAAkC;AAaxC;;;;;;;CAOC,GACD,SAASC,eAAeC,KAAa;IACnC,MAAMC,WAAWD,MAAME,UAAU,CAAC,QAAQF,MAAMG,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZD,QAAQA,MAAMI,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMC,SAASL,MAAME,UAAU,CAAC;IAChC,IAAIG,QAAQ;QACVL,QAAQA,MAAMI,KAAK,CAAC;IACtB;IACA,OAAO;QAAEE,KAAKN;QAAOK;QAAQJ;IAAS;AACxC;AAEA,SAASM,qBAAqBC,KAAa;IACzC,MAAMC,WAAWb,oBAAoBY,OAAOJ,KAAK,CAAC,GAAGM,KAAK,CAAC;IAC3D,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IACjB,OAAO;QACLC,oBAAoBJ,SACjBK,GAAG,CAAC,CAACC;YACJ,MAAMC,cAActB,2BAA2BuB,IAAI,CAAC,CAACC,IACnDH,QAAQb,UAAU,CAACgB;YAErB,MAAMC,eAAeJ,QAAQK,KAAK,CAAC,uBAAuB,uBAAuB;;YAEjF,IAAIJ,eAAeG,cAAc;gBAC/B,MAAM,EAAEb,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGN,eAAeoB,YAAY,CAAC,EAAE;gBAChER,MAAM,CAACL,IAAI,GAAG;oBAAEe,KAAKT;oBAAcP;oBAAQJ;gBAAS;gBACpD,OAAO,AAAC,MAAGN,mBAAmBqB,eAAa;YAC7C,OAAO,IAAIG,cAAc;gBACvB,MAAM,EAAEb,GAAG,EAAED,MAAM,EAAEJ,QAAQ,EAAE,GAAGF,eAAeoB,YAAY,CAAC,EAAE;gBAChER,MAAM,CAACL,IAAI,GAAG;oBAAEe,KAAKT;oBAAcP;oBAAQJ;gBAAS;gBACpD,OAAOI,SAAUJ,WAAW,gBAAgB,WAAY;YAC1D,OAAO;gBACL,OAAO,AAAC,MAAGN,mBAAmBoB;YAChC;QACF,GACCO,IAAI,CAAC;QACRX;IACF;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASY,cAAcC,eAAuB;IACnD,MAAM,EAAEX,kBAAkB,EAAEF,MAAM,EAAE,GAAGJ,qBAAqBiB;IAC5D,OAAO;QACLC,IAAI,IAAIC,OAAO,AAAC,MAAGb,qBAAmB;QACtCF,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAASgB;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAM,AAACF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAC,AAACJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAY9B;IAZ8B,IAAA,EAC7BC,kBAAkB,EAClBC,eAAe,EACftB,OAAO,EACPuB,SAAS,EACTC,SAAS,EAOV,GAZ8B;IAa7B,MAAM,EAAEjC,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGN,eAAegB;IAEjD,uDAAuD;IACvD,kBAAkB;IAClB,IAAIyB,aAAalC,IAAImC,OAAO,CAAC,OAAO;IAEpC,IAAIF,WAAW;QACbC,aAAa,AAAC,KAAED,YAAYC;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWpC,KAAK,CAAC,GAAG,MAAM;QAC5CsC,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaH;IACf;IAEA,IAAIE,WAAW;QACbD,SAAS,CAACE,WAAW,GAAG,AAAC,KAAED,YAAYjC;IACzC,OAAO;QACLgC,SAAS,CAACE,WAAW,GAAGlC;IAC1B;IAEA,wFAAwF;IACxF,0FAA0F;IAC1F,qFAAqF;IACrF,MAAMwC,qBAAqBV,qBACvBzC,mBAAmByC,sBACnB;IAEJ,OAAO/B,SACHJ,WACE,AAAC,SAAM6C,qBAAmB,QAAKN,aAAW,YAC1C,AAAC,MAAGM,qBAAmB,QAAKN,aAAW,UACzC,AAAC,MAAGM,qBAAmB,QAAKN,aAAW;AAC7C;AAEA,SAASO,0BAA0BvC,KAAa,EAAEwC,eAAwB;IACxE,MAAMvC,WAAWb,oBAAoBY,OAAOJ,KAAK,CAAC,GAAGM,KAAK,CAAC;IAC3D,MAAM2B,kBAAkBV;IACxB,MAAMW,YAAyC,CAAC;IAChD,OAAO;QACLW,yBAAyBxC,SACtBK,GAAG,CAAC,CAACC;YACJ,MAAMmC,wBAAwBxD,2BAA2ByD,IAAI,CAAC,CAACjC,IAC7DH,QAAQb,UAAU,CAACgB;YAErB,MAAMC,eAAeJ,QAAQK,KAAK,CAAC,uBAAuB,uBAAuB;;YAEjF,IAAI8B,yBAAyB/B,cAAc;gBACzC,MAAM,CAACiC,WAAW,GAAGrC,QAAQL,KAAK,CAACS,YAAY,CAAC,EAAE;gBAElD,OAAOgB,sBAAsB;oBAC3BE;oBACAD,oBAAoBgB;oBACpBrC,SAASI,YAAY,CAAC,EAAE;oBACxBmB;oBACAC,WAAWS,kBACPlD,kCACAuD;gBACN;YACF,OAAO,IAAIlC,cAAc;gBACvB,OAAOgB,sBAAsB;oBAC3BE;oBACAtB,SAASI,YAAY,CAAC,EAAE;oBACxBmB;oBACAC,WAAWS,kBAAkBnD,0BAA0BwD;gBACzD;YACF,OAAO;gBACL,OAAO,AAAC,MAAG1D,mBAAmBoB;YAChC;QACF,GACCO,IAAI,CAAC;QACRgB;IACF;AACF;AAEA;;;;;;;CAOC,GACD,OAAO,SAASgB,mBACd9B,eAAuB,EACvB+B,cAAuB;IAEvB,MAAMC,SAAST,0BAA0BvB,iBAAiB+B;IAC1D,OAAO;QACL,GAAGhC,cAAcC,gBAAgB;QACjCiC,YAAY,AAAC,MAAGD,OAAOP,uBAAuB,GAAC;QAC/CX,WAAWkB,OAAOlB,SAAS;IAC7B;AACF;AAEA;;;CAGC,GACD,OAAO,SAASoB,wBACdlC,eAAuB,EACvBmC,OAEC;IAED,MAAM,EAAE9C,kBAAkB,EAAE,GAAGN,qBAAqBiB;IACpD,MAAM,EAAEoC,WAAW,IAAI,EAAE,GAAGD;IAC5B,IAAI9C,uBAAuB,KAAK;QAC9B,IAAIgD,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLH,YAAY,AAAC,OAAII,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEZ,uBAAuB,EAAE,GAAGF,0BAClCvB,iBACA;IAEF,IAAIsC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLH,YAAY,AAAC,MAAGR,0BAA0Ba,uBAAqB;IACjE;AACF"}