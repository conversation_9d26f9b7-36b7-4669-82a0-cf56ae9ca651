{"version": 3, "sources": ["../../src/lib/verifyAndLint.ts"], "names": ["red", "Worker", "existsSync", "join", "ESLINT_DEFAULT_DIRS", "eventLintCheckCompleted", "CompileError", "isError", "verifyAndLint", "dir", "cacheLocation", "configLintDirs", "enableWorkerThreads", "telemetry", "lintWorkers", "require", "resolve", "numWorkers", "maxRetries", "getStdout", "pipe", "process", "stdout", "getStderr", "stderr", "lintDirs", "reduce", "res", "d", "currDir", "push", "lintResults", "runLintCheck", "lintDuringBuild", "eslintOptions", "lintOutput", "output", "eventInfo", "record", "buildLint", "flush", "console", "log", "end", "err", "type", "error", "message", "exit"], "mappings": "AAAA,SAASA,GAAG,QAAQ,eAAc;AAClC,SAASC,MAAM,QAAQ,iCAAgC;AACvD,SAASC,UAAU,QAAQ,KAAI;AAC/B,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,mBAAmB,QAAQ,cAAa;AAEjD,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,OAAOC,aAAa,aAAY;AAEhC,OAAO,eAAeC,cACpBC,GAAW,EACXC,aAAqB,EACrBC,cAAoC,EACpCC,mBAAwC,EACxCC,SAAoB;IAEpB,IAAI;QACF,MAAMC,cAAc,IAAIb,OAAOc,QAAQC,OAAO,CAAC,0BAA0B;YACvEC,YAAY;YACZL;YACAM,YAAY;QACd;QAIAJ,YAAYK,SAAS,GAAGC,IAAI,CAACC,QAAQC,MAAM;QAC3CR,YAAYS,SAAS,GAAGH,IAAI,CAACC,QAAQG,MAAM;QAE3C,MAAMC,WAAW,AAACd,CAAAA,kBAAkBP,mBAAkB,EAAGsB,MAAM,CAC7D,CAACC,KAAeC;YACd,MAAMC,UAAU1B,KAAKM,KAAKmB;YAC1B,IAAI,CAAC1B,WAAW2B,UAAU,OAAOF;YACjCA,IAAIG,IAAI,CAACD;YACT,OAAOF;QACT,GACA,EAAE;QAGJ,MAAMI,cAAc,MAAMjB,YAAYkB,YAAY,CAACvB,KAAKgB,UAAU;YAChEQ,iBAAiB;YACjBC,eAAe;gBACbxB;YACF;QACF;QACA,MAAMyB,aACJ,OAAOJ,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaK,MAAM;QAErE,IAAI,OAAOL,gBAAgB,aAAYA,+BAAAA,YAAaM,SAAS,GAAE;YAC7DxB,UAAUyB,MAAM,CACdjC,wBAAwB;gBACtB,GAAG0B,YAAYM,SAAS;gBACxBE,WAAW;YACb;QAEJ;QAEA,IAAI,OAAOR,gBAAgB,aAAYA,+BAAAA,YAAaxB,OAAO,KAAI4B,YAAY;YACzE,MAAMtB,UAAU2B,KAAK;YACrB,MAAM,IAAIlC,aAAa6B;QACzB;QAEA,IAAIA,YAAY;YACdM,QAAQC,GAAG,CAACP;QACd;QAEArB,YAAY6B,GAAG;IACjB,EAAE,OAAOC,KAAK;QACZ,IAAIrC,QAAQqC,MAAM;YAChB,IAAIA,IAAIC,IAAI,KAAK,kBAAkBD,eAAetC,cAAc;gBAC9DmC,QAAQK,KAAK,CAAC9C,IAAI;gBAClByC,QAAQK,KAAK,CAACF,IAAIG,OAAO;gBACzB1B,QAAQ2B,IAAI,CAAC;YACf,OAAO,IAAIJ,IAAIC,IAAI,KAAK,cAAc;gBACpCJ,QAAQK,KAAK,CAACF,IAAIG,OAAO;gBACzB1B,QAAQ2B,IAAI,CAAC;YACf;QACF;QACA,MAAMJ;IACR;AACF"}