{"version": 3, "sources": ["../../trace/trace.ts"], "names": ["NUM_OF_MICROSEC_IN_NANOSEC", "BigInt", "count", "getId", "SpanStatus", "Started", "Stopped", "Span", "constructor", "name", "parentId", "attrs", "startTime", "duration", "status", "id", "_start", "process", "hrtime", "bigint", "now", "Date", "stop", "stopTime", "end", "Number", "MAX_SAFE_INTEGER", "Error", "timestamp", "reporter", "report", "<PERSON><PERSON><PERSON><PERSON>", "manualTraceChild", "span", "setAttribute", "key", "value", "String", "traceFn", "fn", "traceAsyncFn", "trace", "flushAllTraces", "flushAll"], "mappings": "AAAA;;;;;AACyB,IAAA,OAAU,WAAV,UAAU,CAAA;AAEnC,MAAMA,0BAA0B,GAAGC,MAAM,CAAC,MAAM,CAAC;AACjD,IAAIC,KAAK,GAAG,CAAC;AACb,MAAMC,KAAK,GAAG,IAAM;IAClBD,KAAK,EAAE;IACP,OAAOA,KAAK,CAAA;CACb;IAIM,UAGN;;UAHWE,UAAU;IAAVA,UAAU,CAAVA,UAAU,CACpBC,SAAO,IAAPA,CAAO,IAAPA,SAAO;IADGD,UAAU,CAAVA,UAAU,CAEpBE,SAAO,IAAPA,CAAO,IAAPA,SAAO;GAFGF,UAAU,0BAAVA,UAAU;AAKf,MAAMG,IAAI;IAWfC,YAAY,EACVC,IAAI,CAAA,EACJC,QAAQ,CAAA,EACRC,KAAK,CAAA,EACLC,SAAS,CAAA,EAMV,CAAE;QACD,IAAI,CAACH,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACG,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACF,KAAK,GAAGA,KAAK,GAAG;YAAE,GAAGA,KAAK;SAAE,GAAG,EAAE;QACtC,IAAI,CAACG,MAAM,GA9BbT,CAAO,AA8B2B;QAChC,IAAI,CAACU,EAAE,GAAGZ,KAAK,EAAE;QACjB,IAAI,CAACa,MAAM,GAAGJ,SAAS,IAAIK,OAAO,CAACC,MAAM,CAACC,MAAM,EAAE;QAClD,wEAAwE;QACxE,iDAAiD;QACjD,2IAA2I;QAC3I,wDAAwD;QACxD,iFAAiF;QACjF,IAAI,CAACC,GAAG,GAAGC,IAAI,CAACD,GAAG,EAAE;KACtB;IAED,yEAAyE;IACzE,+DAA+D;IAC/D,wEAAwE;IACxE,yCAAyC;IACzCE,IAAI,CAACC,QAAiB,EAAE;QACtB,MAAMC,GAAG,GAAWD,QAAQ,IAAIN,OAAO,CAACC,MAAM,CAACC,MAAM,EAAE;QACvD,MAAMN,QAAQ,GAAG,CAACW,GAAG,GAAG,IAAI,CAACR,MAAM,CAAC,GAAGhB,0BAA0B;QACjE,IAAI,CAACc,MAAM,GA/CbR,CAAO,AA+C2B;QAChC,IAAIO,QAAQ,GAAGY,MAAM,CAACC,gBAAgB,EAAE;YACtC,MAAM,IAAIC,KAAK,CAAC,CAAC,4CAA4C,EAAEd,QAAQ,CAAC,CAAC,CAAC,CAAA;SAC3E;QACD,MAAMe,SAAS,GAAG,IAAI,CAACZ,MAAM,GAAGhB,0BAA0B;QAC1D6B,OAAQ,SAAA,CAACC,MAAM,CACb,IAAI,CAACrB,IAAI,EACTgB,MAAM,CAACZ,QAAQ,CAAC,EAChBY,MAAM,CAACG,SAAS,CAAC,EACjB,IAAI,CAACb,EAAE,EACP,IAAI,CAACL,QAAQ,EACb,IAAI,CAACC,KAAK,EACV,IAAI,CAACS,GAAG,CACT;KACF;IAEDW,UAAU,CAACtB,IAAY,EAAEE,KAAc,EAAE;QACvC,OAAO,IAAIJ,IAAI,CAAC;YAAEE,IAAI;YAAEC,QAAQ,EAAE,IAAI,CAACK,EAAE;YAAEJ,KAAK;SAAE,CAAC,CAAA;KACpD;IAEDqB,gBAAgB,CACdvB,IAAY,EACZG,SAAiB,EACjBW,QAAgB,EAChBZ,KAAc,EACd;QACA,MAAMsB,IAAI,GAAG,IAAI1B,IAAI,CAAC;YAAEE,IAAI;YAAEC,QAAQ,EAAE,IAAI,CAACK,EAAE;YAAEJ,KAAK;YAAEC,SAAS;SAAE,CAAC;QACpEqB,IAAI,CAACX,IAAI,CAACC,QAAQ,CAAC;KACpB;IAEDW,YAAY,CAACC,GAAW,EAAEC,KAAU,EAAE;QACpC,IAAI,CAACzB,KAAK,CAACwB,GAAG,CAAC,GAAGE,MAAM,CAACD,KAAK,CAAC;KAChC;IAEDE,OAAO,CAAIC,EAAW,EAAK;QACzB,IAAI;YACF,OAAOA,EAAE,EAAE,CAAA;SACZ,QAAS;YACR,IAAI,CAACjB,IAAI,EAAE;SACZ;KACF;IAED,MAAMkB,YAAY,CAAID,EAAwB,EAAc;QAC1D,IAAI;YACF,OAAO,MAAMA,EAAE,EAAE,CAAA;SAClB,QAAS;YACR,IAAI,CAACjB,IAAI,EAAE;SACZ;KACF;CACF;QA7FYf,IAAI,GAAJA,IAAI;AA+FV,MAAMkC,KAAK,GAAG,CACnBhC,IAAY,EACZC,QAAiB,EACjBC,KAAiC,GAC9B;IACH,OAAO,IAAIJ,IAAI,CAAC;QAAEE,IAAI;QAAEC,QAAQ;QAAEC,KAAK;KAAE,CAAC,CAAA;CAC3C;QANY8B,KAAK,GAALA,KAAK;AAQX,MAAMC,cAAc,GAAG,IAAMb,OAAQ,SAAA,CAACc,QAAQ,EAAE;QAA1CD,cAAc,GAAdA,cAAc"}