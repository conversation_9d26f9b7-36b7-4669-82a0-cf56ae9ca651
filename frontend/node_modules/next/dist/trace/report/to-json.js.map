{"version": 3, "sources": ["../../../trace/report/to-json.ts"], "names": ["batcher", "localEndpoint", "serviceName", "ipv4", "port", "reportEvents", "events", "queue", "Set", "flushAll", "Promise", "all", "length", "report", "event", "push", "evts", "slice", "add", "then", "delete", "writeStream", "traceId", "batch", "writeStreamOptions", "flags", "encoding", "RotatingWriteStream", "constructor", "file", "sizeLimit", "size", "createWriteStream", "fs", "rotate", "end", "unlinkSync", "err", "code", "rotatePromise", "undefined", "write", "data", "drainPromise", "resolve", "_reject", "once", "reportToLocalHost", "name", "duration", "timestamp", "id", "parentId", "attrs", "startTime", "distDir", "traceGlobals", "get", "phase", "process", "env", "TRACE_ID", "randomBytes", "toString", "promises", "mkdir", "recursive", "path", "join", "PHASE_DEVELOPMENT_SERVER", "Infinity", "eventsJson", "JSON", "stringify", "console", "log", "tags"], "mappings": "AAAA;;;;QAyBgBA,OAAO,GAAPA,OAAO;;AAzBK,IAAA,OAAQ,WAAR,QAAQ,CAAA;AACP,IAAA,OAAW,WAAX,WAAW,CAAA;AACzB,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACkB,IAAA,UAA4B,WAA5B,4BAA4B,CAAA;;;;;;AAErE,MAAMC,aAAa,GAAG;IACpBC,WAAW,EAAE,QAAQ;IACrBC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;CACX;AAeM,SAASJ,OAAO,CAACK,YAA8C,EAAE;IACtE,MAAMC,MAAM,GAAY,EAAE;IAC1B,6DAA6D;IAC7D,MAAMC,KAAK,GAAG,IAAIC,GAAG,EAAE;IACvB,OAAO;QACLC,QAAQ,EAAE,UAAY;YACpB,MAAMC,OAAO,CAACC,GAAG,CAACJ,KAAK,CAAC;YACxB,IAAID,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAMP,YAAY,CAACC,MAAM,CAAC;gBAC1BA,MAAM,CAACM,MAAM,GAAG,CAAC;aAClB;SACF;QACDC,MAAM,EAAE,CAACC,KAAY,GAAK;YACxBR,MAAM,CAACS,IAAI,CAACD,KAAK,CAAC;YAElB,IAAIR,MAAM,CAACM,MAAM,GAAG,GAAG,EAAE;gBACvB,MAAMI,IAAI,GAAGV,MAAM,CAACW,KAAK,EAAE;gBAC3BX,MAAM,CAACM,MAAM,GAAG,CAAC;gBACjB,MAAMC,MAAM,GAAGR,YAAY,CAACW,IAAI,CAAC;gBACjCT,KAAK,CAACW,GAAG,CAACL,MAAM,CAAC;gBACjBA,MAAM,CAACM,IAAI,CAAC,IAAMZ,KAAK,CAACa,MAAM,CAACP,MAAM,CAAC,CAAC;aACxC;SACF;KACF,CAAA;CACF;AAED,IAAIQ,WAAW,AAAqB;AACpC,IAAIC,OAAO,AAAQ;AACnB,IAAIC,KAAK,AAAwC;AAEjD,MAAMC,kBAAkB,GAAG;IACzBC,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE,MAAM;CACjB;AACD,MAAMC,mBAAmB;IAOvBC,YAAYC,IAAY,EAAEC,SAAiB,CAAE;QAC3C,IAAI,CAACD,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACE,IAAI,GAAG,CAAC;QACb,IAAI,CAACD,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACE,iBAAiB,EAAE;KACzB;IACD,AAAQA,iBAAiB,GAAG;QAC1B,IAAI,CAACX,WAAW,GAAGY,GAAE,QAAA,CAACD,iBAAiB,CAAC,IAAI,CAACH,IAAI,EAAEL,kBAAkB,CAAC;KACvE;IACD,oBAAoB;IACpB,MAAcU,MAAM,GAAG;QACrB,MAAM,IAAI,CAACC,GAAG,EAAE;QAChB,IAAI;YACFF,GAAE,QAAA,CAACG,UAAU,CAAC,IAAI,CAACP,IAAI,CAAC;SACzB,CAAC,OAAOQ,GAAG,EAAO;YACjB,2CAA2C;YAC3C,IAAIA,GAAG,CAACC,IAAI,KAAK,QAAQ,EAAE;gBACzB,MAAMD,GAAG,CAAA;aACV;SACF;QACD,IAAI,CAACN,IAAI,GAAG,CAAC;QACb,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACO,aAAa,GAAGC,SAAS;KAC/B;IACD,MAAMC,KAAK,CAACC,IAAY,EAAiB;QACvC,IAAI,IAAI,CAACH,aAAa,EAAE,MAAM,IAAI,CAACA,aAAa;QAEhD,IAAI,CAACR,IAAI,IAAIW,IAAI,CAAC9B,MAAM;QACxB,IAAI,IAAI,CAACmB,IAAI,GAAG,IAAI,CAACD,SAAS,EAAE;YAC9B,MAAM,CAAC,IAAI,CAACS,aAAa,GAAG,IAAI,CAACL,MAAM,EAAE,CAAC;SAC3C;QAED,IAAI,CAAC,IAAI,CAACb,WAAW,CAACoB,KAAK,CAACC,IAAI,EAAE,MAAM,CAAC,EAAE;YACzC,IAAI,IAAI,CAACC,YAAY,KAAKH,SAAS,EAAE;gBACnC,IAAI,CAACG,YAAY,GAAG,IAAIjC,OAAO,CAAO,CAACkC,OAAO,EAAEC,OAAO,GAAK;oBAC1D,IAAI,CAACxB,WAAW,CAACyB,IAAI,CAAC,OAAO,EAAE,IAAM;wBACnC,IAAI,CAACH,YAAY,GAAGH,SAAS;wBAC7BI,OAAO,EAAE;qBACV,CAAC;iBACH,CAAC;aACH;YACD,MAAM,IAAI,CAACD,YAAY;SACxB;KACF;IAEDR,GAAG,GAAkB;QACnB,OAAO,IAAIzB,OAAO,CAAC,CAACkC,OAAO,GAAK;YAC9B,IAAI,CAACvB,WAAW,CAACc,GAAG,CAACS,OAAO,CAAC;SAC9B,CAAC,CAAA;KACH;CACF;AAED,MAAMG,iBAAiB,GAAG,CACxBC,IAAY,EACZC,QAAgB,EAChBC,SAAiB,EACjBC,EAAU,EACVC,QAAiB,EACjBC,KAAc,EACdC,SAAkB,GACf;IACH,MAAMC,OAAO,GAAGC,OAAY,aAAA,CAACC,GAAG,CAAC,SAAS,CAAC;IAC3C,MAAMC,KAAK,GAAGF,OAAY,aAAA,CAACC,GAAG,CAAC,OAAO,CAAC;IACvC,IAAI,CAACF,OAAO,IAAI,CAACG,KAAK,EAAE;QACtB,OAAM;KACP;IAED,IAAI,CAACpC,OAAO,EAAE;QACZA,OAAO,GAAGqC,OAAO,CAACC,GAAG,CAACC,QAAQ,IAAIC,CAAAA,GAAAA,OAAW,AAAG,CAAA,YAAH,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;KACjE;IAED,IAAI,CAACxC,KAAK,EAAE;QACVA,KAAK,GAAGvB,OAAO,CAAC,OAAOM,MAAM,GAAK;YAChC,IAAI,CAACe,WAAW,EAAE;gBAChB,MAAMY,GAAE,QAAA,CAAC+B,QAAQ,CAACC,KAAK,CAACV,OAAO,EAAE;oBAAEW,SAAS,EAAE,IAAI;iBAAE,CAAC;gBACrD,MAAMrC,IAAI,GAAGsC,KAAI,QAAA,CAACC,IAAI,CAACb,OAAO,EAAE,OAAO,CAAC;gBACxClC,WAAW,GAAG,IAAIM,mBAAmB,CACnCE,IAAI,EACJ,0DAA0D;gBAC1D6B,KAAK,KAAKW,UAAwB,yBAAA,GAAG,QAAQ,GAAGC,QAAQ,CACzD;aACF;YACD,MAAMC,UAAU,GAAGC,IAAI,CAACC,SAAS,CAACnE,MAAM,CAAC;YACzC,IAAI;gBACF,MAAMe,WAAW,CAACoB,KAAK,CAAC8B,UAAU,GAAG,IAAI,CAAC;aAC3C,CAAC,OAAOlC,GAAG,EAAE;gBACZqC,OAAO,CAACC,GAAG,CAACtC,GAAG,CAAC;aACjB;SACF,CAAC;KACH;IAEDd,KAAK,CAACV,MAAM,CAAC;QACXS,OAAO;QACP8B,QAAQ;QACRJ,IAAI;QACJG,EAAE;QACFD,SAAS;QACTD,QAAQ;QACR2B,IAAI,EAAEvB,KAAK;QACXC,SAAS;KACV,CAAC;CACH;eAEc;IACb7C,QAAQ,EAAE,IACRc,KAAK,GACDA,KAAK,CAACd,QAAQ,EAAE,CAACU,IAAI,CAAC,IAAM;YAC1B,MAAMuC,KAAK,GAAGF,OAAY,aAAA,CAACC,GAAG,CAAC,OAAO,CAAC;YACvC,4DAA4D;YAC5D,IAAIC,KAAK,KAAKW,UAAwB,yBAAA,EAAE;gBACtChD,WAAW,CAACc,GAAG,EAAE;aAClB;SACF,CAAC,GACFK,SAAS;IACf3B,MAAM,EAAEkC,iBAAiB;CAC1B"}