{"version": 3, "sources": ["../../../trace/report/to-telemetry.ts"], "names": ["TRACE_EVENT_ACCESSLIST", "Map", "Object", "entries", "reportToTelemetry", "spanName", "duration", "eventName", "get", "telemetry", "traceGlobals", "record", "payload", "durationInMicroseconds", "flushAll", "report"], "mappings": "AAAA;;;;;AAA6B,IAAA,OAAW,WAAX,WAAW,CAAA;AAExC,MAAMA,sBAAsB,GAAG,IAAIC,GAAG,CACpCC,MAAM,CAACC,OAAO,CAAC;IACb,qBAAqB,EAAE,qBAAqB;CAC7C,CAAC,CACH;AAED,MAAMC,iBAAiB,GAAG,CAACC,QAAgB,EAAEC,QAAgB,GAAK;IAChE,MAAMC,SAAS,GAAGP,sBAAsB,CAACQ,GAAG,CAACH,QAAQ,CAAC;IACtD,IAAI,CAACE,SAAS,EAAE;QACd,OAAM;KACP;IACD,MAAME,SAAS,GAAGC,OAAY,aAAA,CAACF,GAAG,CAAC,WAAW,CAAC;IAC/C,IAAI,CAACC,SAAS,EAAE;QACd,OAAM;KACP;IAEDA,SAAS,CAACE,MAAM,CAAC;QACfJ,SAAS;QACTK,OAAO,EAAE;YACPC,sBAAsB,EAAEP,QAAQ;SACjC;KACF,CAAC;CACH;eAEc;IACbQ,QAAQ,EAAE,IAAM,EAAE;IAClBC,MAAM,EAAEX,iBAAiB;CAC1B"}