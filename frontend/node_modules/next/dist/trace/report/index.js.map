{"version": 3, "sources": ["../../../trace/report/index.ts"], "names": ["MultiReporter", "reporters", "constructor", "flushAll", "Promise", "all", "map", "reporter", "report", "spanName", "duration", "timestamp", "id", "parentId", "attrs", "startTime", "for<PERSON>ach", "reportToJson", "reportToTelemetry"], "mappings": "AAAA;;;;;AAC8B,IAAA,YAAgB,kCAAhB,gBAAgB,EAAA;AACrB,IAAA,OAAW,kCAAX,WAAW,EAAA;;;;;;AAepC,MAAMA,aAAa;IACjB,AAAQC,SAAS,GAAe,EAAE,CAAA;IAElCC,YAAYD,SAAqB,CAAE;QACjC,IAAI,CAACA,SAAS,GAAGA,SAAS;KAC3B;IAED,MAAME,QAAQ,GAAG;QACf,MAAMC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACJ,SAAS,CAACK,GAAG,CAAC,CAACC,SAAQ,GAAKA,SAAQ,CAACJ,QAAQ,EAAE,CAAC,CAAC;KACzE;IAEDK,MAAM,CACJC,QAAgB,EAChBC,QAAgB,EAChBC,SAAiB,EACjBC,EAAU,EACVC,QAAiB,EACjBC,KAAc,EACdC,SAAkB,EAClB;QACA,IAAI,CAACd,SAAS,CAACe,OAAO,CAAC,CAACT,SAAQ,GAC9BA,SAAQ,CAACC,MAAM,CACbC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,EAAE,EACFC,QAAQ,EACRC,KAAK,EACLC,SAAS,CACV,CACF;KACF;CACF;AAGM,MAAMR,QAAQ,GAAG,IAAIP,aAAa,CAAC;IAACiB,OAAY,QAAA;IAAEC,YAAiB,QAAA;CAAC,CAAC;QAA/DX,QAAQ,GAARA,QAAQ"}