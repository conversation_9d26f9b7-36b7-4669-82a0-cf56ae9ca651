{"version": 3, "sources": ["../../pages/_error.tsx"], "names": ["statusCodes", "_getInitialProps", "res", "err", "statusCode", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "lineHeight", "verticalAlign", "h1", "margin", "marginRight", "padding", "fontSize", "fontWeight", "h2", "React", "Error", "Component", "render", "withDarkMode", "props", "title", "div", "style", "Head", "dangerouslySetInnerHTML", "__html", "className", "displayName", "getInitialProps", "origGetInitialProps"], "mappings": "AAAA;;;;;;AAAkB,IAAA,MAAO,oCAAP,OAAO,EAAA;AACR,IAAA,KAAoB,oCAApB,oBAAoB,EAAA;AAGrC,MAAMA,WAAW,GAA+B;AAC9C,IAAA,GAAG,EAAE,aAAa;AAClB,IAAA,GAAG,EAAE,8BAA8B;AACnC,IAAA,GAAG,EAAE,oBAAoB;AACzB,IAAA,GAAG,EAAE,uBAAuB;CAC7B;AAQD,SAASC,gBAAgB,CAAC,EACxBC,GAAG,CAAA,EACHC,GAAG,CAAA,EACa,EAAoC;IACpD,MAAMC,UAAU,GACdF,GAAG,IAAIA,GAAG,CAACE,UAAU,GAAGF,GAAG,CAACE,UAAU,GAAGD,GAAG,GAAGA,GAAG,CAACC,UAAU,GAAI,GAAG;IACtE,OAAO;QAAEA,UAAU;KAAE,CAAA;CACtB;AAED,MAAMC,MAAM,GAAyC;IACnDC,KAAK,EAAE;QACLC,UAAU,EACR,2HAA2H;QAC7HC,MAAM,EAAE,OAAO;QACfC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;KACzB;IAEDC,IAAI,EAAE;QACJJ,OAAO,EAAE,cAAc;QACvBD,SAAS,EAAE,MAAM;QACjBM,UAAU,EAAE,MAAM;QAClBP,MAAM,EAAE,MAAM;QACdQ,aAAa,EAAE,QAAQ;KACxB;IAEDC,EAAE,EAAE;QACFP,OAAO,EAAE,cAAc;QACvBQ,MAAM,EAAE,CAAC;QACTC,WAAW,EAAE,MAAM;QACnBC,OAAO,EAAE,YAAY;QACrBC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,GAAG;QACfN,aAAa,EAAE,KAAK;QACpBD,UAAU,EAAE,MAAM;KACnB;IAEDQ,EAAE,EAAE;QACFF,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,QAAQ;QACpBP,UAAU,EAAE,MAAM;QAClBG,MAAM,EAAE,CAAC;QACTE,OAAO,EAAE,CAAC;KACX;CACF;IAK0CI,UAAe;AAA3C,MAAMC,KAAK,SAAiBD,CAAAA,UAAe,GAAfA,MAAK,QAAA,CAACE,SAAS,CAAA;IAMxDC,MAAM,GAAG;QACP,MAAM,EAAEvB,UAAU,CAAA,EAAEwB,YAAY,EAAG,IAAI,CAAA,EAAE,GAAG,IAAI,CAACC,KAAK;QACtD,MAAMC,KAAK,GACT,IAAI,CAACD,KAAK,CAACC,KAAK,IAChB9B,WAAW,CAACI,UAAU,CAAC,IACvB,kCAAkC;QAEpC,qBACE,6BAAC2B,KAAG;YAACC,KAAK,EAAE3B,MAAM,CAACC,KAAK;yBACtB,6BAAC2B,KAAI,QAAA,sBACH,6BAACH,OAAK,QACH1B,UAAU,GACP,CAAC,EAAEA,UAAU,CAAC,EAAE,EAAE0B,KAAK,CAAC,CAAC,GACzB,yDAAyD,CACvD,CACH,gBACP,6BAACC,KAAG,sBACF,6BAACC,OAAK;YACJE,uBAAuB,EAAE;gBACvBC,MAAM,EAAE,CAAC;;;;;;gBAMP,EACEP,YAAY,GACR,CAAC;;;;;iBAKN,CAAC,GACI,EAAE,CACP,CAAC;aACL;UACD,EAEDxB,UAAU,iBACT,6BAACa,IAAE;YAACmB,SAAS,EAAC,eAAe;YAACJ,KAAK,EAAE3B,MAAM,CAACY,EAAE;WAC3Cb,UAAU,CACR,GACH,IAAI,gBACR,6BAAC2B,KAAG;YAACC,KAAK,EAAE3B,MAAM,CAACS,IAAI;yBACrB,6BAACS,IAAE;YAACS,KAAK,EAAE3B,MAAM,CAACkB,EAAE;WACjB,IAAI,CAACM,KAAK,CAACC,KAAK,IAAI1B,UAAU,GAC7B0B,KAAK,iBAEL,4DAAE,wGAGF,CAAG,AACJ,EAAC,GAEJ,CAAK,CACD,CACF,CACF,CACP;KACF;CACF;AAjEC,AADmBL,KAAK,CACjBY,WAAW,GAAG,WAAW;AAEhC,AAHmBZ,KAAK,CAGjBa,eAAe,GAAGrC,gBAAgB;AACzC,AAJmBwB,KAAK,CAIjBc,mBAAmB,GAAGtC,gBAAgB;kBAJ1BwB,KAAK"}