{"version": 3, "sources": ["../../telemetry/storage.ts"], "names": ["ciEnvironment", "TELEMETRY_KEY_ENABLED", "TELEMETRY_KEY_NOTIFY_DATE", "TELEMETRY_KEY_ID", "TELEMETRY_KEY_SALT", "getStorageDirectory", "distDir", "isLikelyEphemeral", "isCI", "isDockerFunction", "path", "join", "undefined", "Telemetry", "constructor", "NEXT_TELEMETRY_DISABLED", "NEXT_TELEMETRY_DEBUG", "process", "env", "storageDirectory", "conf", "Conf", "projectName", "cwd", "_", "sessionId", "randomBytes", "toString", "rawProjectId", "getRawProjectId", "queue", "Set", "notify", "isDisabled", "get", "set", "Date", "now", "console", "log", "chalk", "magenta", "bold", "cyan", "anonymousId", "val", "generated", "salt", "setEnabled", "_enabled", "enabled", "isEnabled", "oneWayHash", "payload", "hash", "createHash", "update", "digest", "projectId", "record", "_events", "_this", "wrapper", "submitRecord", "prom", "then", "value", "isFulfilled", "isRejected", "catch", "reason", "res", "delete", "add", "flush", "Promise", "all", "events", "Array", "isArray", "length", "resolve", "for<PERSON>ach", "eventName", "error", "JSON", "stringify", "context", "meta", "getAnonymousMeta", "_postPayload", "map", "fields"], "mappings": "AAAA;;;;AAAkB,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAC3B,IAAA,KAAyB,kCAAzB,yBAAyB,EAAA;AACU,IAAA,OAAQ,WAAR,QAAQ,CAAA;AAC/B,IAAA,SAA8B,kCAA9B,8BAA8B,EAAA;AAC1C,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEU,IAAA,cAAkB,WAAlB,kBAAkB,CAAA;AACvCA,IAAAA,aAAa,mCAAM,WAAW,EAAjB;AACI,IAAA,YAAgB,WAAhB,gBAAgB,CAAA;AACb,IAAA,UAAc,WAAd,cAAc,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9C,+EAA+E;AAC/E,MAAMC,qBAAqB,GAAG,mBAAmB;AAEjD,4EAA4E;AAC5E,wBAAwB;AACxB,MAAMC,yBAAyB,GAAG,sBAAsB;AAExD,8EAA8E;AAC9E,uDAAuD;AACvD,MAAMC,gBAAgB,GAAG,CAAC,qBAAqB,CAAC;AAEhD,6EAA6E;AAC7E,+EAA+E;AAC/E,oEAAoE;AACpE,iCAAiC;AACjC,MAAMC,kBAAkB,GAAG,CAAC,cAAc,CAAC;AAqB3C,SAASC,mBAAmB,CAACC,OAAe,EAAsB;IAChE,MAAMC,iBAAiB,GAAGP,aAAa,CAACQ,IAAI,IAAIC,CAAAA,GAAAA,SAAgB,AAAE,CAAA,QAAF,EAAE;IAElE,IAAIF,iBAAiB,EAAE;QACrB,OAAOG,KAAI,QAAA,CAACC,IAAI,CAACL,OAAO,EAAE,OAAO,CAAC,CAAA;KACnC;IAED,OAAOM,SAAS,CAAA;CACjB;AAEM,MAAMC,SAAS;IASpBC,YAAY,EAAER,OAAO,CAAA,EAAuB,CAAE;QAC5C,oEAAoE;QACpE,MAAM,EAAES,uBAAuB,CAAA,EAAEC,oBAAoB,CAAA,EAAE,GAAGC,OAAO,CAACC,GAAG;QACrE,IAAI,CAACH,uBAAuB,GAAGA,uBAAuB;QACtD,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;QAChD,MAAMG,gBAAgB,GAAGd,mBAAmB,CAACC,OAAO,CAAC;QAErD,IAAI;YACF,qEAAqE;YACrE,wEAAwE;YACxE,qBAAqB;YACrB,IAAI,CAACc,IAAI,GAAG,IAAIC,KAAI,QAAA,CAAC;gBAAEC,WAAW,EAAE,QAAQ;gBAAEC,GAAG,EAAEJ,gBAAgB;aAAE,CAAC;SACvE,CAAC,OAAOK,CAAC,EAAE;YACV,IAAI,CAACJ,IAAI,GAAG,IAAI;SACjB;QACD,IAAI,CAACK,SAAS,GAAGC,CAAAA,GAAAA,OAAW,AAAI,CAAA,YAAJ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;QAChD,IAAI,CAACC,YAAY,GAAGC,CAAAA,GAAAA,UAAe,AAAE,CAAA,gBAAF,EAAE;QAErC,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,EAAE;QAEtB,IAAI,CAACC,MAAM,EAAE;KACd;IAED,AAAQA,MAAM,GAAG,IAAM;QACrB,IAAI,IAAI,CAACC,UAAU,IAAI,CAAC,IAAI,CAACb,IAAI,EAAE;YACjC,OAAM;SACP;QAED,6EAA6E;QAC7E,gDAAgD;QAChD,yEAAyE;QACzE,aAAa;QACb,IAAI,IAAI,CAACA,IAAI,CAACc,GAAG,CAAChC,yBAAyB,EAAE,EAAE,CAAC,EAAE;YAChD,OAAM;SACP;QACD,IAAI,CAACkB,IAAI,CAACe,GAAG,CAACjC,yBAAyB,EAAEkC,IAAI,CAACC,GAAG,EAAE,CAACV,QAAQ,EAAE,CAAC;QAE/DW,OAAO,CAACC,GAAG,CACT,CAAC,EAAEC,MAAK,QAAA,CAACC,OAAO,CAACC,IAAI,CACnB,WAAW,CACZ,CAAC,sEAAsE,CAAC,CAC1E;QACDJ,OAAO,CAACC,GAAG,CACT,CAAC,2EAA2E,CAAC,CAC9E;QACDD,OAAO,CAACC,GAAG,CACT,CAAC,uIAAuI,CAAC,CAC1I;QACDD,OAAO,CAACC,GAAG,CAACC,MAAK,QAAA,CAACG,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACvDL,OAAO,CAACC,GAAG,EAAE;KACd,CAAA;IAED,IAAIK,WAAW,GAAW;QACxB,MAAMC,GAAG,GAAG,IAAI,CAACzB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACc,GAAG,CAAC/B,gBAAgB,CAAC;QACxD,IAAI0C,GAAG,EAAE;YACP,OAAOA,GAAG,CAAA;SACX;QAED,MAAMC,SAAS,GAAGpB,CAAAA,GAAAA,OAAW,AAAI,CAAA,YAAJ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;QACjD,IAAI,CAACP,IAAI,IAAI,IAAI,CAACA,IAAI,CAACe,GAAG,CAAChC,gBAAgB,EAAE2C,SAAS,CAAC;QACvD,OAAOA,SAAS,CAAA;KACjB;IAED,IAAIC,IAAI,GAAW;QACjB,MAAMF,GAAG,GAAG,IAAI,CAACzB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACc,GAAG,CAAC9B,kBAAkB,CAAC;QAC1D,IAAIyC,GAAG,EAAE;YACP,OAAOA,GAAG,CAAA;SACX;QAED,MAAMC,SAAS,GAAGpB,CAAAA,GAAAA,OAAW,AAAI,CAAA,YAAJ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;QACjD,IAAI,CAACP,IAAI,IAAI,IAAI,CAACA,IAAI,CAACe,GAAG,CAAC/B,kBAAkB,EAAE0C,SAAS,CAAC;QACzD,OAAOA,SAAS,CAAA;KACjB;IAED,IAAYb,UAAU,GAAY;QAChC,IAAI,CAAC,CAAC,IAAI,CAAClB,uBAAuB,IAAI,CAAC,IAAI,CAACK,IAAI,EAAE;YAChD,OAAO,IAAI,CAAA;SACZ;QACD,OAAO,IAAI,CAACA,IAAI,CAACc,GAAG,CAACjC,qBAAqB,EAAE,IAAI,CAAC,KAAK,KAAK,CAAA;KAC5D;IAED+C,UAAU,GAAG,CAACC,QAAiB,GAAK;QAClC,MAAMC,OAAO,GAAG,CAAC,CAACD,QAAQ;QAC1B,IAAI,CAAC7B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACe,GAAG,CAAClC,qBAAqB,EAAEiD,OAAO,CAAC;QAC1D,OAAO,IAAI,CAAC9B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACV,IAAI,CAAA;KACnC,CAAA;IAED,IAAIyC,SAAS,GAAY;QACvB,OAAO,CAAC,CAAC,IAAI,CAAC/B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACc,GAAG,CAACjC,qBAAqB,EAAE,IAAI,CAAC,KAAK,KAAK,CAAA;KAC3E;IAEDmD,UAAU,GAAG,CAACC,OAAmB,GAAa;QAC5C,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,OAAU,AAAU,CAAA,WAAV,CAAC,QAAQ,CAAC;QAEjC,6EAA6E;QAC7E,WAAW;QACXD,IAAI,CAACE,MAAM,CAAC,IAAI,CAACT,IAAI,CAAC;QAEtB,4EAA4E;QAC5E,2BAA2B;QAC3BO,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC;QACpB,OAAOC,IAAI,CAACG,MAAM,CAAC,KAAK,CAAC,CAAA;KAC1B,CAAA;IAED,IAAYC,SAAS,GAAW;QAC9B,OAAO,IAAI,CAACN,UAAU,CAAC,IAAI,CAACxB,YAAY,CAAC,CAAA;KAC1C;IAED+B,MAAM,GAAG,CACPC,OAA0C,GAChB;QAC1B,MAAMC,KAAK,GAAG,IAAI;QAClB,mBAAmB;QACnB,eAAeC,OAAO,GAAG;YACvB,OAAO,MAAMD,KAAK,CAACE,YAAY,CAACH,OAAO,CAAC,CAAA;SACzC;QAED,MAAMI,IAAI,GAAGF,OAAO,EAAE,CACnBG,IAAI,CAAC,CAACC,KAAK,GAAK,CAAC;gBAChBC,WAAW,EAAE,IAAI;gBACjBC,UAAU,EAAE,KAAK;gBACjBF,KAAK;aACN,CAAC,CAAC,CACFG,KAAK,CAAC,CAACC,MAAM,GAAK,CAAC;gBAClBH,WAAW,EAAE,KAAK;gBAClBC,UAAU,EAAE,IAAI;gBAChBE,MAAM;aACP,CAAC,CAAC,AACH,iEAAiE;SAChEL,IAAI,CAAC,CAACM,GAAG,GAAK;YACb,uDAAuD;YACvD,IAAI,CAACzC,KAAK,CAAC0C,MAAM,CAACR,IAAI,CAAC;YACvB,OAAOO,GAAG,CAAA;SACX,CAAC;QAEJ,sDAAsD;QACtD,IAAI,CAACzC,KAAK,CAAC2C,GAAG,CAACT,IAAI,CAAC;QAEpB,OAAOA,IAAI,CAAA;KACZ,CAAA;IAEDU,KAAK,GAAG,UAAYC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9C,KAAK,CAAC,CAACuC,KAAK,CAAC,IAAM,IAAI,CAAC,CAAA;IAE7D,AAAQN,YAAY,GAAG,CACrBH,OAA0C,GACzB;QACjB,IAAIiB,MAAM,AAAkB;QAC5B,IAAIC,KAAK,CAACC,OAAO,CAACnB,OAAO,CAAC,EAAE;YAC1BiB,MAAM,GAAGjB,OAAO;SACjB,MAAM;YACLiB,MAAM,GAAG;gBAACjB,OAAO;aAAC;SACnB;QAED,IAAIiB,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;YACrB,OAAOL,OAAO,CAACM,OAAO,EAAE,CAAA;SACzB;QAED,IAAI,IAAI,CAACjE,oBAAoB,EAAE;YAC7B,2DAA2D;YAC3D6D,MAAM,CAACK,OAAO,CAAC,CAAC,EAAEC,SAAS,CAAA,EAAE9B,OAAO,CAAA,EAAE,GACpCf,OAAO,CAAC8C,KAAK,CACX,CAAC,YAAY,CAAC,GAAGC,IAAI,CAACC,SAAS,CAAC;oBAAEH,SAAS;oBAAE9B,OAAO;iBAAE,EAAE,IAAI,EAAE,CAAC,CAAC,CACjE,CACF;YACD,0EAA0E;YAC1E,sCAAsC;YACtC,OAAOsB,OAAO,CAACM,OAAO,EAAE,CAAA;SACzB;QAED,sDAAsD;QACtD,IAAI,IAAI,CAAChD,UAAU,EAAE;YACnB,OAAO0C,OAAO,CAACM,OAAO,EAAE,CAAA;SACzB;QAED,MAAMM,OAAO,GAAiB;YAC5B3C,WAAW,EAAE,IAAI,CAACA,WAAW;YAC7Bc,SAAS,EAAE,IAAI,CAACA,SAAS;YACzBjC,SAAS,EAAE,IAAI,CAACA,SAAS;SAC1B;QACD,MAAM+D,IAAI,GAAcC,CAAAA,GAAAA,cAAgB,AAAE,CAAA,iBAAF,EAAE;QAC1C,OAAOC,CAAAA,GAAAA,YAAY,AAOjB,CAAA,aAPiB,CAAC,CAAC,0CAA0C,CAAC,EAAE;YAChEH,OAAO;YACPC,IAAI;YACJX,MAAM,EAAEA,MAAM,CAACc,GAAG,CAAC,CAAC,EAAER,SAAS,CAAA,EAAE9B,OAAO,CAAA,EAAE,GAAK,CAAC;oBAC9C8B,SAAS;oBACTS,MAAM,EAAEvC,OAAO;iBAChB,CAAC,CAAC;SACJ,CAAC,CAAA;KACH,CAAA;CACF;QAtMYxC,SAAS,GAATA,SAAS"}