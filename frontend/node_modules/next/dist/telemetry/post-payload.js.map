{"version": 3, "sources": ["../../telemetry/post-payload.ts"], "names": ["_postPayload", "endpoint", "body", "retry", "fetch", "method", "JSON", "stringify", "headers", "timeout", "then", "res", "ok", "err", "Error", "statusText", "response", "minTimeout", "retries", "factor", "catch"], "mappings": "AAAA;;;;QAGgBA,YAAY,GAAZA,YAAY;AAHV,IAAA,WAAgC,kCAAhC,gCAAgC,EAAA;AAChC,IAAA,UAA+B,kCAA/B,+BAA+B,EAAA;;;;;;AAE1C,SAASA,YAAY,CAACC,QAAgB,EAAEC,IAAY,EAAE;IAC3D,OACEC,CAAAA,GAAAA,WAAK,AAeJ,CAAA,QAfI,CACH,IACEC,CAAAA,GAAAA,UAAK,AAKH,CAAA,QALG,CAACH,QAAQ,EAAE;YACdI,MAAM,EAAE,MAAM;YACdH,IAAI,EAAEI,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC;YAC1BM,OAAO,EAAE;gBAAE,cAAc,EAAE,kBAAkB;aAAE;YAC/CC,OAAO,EAAE,IAAI;SACd,CAAC,CAACC,IAAI,CAAC,CAACC,GAAG,GAAK;YACf,IAAI,CAACA,GAAG,CAACC,EAAE,EAAE;gBACX,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAACH,GAAG,CAACI,UAAU,CAAC,AACpC;gBAAA,AAACF,GAAG,CAASG,QAAQ,GAAGL,GAAG;gBAC5B,MAAME,GAAG,CAAA;aACV;SACF,CAAC,EACJ;QAAEI,UAAU,EAAE,GAAG;QAAEC,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;KAAE,CAC3C,CACEC,KAAK,CAAC,IAAM;IACX,kDAAkD;KACnD,CAAC,AACF,2BAA2B;KAC1BV,IAAI,CACH,IAAM,EAAE,EACR,IAAM,EAAE,CACT,CACJ;CACF"}