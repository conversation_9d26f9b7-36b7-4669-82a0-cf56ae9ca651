{"version": 3, "sources": ["../../telemetry/project-id.ts"], "names": ["getRawProjectId", "_getProjectIdByGit", "<PERSON><PERSON><PERSON><PERSON>", "execSync", "timeout", "stdio", "String", "trim", "_", "process", "env", "REPOSITORY_URL", "cwd"], "mappings": "AAAA;;;;QA6BgBA,eAAe,GAAfA,eAAe;AA7BN,IAAA,aAAe,WAAf,eAAe,CAAA;AAExC,6EAA6E;AAC7E,KAAK;AACL,4EAA4E;AAC5E,2EAA2E;AAC3E,2EAA2E;AAC3E,6EAA6E;AAC7E,gBAAgB;AAChB,4EAA4E;AAC5E,0EAA0E;AAC1E,2CAA2C;AAE3C,SAASC,kBAAkB,GAAG;IAC5B,IAAI;QACF,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,aAAQ,AAM5B,CAAA,SAN4B,CAC3B,CAAC,0CAA0C,CAAC,EAC5C;YACEC,OAAO,EAAE,IAAI;YACbC,KAAK,EAAE,CAAC,IAAI,CAAC;SACd,CACF;QAED,OAAOC,MAAM,CAACJ,YAAY,CAAC,CAACK,IAAI,EAAE,CAAA;KACnC,CAAC,OAAOC,CAAC,EAAE;QACV,OAAO,IAAI,CAAA;KACZ;CACF;AAEM,SAASR,eAAe,GAAW;IACxC,OAAOC,kBAAkB,EAAE,IAAIQ,OAAO,CAACC,GAAG,CAACC,cAAc,IAAIF,OAAO,CAACG,GAAG,EAAE,CAAA;CAC3E"}