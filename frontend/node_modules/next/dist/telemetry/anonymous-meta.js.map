{"version": 3, "sources": ["../../telemetry/anonymous-meta.ts"], "names": ["getAnonymousMeta", "ciEnvironment", "traits", "cpus", "os", "NOW_REGION", "process", "env", "systemPlatform", "platform", "systemRelease", "release", "systemArchitecture", "arch", "cpuCount", "length", "cpuModel", "model", "cpuSpeed", "speed", "memoryInMb", "Math", "trunc", "totalmem", "pow", "is<PERSON>ock<PERSON>", "isDockerFunction", "isNowDev", "isWsl", "isWslBoolean", "isCI", "ciName", "name", "nextVersion", "__NEXT_VERSION"], "mappings": "AAAA;;;;QAwBgBA,gBAAgB,GAAhBA,gBAAgB;AAxBH,IAAA,SAA8B,kCAA9B,8BAA8B,EAAA;AAClC,IAAA,MAA2B,kCAA3B,2BAA2B,EAAA;AACrC,IAAA,GAAI,kCAAJ,IAAI,EAAA;AAEPC,IAAAA,aAAa,mCAAM,WAAW,EAAjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBzB,IAAIC,MAAM,AAA2B;AAE9B,SAASF,gBAAgB,GAAkB;IAChD,IAAIE,MAAM,EAAE;QACV,OAAOA,MAAM,CAAA;KACd;IAED,MAAMC,IAAI,GAAGC,GAAE,QAAA,CAACD,IAAI,EAAE,IAAI,EAAE;IAC5B,MAAM,EAAEE,UAAU,CAAA,EAAE,GAAGC,OAAO,CAACC,GAAG;IAClCL,MAAM,GAAG;QACP,uBAAuB;QACvBM,cAAc,EAAEJ,GAAE,QAAA,CAACK,QAAQ,EAAE;QAC7BC,aAAa,EAAEN,GAAE,QAAA,CAACO,OAAO,EAAE;QAC3BC,kBAAkB,EAAER,GAAE,QAAA,CAACS,IAAI,EAAE;QAC7B,sBAAsB;QACtBC,QAAQ,EAAEX,IAAI,CAACY,MAAM;QACrBC,QAAQ,EAAEb,IAAI,CAACY,MAAM,GAAGZ,IAAI,CAAC,CAAC,CAAC,CAACc,KAAK,GAAG,IAAI;QAC5CC,QAAQ,EAAEf,IAAI,CAACY,MAAM,GAAGZ,IAAI,CAAC,CAAC,CAAC,CAACgB,KAAK,GAAG,IAAI;QAC5CC,UAAU,EAAEC,IAAI,CAACC,KAAK,CAAClB,GAAE,QAAA,CAACmB,QAAQ,EAAE,GAAGF,IAAI,CAACG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACzD,0BAA0B;QAC1BC,QAAQ,EAAEC,CAAAA,GAAAA,SAAgB,AAAE,CAAA,QAAF,EAAE;QAC5BC,QAAQ,EAAEtB,UAAU,KAAK,MAAM;QAC/BuB,KAAK,EAAEC,MAAY,QAAA;QACnBC,IAAI,EAAE7B,aAAa,CAAC6B,IAAI;QACxBC,MAAM,EAAE,AAAC9B,aAAa,CAAC6B,IAAI,IAAI7B,aAAa,CAAC+B,IAAI,IAAK,IAAI;QAC1DC,WAAW,EAAE3B,OAAO,CAACC,GAAG,CAAC2B,cAAc;KACxC;IAED,OAAOhC,MAAM,CAAA;CACd"}