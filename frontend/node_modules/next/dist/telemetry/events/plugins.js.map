{"version": 3, "sources": ["../../../telemetry/events/plugins.ts"], "names": ["eventNextPlugins", "EVENT_PLUGIN_PRESENT", "dir", "packageJsonPath", "findUp", "cwd", "dependencies", "devDependencies", "require", "deps", "Object", "keys", "reduce", "events", "plugin", "version", "push", "eventName", "payload", "packageName", "packageVersion", "_"], "mappings": "AAAA;;;;QAWsBA,gBAAgB,GAAhBA,gBAAgB;AAXnB,IAAA,OAA4B,kCAA5B,4BAA4B,EAAA;;;;;;AAE/C,MAAMC,oBAAoB,GAAG,uBAAuB;AAS7C,eAAeD,gBAAgB,CACpCE,GAAW,EACuB;IAClC,IAAI;QACF,MAAMC,eAAe,GAAG,MAAMC,CAAAA,GAAAA,OAAM,AAA8B,CAAA,QAA9B,CAAC,cAAc,EAAE;YAAEC,GAAG,EAAEH,GAAG;SAAE,CAAC;QAClE,IAAI,CAACC,eAAe,EAAE;YACpB,OAAO,EAAE,CAAA;SACV;QAED,MAAM,EAAEG,YAAY,EAAG,EAAE,CAAA,EAAEC,eAAe,EAAG,EAAE,CAAA,EAAE,GAAGC,OAAO,CAACL,eAAe,CAAC;QAE5E,MAAMM,IAAI,GAAG;YAAE,GAAGF,eAAe;YAAE,GAAGD,YAAY;SAAE;QAEpD,OAAOI,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,MAAM,CAC7B,CAACC,MAA0B,EAAEC,MAAc,GAAyB;YAClE,MAAMC,OAAO,GAAGN,IAAI,CAACK,MAAM,CAAC;YAC5B,uCAAuC;YACvC,IAAI,CAACC,OAAO,EAAE;gBACZ,OAAOF,MAAM,CAAA;aACd;YAEDA,MAAM,CAACG,IAAI,CAAC;gBACVC,SAAS,EAAEhB,oBAAoB;gBAC/BiB,OAAO,EAAE;oBACPC,WAAW,EAAEL,MAAM;oBACnBM,cAAc,EAAEL,OAAO;iBACxB;aACF,CAAC;YAEF,OAAOF,MAAM,CAAA;SACd,EACD,EAAE,CACH,CAAA;KACF,CAAC,OAAOQ,CAAC,EAAE;QACV,OAAO,EAAE,CAAA;KACV;CACF"}