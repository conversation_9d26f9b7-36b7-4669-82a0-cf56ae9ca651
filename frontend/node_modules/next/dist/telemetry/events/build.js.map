{"version": 3, "sources": ["../../../telemetry/events/build.ts"], "names": ["eventTypeCheckCompleted", "eventLintCheckCompleted", "eventBuildCompleted", "eventBuildOptimize", "eventBuildFeatureUsage", "eventPackageUsedInGetServerSideProps", "REGEXP_DIRECTORY_DUNDER", "REGEXP_DIRECTORY_TESTS", "REGEXP_FILE_TEST", "EVENT_TYPE_CHECK_COMPLETED", "event", "eventName", "payload", "EVENT_LINT_CHECK_COMPLETED", "EVENT_BUILD_COMPLETED", "pagePaths", "totalPageCount", "length", "hasDunderPages", "some", "path", "test", "hasTestPages", "EVENT_BUILD_OPTIMIZED", "EVENT_BUILD_FEATURE_USAGE", "telemetryPlugin", "usages", "map", "featureName", "invocationCount", "EVENT_NAME_PACKAGE_USED_IN_GET_SERVER_SIDE_PROPS", "packagesUsedInServerSideProps", "packageName", "package"], "mappings": "AAAA;;;;QAiBgBA,uBAAuB,GAAvBA,uBAAuB;QAyBvBC,uBAAuB,GAAvBA,uBAAuB;QAkBvBC,mBAAmB,GAAnBA,mBAAmB;QA4CnBC,kBAAkB,GAAlBA,kBAAkB;QAiDlBC,sBAAsB,GAAtBA,sBAAsB;QAmBtBC,oCAAoC,GAApCA,oCAAoC;;AAzKpD,MAAMC,uBAAuB,sDACwB;AACrD,MAAMC,sBAAsB,iCAAiC;AAC7D,MAAMC,gBAAgB,6BAA6B;AAEnD,MAAMC,0BAA0B,GAAG,2BAA2B;AASvD,SAAST,uBAAuB,CAACU,KAA8B,EAGpE;IACA,OAAO;QACLC,SAAS,EAAEF,0BAA0B;QACrCG,OAAO,EAAEF,KAAK;KACf,CAAA;CACF;AAED,MAAMG,0BAA0B,GAAG,2BAA2B;AAevD,SAASZ,uBAAuB,CAACS,KAA8B,EAGpE;IACA,OAAO;QACLC,SAAS,EAAEE,0BAA0B;QACrCD,OAAO,EAAEF,KAAK;KACf,CAAA;CACF;AAED,MAAMI,qBAAqB,GAAG,sBAAsB;AAQ7C,SAASZ,mBAAmB,CACjCa,SAAmB,EACnBL,KAGC,EACoD;IACrD,OAAO;QACLC,SAAS,EAAEG,qBAAqB;QAChCF,OAAO,EAAE;YACP,GAAGF,KAAK;YACRM,cAAc,EAAED,SAAS,CAACE,MAAM;YAChCC,cAAc,EAAEH,SAAS,CAACI,IAAI,CAAC,CAACC,IAAI,GAClCd,uBAAuB,CAACe,IAAI,CAACD,IAAI,CAAC,CACnC;YACDE,YAAY,EAAEP,SAAS,CAACI,IAAI,CAC1B,CAACC,IAAI,GACHb,sBAAsB,CAACc,IAAI,CAACD,IAAI,CAAC,IAAIZ,gBAAgB,CAACa,IAAI,CAACD,IAAI,CAAC,CACnE;SACF;KACF,CAAA;CACF;AAED,MAAMG,qBAAqB,GAAG,sBAAsB;AAqB7C,SAASpB,kBAAkB,CAChCY,SAAmB,EACnBL,KAGC,EACoD;IACrD,OAAO;QACLC,SAAS,EAAEY,qBAAqB;QAChCX,OAAO,EAAE;YACP,GAAGF,KAAK;YACRM,cAAc,EAAED,SAAS,CAACE,MAAM;YAChCC,cAAc,EAAEH,SAAS,CAACI,IAAI,CAAC,CAACC,IAAI,GAClCd,uBAAuB,CAACe,IAAI,CAACD,IAAI,CAAC,CACnC;YACDE,YAAY,EAAEP,SAAS,CAACI,IAAI,CAC1B,CAACC,IAAI,GACHb,sBAAsB,CAACc,IAAI,CAACD,IAAI,CAAC,IAAIZ,gBAAgB,CAACa,IAAI,CAACD,IAAI,CAAC,CACnE;SACF;KACF,CAAA;CACF;AAEM,MAAMI,yBAAyB,GAAG,0BAA0B;QAAtDA,yBAAyB,GAAzBA,yBAAyB;AA0B/B,SAASpB,sBAAsB,CACpCqB,eAAgC,EAC+B;IAC/D,OAAOA,eAAe,CAACC,MAAM,EAAE,CAACC,GAAG,CAAC,CAAC,EAAEC,WAAW,CAAA,EAAEC,eAAe,CAAA,EAAE,GAAK,CAAC;YACzElB,SAAS,EAAEa,yBAAyB;YACpCZ,OAAO,EAAE;gBACPgB,WAAW;gBACXC,eAAe;aAChB;SACF,CAAC,CAAC,CAAA;CACJ;AAEM,MAAMC,gDAAgD,GAC3D,4CAA4C;QADjCA,gDAAgD,GAAhDA,gDAAgD;AAOtD,SAASzB,oCAAoC,CAClDoB,eAAgC,EAC6C;IAC7E,OAAOA,eAAe,CAACM,6BAA6B,EAAE,CAACJ,GAAG,CAAC,CAACK,WAAW,GAAK,CAAC;YAC3ErB,SAAS,EAAEmB,gDAAgD;YAC3DlB,OAAO,EAAE;gBACPqB,OAAO,EAAED,WAAW;aACrB;SACF,CAAC,CAAC,CAAA;CACJ"}