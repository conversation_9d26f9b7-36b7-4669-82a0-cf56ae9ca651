{"version": 3, "sources": ["../../../telemetry/events/swc-load-failure.ts"], "names": ["eventSwcLoadFailure", "EVENT_PLUGIN_PRESENT", "event", "telemetry", "traceGlobals", "get", "glibcVersion", "installedSwcPackages", "process", "report", "getReport", "header", "glibcVersionRuntime", "_", "pkgNames", "Object", "keys", "optionalDependencies", "filter", "pkg", "startsWith", "installedPkgs", "version", "require", "push", "length", "sort", "join", "record", "eventName", "payload", "nextVersion", "arch", "platform", "nodeVersion", "versions", "node", "wasm", "flush"], "mappings": "AAAA;;;;QAmBsBA,mBAAmB,GAAnBA,mBAAmB;AAnBZ,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AAGY,IAAA,YAAmB,WAAnB,mBAAmB,CAAA;AAEhF,MAAMC,oBAAoB,GAAG,uBAAuB;AAc7C,eAAeD,mBAAmB,CACvCE,KAAsC,EACvB;IACf,MAAMC,SAAS,GAAcC,OAAY,aAAA,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1D,wCAAwC;IACxC,IAAI,CAACF,SAAS,EAAE,OAAM;IAEtB,IAAIG,YAAY;IAChB,IAAIC,oBAAoB;IAExB,IAAI;YAEaC,GAAc;QAD7B,aAAa;QACbF,YAAY,GAAGE,CAAAA,GAAc,GAAdA,OAAO,CAACC,MAAM,SAAW,GAAzBD,KAAAA,CAAyB,GAAzBA,GAAc,CAAEE,SAAS,EAAE,CAACC,MAAM,CAACC,mBAAmB;KACtE,CAAC,OAAOC,CAAC,EAAE,EAAE;IAEd,IAAI;QACF,MAAMC,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACC,YAAoB,qBAAA,IAAI,EAAE,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,GAClEA,GAAG,CAACC,UAAU,CAAC,WAAW,CAAC,CAC5B;QACD,MAAMC,aAAa,GAAG,EAAE;QAExB,KAAK,MAAMF,IAAG,IAAIL,QAAQ,CAAE;YAC1B,IAAI;gBACF,MAAM,EAAEQ,OAAO,CAAA,EAAE,GAAGC,OAAO,CAAC,CAAC,EAAEJ,IAAG,CAAC,aAAa,CAAC,CAAC;gBAClDE,aAAa,CAACG,IAAI,CAAC,CAAC,EAAEL,IAAG,CAAC,CAAC,EAAEG,OAAO,CAAC,CAAC,CAAC;aACxC,CAAC,OAAOT,CAAC,EAAE,EAAE;SACf;QAED,IAAIQ,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;YAC5BlB,oBAAoB,GAAGc,aAAa,CAACK,IAAI,EAAE,CAACC,IAAI,CAAC,GAAG,CAAC;SACtD;KACF,CAAC,OAAOd,EAAC,EAAE,EAAE;IAEdV,SAAS,CAACyB,MAAM,CAAC;QACfC,SAAS,EAAE5B,oBAAoB;QAC/B6B,OAAO,EAAE;YACPC,WAAW,EAAXA,YAAW,QAAA;YACXzB,YAAY;YACZC,oBAAoB;YACpByB,IAAI,EAAExB,OAAO,CAACwB,IAAI;YAClBC,QAAQ,EAAEzB,OAAO,CAACyB,QAAQ;YAC1BC,WAAW,EAAE1B,OAAO,CAAC2B,QAAQ,CAACC,IAAI;YAClCC,IAAI,EAAEnC,KAAK,QAAM,GAAXA,KAAAA,CAAW,GAAXA,KAAK,CAAEmC,IAAI;SAClB;KACF,CAAC;IACF,oDAAoD;IACpD,MAAMlC,SAAS,CAACmC,KAAK,EAAE;CACxB"}