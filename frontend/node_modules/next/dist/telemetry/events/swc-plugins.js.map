{"version": 3, "sources": ["../../../telemetry/events/swc-plugins.ts"], "names": ["eventSwcPlugins", "EVENT_SWC_PLUGIN_PRESENT", "dir", "config", "packageJsonPath", "findUp", "cwd", "dependencies", "devDependencies", "require", "deps", "swcPluginPackages", "experimental", "swcPlugins", "map", "name", "_", "Promise", "all", "plugin", "version", "undefined", "pluginName", "fileExists", "path", "basename", "eventName", "payload", "pluginVersion"], "mappings": "AAAA;;;;QAcsBA,eAAe,GAAfA,eAAe;AAdlB,IAAA,OAA4B,kCAA5B,4BAA4B,EAAA;AAC9B,IAAA,KAAM,kCAAN,MAAM,EAAA;AACI,IAAA,WAAuB,WAAvB,uBAAuB,CAAA;;;;;;AAGlD,MAAMC,wBAAwB,GAAG,0BAA0B;AASpD,eAAeD,eAAe,CACnCE,GAAW,EACXC,MAAkB,EACe;IACjC,IAAI;YAUAA,GAAmB;QATrB,MAAMC,eAAe,GAAG,MAAMC,CAAAA,GAAAA,OAAM,AAA8B,CAAA,QAA9B,CAAC,cAAc,EAAE;YAAEC,GAAG,EAAEJ,GAAG;SAAE,CAAC;QAClE,IAAI,CAACE,eAAe,EAAE;YACpB,OAAO,EAAE,CAAA;SACV;QAED,MAAM,EAAEG,YAAY,EAAG,EAAE,CAAA,EAAEC,eAAe,EAAG,EAAE,CAAA,EAAE,GAAGC,OAAO,CAACL,eAAe,CAAC;QAE5E,MAAMM,IAAI,GAAG;YAAE,GAAGF,eAAe;YAAE,GAAGD,YAAY;SAAE;YAElDJ,IAAyD;QAD3D,MAAMQ,iBAAiB,GACrBR,CAAAA,IAAyD,GAAzDA,CAAAA,GAAmB,GAAnBA,MAAM,CAACS,YAAY,SAAY,GAA/BT,KAAAA,CAA+B,GAA/BA,QAAAA,GAAmB,CAAEU,UAAU,SAAA,GAA/BV,KAAAA,CAA+B,GAA/BA,KAAiCW,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEC,CAAC,CAAC,GAAKD,IAAI,CAAC,YAAzDZ,IAAyD,GAAI,EAAE;QAEjE,OAAOc,OAAO,CAACC,GAAG,CAChBP,iBAAiB,CAACG,GAAG,CAAC,OAAOK,MAAM,GAAK;gBAEtBT,OAAY;YAD5B,0EAA0E;YAC1E,MAAMU,OAAO,GAAGV,CAAAA,OAAY,GAAZA,IAAI,CAACS,MAAM,CAAC,YAAZT,OAAY,GAAIW,SAAS;YACzC,IAAIC,UAAU,GAAGH,MAAM;YACvB,IAAI,MAAMI,CAAAA,GAAAA,WAAU,AAAY,CAAA,WAAZ,CAACD,UAAU,CAAC,EAAE;gBAChCA,UAAU,GAAGE,KAAI,QAAA,CAACC,QAAQ,CAACN,MAAM,EAAE,OAAO,CAAC;aAC5C;YAED,OAAO;gBACLO,SAAS,EAAEzB,wBAAwB;gBACnC0B,OAAO,EAAE;oBACPL,UAAU,EAAEA,UAAU;oBACtBM,aAAa,EAAER,OAAO;iBACvB;aACF,CAAA;SACF,CAAC,CACH,CAAA;KACF,CAAC,OAAOJ,CAAC,EAAE;QACV,OAAO,EAAE,CAAA;KACV;CACF"}