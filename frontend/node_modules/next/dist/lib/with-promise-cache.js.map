{"version": 3, "sources": ["../../lib/with-promise-cache.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cache", "fn", "<PERSON><PERSON><PERSON>", "values", "key", "p", "get", "catch", "del", "set"], "mappings": "AACA;;;;QAegBA,gBAAgB,GAAhBA,gBAAgB;AAAzB,SAASA,gBAAgB,CAC9BC,KAA2B,EAC3BC,EAAgC,EAChCC,MAA4B,EACE;IAC9B,OAAO,CAAC,GAAGC,MAAM,AAAG,GAAK;QACvB,MAAMC,GAAG,GAAGF,MAAM,GAAGA,MAAM,IAAIC,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;QAClD,IAAIE,CAAC,GAAGL,KAAK,CAACM,GAAG,CAACF,GAAG,CAAC;QACtB,IAAI,CAACC,CAAC,EAAE;YACNA,CAAC,GAAGJ,EAAE,IAAIE,MAAM,CAAC;YACjBE,CAAC,CAACE,KAAK,CAAC,IAAMP,KAAK,CAACQ,GAAG,CAACJ,GAAG,CAAC,CAAC;YAC7BJ,KAAK,CAACS,GAAG,CAACL,GAAG,EAAEC,CAAC,CAAC;SAClB;QACD,OAAOA,CAAC,CAAA;KACT,CAAA;CACF"}