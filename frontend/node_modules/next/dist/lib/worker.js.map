{"version": 3, "sources": ["../../lib/worker.ts"], "names": ["RESTARTED", "Symbol", "Worker", "constructor", "worker<PERSON><PERSON>", "options", "timeout", "onRestart", "farmOptions", "restartPromise", "resolveRestartPromise", "activeTasks", "_worker", "undefined", "createWorker", "JestWorker", "Promise", "resolve", "getStdout", "pipe", "process", "stdout", "getStderr", "stderr", "onHanging", "worker", "end", "then", "hanging<PERSON><PERSON>r", "onActivity", "clearTimeout", "setTimeout", "method", "exposedMethods", "startsWith", "args", "attempts", "result", "race", "bind", "Error", "close"], "mappings": "AAAA;;;;AAAqC,IAAA,WAAgC,WAAhC,gCAAgC,CAAA;AAIrE,MAAMA,SAAS,GAAGC,MAAM,CAAC,WAAW,CAAC;AAE9B,MAAMC,MAAM;IAGjBC,YACEC,UAAkB,EAClBC,OAIC,CACD;QACA,IAAI,EAAEC,OAAO,CAAA,EAAEC,SAAS,CAAA,EAAE,GAAGC,WAAW,EAAE,GAAGH,OAAO;QAEpD,IAAII,cAAc,AAA2B;QAC7C,IAAIC,qBAAqB,AAAiC;QAC1D,IAAIC,WAAW,GAAG,CAAC;QAEnB,IAAI,CAACC,OAAO,GAAGC,SAAS;QAExB,MAAMC,YAAY,GAAG,IAAM;YACzB,IAAI,CAACF,OAAO,GAAG,IAAIG,WAAU,OAAA,CAACX,UAAU,EAAEI,WAAW,CAAC,AAAc;YACpEC,cAAc,GAAG,IAAIO,OAAO,CAC1B,CAACC,OAAO,GAAMP,qBAAqB,GAAGO,OAAO,AAAC,CAC/C;YAED,IAAI,CAACL,OAAO,CAACM,SAAS,EAAE,CAACC,IAAI,CAACC,OAAO,CAACC,MAAM,CAAC;YAC7C,IAAI,CAACT,OAAO,CAACU,SAAS,EAAE,CAACH,IAAI,CAACC,OAAO,CAACG,MAAM,CAAC;SAC9C;QACDT,YAAY,EAAE;QAEd,MAAMU,SAAS,GAAG,IAAM;YACtB,MAAMC,MAAM,GAAG,IAAI,CAACb,OAAO;YAC3B,IAAI,CAACa,MAAM,EAAE,OAAM;YACnB,MAAMR,OAAO,GAAGP,qBAAqB;YACrCI,YAAY,EAAE;YACdW,MAAM,CAACC,GAAG,EAAE,CAACC,IAAI,CAAC,IAAM;gBACtBV,OAAO,CAACjB,SAAS,CAAC;aACnB,CAAC;SACH;QAED,IAAI4B,YAAY,GAAmB,KAAK;QAExC,MAAMC,UAAU,GAAG,IAAM;YACvB,IAAID,YAAY,EAAEE,YAAY,CAACF,YAAY,CAAC;YAC5CA,YAAY,GAAGjB,WAAW,GAAG,CAAC,IAAIoB,UAAU,CAACP,SAAS,EAAElB,OAAO,CAAC;SACjE;QAED,KAAK,MAAM0B,MAAM,IAAIxB,WAAW,CAACyB,cAAc,CAAE;YAC/C,IAAID,MAAM,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,SAC3B;YAAA,AAAC,IAAI,AAAQ,CAACF,MAAM,CAAC,GAAG1B,OAAO,GAE5B,OAAO,GAAG6B,IAAI,AAAO,GAAK;gBACxBxB,WAAW,EAAE;gBACb,IAAI;oBACF,IAAIyB,QAAQ,GAAG,CAAC;oBAChB,OAAS;wBACPP,UAAU,EAAE;wBACZ,MAAMQ,MAAM,GAAG,MAAMrB,OAAO,CAACsB,IAAI,CAAC;4BAC/B,IAAI,CAAC1B,OAAO,AAAQ,CAACoB,MAAM,CAAC,IAAIG,IAAI,CAAC;4BACtC1B,cAAc;yBACf,CAAC;wBACF,IAAI4B,MAAM,KAAKrC,SAAS,EAAE,OAAOqC,MAAM,CAAA;wBACvC,IAAI9B,SAAS,EAAEA,SAAS,CAACyB,MAAM,EAAEG,IAAI,EAAE,EAAEC,QAAQ,CAAC;qBACnD;iBACF,QAAS;oBACRzB,WAAW,EAAE;oBACbkB,UAAU,EAAE;iBACb;aACF,GACD,AAAC,IAAI,CAACjB,OAAO,AAAQ,CAACoB,MAAM,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAAC;SACrD;KACF;IAEDc,GAAG,GAAkC;QACnC,MAAMD,MAAM,GAAG,IAAI,CAACb,OAAO;QAC3B,IAAI,CAACa,MAAM,EAAE;YACX,MAAM,IAAIe,KAAK,CAAC,gDAAgD,CAAC,CAAA;SAClE;QACD,IAAI,CAAC5B,OAAO,GAAGC,SAAS;QACxB,OAAOY,MAAM,CAACC,GAAG,EAAE,CAAA;KACpB;IAED;;KAEG,CACHe,KAAK,GAAS;QACZ,IAAI,IAAI,CAAC7B,OAAO,EAAE;YAChB,IAAI,CAACA,OAAO,CAACc,GAAG,EAAE;SACnB;KACF;CACF;QA1FYxB,MAAM,GAANA,MAAM"}