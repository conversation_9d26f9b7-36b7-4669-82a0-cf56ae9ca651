{"version": 3, "sources": ["../../src/lib/worker.ts"], "names": ["Worker", "RESTARTED", "Symbol", "cleanupWorkers", "worker", "cur<PERSON><PERSON><PERSON>", "_workerPool", "_workers", "_child", "kill", "constructor", "worker<PERSON><PERSON>", "options", "timeout", "onRestart", "logger", "console", "farmOptions", "restartPromise", "resolveRestartPromise", "activeTasks", "_worker", "undefined", "createWorker", "JestWorker", "forkOptions", "env", "process", "NODE_OPTIONS", "getNodeOptionsWithoutInspect", "replace", "trim", "Promise", "resolve", "enableWorkerThreads", "on", "code", "signal", "error", "getStdout", "pipe", "stdout", "getStderr", "stderr", "onHanging", "warn", "end", "then", "hanging<PERSON><PERSON>r", "onActivity", "clearTimeout", "setTimeout", "method", "exposedMethods", "startsWith", "args", "attempts", "result", "race", "bind", "Error", "close"], "mappings": ";;;;+BAeaA;;;eAAAA;;;4BAdwB;uBACQ;AAG7C,MAAMC,YAAYC,OAAO;AAEzB,MAAMC,iBAAiB,CAACC;QACG;IAAzB,KAAK,MAAMC,aAAc,EAAA,sBAAA,AAACD,OAAeE,WAAW,qBAA3B,oBAA6BC,QAAQ,KAAI,EAAE,CAE/D;YACHF;SAAAA,oBAAAA,UAAUG,MAAM,qBAAhBH,kBAAkBI,IAAI,CAAC;IACzB;AACF;AAEO,MAAMT;IAGXU,YACEC,UAAkB,EAClBC,OAMC,CACD;QACA,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAASC,OAAO,EAAE,GAAGC,aAAa,GAAGL;QAE/D,IAAIM;QACJ,IAAIC;QACJ,IAAIC,cAAc;QAElB,IAAI,CAACC,OAAO,GAAGC;QAEf,MAAMC,eAAe;gBAMRN;YALX,IAAI,CAACI,OAAO,GAAG,IAAIG,kBAAU,CAACb,YAAY;gBACxC,GAAGM,WAAW;gBACdQ,aAAa;oBACX,GAAGR,YAAYQ,WAAW;oBAC1BC,KAAK;wBACH,GAAKT,EAAAA,2BAAAA,YAAYQ,WAAW,qBAAvBR,yBAAyBS,GAAG,KAAI,CAAC,CAAC;wBACvC,GAAGC,QAAQD,GAAG;wBACd,4CAA4C;wBAC5C,qBAAqB;wBACrBE,cAAcC,IAAAA,mCAA4B,IACvCC,OAAO,CAAC,iCAAiC,IACzCC,IAAI;oBACT;gBACF;YACF;YACAb,iBAAiB,IAAIc,QACnB,CAACC,UAAad,wBAAwBc;YAGxC;;;;;;;;OAQC,GACD,IAAI,CAAChB,YAAYiB,mBAAmB,EAAE;oBACd;gBAAtB,KAAK,MAAM9B,UAAW,EAAA,4BAAA,AAAC,IAAI,CAACiB,OAAO,CAASf,WAAW,qBAAjC,0BAAmCC,QAAQ,KAC/D,EAAE,CAEC;wBACHH;qBAAAA,iBAAAA,OAAOI,MAAM,qBAAbJ,eAAe+B,EAAE,CAAC,QAAQ,CAACC,MAAMC;wBAC/B,IAAI,AAACD,CAAAA,QAASC,UAAUA,WAAW,QAAQ,KAAM,IAAI,CAAChB,OAAO,EAAE;4BAC7DN,OAAOuB,KAAK,CACV,CAAC,gCAAgC,EAAEF,KAAK,aAAa,EAAEC,OAAO,CAAC;wBAEnE;oBACF;gBACF;YACF;YAEA,IAAI,CAAChB,OAAO,CAACkB,SAAS,GAAGC,IAAI,CAACb,QAAQc,MAAM;YAC5C,IAAI,CAACpB,OAAO,CAACqB,SAAS,GAAGF,IAAI,CAACb,QAAQgB,MAAM;QAC9C;QACApB;QAEA,MAAMqB,YAAY;YAChB,MAAMxC,SAAS,IAAI,CAACiB,OAAO;YAC3B,IAAI,CAACjB,QAAQ;YACb,MAAM6B,UAAUd;YAChBI;YACAR,OAAO8B,IAAI,CACT,CAAC,sDAAsD,EACrDhC,UAAU,CAAC,IAAI,EAAEA,UAAU,KAAK,QAAQ,CAAC,GAAG,GAC7C,0DAA0D,CAAC;YAE9DT,OAAO0C,GAAG,GAAGC,IAAI,CAAC;gBAChBd,QAAQhC;YACV;QACF;QAEA,IAAI+C,eAAuC;QAE3C,MAAMC,aAAa;YACjB,IAAID,cAAcE,aAAaF;YAC/BA,eAAe5B,cAAc,KAAK+B,WAAWP,WAAW/B;QAC1D;QAEA,KAAK,MAAMuC,UAAUnC,YAAYoC,cAAc,CAAE;YAC/C,IAAID,OAAOE,UAAU,CAAC,MAAM;YAC3B,AAAC,IAAI,AAAQ,CAACF,OAAO,GAAGvC,UAErB,OAAO,GAAG0C;gBACRnC;gBACA,IAAI;oBACF,IAAIoC,WAAW;oBACf,OAAS;wBACPP;wBACA,MAAMQ,SAAS,MAAMzB,QAAQ0B,IAAI,CAAC;4BAC/B,IAAI,CAACrC,OAAO,AAAQ,CAAC+B,OAAO,IAAIG;4BACjCrC;yBACD;wBACD,IAAIuC,WAAWxD,WAAW,OAAOwD;wBACjC,IAAI3C,WAAWA,UAAUsC,QAAQG,MAAM,EAAEC;oBAC3C;gBACF,SAAU;oBACRpC;oBACA6B;gBACF;YACF,IACA,AAAC,IAAI,CAAC5B,OAAO,AAAQ,CAAC+B,OAAO,CAACO,IAAI,CAAC,IAAI,CAACtC,OAAO;QACrD;IACF;IAEAyB,MAAqC;QACnC,MAAM1C,SAAS,IAAI,CAACiB,OAAO;QAC3B,IAAI,CAACjB,QAAQ;YACX,MAAM,IAAIwD,MAAM;QAClB;QACAzD,eAAeC;QACf,IAAI,CAACiB,OAAO,GAAGC;QACf,OAAOlB,OAAO0C,GAAG;IACnB;IAEA;;GAEC,GACDe,QAAc;QACZ,IAAI,IAAI,CAACxC,OAAO,EAAE;YAChBlB,eAAe,IAAI,CAACkB,OAAO;YAC3B,IAAI,CAACA,OAAO,CAACyB,GAAG;QAClB;IACF;AACF"}