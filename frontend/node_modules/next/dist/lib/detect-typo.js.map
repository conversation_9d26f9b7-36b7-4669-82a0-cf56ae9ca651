{"version": 3, "sources": ["../../lib/detect-typo.ts"], "names": ["detectTypo", "minDistance", "a", "b", "threshold", "m", "length", "n", "previousRow", "Array", "from", "_", "i", "s1", "currentRow", "j", "s2", "insertions", "deletions", "substitutions", "Number", "push", "Math", "min", "input", "options", "potentialTypos", "map", "o", "option", "distance", "filter", "sort"], "mappings": "AACA;;;;QA6BgBA,UAAU,GAAVA,UAAU;AA9B1B,6EAA6E;AAC7E,SAASC,WAAW,CAACC,CAAS,EAAEC,CAAS,EAAEC,SAAiB,EAAU;IACpE,MAAMC,CAAC,GAAGH,CAAC,CAACI,MAAM;IAClB,MAAMC,CAAC,GAAGJ,CAAC,CAACG,MAAM;IAElB,IAAID,CAAC,GAAGE,CAAC,EAAE;QACT,OAAON,WAAW,CAACE,CAAC,EAAED,CAAC,EAAEE,SAAS,CAAC,CAAA;KACpC;IAED,IAAIG,CAAC,KAAK,CAAC,EAAE;QACX,OAAOF,CAAC,CAAA;KACT;IAED,IAAIG,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;QAAEJ,MAAM,EAAEC,CAAC,GAAG,CAAC;KAAE,EAAE,CAACI,CAAC,EAAEC,CAAC,GAAKA,CAAC,CAAC;IAE5D,IAAK,IAAIA,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGP,CAAC,EAAEO,EAAC,EAAE,CAAE;QAC1B,MAAMC,EAAE,GAAGX,CAAC,CAACU,EAAC,CAAC;QACf,IAAIE,UAAU,GAAG;YAACF,EAAC,GAAG,CAAC;SAAC;QACxB,IAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,EAAEQ,CAAC,EAAE,CAAE;YAC1B,MAAMC,EAAE,GAAGb,CAAC,CAACY,CAAC,CAAC;YACf,MAAME,UAAU,GAAGT,WAAW,CAACO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;YACzC,MAAMG,SAAS,GAAGJ,UAAU,CAACC,CAAC,CAAC,GAAG,CAAC;YACnC,MAAMI,aAAa,GAAGX,WAAW,CAACO,CAAC,CAAC,GAAGK,MAAM,CAACP,EAAE,KAAKG,EAAE,CAAC;YACxDF,UAAU,CAACO,IAAI,CAACC,IAAI,CAACC,GAAG,CAACN,UAAU,EAAEC,SAAS,EAAEC,aAAa,CAAC,CAAC;SAChE;QACDX,WAAW,GAAGM,UAAU;KACzB;IACD,OAAON,WAAW,CAACA,WAAW,CAACF,MAAM,GAAG,CAAC,CAAC,CAAA;CAC3C;AAEM,SAASN,UAAU,CAACwB,KAAa,EAAEC,OAAiB,EAAErB,SAAS,GAAG,CAAC,EAAE;IAC1E,MAAMsB,cAAc,GAAGD,OAAO,CAC3BE,GAAG,CAAC,CAACC,CAAC,GAAK,CAAC;YACXC,MAAM,EAAED,CAAC;YACTE,QAAQ,EAAE7B,WAAW,CAAC2B,CAAC,EAAEJ,KAAK,EAAEpB,SAAS,CAAC;SAC3C,CAAC,CAAC,CACF2B,MAAM,CAAC,CAAC,EAAED,QAAQ,CAAA,EAAE,GAAKA,QAAQ,IAAI1B,SAAS,IAAI0B,QAAQ,GAAG,CAAC,CAAC,CAC/DE,IAAI,CAAC,CAAC9B,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC4B,QAAQ,GAAG3B,CAAC,CAAC2B,QAAQ,CAAC;IAE1C,IAAIJ,cAAc,CAACpB,MAAM,EAAE;QACzB,OAAOoB,cAAc,CAAC,CAAC,CAAC,CAACG,MAAM,CAAA;KAChC;IACD,OAAO,IAAI,CAAA;CACZ"}