{"version": 3, "sources": ["../../../lib/typescript/missingDependencyError.ts"], "names": ["missingDepsError", "dir", "missingPackages", "packagesHuman", "getOxfordCommaList", "map", "p", "pkg", "packagesCli", "join", "packageManager", "getPkgManager", "removalMsg", "chalk", "bold", "cyan", "FatalE<PERSON>r", "red"], "mappings": "AAAA;;;;QAOsBA,gBAAgB,GAAhBA,gBAAgB;AAPpB,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAET,IAAA,gBAAsB,WAAtB,sBAAsB,CAAA;AAE9B,IAAA,WAAgB,WAAhB,gBAAgB,CAAA;AACb,IAAA,cAA4B,WAA5B,4BAA4B,CAAA;;;;;;AAEnD,eAAeA,gBAAgB,CACpCC,GAAW,EACXC,eAAoC,EACpC;IACA,MAAMC,aAAa,GAAGC,CAAAA,GAAAA,gBAAkB,AAAmC,CAAA,mBAAnC,CAACF,eAAe,CAACG,GAAG,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,GAAG,CAAC,CAAC;IAC3E,MAAMC,WAAW,GAAGN,eAAe,CAACG,GAAG,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,GAAG,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;IAC/D,MAAMC,cAAc,GAAGC,CAAAA,GAAAA,cAAa,AAAK,CAAA,cAAL,CAACV,GAAG,CAAC;IAEzC,MAAMW,UAAU,GACd,MAAM,GACNC,MAAK,QAAA,CAACC,IAAI,CACR,6DAA6D,GAC3DD,MAAK,QAAA,CAACE,IAAI,CAAC,eAAe,CAAC,GAC3B,kFAAkF,CACrF;IAEH,MAAM,IAAIC,WAAU,WAAA,CAClBH,MAAK,QAAA,CAACC,IAAI,CAACG,GAAG,CACZ,CAAC,gGAAgG,CAAC,CACnG,GACC,MAAM,GACNJ,MAAK,QAAA,CAACC,IAAI,CAAC,CAAC,eAAe,EAAED,MAAK,QAAA,CAACC,IAAI,CAACX,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,GACrE,MAAM,GACN,CAAC,EAAE,EAAEU,MAAK,QAAA,CAACC,IAAI,CAACC,IAAI,CAClB,CAACL,cAAc,KAAK,MAAM,GACtB,gBAAgB,GAChBA,cAAc,KAAK,MAAM,GACzB,yBAAyB,GACzB,wBAAwB,CAAC,GAC3B,GAAG,GACHF,WAAW,CACd,CAAC,CAAC,GACHI,UAAU,GACV,IAAI,CACP,CAAA;CACF"}