{"version": 3, "sources": ["../../../lib/typescript/runTypeCheck.ts"], "names": ["runTypeCheck", "ts", "baseDir", "tsConfigPath", "cacheDir", "effectiveConfiguration", "getTypeScriptConfiguration", "fileNames", "length", "hasWarnings", "inputFilesCount", "totalFilesCount", "incremental", "requiredConfig", "getRequiredConfiguration", "options", "declarationMap", "emitDeclarationOnly", "noEmit", "program", "composite", "warn", "createIncrementalProgram", "rootNames", "tsBuildInfoFile", "path", "join", "createProgram", "result", "emit", "regexIgnoredFile", "allDiagnostics", "getPreEmitDiagnostics", "concat", "diagnostics", "filter", "d", "file", "test", "fileName", "firstError", "find", "category", "DiagnosticCategory", "Error", "Boolean", "CompileError", "getFormattedDiagnostic", "warnings", "Promise", "all", "Warning", "map", "getSourceFiles"], "mappings": "AAAA;;;;QAmBsBA,YAAY,GAAZA,YAAY;AAnBjB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAIhB,IAAA,oBAAuB,WAAvB,uBAAuB,CAAA;AACa,IAAA,2BAA8B,WAA9B,8BAA8B,CAAA;AAChC,IAAA,2BAA8B,WAA9B,8BAA8B,CAAA;AAE1C,IAAA,aAAkB,WAAlB,kBAAkB,CAAA;AAC1B,IAAA,IAAwB,WAAxB,wBAAwB,CAAA;;;;;;AAUtC,eAAeA,YAAY,CAChCC,EAA+B,EAC/BC,OAAe,EACfC,YAAoB,EACpBC,QAAiB,EACS;IAC1B,MAAMC,sBAAsB,GAAG,MAAMC,CAAAA,GAAAA,2BAA0B,AAG9D,CAAA,2BAH8D,CAC7DL,EAAE,EACFE,YAAY,CACb;IAED,IAAIE,sBAAsB,CAACE,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/C,OAAO;YACLC,WAAW,EAAE,KAAK;YAClBC,eAAe,EAAE,CAAC;YAClBC,eAAe,EAAE,CAAC;YAClBC,WAAW,EAAE,KAAK;SACnB,CAAA;KACF;IACD,MAAMC,cAAc,GAAGC,CAAAA,GAAAA,2BAAwB,AAAI,CAAA,yBAAJ,CAACb,EAAE,CAAC;IAEnD,MAAMc,OAAO,GAAG;QACd,GAAGV,sBAAsB,CAACU,OAAO;QACjC,GAAGF,cAAc;QACjBG,cAAc,EAAE,KAAK;QACrBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE,IAAI;KACb;IAED,IAAIC,OAAO,AAE4B;IACvC,IAAIP,WAAW,GAAG,KAAK;IACvB,IAAI,CAACG,OAAO,CAACH,WAAW,IAAIG,OAAO,CAACK,SAAS,CAAC,IAAIhB,QAAQ,EAAE;QAC1D,IAAIW,OAAO,CAACK,SAAS,EAAE;YACrBC,CAAAA,GAAAA,IAAI,AAEH,CAAA,KAFG,CACF,iGAAiG,CAClG;SACF;QACDT,WAAW,GAAG,IAAI;QAClBO,OAAO,GAAGlB,EAAE,CAACqB,wBAAwB,CAAC;YACpCC,SAAS,EAAElB,sBAAsB,CAACE,SAAS;YAC3CQ,OAAO,EAAE;gBACP,GAAGA,OAAO;gBACVK,SAAS,EAAE,KAAK;gBAChBR,WAAW,EAAE,IAAI;gBACjBY,eAAe,EAAEC,KAAI,QAAA,CAACC,IAAI,CAACtB,QAAQ,EAAE,cAAc,CAAC;aACrD;SACF,CAAC;KACH,MAAM;QACLe,OAAO,GAAGlB,EAAE,CAAC0B,aAAa,CAACtB,sBAAsB,CAACE,SAAS,EAAEQ,OAAO,CAAC;KACtE;IACD,MAAMa,MAAM,GAAGT,OAAO,CAACU,IAAI,EAAE;IAE7B,qBAAqB;IACrB,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;IACzB,EAAE;IACF,WAAW;IACX,qBAAqB;IACrB,mBAAmB;IACnB,oBAAoB;IACpB,EAAE;IACF,MAAMC,gBAAgB,qEAC8C;IACpE,MAAMC,cAAc,GAAG9B,EAAE,CACtB+B,qBAAqB,CAACb,OAAO,CAAiC,CAC9Dc,MAAM,CAACL,MAAM,CAACM,WAAW,CAAC,CAC1BC,MAAM,CAAC,CAACC,CAAC,GAAK,CAAC,CAACA,CAAC,CAACC,IAAI,IAAIP,gBAAgB,CAACQ,IAAI,CAACF,CAAC,CAACC,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;QAGnER,GAEC;IAHH,MAAMS,UAAU,GACdT,CAAAA,GAEC,GAFDA,cAAc,CAACU,IAAI,CACjB,CAACL,CAAC,GAAKA,CAAC,CAACM,QAAQ,KAAKC,oBAAkB,mBAAA,CAACC,KAAK,IAAIC,OAAO,CAACT,CAAC,CAACC,IAAI,CAAC,CAClE,YAFDN,GAEC,GAAIA,cAAc,CAACU,IAAI,CAAC,CAACL,CAAC,GAAKA,CAAC,CAACM,QAAQ,KAAKC,oBAAkB,mBAAA,CAACC,KAAK,CAAC;IAE1E,IAAIJ,UAAU,EAAE;QACd,MAAM,IAAIM,aAAY,aAAA,CACpB,MAAMC,CAAAA,GAAAA,oBAAsB,AAAyB,CAAA,uBAAzB,CAAC9C,EAAE,EAAEC,OAAO,EAAEsC,UAAU,CAAC,CACtD,CAAA;KACF;IAED,MAAMQ,QAAQ,GAAG,MAAMC,OAAO,CAACC,GAAG,CAChCnB,cAAc,CACXI,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACM,QAAQ,KAAKC,oBAAkB,mBAAA,CAACQ,OAAO,CAAC,CACxDC,GAAG,CAAC,CAAChB,CAAC,GAAKW,CAAAA,GAAAA,oBAAsB,AAAgB,CAAA,uBAAhB,CAAC9C,EAAE,EAAEC,OAAO,EAAEkC,CAAC,CAAC,CAAC,CACtD;IACD,OAAO;QACL3B,WAAW,EAAE,IAAI;QACjBuC,QAAQ;QACRtC,eAAe,EAAEL,sBAAsB,CAACE,SAAS,CAACC,MAAM;QACxDG,eAAe,EAAEQ,OAAO,CAACkC,cAAc,EAAE,CAAC7C,MAAM;QAChDI,WAAW;KACZ,CAAA;CACF"}