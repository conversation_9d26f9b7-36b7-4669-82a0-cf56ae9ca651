{"version": 3, "sources": ["../../../lib/typescript/diagnosticFormatter.ts"], "names": ["getFormattedDiagnostic", "DiagnosticCategory", "Warning", "Error", "Suggestion", "Message", "ts", "baseDir", "diagnostic", "message", "reason", "flattenDiagnosticMessageText", "messageText", "category", "chalk", "yellow", "bold", "red", "cyan", "file", "pos", "getLineAndCharacterOfPosition", "start", "line", "character", "fileName", "path", "posix", "normalize", "relative", "replace", "startsWith", "toString", "codeFrameColumns", "getFullText", "getSourceFile", "column", "forceColor"], "mappings": "AAAA;;;;QAasBA,sBAAsB,GAAtBA,sBAAsB;;AAbX,IAAA,UAAqC,WAArC,qCAAqC,CAAA;AACpD,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAC3B,IAAA,KAAM,kCAAN,MAAM,EAAA;;;;;;IAIhB,kBAKN;;UALWC,kBAAkB;IAAlBA,kBAAkB,CAAlBA,kBAAkB,CAC5BC,SAAO,IAAG,CAAC,IAAXA,SAAO;IADGD,kBAAkB,CAAlBA,kBAAkB,CAE5BE,OAAK,IAAG,CAAC,IAATA,OAAK;IAFKF,kBAAkB,CAAlBA,kBAAkB,CAG5BG,YAAU,IAAG,CAAC,IAAdA,YAAU;IAHAH,kBAAkB,CAAlBA,kBAAkB,CAI5BI,SAAO,IAAG,CAAC,IAAXA,SAAO;GAJGJ,kBAAkB,kCAAlBA,kBAAkB;AAOvB,eAAeD,sBAAsB,CAC1CM,EAA+B,EAC/BC,OAAe,EACfC,UAA2C,EAC1B;IACjB,IAAIC,OAAO,GAAG,EAAE;IAEhB,MAAMC,MAAM,GAAGJ,EAAE,CAACK,4BAA4B,CAACH,UAAU,CAACI,WAAW,EAAE,IAAI,CAAC;IAC5E,MAAMC,QAAQ,GAAGL,UAAU,CAACK,QAAQ;IACpC,OAAQA,QAAQ;QACd,UAAU;QACV,KAjBQ,CAAC;YAiBwB;gBAC/BJ,OAAO,IAAIK,MAAK,QAAA,CAACC,MAAM,CAACC,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI;gBACnD,MAAK;aACN;QACD,QAAQ;QACR,KArBM,CAAC;YAqBwB;gBAC7BP,OAAO,IAAIK,MAAK,QAAA,CAACG,GAAG,CAACD,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI;gBAC9C,MAAK;aACN;QACD,8BAA8B;QAC9B,KAzBW,CAAC,CAyBuB;QACnC,KAzBQ,CAAC,CAyBuB;QAChC;YAAS;gBACPP,OAAO,IAAIK,MAAK,QAAA,CAACI,IAAI,CAACF,IAAI,CAACH,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,GAAG,IAAI;gBACzE,MAAK;aACN;KACF;IACDJ,OAAO,IAAIC,MAAM,GAAG,IAAI;IAExB,IAAIF,UAAU,CAACW,IAAI,EAAE;QACnB,MAAMC,GAAG,GAAGZ,UAAU,CAACW,IAAI,CAACE,6BAA6B,CAACb,UAAU,CAACc,KAAK,CAAE;QAC5E,MAAMC,IAAI,GAAGH,GAAG,CAACG,IAAI,GAAG,CAAC;QACzB,MAAMC,SAAS,GAAGJ,GAAG,CAACI,SAAS,GAAG,CAAC;QAEnC,IAAIC,QAAQ,GAAGC,KAAI,QAAA,CAACC,KAAK,CAACC,SAAS,CACjCF,KAAI,QAAA,CAACG,QAAQ,CAACtB,OAAO,EAAEC,UAAU,CAACW,IAAI,CAACM,QAAQ,CAAC,CAACK,OAAO,QAAQ,GAAG,CAAC,CACrE;QACD,IAAI,CAACL,QAAQ,CAACM,UAAU,CAAC,GAAG,CAAC,EAAE;YAC7BN,QAAQ,GAAG,IAAI,GAAGA,QAAQ;SAC3B;QAEDhB,OAAO,GACLK,MAAK,QAAA,CAACI,IAAI,CAACO,QAAQ,CAAC,GACpB,GAAG,GACHX,MAAK,QAAA,CAACC,MAAM,CAACQ,IAAI,CAACS,QAAQ,EAAE,CAAC,GAC7B,GAAG,GACHlB,MAAK,QAAA,CAACC,MAAM,CAACS,SAAS,CAACQ,QAAQ,EAAE,CAAC,GAClC,IAAI,GACJvB,OAAO;QAETA,OAAO,IACL,IAAI,GACJwB,CAAAA,GAAAA,UAAgB,AAMf,CAAA,iBANe,CACdzB,UAAU,CAACW,IAAI,CAACe,WAAW,CAAC1B,UAAU,CAACW,IAAI,CAACgB,aAAa,EAAE,CAAC,EAC5D;YACEb,KAAK,EAAE;gBAAEC,IAAI,EAAEA,IAAI;gBAAEa,MAAM,EAAEZ,SAAS;aAAE;SACzC,EACD;YAAEa,UAAU,EAAE,IAAI;SAAE,CACrB;KACJ;IAED,OAAO5B,OAAO,CAAA;CACf"}