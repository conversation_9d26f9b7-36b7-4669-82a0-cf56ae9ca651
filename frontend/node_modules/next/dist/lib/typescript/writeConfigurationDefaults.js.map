{"version": 3, "sources": ["../../../lib/typescript/writeConfigurationDefaults.ts"], "names": ["getRequiredConfiguration", "writeConfigurationDefaults", "CommentJson", "getDesiredCompilerOptions", "ts", "o", "target", "suggested", "lib", "allowJs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strict", "forceConsistentCasingInFileNames", "noEmit", "semver", "gte", "version", "incremental", "undefined", "esModuleInterop", "value", "reason", "module", "parsedValue", "Module<PERSON>ind", "ESNext", "parsed<PERSON><PERSON>ues", "ES2020", "CommonJS", "AMD", "moduleResolution", "ModuleResolutionKind", "NodeJs", "Node12", "Node16", "NodeNext", "filter", "val", "resolveJsonModule", "isolatedModules", "jsx", "JsxEmit", "Preserve", "res", "desiredCompilerOptions", "optionKey", "Object", "keys", "ev", "tsConfigPath", "isFirstTimeSetup", "fs", "writeFile", "os", "EOL", "options", "tsOptions", "raw", "rawConfig", "getTypeScriptConfiguration", "userTsConfigContent", "readFile", "encoding", "userTsConfig", "parse", "compilerOptions", "suggestedActions", "requiredActions", "check", "push", "chalk", "cyan", "bold", "includes", "_", "include", "exclude", "length", "stringify", "console", "log", "green", "for<PERSON>ach", "action"], "mappings": "AAAA;;;;QAoFgBA,wBAAwB,GAAxBA,wBAAwB;QAiBlBC,0BAA0B,GAA1BA,0BAA0B;AArGjB,IAAA,GAAI,WAAJ,IAAI,CAAA;AACjB,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAChCC,IAAAA,WAAW,mCAAM,iCAAiC,EAAvC;AACJ,IAAA,OAA2B,kCAA3B,2BAA2B,EAAA;AAC/B,IAAA,GAAI,kCAAJ,IAAI,EAAA;AAEwB,IAAA,2BAA8B,WAA9B,8BAA8B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAazE,SAASC,yBAAyB,CAChCC,EAA+B,EACF;IAC7B,MAAMC,CAAC,GAAgC;QACrC,qEAAqE;QACrE,gBAAgB;QAChBC,MAAM,EAAE;YAAEC,SAAS,EAAE,KAAK;SAAE;QAC5BC,GAAG,EAAE;YAAED,SAAS,EAAE;gBAAC,KAAK;gBAAE,cAAc;gBAAE,QAAQ;aAAC;SAAE;QACrDE,OAAO,EAAE;YAAEF,SAAS,EAAE,IAAI;SAAE;QAC5BG,YAAY,EAAE;YAAEH,SAAS,EAAE,IAAI;SAAE;QACjCI,MAAM,EAAE;YAAEJ,SAAS,EAAE,KAAK;SAAE;QAC5BK,gCAAgC,EAAE;YAAEL,SAAS,EAAE,IAAI;SAAE;QACrDM,MAAM,EAAE;YAAEN,SAAS,EAAE,IAAI;SAAE;QAC3B,GAAIO,OAAM,QAAA,CAACC,GAAG,CAACX,EAAE,CAACY,OAAO,EAAE,OAAO,CAAC,GAC/B;YAAEC,WAAW,EAAE;gBAAEV,SAAS,EAAE,IAAI;aAAE;SAAE,GACpCW,SAAS;QAEb,8DAA8D;QAC9D,4CAA4C;QAC5C,8EAA8E;QAC9EC,eAAe,EAAE;YACfC,KAAK,EAAE,IAAI;YACXC,MAAM,EAAE,6BAA6B;SACtC;QACDC,MAAM,EAAE;YACNC,WAAW,EAAEnB,EAAE,CAACoB,UAAU,CAACC,MAAM;YACjC,4BAA4B;YAC5BC,YAAY,EAAE;gBACZtB,EAAE,CAACoB,UAAU,CAACG,MAAM;gBACpBvB,EAAE,CAACoB,UAAU,CAACC,MAAM;gBACpBrB,EAAE,CAACoB,UAAU,CAACI,QAAQ;gBACtBxB,EAAE,CAACoB,UAAU,CAACK,GAAG;aAClB;YACDT,KAAK,EAAE,QAAQ;YACfC,MAAM,EAAE,8BAA8B;SACvC;QACDS,gBAAgB,EAAE;YAChBP,WAAW,EAAEnB,EAAE,CAAC2B,oBAAoB,CAACC,MAAM;YAC3C,4BAA4B;YAC5BN,YAAY,EAAE;gBACZtB,EAAE,CAAC2B,oBAAoB,CAACC,MAAM;gBAC9B,qDAAqD;gBACrD,kDAAkD;gBAClD,CAAC5B,EAAE,CAAC2B,oBAAoB,CAAQ,CAACE,MAAM;gBACvC7B,EAAE,CAAC2B,oBAAoB,CAACG,MAAM;gBAC9B9B,EAAE,CAAC2B,oBAAoB,CAACI,QAAQ;aACjC,CAACC,MAAM,CAAC,CAACC,GAAG,GAAK,OAAOA,GAAG,KAAK,WAAW,CAAC;YAC7CjB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,6BAA6B;SACtC;QACDiB,iBAAiB,EAAE;YAAElB,KAAK,EAAE,IAAI;YAAEC,MAAM,EAAE,6BAA6B;SAAE;QACzEkB,eAAe,EAAE;YACfnB,KAAK,EAAE,IAAI;YACXC,MAAM,EAAE,6BAA6B;SACtC;QACDmB,GAAG,EAAE;YACHjB,WAAW,EAAEnB,EAAE,CAACqC,OAAO,CAACC,QAAQ;YAChCtB,KAAK,EAAE,UAAU;YACjBC,MAAM,EAAE,oDAAoD;SAC7D;KACF;IAED,OAAOhB,CAAC,CAAA;CACT;AAEM,SAASL,wBAAwB,CACtCI,EAA+B,EACgB;IAC/C,MAAMuC,GAAG,GAAkD,EAAE;IAE7D,MAAMC,sBAAsB,GAAGzC,yBAAyB,CAACC,EAAE,CAAC;IAC5D,KAAK,MAAMyC,SAAS,IAAIC,MAAM,CAACC,IAAI,CAACH,sBAAsB,CAAC,CAAE;QAC3D,MAAMI,EAAE,GAAGJ,sBAAsB,CAACC,SAAS,CAAC;QAC5C,IAAI,CAAC,CAAC,OAAO,IAAIG,EAAE,CAAC,EAAE;YACpB,SAAQ;SACT;YACgBA,YAAc;QAA/BL,GAAG,CAACE,SAAS,CAAC,GAAGG,CAAAA,YAAc,GAAdA,EAAE,CAACzB,WAAW,YAAdyB,YAAc,GAAIA,EAAE,CAAC5B,KAAK;KAC5C;IAED,OAAOuB,GAAG,CAAA;CACX;AAEM,eAAe1C,0BAA0B,CAC9CG,EAA+B,EAC/B6C,YAAoB,EACpBC,gBAAyB,EACV;IACf,IAAIA,gBAAgB,EAAE;QACpB,MAAMC,GAAE,SAAA,CAACC,SAAS,CAACH,YAAY,EAAE,IAAI,GAAGI,GAAE,QAAA,CAACC,GAAG,CAAC;KAChD;IAED,MAAMV,sBAAsB,GAAGzC,yBAAyB,CAACC,EAAE,CAAC;IAC5D,MAAM,EAAEmD,OAAO,EAAEC,SAAS,CAAA,EAAEC,GAAG,EAAEC,SAAS,CAAA,EAAE,GAC1C,MAAMC,CAAAA,GAAAA,2BAA0B,AAAwB,CAAA,2BAAxB,CAACvD,EAAE,EAAE6C,YAAY,EAAE,IAAI,CAAC;IAE1D,MAAMW,mBAAmB,GAAG,MAAMT,GAAE,SAAA,CAACU,QAAQ,CAACZ,YAAY,EAAE;QAC1Da,QAAQ,EAAE,MAAM;KACjB,CAAC;IACF,MAAMC,YAAY,GAAG7D,WAAW,CAAC8D,KAAK,CAACJ,mBAAmB,CAAC;IAC3D,IAAIG,YAAY,CAACE,eAAe,IAAI,IAAI,IAAI,CAAC,CAAC,SAAS,IAAIP,SAAS,CAAC,EAAE;QACrEK,YAAY,CAACE,eAAe,GAAG,EAAE;QACjCf,gBAAgB,GAAG,IAAI;KACxB;IAED,MAAMgB,gBAAgB,GAAa,EAAE;IACrC,MAAMC,eAAe,GAAa,EAAE;IACpC,KAAK,MAAMtB,SAAS,IAAIC,MAAM,CAACC,IAAI,CAACH,sBAAsB,CAAC,CAAE;QAC3D,MAAMwB,KAAK,GAAGxB,sBAAsB,CAACC,SAAS,CAAC;QAC/C,IAAI,WAAW,IAAIuB,KAAK,EAAE;YACxB,IAAI,CAAC,CAACvB,SAAS,IAAIW,SAAS,CAAC,EAAE;gBAC7B,IAAI,CAACO,YAAY,CAACE,eAAe,EAAE;oBACjCF,YAAY,CAACE,eAAe,GAAG,EAAE;iBAClC;gBACDF,YAAY,CAACE,eAAe,CAACpB,SAAS,CAAC,GAAGuB,KAAK,CAAC7D,SAAS;gBACzD2D,gBAAgB,CAACG,IAAI,CACnBC,MAAK,QAAA,CAACC,IAAI,CAAC1B,SAAS,CAAC,GAAG,cAAc,GAAGyB,MAAK,QAAA,CAACE,IAAI,CAACJ,KAAK,CAAC7D,SAAS,CAAC,CACrE;aACF;SACF,MAAM,IAAI,OAAO,IAAI6D,KAAK,EAAE;gBAIrBA,GAAkB;YAHxB,MAAMpB,EAAE,GAAGQ,SAAS,CAACX,SAAS,CAAC;YAC/B,IACE,CAAC,CAAC,cAAc,IAAIuB,KAAK,GACrBA,CAAAA,GAAkB,GAAlBA,KAAK,CAAC1C,YAAY,SAAU,GAA5B0C,KAAAA,CAA4B,GAA5BA,GAAkB,CAAEK,QAAQ,CAACzB,EAAE,CAAC,GAChC,aAAa,IAAIoB,KAAK,GACtBA,KAAK,CAAC7C,WAAW,KAAKyB,EAAE,GACxBoB,KAAK,CAAChD,KAAK,KAAK4B,EAAE,CAAC,EACvB;gBACA,IAAI,CAACe,YAAY,CAACE,eAAe,EAAE;oBACjCF,YAAY,CAACE,eAAe,GAAG,EAAE;iBAClC;gBACDF,YAAY,CAACE,eAAe,CAACpB,SAAS,CAAC,GAAGuB,KAAK,CAAChD,KAAK;gBACrD+C,eAAe,CAACE,IAAI,CAClBC,MAAK,QAAA,CAACC,IAAI,CAAC1B,SAAS,CAAC,GACnB,cAAc,GACdyB,MAAK,QAAA,CAACE,IAAI,CAACJ,KAAK,CAAChD,KAAK,CAAC,GACvB,CAAC,EAAE,EAAEgD,KAAK,CAAC/C,MAAM,CAAC,CAAC,CAAC,CACvB;aACF;SACF,MAAM;YACL,6DAA6D;YAC7D,MAAMqD,CAAC,GAAUN,KAAK;SACvB;KACF;IAED,IAAI,CAAC,CAAC,SAAS,IAAIV,SAAS,CAAC,EAAE;QAC7BK,YAAY,CAACY,OAAO,GAAG;YAAC,eAAe;YAAE,SAAS;YAAE,UAAU;SAAC;QAC/DT,gBAAgB,CAACG,IAAI,CACnBC,MAAK,QAAA,CAACC,IAAI,CAAC,SAAS,CAAC,GACnB,cAAc,GACdD,MAAK,QAAA,CAACE,IAAI,CAAC,CAAC,wCAAwC,CAAC,CAAC,CACzD;KACF;IAED,IAAI,CAAC,CAAC,SAAS,IAAId,SAAS,CAAC,EAAE;QAC7BK,YAAY,CAACa,OAAO,GAAG;YAAC,cAAc;SAAC;QACvCV,gBAAgB,CAACG,IAAI,CACnBC,MAAK,QAAA,CAACC,IAAI,CAAC,SAAS,CAAC,GAAG,cAAc,GAAGD,MAAK,QAAA,CAACE,IAAI,CAAC,CAAC,gBAAgB,CAAC,CAAC,CACxE;KACF;IAED,IAAIN,gBAAgB,CAACW,MAAM,GAAG,CAAC,IAAIV,eAAe,CAACU,MAAM,GAAG,CAAC,EAAE;QAC7D,OAAM;KACP;IAED,MAAM1B,GAAE,SAAA,CAACC,SAAS,CAChBH,YAAY,EACZ/C,WAAW,CAAC4E,SAAS,CAACf,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGV,GAAE,QAAA,CAACC,GAAG,CACtD;IAED,IAAIJ,gBAAgB,EAAE;QACpB6B,OAAO,CAACC,GAAG,CACTV,MAAK,QAAA,CAACW,KAAK,CACT,CAAC,qDAAqD,EAAEX,MAAK,QAAA,CAACE,IAAI,CAChE,eAAe,CAChB,CAAC,cAAc,CAAC,CAClB,GAAG,IAAI,CACT;QACD,OAAM;KACP;IAEDO,OAAO,CAACC,GAAG,CACTV,MAAK,QAAA,CAACW,KAAK,CACT,CAAC,6DAA6D,EAAEX,MAAK,QAAA,CAACE,IAAI,CACxE,eAAe,CAChB,CAAC,qCAAqC,EAAEF,MAAK,QAAA,CAACE,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAC3E,GAAG,IAAI,CACT;IACD,IAAIN,gBAAgB,CAACW,MAAM,EAAE;QAC3BE,OAAO,CAACC,GAAG,CACT,CAAC,kDAAkD,EAAEV,MAAK,QAAA,CAACC,IAAI,CAC7D,eAAe,CAChB,CAAC,eAAe,EAAED,MAAK,QAAA,CAACE,IAAI,CAC3B,gBAAgB,CACjB,CAAC,+BAA+B,CAAC,CACnC;QAEDN,gBAAgB,CAACgB,OAAO,CAAC,CAACC,MAAM,GAAKJ,OAAO,CAACC,GAAG,CAAC,CAAC,IAAI,EAAEG,MAAM,CAAC,CAAC,CAAC,CAAC;QAElEJ,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;KAChB;IAED,IAAIb,eAAe,CAACU,MAAM,EAAE;QAC1BE,OAAO,CAACC,GAAG,CACT,CAAC,cAAc,EAAEV,MAAK,QAAA,CAACE,IAAI,CACzB,mBAAmB,CACpB,CAAC,mBAAmB,EAAEF,MAAK,QAAA,CAACC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CACxD;QAEDJ,eAAe,CAACe,OAAO,CAAC,CAACC,MAAM,GAAKJ,OAAO,CAACC,GAAG,CAAC,CAAC,IAAI,EAAEG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEjEJ,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;KAChB;CACF"}