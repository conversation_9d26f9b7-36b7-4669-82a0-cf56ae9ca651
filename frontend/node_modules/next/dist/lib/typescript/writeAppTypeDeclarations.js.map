{"version": 3, "sources": ["../../../lib/typescript/writeAppTypeDeclarations.ts"], "names": ["writeAppTypeDeclarations", "baseDir", "imageImportsEnabled", "appTypeDeclarations", "path", "join", "eol", "os", "EOL", "currentC<PERSON>nt", "fs", "readFile", "lf", "indexOf", "err", "content", "writeFile"], "mappings": "AAAA;;;;QAIsBA,wBAAwB,GAAxBA,wBAAwB;AAJ/B,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACQ,IAAA,GAAI,WAAJ,IAAI,CAAA;;;;;;AAE5B,eAAeA,wBAAwB,CAC5CC,OAAe,EACfC,mBAA4B,EACb;IACf,yBAAyB;IACzB,MAAMC,mBAAmB,GAAGC,KAAI,QAAA,CAACC,IAAI,CAACJ,OAAO,EAAE,eAAe,CAAC;IAE/D,iCAAiC;IACjC,IAAIK,GAAG,GAAGC,GAAE,QAAA,CAACC,GAAG;IAChB,IAAIC,cAAc,AAAoB;IAEtC,IAAI;QACFA,cAAc,GAAG,MAAMC,GAAE,SAAA,CAACC,QAAQ,CAACR,mBAAmB,EAAE,MAAM,CAAC;QAC/D,uDAAuD;QACvD,MAAMS,EAAE,GAAGH,cAAc,CAACI,OAAO,CAAC,IAAI,EAAE,iCAAiC,CAAC,CAAC,CAAC;QAE5E,IAAID,EAAE,KAAK,CAAC,CAAC,EAAE;YACb,IAAIH,cAAc,CAACG,EAAE,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;gBACnCN,GAAG,GAAG,MAAM;aACb,MAAM;gBACLA,GAAG,GAAG,IAAI;aACX;SACF;KACF,CAAC,OAAOQ,GAAG,EAAE,EAAE;IAEhB,MAAMC,OAAO,GACX,gCAAgC,GAChCT,GAAG,GACH,CAACJ,mBAAmB,GAChB,mDAAmD,GAAGI,GAAG,GACzD,EAAE,CAAC,GACPA,GAAG,GACH,yCAAyC,GACzCA,GAAG,GACH,gFAAgF,GAChFA,GAAG;IAEL,+CAA+C;IAC/C,IAAIG,cAAc,KAAKM,OAAO,EAAE;QAC9B,OAAM;KACP;IACD,MAAML,GAAE,SAAA,CAACM,SAAS,CAACb,mBAAmB,EAAEY,OAAO,CAAC;CACjD"}