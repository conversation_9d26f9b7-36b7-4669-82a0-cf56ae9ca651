{"version": 3, "sources": ["../../lib/find-config.ts"], "names": ["findConfig", "directory", "key", "packageJsonPath", "findUp", "cwd", "packageJson", "require", "filePath", "endsWith", "fileContents", "fs", "readFileSync", "JSON5", "parse"], "mappings": "AAAA;;;;QAWsBA,UAAU,GAAVA,UAAU;AAXb,IAAA,OAA4B,kCAA5B,4BAA4B,EAAA;AAChC,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACD,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;;;;;;AASrC,eAAeA,UAAU,CAC9BC,SAAiB,EACjBC,GAAW,EAC0B;IACrC,oEAAoE;IACpE,MAAMC,eAAe,GAAG,MAAMC,CAAAA,GAAAA,OAAM,AAAoC,CAAA,QAApC,CAAC,cAAc,EAAE;QAAEC,GAAG,EAAEJ,SAAS;KAAE,CAAC;IACxE,IAAIE,eAAe,EAAE;QACnB,MAAMG,WAAW,GAAGC,OAAO,CAACJ,eAAe,CAAC;QAC5C,IAAIG,WAAW,CAACJ,GAAG,CAAC,IAAI,IAAI,IAAI,OAAOI,WAAW,CAACJ,GAAG,CAAC,KAAK,QAAQ,EAAE;YACpE,OAAOI,WAAW,CAACJ,GAAG,CAAC,CAAA;SACxB;KACF;IAED,4EAA4E;IAC5E,mBAAmB;IACnB,MAAMM,QAAQ,GAAG,MAAMJ,CAAAA,GAAAA,OAAM,AAW5B,CAAA,QAX4B,CAC3B;QACE,CAAC,CAAC,EAAEF,GAAG,CAAC,OAAO,CAAC;QAChB,CAAC,EAAEA,GAAG,CAAC,YAAY,CAAC;QACpB,CAAC,CAAC,EAAEA,GAAG,CAAC,KAAK,CAAC;QACd,CAAC,EAAEA,GAAG,CAAC,UAAU,CAAC;QAClB,CAAC,EAAEA,GAAG,CAAC,WAAW,CAAC;KACpB,EACD;QACEG,GAAG,EAAEJ,SAAS;KACf,CACF;IACD,IAAIO,QAAQ,EAAE;QACZ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACzD,OAAOF,OAAO,CAACC,QAAQ,CAAC,CAAA;SACzB;QAED,sEAAsE;QACtE,kEAAkE;QAClE,MAAME,YAAY,GAAGC,GAAE,QAAA,CAACC,YAAY,CAACJ,QAAQ,EAAE,MAAM,CAAC;QACtD,OAAOK,MAAK,QAAA,CAACC,KAAK,CAACJ,YAAY,CAAC,CAAA;KACjC;IAED,OAAO,IAAI,CAAA;CACZ"}