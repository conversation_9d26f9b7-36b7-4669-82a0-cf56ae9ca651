{"version": 3, "sources": ["../../../lib/eslint/hasEslintConfiguration.ts"], "names": ["hasEslintConfiguration", "eslintrcFile", "packageJsonConfig", "configObject", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "content", "fs", "readFile", "encoding", "then", "txt", "trim", "replace", "eslintConfig", "Object", "keys", "length"], "mappings": "AAAA;;;;QASsBA,sBAAsB,GAAtBA,sBAAsB;AATb,IAAA,GAAI,WAAJ,IAAI,CAAA;AAS5B,eAAeA,sBAAsB,CAC1CC,YAA2B,EAC3BC,iBAA+C,EACrB;IAC1B,MAAMC,YAAY,GAAG;QACnBC,MAAM,EAAE,KAAK;QACbC,aAAa,EAAE,KAAK;QACpBC,kBAAkB,EAAE,KAAK;KAC1B;IAED,IAAIL,YAAY,EAAE;QAChB,MAAMM,OAAO,GAAG,MAAMC,GAAE,SAAA,CAACC,QAAQ,CAACR,YAAY,EAAE;YAAES,QAAQ,EAAE,MAAM;SAAE,CAAC,CAACC,IAAI,CACxE,CAACC,GAAG,GAAKA,GAAG,CAACC,IAAI,EAAE,CAACC,OAAO,QAAQ,EAAE,CAAC,EACtC,IAAM,IAAI,CACX;QAED,IACEP,OAAO,KAAK,EAAE,IACdA,OAAO,KAAK,IAAI,IAChBA,OAAO,KAAK,KAAK,IACjBA,OAAO,KAAK,qBAAqB,EACjC;YACA,OAAO;gBAAE,GAAGJ,YAAY;gBAAEE,aAAa,EAAE,IAAI;aAAE,CAAA;SAChD;QACD,OAAO;YAAE,GAAGF,YAAY;YAAEC,MAAM,EAAE,IAAI;SAAE,CAAA;KACzC,MAAM,IAAIF,iBAAiB,QAAc,GAA/BA,KAAAA,CAA+B,GAA/BA,iBAAiB,CAAEa,YAAY,EAAE;QAC1C,IAAIC,MAAM,CAACC,IAAI,CAACf,iBAAiB,QAAc,GAA/BA,KAAAA,CAA+B,GAA/BA,iBAAiB,CAAEa,YAAY,CAAC,CAACG,MAAM,EAAE;YACvD,OAAO;gBAAE,GAAGf,YAAY;gBAAEC,MAAM,EAAE,IAAI;aAAE,CAAA;SACzC;QACD,OAAO;YAAE,GAAGD,YAAY;YAAEG,kBAAkB,EAAE,IAAI;SAAE,CAAA;KACrD;IACD,OAAOH,YAAY,CAAA;CACpB"}