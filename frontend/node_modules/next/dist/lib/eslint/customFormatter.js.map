{"version": 3, "sources": ["../../../lib/eslint/customFormatter.ts"], "names": ["formatResults", "MessageSeverity", "Warning", "Error", "pluginCount", "messages", "nextPluginWarningCount", "nextPluginErrorCount", "i", "length", "severity", "ruleId", "includes", "formatMessage", "dir", "filePath", "fileName", "path", "posix", "normalize", "relative", "replace", "startsWith", "output", "chalk", "cyan", "message", "line", "column", "yellow", "toString", "bold", "red", "gray", "baseDir", "results", "format", "totalNextPluginErrorCount", "totalNextPluginWarningCount", "resultsWithMessages", "filter", "for<PERSON>ach", "res", "map", "join", "outputWithMessages"], "mappings": "AAAA;;;;QAgGgBA,aAAa,GAAbA,aAAa;;AAhGX,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAC3B,IAAA,KAAM,kCAAN,MAAM,EAAA;;;;;;IAGhB,eAGN;;UAHWC,eAAe;IAAfA,eAAe,CAAfA,eAAe,CACzBC,SAAO,IAAG,CAAC,IAAXA,SAAO;IADGD,eAAe,CAAfA,eAAe,CAEzBE,OAAK,IAAG,CAAC,IAATA,OAAK;GAFKF,eAAe,+BAAfA,eAAe;AAsB3B,SAASG,WAAW,CAACC,QAAuB,EAG1C;IACA,IAAIC,sBAAsB,GAAG,CAAC;IAC9B,IAAIC,oBAAoB,GAAG,CAAC;IAE5B,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,CAAE;QACxC,MAAM,EAAEE,QAAQ,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAGN,QAAQ,CAACG,CAAC,CAAC;QAExC,IAAIG,MAAM,QAAU,GAAhBA,KAAAA,CAAgB,GAAhBA,MAAM,CAAEC,QAAQ,CAAC,YAAY,CAAC,EAAE;YAClC,IAAIF,QAAQ,KAhCN,CAAC,AAgCiC,EAAE;gBACxCJ,sBAAsB,IAAI,CAAC;aAC5B,MAAM;gBACLC,oBAAoB,IAAI,CAAC;aAC1B;SACF;KACF;IAED,OAAO;QACLA,oBAAoB;QACpBD,sBAAsB;KACvB,CAAA;CACF;AAED,SAASO,aAAa,CACpBC,GAAW,EACXT,QAAuB,EACvBU,QAAgB,EACR;IACR,IAAIC,QAAQ,GAAGC,KAAI,QAAA,CAACC,KAAK,CAACC,SAAS,CACjCF,KAAI,QAAA,CAACG,QAAQ,CAACN,GAAG,EAAEC,QAAQ,CAAC,CAACM,OAAO,QAAQ,GAAG,CAAC,CACjD;IAED,IAAI,CAACL,QAAQ,CAACM,UAAU,CAAC,GAAG,CAAC,EAAE;QAC7BN,QAAQ,GAAG,IAAI,GAAGA,QAAQ;KAC3B;IAED,IAAIO,MAAM,GAAG,IAAI,GAAGC,MAAK,QAAA,CAACC,IAAI,CAACT,QAAQ,CAAC;IAExC,IAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAACI,MAAM,EAAED,CAAC,EAAE,CAAE;QACxC,MAAM,EAAEkB,OAAO,CAAA,EAAEhB,QAAQ,CAAA,EAAEiB,IAAI,CAAA,EAAEC,MAAM,CAAA,EAAEjB,MAAM,CAAA,EAAE,GAAGN,QAAQ,CAACG,CAAC,CAAC;QAE/De,MAAM,GAAGA,MAAM,GAAG,IAAI;QAEtB,IAAII,IAAI,IAAIC,MAAM,EAAE;YAClBL,MAAM,GACJA,MAAM,GACNC,MAAK,QAAA,CAACK,MAAM,CAACF,IAAI,CAACG,QAAQ,EAAE,CAAC,GAC7B,GAAG,GACHN,MAAK,QAAA,CAACK,MAAM,CAACD,MAAM,CAACE,QAAQ,EAAE,CAAC,GAC/B,IAAI;SACP;QAED,IAAIpB,QAAQ,KA3EJ,CAAC,AA2E+B,EAAE;YACxCa,MAAM,IAAIC,MAAK,QAAA,CAACK,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI;SAC9C,MAAM;YACLR,MAAM,IAAIC,MAAK,QAAA,CAACQ,GAAG,CAACD,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI;SACzC;QAEDR,MAAM,IAAIG,OAAO;QAEjB,IAAIf,MAAM,EAAE;YACVY,MAAM,IAAI,IAAI,GAAGC,MAAK,QAAA,CAACS,IAAI,CAACF,IAAI,CAACpB,MAAM,CAAC;SACzC;KACF;IAED,OAAOY,MAAM,CAAA;CACd;AAEM,SAASvB,aAAa,CAC3BkC,OAAe,EACfC,OAAqB,EACrBC,MAAmC,EAMnC;IACA,IAAIC,yBAAyB,GAAG,CAAC;IACjC,IAAIC,2BAA2B,GAAG,CAAC;IACnC,IAAIC,mBAAmB,GAAGJ,OAAO,CAACK,MAAM,CAAC,CAAC,EAAEnC,QAAQ,CAAA,EAAE;QAAKA,OAAAA,QAAQ,QAAQ,GAAhBA,KAAAA,CAAgB,GAAhBA,QAAQ,CAAEI,MAAM,CAAA;KAAA,CAAC;IAE5E,qDAAqD;IACrD8B,mBAAmB,CAACE,OAAO,CAAC,CAAC,EAAEpC,QAAQ,CAAA,EAAE,GAAK;QAC5C,MAAMqC,GAAG,GAAGtC,WAAW,CAACC,QAAQ,CAAC;QACjCgC,yBAAyB,IAAIK,GAAG,CAACnC,oBAAoB;QACrD+B,2BAA2B,IAAII,GAAG,CAACpC,sBAAsB;KAC1D,CAAC;IAEF,oEAAoE;IACpE,MAAMiB,MAAM,GAAGa,MAAM,GACjBA,MAAM,CAACG,mBAAmB,CAAC,GAC3BA,mBAAmB,CAChBI,GAAG,CAAC,CAAC,EAAEtC,QAAQ,CAAA,EAAEU,QAAQ,CAAA,EAAE,GAC1BF,aAAa,CAACqB,OAAO,EAAE7B,QAAQ,EAAEU,QAAQ,CAAC,CAC3C,CACA6B,IAAI,CAAC,IAAI,CAAC;IAEjB,OAAO;QACLrB,MAAM,EAAEA,MAAM;QACdsB,kBAAkB,EAChBN,mBAAmB,CAAC9B,MAAM,GAAG,CAAC,GAC1Bc,MAAM,GACN,CAAC,IAAI,EAAEC,MAAK,QAAA,CAACC,IAAI,CACf,MAAM,CACP,CAAC,qHAAqH,CAAC,GACxH,EAAE;QACRY,yBAAyB;QACzBC,2BAA2B;KAC5B,CAAA;CACF"}