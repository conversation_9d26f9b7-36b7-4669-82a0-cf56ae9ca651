{"version": 3, "sources": ["../../../lib/eslint/writeOutputFile.ts"], "names": ["writeOutputFile", "Log", "isDirectory", "filePath", "fs", "stat", "error", "isError", "code", "outputFile", "outputData", "path", "resolve", "process", "cwd", "mkdir", "dirname", "recursive", "writeFile", "info", "err", "console"], "mappings": "AAAA;;;;QA4BsBA,eAAe,GAAfA,eAAe;AA5BN,IAAA,GAAI,WAAJ,IAAI,CAAA;AAClB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACXC,IAAAA,GAAG,mCAAM,wBAAwB,EAA9B;AACK,IAAA,QAAoB,kCAApB,oBAAoB,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExC;;;;GAIG,CACH,eAAeC,WAAW,CAACC,QAAgB,EAAoB;IAC7D,IAAI;QACF,OAAO,CAAC,MAAMC,GAAE,SAAA,CAACC,IAAI,CAACF,QAAQ,CAAC,CAAC,CAACD,WAAW,EAAE,CAAA;KAC/C,CAAC,OAAOI,KAAK,EAAE;QACd,IACEC,CAAAA,GAAAA,QAAO,AAAO,CAAA,QAAP,CAACD,KAAK,CAAC,IACd,CAACA,KAAK,CAACE,IAAI,KAAK,QAAQ,IAAIF,KAAK,CAACE,IAAI,KAAK,SAAS,CAAC,EACrD;YACA,OAAO,KAAK,CAAA;SACb;QACD,MAAMF,KAAK,CAAA;KACZ;CACF;AAMM,eAAeN,eAAe,CAACS,UAAkB,EAAEC,UAAkB,EAAE;IAC5E,MAAMP,QAAQ,GAAGQ,KAAI,QAAA,CAACC,OAAO,CAACC,OAAO,CAACC,GAAG,EAAE,EAAEL,UAAU,CAAC;IAExD,IAAI,MAAMP,WAAW,CAACC,QAAQ,CAAC,EAAE;QAC/BF,GAAG,CAACK,KAAK,CACP,CAAC,qDAAqD,EAAEH,QAAQ,CAAC,CAAC,CACnE;KACF,MAAM;QACL,IAAI;YACF,MAAMC,GAAE,SAAA,CAACW,KAAK,CAACJ,KAAI,QAAA,CAACK,OAAO,CAACb,QAAQ,CAAC,EAAE;gBAAEc,SAAS,EAAE,IAAI;aAAE,CAAC;YAC3D,MAAMb,GAAE,SAAA,CAACc,SAAS,CAACf,QAAQ,EAAEO,UAAU,CAAC;YACxCT,GAAG,CAACkB,IAAI,CAAC,CAAC,kCAAkC,EAAEhB,QAAQ,CAAC,CAAC,CAAC;SAC1D,CAAC,OAAOiB,GAAG,EAAE;YACZnB,GAAG,CAACK,KAAK,CAAC,CAAC,6CAA6C,EAAEH,QAAQ,CAAC,CAAC,CAAC;YACrEkB,OAAO,CAACf,KAAK,CAACc,GAAG,CAAC;SACnB;KACF;CACF"}