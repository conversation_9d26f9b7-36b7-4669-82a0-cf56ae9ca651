{"version": 3, "sources": ["../../../lib/eslint/writeDefaultConfig.ts"], "names": ["writeDefaultConfig", "CommentJson", "Log", "baseDir", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "selectedConfig", "eslintrcFile", "pkgJsonPath", "packageJsonConfig", "ext", "path", "extname", "newFileContent", "stringify", "fs", "writeFile", "os", "EOL", "info", "chalk", "bold", "basename", "eslintConfig", "join", "console", "log", "green"], "mappings": "AAAA;;;;QASsBA,kBAAkB,GAAlBA,kBAAkB;AATT,IAAA,GAAI,WAAJ,IAAI,CAAA;AACjB,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAC7B,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACXC,IAAAA,WAAW,mCAAM,iCAAiC,EAAvC;AAGXC,IAAAA,GAAG,mCAAM,wBAAwB,EAA9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAER,eAAeF,kBAAkB,CACtCG,OAAe,EACf,EAAEC,MAAM,CAAA,EAAEC,aAAa,CAAA,EAAEC,kBAAkB,CAAA,EAAmB,EAC9DC,cAAmB,EACnBC,YAA2B,EAC3BC,WAA0B,EAC1BC,iBAA+C,EAC/C;IACA,IAAI,CAACN,MAAM,IAAIC,aAAa,IAAIG,YAAY,EAAE;QAC5C,MAAMG,GAAG,GAAGC,KAAI,QAAA,CAACC,OAAO,CAACL,YAAY,CAAC;QAEtC,IAAIM,cAAc;QAClB,IAAIH,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAK,MAAM,EAAE;YACrCG,cAAc,GAAG,iBAAiB;SACnC,MAAM;YACLA,cAAc,GAAGb,WAAW,CAACc,SAAS,CAACR,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC;YAE/D,IAAII,GAAG,KAAK,KAAK,EAAE;gBACjBG,cAAc,GAAG,mBAAmB,GAAGA,cAAc;aACtD;SACF;QAED,MAAME,GAAE,SAAA,CAACC,SAAS,CAACT,YAAY,EAAEM,cAAc,GAAGI,GAAE,QAAA,CAACC,GAAG,CAAC;QAEzDjB,GAAG,CAACkB,IAAI,CACN,CAAC,gDAAgD,EAAEC,MAAK,QAAA,CAACC,IAAI,CAC3DV,KAAI,QAAA,CAACW,QAAQ,CAACf,YAAY,CAAC,CAC5B,CAAC,yBAAyB,CAAC,CAC7B;KACF,MAAM,IAAI,CAACJ,MAAM,IAAIE,kBAAkB,IAAII,iBAAiB,EAAE;QAC7DA,iBAAiB,CAACc,YAAY,GAAGjB,cAAc;QAE/C,IAAIE,WAAW,EACb,MAAMO,GAAE,SAAA,CAACC,SAAS,CAChBR,WAAW,EACXR,WAAW,CAACc,SAAS,CAACL,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGQ,GAAE,QAAA,CAACC,GAAG,CAC3D;QAEHjB,GAAG,CAACkB,IAAI,CACN,CAAC,qBAAqB,EAAEC,MAAK,QAAA,CAACC,IAAI,CAChC,cAAc,CACf,CAAC,8CAA8C,CAAC,CAClD;KACF,MAAM,IAAI,CAAClB,MAAM,EAAE;QAClB,MAAMY,GAAE,SAAA,CAACC,SAAS,CAChBL,KAAI,QAAA,CAACa,IAAI,CAACtB,OAAO,EAAE,gBAAgB,CAAC,EACpCF,WAAW,CAACc,SAAS,CAACR,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGW,GAAE,QAAA,CAACC,GAAG,CACxD;QAEDO,OAAO,CAACC,GAAG,CACTN,MAAK,QAAA,CAACO,KAAK,CACT,CAAC,eAAe,EAAEP,MAAK,QAAA,CAACC,IAAI,CAC1B,gBAAgB,CACjB,CAAC,uDAAuD,CAAC,CAC3D,CACF;KACF;CACF"}