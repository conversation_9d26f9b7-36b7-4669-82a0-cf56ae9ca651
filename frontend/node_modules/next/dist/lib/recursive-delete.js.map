{"version": 3, "sources": ["../../lib/recursive-delete.ts"], "names": ["recursiveDelete", "sleep", "timeout", "Promise", "resolve", "setTimeout", "unlinkPath", "p", "isDir", "t", "promises", "rmdir", "unlink", "e", "code", "isError", "dir", "exclude", "previousPath", "result", "readdir", "withFileTypes", "all", "map", "part", "absolutePath", "join", "name", "isDirectory", "isSymlink", "isSymbolicLink", "linkPath", "readlink", "stats", "stat", "isAbsolute", "dirname", "_", "pp", "isNotExcluded", "test"], "mappings": "AAAA;;;;QA0CsBA,eAAe,GAAfA,eAAe;AA1CJ,IAAA,GAAI,WAAJ,IAAI,CAAA;AACK,IAAA,KAAM,WAAN,MAAM,CAAA;AAC5B,IAAA,QAAY,kCAAZ,YAAY,EAAA;;;;;;AAEhC,MAAMC,KAAK,GAAG,CAACC,OAAe,GAC5B,IAAIC,OAAO,CAAC,CAACC,OAAO,GAAKC,UAAU,CAACD,OAAO,EAAEF,OAAO,CAAC,CAAC;AAExD,MAAMI,UAAU,GAAG,OAAOC,CAAS,EAAEC,KAAK,GAAG,KAAK,EAAEC,CAAC,GAAG,CAAC,GAAoB;IAC3E,IAAI;QACF,IAAID,KAAK,EAAE;YACT,MAAME,GAAQ,SAAA,CAACC,KAAK,CAACJ,CAAC,CAAC;SACxB,MAAM;YACL,MAAMG,GAAQ,SAAA,CAACE,MAAM,CAACL,CAAC,CAAC;SACzB;KACF,CAAC,OAAOM,CAAC,EAAE;QACV,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,QAAO,AAAG,CAAA,QAAH,CAACF,CAAC,CAAC,IAAIA,CAAC,CAACC,IAAI;QACjC,IACE,CAACA,IAAI,KAAK,OAAO,IACfA,IAAI,KAAK,WAAW,IACpBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,QAAQ,CAAC,IACpBL,CAAC,GAAG,CAAC,EACL;YACA,MAAMR,KAAK,CAACQ,CAAC,GAAG,GAAG,CAAC;YACpB,OAAOH,UAAU,CAACC,CAAC,EAAEC,KAAK,EAAEC,CAAC,EAAE,CAAC,CAAA;SACjC;QAED,IAAIK,IAAI,KAAK,QAAQ,EAAE;YACrB,OAAM;SACP;QAED,MAAMD,CAAC,CAAA;KACR;CACF;AASM,eAAeb,eAAe,CACnCgB,GAAW,EACXC,OAAgB,EAChBC,YAAoB,GAAG,EAAE,EACV;IACf,IAAIC,MAAM;IACV,IAAI;QACFA,MAAM,GAAG,MAAMT,GAAQ,SAAA,CAACU,OAAO,CAACJ,GAAG,EAAE;YAAEK,aAAa,EAAE,IAAI;SAAE,CAAC;KAC9D,CAAC,OAAOR,CAAC,EAAE;QACV,IAAIE,CAAAA,GAAAA,QAAO,AAAG,CAAA,QAAH,CAACF,CAAC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,QAAQ,EAAE;YACrC,OAAM;SACP;QACD,MAAMD,CAAC,CAAA;KACR;IAED,MAAMV,OAAO,CAACmB,GAAG,CACfH,MAAM,CAACI,GAAG,CAAC,OAAOC,IAAY,GAAK;QACjC,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,KAAI,AAAgB,CAAA,KAAhB,CAACV,GAAG,EAAEQ,IAAI,CAACG,IAAI,CAAC;QAEzC,yCAAyC;QACzC,mDAAmD;QACnD,IAAIC,WAAW,GAAGJ,IAAI,CAACI,WAAW,EAAE;QACpC,MAAMC,SAAS,GAAGL,IAAI,CAACM,cAAc,EAAE;QAEvC,IAAID,SAAS,EAAE;YACb,MAAME,QAAQ,GAAG,MAAMrB,GAAQ,SAAA,CAACsB,QAAQ,CAACP,YAAY,CAAC;YAEtD,IAAI;gBACF,MAAMQ,KAAK,GAAG,MAAMvB,GAAQ,SAAA,CAACwB,IAAI,CAC/BC,CAAAA,GAAAA,KAAU,AAAU,CAAA,WAAV,CAACJ,QAAQ,CAAC,GAChBA,QAAQ,GACRL,CAAAA,GAAAA,KAAI,AAAiC,CAAA,KAAjC,CAACU,CAAAA,GAAAA,KAAO,AAAc,CAAA,QAAd,CAACX,YAAY,CAAC,EAAEM,QAAQ,CAAC,CAC1C;gBACDH,WAAW,GAAGK,KAAK,CAACL,WAAW,EAAE;aAClC,CAAC,OAAOS,CAAC,EAAE,EAAE;SACf;QAED,MAAMC,EAAE,GAAGZ,CAAAA,GAAAA,KAAI,AAAyB,CAAA,KAAzB,CAACR,YAAY,EAAEM,IAAI,CAACG,IAAI,CAAC;QACxC,MAAMY,aAAa,GAAG,CAACtB,OAAO,IAAI,CAACA,OAAO,CAACuB,IAAI,CAACF,EAAE,CAAC;QAEnD,IAAIC,aAAa,EAAE;YACjB,IAAIX,WAAW,EAAE;gBACf,MAAM5B,eAAe,CAACyB,YAAY,EAAER,OAAO,EAAEqB,EAAE,CAAC;aACjD;YACD,OAAOhC,UAAU,CAACmB,YAAY,EAAE,CAACI,SAAS,IAAID,WAAW,CAAC,CAAA;SAC3D;KACF,CAAC,CACH;CACF"}