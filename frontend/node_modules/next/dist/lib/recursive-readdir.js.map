{"version": 3, "sources": ["../../lib/recursive-readdir.ts"], "names": ["recursiveReadDir", "dir", "filter", "ignore", "arr", "rootDir", "result", "promises", "readdir", "withFileTypes", "Promise", "all", "map", "part", "absolutePath", "join", "name", "test", "isDirectory", "isSymbolicLink", "stats", "stat", "push", "replace", "sort"], "mappings": "AAAA;;;;QAWsBA,gBAAgB,GAAhBA,gBAAgB;AAXL,IAAA,GAAI,WAAJ,IAAI,CAAA;AAChB,IAAA,KAAM,WAAN,MAAM,CAAA;AAUpB,eAAeA,gBAAgB,CACpCC,GAAW,EACXC,MAAc,EACdC,MAAe,EACfC,GAAa,GAAG,EAAE,EAClBC,OAAe,GAAGJ,GAAG,EACF;IACnB,MAAMK,MAAM,GAAG,MAAMC,GAAQ,SAAA,CAACC,OAAO,CAACP,GAAG,EAAE;QAAEQ,aAAa,EAAE,IAAI;KAAE,CAAC;IAEnE,MAAMC,OAAO,CAACC,GAAG,CACfL,MAAM,CAACM,GAAG,CAAC,OAAOC,IAAY,GAAK;QACjC,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,KAAI,AAAgB,CAAA,KAAhB,CAACd,GAAG,EAAEY,IAAI,CAACG,IAAI,CAAC;QACzC,IAAIb,MAAM,IAAIA,MAAM,CAACc,IAAI,CAACJ,IAAI,CAACG,IAAI,CAAC,EAAE,OAAM;QAE5C,yCAAyC;QACzC,mDAAmD;QACnD,IAAIE,WAAW,GAAGL,IAAI,CAACK,WAAW,EAAE;QACpC,IAAIL,IAAI,CAACM,cAAc,EAAE,EAAE;YACzB,MAAMC,KAAK,GAAG,MAAMb,GAAQ,SAAA,CAACc,IAAI,CAACP,YAAY,CAAC;YAC/CI,WAAW,GAAGE,KAAK,CAACF,WAAW,EAAE;SAClC;QAED,IAAIA,WAAW,EAAE;YACf,MAAMlB,gBAAgB,CAACc,YAAY,EAAEZ,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAEC,OAAO,CAAC;YAClE,OAAM;SACP;QAED,IAAI,CAACH,MAAM,CAACe,IAAI,CAACJ,IAAI,CAACG,IAAI,CAAC,EAAE;YAC3B,OAAM;SACP;QAEDZ,GAAG,CAACkB,IAAI,CAACR,YAAY,CAACS,OAAO,CAAClB,OAAO,EAAE,EAAE,CAAC,CAAC;KAC5C,CAAC,CACH;IAED,OAAOD,GAAG,CAACoB,IAAI,EAAE,CAAA;CAClB"}