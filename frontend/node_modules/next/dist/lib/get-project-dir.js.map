{"version": 3, "sources": ["../../lib/get-project-dir.ts"], "names": ["getProjectDir", "Log", "dir", "resolvedDir", "path", "resolve", "realDir", "fs", "realpathSync", "native", "toLowerCase", "warn", "err", "code", "detectedTypo", "detectTypo", "Object", "keys", "commands", "error", "process", "exit"], "mappings": "AAAA;;;;QAMgBA,aAAa,GAAbA,aAAa;AANd,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACE,IAAA,SAAY,WAAZ,YAAY,CAAA;AACzBC,IAAAA,GAAG,mCAAM,qBAAqB,EAA3B;AACY,IAAA,WAAe,WAAf,eAAe,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,SAASD,aAAa,CAACE,GAAY,EAAE;IAC1C,IAAI;QACF,MAAMC,WAAW,GAAGC,KAAI,QAAA,CAACC,OAAO,CAACH,GAAG,IAAI,GAAG,CAAC;QAC5C,MAAMI,OAAO,GAAGC,GAAE,QAAA,CAACC,YAAY,CAACC,MAAM,CAACN,WAAW,CAAC;QAEnD,IACEA,WAAW,KAAKG,OAAO,IACvBH,WAAW,CAACO,WAAW,EAAE,KAAKJ,OAAO,CAACI,WAAW,EAAE,EACnD;YACAT,GAAG,CAACU,IAAI,CACN,CAAC,kDAAkD,EAAER,WAAW,CAAC,aAAa,EAAEG,OAAO,CAAC,gFAAgF,CAAC,CAC1K;SACF;QAED,OAAOA,OAAO,CAAA;KACf,CAAC,OAAOM,GAAG,EAAO;QACjB,IAAIA,GAAG,CAACC,IAAI,KAAK,QAAQ,EAAE;YACzB,IAAI,OAAOX,GAAG,KAAK,QAAQ,EAAE;gBAC3B,MAAMY,YAAY,GAAGC,CAAAA,GAAAA,WAAU,AAA4B,CAAA,WAA5B,CAACb,GAAG,EAAEc,MAAM,CAACC,IAAI,CAACC,SAAQ,SAAA,CAAC,CAAC;gBAE3D,IAAIJ,YAAY,EAAE;oBAChBb,GAAG,CAACkB,KAAK,CACP,CAAC,MAAM,EAAEjB,GAAG,CAAC,qCAAqC,EAAEY,YAAY,CAAC,EAAE,CAAC,CACrE;oBACDM,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;iBAChB;aACF;YAEDpB,GAAG,CAACkB,KAAK,CACP,CAAC,uDAAuD,EAAEf,KAAI,QAAA,CAACC,OAAO,CACpEH,GAAG,IAAI,GAAG,CACX,CAAC,CAAC,CACJ;YACDkB,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;SAChB;QACD,MAAMT,GAAG,CAAA;KACV;CACF"}