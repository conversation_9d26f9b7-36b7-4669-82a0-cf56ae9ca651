{"version": 3, "sources": ["../../lib/find-pages-dir.ts"], "names": ["findPagesDir", "existsSync", "f", "fs", "accessSync", "constants", "F_OK", "_", "findDir", "dir", "name", "curDir", "path", "join", "appDirEnabled", "pagesDir", "undefined", "appDir", "Error", "pages"], "mappings": "AAAA;;;;QAuBgBA,YAAY,GAAZA,YAAY;;AAvBb,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;;;;;;AAEhB,MAAMC,UAAU,GAAG,CAACC,CAAS,GAAc;IAChD,IAAI;QACFC,GAAE,QAAA,CAACC,UAAU,CAACF,CAAC,EAAEC,GAAE,QAAA,CAACE,SAAS,CAACC,IAAI,CAAC;QACnC,OAAO,IAAI,CAAA;KACZ,CAAC,OAAOC,CAAC,EAAE;QACV,OAAO,KAAK,CAAA;KACb;CACF;QAPYN,UAAU,GAAVA,UAAU;AASvB,SAASO,OAAO,CAACC,GAAW,EAAEC,IAAqB,EAAiB;IAClE,0CAA0C;IAC1C,IAAIC,MAAM,GAAGC,KAAI,QAAA,CAACC,IAAI,CAACJ,GAAG,EAAEC,IAAI,CAAC;IACjC,IAAIT,UAAU,CAACU,MAAM,CAAC,EAAE,OAAOA,MAAM,CAAA;IAErCA,MAAM,GAAGC,KAAI,QAAA,CAACC,IAAI,CAACJ,GAAG,EAAE,KAAK,EAAEC,IAAI,CAAC;IACpC,IAAIT,UAAU,CAACU,MAAM,CAAC,EAAE,OAAOA,MAAM,CAAA;IAErC,OAAO,IAAI,CAAA;CACZ;AAEM,SAASX,YAAY,CAC1BS,GAAW,EACXK,aAAuB,EACoC;IAC3D,MAAMC,QAAQ,GAAGP,OAAO,CAACC,GAAG,EAAE,OAAO,CAAC,IAAIO,SAAS;IACnD,IAAIC,MAAM,AAAoB;IAE9B,IAAIH,aAAa,EAAE;QACjBG,MAAM,GAAGT,OAAO,CAACC,GAAG,EAAE,KAAK,CAAC,IAAIO,SAAS;QACzC,IAAIF,aAAa,IAAI,IAAI,IAAIC,QAAQ,IAAI,IAAI,EAAE;YAC7C,MAAM,IAAIG,KAAK,CACb,0FAA0F,CAC3F,CAAA;SACF;KACF;IAED,IAAI,CAACJ,aAAa,EAAE;QAClB,IAAIC,QAAQ,IAAI,IAAI,EAAE;YACpB,MAAM,IAAIG,KAAK,CACb,+EAA+E,CAChF,CAAA;SACF;KACF;IAED,OAAO;QACLC,KAAK,EAAEJ,QAAQ;QACfE,MAAM;KACP,CAAA;CACF"}