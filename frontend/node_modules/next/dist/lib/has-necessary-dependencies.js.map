{"version": 3, "sources": ["../../lib/has-necessary-dependencies.ts"], "names": ["hasNecessaryDependencies", "baseDir", "requiredPackages", "resolutions", "Map", "missingPackages", "Promise", "all", "map", "p", "pkgPath", "fs", "realpath", "resolveFrom", "pkg", "pkgDir", "dirname", "exportsRestrict", "fileNameToVerify", "relative", "file", "fileToVerify", "join", "fileExists", "set", "push", "_", "resolved", "missing"], "mappings": "AAAA;;;;QAgBsBA,wBAAwB,GAAxBA,wBAAwB;AAhBf,IAAA,GAAI,WAAJ,IAAI,CAAA;AACR,IAAA,WAAe,WAAf,eAAe,CAAA;AACd,IAAA,YAAgB,WAAhB,gBAAgB,CAAA;AACJ,IAAA,KAAM,WAAN,MAAM,CAAA;AAavC,eAAeA,wBAAwB,CAC5CC,OAAe,EACfC,gBAAqC,EACL;IAChC,IAAIC,WAAW,GAAG,IAAIC,GAAG,EAAkB;IAC3C,MAAMC,eAAe,GAAwB,EAAE;IAE/C,MAAMC,OAAO,CAACC,GAAG,CACfL,gBAAgB,CAACM,GAAG,CAAC,OAAOC,CAAC,GAAK;QAChC,IAAI;YACF,MAAMC,OAAO,GAAG,MAAMC,GAAE,SAAA,CAACC,QAAQ,CAC/BC,CAAAA,GAAAA,YAAW,AAAkC,CAAA,YAAlC,CAACZ,OAAO,EAAE,CAAC,EAAEQ,CAAC,CAACK,GAAG,CAAC,aAAa,CAAC,CAAC,CAC9C;YACD,MAAMC,MAAM,GAAGC,CAAAA,GAAAA,KAAO,AAAS,CAAA,QAAT,CAACN,OAAO,CAAC;YAE/B,IAAID,CAAC,CAACQ,eAAe,EAAE;gBACrB,MAAMC,gBAAgB,GAAGC,CAAAA,GAAAA,KAAQ,AAAe,CAAA,SAAf,CAACV,CAAC,CAACK,GAAG,EAAEL,CAAC,CAACW,IAAI,CAAC;gBAChD,IAAIF,gBAAgB,EAAE;oBACpB,MAAMG,YAAY,GAAGC,CAAAA,GAAAA,KAAI,AAA0B,CAAA,KAA1B,CAACP,MAAM,EAAEG,gBAAgB,CAAC;oBACnD,IAAI,MAAMK,CAAAA,GAAAA,WAAU,AAAc,CAAA,WAAd,CAACF,YAAY,CAAC,EAAE;wBAClClB,WAAW,CAACqB,GAAG,CAACf,CAAC,CAACK,GAAG,EAAEO,YAAY,CAAC;qBACrC,MAAM;wBACL,OAAOhB,eAAe,CAACoB,IAAI,CAAChB,CAAC,CAAC,CAAA;qBAC/B;iBACF,MAAM;oBACLN,WAAW,CAACqB,GAAG,CAACf,CAAC,CAACK,GAAG,EAAEJ,OAAO,CAAC;iBAChC;aACF,MAAM;gBACLP,WAAW,CAACqB,GAAG,CAACf,CAAC,CAACK,GAAG,EAAED,CAAAA,GAAAA,YAAW,AAAiB,CAAA,YAAjB,CAACZ,OAAO,EAAEQ,CAAC,CAACW,IAAI,CAAC,CAAC;aACrD;SACF,CAAC,OAAOM,CAAC,EAAE;YACV,OAAOrB,eAAe,CAACoB,IAAI,CAAChB,CAAC,CAAC,CAAA;SAC/B;KACF,CAAC,CACH;IAED,OAAO;QACLkB,QAAQ,EAAExB,WAAW;QACrByB,OAAO,EAAEvB,eAAe;KACzB,CAAA;CACF"}