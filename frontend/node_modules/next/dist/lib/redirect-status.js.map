{"version": 3, "sources": ["../../lib/redirect-status.ts"], "names": ["getRedirectStatus", "modifyRouteRegex", "allowedStatusCodes", "Set", "route", "statusCode", "permanent", "PERMANENT_REDIRECT_STATUS", "TEMPORARY_REDIRECT_STATUS", "regex", "restrictedPaths", "replace", "map", "path", "join"], "mappings": "AAAA;;;;QAKgBA,iBAAiB,GAAjBA,iBAAiB;QAajBC,gBAAgB,GAAhBA,gBAAgB;;AAlBU,IAAA,UAAyB,WAAzB,yBAAyB,CAAA;AAG5D,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAC;AAAC,OAAG;AAAE,OAAG;AAAE,OAAG;AAAE,OAAG;AAAE,OAAG;CAAC,CAAC;QAAvDD,kBAAkB,GAAlBA,kBAAkB;AAExB,SAASF,iBAAiB,CAACI,KAGjC,EAAU;IACT,OACEA,KAAK,CAACC,UAAU,IAChB,CAACD,KAAK,CAACE,SAAS,GAAGC,UAAyB,0BAAA,GAAGC,UAAyB,0BAAA,CAAC,CAC1E;CACF;AAKM,SAASP,gBAAgB,CAACQ,KAAa,EAAEC,eAA0B,EAAE;IAC1E,IAAIA,eAAe,EAAE;QACnBD,KAAK,GAAGA,KAAK,CAACE,OAAO,OAEnB,CAAC,IAAI,EAAED,eAAe,CACnBE,GAAG,CAAC,CAACC,IAAI,GAAKA,IAAI,CAACF,OAAO,QAAQ,KAAK,CAAC,CAAC,CACzCG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAChB;KACF;IACDL,KAAK,GAAGA,KAAK,CAACE,OAAO,QAAQ,WAAW,CAAC;IACzC,OAAOF,KAAK,CAAA;CACb"}