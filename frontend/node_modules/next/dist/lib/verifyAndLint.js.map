{"version": 3, "sources": ["../../lib/verifyAndLint.ts"], "names": ["verifyAndLint", "dir", "cacheLocation", "configLintDirs", "numWorkers", "enableWorkerThreads", "telemetry", "hasAppDir", "lintWorkers", "Worker", "require", "resolve", "maxRetries", "getStdout", "pipe", "process", "stdout", "getStderr", "stderr", "lintDirs", "ESLINT_DEFAULT_DIRS", "reduce", "res", "d", "currDir", "join", "existsSync", "push", "lintResults", "runLintCheck", "lintDuringBuild", "eslintOptions", "lintOutput", "output", "eventInfo", "record", "eventLintCheckCompleted", "buildLint", "isError", "flush", "CompileError", "console", "log", "end", "err", "type", "error", "chalk", "red", "message", "exit"], "mappings": "AAAA;;;;QAUsBA,aAAa,GAAbA,aAAa;AAVjB,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AACrB,IAAA,WAAgC,WAAhC,gCAAgC,CAAA;AAC5B,IAAA,GAAI,WAAJ,IAAI,CAAA;AACV,IAAA,KAAM,WAAN,MAAM,CAAA;AACS,IAAA,UAAa,WAAb,aAAa,CAAA;AAET,IAAA,OAAqB,WAArB,qBAAqB,CAAA;AAChC,IAAA,aAAiB,WAAjB,iBAAiB,CAAA;AAC1B,IAAA,QAAY,kCAAZ,YAAY,EAAA;;;;;;AAEzB,eAAeA,aAAa,CACjCC,GAAW,EACXC,aAAqB,EACrBC,cAAoC,EACpCC,UAA8B,EAC9BC,mBAAwC,EACxCC,SAAoB,EACpBC,SAAkB,EACH;IACf,IAAI;QACF,MAAMC,WAAW,GAAG,IAAIC,WAAM,OAAA,CAACC,OAAO,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;YACvEP,UAAU;YACVC,mBAAmB;YACnBO,UAAU,EAAE,CAAC;SACd,CAAC,AAED;QAEDJ,WAAW,CAACK,SAAS,EAAE,CAACC,IAAI,CAACC,OAAO,CAACC,MAAM,CAAC;QAC5CR,WAAW,CAACS,SAAS,EAAE,CAACH,IAAI,CAACC,OAAO,CAACG,MAAM,CAAC;QAE5C,MAAMC,QAAQ,GAAG,CAAChB,cAAc,WAAdA,cAAc,GAAIiB,UAAmB,oBAAA,CAAC,CAACC,MAAM,CAC7D,CAACC,GAAa,EAAEC,CAAS,GAAK;YAC5B,MAAMC,OAAO,GAAGC,CAAAA,GAAAA,KAAI,AAAQ,CAAA,KAAR,CAACxB,GAAG,EAAEsB,CAAC,CAAC;YAC5B,IAAI,CAACG,CAAAA,GAAAA,GAAU,AAAS,CAAA,WAAT,CAACF,OAAO,CAAC,EAAE,OAAOF,GAAG,CAAA;YACpCA,GAAG,CAACK,IAAI,CAACH,OAAO,CAAC;YACjB,OAAOF,GAAG,CAAA;SACX,EACD,EAAE,CACH;QAED,MAAMM,WAAW,GAAG,MAAMpB,WAAW,CAACqB,YAAY,CAAC5B,GAAG,EAAEkB,QAAQ,EAAE;YAChEZ,SAAS;YACTuB,eAAe,EAAE,IAAI;YACrBC,aAAa,EAAE;gBACb7B,aAAa;aACd;SACF,CAAC;QACF,MAAM8B,UAAU,GACd,OAAOJ,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGA,WAAW,QAAQ,GAAnBA,KAAAA,CAAmB,GAAnBA,WAAW,CAAEK,MAAM;QAErE,IAAI,OAAOL,WAAW,KAAK,QAAQ,IAAIA,CAAAA,WAAW,QAAW,GAAtBA,KAAAA,CAAsB,GAAtBA,WAAW,CAAEM,SAAS,CAAA,EAAE;YAC7D5B,SAAS,CAAC6B,MAAM,CACdC,CAAAA,GAAAA,OAAuB,AAGrB,CAAA,wBAHqB,CAAC;gBACtB,GAAGR,WAAW,CAACM,SAAS;gBACxBG,SAAS,EAAE,IAAI;aAChB,CAAC,CACH;SACF;QAED,IAAI,OAAOT,WAAW,KAAK,QAAQ,IAAIA,CAAAA,WAAW,QAAS,GAApBA,KAAAA,CAAoB,GAApBA,WAAW,CAAEU,OAAO,CAAA,IAAIN,UAAU,EAAE;YACzE,MAAM1B,SAAS,CAACiC,KAAK,EAAE;YACvB,MAAM,IAAIC,aAAY,aAAA,CAACR,UAAU,CAAC,CAAA;SACnC;QAED,IAAIA,UAAU,EAAE;YACdS,OAAO,CAACC,GAAG,CAACV,UAAU,CAAC;SACxB;QAEDxB,WAAW,CAACmC,GAAG,EAAE;KAClB,CAAC,OAAOC,GAAG,EAAE;QACZ,IAAIN,CAAAA,GAAAA,QAAO,AAAK,CAAA,QAAL,CAACM,GAAG,CAAC,EAAE;YAChB,IAAIA,GAAG,CAACC,IAAI,KAAK,cAAc,IAAID,GAAG,YAAYJ,aAAY,aAAA,EAAE;gBAC9DC,OAAO,CAACK,KAAK,CAACC,MAAK,QAAA,CAACC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBAChDP,OAAO,CAACK,KAAK,CAACF,GAAG,CAACK,OAAO,CAAC;gBAC1BlC,OAAO,CAACmC,IAAI,CAAC,CAAC,CAAC;aAChB,MAAM,IAAIN,GAAG,CAACC,IAAI,KAAK,YAAY,EAAE;gBACpCJ,OAAO,CAACK,KAAK,CAACF,GAAG,CAACK,OAAO,CAAC;gBAC1BlC,OAAO,CAACmC,IAAI,CAAC,CAAC,CAAC;aAChB;SACF;QACD,MAAMN,GAAG,CAAA;KACV;CACF"}