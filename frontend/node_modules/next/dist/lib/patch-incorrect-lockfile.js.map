{"version": 3, "sources": ["../../lib/patch-incorrect-lockfile.ts"], "names": ["patchIncorrectLockfile", "Log", "registry", "fetchPkgInfo", "pkg", "output", "execSync", "toString", "trim", "startsWith", "endsWith", "_", "res", "fetch", "ok", "Error", "status", "data", "json", "versionData", "versions", "nextPkgJson", "version", "os", "cpu", "engines", "tarball", "dist", "integrity", "dir", "process", "env", "NEXT_IGNORE_INCORRECT_LOCKFILE", "lockfilePath", "findUp", "cwd", "content", "promises", "readFile", "endingNewline", "lockfileParsed", "JSON", "parse", "lockfileVersion", "parseInt", "expectedSwcPkgs", "Object", "keys", "optionalDependencies", "patchDependency", "pkgData", "dependencies", "resolved", "optional", "patchPackage", "packages", "supportedVersions", "includes", "shouldPatchDependencies", "shouldPatchPackages", "missingSwcPkgs", "pkgPrefix", "substring", "length", "push", "warn", "isCI", "pkgsData", "Promise", "all", "map", "i", "writeFile", "stringify", "err", "error", "console"], "mappings": "AAAA;;;;QAoDsBA,sBAAsB,GAAtBA,sBAAsB;AApDnB,IAAA,GAAI,WAAJ,IAAI,CAAA;QACtB,+BAA+B;AAC1BC,IAAAA,GAAG,mCAAM,qBAAqB,EAA3B;AACI,IAAA,OAA4B,kCAA5B,4BAA4B,EAAA;AACtB,IAAA,aAAe,WAAf,eAAe,CAAA;AAEhB,IAAA,YAAmB,kCAAnB,mBAAmB,EAAA;AAEtB,IAAA,OAAsB,WAAtB,sBAAsB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3C,IAAIC,QAAQ,AAAoB;AAEhC,eAAeC,YAAY,CAACC,GAAW,EAAE;IACvC,IAAI,CAACF,QAAQ,EAAE;QACb,IAAI;YACF,MAAMG,MAAM,GAAGC,CAAAA,GAAAA,aAAQ,AAA2B,CAAA,SAA3B,CAAC,yBAAyB,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE;YACpE,IAAIH,MAAM,CAACI,UAAU,CAAC,MAAM,CAAC,EAAE;gBAC7BP,QAAQ,GAAGG,MAAM;gBAEjB,IAAI,CAACH,QAAQ,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC3BR,QAAQ,IAAI,GAAG;iBAChB;aACF;SACF,CAAC,OAAOS,CAAC,EAAE;YACVT,QAAQ,GAAG,CAAC,2BAA2B,CAAC;SACzC;KACF;IACD,MAAMU,GAAG,GAAG,MAAMC,KAAK,CAAC,CAAC,EAAEX,QAAQ,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC;IAE5C,IAAI,CAACQ,GAAG,CAACE,EAAE,EAAE;QACX,MAAM,IAAIC,KAAK,CACb,CAAC,kCAAkC,EAAEX,GAAG,CAAC,aAAa,EAAEQ,GAAG,CAACI,MAAM,CAAC,CAAC,CACrE,CAAA;KACF;IACD,MAAMC,IAAI,GAAG,MAAML,GAAG,CAACM,IAAI,EAAE;IAC7B,MAAMC,WAAW,GAAGF,IAAI,CAACG,QAAQ,CAACC,YAAW,QAAA,CAACC,OAAO,CAAC;IAEtD,OAAO;QACLC,EAAE,EAAEJ,WAAW,CAACI,EAAE;QAClBC,GAAG,EAAEL,WAAW,CAACK,GAAG;QACpBC,OAAO,EAAEN,WAAW,CAACM,OAAO;QAC5BC,OAAO,EAAEP,WAAW,CAACQ,IAAI,CAACD,OAAO;QACjCE,SAAS,EAAET,WAAW,CAACQ,IAAI,CAACC,SAAS;KACtC,CAAA;CACF;AAQM,eAAe5B,sBAAsB,CAAC6B,GAAW,EAAE;IACxD,IAAIC,OAAO,CAACC,GAAG,CAACC,8BAA8B,EAAE;QAC9C,OAAM;KACP;IACD,MAAMC,YAAY,GAAG,MAAMC,CAAAA,GAAAA,OAAM,AAAmC,CAAA,QAAnC,CAAC,mBAAmB,EAAE;QAAEC,GAAG,EAAEN,GAAG;KAAE,CAAC;IAEpE,IAAI,CAACI,YAAY,EAAE;QACjB,oDAAoD;QACpD,OAAM;KACP;IACD,MAAMG,OAAO,GAAG,MAAMC,GAAQ,SAAA,CAACC,QAAQ,CAACL,YAAY,EAAE,MAAM,CAAC;IAC7D,+BAA+B;IAC/B,MAAMM,aAAa,GAAGH,OAAO,CAAC1B,QAAQ,CAAC,MAAM,CAAC,GAC1C,MAAM,GACN0B,OAAO,CAAC1B,QAAQ,CAAC,IAAI,CAAC,GACtB,IAAI,GACJ,EAAE;IAEN,MAAM8B,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACN,OAAO,CAAC;IAC1C,MAAMO,eAAe,GAAGC,QAAQ,CAACJ,cAAc,QAAiB,GAA/BA,KAAAA,CAA+B,GAA/BA,cAAc,CAAEG,eAAe,EAAE,EAAE,CAAC;IACrE,MAAME,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC1B,YAAW,QAAA,CAAC2B,oBAAoB,IAAI,EAAE,CAAC;IAE3E,MAAMC,eAAe,GAAG,CACtB7C,GAAW,EACX8C,OAAuD,GACpD;QACHV,cAAc,CAACW,YAAY,CAAC/C,GAAG,CAAC,GAAG;YACjCkB,OAAO,EAAED,YAAW,QAAA,CAACC,OAAO;YAC5B8B,QAAQ,EAAEF,OAAO,CAACxB,OAAO;YACzBE,SAAS,EAAEsB,OAAO,CAACtB,SAAS;YAC5ByB,QAAQ,EAAE,IAAI;SACf;KACF;IAED,MAAMC,YAAY,GAAG,CACnBlD,GAAW,EACX8C,OAAuD,GACpD;QACHV,cAAc,CAACe,QAAQ,CAACnD,GAAG,CAAC,GAAG;YAC7BkB,OAAO,EAAED,YAAW,QAAA,CAACC,OAAO;YAC5B8B,QAAQ,EAAEF,OAAO,CAACxB,OAAO;YACzBE,SAAS,EAAEsB,OAAO,CAACtB,SAAS;YAC5BJ,GAAG,EAAE0B,OAAO,CAAC1B,GAAG;YAChB6B,QAAQ,EAAE,IAAI;YACd9B,EAAE,EAAE2B,OAAO,CAAC3B,EAAE;YACdE,OAAO,EAAEyB,OAAO,CAACzB,OAAO;SACzB;KACF;IAED,IAAI;QACF,MAAM+B,iBAAiB,GAAG;AAAC,aAAC;AAAE,aAAC;AAAE,aAAC;SAAC;QAEnC,IAAI,CAACA,iBAAiB,CAACC,QAAQ,CAACd,eAAe,CAAC,EAAE;YAChD,8BAA8B;YAC9B,OAAM;SACP;QACD,4BAA4B;QAC5B,oCAAoC;QACpC,wBAAwB;QACxB,MAAMe,uBAAuB,GAC3Bf,eAAe,KAAK,CAAC,IAAIA,eAAe,KAAK,CAAC;QAChD,MAAMgB,mBAAmB,GAAGhB,eAAe,KAAK,CAAC,IAAIA,eAAe,KAAK,CAAC;QAE1E,IACE,AAACe,uBAAuB,IAAI,CAAClB,cAAc,CAACW,YAAY,IACvDQ,mBAAmB,IAAI,CAACnB,cAAc,CAACe,QAAQ,AAAC,EACjD;YACA,2BAA2B;YAC3B,OAAM;SACP;QACD,MAAMK,cAAc,GAAG,EAAE;QACzB,IAAIC,SAAS,AAAoB;QAEjC,IAAIF,mBAAmB,EAAE;YACvBE,SAAS,GAAG,EAAE;YACd,KAAK,MAAMzD,GAAG,IAAI0C,MAAM,CAACC,IAAI,CAACP,cAAc,CAACe,QAAQ,CAAC,CAAE;gBACtD,IAAInD,GAAG,CAACM,QAAQ,CAAC,mBAAmB,CAAC,EAAE;oBACrCmD,SAAS,GAAGzD,GAAG,CAAC0D,SAAS,CAAC,CAAC,EAAE1D,GAAG,CAAC2D,MAAM,GAAG,CAAC,CAAC;iBAC7C;aACF;YAED,IAAI,CAACF,SAAS,EAAE;gBACd,4CAA4C;gBAC5C,OAAM;aACP;SACF;QAED,KAAK,MAAMzD,IAAG,IAAIyC,eAAe,CAAE;YACjC,IACE,AAACa,uBAAuB,IAAI,CAAClB,cAAc,CAACW,YAAY,CAAC/C,IAAG,CAAC,IAC5DuD,mBAAmB,IAAI,CAACnB,cAAc,CAACe,QAAQ,CAAC,CAAC,EAAEM,SAAS,CAAC,EAAEzD,IAAG,CAAC,CAAC,CAAC,AAAC,EACvE;gBACAwD,cAAc,CAACI,IAAI,CAAC5D,IAAG,CAAC;aACzB;SACF;QACD,IAAIwD,cAAc,CAACG,MAAM,KAAK,CAAC,EAAE;YAC/B,OAAM;SACP;QACD9D,GAAG,CAACgE,IAAI,CACN,CAAC,wCAAwC,CAAC,EAC1CC,OAAI,KAAA,GAAG,yCAAyC,GAAG,aAAa,CACjE;QAED,IAAIA,OAAI,KAAA,EAAE;YACR,8DAA8D;YAC9D,OAAM;SACP;QACD,MAAMC,QAAQ,GAAG,MAAMC,OAAO,CAACC,GAAG,CAChCT,cAAc,CAACU,GAAG,CAAC,CAAClE,GAAG,GAAKD,YAAY,CAACC,GAAG,CAAC,CAAC,CAC/C;QAED,IAAK,IAAImE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACJ,MAAM,EAAEQ,CAAC,EAAE,CAAE;YACxC,MAAMnE,GAAG,GAAGwD,cAAc,CAACW,CAAC,CAAC;YAC7B,MAAMrB,OAAO,GAAGiB,QAAQ,CAACI,CAAC,CAAC;YAE3B,IAAIb,uBAAuB,EAAE;gBAC3BT,eAAe,CAAC7C,GAAG,EAAE8C,OAAO,CAAC;aAC9B;YACD,IAAIS,mBAAmB,EAAE;gBACvBL,YAAY,CAAC,CAAC,EAAEO,SAAS,CAAC,EAAEzD,GAAG,CAAC,CAAC,EAAE8C,OAAO,CAAC;aAC5C;SACF;QAED,MAAMb,GAAQ,SAAA,CAACmC,SAAS,CACtBvC,YAAY,EACZQ,IAAI,CAACgC,SAAS,CAACjC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGD,aAAa,CACxD;QACDtC,GAAG,CAACgE,IAAI,CACN,6GAA6G,CAC9G;KACF,CAAC,OAAOS,GAAG,EAAE;QACZzE,GAAG,CAAC0E,KAAK,CACP,CAAC,yFAAyF,CAAC,CAC5F;QACDC,OAAO,CAACD,KAAK,CAACD,GAAG,CAAC;KACnB;CACF"}