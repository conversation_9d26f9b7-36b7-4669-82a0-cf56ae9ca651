{"version": 3, "sources": ["../../lib/resolve-from.ts"], "names": ["<PERSON><PERSON><PERSON>", "require", "resolveFrom", "fromDirectory", "moduleId", "silent", "TypeError", "fs", "realpathSync", "error", "isError", "code", "path", "resolve", "fromFile", "join", "resolveFileName", "_resolveFilename", "id", "filename", "paths", "_nodeModulePaths"], "mappings": "AACA;;;;;AAAe,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACH,IAAA,QAAY,kCAAZ,YAAY,EAAA;;;;;;AAEhC,MAAMA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAEzB,MAAMC,WAAW,GAAG,CACzBC,aAAqB,EACrBC,QAAgB,EAChBC,MAAgB,GACb;IACH,IAAI,OAAOF,aAAa,KAAK,QAAQ,EAAE;QACrC,MAAM,IAAIG,SAAS,CACjB,CAAC,qDAAqD,EAAE,OAAOH,aAAa,CAAC,EAAE,CAAC,CACjF,CAAA;KACF;IAED,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;QAChC,MAAM,IAAIE,SAAS,CACjB,CAAC,sDAAsD,EAAE,OAAOF,QAAQ,CAAC,EAAE,CAAC,CAC7E,CAAA;KACF;IAED,IAAI;QACFD,aAAa,GAAGI,GAAE,QAAA,CAACC,YAAY,CAACL,aAAa,CAAC;KAC/C,CAAC,OAAOM,KAAK,EAAW;QACvB,IAAIC,CAAAA,GAAAA,QAAO,AAAO,CAAA,QAAP,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACE,IAAI,KAAK,QAAQ,EAAE;YAC7CR,aAAa,GAAGS,KAAI,QAAA,CAACC,OAAO,CAACV,aAAa,CAAC;SAC5C,MAAM,IAAIE,MAAM,EAAE;YACjB,OAAM;SACP,MAAM;YACL,MAAMI,KAAK,CAAA;SACZ;KACF;IAED,MAAMK,QAAQ,GAAGF,KAAI,QAAA,CAACG,IAAI,CAACZ,aAAa,EAAE,SAAS,CAAC;IAEpD,MAAMa,eAAe,GAAG,IACtBhB,MAAM,CAACiB,gBAAgB,CAACb,QAAQ,EAAE;YAChCc,EAAE,EAAEJ,QAAQ;YACZK,QAAQ,EAAEL,QAAQ;YAClBM,KAAK,EAAEpB,MAAM,CAACqB,gBAAgB,CAAClB,aAAa,CAAC;SAC9C,CAAC;IAEJ,IAAIE,MAAM,EAAE;QACV,IAAI;YACF,OAAOW,eAAe,EAAE,CAAA;SACzB,CAAC,OAAOP,KAAK,EAAE;YACd,OAAM;SACP;KACF;IAED,OAAOO,eAAe,EAAE,CAAA;CACzB;QA/CYd,WAAW,GAAXA,WAAW"}