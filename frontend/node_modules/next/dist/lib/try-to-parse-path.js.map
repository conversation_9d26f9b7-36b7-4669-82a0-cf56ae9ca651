{"version": 3, "sources": ["../../lib/try-to-parse-path.ts"], "names": ["tryToParsePath", "reportError", "route", "parsed<PERSON><PERSON>", "err", "err<PERSON><PERSON><PERSON>", "isError", "message", "match", "position", "parseInt", "console", "error", "Array", "fill", "join", "options", "result", "handleUrl", "parsed", "parseURL", "pathname", "hash", "tokens", "parse", "regexStr", "tokensToRegexp", "source"], "mappings": "AAAA;;;;QA2CgBA,cAAc,GAAdA,cAAc;AA1CQ,IAAA,aAAmC,WAAnC,mCAAmC,CAAA;AACvC,IAAA,IAAK,WAAL,KAAK,CAAA;AACnB,IAAA,QAAY,kCAAZ,YAAY,EAAA;;;;;;AAUhC;;;GAGG,CACH,SAASC,WAAW,CAAC,EAAEC,KAAK,CAAA,EAAEC,UAAU,CAAA,EAAe,EAAEC,GAAQ,EAAE;IACjE,IAAIC,UAAU;IACd,IAAIC,CAAAA,GAAAA,QAAO,AAAK,CAAA,QAAL,CAACF,GAAG,CAAC,IAAI,CAACC,UAAU,GAAGD,GAAG,CAACG,OAAO,CAACC,KAAK,eAAe,CAAC,EAAE;QACnE,MAAMC,QAAQ,GAAGC,QAAQ,CAACL,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC5CM,OAAO,CAACC,KAAK,CACX,CAAC,kBAAkB,EAAEV,KAAK,CAAC,GAAG,CAAC,GAC7B,CAAC,uDAAuD,CAAC,GACzD,CAAC,QAAQ,EAAEE,GAAG,CAACG,OAAO,CAAC,IAAI,CAAC,GAC5B,CAAC,EAAE,EAAEJ,UAAU,CAAC,EAAE,CAAC,GACnB,CAAC,EAAE,EAAE,IAAIU,KAAK,CAACJ,QAAQ,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CACnD;KACF,MAAM;QACLJ,OAAO,CAACC,KAAK,CACX,CAAC,gBAAgB,EAAEV,KAAK,CAAC,sDAAsD,CAAC,EAChFE,GAAG,CACJ;KACF;CACF;AASM,SAASJ,cAAc,CAC5BE,KAAa,EACbc,OAEC,EACY;IACb,MAAMC,MAAM,GAAgB;QAAEf,KAAK;QAAEC,UAAU,EAAED,KAAK;KAAE;IACxD,IAAI;QACF,IAAIc,OAAO,QAAW,GAAlBA,KAAAA,CAAkB,GAAlBA,OAAO,CAAEE,SAAS,EAAE;YACtB,MAAMC,MAAM,GAAGC,CAAAA,GAAAA,IAAQ,AAAa,CAAA,MAAb,CAAClB,KAAK,EAAE,IAAI,CAAC;YACpCe,MAAM,CAACd,UAAU,GAAG,CAAC,EAAEgB,MAAM,CAACE,QAAQ,CAAE,EAAEF,MAAM,CAACG,IAAI,IAAI,EAAE,CAAC,CAAC;SAC9D;QAEDL,MAAM,CAACM,MAAM,GAAGC,CAAAA,GAAAA,aAAK,AAAmB,CAAA,MAAnB,CAACP,MAAM,CAACd,UAAU,CAAC;QACxCc,MAAM,CAACQ,QAAQ,GAAGC,CAAAA,GAAAA,aAAc,AAAe,CAAA,eAAf,CAACT,MAAM,CAACM,MAAM,CAAC,CAACI,MAAM;KACvD,CAAC,OAAOvB,GAAG,EAAE;QACZH,WAAW,CAACgB,MAAM,EAAEb,GAAG,CAAC;QACxBa,MAAM,CAACL,KAAK,GAAGR,GAAG;KACnB;IAED,OAAOa,MAAM,CAAA;CACd"}