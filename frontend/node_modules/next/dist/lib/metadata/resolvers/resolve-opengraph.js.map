{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-opengraph.ts"], "names": ["resolveImages", "resolveOpenGraph", "resolveTwitter", "Og<PERSON><PERSON><PERSON><PERSON>s", "article", "song", "playlist", "radio", "video", "basic", "resolveAndValidateImage", "item", "metadataBase", "isMetadataBaseMissing", "undefined", "isItemUrl", "isStringOrURL", "inputUrl", "url", "validateResolvedImageUrl", "resolveUrl", "images", "resolvedImages", "resolveAsArrayOrUndefined", "fallbackMetadataBase", "getSocialImageFallbackMetadataBase", "nonNullableImages", "resolvedItem", "push", "ogTypeToFields", "book", "getFieldsByOgType", "ogType", "concat", "isFullStringUrl", "warnOnce", "origin", "openGraph", "metadataContext", "titleTemplate", "resolveProps", "target", "og", "type", "keys", "k", "key", "value", "arrayValue", "resolved", "title", "resolveTitle", "resolveAbsoluteUrlWithPathname", "TwitterBasicInfoKeys", "twitter", "card", "infoKey", "length", "players", "app"], "mappings": ";;;;;;;;;;;;;;;;IAwEgBA,aAAa;eAAbA;;IA2DHC,gBAAgB;eAAhBA;;IAgDAC,cAAc;eAAdA;;;uBAxK6B;4BAMnC;8BACsB;qBACG;qBACP;AAKzB,MAAMC,eAAe;IACnBC,SAAS;QAAC;QAAW;KAAO;IAC5BC,MAAM;QAAC;QAAU;KAAY;IAC7BC,UAAU;QAAC;QAAU;KAAY;IACjCC,OAAO;QAAC;KAAW;IACnBC,OAAO;QAAC;QAAU;QAAa;QAAW;KAAO;IACjDC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,SAASC,wBACPC,IAA2D,EAC3DC,YAA+C,EAC/CC,qBAA8B;IAE9B,IAAI,CAACF,MAAM,OAAOG;IAClB,MAAMC,YAAYC,IAAAA,yBAAa,EAACL;IAChC,MAAMM,WAAWF,YAAYJ,OAAOA,KAAKO,GAAG;IAC5C,IAAI,CAACD,UAAU,OAAOH;IAEtBK,yBAAyBF,UAAUL,cAAcC;IAEjD,OAAOE,YACH;QACEG,KAAKE,IAAAA,sBAAU,EAACH,UAAUL;IAC5B,IACA;QACE,GAAGD,IAAI;QACP,8BAA8B;QAC9BO,KAAKE,IAAAA,sBAAU,EAACH,UAAUL;IAC5B;AACN;AAUO,SAASZ,cACdqB,MAA+C,EAC/CT,YAAkC;IAIlC,MAAMU,iBAAiBC,IAAAA,gCAAyB,EAACF;IACjD,IAAI,CAACC,gBAAgB,OAAOA;IAE5B,MAAM,EAAET,qBAAqB,EAAEW,oBAAoB,EAAE,GACnDC,IAAAA,8CAAkC,EAACb;IACrC,MAAMc,oBAAoB,EAAE;IAC5B,KAAK,MAAMf,QAAQW,eAAgB;QACjC,MAAMK,eAAejB,wBACnBC,MACAa,sBACAX;QAEF,IAAI,CAACc,cAAc;QAEnBD,kBAAkBE,IAAI,CAACD;IACzB;IAEA,OAAOD;AACT;AAEA,MAAMG,iBAAoD;IACxDzB,SAASD,aAAaC,OAAO;IAC7B0B,MAAM3B,aAAaC,OAAO;IAC1B,cAAcD,aAAaE,IAAI;IAC/B,eAAeF,aAAaE,IAAI;IAChC,kBAAkBF,aAAaG,QAAQ;IACvC,uBAAuBH,aAAaI,KAAK;IACzC,eAAeJ,aAAaK,KAAK;IACjC,iBAAiBL,aAAaK,KAAK;AACrC;AAEA,SAASuB,kBAAkBC,MAAiC;IAC1D,IAAI,CAACA,UAAU,CAAEA,CAAAA,UAAUH,cAAa,GAAI,OAAO1B,aAAaM,KAAK;IACrE,OAAOoB,cAAc,CAACG,OAAO,CAACC,MAAM,CAAC9B,aAAaM,KAAK;AACzD;AAEA,SAASU,yBACPF,QAAsB,EACtBO,oBAAuD,EACvDX,qBAA8B;IAE9B,yEAAyE;IACzE,IACE,OAAOI,aAAa,YACpB,CAACiB,IAAAA,oBAAe,EAACjB,aACjBJ,uBACA;QACAsB,IAAAA,aAAQ,EACN,CAAC,8GAA8G,EAAEX,qBAAqBY,MAAM,CAAC,yFAAyF,CAAC;IAE3O;AACF;AAEO,MAAMnC,mBAGT,CAACoC,WAAWzB,cAAc0B,iBAAiBC;IAC7C,IAAI,CAACF,WAAW,OAAO;IAEvB,SAASG,aAAaC,MAAyB,EAAEC,EAAa;QAC5D,MAAMV,SAASU,MAAM,UAAUA,KAAKA,GAAGC,IAAI,GAAG7B;QAC9C,MAAM8B,OAAOb,kBAAkBC;QAC/B,KAAK,MAAMa,KAAKD,KAAM;YACpB,MAAME,MAAMD;YACZ,IAAIC,OAAOJ,MAAMI,QAAQ,OAAO;gBAC9B,MAAMC,QAAQL,EAAE,CAACI,IAAI;gBACrB,IAAIC,OAAO;oBACT,MAAMC,aAAazB,IAAAA,gCAAyB,EAACwB;oBAE3CN,MAAc,CAACK,IAAI,GAAGE;gBAC1B;YACF;QACF;QACAP,OAAOpB,MAAM,GAAGrB,cAAc0C,GAAGrB,MAAM,EAAET;IAC3C;IAEA,MAAMqC,WAAW;QACf,GAAGZ,SAAS;QACZa,OAAOC,IAAAA,0BAAY,EAACd,UAAUa,KAAK,EAAEX;IACvC;IACAC,aAAaS,UAAUZ;IAEvBY,SAAS/B,GAAG,GAAGmB,UAAUnB,GAAG,GACxBkC,IAAAA,0CAA8B,EAC5Bf,UAAUnB,GAAG,EACbN,cACA0B,mBAEF;IAEJ,OAAOW;AACT;AAEA,MAAMI,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAMnD,iBAGT,CAACoD,SAAS1C,cAAc2B;QAaVU;IAZhB,IAAI,CAACK,SAAS,OAAO;IACrB,IAAIC,OAAO,UAAUD,UAAUA,QAAQC,IAAI,GAAGzC;IAC9C,MAAMmC,WAAW;QACf,GAAGK,OAAO;QACVJ,OAAOC,IAAAA,0BAAY,EAACG,QAAQJ,KAAK,EAAEX;IACrC;IACA,KAAK,MAAMiB,WAAWH,qBAAsB;QAC1CJ,QAAQ,CAACO,QAAQ,GAAGF,OAAO,CAACE,QAAQ,IAAI;IAC1C;IAEAP,SAAS5B,MAAM,GAAGrB,cAAcsD,QAAQjC,MAAM,EAAET;IAEhD2C,OAAOA,QAASN,CAAAA,EAAAA,mBAAAA,SAAS5B,MAAM,qBAAf4B,iBAAiBQ,MAAM,IAAG,wBAAwB,SAAQ;IAC1ER,SAASM,IAAI,GAAGA;IAEhB,IAAI,UAAUN,UAAU;QACtB,OAAQA,SAASM,IAAI;YACnB,KAAK;gBAAU;oBACbN,SAASS,OAAO,GAAGnC,IAAAA,gCAAyB,EAAC0B,SAASS,OAAO,KAAK,EAAE;oBACpE;gBACF;YACA,KAAK;gBAAO;oBACVT,SAASU,GAAG,GAAGV,SAASU,GAAG,IAAI,CAAC;oBAChC;gBACF;YACA;gBACE;QACJ;IACF;IAEA,OAAOV;AACT"}