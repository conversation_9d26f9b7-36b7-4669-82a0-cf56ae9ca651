{"version": 3, "sources": ["../../lib/get-package-version.ts"], "names": ["getPackageVersion", "path", "cachedDeps", "getDependencies", "cwd", "configurationPath", "findUp", "dependencies", "devDependencies", "content", "fs", "readFile", "packageJson", "JSON5", "parse", "name", "cwd2", "endsWith", "posix", "sep", "win32", "targetPath", "require", "resolve", "paths", "targetContent", "version"], "mappings": "AAAA;;;;QAqCsBA,iBAAiB,GAAjBA,iBAAiB;AArCR,IAAA,GAAI,WAAJ,IAAI,CAAA;AAChB,IAAA,OAA4B,kCAA5B,4BAA4B,EAAA;AAC7B,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAChCC,IAAAA,IAAI,mCAAM,MAAM,EAAZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhB,IAAIC,UAAU,AAAkC;AAEhD,SAASC,eAAe,CAAC,EACvBC,GAAG,CAAA,EAGJ,EAAoC;IACnC,IAAIF,UAAU,EAAE;QACd,OAAOA,UAAU,CAAA;KAClB;IAED,OAAQA,UAAU,GAAG,CAAC,UAAY;QAChC,MAAMG,iBAAiB,GAAuB,MAAMC,CAAAA,GAAAA,OAAM,AAExD,CAAA,QAFwD,CAAC,cAAc,EAAE;YACzEF,GAAG;SACJ,CAAC;QACF,IAAI,CAACC,iBAAiB,EAAE;YACtB,OAAO;gBAAEE,YAAY,EAAE,EAAE;gBAAEC,eAAe,EAAE,EAAE;aAAE,CAAA;SACjD;QAED,MAAMC,OAAO,GAAG,MAAMC,GAAE,SAAA,CAACC,QAAQ,CAACN,iBAAiB,EAAE,OAAO,CAAC;QAC7D,MAAMO,WAAW,GAAQC,MAAK,QAAA,CAACC,KAAK,CAACL,OAAO,CAAC;QAE7C,MAAM,EAAEF,YAAY,EAAG,EAAE,CAAA,EAAEC,eAAe,EAAG,EAAE,CAAA,EAAE,GAAGI,WAAW,IAAI,EAAE;QACrE,OAAO;YAAEL,YAAY;YAAEC,eAAe;SAAE,CAAA;KACzC,CAAC,EAAE,CAAC;CACN;AAEM,eAAeR,iBAAiB,CAAC,EACtCI,GAAG,CAAA,EACHW,IAAI,CAAA,EAIL,EAA0B;IACzB,MAAM,EAAER,YAAY,CAAA,EAAEC,eAAe,CAAA,EAAE,GAAG,MAAML,eAAe,CAAC;QAAEC,GAAG;KAAE,CAAC;IACxE,IAAI,CAAC,CAACG,YAAY,CAACQ,IAAI,CAAC,IAAIP,eAAe,CAACO,IAAI,CAAC,CAAC,EAAE;QAClD,OAAO,IAAI,CAAA;KACZ;IAED,MAAMC,IAAI,GACRZ,GAAG,CAACa,QAAQ,CAAChB,IAAI,CAACiB,KAAK,CAACC,GAAG,CAAC,IAAIf,GAAG,CAACa,QAAQ,CAAChB,IAAI,CAACmB,KAAK,CAACD,GAAG,CAAC,GACxDf,GAAG,GACH,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC;IAEf,IAAI;QACF,MAAMiB,UAAU,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC,EAAER,IAAI,CAAC,aAAa,CAAC,EAAE;YACzDS,KAAK,EAAE;gBAACR,IAAI;aAAC;SACd,CAAC;QACF,MAAMS,aAAa,GAAG,MAAMf,GAAE,SAAA,CAACC,QAAQ,CAACU,UAAU,EAAE,OAAO,CAAC;YACrDR,QAAkC;QAAzC,OAAOA,CAAAA,QAAkC,GAAlCA,MAAK,QAAA,CAACC,KAAK,CAACW,aAAa,CAAC,CAACC,OAAO,YAAlCb,QAAkC,GAAI,IAAI,CAAA;KAClD,CAAC,OAAM;QACN,OAAO,IAAI,CAAA;KACZ;CACF"}