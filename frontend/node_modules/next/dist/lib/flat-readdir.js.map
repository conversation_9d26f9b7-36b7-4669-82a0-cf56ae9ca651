{"version": 3, "sources": ["../../lib/flat-readdir.ts"], "names": ["flatReaddir", "dir", "include", "dirents", "promises", "readdir", "withFileTypes", "result", "Promise", "all", "map", "part", "absolutePath", "join", "name", "isSymbolicLink", "stats", "stat", "isDirectory", "test", "filter", "nonNullable"], "mappings": "AAAA;;;;QAIsBA,WAAW,GAAXA,WAAW;AAJZ,IAAA,KAAM,WAAN,MAAM,CAAA;AACC,IAAA,YAAgB,WAAhB,gBAAgB,CAAA;AACnB,IAAA,GAAI,WAAJ,IAAI,CAAA;AAEtB,eAAeA,WAAW,CAACC,GAAW,EAAEC,OAAe,EAAE;IAC9D,MAAMC,OAAO,GAAG,MAAMC,GAAQ,SAAA,CAACC,OAAO,CAACJ,GAAG,EAAE;QAAEK,aAAa,EAAE,IAAI;KAAE,CAAC;IACpE,MAAMC,MAAM,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC9BN,OAAO,CAACO,GAAG,CAAC,OAAOC,IAAI,GAAK;QAC1B,MAAMC,YAAY,GAAGC,CAAAA,GAAAA,KAAI,AAAgB,CAAA,KAAhB,CAACZ,GAAG,EAAEU,IAAI,CAACG,IAAI,CAAC;QACzC,IAAIH,IAAI,CAACI,cAAc,EAAE,EAAE;YACzB,MAAMC,KAAK,GAAG,MAAMZ,GAAQ,SAAA,CAACa,IAAI,CAACL,YAAY,CAAC;YAC/C,IAAII,KAAK,CAACE,WAAW,EAAE,EAAE;gBACvB,OAAO,IAAI,CAAA;aACZ;SACF;QAED,IAAIP,IAAI,CAACO,WAAW,EAAE,IAAI,CAAChB,OAAO,CAACiB,IAAI,CAACR,IAAI,CAACG,IAAI,CAAC,EAAE;YAClD,OAAO,IAAI,CAAA;SACZ;QAED,OAAOF,YAAY,CAAA;KACpB,CAAC,CACH;IAED,OAAOL,MAAM,CAACa,MAAM,CAACC,YAAW,YAAA,CAAC,CAAA;CAClC"}