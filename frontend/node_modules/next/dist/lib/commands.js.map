{"version": 3, "sources": ["../../lib/commands.ts"], "names": ["commands", "build", "Promise", "resolve", "require", "nextBuild", "start", "nextStart", "export", "nextExport", "dev", "nextDev", "lint", "nextLint", "telemetry", "nextTelemetry", "info", "nextInfo"], "mappings": "AAAA;;;;;AAEO,MAAMA,QAAQ,GAAqD;IACxEC,KAAK,EAAE,IAAMC,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAACC,SAAS,CAAC;IACpEC,KAAK,EAAE,IAAMJ,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAACG,SAAS,CAAC;IACpEC,MAAM,EAAE,IAAMN,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAACK,UAAU,CAAC;IACvEC,GAAG,EAAE,IAAMR,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAACO,OAAO,CAAC;IAC9DC,IAAI,EAAE,IAAMV,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAACS,QAAQ,CAAC;IACjEC,SAAS,EAAE,IACTZ,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAACW,aAAa,CAAC;IACjEC,IAAI,EAAE,IAAMd,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAACa,QAAQ,CAAC;CAClE;QATYjB,QAAQ,GAARA,QAAQ"}