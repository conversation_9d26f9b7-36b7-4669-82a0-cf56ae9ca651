{"version": 3, "sources": ["../../lib/recursive-copy.ts"], "names": ["recursiveCopy", "COPYFILE_EXCL", "constants", "source", "dest", "concurrency", "overwrite", "filter", "cwdPath", "process", "cwd", "from", "path", "resolve", "to", "sema", "<PERSON><PERSON>", "_copy", "item", "lstats", "target", "replace", "acquire", "promises", "lstat", "isFile", "isDirectory", "isSymbolicLink", "stats", "stat", "mkdir", "recursive", "err", "isError", "code", "release", "files", "readdir", "withFileTypes", "Promise", "all", "map", "file", "join", "name", "copyFile", "undefined"], "mappings": "AAAA;;;;QAOsBA,aAAa,GAAbA,aAAa;AAPlB,IAAA,KAAM,kCAAN,MAAM,EAAA;AAC4B,IAAA,GAAI,WAAJ,IAAI,CAAA;AAClC,IAAA,UAA+B,WAA/B,+BAA+B,CAAA;AAChC,IAAA,QAAY,kCAAZ,YAAY,EAAA;;;;;;AAEhC,MAAMC,aAAa,GAAGC,GAAS,UAAA,CAACD,aAAa;AAEtC,eAAeD,aAAa,CACjCG,MAAc,EACdC,IAAY,EACZ,EACEC,WAAW,EAAG,EAAE,CAAA,EAChBC,SAAS,EAAG,KAAK,CAAA,EACjBC,MAAM,EAAG,IAAM,IAAI,CAAA,EAKpB,GAAG,EAAE,EACS;IACf,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,EAAE;IAC7B,MAAMC,IAAI,GAAGC,KAAI,QAAA,CAACC,OAAO,CAACL,OAAO,EAAEL,MAAM,CAAC;IAC1C,MAAMW,EAAE,GAAGF,KAAI,QAAA,CAACC,OAAO,CAACL,OAAO,EAAEJ,IAAI,CAAC;IAEtC,MAAMW,IAAI,GAAG,IAAIC,UAAI,KAAA,CAACX,WAAW,CAAC;IAElC,+BAA+B;IAC/B,eAAeY,KAAK,CAACC,IAAY,EAAEC,MAAuB,EAAiB;QACzE,MAAMC,MAAM,GAAGF,IAAI,CAACG,OAAO,CAACV,IAAI,EAAEG,EAAE,CAAC;QAErC,MAAMC,IAAI,CAACO,OAAO,EAAE;QAEpB,IAAI,CAACH,MAAM,EAAE;YACX,0BAA0B;YAC1BA,MAAM,GAAG,MAAMI,GAAQ,SAAA,CAACC,KAAK,CAACb,IAAI,CAAC;SACpC;QAED,+CAA+C;QAC/C,kDAAkD;QAClD,IAAIc,MAAM,GAAGN,MAAM,CAACM,MAAM,EAAE;QAC5B,IAAIC,WAAW,GAAGP,MAAM,CAACO,WAAW,EAAE;QACtC,IAAIP,MAAM,CAACQ,cAAc,EAAE,EAAE;YAC3B,MAAMC,KAAK,GAAG,MAAML,GAAQ,SAAA,CAACM,IAAI,CAACX,IAAI,CAAC;YACvCO,MAAM,GAAGG,KAAK,CAACH,MAAM,EAAE;YACvBC,WAAW,GAAGE,KAAK,CAACF,WAAW,EAAE;SAClC;QAED,IAAIA,WAAW,EAAE;YACf,IAAI;gBACF,MAAMH,GAAQ,SAAA,CAACO,KAAK,CAACV,MAAM,EAAE;oBAAEW,SAAS,EAAE,IAAI;iBAAE,CAAC;aAClD,CAAC,OAAOC,GAAG,EAAE;gBACZ,8CAA8C;gBAC9C,IAAIC,CAAAA,GAAAA,QAAO,AAAK,CAAA,QAAL,CAACD,GAAG,CAAC,IAAIA,GAAG,CAACE,IAAI,KAAK,QAAQ,EAAE;oBACzC,MAAMF,GAAG,CAAA;iBACV;aACF;YACDjB,IAAI,CAACoB,OAAO,EAAE;YACd,MAAMC,KAAK,GAAG,MAAMb,GAAQ,SAAA,CAACc,OAAO,CAACnB,IAAI,EAAE;gBAAEoB,aAAa,EAAE,IAAI;aAAE,CAAC;YACnE,MAAMC,OAAO,CAACC,GAAG,CACfJ,KAAK,CAACK,GAAG,CAAC,CAACC,IAAI,GAAKzB,KAAK,CAACL,KAAI,QAAA,CAAC+B,IAAI,CAACzB,IAAI,EAAEwB,IAAI,CAACE,IAAI,CAAC,EAAEF,IAAI,CAAC,CAAC,CAC7D;SACF,MAAM,IACLjB,MAAM,IACN,oCAAoC;QACpC,8DAA8D;QAC9DlB,MAAM,CAACW,IAAI,CAACG,OAAO,CAACV,IAAI,EAAE,EAAE,CAAC,CAACU,OAAO,QAAQ,GAAG,CAAC,CAAC,EAClD;YACA,MAAME,GAAQ,SAAA,CAACsB,QAAQ,CACrB3B,IAAI,EACJE,MAAM,EACNd,SAAS,GAAGwC,SAAS,GAAG7C,aAAa,CACtC;YACDc,IAAI,CAACoB,OAAO,EAAE;SACf,MAAM;YACLpB,IAAI,CAACoB,OAAO,EAAE;SACf;KACF;IAED,MAAMlB,KAAK,CAACN,IAAI,CAAC;CAClB"}