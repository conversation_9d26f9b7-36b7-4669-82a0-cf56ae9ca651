{"version": 3, "sources": ["../../lib/coalesced-function.ts"], "names": ["withCoalescedInvoke", "globalInvokeCache", "Map", "func", "key", "args", "entry", "get", "then", "res", "is<PERSON><PERSON>in", "value", "__wrapper", "apply", "undefined", "future", "delete", "catch", "err", "Promise", "reject", "set"], "mappings": "AAAA;;;;QASgBA,mBAAmB,GAAnBA,mBAAmB;AAFnC,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,EAA6C;AAEvE,SAASF,mBAAmB,CACjCG,IAAO,EAImD;IAC1D,OAAO,eAAgBC,GAAW,EAAEC,IAAmB,EAAE;QACvD,MAAMC,KAAK,GAAGL,iBAAiB,CAACM,GAAG,CAACH,GAAG,CAAC;QACxC,IAAIE,KAAK,EAAE;YACT,OAAOA,KAAK,CAACE,IAAI,CAAC,CAACC,GAAG,GAAK,CAAC;oBAC1BC,QAAQ,EAAE,KAAK;oBACfC,KAAK,EAAEF,GAAG,CAACE,KAAK;iBACjB,CAAC,CAAC,CAAA;SACJ;QAED,eAAeC,SAAS,GAAG;YACzB,OAAO,MAAMT,IAAI,CAACU,KAAK,CAACC,SAAS,EAAET,IAAI,CAAC,CAAA;SACzC;QAED,MAAMU,MAAM,GAAGH,SAAS,EAAE,CACvBJ,IAAI,CAAC,CAACC,GAAG,GAAK;YACbR,iBAAiB,CAACe,MAAM,CAACZ,GAAG,CAAC;YAC7B,OAAO;gBAAEM,QAAQ,EAAE,IAAI;gBAAEC,KAAK,EAAEF,GAAG;aAAkC,CAAA;SACtE,CAAC,CACDQ,KAAK,CAAC,CAACC,GAAG,GAAK;YACdjB,iBAAiB,CAACe,MAAM,CAACZ,GAAG,CAAC;YAC7B,OAAOe,OAAO,CAACC,MAAM,CAACF,GAAG,CAAC,CAAA;SAC3B,CAAC;QACJjB,iBAAiB,CAACoB,GAAG,CAACjB,GAAG,EAAEW,MAAM,CAAC;QAClC,OAAOA,MAAM,CAAA;KACd,CAAA;CACF"}