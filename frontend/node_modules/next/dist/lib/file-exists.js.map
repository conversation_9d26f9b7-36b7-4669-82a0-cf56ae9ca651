{"version": 3, "sources": ["../../lib/file-exists.ts"], "names": ["fileExists", "fileName", "type", "stats", "promises", "stat", "isFile", "isDirectory", "access", "constants", "F_OK", "err", "isError", "code"], "mappings": "AAAA;;;;QAGsBA,UAAU,GAAVA,UAAU;AAHI,IAAA,GAAI,WAAJ,IAAI,CAAA;AACpB,IAAA,QAAY,kCAAZ,YAAY,EAAA;;;;;;AAEzB,eAAeA,UAAU,CAC9BC,QAAgB,EAChBC,IAA2B,EACT;IAClB,IAAI;QACF,IAAIA,IAAI,KAAK,MAAM,EAAE;YACnB,MAAMC,KAAK,GAAG,MAAMC,GAAQ,SAAA,CAACC,IAAI,CAACJ,QAAQ,CAAC;YAC3C,OAAOE,KAAK,CAACG,MAAM,EAAE,CAAA;SACtB,MAAM,IAAIJ,IAAI,KAAK,WAAW,EAAE;YAC/B,MAAMC,KAAK,GAAG,MAAMC,GAAQ,SAAA,CAACC,IAAI,CAACJ,QAAQ,CAAC;YAC3C,OAAOE,KAAK,CAACI,WAAW,EAAE,CAAA;SAC3B,MAAM;YACL,MAAMH,GAAQ,SAAA,CAACI,MAAM,CAACP,QAAQ,EAAEQ,GAAS,UAAA,CAACC,IAAI,CAAC;SAChD;QACD,OAAO,IAAI,CAAA;KACZ,CAAC,OAAOC,GAAG,EAAE;QACZ,IACEC,CAAAA,GAAAA,QAAO,AAAK,CAAA,QAAL,CAACD,GAAG,CAAC,IACZ,CAACA,GAAG,CAACE,IAAI,KAAK,QAAQ,IAAIF,GAAG,CAACE,IAAI,KAAK,cAAc,CAAC,EACtD;YACA,OAAO,KAAK,CAAA;SACb;QACD,MAAMF,GAAG,CAAA;KACV;CACF"}