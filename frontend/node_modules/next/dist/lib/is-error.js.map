{"version": 3, "sources": ["../../lib/is-error.ts"], "names": ["isError", "getProperError", "err", "process", "env", "NODE_ENV", "Error", "isPlainObject", "JSON", "stringify"], "mappings": "AAAA;;;;kBAUwBA,OAAO;QAMfC,cAAc,GAAdA,cAAc;AAhBA,IAAA,cAA+B,WAA/B,+BAA+B,CAAA;AAU9C,SAASD,OAAO,CAACE,GAAY,EAAoB;IAC9D,OACE,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAI,MAAM,IAAIA,GAAG,IAAI,SAAS,IAAIA,GAAG,CAC7E;CACF;AAEM,SAASD,cAAc,CAACC,GAAY,EAAS;IAClD,IAAIF,OAAO,CAACE,GAAG,CAAC,EAAE;QAChB,OAAOA,GAAG,CAAA;KACX;IAED,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1C,wDAAwD;QACxD,2BAA2B;QAC3B,IAAI,OAAOH,GAAG,KAAK,WAAW,EAAE;YAC9B,OAAO,IAAII,KAAK,CACd,iCAAiC,GAC/B,0EAA0E,CAC7E,CAAA;SACF;QAED,IAAIJ,GAAG,KAAK,IAAI,EAAE;YAChB,OAAO,IAAII,KAAK,CACd,2BAA2B,GACzB,0EAA0E,CAC7E,CAAA;SACF;KACF;IAED,OAAO,IAAIA,KAAK,CAACC,CAAAA,GAAAA,cAAa,AAAK,CAAA,cAAL,CAACL,GAAG,CAAC,GAAGM,IAAI,CAACC,SAAS,CAACP,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE,CAAC,CAAA;CACtE"}