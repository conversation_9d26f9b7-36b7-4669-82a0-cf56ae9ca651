{"version": 3, "sources": ["../../../lib/helpers/get-online.ts"], "names": ["getOnline", "getProxy", "process", "env", "https_proxy", "httpsProxy", "execSync", "toString", "trim", "undefined", "e", "Promise", "resolve", "dns", "lookup", "registryErr", "proxy", "hostname", "url", "parse", "proxyErr"], "mappings": "AAAA;;;;QAiBgBA,SAAS,GAATA,SAAS;AAjBA,IAAA,aAAe,WAAf,eAAe,CAAA;AACxB,IAAA,IAAK,kCAAL,KAAK,EAAA;AACL,IAAA,IAAK,kCAAL,KAAK,EAAA;;;;;;AAErB,SAASC,QAAQ,GAAuB;IACtC,IAAIC,OAAO,CAACC,GAAG,CAACC,WAAW,EAAE;QAC3B,OAAOF,OAAO,CAACC,GAAG,CAACC,WAAW,CAAA;KAC/B;IAED,IAAI;QACF,MAAMC,UAAU,GAAGC,CAAAA,GAAAA,aAAQ,AAA8B,CAAA,SAA9B,CAAC,4BAA4B,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE;QAC3E,OAAOH,UAAU,KAAK,MAAM,GAAGA,UAAU,GAAGI,SAAS,CAAA;KACtD,CAAC,OAAOC,CAAC,EAAE;QACV,OAAM;KACP;CACF;AAEM,SAASV,SAAS,GAAqB;IAC5C,OAAO,IAAIW,OAAO,CAAC,CAACC,OAAO,GAAK;QAC9BC,IAAG,QAAA,CAACC,MAAM,CAAC,sBAAsB,EAAE,CAACC,WAAW,GAAK;YAClD,IAAI,CAACA,WAAW,EAAE;gBAChB,OAAOH,OAAO,CAAC,IAAI,CAAC,CAAA;aACrB;YAED,MAAMI,KAAK,GAAGf,QAAQ,EAAE;YACxB,IAAI,CAACe,KAAK,EAAE;gBACV,OAAOJ,OAAO,CAAC,KAAK,CAAC,CAAA;aACtB;YAED,MAAM,EAAEK,QAAQ,CAAA,EAAE,GAAGC,IAAG,QAAA,CAACC,KAAK,CAACH,KAAK,CAAC;YACrC,IAAI,CAACC,QAAQ,EAAE;gBACb,OAAOL,OAAO,CAAC,KAAK,CAAC,CAAA;aACtB;YAEDC,IAAG,QAAA,CAACC,MAAM,CAACG,QAAQ,EAAE,CAACG,QAAQ,GAAK;gBACjCR,OAAO,CAACQ,QAAQ,IAAI,IAAI,CAAC;aAC1B,CAAC;SACH,CAAC;KACH,CAAC,CAAA;CACH"}