{"version": 3, "sources": ["../../../lib/helpers/get-pkg-manager.ts"], "names": ["getPkgManager", "baseDir", "lockFile", "packageManager", "fs", "existsSync", "path", "join", "userAgent", "process", "env", "npm_config_user_agent", "startsWith", "execSync", "stdio"], "mappings": "AAAA;;;;QAMgBA,aAAa,GAAbA,aAAa;AANd,IAAA,GAAI,kCAAJ,IAAI,EAAA;AACF,IAAA,KAAM,kCAAN,MAAM,EAAA;AACE,IAAA,aAAe,WAAf,eAAe,CAAA;;;;;;AAIjC,SAASA,aAAa,CAACC,OAAe,EAAkB;IAC7D,IAAI;QACF,KAAK,MAAM,EAAEC,QAAQ,CAAA,EAAEC,cAAc,CAAA,EAAE,IAAI;YACzC;gBAAED,QAAQ,EAAE,WAAW;gBAAEC,cAAc,EAAE,MAAM;aAAE;YACjD;gBAAED,QAAQ,EAAE,gBAAgB;gBAAEC,cAAc,EAAE,MAAM;aAAE;YACtD;gBAAED,QAAQ,EAAE,mBAAmB;gBAAEC,cAAc,EAAE,KAAK;aAAE;SACzD,CAAE;YACD,IAAIC,GAAE,QAAA,CAACC,UAAU,CAACC,KAAI,QAAA,CAACC,IAAI,CAACN,OAAO,EAAEC,QAAQ,CAAC,CAAC,EAAE;gBAC/C,OAAOC,cAAc,CAAkB;aACxC;SACF;QACD,MAAMK,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB;QACnD,IAAIH,SAAS,EAAE;YACb,IAAIA,SAAS,CAACI,UAAU,CAAC,MAAM,CAAC,EAAE;gBAChC,OAAO,MAAM,CAAA;aACd,MAAM,IAAIJ,SAAS,CAACI,UAAU,CAAC,MAAM,CAAC,EAAE;gBACvC,OAAO,MAAM,CAAA;aACd;SACF;QACD,IAAI;YACFC,CAAAA,GAAAA,aAAQ,AAAuC,CAAA,SAAvC,CAAC,gBAAgB,EAAE;gBAAEC,KAAK,EAAE,QAAQ;aAAE,CAAC;YAC/C,OAAO,MAAM,CAAA;SACd,CAAC,OAAM;YACND,CAAAA,GAAAA,aAAQ,AAAuC,CAAA,SAAvC,CAAC,gBAAgB,EAAE;gBAAEC,KAAK,EAAE,QAAQ;aAAE,CAAC;YAC/C,OAAO,MAAM,CAAA;SACd;KACF,CAAC,OAAM;QACN,OAAO,KAAK,CAAA;KACb;CACF"}