{"version": 3, "sources": ["../../../lib/helpers/install.ts"], "names": ["install", "root", "dependencies", "packageManager", "isOnline", "devDependencies", "npmFlags", "yarnFlags", "Promise", "resolve", "reject", "args", "command", "<PERSON><PERSON><PERSON><PERSON>", "length", "push", "console", "log", "chalk", "yellow", "child", "spawn", "stdio", "env", "process", "ADBLOCK", "NODE_ENV", "DISABLE_OPENCOLLECTIVE", "on", "code", "join"], "mappings": "AAAA;;;;QAyBgBA,OAAO,GAAPA,OAAO;AAzBL,IAAA,MAA0B,kCAA1B,0BAA0B,EAAA;AAC1B,IAAA,WAAgC,kCAAhC,gCAAgC,EAAA;;;;;;AAwB3C,SAASA,OAAO,CACrBC,IAAY,EACZC,YAA6B,EAC7B,EAAEC,cAAc,CAAA,EAAEC,QAAQ,CAAA,EAAEC,eAAe,CAAA,EAAe,EAC3C;IACf;;KAEG,CACH,MAAMC,QAAQ,GAAa,EAAE;IAC7B;;KAEG,CACH,MAAMC,SAAS,GAAa,EAAE;IAC9B;;KAEG,CACH,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,GAAK;QACtC,IAAIC,IAAI,AAAU;QAClB,IAAIC,OAAO,GAAGT,cAAc;QAC5B,MAAMU,OAAO,GAAGV,cAAc,KAAK,MAAM;QAEzC,IAAID,YAAY,IAAIA,YAAY,CAACY,MAAM,EAAE;YACvC;;SAEG,CACH,IAAID,OAAO,EAAE;gBACX;;WAEG,CACHF,IAAI,GAAG;oBAAC,KAAK;oBAAE,SAAS;iBAAC;gBACzB,IAAI,CAACP,QAAQ,EAAEO,IAAI,CAACI,IAAI,CAAC,WAAW,CAAC;gBACrCJ,IAAI,CAACI,IAAI,CAAC,OAAO,EAAEd,IAAI,CAAC;gBACxB,IAAII,eAAe,EAAEM,IAAI,CAACI,IAAI,CAAC,OAAO,CAAC;gBACvCJ,IAAI,CAACI,IAAI,IAAIb,YAAY,CAAC;aAC3B,MAAM;gBACL;;WAEG,CACHS,IAAI,GAAG;oBAAC,SAAS;oBAAE,cAAc;iBAAC;gBAClCA,IAAI,CAACI,IAAI,CAACV,eAAe,GAAG,YAAY,GAAG,QAAQ,CAAC;gBACpDM,IAAI,CAACI,IAAI,IAAIb,YAAY,CAAC;aAC3B;SACF,MAAM;YACL;;;SAGG,CACHS,IAAI,GAAG;gBAAC,SAAS;aAAC;YAClB,IAAI,CAACP,QAAQ,EAAE;gBACbY,OAAO,CAACC,GAAG,CAACC,MAAK,QAAA,CAACC,MAAM,CAAC,2BAA2B,CAAC,CAAC;gBACtD,IAAIN,OAAO,EAAE;oBACXG,OAAO,CAACC,GAAG,CAACC,MAAK,QAAA,CAACC,MAAM,CAAC,uCAAuC,CAAC,CAAC;oBAClEH,OAAO,CAACC,GAAG,EAAE;oBACbN,IAAI,CAACI,IAAI,CAAC,WAAW,CAAC;iBACvB,MAAM;oBACLC,OAAO,CAACC,GAAG,EAAE;iBACd;aACF;SACF;QACD;;OAEG,CACH,IAAIJ,OAAO,EAAE;YACXF,IAAI,CAACI,IAAI,IAAIR,SAAS,CAAC;SACxB,MAAM;YACLI,IAAI,CAACI,IAAI,IAAIT,QAAQ,CAAC;SACvB;QACD;;OAEG,CACH,MAAMc,KAAK,GAAGC,CAAAA,GAAAA,WAAK,AAUjB,CAAA,QAViB,CAACT,OAAO,EAAED,IAAI,EAAE;YACjCW,KAAK,EAAE,SAAS;YAChBC,GAAG,EAAE;gBACH,GAAGC,OAAO,CAACD,GAAG;gBACdE,OAAO,EAAE,GAAG;gBACZ,mDAAmD;gBACnD,+BAA+B;gBAC/BC,QAAQ,EAAE,aAAa;gBACvBC,sBAAsB,EAAE,GAAG;aAC5B;SACF,CAAC;QACFP,KAAK,CAACQ,EAAE,CAAC,OAAO,EAAE,CAACC,IAAI,GAAK;YAC1B,IAAIA,IAAI,KAAK,CAAC,EAAE;gBACdnB,MAAM,CAAC;oBAAEE,OAAO,EAAE,CAAC,EAAEA,OAAO,CAAC,CAAC,EAAED,IAAI,CAACmB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;iBAAE,CAAC;gBACnD,OAAM;aACP;YACDrB,OAAO,EAAE;SACV,CAAC;KACH,CAAC,CAAA;CACH"}