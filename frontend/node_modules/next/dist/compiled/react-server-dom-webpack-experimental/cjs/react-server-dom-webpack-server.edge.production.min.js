/*
 React
 react-server-dom-webpack-server.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react"),ba=require("react-dom"),n=null,p=0;function ca(a,b){if(0!==b.byteLength)if(2048<b.byteLength)0<p&&(a.enqueue(new Uint8Array(n.buffer,0,p)),n=new Uint8Array(2048),p=0),a.enqueue(b);else{var c=n.length-p;c<b.byteLength&&(0===c?a.enqueue(n):(n.set(b.subarray(0,c),p),a.enqueue(n),b=b.subarray(c)),n=new Uint8Array(2048),p=0);n.set(b,p);p+=b.byteLength}return!0}var q=new TextEncoder;function da(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),ea=Symbol.for("react.server.reference");function t(a,b,c){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:c}})}var fa=Function.prototype.bind,ha=Array.prototype.slice;function ia(){var a=fa.apply(this,arguments);if(this.$$typeof===ea){var b=ha.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:ea},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ia}})}return a}
var ka=Promise.prototype,la={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ma(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "__esModule":var c=a.$$id;a.default=t(function(){throw Error("Attempted to call the default export of "+c+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var e=t({},a.$$id,!0),d=new Proxy(e,na);a.status="fulfilled";a.value=d;return a.then=t(function(g){return Promise.resolve(g(d))},a.$$id+"#then",!1)}if("symbol"===typeof b)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");e=a[b];e||(e=t(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(e,"name",{value:b}),e=a[b]=new Proxy(e,la));return e}
var na={get:function(a,b){return ma(a,b)},getOwnPropertyDescriptor:function(a,b){var c=Object.getOwnPropertyDescriptor(a,b);c||(c={value:ma(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,c));return c},getPrototypeOf:function(){return ka},set:function(){throw Error("Cannot assign to a client module from a server module.");}},va={prefetchDNS:oa,preconnect:pa,preload:qa,preloadModule:ra,preinitStyle:sa,preinitScript:ta,preinitModuleScript:ua};
function oa(a){if("string"===typeof a&&a){var b=u();if(b){var c=b.hints,e="D|"+a;c.has(e)||(c.add(e),v(b,"D",a))}}}function pa(a,b){if("string"===typeof a){var c=u();if(c){var e=c.hints,d="C|"+(null==b?"null":b)+"|"+a;e.has(d)||(e.add(d),"string"===typeof b?v(c,"C",[a,b]):v(c,"C",a))}}}
function qa(a,b,c){if("string"===typeof a){var e=u();if(e){var d=e.hints,g="L";if("image"===b&&c){var f=c.imageSrcSet,h=c.imageSizes,k="";"string"===typeof f&&""!==f?(k+="["+f+"]","string"===typeof h&&(k+="["+h+"]")):k+="[][]"+a;g+="[image]"+k}else g+="["+b+"]"+a;d.has(g)||(d.add(g),(c=w(c))?v(e,"L",[a,b,c]):v(e,"L",[a,b]))}}}function ra(a,b){if("string"===typeof a){var c=u();if(c){var e=c.hints,d="m|"+a;if(!e.has(d))return e.add(d),(b=w(b))?v(c,"m",[a,b]):v(c,"m",a)}}}
function sa(a,b,c){if("string"===typeof a){var e=u();if(e){var d=e.hints,g="S|"+a;if(!d.has(g))return d.add(g),(c=w(c))?v(e,"S",[a,"string"===typeof b?b:0,c]):"string"===typeof b?v(e,"S",[a,b]):v(e,"S",a)}}}function ta(a,b){if("string"===typeof a){var c=u();if(c){var e=c.hints,d="X|"+a;if(!e.has(d))return e.add(d),(b=w(b))?v(c,"X",[a,b]):v(c,"X",a)}}}function ua(a,b){if("string"===typeof a){var c=u();if(c){var e=c.hints,d="M|"+a;if(!e.has(d))return e.add(d),(b=w(b))?v(c,"M",[a,b]):v(c,"M",a)}}}
function w(a){if(null==a)return null;var b=!1,c={},e;for(e in a)null!=a[e]&&(b=!0,c[e]=a[e]);return b?c:null}var wa=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,x="function"===typeof AsyncLocalStorage,xa=x?new AsyncLocalStorage:null;"object"===typeof async_hooks?async_hooks.createHook:function(){return{enable:function(){},disable:function(){}}};"object"===typeof async_hooks?async_hooks.executionAsyncId:null;
var z=Symbol.for("react.element"),ya=Symbol.for("react.fragment"),za=Symbol.for("react.context"),Aa=Symbol.for("react.forward_ref"),Ba=Symbol.for("react.suspense"),Ca=Symbol.for("react.suspense_list"),Da=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Ea=Symbol.for("react.memo_cache_sentinel"),B=Symbol.for("react.postpone"),Fa=Symbol.iterator,Ga=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ha(){}function Ia(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Ha,Ha),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(e){if("pending"===b.status){var d=b;d.status="fulfilled";d.value=e}},function(e){if("pending"===b.status){var d=b;d.status="rejected";d.reason=e}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Ja=b;throw Ga;}}var Ja=null;
function Ka(){if(null===Ja)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Ja;Ja=null;return a}var C=null,La=0,E=null;function Ma(){var a=E||[];E=null;return a}
var Ra={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:F,useTransition:F,readContext:Na,useContext:Na,useReducer:F,useRef:F,useState:F,useInsertionEffect:F,useLayoutEffect:F,useImperativeHandle:F,useEffect:F,useId:Oa,useSyncExternalStore:F,useCacheRefresh:function(){return Pa},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=Ea;return b},use:Qa};
function F(){throw Error("This Hook is not supported in Server Components.");}function Pa(){throw Error("Refreshing the cache is not supported in Server Components.");}function Na(){throw Error("Cannot read a Client Context from a Server Component.");}function Oa(){if(null===C)throw Error("useId can only be used while React is rendering");var a=C.identifierCount++;return":"+C.identifierPrefix+"S"+a.toString(32)+":"}
function Qa(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=La;La+=1;null===E&&(E=[]);return Ia(E,a,b)}a.$$typeof===za&&Na()}if(a.$$typeof===r){if(null!=a.value&&a.value.$$typeof===za)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.");}throw Error("An unsupported type was passed to use(): "+String(a));}function Sa(){return(new AbortController).signal}
function Ta(){var a=u();return a?a.cache:new Map}var Ua={getCacheSignal:function(){var a=Ta(),b=a.get(Sa);void 0===b&&(b=Sa(),a.set(Sa,b));return b},getCacheForType:function(a){var b=Ta(),c=b.get(a);void 0===c&&(c=a(),b.set(a,c));return c}},Va=Array.isArray,Wa=Object.getPrototypeOf;function Xa(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,c){return c})}
function Ya(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Va(a))return"[...]";if(null!==a&&a.$$typeof===Za)return"client";a=Xa(a);return"Object"===a?"{...}":a;case "function":return a.$$typeof===Za?"client":(a=a.displayName||a.name)?"function "+a:"function";default:return String(a)}}
function $a(a){if("string"===typeof a)return a;switch(a){case Ba:return"Suspense";case Ca:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case Aa:return $a(a.render);case Da:return $a(a.type);case A:var b=a._payload;a=a._init;try{return $a(a(b))}catch(c){}}return""}var Za=Symbol.for("react.client.reference");
function G(a,b){var c=Xa(a);if("Object"!==c&&"Array"!==c)return c;c=-1;var e=0;if(Va(a)){var d="[";for(var g=0;g<a.length;g++){0<g&&(d+=", ");var f=a[g];f="object"===typeof f&&null!==f?G(f):Ya(f);""+g===b?(c=d.length,e=f.length,d+=f):d=10>f.length&&40>d.length+f.length?d+f:d+"..."}d+="]"}else if(a.$$typeof===z)d="<"+$a(a.type)+"/>";else{if(a.$$typeof===Za)return"client";d="{";g=Object.keys(a);for(f=0;f<g.length;f++){0<f&&(d+=", ");var h=g[f],k=JSON.stringify(h);d+=('"'+h+'"'===k?h:k)+": ";k=a[h];
k="object"===typeof k&&null!==k?G(k):Ya(k);h===b?(c=d.length,e=k.length,d+=k):d=10>k.length&&40>d.length+k.length?d+k:d+"..."}d+="}"}return void 0===b?d:-1<c&&0<e?(a=" ".repeat(c)+"^".repeat(e),"\n  "+d+"\n  "+a):"\n  "+d}var ab=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,H=aa.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!H)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var bb=Object.prototype,I=JSON.stringify,cb=H.TaintRegistryObjects,J=H.TaintRegistryValues,db=H.TaintRegistryByteLengths,eb=H.TaintRegistryPendingRequests,fb=H.ReactCurrentCache,gb=ab.ReactCurrentDispatcher;function K(a){throw Error(a);}
function hb(a){a=a.taintCleanupQueue;eb.delete(a);for(var b=0;b<a.length;b++){var c=a[b],e=J.get(c);void 0!==e&&(1===e.count?J.delete(c):e.count--)}a.length=0}function ib(a){console.error(a)}function jb(){}
function kb(a,b,c,e,d){if(null!==fb.current&&fb.current!==Ua)throw Error("Currently React only supports one RSC renderer at a time.");wa.current=va;fb.current=Ua;var g=new Set,f=[],h=[];eb.add(h);var k=new Set;b={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:g,pingedTasks:f,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:h,onError:void 0===c?ib:c,onPostpone:void 0===d?jb:d};a=lb(b,a,null,!1,g);f.push(a);return b}var L=null;function u(){if(L)return L;if(x){var a=xa.getStore();if(a)return a}return null}
function mb(a,b,c){var e=lb(a,null,b.keyPath,b.implicitSlot,a.abortableTasks);switch(c.status){case "fulfilled":return e.model=c.value,nb(a,e),e.id;case "rejected":return b=c.reason,"object"===typeof b&&null!==b&&b.$$typeof===B?(M(a,b.message),N(a,e.id)):(b=O(a,b),P(a,e.id,b)),e.id;default:"string"!==typeof c.status&&(c.status="pending",c.then(function(d){"pending"===c.status&&(c.status="fulfilled",c.value=d)},function(d){"pending"===c.status&&(c.status="rejected",c.reason=d)}))}c.then(function(d){e.model=
d;nb(a,e)},function(d){"object"===typeof d&&null!==d&&d.$$typeof===B?(M(a,d.message),N(a,e.id)):(e.status=4,d=O(a,d),P(a,e.id,d));a.abortableTasks.delete(e);null!==a.destination&&Q(a,a.destination)});return e.id}function v(a,b,c){c=I(c);var e=a.nextChunkId++;b="H"+b;b=e.toString(16)+":"+b;c=q.encode(b+c+"\n");a.completedHintChunks.push(c);ob(a)}function pb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function qb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:pb}}
function rb(a,b,c,e,d){var g=b.thenableState;b.thenableState=null;La=0;E=g;e=e(d,void 0);if("object"===typeof e&&null!==e&&"function"===typeof e.then){d=e;if("fulfilled"===d.status)return d.value;e=qb(e)}d=b.keyPath;g=b.implicitSlot;null!==c?b.keyPath=null===d?c:d+","+c:null===d&&(b.implicitSlot=!0);a=R(a,b,sb,"",e);b.keyPath=d;b.implicitSlot=g;return a}function tb(a,b,c){return null!==b.keyPath?(a=[z,ya,b.keyPath,{children:c}],b.implicitSlot?[a]:a):c}
function ub(a,b,c,e){var d=a.keyPath;null===c?c=d:null!==d&&(c=d+","+c);b=[z,b,c,e];return a.implicitSlot&&null!==c?[b]:b}
function vb(a,b,c,e,d,g){if(null!==d&&void 0!==d)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof c)return c.$$typeof===r?ub(b,c,e,g):rb(a,b,e,c,g);if("string"===typeof c)return ub(b,c,e,g);if("symbol"===typeof c)return c===ya&&null===e?(e=b.implicitSlot,null===b.keyPath&&(b.implicitSlot=!0),a=R(a,b,sb,"",g.children),b.implicitSlot=e,a):ub(b,c,e,g);if(null!=c&&"object"===typeof c){if(c.$$typeof===r)return ub(b,c,e,g);switch(c.$$typeof){case A:var f=
c._init;c=f(c._payload);return vb(a,b,c,e,d,g);case Aa:return rb(a,b,e,c.render,g);case Da:return vb(a,b,c.type,e,d,g)}}throw Error("Unsupported Server Component type: "+Ya(c));}function nb(a,b){var c=a.pingedTasks;c.push(b);1===c.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return wb(a)},0))}
function lb(a,b,c,e,d){a.pendingChunks++;var g=a.nextChunkId++;"object"!==typeof b||null===b||null!==c||e||a.writtenObjects.set(b,g);var f={id:g,status:0,model:b,keyPath:c,implicitSlot:e,ping:function(){return nb(a,f)},toJSON:function(h,k){a:{var l=f.keyPath,y=f.implicitSlot;try{var m=R(a,f,this,h,k)}catch(ja){h=ja===Ga?Ka():ja;k=f.model;k="object"===typeof k&&null!==k&&(k.$$typeof===z||k.$$typeof===A);if("object"===typeof h&&null!==h){if("function"===typeof h.then){m=lb(a,f.model,f.keyPath,f.implicitSlot,
a.abortableTasks);var D=m.ping;h.then(D,D);m.thenableState=Ma();f.keyPath=l;f.implicitSlot=y;m=k?"$L"+m.id.toString(16):S(m.id);break a}if(h.$$typeof===B){a.pendingChunks++;m=a.nextChunkId++;M(a,h.message);N(a,m);f.keyPath=l;f.implicitSlot=y;m=k?"$L"+m.toString(16):S(m);break a}}f.keyPath=l;f.implicitSlot=y;if(k)a.pendingChunks++,l=a.nextChunkId++,y=O(a,h),P(a,l,y),m="$L"+l.toString(16);else throw h;}}return m},thenableState:null};d.add(f);return f}function S(a){return"$"+a.toString(16)}
function xb(a,b,c){a=I(c);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function yb(a,b,c,e){var d=e.$$async?e.$$id+"#async":e.$$id,g=a.writtenClientReferences,f=g.get(d);if(void 0!==f)return b[0]===z&&"1"===c?"$L"+f.toString(16):S(f);try{var h=a.bundlerConfig,k=e.$$id;f="";var l=h[k];if(l)f=l.name;else{var y=k.lastIndexOf("#");-1!==y&&(f=k.slice(y+1),l=h[k.slice(0,y)]);if(!l)throw Error('Could not find the module "'+k+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var m=!0===e.$$async?[l.id,l.chunks,f,1]:[l.id,l.chunks,
f];a.pendingChunks++;var D=a.nextChunkId++,ja=I(m),Rb=D.toString(16)+":I"+ja+"\n",Sb=q.encode(Rb);a.completedImportChunks.push(Sb);g.set(d,D);return b[0]===z&&"1"===c?"$L"+D.toString(16):S(D)}catch(Tb){return a.pendingChunks++,b=a.nextChunkId++,c=O(a,Tb),P(a,b,c),S(b)}}function T(a,b){b=lb(a,b,null,!1,a.abortableTasks);zb(a,b);return b.id}
function U(a,b,c){if(db.has(c.byteLength)){var e=J.get(String.fromCharCode.apply(String,new Uint8Array(c.buffer,c.byteOffset,c.byteLength)));void 0!==e&&K(e.message)}a.pendingChunks+=2;e=a.nextChunkId++;var d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength);c=2048<c.byteLength?d.slice():d;d=c.byteLength;b=e.toString(16)+":"+b+d.toString(16)+",";b=q.encode(b);a.completedRegularChunks.push(b,c);return S(e)}var V=!1;
function R(a,b,c,e,d){b.model=d;if(d===z)return"$";if(null===d)return null;if("object"===typeof d){switch(d.$$typeof){case z:c=a.writtenObjects;e=c.get(d);if(void 0!==e){if(null===b.keyPath&&!b.implicitSlot)if(V===d)V=null;else return-1===e?(a=T(a,d),S(a)):S(e)}else c.set(d,-1);c=d.props;e=c.ref;return vb(a,b,d.type,d.key,void 0!==e?e:null,c);case A:return b.thenableState=null,c=d._init,d=c(d._payload),R(a,b,sb,"",d)}if(d.$$typeof===r)return yb(a,c,e,d);c=cb.get(d);void 0!==c&&K(c);c=a.writtenObjects;
e=c.get(d);if("function"===typeof d.then){if(void 0!==e){if(null!==b.keyPath||b.implicitSlot)return"$@"+mb(a,b,d).toString(16);if(V===d)V=null;else return"$@"+e.toString(16)}a=mb(a,b,d);c.set(d,a);return"$@"+a.toString(16)}if(void 0!==e)if(V===d)V=null;else return-1===e?(a=T(a,d),S(a)):S(e);else c.set(d,-1);if(Va(d))return tb(a,b,d);if(d instanceof Map){d=Array.from(d);for(b=0;b<d.length;b++)c=d[b][0],"object"===typeof c&&null!==c&&(e=a.writtenObjects,void 0===e.get(c)&&e.set(c,-1));return"$Q"+T(a,
d).toString(16)}if(d instanceof Set){d=Array.from(d);for(b=0;b<d.length;b++)c=d[b],"object"===typeof c&&null!==c&&(e=a.writtenObjects,void 0===e.get(c)&&e.set(c,-1));return"$W"+T(a,d).toString(16)}if(d instanceof ArrayBuffer)return U(a,"A",new Uint8Array(d));if(d instanceof Int8Array)return U(a,"C",d);if(d instanceof Uint8Array)return U(a,"c",d);if(d instanceof Uint8ClampedArray)return U(a,"U",d);if(d instanceof Int16Array)return U(a,"S",d);if(d instanceof Uint16Array)return U(a,"s",d);if(d instanceof
Int32Array)return U(a,"L",d);if(d instanceof Uint32Array)return U(a,"l",d);if(d instanceof Float32Array)return U(a,"F",d);if(d instanceof Float64Array)return U(a,"d",d);if(d instanceof BigInt64Array)return U(a,"N",d);if(d instanceof BigUint64Array)return U(a,"m",d);if(d instanceof DataView)return U(a,"V",d);null===d||"object"!==typeof d?c=null:(c=Fa&&d[Fa]||d["@@iterator"],c="function"===typeof c?c:null);if(c)return tb(a,b,Array.from(d));a=Wa(d);if(a!==bb&&(null===a||null!==Wa(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");
return d}if("string"===typeof d){b=J.get(d);void 0!==b&&K(b.message);if("Z"===d[d.length-1]&&c[e]instanceof Date)return"$D"+d;if(1024<=d.length)return a.pendingChunks+=2,b=a.nextChunkId++,d=q.encode(d),c=d.byteLength,c=b.toString(16)+":T"+c.toString(16)+",",c=q.encode(c),a.completedRegularChunks.push(c,d),S(b);a="$"===d[0]?"$"+d:d;return a}if("boolean"===typeof d)return d;if("number"===typeof d)return Number.isFinite(d)?0===d&&-Infinity===1/d?"$-0":d:Infinity===d?"$Infinity":-Infinity===d?"$-Infinity":
"$NaN";if("undefined"===typeof d)return"$undefined";if("function"===typeof d){if(d.$$typeof===r)return yb(a,c,e,d);if(d.$$typeof===ea)return b=a.writtenServerReferences,c=b.get(d),void 0!==c?a="$F"+c.toString(16):(c=d.$$bound,c={id:d.$$id,bound:c?Promise.resolve(c):null},a=T(a,c),b.set(d,a),a="$F"+a.toString(16)),a;a=cb.get(d);void 0!==a&&K(a);if(/^on[A-Z]/.test(e))throw Error("Event handlers cannot be passed to Client Component props."+G(c,e)+"\nIf you need interactivity, consider converting part of this to a Client Component.");
throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+G(c,e));}if("symbol"===typeof d){b=a.writtenSymbols;var g=b.get(d);if(void 0!==g)return S(g);g=d.description;if(Symbol.for(g)!==d)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(d.description+") cannot be found among global symbols.")+
G(c,e));a.pendingChunks++;c=a.nextChunkId++;e=xb(a,c,"$S"+g);a.completedImportChunks.push(e);b.set(d,c);return S(c)}if("bigint"===typeof d)return a=J.get(d),void 0!==a&&K(a.message),"$n"+d.toString(10);throw Error("Type "+typeof d+" is not supported in Client Component props."+G(c,e));}function M(a,b){var c=L;L=null;try{var e=a.onPostpone;x?xa.run(void 0,e,b):e(b)}finally{L=c}}
function O(a,b){var c=L;L=null;try{var e=a.onError;var d=x?xa.run(void 0,e,b):e(b)}finally{L=c}if(null!=d&&"string"!==typeof d)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof d+'" instead');return d||""}function Ab(a,b){hb(a);null!==a.destination?(a.status=2,da(a.destination,b)):(a.status=1,a.fatalError=b)}
function N(a,b){b=b.toString(16)+":P\n";b=q.encode(b);a.completedErrorChunks.push(b)}function P(a,b,c){c={digest:c};b=b.toString(16)+":E"+I(c)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}var sb={};
function zb(a,b){if(0===b.status)try{V=b.model;var c=R(a,b,sb,"",b.model);V=c;b.keyPath=null;b.implicitSlot=!1;var e="object"===typeof c&&null!==c?I(c,b.toJSON):I(c),d=b.id.toString(16)+":"+e+"\n",g=q.encode(d);a.completedRegularChunks.push(g);a.abortableTasks.delete(b);b.status=1}catch(l){var f=l===Ga?Ka():l;if("object"===typeof f&&null!==f){if("function"===typeof f.then){var h=b.ping;f.then(h,h);b.thenableState=Ma();return}if(f.$$typeof===B){a.abortableTasks.delete(b);b.status=4;M(a,f.message);
N(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;var k=O(a,f);P(a,b.id,k)}finally{}}function wb(a){var b=gb.current;gb.current=Ra;var c=L;C=L=a;try{var e=a.pingedTasks;a.pingedTasks=[];for(var d=0;d<e.length;d++)zb(a,e[d]);null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),Ab(a,g)}finally{gb.current=b,C=null,L=c}}
function Q(a,b){n=new Uint8Array(2048);p=0;try{for(var c=a.completedImportChunks,e=0;e<c.length;e++)a.pendingChunks--,ca(b,c[e]);c.splice(0,e);var d=a.completedHintChunks;for(e=0;e<d.length;e++)ca(b,d[e]);d.splice(0,e);var g=a.completedRegularChunks;for(e=0;e<g.length;e++)a.pendingChunks--,ca(b,g[e]);g.splice(0,e);var f=a.completedErrorChunks;for(e=0;e<f.length;e++)a.pendingChunks--,ca(b,f[e]);f.splice(0,e)}finally{a.flushScheduled=!1,n&&0<p&&(b.enqueue(new Uint8Array(n.buffer,0,p)),n=null,p=0)}0===
a.pendingChunks&&(hb(a),b.close())}function Bb(a){a.flushScheduled=null!==a.destination;x?setTimeout(function(){return xa.run(a,wb,a)},0):setTimeout(function(){return wb(a)},0)}function ob(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setTimeout(function(){return Q(a,b)},0)}}
function Cb(a,b){try{var c=a.abortableTasks;if(0<c.size){a.pendingChunks++;var e=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===B)M(a,b.message),N(a,e,b);else{var d=void 0===b?Error("The render was aborted by the server without a reason."):b,g=O(a,d);P(a,e,g,d)}c.forEach(function(f){f.status=3;var h=S(e);f=xb(a,f.id,h);a.completedErrorChunks.push(f)});c.clear()}null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),Ab(a,f)}}
function Db(a,b){var c="",e=a[b];if(e)c=e.name;else{var d=b.lastIndexOf("#");-1!==d&&(c=b.slice(d+1),e=a[b.slice(0,d)]);if(!e)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[e.id,e.chunks,c]}var Eb=new Map;
function Fb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function Gb(){}
function Hb(a){for(var b=a[1],c=[],e=0;e<b.length;){var d=b[e++];b[e++];var g=Eb.get(d);if(void 0===g){g=__webpack_chunk_load__(d);c.push(g);var f=Eb.set.bind(Eb,d,null);g.then(f,Gb);Eb.set(d,g)}else null!==g&&c.push(g)}return 4===a.length?0===c.length?Fb(a[0]):Promise.all(c).then(function(){return Fb(a[0])}):0<c.length?Promise.all(c):null}
function W(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Ib(a,b,c,e){this.status=a;this.value=b;this.reason=c;this._response=e}Ib.prototype=Object.create(Promise.prototype);
Ib.prototype.then=function(a,b){switch(this.status){case "resolved_model":Jb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Kb(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}
function Lb(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&Kb(c,b)}}function Mb(a,b,c,e,d,g){var f=Db(a._bundlerConfig,b);a=Hb(f);if(c)c=Promise.all([c,a]).then(function(h){h=h[0];var k=W(f);return k.bind.apply(k,[null].concat(h))});else if(a)c=Promise.resolve(a).then(function(){return W(f)});else return W(f);c.then(Nb(e,d,g),Ob(e));return null}var X=null,Y=null;
function Jb(a){var b=X,c=Y;X=a;Y=null;try{var e=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=e,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=e)}catch(d){a.status="rejected",a.reason=d}finally{X=b,Y=c}}function Pb(a,b){a._chunks.forEach(function(c){"pending"===c.status&&Lb(c,b)})}
function Z(a,b){var c=a._chunks,e=c.get(b);e||(e=a._formData.get(a._prefix+b),e=null!=e?new Ib("resolved_model",e,null,a):new Ib("pending",null,null,a),c.set(b,e));return e}function Nb(a,b,c){if(Y){var e=Y;e.deps++}else e=Y={deps:1,value:null};return function(d){b[c]=d;e.deps--;0===e.deps&&"blocked"===a.status&&(d=a.value,a.status="fulfilled",a.value=e.value,null!==d&&Kb(d,e.value))}}function Ob(a){return function(b){return Lb(a,b)}}
function Qb(a,b){a=Z(a,b);"resolved_model"===a.status&&Jb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Ub(a,b,c,e){if("$"===e[0])switch(e[1]){case "$":return e.slice(1);case "@":return b=parseInt(e.slice(2),16),Z(a,b);case "S":return Symbol.for(e.slice(2));case "F":return e=parseInt(e.slice(2),16),e=Qb(a,e),Mb(a,e.id,e.bound,X,b,c);case "Q":return b=parseInt(e.slice(2),16),a=Qb(a,b),new Map(a);case "W":return b=parseInt(e.slice(2),16),a=Qb(a,b),new Set(a);case "K":b=e.slice(2);var d=a._prefix+b+"_",g=new FormData;a._formData.forEach(function(f,h){h.startsWith(d)&&g.append(h.slice(d.length),
f)});return g;case "I":return Infinity;case "-":return"$-0"===e?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(e.slice(2)));case "n":return BigInt(e.slice(2));default:e=parseInt(e.slice(1),16);a=Z(a,e);switch(a.status){case "resolved_model":Jb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return e=X,a.then(Nb(e,b,c),Ob(e)),null;default:throw a.reason;}}return e}
function Vb(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,e=new Map,d={_bundlerConfig:a,_prefix:b,_formData:c,_chunks:e,_fromJSON:function(g,f){return"string"===typeof f?Ub(d,this,g,f):f}};return d}function Wb(a){Pb(a,Error("Connection closed."))}function Xb(a,b,c){var e=Db(a,b);a=Hb(e);return c?Promise.all([c,a]).then(function(d){d=d[0];var g=W(e);return g.bind.apply(g,[null].concat(d))}):a?Promise.resolve(a).then(function(){return W(e)}):Promise.resolve(W(e))}
function Yb(a,b,c){a=Vb(b,c,a);Wb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=t({},a,!1);return new Proxy(a,na)};
exports.decodeAction=function(a,b){var c=new FormData,e=null;a.forEach(function(d,g){g.startsWith("$ACTION_")?g.startsWith("$ACTION_REF_")?(d="$ACTION_"+g.slice(12)+":",d=Yb(a,b,d),e=Xb(b,d.id,d.bound)):g.startsWith("$ACTION_ID_")&&(d=g.slice(11),e=Xb(b,d,null)):c.append(g,d)});return null===e?null:e.then(function(d){return d.bind(null,c)})};
exports.decodeFormState=function(a,b,c){var e=b.get("$ACTION_KEY");if("string"!==typeof e)return Promise.resolve(null);var d=null;b.forEach(function(f,h){h.startsWith("$ACTION_REF_")&&(f="$ACTION_"+h.slice(12)+":",d=Yb(b,c,f))});if(null===d)return Promise.resolve(null);var g=d.id;return Promise.resolve(d.bound).then(function(f){return null===f?null:[a,e,g,f.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var c=new FormData;c.append("0",a);a=c}a=Vb(b,"",a);b=Z(a,0);Wb(a);return b};
exports.registerClientReference=function(a,b,c){return t(a,b+"#"+c,!1)};exports.registerServerReference=function(a,b,c){return Object.defineProperties(a,{$$typeof:{value:ea},$$id:{value:null===c?b:b+"#"+c,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:ia,configurable:!0}})};
exports.renderToReadableStream=function(a,b,c){var e=kb(a,b,c?c.onError:void 0,c?c.identifierPrefix:void 0,c?c.onPostpone:void 0);if(c&&c.signal){var d=c.signal;if(d.aborted)Cb(e,d.reason);else{var g=function(){Cb(e,d.reason);d.removeEventListener("abort",g)};d.addEventListener("abort",g)}}return new ReadableStream({type:"bytes",start:function(){Bb(e)},pull:function(f){if(1===e.status)e.status=2,da(f,e.fatalError);else if(2!==e.status&&null===e.destination){e.destination=f;try{Q(e,f)}catch(h){O(e,
h),Ab(e,h)}}},cancel:function(f){e.destination=null;Cb(e,f)}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-webpack-server.edge.production.min.js.map
