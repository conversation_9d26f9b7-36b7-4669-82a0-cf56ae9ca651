/*
 React
 react-server-dom-turbopack-server.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("util");require("crypto");var ba=require("async_hooks"),ca=require("react"),da=require("react-dom"),l=null,n=0,q=!0;function r(a,b){a=a.write(b);q=q&&a}
function ea(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<n&&(r(a,l.subarray(0,n)),l=new Uint8Array(2048),n=0),r(a,fa.encode(b));else{var c=l;0<n&&(c=l.subarray(n));c=fa.encodeInto(b,c);var e=c.read;n+=c.written;e<b.length&&(r(a,l.subarray(0,n)),l=new Uint8Array(2048),n=fa.encodeInto(b.slice(e),l).written);2048===n&&(r(a,l),l=new Uint8Array(2048),n=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<n&&(r(a,l.subarray(0,n)),l=new Uint8Array(2048),n=0),r(a,b)):(c=l.length-n,c<b.byteLength&&
(0===c?r(a,l):(l.set(b.subarray(0,c),n),n+=c,r(a,l),b=b.subarray(c)),l=new Uint8Array(2048),n=0),l.set(b,n),n+=b.byteLength,2048===n&&(r(a,l),l=new Uint8Array(2048),n=0)));return q}var fa=new aa.TextEncoder,t=Symbol.for("react.client.reference"),ha=Symbol.for("react.server.reference");function v(a,b,c){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:b},$$async:{value:c}})}var ia=Function.prototype.bind,ka=Array.prototype.slice;
function la(){var a=ia.apply(this,arguments);if(this.$$typeof===ha){var b=ka.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:ha},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:la}})}return a}
var ma=Promise.prototype,na={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function oa(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "__esModule":var c=a.$$id;a.default=v(function(){throw Error("Attempted to call the default export of "+c+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var e=v({},a.$$id,!0),d=new Proxy(e,pa);a.status="fulfilled";a.value=d;return a.then=v(function(f){return Promise.resolve(f(d))},a.$$id+"#then",!1)}if("symbol"===typeof b)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");e=a[b];e||(e=v(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(e,"name",{value:b}),e=a[b]=new Proxy(e,na));return e}
var pa={get:function(a,b){return oa(a,b)},getOwnPropertyDescriptor:function(a,b){var c=Object.getOwnPropertyDescriptor(a,b);c||(c={value:oa(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,c));return c},getPrototypeOf:function(){return ma},set:function(){throw Error("Cannot assign to a client module from a server module.");}},xa={prefetchDNS:qa,preconnect:ra,preload:sa,preloadModule:ta,preinitStyle:ua,preinitScript:va,preinitModuleScript:wa};
function qa(a){if("string"===typeof a&&a){var b=w();if(b){var c=b.hints,e="D|"+a;c.has(e)||(c.add(e),x(b,"D",a))}}}function ra(a,b){if("string"===typeof a){var c=w();if(c){var e=c.hints,d="C|"+(null==b?"null":b)+"|"+a;e.has(d)||(e.add(d),"string"===typeof b?x(c,"C",[a,b]):x(c,"C",a))}}}
function sa(a,b,c){if("string"===typeof a){var e=w();if(e){var d=e.hints,f="L";if("image"===b&&c){var g=c.imageSrcSet,k=c.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;d.has(f)||(d.add(f),(c=y(c))?x(e,"L",[a,b,c]):x(e,"L",[a,b]))}}}function ta(a,b){if("string"===typeof a){var c=w();if(c){var e=c.hints,d="m|"+a;if(!e.has(d))return e.add(d),(b=y(b))?x(c,"m",[a,b]):x(c,"m",a)}}}
function ua(a,b,c){if("string"===typeof a){var e=w();if(e){var d=e.hints,f="S|"+a;if(!d.has(f))return d.add(f),(c=y(c))?x(e,"S",[a,"string"===typeof b?b:0,c]):"string"===typeof b?x(e,"S",[a,b]):x(e,"S",a)}}}function va(a,b){if("string"===typeof a){var c=w();if(c){var e=c.hints,d="X|"+a;if(!e.has(d))return e.add(d),(b=y(b))?x(c,"X",[a,b]):x(c,"X",a)}}}function wa(a,b){if("string"===typeof a){var c=w();if(c){var e=c.hints,d="M|"+a;if(!e.has(d))return e.add(d),(b=y(b))?x(c,"M",[a,b]):x(c,"M",a)}}}
function y(a){if(null==a)return null;var b=!1,c={},e;for(e in a)null!=a[e]&&(b=!0,c[e]=a[e]);return b?c:null}
var ya=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,za=new ba.AsyncLocalStorage,z=Symbol.for("react.element"),Aa=Symbol.for("react.fragment"),Ba=Symbol.for("react.context"),Ca=Symbol.for("react.forward_ref"),Da=Symbol.for("react.suspense"),Ea=Symbol.for("react.suspense_list"),Fa=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Ga=Symbol.for("react.memo_cache_sentinel"),B=Symbol.for("react.postpone"),Ha=Symbol.iterator,Ia=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ja(){}function Ka(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Ja,Ja),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(e){if("pending"===b.status){var d=b;d.status="fulfilled";d.value=e}},function(e){if("pending"===b.status){var d=b;d.status="rejected";d.reason=e}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}La=b;throw Ia;}}var La=null;
function Ma(){if(null===La)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=La;La=null;return a}var C=null,Na=0,E=null;function Oa(){var a=E||[];E=null;return a}
var Ta={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:F,useTransition:F,readContext:Pa,useContext:Pa,useReducer:F,useRef:F,useState:F,useInsertionEffect:F,useLayoutEffect:F,useImperativeHandle:F,useEffect:F,useId:Qa,useSyncExternalStore:F,useCacheRefresh:function(){return Ra},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=Ga;return b},use:Sa};
function F(){throw Error("This Hook is not supported in Server Components.");}function Ra(){throw Error("Refreshing the cache is not supported in Server Components.");}function Pa(){throw Error("Cannot read a Client Context from a Server Component.");}function Qa(){if(null===C)throw Error("useId can only be used while React is rendering");var a=C.identifierCount++;return":"+C.identifierPrefix+"S"+a.toString(32)+":"}
function Sa(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Na;Na+=1;null===E&&(E=[]);return Ka(E,a,b)}a.$$typeof===Ba&&Pa()}if(a.$$typeof===t){if(null!=a.value&&a.value.$$typeof===Ba)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.");}throw Error("An unsupported type was passed to use(): "+String(a));}function Ua(){return(new AbortController).signal}
function Va(){var a=w();return a?a.cache:new Map}var Wa={getCacheSignal:function(){var a=Va(),b=a.get(Ua);void 0===b&&(b=Ua(),a.set(Ua,b));return b},getCacheForType:function(a){var b=Va(),c=b.get(a);void 0===c&&(c=a(),b.set(a,c));return c}},Xa=Array.isArray,Ya=Object.getPrototypeOf;function Za(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,c){return c})}
function $a(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Xa(a))return"[...]";if(null!==a&&a.$$typeof===ab)return"client";a=Za(a);return"Object"===a?"{...}":a;case "function":return a.$$typeof===ab?"client":(a=a.displayName||a.name)?"function "+a:"function";default:return String(a)}}
function bb(a){if("string"===typeof a)return a;switch(a){case Da:return"Suspense";case Ea:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case Ca:return bb(a.render);case Fa:return bb(a.type);case A:var b=a._payload;a=a._init;try{return bb(a(b))}catch(c){}}return""}var ab=Symbol.for("react.client.reference");
function G(a,b){var c=Za(a);if("Object"!==c&&"Array"!==c)return c;c=-1;var e=0;if(Xa(a)){var d="[";for(var f=0;f<a.length;f++){0<f&&(d+=", ");var g=a[f];g="object"===typeof g&&null!==g?G(g):$a(g);""+f===b?(c=d.length,e=g.length,d+=g):d=10>g.length&&40>d.length+g.length?d+g:d+"..."}d+="]"}else if(a.$$typeof===z)d="<"+bb(a.type)+"/>";else{if(a.$$typeof===ab)return"client";d="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(d+=", ");var k=f[g],h=JSON.stringify(k);d+=('"'+k+'"'===h?k:h)+": ";h=a[k];
h="object"===typeof h&&null!==h?G(h):$a(h);k===b?(c=d.length,e=h.length,d+=h):d=10>h.length&&40>d.length+h.length?d+h:d+"..."}d+="}"}return void 0===b?d:-1<c&&0<e?(a=" ".repeat(c)+"^".repeat(e),"\n  "+d+"\n  "+a):"\n  "+d}var cb=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,H=ca.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!H)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var db=Object.prototype,I=JSON.stringify,eb=H.TaintRegistryObjects,J=H.TaintRegistryValues,fb=H.TaintRegistryByteLengths,gb=H.TaintRegistryPendingRequests,hb=H.ReactCurrentCache,ib=cb.ReactCurrentDispatcher;function K(a){throw Error(a);}
function jb(a){a=a.taintCleanupQueue;gb.delete(a);for(var b=0;b<a.length;b++){var c=a[b],e=J.get(c);void 0!==e&&(1===e.count?J.delete(c):e.count--)}a.length=0}function kb(a){console.error(a)}function lb(){}
function mb(a,b,c,e,d){if(null!==hb.current&&hb.current!==Wa)throw Error("Currently React only supports one RSC renderer at a time.");ya.current=xa;hb.current=Wa;var f=new Set,g=[],k=[];gb.add(k);var h=new Set;b={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:h,abortableTasks:f,pingedTasks:g,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:k,onError:void 0===c?kb:c,onPostpone:void 0===d?lb:d};a=nb(b,a,null,!1,f);g.push(a);return b}var L=null;function w(){if(L)return L;var a=za.getStore();return a?a:null}
function ob(a,b,c){var e=nb(a,null,b.keyPath,b.implicitSlot,a.abortableTasks);switch(c.status){case "fulfilled":return e.model=c.value,pb(a,e),e.id;case "rejected":return b=c.reason,"object"===typeof b&&null!==b&&b.$$typeof===B?(M(a,b.message),N(a,e.id)):(b=O(a,b),P(a,e.id,b)),e.id;default:"string"!==typeof c.status&&(c.status="pending",c.then(function(d){"pending"===c.status&&(c.status="fulfilled",c.value=d)},function(d){"pending"===c.status&&(c.status="rejected",c.reason=d)}))}c.then(function(d){e.model=
d;pb(a,e)},function(d){"object"===typeof d&&null!==d&&d.$$typeof===B?(M(a,d.message),N(a,e.id)):(e.status=4,d=O(a,d),P(a,e.id,d));a.abortableTasks.delete(e);null!==a.destination&&Q(a,a.destination)});return e.id}function x(a,b,c){c=I(c);var e=a.nextChunkId++;b="H"+b;b=e.toString(16)+":"+b;a.completedHintChunks.push(b+c+"\n");qb(a)}function rb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function sb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:rb}}
function tb(a,b,c,e,d){var f=b.thenableState;b.thenableState=null;Na=0;E=f;e=e(d,void 0);if("object"===typeof e&&null!==e&&"function"===typeof e.then){d=e;if("fulfilled"===d.status)return d.value;e=sb(e)}d=b.keyPath;f=b.implicitSlot;null!==c?b.keyPath=null===d?c:d+","+c:null===d&&(b.implicitSlot=!0);a=R(a,b,ub,"",e);b.keyPath=d;b.implicitSlot=f;return a}function vb(a,b,c){return null!==b.keyPath?(a=[z,Aa,b.keyPath,{children:c}],b.implicitSlot?[a]:a):c}
function wb(a,b,c,e){var d=a.keyPath;null===c?c=d:null!==d&&(c=d+","+c);b=[z,b,c,e];return a.implicitSlot&&null!==c?[b]:b}
function xb(a,b,c,e,d,f){if(null!==d&&void 0!==d)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof c)return c.$$typeof===t?wb(b,c,e,f):tb(a,b,e,c,f);if("string"===typeof c)return wb(b,c,e,f);if("symbol"===typeof c)return c===Aa&&null===e?(e=b.implicitSlot,null===b.keyPath&&(b.implicitSlot=!0),a=R(a,b,ub,"",f.children),b.implicitSlot=e,a):wb(b,c,e,f);if(null!=c&&"object"===typeof c){if(c.$$typeof===t)return wb(b,c,e,f);switch(c.$$typeof){case A:var g=
c._init;c=g(c._payload);return xb(a,b,c,e,d,f);case Ca:return tb(a,b,e,c.render,f);case Fa:return xb(a,b,c.type,e,d,f)}}throw Error("Unsupported Server Component type: "+$a(c));}function pb(a,b){var c=a.pingedTasks;c.push(b);1===c.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return yb(a)}))}
function nb(a,b,c,e,d){a.pendingChunks++;var f=a.nextChunkId++;"object"!==typeof b||null===b||null!==c||e||a.writtenObjects.set(b,f);var g={id:f,status:0,model:b,keyPath:c,implicitSlot:e,ping:function(){return pb(a,g)},toJSON:function(k,h){a:{var p=g.keyPath,u=g.implicitSlot;try{var m=R(a,g,this,k,h)}catch(ja){k=ja===Ia?Ma():ja;h=g.model;h="object"===typeof h&&null!==h&&(h.$$typeof===z||h.$$typeof===A);if("object"===typeof k&&null!==k){if("function"===typeof k.then){m=nb(a,g.model,g.keyPath,g.implicitSlot,
a.abortableTasks);var D=m.ping;k.then(D,D);m.thenableState=Oa();g.keyPath=p;g.implicitSlot=u;m=h?"$L"+m.id.toString(16):S(m.id);break a}if(k.$$typeof===B){a.pendingChunks++;m=a.nextChunkId++;M(a,k.message);N(a,m);g.keyPath=p;g.implicitSlot=u;m=h?"$L"+m.toString(16):S(m);break a}}g.keyPath=p;g.implicitSlot=u;if(h)a.pendingChunks++,p=a.nextChunkId++,u=O(a,k),P(a,p,u),m="$L"+p.toString(16);else throw k;}}return m},thenableState:null};d.add(g);return g}function S(a){return"$"+a.toString(16)}
function zb(a,b,c){a=I(c);return b.toString(16)+":"+a+"\n"}
function Ab(a,b,c,e){var d=e.$$async?e.$$id+"#async":e.$$id,f=a.writtenClientReferences,g=f.get(d);if(void 0!==g)return b[0]===z&&"1"===c?"$L"+g.toString(16):S(g);try{var k=a.bundlerConfig,h=e.$$id;g="";var p=k[h];if(p)g=p.name;else{var u=h.lastIndexOf("#");-1!==u&&(g=h.slice(u+1),p=k[h.slice(0,u)]);if(!p)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var m=!0===e.$$async?[p.id,p.chunks,g,1]:[p.id,p.chunks,
g];a.pendingChunks++;var D=a.nextChunkId++,ja=I(m),Ub=D.toString(16)+":I"+ja+"\n";a.completedImportChunks.push(Ub);f.set(d,D);return b[0]===z&&"1"===c?"$L"+D.toString(16):S(D)}catch(Vb){return a.pendingChunks++,b=a.nextChunkId++,c=O(a,Vb),P(a,b,c),S(b)}}function T(a,b){b=nb(a,b,null,!1,a.abortableTasks);Bb(a,b);return b.id}
function U(a,b,c){if(fb.has(c.byteLength)){var e=J.get(String.fromCharCode.apply(String,new Uint8Array(c.buffer,c.byteOffset,c.byteLength)));void 0!==e&&K(e.message)}a.pendingChunks+=2;e=a.nextChunkId++;c=new Uint8Array(c.buffer,c.byteOffset,c.byteLength);var d=c.byteLength;b=e.toString(16)+":"+b+d.toString(16)+",";a.completedRegularChunks.push(b,c);return S(e)}var V=!1;
function R(a,b,c,e,d){b.model=d;if(d===z)return"$";if(null===d)return null;if("object"===typeof d){switch(d.$$typeof){case z:c=a.writtenObjects;e=c.get(d);if(void 0!==e){if(null===b.keyPath&&!b.implicitSlot)if(V===d)V=null;else return-1===e?(a=T(a,d),S(a)):S(e)}else c.set(d,-1);c=d.props;e=c.ref;return xb(a,b,d.type,d.key,void 0!==e?e:null,c);case A:return b.thenableState=null,c=d._init,d=c(d._payload),R(a,b,ub,"",d)}if(d.$$typeof===t)return Ab(a,c,e,d);c=eb.get(d);void 0!==c&&K(c);c=a.writtenObjects;
e=c.get(d);if("function"===typeof d.then){if(void 0!==e){if(null!==b.keyPath||b.implicitSlot)return"$@"+ob(a,b,d).toString(16);if(V===d)V=null;else return"$@"+e.toString(16)}a=ob(a,b,d);c.set(d,a);return"$@"+a.toString(16)}if(void 0!==e)if(V===d)V=null;else return-1===e?(a=T(a,d),S(a)):S(e);else c.set(d,-1);if(Xa(d))return vb(a,b,d);if(d instanceof Map){d=Array.from(d);for(b=0;b<d.length;b++)c=d[b][0],"object"===typeof c&&null!==c&&(e=a.writtenObjects,void 0===e.get(c)&&e.set(c,-1));return"$Q"+T(a,
d).toString(16)}if(d instanceof Set){d=Array.from(d);for(b=0;b<d.length;b++)c=d[b],"object"===typeof c&&null!==c&&(e=a.writtenObjects,void 0===e.get(c)&&e.set(c,-1));return"$W"+T(a,d).toString(16)}if(d instanceof ArrayBuffer)return U(a,"A",new Uint8Array(d));if(d instanceof Int8Array)return U(a,"C",d);if(d instanceof Uint8Array)return U(a,"c",d);if(d instanceof Uint8ClampedArray)return U(a,"U",d);if(d instanceof Int16Array)return U(a,"S",d);if(d instanceof Uint16Array)return U(a,"s",d);if(d instanceof
Int32Array)return U(a,"L",d);if(d instanceof Uint32Array)return U(a,"l",d);if(d instanceof Float32Array)return U(a,"F",d);if(d instanceof Float64Array)return U(a,"d",d);if(d instanceof BigInt64Array)return U(a,"N",d);if(d instanceof BigUint64Array)return U(a,"m",d);if(d instanceof DataView)return U(a,"V",d);null===d||"object"!==typeof d?c=null:(c=Ha&&d[Ha]||d["@@iterator"],c="function"===typeof c?c:null);if(c)return vb(a,b,Array.from(d));a=Ya(d);if(a!==db&&(null===a||null!==Ya(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");
return d}if("string"===typeof d){b=J.get(d);void 0!==b&&K(b.message);if("Z"===d[d.length-1]&&c[e]instanceof Date)return"$D"+d;if(1024<=d.length)return a.pendingChunks+=2,b=a.nextChunkId++,c="string"===typeof d?Buffer.byteLength(d,"utf8"):d.byteLength,c=b.toString(16)+":T"+c.toString(16)+",",a.completedRegularChunks.push(c,d),S(b);a="$"===d[0]?"$"+d:d;return a}if("boolean"===typeof d)return d;if("number"===typeof d)return Number.isFinite(d)?0===d&&-Infinity===1/d?"$-0":d:Infinity===d?"$Infinity":-Infinity===
d?"$-Infinity":"$NaN";if("undefined"===typeof d)return"$undefined";if("function"===typeof d){if(d.$$typeof===t)return Ab(a,c,e,d);if(d.$$typeof===ha)return b=a.writtenServerReferences,c=b.get(d),void 0!==c?a="$F"+c.toString(16):(c=d.$$bound,c={id:d.$$id,bound:c?Promise.resolve(c):null},a=T(a,c),b.set(d,a),a="$F"+a.toString(16)),a;a=eb.get(d);void 0!==a&&K(a);if(/^on[A-Z]/.test(e))throw Error("Event handlers cannot be passed to Client Component props."+G(c,e)+"\nIf you need interactivity, consider converting part of this to a Client Component.");
throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+G(c,e));}if("symbol"===typeof d){b=a.writtenSymbols;var f=b.get(d);if(void 0!==f)return S(f);f=d.description;if(Symbol.for(f)!==d)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(d.description+") cannot be found among global symbols.")+
G(c,e));a.pendingChunks++;c=a.nextChunkId++;e=zb(a,c,"$S"+f);a.completedImportChunks.push(e);b.set(d,c);return S(c)}if("bigint"===typeof d)return a=J.get(d),void 0!==a&&K(a.message),"$n"+d.toString(10);throw Error("Type "+typeof d+" is not supported in Client Component props."+G(c,e));}function M(a,b){var c=L;L=null;try{za.run(void 0,a.onPostpone,b)}finally{L=c}}
function O(a,b){var c=L;L=null;try{var e=za.run(void 0,a.onError,b)}finally{L=c}if(null!=e&&"string"!==typeof e)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e||""}function Cb(a,b){jb(a);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function N(a,b){b=b.toString(16)+":P\n";a.completedErrorChunks.push(b)}function P(a,b,c){c={digest:c};b=b.toString(16)+":E"+I(c)+"\n";a.completedErrorChunks.push(b)}var ub={};
function Bb(a,b){if(0===b.status)try{V=b.model;var c=R(a,b,ub,"",b.model);V=c;b.keyPath=null;b.implicitSlot=!1;var e="object"===typeof c&&null!==c?I(c,b.toJSON):I(c),d=b.id.toString(16)+":"+e+"\n";a.completedRegularChunks.push(d);a.abortableTasks.delete(b);b.status=1}catch(h){var f=h===Ia?Ma():h;if("object"===typeof f&&null!==f){if("function"===typeof f.then){var g=b.ping;f.then(g,g);b.thenableState=Oa();return}if(f.$$typeof===B){a.abortableTasks.delete(b);b.status=4;M(a,f.message);N(a,b.id);return}}a.abortableTasks.delete(b);
b.status=4;var k=O(a,f);P(a,b.id,k)}finally{}}function yb(a){var b=ib.current;ib.current=Ta;var c=L;C=L=a;try{var e=a.pingedTasks;a.pingedTasks=[];for(var d=0;d<e.length;d++)Bb(a,e[d]);null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),Cb(a,f)}finally{ib.current=b,C=null,L=c}}
function Q(a,b){l=new Uint8Array(2048);n=0;q=!0;try{for(var c=a.completedImportChunks,e=0;e<c.length;e++)if(a.pendingChunks--,!ea(b,c[e])){a.destination=null;e++;break}c.splice(0,e);var d=a.completedHintChunks;for(e=0;e<d.length;e++)if(!ea(b,d[e])){a.destination=null;e++;break}d.splice(0,e);var f=a.completedRegularChunks;for(e=0;e<f.length;e++)if(a.pendingChunks--,!ea(b,f[e])){a.destination=null;e++;break}f.splice(0,e);var g=a.completedErrorChunks;for(e=0;e<g.length;e++)if(a.pendingChunks--,!ea(b,
g[e])){a.destination=null;e++;break}g.splice(0,e)}finally{a.flushScheduled=!1,l&&0<n&&b.write(l.subarray(0,n)),l=null,n=0,q=!0}"function"===typeof b.flush&&b.flush();0===a.pendingChunks&&(jb(a),b.end())}function Db(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return za.run(a,yb,a)})}function qb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setImmediate(function(){return Q(a,b)})}}
function Eb(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{Q(a,b)}catch(c){O(a,c),Cb(a,c)}}}
function Fb(a,b){try{var c=a.abortableTasks;if(0<c.size){a.pendingChunks++;var e=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===B)M(a,b.message),N(a,e,b);else{var d=void 0===b?Error("The render was aborted by the server without a reason."):b,f=O(a,d);P(a,e,f,d)}c.forEach(function(g){g.status=3;var k=S(e);g=zb(a,g.id,k);a.completedErrorChunks.push(g)});c.clear()}null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),Cb(a,g)}}
function Gb(a,b){var c="",e=a[b];if(e)c=e.name;else{var d=b.lastIndexOf("#");-1!==d&&(c=b.slice(d+1),e=a[b.slice(0,d)]);if(!e)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[e.id,e.chunks,c]}var Hb=new Map;
function Ib(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function Jb(){}
function Kb(a){for(var b=a[1],c=[],e=0;e<b.length;e++){var d=b[e],f=Hb.get(d);if(void 0===f){f=globalThis.__next_chunk_load__(d);c.push(f);var g=Hb.set.bind(Hb,d,null);f.then(g,Jb);Hb.set(d,f)}else null!==f&&c.push(f)}return 4===a.length?0===c.length?Ib(a[0]):Promise.all(c).then(function(){return Ib(a[0])}):0<c.length?Promise.all(c):null}
function W(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Lb(a,b,c,e){this.status=a;this.value=b;this.reason=c;this._response=e}Lb.prototype=Object.create(Promise.prototype);
Lb.prototype.then=function(a,b){switch(this.status){case "resolved_model":Mb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Nb(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}
function Ob(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&Nb(c,b)}}function Pb(a,b,c,e,d,f){var g=Gb(a._bundlerConfig,b);a=Kb(g);if(c)c=Promise.all([c,a]).then(function(k){k=k[0];var h=W(g);return h.bind.apply(h,[null].concat(k))});else if(a)c=Promise.resolve(a).then(function(){return W(g)});else return W(g);c.then(Qb(e,d,f),Rb(e));return null}var X=null,Y=null;
function Mb(a){var b=X,c=Y;X=a;Y=null;try{var e=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=e,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=e)}catch(d){a.status="rejected",a.reason=d}finally{X=b,Y=c}}function Sb(a,b){a._chunks.forEach(function(c){"pending"===c.status&&Ob(c,b)})}
function Z(a,b){var c=a._chunks,e=c.get(b);e||(e=a._formData.get(a._prefix+b),e=null!=e?new Lb("resolved_model",e,null,a):new Lb("pending",null,null,a),c.set(b,e));return e}function Qb(a,b,c){if(Y){var e=Y;e.deps++}else e=Y={deps:1,value:null};return function(d){b[c]=d;e.deps--;0===e.deps&&"blocked"===a.status&&(d=a.value,a.status="fulfilled",a.value=e.value,null!==d&&Nb(d,e.value))}}function Rb(a){return function(b){return Ob(a,b)}}
function Tb(a,b){a=Z(a,b);"resolved_model"===a.status&&Mb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Wb(a,b,c,e){if("$"===e[0])switch(e[1]){case "$":return e.slice(1);case "@":return b=parseInt(e.slice(2),16),Z(a,b);case "S":return Symbol.for(e.slice(2));case "F":return e=parseInt(e.slice(2),16),e=Tb(a,e),Pb(a,e.id,e.bound,X,b,c);case "Q":return b=parseInt(e.slice(2),16),a=Tb(a,b),new Map(a);case "W":return b=parseInt(e.slice(2),16),a=Tb(a,b),new Set(a);case "K":b=e.slice(2);var d=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(d)&&f.append(k.slice(d.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===e?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(e.slice(2)));case "n":return BigInt(e.slice(2));default:e=parseInt(e.slice(1),16);a=Z(a,e);switch(a.status){case "resolved_model":Mb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return e=X,a.then(Qb(e,b,c),Rb(e)),null;default:throw a.reason;}}return e}
function Xb(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,e=new Map,d={_bundlerConfig:a,_prefix:b,_formData:c,_chunks:e,_fromJSON:function(f,g){return"string"===typeof g?Wb(d,this,f,g):g}};return d}
function Yb(a,b,c){a._formData.append(b,c);var e=a._prefix;if(b.startsWith(e)&&(a=a._chunks,b=+b.slice(e.length),(b=a.get(b))&&"pending"===b.status&&(e=b.value,a=b.reason,b.status="resolved_model",b.value=c,null!==e)))switch(Mb(b),b.status){case "fulfilled":Nb(e,b.value);break;case "pending":case "blocked":b.value=e;b.reason=a;break;case "rejected":a&&Nb(a,b.reason)}}function Zb(a){Sb(a,Error("Connection closed."))}
function $b(a,b,c){var e=Gb(a,b);a=Kb(e);return c?Promise.all([c,a]).then(function(d){d=d[0];var f=W(e);return f.bind.apply(f,[null].concat(d))}):a?Promise.resolve(a).then(function(){return W(e)}):Promise.resolve(W(e))}function ac(a,b,c){a=Xb(b,c,a);Zb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}function bc(a,b){return function(){return Eb(b,a)}}exports.createClientModuleProxy=function(a){a=v({},a,!1);return new Proxy(a,pa)};
exports.decodeAction=function(a,b){var c=new FormData,e=null;a.forEach(function(d,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(d="$ACTION_"+f.slice(12)+":",d=ac(a,b,d),e=$b(b,d.id,d.bound)):f.startsWith("$ACTION_ID_")&&(d=f.slice(11),e=$b(b,d,null)):c.append(f,d)});return null===e?null:e.then(function(d){return d.bind(null,c)})};exports.decodeReply=function(a,b){if("string"===typeof a){var c=new FormData;c.append("0",a);a=c}a=Xb(b,"",a);b=Z(a,0);Zb(a);return b};
exports.decodeReplyFromBusboy=function(a,b){var c=Xb(b,""),e=0,d=[];a.on("field",function(f,g){0<e?d.push(f,g):Yb(c,f,g)});a.on("file",function(f,g,k){var h=k.filename,p=k.mimeType;if("base64"===k.encoding.toLowerCase())throw Error("React doesn't accept base64 encoded file uploads because we don't expect form data passed from a browser to ever encode data that way. If that's the wrong assumption, we can easily fix it.");e++;var u=[];g.on("data",function(m){u.push(m)});g.on("end",function(){var m=
new Blob(u,{type:p});c._formData.append(f,m,h);e--;if(0===e){for(m=0;m<d.length;m+=2)Yb(c,d[m],d[m+1]);d.length=0}})});a.on("finish",function(){Zb(c)});a.on("error",function(f){Sb(c,f)});return Z(c,0)};exports.registerClientReference=function(a,b,c){return v(a,b+"#"+c,!1)};exports.registerServerReference=function(a,b,c){return Object.defineProperties(a,{$$typeof:{value:ha},$$id:{value:null===c?b:b+"#"+c,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:la,configurable:!0}})};
exports.renderToPipeableStream=function(a,b,c){var e=mb(a,b,c?c.onError:void 0,c?c.identifierPrefix:void 0,c?c.onPostpone:void 0),d=!1;Db(e);return{pipe:function(f){if(d)throw Error("React currently only supports piping to one writable stream.");d=!0;Eb(e,f);f.on("drain",bc(f,e));return f},abort:function(f){Fb(e,f)}}};

//# sourceMappingURL=react-server-dom-turbopack-server.node.production.min.js.map
