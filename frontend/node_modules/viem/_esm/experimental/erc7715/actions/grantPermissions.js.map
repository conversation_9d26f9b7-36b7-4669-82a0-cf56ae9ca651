{"version": 3, "file": "grantPermissions.js", "sourceRoot": "", "sources": ["../../../../experimental/erc7715/actions/grantPermissions.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAA;AAwCnE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,MAAyB,EACzB,UAAsC;IAEtC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,UAAU,CAAA;IAC3D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CACjC;QACE,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE;YACN,gBAAgB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAS,CAAC;SAClE;KACF,EACD,EAAE,UAAU,EAAE,CAAC,EAAE,CAClB,CAAA;IACD,OAAO,aAAa,CAAC,MAAM,CAA+B,CAAA;AAC5D,CAAC;AAED,SAAS,gBAAgB,CAAC,UAAsC;IAC9D,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAA;IAE3D,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO;QAChC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC;QAClC,CAAC,CAAC,SAAS,CAAA;IAEb,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE;QACnB,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO;YAAE,OAAO,SAAS,CAAA;QAE1C,8BAA8B;QAC9B,IAAI,OAAO,EAAE,IAAI,KAAK,UAAU;YAC9B,OAAO;gBACL,IAAI,EAAE,QAAQ;aACf,CAAA;QAEH,2BAA2B;QAC3B,IAAI,OAAO,EAAE,IAAI,KAAK,OAAO;YAC3B,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE;oBACJ,EAAE,EAAE,OAAO,CAAC,OAAO;iBACpB;aACF,CAAA;QAEH,6BAA6B;QAC7B,OAAO,OAAO,CAAA;IAChB,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO;QACL,MAAM;QACN,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC5C,GAAG,UAAU;YACb,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC3C,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE;oBACjB,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB;wBACnC,OAAO;4BACL,SAAS,EAAE,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;yBAC9C,CAAA;oBACH,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;wBAC7B,OAAO;4BACL,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;yBACtC,CAAA;oBACH,OAAO,MAAM,CAAC,IAAI,CAAA;gBACpB,CAAC,CAAC,EAAE,CAAA;gBAEJ,OAAO;oBACL,IAAI;oBACJ,IAAI,EACF,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;iBACrE,CAAA;YACH,CAAC,CAAC;YACF,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,KAAK;YACtC,IAAI,EACF,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ;gBACjC,CAAC,CAAC,UAAU,CAAC,IAAI;gBACjB,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;SAC7B,CAAC,CAAC;QACH,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KAC9B,CAAA;AACH,CAAC;AAED,SAAS,aAAa,CAAC,MAAwC;IAC7D,OAAO;QACL,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACtD,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAClE,kBAAkB,EAAE,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YACjE,GAAG,UAAU;YACb,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC3C,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE;oBACjB,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB;wBACnC,OAAO;4BACL,SAAS,EAAE,MAAM,CAAE,MAAM,CAAC,IAAY,CAAC,SAAS,CAAC;yBAClD,CAAA;oBACH,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;wBAC7B,OAAO;4BACL,KAAK,EAAE,MAAM,CAAE,MAAM,CAAC,IAAY,CAAC,KAAK,CAAC;yBAC1C,CAAA;oBACH,OAAO,MAAM,CAAC,IAAI,CAAA;gBACpB,CAAC,CAAC,EAAE,CAAA;gBAEJ,OAAO;oBACL,IAAI;oBACJ,IAAI,EAAE,MAAM,CAAC,IAAI;iBAClB,CAAA;YACH,CAAC,CAAC;SACH,CAAC,CAAC;QACH,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;QAC7C,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KAChE,CAAA;AACH,CAAC"}