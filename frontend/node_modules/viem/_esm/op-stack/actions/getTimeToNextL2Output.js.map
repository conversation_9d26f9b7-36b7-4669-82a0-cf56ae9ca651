{"version": 3, "file": "getTimeToNextL2Output.js", "sourceRoot": "", "sources": ["../../../op-stack/actions/getTimeToNextL2Output.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,SAAS,GACV,MAAM,mCAAmC,CAAA;AAC1C,OAAO,EAEL,YAAY,GACb,MAAM,sCAAsC,CAAA;AAU7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAA;AAmC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CAKzC,MAAyC,EACzC,UAAiE;IAEjE,MAAM,EACJ,cAAc,GAAG,GAAG,EACpB,KAAK,GAAG,MAAM,CAAC,KAAK,EACpB,aAAa,EACb,WAAW,GACZ,GAAG,UAAU,CAAA;IAEd,MAAM,qBAAqB,GAAG,CAAC,GAAG,EAAE;QAClC,IAAI,UAAU,CAAC,qBAAqB;YAClC,OAAO,UAAU,CAAC,qBAAqB,CAAA;QACzC,IAAI,KAAK;YAAE,OAAO,WAAY,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAA;QACzE,OAAO,MAAM,CAAC,MAAM,CAAC,WAAY,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;IACxE,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,aAAa,CAAC,GAAG,MAAM,SAAS,CACnE,MAAM,EACN;QACE,YAAY,EAAE,KAAK;QACnB,SAAS,EAAE;YACT;gBACE,GAAG,EAAE,iBAAiB;gBACtB,OAAO,EAAE,qBAAqB;gBAC9B,YAAY,EAAE,mBAAmB;aAClC;YACD;gBACE,GAAG,EAAE,iBAAiB;gBACtB,OAAO,EAAE,qBAAqB;gBAC9B,YAAY,EAAE,eAAe;aAC9B;YACD;gBACE,GAAG,EAAE,iBAAiB;gBACtB,OAAO,EAAE,qBAAqB;gBAC9B,YAAY,EAAE,qBAAqB;aACpC;SACF;KACF,CACF,CAAA;IACD,MAAM,YAAY,GAAG,MAAM,YAAY,CAAC,MAAM,EAAE;QAC9C,GAAG,EAAE,iBAAiB;QACtB,OAAO,EAAE,qBAAqB;QAC9B,YAAY,EAAE,aAAa;QAC3B,IAAI,EAAE,CAAC,iBAAiB,CAAC;KAC1B,CAAC,CAAA;IACF,MAAM,qBAAqB,GAAG,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;IAEnE,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC,CAAA;IAClD,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,CAAA;IAE/D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IAEtB,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE;QACpB,0EAA0E;QAC1E,gEAAgE;QAChE,IAAI,GAAG,GAAG,qBAAqB;YAAE,OAAO,CAAC,CAAA;QAEzC,4EAA4E;QAC5E,gEAAgE;QAChE,IAAI,YAAY,CAAC,aAAa,GAAG,aAAa;YAAE,OAAO,CAAC,CAAA;QAExD,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC,CAAA;QAExE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,qBAAqB,CAAC,GAAG,IAAI,CAAC,CAAA;QAC/D,MAAM,mBAAmB,GACvB,kBAAkB,GAAG,CAAC,OAAO,GAAG,kBAAkB,CAAC,CAAA;QACrD,OAAO,aAAa,GAAG,aAAa;YAClC,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,kBAAkB;gBACpE,mBAAmB,CAAA;IAC3B,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,SAAS,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAA;IAEhE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAA;AACzC,CAAC"}