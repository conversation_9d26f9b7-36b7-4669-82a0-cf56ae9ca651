{"version": 3, "file": "getTimeToNextGame.js", "sourceRoot": "", "sources": ["../../../op-stack/actions/getTimeToNextGame.ts"], "names": [], "mappings": "AAUA,OAAO,EAA0B,QAAQ,EAAE,MAAM,eAAe,CAAA;AAqChE;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAKrC,MAAyC,EACzC,UAA6D;IAE7D,MAAM,EAAE,cAAc,GAAG,GAAG,EAAE,aAAa,EAAE,GAAG,UAAU,CAAA;IAE1D,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,MAAM,EAAE;QACnC,GAAG,UAAU;QACb,aAAa,EAAE,SAAS;QACxB,KAAK,EAAE,EAAE;KACV,CAAC,CAAA;IAEF,MAAM,MAAM,GAAG,KAAK;SACjB,GAAG,CAAC,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE;QAC3C,OAAO,KAAK,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC;YAC/B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC;gBACE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,GAAG,SAAS;gBACtC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,aAAa,GAAG,aAAa;aAC/C,CAAA;IACP,CAAC,CAAC;SACD,MAAM,CAAC,OAAO,CAAC,CAAA;IAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CACvB,MAA6B,CAAC,MAAM,CACnC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EACjC,CAAC,CACF,GAAG,MAAM,CAAC,MAAM,CAClB,CAAA;IACD,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAC5B,MAA6B,CAAC,MAAM,CACnC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EACpC,CAAC,CACF,GAAG,MAAM,CAAC,MAAM,CAClB,CAAA;IAED,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;IAC3B,MAAM,mBAAmB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;IAE/D,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,CAAA;IAE/D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IAEtB,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE;QACpB,6EAA6E;QAC7E,mEAAmE;QACnE,IAAI,GAAG,GAAG,mBAAmB;YAAE,OAAO,CAAC,CAAA;QAEvC,yFAAyF;QACzF,mEAAmE;QACnE,IAAI,UAAU,CAAC,aAAa,GAAG,aAAa;YAAE,OAAO,CAAC,CAAA;QAEtD,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,CAAA;QAEtE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,mBAAmB,CAAC,GAAG,IAAI,CAAC,CAAA;QAC7D,MAAM,mBAAmB,GACvB,kBAAkB,GAAG,CAAC,OAAO,GAAG,kBAAkB,CAAC,CAAA;QACrD,OAAO,aAAa,GAAG,aAAa;YAClC,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,kBAAkB;gBACpE,mBAAmB,CAAA;IAC3B,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,SAAS,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAA;IAEhE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAA;AACzC,CAAC"}