{"version": 3, "file": "getGames.js", "sourceRoot": "", "sources": ["../../../op-stack/actions/getGames.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,YAAY,GACb,MAAM,sCAAsC,CAAA;AAU7C,OAAO,EAAE,mBAAmB,EAAE,MAAM,wCAAwC,CAAA;AAC5E,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,MAAM,YAAY,CAAA;AA4B9D;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,CAAC,KAAK,UAAU,QAAQ,CAK5B,MAAyC,EACzC,UAAoD;IAEpD,MAAM,EACJ,KAAK,GAAG,MAAM,CAAC,KAAK,EACpB,aAAa,EACb,KAAK,GAAG,GAAG,EACX,WAAW,GACZ,GAAG,UAAU,CAAA;IAEd,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE;QAC1B,IAAI,UAAU,CAAC,aAAa;YAAE,OAAO,UAAU,CAAC,aAAa,CAAA;QAC7D,IAAI,KAAK;YAAE,OAAO,WAAY,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAA;QACjE,OAAO,MAAM,CAAC,MAAM,CAAC,WAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;IAChE,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,yBAAyB,GAAG,CAAC,GAAG,EAAE;QACtC,IAAI,UAAU,CAAC,yBAAyB;YACtC,OAAO,UAAU,CAAC,yBAAyB,CAAA;QAC7C,IAAI,KAAK;YACP,OAAO,WAAY,CAAC,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAA;QACpE,OAAO,MAAM,CAAC,MAAM,CAAC,WAAY,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;IAC5E,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAC9C,YAAY,CAAC,MAAM,EAAE;YACnB,GAAG,EAAE,qBAAqB;YAC1B,YAAY,EAAE,WAAW;YACzB,IAAI,EAAE,EAAE;YACR,OAAO,EAAE,yBAAyB;SACnC,CAAC;QACF,YAAY,CAAC,MAAM,EAAE;YACnB,GAAG,EAAE,UAAU;YACf,YAAY,EAAE,mBAAmB;YACjC,OAAO,EAAE,aAAa;SACvB,CAAC;KACH,CAAC,CAAA;IAEF,MAAM,KAAK,GACT,CAAC,MAAM,YAAY,CAAC,MAAM,EAAE;QAC1B,GAAG,EAAE,qBAAqB;QAC1B,YAAY,EAAE,iBAAiB;QAC/B,OAAO,EAAE,yBAAyB;QAClC,IAAI,EAAE;YACJ,QAAQ;YACR,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;SAC3C;KACF,CAAC,CACH;SACE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACZ,MAAM,CAAC,WAAW,CAAC,GAAG,mBAAmB,CACvC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EACrB,IAAI,CAAC,SAAS,CACf,CAAA;QACD,OAAO,CAAC,aAAa,IAAI,WAAW,GAAG,aAAa;YAClD,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE;YACzC,CAAC,CAAC,IAAI,CAAA;IACV,CAAC,CAAC;SACD,MAAM,CAAC,OAAO,CAAuB,CAAA;IAExC,OAAO,KAAK,CAAA;AACd,CAAC"}