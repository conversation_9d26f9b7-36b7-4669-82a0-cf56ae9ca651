{"version": 3, "file": "getL2HashFromPriorityOp.js", "sourceRoot": "", "sources": ["../../../../zksync/utils/bridge/getL2HashFromPriorityOp.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAA;AACxE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAA;AACnD,OAAO,EACL,yBAAyB,GAE1B,MAAM,wBAAwB,CAAA;AAI/B;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAM,UAAU,uBAAuB,CACrC,OAA2B,EAC3B,MAAe;IAEf,IAAI,IAAI,GAAgB,IAAI,CAAA;IAC5B,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;YAAE,SAAQ;QAClD,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,cAAc,CAAC;gBACtC,GAAG,EAAE,SAAS;gBACd,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB,CAAC,CAAA;YACF,IAAI,gBAAgB,IAAK,gBAAgB,CAAC,IAAY,CAAC,MAAM,KAAK,IAAI;gBACpE,IAAI,GAAI,gBAAgB,CAAC,IAAY,CAAC,MAAM,CAAA;QAChD,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC,CAAA,CAAC;IACjB,CAAC;IAED,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,yBAAyB,EAAE,CAAA;IACvC,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC"}