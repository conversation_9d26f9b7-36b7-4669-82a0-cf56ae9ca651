{"version": 3, "file": "getL2TokenAddress.js", "sourceRoot": "", "sources": ["../../../zksync/actions/getL2TokenAddress.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,sCAAsC,CAAA;AAKnE,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAA;AACrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAA;AACxD,OAAO,EACL,qBAAqB,EACrB,kBAAkB,EAClB,gBAAgB,GACjB,MAAM,yBAAyB,CAAA;AAChC,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAA;AAClE,OAAO,EAAE,yBAAyB,EAAE,MAAM,gCAAgC,CAAA;AAW1E;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAIrC,MAAyC,EACzC,UAAuC;IAEvC,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,UAAU,CAAA;IACzC,IAAI,cAAc,CAAC,KAAK,EAAE,gBAAgB,CAAC;QAAE,KAAK,GAAG,qBAAqB,CAAA;IAE1E,MAAM,SAAS,GAAG,MAAM,qBAAqB,CAAC,MAAM,CAAC,CAAA;IACrD,IAAI,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC;QAAE,OAAO,kBAAkB,CAAA;IAE/D,aAAa,KAAK,CAAC,MAAM,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAA;IAEpE,OAAO,MAAM,YAAY,CAAC,MAAM,EAAE;QAChC,OAAO,EAAE,aAAa;QACtB,GAAG,EAAE,iBAAiB;QACtB,YAAY,EAAE,gBAAgB;QAC9B,IAAI,EAAE,CAAC,KAAK,CAAC;KACd,CAAC,CAAA;AACJ,CAAC"}