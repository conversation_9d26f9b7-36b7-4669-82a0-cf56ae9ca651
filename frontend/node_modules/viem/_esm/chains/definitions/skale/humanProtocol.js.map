{"version": 3, "file": "humanProtocol.js", "sourceRoot": "", "sources": ["../../../../chains/definitions/skale/humanProtocol.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAA;AAEjE,MAAM,CAAC,MAAM,kBAAkB,GAAG,aAAa,CAAC,WAAW,CAAC;IAC1D,EAAE,EAAE,aAAa;IACjB,IAAI,EAAE,wBAAwB;IAC9B,cAAc,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;IAChE,OAAO,EAAE;QACP,OAAO,EAAE;YACP,IAAI,EAAE,CAAC,+CAA+C,CAAC;YACvD,SAAS,EAAE,CAAC,gDAAgD,CAAC;SAC9D;KACF;IACD,cAAc,EAAE;QACd,OAAO,EAAE;YACP,IAAI,EAAE,gBAAgB;YACtB,GAAG,EAAE,qDAAqD;SAC3D;KACF;IACD,SAAS,EAAE,EAAE;CACd,CAAC,CAAA"}