import type { MutateOptions } from '@tanstack/query-core';
import { type ConnectErrorType, type ConnectParameters, type ConnectReturnType } from '../actions/connect.js';
import type { Config, Connector } from '../createConfig.js';
import type { CreateConnectorFn } from '../connectors/createConnector.js';
import type { Compute } from '../types/utils.js';
export declare function connectMutationOptions<config extends Config>(config: config): {
    readonly mutationFn: (variables: ConnectVariables<config, CreateConnectorFn | Connector>) => Promise<ConnectReturnType<config>>;
    readonly mutationKey: readonly ["connect"];
};
export type ConnectData<config extends Config> = ConnectReturnType<config>;
export type ConnectVariables<config extends Config, connector extends Connector | CreateConnectorFn> = ConnectParameters<config, connector>;
export type ConnectMutate<config extends Config, context = unknown> = <connector extends config['connectors'][number] | Connector | CreateConnectorFn>(variables: ConnectVariables<config, connector>, options?: Compute<MutateOptions<ConnectData<config>, ConnectErrorType, Compute<ConnectVariables<config, connector>>, context>> | undefined) => void;
export type ConnectMutateAsync<config extends Config, context = unknown> = <connector extends config['connectors'][number] | Connector | CreateConnectorFn>(variables: ConnectVariables<config, connector>, options?: Compute<MutateOptions<ConnectData<config>, ConnectErrorType, Compute<ConnectVariables<config, connector>>, context>> | undefined) => Promise<ConnectData<config>>;
//# sourceMappingURL=connect.d.ts.map