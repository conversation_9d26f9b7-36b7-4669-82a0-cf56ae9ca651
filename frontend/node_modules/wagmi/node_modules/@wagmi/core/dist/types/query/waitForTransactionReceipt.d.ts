import { type WaitForTransactionReceiptParameters, type WaitForTransactionReceiptReturnType } from '../actions/waitForTransactionReceipt.js';
import type { Config } from '../createConfig.js';
import type { Scope<PERSON>eyParameter } from '../types/properties.js';
import type { Compute, ExactPartial } from '../types/utils.js';
export type WaitForTransactionReceiptOptions<config extends Config, chainId extends config['chains'][number]['id']> = Compute<ExactPartial<WaitForTransactionReceiptParameters<config, chainId>> & ScopeKeyParameter>;
export declare function waitForTransactionReceiptQueryOptions<config extends Config, chainId extends config['chains'][number]['id']>(config: config, options?: WaitForTransactionReceiptOptions<config, chainId>): {
    readonly queryFn: ({ queryKey }: {
        queryKey: readonly ["waitForTransactionReceipt", {
            confirmations?: number | undefined | undefined;
            hash?: `0x${string}` | undefined;
            pollingInterval?: number | undefined | undefined;
            retryCount?: number | undefined;
            retryDelay?: (number | ((config: {
                count: number;
                error: Error;
            }) => number) | undefined) | undefined;
            timeout?: number | undefined | undefined;
            chainId?: config["chains"][number]["id"] | (chainId extends config["chains"][number]["id"] ? chainId : undefined) | undefined;
            scopeKey?: string | undefined | undefined;
        }];
        signal: AbortSignal;
        meta: import("@tanstack/query-core").QueryMeta | undefined;
        pageParam?: unknown;
        direction?: unknown;
    }) => Promise<(import("../types/chain.js").SelectChains<config, chainId> extends infer T_2 extends readonly import("viem").Chain[] ? { [key_1 in keyof T_2]: import("viem").ExtractChainFormatterReturnType<import("../types/utils.js").IsNarrowable<import("../types/chain.js").SelectChains<config, chainId>[key_1], import("viem").Chain> extends true ? import("../types/chain.js").SelectChains<config, chainId>[key_1] : undefined, "transactionReceipt", import("viem").TransactionReceipt> & {
        chainId: import("../types/chain.js").SelectChains<config, chainId>[key_1]["id"];
    }; } : never)[number] extends infer T ? { [key in keyof T]: (import("../types/chain.js").SelectChains<config, chainId> extends infer T_1 extends readonly import("viem").Chain[] ? { [key_1 in keyof T_1]: import("viem").ExtractChainFormatterReturnType<import("../types/utils.js").IsNarrowable<import("../types/chain.js").SelectChains<config, chainId>[key_1], import("viem").Chain> extends true ? import("../types/chain.js").SelectChains<config, chainId>[key_1] : undefined, "transactionReceipt", import("viem").TransactionReceipt> & {
        chainId: import("../types/chain.js").SelectChains<config, chainId>[key_1]["id"];
    }; } : never)[number][key]; } : never>;
    readonly queryKey: readonly ["waitForTransactionReceipt", {
        confirmations?: number | undefined | undefined;
        hash?: `0x${string}` | undefined;
        pollingInterval?: number | undefined | undefined;
        retryCount?: number | undefined;
        retryDelay?: (number | ((config: {
            count: number;
            error: Error;
        }) => number) | undefined) | undefined;
        timeout?: number | undefined | undefined;
        chainId?: config["chains"][number]["id"] | (config["chains"][number]["id"] extends infer T ? T extends config["chains"][number]["id"] ? T extends config["chains"][number]["id"] ? T : undefined : never : never) | undefined;
        scopeKey?: string | undefined | undefined;
    }];
};
export type WaitForTransactionReceiptQueryFnData<config extends Config, chainId extends config['chains'][number]['id']> = WaitForTransactionReceiptReturnType<config, chainId>;
export type WaitForTransactionReceiptData<config extends Config, chainId extends config['chains'][number]['id']> = WaitForTransactionReceiptQueryFnData<config, chainId>;
export declare function waitForTransactionReceiptQueryKey<config extends Config, chainId extends config['chains'][number]['id']>(options?: WaitForTransactionReceiptOptions<config, chainId>): readonly ["waitForTransactionReceipt", {
    confirmations?: number | undefined | undefined;
    hash?: `0x${string}` | undefined;
    pollingInterval?: number | undefined | undefined;
    retryCount?: number | undefined;
    retryDelay?: number | ((config: {
        count: number;
        error: Error;
    }) => number) | undefined;
    timeout?: number | undefined | undefined;
    chainId?: config["chains"][number]["id"] | (chainId extends config["chains"][number]["id"] ? chainId : undefined) | undefined;
    scopeKey?: string | undefined | undefined;
}];
export type WaitForTransactionReceiptQueryKey<config extends Config, chainId extends config['chains'][number]['id']> = ReturnType<typeof waitForTransactionReceiptQueryKey<config, chainId>>;
//# sourceMappingURL=waitForTransactionReceipt.d.ts.map