{"version": 3, "file": "createConnector.d.ts", "sourceRoot": "", "sources": ["../../../src/connectors/createConnector.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,yBAAyB,EACzB,OAAO,EACP,KAAK,EACL,MAAM,EACN,mBAAmB,EACnB,eAAe,EAChB,MAAM,MAAM,CAAA;AAEb,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAA;AACnD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAA;AAClD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAA;AAClD,OAAO,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAA;AAE1E,MAAM,MAAM,iBAAiB,GAAG;IAC9B,MAAM,EAAE;QACN,QAAQ,CAAC,EAAE,SAAS,OAAO,EAAE,GAAG,SAAS,CAAA;QACzC,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC7B,CAAA;IACD,OAAO,EAAE;QAAE,QAAQ,EAAE,SAAS,OAAO,EAAE,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE,CAAA;IAC1D,UAAU,EAAE,KAAK,CAAA;IACjB,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,CAAA;IACvB,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KAAE,CAAA;CACtD,CAAA;AAED,MAAM,MAAM,iBAAiB,CAC3B,QAAQ,GAAG,OAAO,EAClB,UAAU,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACpE,WAAW,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IACnE,CAAC,MAAM,EAAE;IACX,MAAM,EAAE,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,CAAA;IACpC,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAA;IACnC,OAAO,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,GAAG,SAAS,CAAA;IAC1D,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,SAAS,CAAA;CACnD,KAAK,OAAO,CACX;IACE,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAClC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAA;IACnB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,CAAA;IACtD,kBAAkB;IAClB,QAAQ,CAAC,kBAAkB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACjD,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IAErB,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IACvB,OAAO,CACL,UAAU,CAAC,EACP;QAAE,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,cAAc,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KAAE,GACtE,SAAS,GACZ,OAAO,CAAC;QACT,QAAQ,EAAE,SAAS,OAAO,EAAE,CAAA;QAC5B,OAAO,EAAE,MAAM,CAAA;KAChB,CAAC,CAAA;IACF,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3B,WAAW,IAAI,OAAO,CAAC,SAAS,OAAO,EAAE,CAAC,CAAA;IAC1C,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC,CAAA;IAC7B,WAAW,CACT,UAAU,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,SAAS,GACxD,OAAO,CAAC,QAAQ,CAAC,CAAA;IACpB,SAAS,CAAC,CACR,UAAU,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,SAAS,GACxD,OAAO,CAAC,MAAM,CAAC,CAAA;IAClB,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC,CAAA;IAChC,WAAW,CAAC,CACV,UAAU,EAAE,OAAO,CAAC;QAClB,yBAAyB,CAAC,EACtB,YAAY,CAAC,UAAU,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC,GAC9D,SAAS,CAAA;QACb,OAAO,EAAE,MAAM,CAAA;KAChB,CAAC,GACD,OAAO,CAAC,KAAK,CAAC,CAAA;IAEjB,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;IAC3C,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,CAAA;IACrC,SAAS,CAAC,CAAC,WAAW,EAAE,mBAAmB,GAAG,IAAI,CAAA;IAClD,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,IAAI,CAAA;IAC7C,SAAS,CAAC,CAAC,OAAO,EAAE,eAAe,GAAG,IAAI,CAAA;CAC3C,GAAG,UAAU,CACf,CAAA;AAED,wBAAgB,eAAe,CAC7B,QAAQ,EACR,UAAU,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACpE,WAAW,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAErE,iBAAiB,SAAS,iBAAiB,CACzC,QAAQ,EACR,UAAU,EACV,WAAW,CACZ,GAAG,iBAAiB,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,EACxD,iBAAiB,EAAE,iBAAiB,qBAErC"}