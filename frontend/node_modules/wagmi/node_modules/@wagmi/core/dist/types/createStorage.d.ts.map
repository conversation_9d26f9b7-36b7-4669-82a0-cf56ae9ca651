{"version": 3, "file": "createStorage.d.ts", "sourceRoot": "", "sources": ["../../src/createStorage.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAA;AACzD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAA;AAK/C,MAAM,MAAM,cAAc,GAAG;IAC3B,iBAAiB,EAAE,MAAM,CAAA;IACzB,KAAK,EAAE,gBAAgB,CAAA;CACxB,CAAA;AAED,MAAM,MAAM,OAAO,CACjB,OAAO,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAEjE,cAAc,SAAS,cAAc,GAAG,cAAc,GAAG,OAAO,IAC9D;IACF,GAAG,EAAE,MAAM,CAAA;IACX,OAAO,CACL,GAAG,SAAS,MAAM,cAAc,EAChC,KAAK,SAAS,cAAc,CAAC,GAAG,CAAC,EACjC,YAAY,SAAS,KAAK,GAAG,IAAI,GAAG,SAAS,EAE7C,GAAG,EAAE,GAAG,EACR,YAAY,CAAC,EAAE,YAAY,GAAG,SAAS,GAErC,CAAC,YAAY,SAAS,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,GAClD,OAAO,CAAC,YAAY,SAAS,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,CAAA;IAC7D,OAAO,CACL,GAAG,SAAS,MAAM,cAAc,EAChC,KAAK,SAAS,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,EACxC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC/C,UAAU,CAAC,GAAG,EAAE,MAAM,cAAc,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;CAC5D,CAAA;AAED,MAAM,MAAM,WAAW,GAAG;IACxB,OAAO,CACL,GAAG,EAAE,MAAM,GACV,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IACjE,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACzD,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;CAC9C,CAAA;AAED,MAAM,MAAM,uBAAuB,GAAG;IACpC,WAAW,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,OAAO,CAAC,GAAG,SAAS,CAAA;IACnE,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACxB,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,GAAG,GAAG,KAAK,MAAM,CAAC,GAAG,SAAS,CAAA;IAC7D,OAAO,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,CAAA;CAC3C,CAAA;AAED,wBAAgB,aAAa,CAC3B,OAAO,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACjE,cAAc,SAAS,cAAc,GAAG,cAAc,GAAG,OAAO,EAChE,UAAU,EAAE,uBAAuB,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CA+BvE;AAED,eAAO,MAAM,WAAW;;;;CAID,CAAA;AAEvB,wBAAgB,iBAAiB;;;;EAoBhC"}