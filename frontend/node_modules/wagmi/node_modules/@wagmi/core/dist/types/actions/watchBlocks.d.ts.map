{"version": 3, "file": "watchBlocks.d.ts", "sourceRoot": "", "sources": ["../../../src/actions/watchBlocks.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,KAAK,qBAAqB,IAAI,0BAA0B,EACxD,KAAK,qBAAqB,IAAI,0BAA0B,EAEzD,MAAM,cAAc,CAAA;AAErB,OAAO,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,kBAAkB,EAAE,MAAM,MAAM,CAAA;AAC1E,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AACrD,OAAO,KAAK,EACV,gBAAgB,EAChB,2BAA2B,EAC5B,MAAM,wBAAwB,CAAA;AAC/B,OAAO,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAGnE,MAAM,MAAM,qBAAqB,CAC/B,mBAAmB,SAAS,OAAO,GAAG,KAAK,EAC3C,QAAQ,SAAS,QAAQ,GAAG,QAAQ,EACpC,MAAM,SAAS,MAAM,GAAG,MAAM,EAC9B,OAAO,SACL,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAEjE,MAAM,SAAS,SAAS,KAAK,EAAE,GAAG,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,IAC7D;KACD,GAAG,IAAI,MAAM,MAAM,GAAG,YAAY,CACjC,0BAA0B,CACxB,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,MAAM,SAAS,SAC1E,SAAS,GACP,SAAS,SAAS,SAAS,GACzB,kBAAkB,GAClB,SAAS,GACX,kBAAkB,EACtB,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,SAAS,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,EACvE,mBAAmB,EACnB,QAAQ,CACT,GACC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,GACjC,2BAA2B,CAC9B;CACF,CAAC,MAAM,CAAC,CAAA;AAET,MAAM,MAAM,qBAAqB,GAAG,0BAA0B,CAAA;AAG9D,gDAAgD;AAChD,wBAAgB,WAAW,CACzB,MAAM,SAAS,MAAM,EACrB,OAAO,SACL,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EACjE,mBAAmB,SAAS,OAAO,GAAG,KAAK,EAC3C,QAAQ,SAAS,QAAQ,GAAG,QAAQ,EAEpC,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,qBAAqB,CAC/B,mBAAmB,EACnB,QAAQ,EACR,MAAM,EACN,OAAO,CACR,GACA,qBAAqB,CA6BvB"}