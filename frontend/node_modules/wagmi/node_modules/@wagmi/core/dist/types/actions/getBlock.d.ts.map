{"version": 3, "file": "getBlock.d.ts", "sourceRoot": "", "sources": ["../../../src/actions/getBlock.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,MAAM,CAAA;AAC3C,OAAO,EACL,KAAK,iBAAiB,IAAI,sBAAsB,EAChD,KAAK,kBAAkB,IAAI,uBAAuB,EAClD,KAAK,kBAAkB,IAAI,uBAAuB,EAEnD,MAAM,cAAc,CAAA;AAErB,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AACrD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAA;AAC9D,OAAO,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAG9D,MAAM,MAAM,kBAAkB,CAC5B,mBAAmB,SAAS,OAAO,GAAG,KAAK,EAC3C,QAAQ,SAAS,QAAQ,GAAG,QAAQ,EACpC,MAAM,SAAS,MAAM,GAAG,MAAM,EAC9B,OAAO,SACL,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAC/D,OAAO,CACT,uBAAuB,CAAC,mBAAmB,EAAE,QAAQ,CAAC,GACpD,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CACpC,CAAA;AAED,MAAM,MAAM,kBAAkB,CAC5B,mBAAmB,SAAS,OAAO,GAAG,KAAK,EAC3C,QAAQ,SAAS,QAAQ,GAAG,QAAQ,EACpC,MAAM,SAAS,MAAM,GAAG,MAAM,EAC9B,OAAO,SACL,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAEjE,MAAM,SAAS,SAAS,KAAK,EAAE,GAAG,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,IAC7D,OAAO,CACT;KACG,GAAG,IAAI,MAAM,MAAM,GAAG,uBAAuB,CAC5C,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,SAAS,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,EACvE,mBAAmB,EACnB,QAAQ,CACT,GAAG;QAAE,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;KAAE;CACnC,CAAC,MAAM,CAAC,CACV,CAAA;AAED,MAAM,MAAM,iBAAiB,GAAG,sBAAsB,CAAA;AAEtD,6CAA6C;AAC7C,wBAAsB,QAAQ,CAC5B,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAC9C,mBAAmB,SAAS,OAAO,GAAG,KAAK,EAC3C,QAAQ,SAAS,QAAQ,GAAG,QAAQ,EAEpC,MAAM,EAAE,MAAM,EACd,UAAU,GAAE,kBAAkB,CAC5B,mBAAmB,EACnB,QAAQ,EACR,MAAM,EACN,OAAO,CACH,GACL,OAAO,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAc7E"}