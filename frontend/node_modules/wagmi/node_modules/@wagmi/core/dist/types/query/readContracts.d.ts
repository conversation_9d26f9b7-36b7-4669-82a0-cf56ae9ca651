import type { MulticallParameters as viem_MulticallParameters } from 'viem';
import { type ReadContractsReturnType } from '../actions/readContracts.js';
import type { Config } from '../createConfig.js';
import type { ChainIdParameter } from '../types/properties.js';
import type { ScopeKeyParameter } from '../types/properties.js';
import type { ExactPartial } from '../types/utils.js';
export type ReadContractsOptions<contracts extends readonly unknown[], allowFailure extends boolean, config extends Config> = ExactPartial<viem_MulticallParameters<contracts, allowFailure, {
    optional: true;
    properties: ChainIdParameter<config>;
}>> & ScopeKeyParameter;
export declare function readContractsQueryOptions<config extends Config, const contracts extends readonly unknown[], allowFailure extends boolean = true>(config: config, options?: ReadContractsOptions<contracts, allowFailure, config> & ChainIdParameter<config>): {
    readonly queryFn: ({ queryKey }: {
        queryKey: readonly ["readContracts", {
            contracts: {
                chainId: number;
                functionName: string;
                args?: readonly unknown[] | undefined;
                address: import("viem").Address;
            }[];
            blockNumber?: bigint | undefined | undefined;
            stateOverride?: import("viem").StateOverride | undefined;
            blockTag?: import("viem").BlockTag | undefined;
            allowFailure?: boolean | allowFailure | undefined;
            batchSize?: number | undefined | undefined;
            multicallAddress?: import("viem").Address | undefined;
            scopeKey?: string | undefined;
            chainId?: config["chains"][number]["id"] | (config["chains"][number]["id"] extends infer T ? T extends config["chains"][number]["id"] ? T extends config["chains"][number]["id"] ? T : undefined : never : never) | undefined;
        }];
        signal: AbortSignal;
        meta: import("@tanstack/query-core").QueryMeta | undefined;
        pageParam?: unknown;
        direction?: unknown;
    }) => Promise<ReadContractsReturnType<contracts, allowFailure>>;
    readonly queryKey: readonly ["readContracts", {
        contracts: {
            chainId: number;
            functionName: string;
            args?: readonly unknown[] | undefined;
            address: import("viem").Address;
        }[];
        blockNumber?: bigint | undefined | undefined;
        stateOverride?: import("viem").StateOverride | undefined;
        blockTag?: import("viem").BlockTag | undefined;
        allowFailure?: boolean | allowFailure | undefined;
        batchSize?: number | undefined | undefined;
        multicallAddress?: import("viem").Address | undefined;
        scopeKey?: string | undefined;
        chainId?: config["chains"][number]["id"] | (config["chains"][number]["id"] extends infer T ? T extends config["chains"][number]["id"] ? T extends config["chains"][number]["id"] ? T : undefined : never : never) | undefined;
    }];
};
export type ReadContractsQueryFnData<contracts extends readonly unknown[], allowFailure extends boolean> = ReadContractsReturnType<contracts, allowFailure>;
export type ReadContractsData<contracts extends readonly unknown[], allowFailure extends boolean> = ReadContractsQueryFnData<contracts, allowFailure>;
export declare function readContractsQueryKey<config extends Config, const contracts extends readonly unknown[], allowFailure extends boolean>(options?: ReadContractsOptions<contracts, allowFailure, config> & ChainIdParameter<config>): readonly ["readContracts", {
    contracts: {
        chainId: number;
        functionName: string;
        args?: readonly unknown[] | undefined;
        address: import("viem").Address;
    }[];
    blockNumber?: bigint | undefined | undefined;
    stateOverride?: import("viem").StateOverride | undefined;
    blockTag?: import("viem").BlockTag | undefined;
    allowFailure?: boolean | allowFailure | undefined;
    batchSize?: number | undefined | undefined;
    multicallAddress?: `0x${string}` | undefined;
    scopeKey?: string | undefined;
    chainId?: config["chains"][number]["id"] | (config["chains"][number]["id"] extends infer T ? T extends config["chains"][number]["id"] ? T extends config["chains"][number]["id"] ? T : undefined : never : never) | undefined;
}];
export type ReadContractsQueryKey<contracts extends readonly unknown[], allowFailure extends boolean, config extends Config> = ReturnType<typeof readContractsQueryKey<config, contracts, allowFailure>>;
//# sourceMappingURL=readContracts.d.ts.map