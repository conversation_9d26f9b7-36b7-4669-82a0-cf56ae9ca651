{"version": 3, "file": "getBlock.d.ts", "sourceRoot": "", "sources": ["../../../src/query/getBlock.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAA;AAEpC,OAAO,EAEL,KAAK,kBAAkB,EACvB,KAAK,kBAAkB,EAExB,MAAM,wBAAwB,CAAA;AAC/B,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAA;AAC/D,OAAO,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAG9D,MAAM,MAAM,eAAe,CACzB,mBAAmB,SAAS,OAAO,EACnC,QAAQ,SAAS,QAAQ,EACzB,MAAM,SAAS,MAAM,EACrB,OAAO,SACL,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAC/D,OAAO,CACT,YAAY,CACV,kBAAkB,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CACnE,GACC,iBAAiB,CACpB,CAAA;AAED,wBAAgB,oBAAoB,CAClC,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAC9C,mBAAmB,SAAS,OAAO,EACnC,QAAQ,SAAS,QAAQ,EAEzB,MAAM,EAAE,MAAM,EACd,OAAO,GAAE,eAAe,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAM;;;;;;;;;EAe9E;AAED,MAAM,MAAM,mBAAmB,CAC7B,mBAAmB,SAAS,OAAO,EACnC,QAAQ,SAAS,QAAQ,EACzB,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAC5C,kBAAkB,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;AAEtE,MAAM,MAAM,YAAY,CACtB,mBAAmB,SAAS,OAAO,EACnC,QAAQ,SAAS,QAAQ,EACzB,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAC5C,mBAAmB,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;AAEvE,wBAAgB,gBAAgB,CAC9B,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAC9C,mBAAmB,SAAS,OAAO,GAAG,KAAK,EAC3C,QAAQ,SAAS,QAAQ,GAAG,QAAQ,EAEpC,OAAO,GAAE,eAAe,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAM,uFAG9E;AAED,MAAM,MAAM,gBAAgB,CAC1B,mBAAmB,SAAS,OAAO,EACnC,QAAQ,SAAS,QAAQ,EACzB,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAC5C,UAAU,CACZ,OAAO,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,QAAQ,CAAC,CACxE,CAAA"}