{"version": 3, "file": "createConfig.d.ts", "sourceRoot": "", "sources": ["../../src/createConfig.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,KAAK,qBAAqB,EAC1B,KAAK,KAAK,IAAI,SAAS,EAExB,MAAM,MAAM,CAAA;AACb,OAAO,EACL,KAAK,OAAO,EACZ,KAAK,KAAK,EACV,KAAK,MAAM,EACX,KAAK,gBAAgB,EAErB,KAAK,YAAY,IAAI,iBAAiB,EACtC,KAAK,SAAS,IAAI,cAAc,EACjC,MAAM,MAAM,CAAA;AAEb,OAAO,EAAE,KAAK,MAAM,EAAE,KAAK,QAAQ,EAAe,MAAM,iBAAiB,CAAA;AAEzE,OAAO,KAAK,EACV,iBAAiB,EACjB,iBAAiB,EAClB,MAAM,iCAAiC,CAAA;AAExC,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,SAAS,EAAiB,MAAM,oBAAoB,CAAA;AAChF,OAAO,EACL,KAAK,OAAO,EAGb,MAAM,oBAAoB,CAAA;AAE3B,OAAO,KAAK,EACV,OAAO,EACP,YAAY,EACZ,SAAS,EACT,KAAK,EAEN,MAAM,kBAAkB,CAAA;AAIzB,wBAAgB,YAAY,CAC1B,KAAK,CAAC,MAAM,SAAS,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,EACjD,UAAU,SAAS,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,EAC1D,KAAK,CAAC,YAAY,SAAS,SAAS,iBAAiB,EAAE,EAEvD,UAAU,EAAE,sBAAsB,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC,GACnE,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC,CA0b1C;AAMD,MAAM,MAAM,sBAAsB,CAChC,MAAM,SAAS,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,EAC1E,UAAU,SAAS,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,GAAG,MAAM,CACjE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EACpB,SAAS,CACV,EACD,YAAY,SACV,SAAS,iBAAiB,EAAE,GAAG,SAAS,iBAAiB,EAAE,IAC3D,OAAO,CACT;IACE,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,CAAC,EAAE,YAAY,GAAG,SAAS,CAAA;IACrC,8BAA8B,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACpD,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAA;IACpC,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACzB,kBAAkB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CACzC,GAAG,KAAK,CACL,CAAC;IAAE,UAAU,EAAE,UAAU,CAAA;CAAE,GAAG;KAC3B,GAAG,IAAI,MAAM,YAAY,CAAC,CAAC,EACxB,YAAY,CAAC,GAAG,CAAC,GACjB;SAAG,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,SAAS;KAAE,GAC/D,SAAS;CACd,CAAC,GACF;IACE,MAAM,CAAC,UAAU,EAAE;QAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;KAAE,GAAG,MAAM,CACnD,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EAChC,MAAM,CAAC,MAAM,CAAC,CACf,CAAA;CACF,CACJ,CACF,CAAA;AAED,MAAM,MAAM,MAAM,CAChB,MAAM,SAAS,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,EAC1E,UAAU,SAAS,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,GAAG,MAAM,CACjE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EACpB,SAAS,CACV,EACD,YAAY,SACV,SAAS,iBAAiB,EAAE,GAAG,SAAS,iBAAiB,EAAE,IAC3D;IACF,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAA;IACvB,QAAQ,CAAC,UAAU,EAAE,SAAS,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAA;IAC/D,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAA;IAEhC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;IAC7B,QAAQ,CAAC,OAAO,SAAS,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EAC5D,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC,GAClE,IAAI,CAAA;IACP,SAAS,CAAC,KAAK,EACb,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,KAAK,EACzC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,KAAK,IAAI,EACtD,OAAO,CAAC,EACJ;QACE,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QACrC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,KAAK,OAAO,CAAC,GAAG,SAAS,CAAA;KAC3D,GACD,SAAS,GACZ,MAAM,IAAI,CAAA;IAEb,SAAS,CAAC,OAAO,SAAS,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,EAAE;QAC3D,OAAO,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,SAAS,CAAA;KACrD,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;QAAE,EAAE,EAAE,OAAO,CAAA;KAAE,CAAC,CAAC,CAAA;IAEzE;;;OAGG;IACH,SAAS,EAAE,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;CACxC,CAAA;AAED,KAAK,QAAQ,CACX,MAAM,SAAS,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,EAC1E,UAAU,SAAS,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,GAAG,MAAM,CACjE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EACpB,SAAS,CACV,IACC;IACF,QAAQ,CAAC,IAAI,EAAE,SAAS,GAAG,SAAS,CAAA;IACpC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;IACjE,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAA;IACrB,QAAQ,CAAC,kBAAkB,EAAE,OAAO,CAAA;IACpC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAA;IAE/B,MAAM,EAAE;QACN,QAAQ,CACN,KAAK,EACD,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,GAC5B,CAAC,CACC,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,KAChC,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,GACrC,IAAI,CAAA;QACP,SAAS,CACP,QAAQ,EAAE,CACR,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,EACnC,SAAS,EAAE,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,KACpC,IAAI,GACR,MAAM,IAAI,CAAA;KACd,CAAA;IACD,UAAU,EAAE;QACV,yBAAyB,CACvB,cAAc,EAAE,qBAAqB,GACpC,iBAAiB,CAAA;QACpB,KAAK,CAAC,WAAW,SAAS,iBAAiB,EACzC,WAAW,EAAE,WAAW,GACvB,SAAS,CAAC,WAAW,CAAC,CAAA;QACzB,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,SAAS,EAAE,CAAC,GAAG,IAAI,CAAA;QAC1E,SAAS,CACP,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,IAAI,GAC7D,MAAM,IAAI,CAAA;KACd,CAAA;IACD,MAAM,EAAE;QACN,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,iBAAiB,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAA;QAC1D,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,iBAAiB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAA;QAC5D,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,iBAAiB,EAAE,YAAY,CAAC,GAAG,IAAI,CAAA;KACnE,CAAA;CACF,CAAA;AAED,MAAM,MAAM,KAAK,CACf,MAAM,SAAS,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,IACxE;IACF,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAA;IAC7B,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;IACpC,OAAO,EAAE,MAAM,GAAG,IAAI,CAAA;IACtB,MAAM,EAAE,WAAW,GAAG,YAAY,GAAG,cAAc,GAAG,cAAc,CAAA;CACrE,CAAA;AAED,MAAM,MAAM,gBAAgB,GAAG,OAAO,CACpC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,QAAQ,CAAC,CAAC,CAC5E,CAAA;AAED,MAAM,MAAM,UAAU,GAAG;IACvB,QAAQ,EAAE,SAAS,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,CAAA;IAC1C,OAAO,EAAE,MAAM,CAAA;IACf,SAAS,EAAE,SAAS,CAAA;CACrB,CAAA;AAED,MAAM,MAAM,SAAS,CACnB,iBAAiB,SAAS,iBAAiB,GAAG,iBAAiB,IAC7D,UAAU,CAAC,iBAAiB,CAAC,GAAG;IAClC,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAA;IACnC,GAAG,EAAE,MAAM,CAAA;CACZ,CAAA;AAED,MAAM,MAAM,SAAS,CACnB,IAAI,SAAS,MAAM,GAAG,MAAM,EAC5B,aAAa,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EACnC,gBAAgB,SAAS,gBAAgB,GAAG,gBAAgB,IAC1D,CACF,MAAM,EAAE,UAAU,CAChB,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE,gBAAgB,CAAC,CACtD,CAAC,CAAC,CAAC,GAAG;IACL,UAAU,CAAC,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAA;CAC/C,KACE,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC,CAAA;AAEtE,KAAK,YAAY,GAAG,SAAS,CAC3B,iBAAiB,EACjB,SAAS,GAAG,OAAO,GAAG,KAAK,GAAG,MAAM,GAAG,WAAW,GAAG,MAAM,CAC5D,CAAA"}