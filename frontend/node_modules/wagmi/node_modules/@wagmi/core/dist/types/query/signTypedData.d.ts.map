{"version": 3, "file": "signTypedData.d.ts", "sourceRoot": "", "sources": ["../../../src/query/signTypedData.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAmB,MAAM,sBAAsB,CAAA;AAE1E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,MAAM,CAAA;AACrC,OAAO,EACL,KAAK,sBAAsB,EAC3B,KAAK,uBAAuB,EAC5B,KAAK,uBAAuB,EAE7B,MAAM,6BAA6B,CAAA;AACpC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAA;AAEhD,wBAAgB,4BAA4B,CAAC,MAAM,SAAS,MAAM,EAChE,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAYf;AAED,MAAM,MAAM,iBAAiB,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAA;AAEhE,MAAM,MAAM,sBAAsB,CAChC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,EACjE,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,GAAG,MAAM,SAAS,EAEtE,YAAY,GAAG,SAAS,SAAS,SAAS,GAAG,MAAM,SAAS,GAAG,MAAM,IACnE,uBAAuB,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,CAAC,CAAA;AAEjE,MAAM,MAAM,mBAAmB,CAAC,OAAO,GAAG,OAAO,IAAI,CACnD,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EAEpD,SAAS,EAAE,sBAAsB,CAAC,SAAS,EAAE,WAAW,CAAC,EACzD,OAAO,CAAC,EACJ,aAAa,CACX,iBAAiB,EACjB,sBAAsB,EACtB,sBAAsB,CACpB,SAAS,EACT,WAAW,EAEX,WAAW,CACZ,EACD,OAAO,CACR,GACD,SAAS,KACV,IAAI,CAAA;AAET,MAAM,MAAM,wBAAwB,CAAC,OAAO,GAAG,OAAO,IAAI,CACxD,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EAEpD,SAAS,EAAE,sBAAsB,CAAC,SAAS,EAAE,WAAW,CAAC,EACzD,OAAO,CAAC,EACJ,aAAa,CACX,iBAAiB,EACjB,sBAAsB,EACtB,sBAAsB,CACpB,SAAS,EACT,WAAW,EAEX,WAAW,CACZ,EACD,OAAO,CACR,GACD,SAAS,KACV,OAAO,CAAC,iBAAiB,CAAC,CAAA"}