{"version": 3, "file": "estimateFeesPerGas.d.ts", "sourceRoot": "", "sources": ["../../../src/query/estimateFeesPerGas.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,MAAM,CAAA;AAEzC,OAAO,EAEL,KAAK,4BAA4B,EACjC,KAAK,4BAA4B,EAElC,MAAM,kCAAkC,CAAA;AACzC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAA;AAC/D,OAAO,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAG9D,MAAM,MAAM,yBAAyB,CACnC,IAAI,SAAS,aAAa,EAC1B,MAAM,SAAS,MAAM,IACnB,OAAO,CACT,YAAY,CAAC,4BAA4B,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,iBAAiB,CAC7E,CAAA;AAED,wBAAgB,8BAA8B,CAC5C,MAAM,SAAS,MAAM,EACrB,IAAI,SAAS,aAAa,GAAG,SAAS,EACtC,MAAM,EAAE,MAAM,EAAE,OAAO,GAAE,yBAAyB,CAAC,IAAI,EAAE,MAAM,CAAM;;;;;;;;;;;;;;;;;;;EAatE;AAED,MAAM,MAAM,6BAA6B,CAAC,IAAI,SAAS,aAAa,IAClE,4BAA4B,CAAC,IAAI,CAAC,CAAA;AAEpC,MAAM,MAAM,sBAAsB,CAAC,IAAI,SAAS,aAAa,IAC3D,6BAA6B,CAAC,IAAI,CAAC,CAAA;AAErC,wBAAgB,0BAA0B,CACxC,MAAM,SAAS,MAAM,EACrB,IAAI,SAAS,aAAa,GAAG,SAAS,EACtC,OAAO,GAAE,yBAAyB,CAAC,IAAI,EAAE,MAAM,CAAM;;;;;GAEtD;AAED,MAAM,MAAM,0BAA0B,CACpC,MAAM,SAAS,MAAM,EACrB,IAAI,SAAS,aAAa,IACxB,UAAU,CAAC,OAAO,0BAA0B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAA"}