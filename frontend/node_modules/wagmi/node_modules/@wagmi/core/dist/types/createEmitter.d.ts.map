{"version": 3, "file": "createEmitter.d.ts", "sourceRoot": "", "sources": ["../../src/createEmitter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAA;AAE5C,KAAK,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAA;AAC9C,KAAK,QAAQ,CAAC,QAAQ,SAAS,QAAQ,IAAI,MAAM,GAAG,MAAM,QAAQ,CAAA;AAClE,KAAK,OAAO,CAAC,UAAU,SAAS,OAAO,EAAE,GAAG,GAAG,EAAE,IAAI,CACnD,GAAG,UAAU,EAAE,UAAU,KACtB,IAAI,CAAA;AACT,MAAM,MAAM,SAAS,CACnB,QAAQ,SAAS,QAAQ,EACzB,SAAS,SAAS,MAAM,QAAQ,IAC9B,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG;IAC1E,GAAG,EAAE,MAAM,CAAA;CACZ,CAAA;AAED,qBAAa,OAAO,CAAC,QAAQ,SAAS,QAAQ;IAGzB,GAAG,EAAE,MAAM;IAF9B,QAAQ,qCAAqB;gBAEV,GAAG,EAAE,MAAM;IAE9B,EAAE,CAAC,GAAG,SAAS,QAAQ,CAAC,QAAQ,CAAC,EAC/B,SAAS,EAAE,GAAG,EACd,EAAE,EAAE,OAAO,CACT,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,GACzB,CAAC;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC,GACjB,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC,CAC5C;IAKH,IAAI,CAAC,GAAG,SAAS,QAAQ,CAAC,QAAQ,CAAC,EACjC,SAAS,EAAE,GAAG,EACd,EAAE,EAAE,OAAO,CACT,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,GACzB,CAAC;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC,GACjB,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC,CAC5C;IAKH,GAAG,CAAC,GAAG,SAAS,QAAQ,CAAC,QAAQ,CAAC,EAChC,SAAS,EAAE,GAAG,EACd,EAAE,EAAE,OAAO,CACT,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,GACzB,CAAC;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC,GACjB,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC,CAC5C;IAKH,IAAI,CAAC,GAAG,SAAS,QAAQ,CAAC,QAAQ,CAAC,EACjC,SAAS,EAAE,GAAG,EACd,GAAG,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;IAMvE,aAAa,CAAC,GAAG,SAAS,QAAQ,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,GAAG;CAG7D;AAED,wBAAgB,aAAa,CAAC,QAAQ,SAAS,QAAQ,EAAE,GAAG,EAAE,MAAM,qBAEnE"}