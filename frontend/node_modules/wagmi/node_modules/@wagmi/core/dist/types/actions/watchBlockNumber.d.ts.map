{"version": 3, "file": "watchBlockNumber.d.ts", "sourceRoot": "", "sources": ["../../../src/actions/watchBlockNumber.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,KAAK,0BAA0B,IAAI,+BAA+B,EAClE,KAAK,0BAA0B,IAAI,+BAA+B,EAEnE,MAAM,cAAc,CAAA;AAErB,OAAO,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,kBAAkB,EAAE,MAAM,MAAM,CAAA;AAChE,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AACrD,OAAO,KAAK,EACV,gBAAgB,EAChB,2BAA2B,EAC5B,MAAM,wBAAwB,CAAA;AAC/B,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAGrD,MAAM,MAAM,0BAA0B,CACpC,MAAM,SAAS,MAAM,GAAG,MAAM,EAC9B,OAAO,SACL,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAEjE,MAAM,SAAS,SAAS,KAAK,EAAE,GAAG,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,IAC7D;KACD,GAAG,IAAI,MAAM,MAAM,GAAG,YAAY,CACjC,+BAA+B,CAC7B,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,MAAM,SAAS,SAC1E,SAAS,GACP,SAAS,SAAS,SAAS,GACzB,kBAAkB,GAClB,SAAS,GACX,kBAAkB,CACvB,GACC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,GACjC,2BAA2B,CAC9B;CACF,CAAC,MAAM,CAAC,CAAA;AAET,MAAM,MAAM,0BAA0B,GAAG,+BAA+B,CAAA;AAGxE,yDAAyD;AACzD,wBAAgB,gBAAgB,CAC9B,MAAM,SAAS,MAAM,EACrB,OAAO,SACL,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAEjE,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,0BAA0B,CAAC,MAAM,EAAE,OAAO,CAAC,GACtD,0BAA0B,CA6B5B"}