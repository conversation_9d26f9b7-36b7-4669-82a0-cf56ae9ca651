{"version": 3, "file": "multicall.d.ts", "sourceRoot": "", "sources": ["../../../src/actions/multicall.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,0BAA0B,EAC1B,kBAAkB,IAAI,uBAAuB,EAC7C,mBAAmB,IAAI,wBAAwB,EAC/C,mBAAmB,IAAI,wBAAwB,EAChD,MAAM,MAAM,CAAA;AAGb,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAA;AAG9D,MAAM,MAAM,mBAAmB,CAC7B,SAAS,SAAS,SAAS,OAAO,EAAE,GAAG,SAAS,0BAA0B,EAAE,EAC5E,YAAY,SAAS,OAAO,GAAG,IAAI,EACnC,MAAM,SAAS,MAAM,GAAG,MAAM,IAC5B,wBAAwB,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAA;AAEhF,MAAM,MAAM,mBAAmB,CAC7B,SAAS,SAAS,SAAS,OAAO,EAAE,GAAG,SAAS,0BAA0B,EAAE,EAC5E,YAAY,SAAS,OAAO,GAAG,IAAI,IACjC,wBAAwB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAA;AAErD,MAAM,MAAM,kBAAkB,GAAG,uBAAuB,CAAA;AAExD,wBAAsB,SAAS,CAC7B,MAAM,SAAS,MAAM,EACrB,KAAK,CAAC,SAAS,SAAS,SAAS,0BAA0B,EAAE,EAC7D,YAAY,SAAS,OAAO,GAAG,IAAI,EAEnC,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,mBAAmB,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,CAAC,GAC/D,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CASvD"}