{"version": 3, "file": "getBlockTransactionCount.d.ts", "sourceRoot": "", "sources": ["../../../src/query/getBlockTransactionCount.ts"], "names": [], "mappings": "AAEA,OAAO,EAEL,KAAK,kCAAkC,EACvC,KAAK,kCAAkC,EAExC,MAAM,wCAAwC,CAAA;AAC/C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAA;AAC/D,OAAO,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAGnE,MAAM,MAAM,+BAA+B,CACzC,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAC5C,YAAY,CACd,YAAY,CAAC,kCAAkC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,GAC/D,iBAAiB,CACpB,CAAA;AAED,wBAAgB,oCAAoC,CAClD,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAE9C,MAAM,EAAE,MAAM,EACd,OAAO,GAAE,+BAA+B,CAAC,MAAM,EAAE,OAAO,CAAM;;;;;;;;;EAkB/D;AAED,MAAM,MAAM,mCAAmC,GAC7C,kCAAkC,CAAA;AAEpC,MAAM,MAAM,4BAA4B,GAAG,mCAAmC,CAAA;AAE9E,wBAAgB,gCAAgC,CAC9C,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAC9C,OAAO,GAAE,+BAA+B,CAAC,MAAM,EAAE,OAAO,CAAM,wFAE/D;AAED,MAAM,MAAM,gCAAgC,CAC1C,MAAM,SAAS,MAAM,EACrB,OAAO,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAC5C,UAAU,CAAC,OAAO,gCAAgC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAA"}