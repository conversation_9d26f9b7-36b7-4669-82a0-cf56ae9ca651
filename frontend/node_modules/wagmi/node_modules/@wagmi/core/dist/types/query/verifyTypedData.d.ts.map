{"version": 3, "file": "verifyTypedData.d.ts", "sourceRoot": "", "sources": ["../../../src/query/verifyTypedData.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,MAAM,CAAA;AAErC,OAAO,EAEL,KAAK,yBAAyB,EAC9B,KAAK,yBAAyB,EAE/B,MAAM,+BAA+B,CAAA;AACtC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAA;AAC/D,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAGrD,MAAM,MAAM,sBAAsB,CAChC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACrD,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,MAAM,SAAS,MAAM,IACnB,YAAY,CAAC,yBAAyB,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,GACzE,iBAAiB,CAAA;AAEnB,wBAAgB,2BAA2B,CACzC,MAAM,SAAS,MAAM,EACrB,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EAEpD,MAAM,EAAE,MAAM,EACd,OAAO,GAAE,sBAAsB,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,CAAa;;;;;;;;;EAoC5E;AAED,MAAM,MAAM,0BAA0B,GAAG,yBAAyB,CAAA;AAElE,MAAM,MAAM,mBAAmB,GAAG,0BAA0B,CAAA;AAE5D,wBAAgB,uBAAuB,CACrC,MAAM,SAAS,MAAM,EACrB,KAAK,CAAC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC3D,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,OAAO,EAAE,sBAAsB,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,CAAC,wFAEhE;AAED,MAAM,MAAM,uBAAuB,CACjC,SAAS,SAAS,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACrD,WAAW,SAAS,MAAM,SAAS,GAAG,cAAc,EACpD,MAAM,SAAS,MAAM,IACnB,UAAU,CAAC,OAAO,uBAAuB,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,CAAA"}