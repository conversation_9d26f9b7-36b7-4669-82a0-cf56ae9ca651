{"version": 3, "file": "prepareTransactionRequest.js", "sourceRoot": "", "sources": ["../../../src/query/prepareTransactionRequest.ts"], "names": [], "mappings": "AAIA,OAAO,EAIL,yBAAyB,GAC1B,MAAM,yCAAyC,CAAA;AAKhD,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAA;AAc/C,MAAM,UAAU,qCAAqC,CAQnD,MAAc,EACd,UAII,EAAS;IAEb,OAAO;QACL,OAAO,CAAC,EAAE,QAAQ,EAAE;YAClB,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YACtD,IAAI,CAAC,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;YAC1C,OAAO,yBAAyB,CAAC,MAAM,EAAE;gBACvC,EAAE;gBACF,GAAI,UAAkB;aACvB,CAEA,CAAA;QACH,CAAC;QACD,QAAQ,EAAE,iCAAiC,CAAC,OAAO,CAAC;KAMrD,CAAA;AACH,CAAC;AAmBD,MAAM,UAAU,iCAAiC,CAO/C,OAAmE;IACnE,OAAO,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAU,CAAA;AAC5E,CAAC"}