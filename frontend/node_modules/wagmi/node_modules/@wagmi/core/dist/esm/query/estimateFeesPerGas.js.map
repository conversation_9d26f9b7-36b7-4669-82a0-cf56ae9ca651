{"version": 3, "file": "estimateFeesPerGas.js", "sourceRoot": "", "sources": ["../../../src/query/estimateFeesPerGas.ts"], "names": [], "mappings": "AAGA,OAAO,EAIL,kBAAkB,GACnB,MAAM,kCAAkC,CAAA;AAIzC,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAA;AAS/C,MAAM,UAAU,8BAA8B,CAG5C,MAAc,EAAE,UAAmD,EAAE;IACrE,OAAO;QACL,KAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE;YACxB,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YAClD,OAAO,kBAAkB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QAC/C,CAAC;QACD,QAAQ,EAAE,0BAA0B,CAAC,OAAO,CAAC;KAM9C,CAAA;AACH,CAAC;AAQD,MAAM,UAAU,0BAA0B,CAGxC,UAAmD,EAAE;IACrD,OAAO,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAU,CAAA;AACrE,CAAC"}