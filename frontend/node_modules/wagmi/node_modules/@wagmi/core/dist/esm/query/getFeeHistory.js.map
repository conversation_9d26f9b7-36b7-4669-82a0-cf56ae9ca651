{"version": 3, "file": "getFeeHistory.js", "sourceRoot": "", "sources": ["../../../src/query/getFeeHistory.ts"], "names": [], "mappings": "AAEA,OAAO,EAIL,aAAa,GACd,MAAM,6BAA6B,CAAA;AAIpC,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAA;AAa/C,MAAM,UAAU,yBAAyB,CAGvC,MAAc,EAAE,UAAiD,EAAE;IACnE,OAAO;QACL,KAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE;YACxB,MAAM,EACJ,UAAU,EACV,iBAAiB,EACjB,QAAQ,EAAE,CAAC,EACX,GAAG,UAAU,EACd,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YACf,IAAI,CAAC,UAAU;gBAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;YAC1D,IAAI,CAAC,iBAAiB;gBAAE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;YACxE,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,MAAM,EAAE;gBAC7C,GAAI,UAAsC;gBAC1C,UAAU;gBACV,iBAAiB;aAClB,CAAC,CAAA;YACF,OAAO,UAAU,IAAI,IAAI,CAAA;QAC3B,CAAC;QACD,QAAQ,EAAE,qBAAqB,CAAC,OAAO,CAAC;KAMzC,CAAA;AACH,CAAC;AAMD,MAAM,UAAU,qBAAqB,CAGnC,UAAiD,EAAE;IACnD,OAAO,CAAC,YAAY,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAU,CAAA;AAC7D,CAAC"}