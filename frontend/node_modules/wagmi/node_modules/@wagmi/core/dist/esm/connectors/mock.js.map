{"version": 3, "file": "mock.js", "sourceRoot": "", "sources": ["../../../src/connectors/mock.ts"], "names": [], "mappings": "AAAA,OAAO,EAIL,eAAe,EACf,gBAAgB,EAEhB,wBAAwB,EAIxB,MAAM,EACN,OAAO,EACP,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,GACZ,MAAM,MAAM,CAAA;AACb,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAA;AAEhC,OAAO,EACL,uBAAuB,EACvB,0BAA0B,GAC3B,MAAM,qBAAqB,CAAA;AAC5B,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAA;AAiBtD,IAAI,CAAC,IAAI,GAAG,MAAe,CAAA;AAC3B,MAAM,UAAU,IAAI,CAAC,UAA0B;IAC7C,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAc,CAAA;IAC9C,MAAM,QAAQ,GACZ,UAAU,CAAC,QAAQ;QAClB,EAAE,gBAAgB,EAAE,KAAK,EAAwC,CAAA;IAepE,IAAI,SAAS,GAAG,QAAQ,CAAC,gBAAgB,CAAA;IACzC,IAAI,gBAAwB,CAAA;IAE5B,OAAO,eAAe,CAAuB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACxD,EAAE,EAAE,MAAM;QACV,IAAI,EAAE,gBAAgB;QACtB,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,KAAK,CAAC,KAAK;YACT,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QACxC,CAAC;QACD,KAAK,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE;YAC5B,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;gBAC1B,IAAI,OAAO,QAAQ,CAAC,YAAY,KAAK,SAAS;oBAC5C,MAAM,IAAI,wBAAwB,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAA;gBACrE,MAAM,QAAQ,CAAC,YAAY,CAAA;YAC7B,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;gBACtC,MAAM,EAAE,qBAAqB;aAC9B,CAAC,CAAA;YAEF,IAAI,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YAC5C,IAAI,OAAO,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;gBAC1C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,WAAY,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;gBAClD,cAAc,GAAG,KAAK,CAAC,EAAE,CAAA;YAC3B,CAAC;YAED,SAAS,GAAG,IAAI,CAAA;YAEhB,OAAO;gBACL,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC5C,OAAO,EAAE,cAAc;aACxB,CAAA;QACH,CAAC;QACD,KAAK,CAAC,UAAU;YACd,SAAS,GAAG,KAAK,CAAA;QACnB,CAAC;QACD,KAAK,CAAC,WAAW;YACf,IAAI,CAAC,SAAS;gBAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;YACtD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC,CAAA;YACnE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;QAC3C,CAAC;QACD,KAAK,CAAC,UAAU;YACd,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAA;YACpE,OAAO,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;QACtC,CAAC;QACD,KAAK,CAAC,YAAY;YAChB,IAAI,CAAC,QAAQ,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAA;YACrC,IAAI,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAA;YAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAA;QAC1B,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE;YAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAA;YACzD,IAAI,CAAC,KAAK;gBAAE,MAAM,IAAI,gBAAgB,CAAC,IAAI,uBAAuB,EAAE,CAAC,CAAA;YAErE,MAAM,QAAQ,CAAC,OAAO,CAAC;gBACrB,MAAM,EAAE,4BAA4B;gBACpC,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;aAC5C,CAAC,CAAA;YACF,OAAO,KAAK,CAAA;QACd,CAAC;QACD,iBAAiB,CAAC,QAAQ;YACxB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;gBAAE,IAAI,CAAC,YAAY,EAAE,CAAA;;gBAE5C,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAC5B,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;iBAC7C,CAAC,CAAA;QACN,CAAC;QACD,cAAc,CAAC,KAAK;YAClB,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;YAC7B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA;QAC5C,CAAC;QACD,KAAK,CAAC,YAAY,CAAC,MAAM;YACvB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;YACjC,SAAS,GAAG,KAAK,CAAA;QACnB,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE;YAChC,MAAM,KAAK,GACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YACjE,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAE,CAAA;YAE1C,MAAM,OAAO,GAAqB,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC7D,cAAc;gBACd,IAAI,MAAM,KAAK,aAAa;oBAAE,OAAO,WAAW,CAAC,gBAAgB,CAAC,CAAA;gBAClE,IAAI,MAAM,KAAK,qBAAqB;oBAAE,OAAO,UAAU,CAAC,QAAQ,CAAA;gBAChE,IAAI,MAAM,KAAK,sBAAsB;oBACnC,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;wBAChC,IAAI,OAAO,QAAQ,CAAC,kBAAkB,KAAK,SAAS;4BAClD,MAAM,IAAI,wBAAwB,CAChC,IAAI,KAAK,CAAC,4BAA4B,CAAC,CACxC,CAAA;wBACH,MAAM,QAAQ,CAAC,kBAAkB,CAAA;oBACnC,CAAC;gBAEH,iBAAiB;gBACjB,IAAI,MAAM,KAAK,4BAA4B,EAAE,CAAC;oBAC5C,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;wBAC9B,IAAI,OAAO,QAAQ,CAAC,gBAAgB,KAAK,SAAS;4BAChD,MAAM,IAAI,wBAAwB,CAChC,IAAI,KAAK,CAAC,yBAAyB,CAAC,CACrC,CAAA;wBACH,MAAM,QAAQ,CAAC,gBAAgB,CAAA;oBACjC,CAAC;oBAED,gBAAgB,GAAG,OAAO,CAAE,MAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;oBACnE,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAA;oBAChD,OAAM;gBACR,CAAC;gBAED,IAAI,MAAM,KAAK,mBAAmB,EAAE,CAAC;oBACnC,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;wBAC7B,IAAI,OAAO,QAAQ,CAAC,eAAe,KAAK,SAAS;4BAC/C,MAAM,IAAI,wBAAwB,CAChC,IAAI,KAAK,CAAC,yBAAyB,CAAC,CACrC,CAAA;wBACH,MAAM,QAAQ,CAAC,eAAe,CAAA;oBAChC,CAAC;oBACD,OAAO,SAAS,CAAA;gBAClB,CAAC;gBAED,IAAI,MAAM,KAAK,wBAAwB;oBACrC,OAAO;wBACL,QAAQ,EAAE;4BACR,gBAAgB,EAAE;gCAChB,SAAS,EACN,MAAgB,CAAC,CAAC,CAAC;oCACpB,4CAA4C;6BAC/C;4BACD,WAAW,EAAE;gCACX,SAAS,EAAE,IAAI;6BAChB;yBACF;wBACD,SAAS,EAAE;4BACT,gBAAgB,EAAE;gCAChB,SAAS,EACN,MAAgB,CAAC,CAAC,CAAC;oCACpB,4CAA4C;6BAC/C;yBACF;qBACF,CAAA;gBAEH,IAAI,MAAM,KAAK,kBAAkB,EAAE,CAAC;oBAClC,MAAM,MAAM,GAAG,EAAE,CAAA;oBACjB,MAAM,KAAK,GAAI,MAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;oBACtC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE;4BAC5C,IAAI,EAAE;gCACJ,MAAM,EAAE,qBAAqB;gCAC7B,MAAM,EAAE,CAAC,IAAI,CAAC;6BACf;yBACF,CAAC,CAAA;wBACF,IAAI,KAAK;4BACP,MAAM,IAAI,eAAe,CAAC;gCACxB,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;gCACxB,KAAK;gCACL,GAAG;6BACJ,CAAC,CAAA;wBACJ,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;oBACrB,CAAC;oBACD,MAAM,EAAE,GAAG,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;oBACxD,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;oBAChC,OAAO,EAAE,EAAE,EAAE,CAAA;gBACf,CAAC;gBAED,IAAI,MAAM,KAAK,uBAAuB,EAAE,CAAC;oBACvC,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAE,MAAc,CAAC,CAAC,CAAC,CAAC,CAAA;oBACvD,IAAI,CAAC,MAAM;wBACT,OAAO;4BACL,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE,KAAK;4BACd,EAAE,EAAG,MAAc,CAAC,CAAC,CAAC;4BACtB,MAAM,EAAE,GAAG;4BACX,QAAQ,EAAE,EAAE;4BACZ,OAAO,EAAE,OAAO;yBACwB,CAAA;oBAE5C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAChC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;wBACxB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE;4BAC5C,IAAI,EAAE;gCACJ,MAAM,EAAE,2BAA2B;gCACnC,MAAM,EAAE,CAAC,IAAI,CAAC;gCACd,EAAE,EAAE,CAAC;6BACN;yBACF,CAAC,CAAA;wBACF,IAAI,KAAK;4BACP,MAAM,IAAI,eAAe,CAAC;gCACxB,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;gCACxB,KAAK;gCACL,GAAG;6BACJ,CAAC,CAAA;wBACJ,IAAI,CAAC,MAAM;4BAAE,OAAO,IAAI,CAAA;wBACxB,OAAO;4BACL,SAAS,EAAE,MAAM,CAAC,SAAS;4BAC3B,WAAW,EAAE,MAAM,CAAC,WAAW;4BAC/B,OAAO,EAAE,MAAM,CAAC,OAAO;4BACvB,IAAI,EAAE,MAAM,CAAC,IAAI;4BACjB,MAAM,EAAE,MAAM,CAAC,MAAM;4BACrB,eAAe,EAAE,MAAM,CAAC,eAAe;yBACZ,CAAA;oBAC/B,CAAC,CAAC,CACH,CAAA;oBACD,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAA;oBACpD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;wBACxB,OAAO;4BACL,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE,KAAK;4BACd,EAAE,EAAG,MAAc,CAAC,CAAC,CAAC;4BACtB,MAAM,EAAE,GAAG;4BACX,QAAQ,EAAE,EAAE;4BACZ,OAAO,EAAE,OAAO;yBACwB,CAAA;oBAC5C,OAAO;wBACL,MAAM,EAAE,KAAK;wBACb,OAAO,EAAE,KAAK;wBACd,EAAE,EAAG,MAAc,CAAC,CAAC,CAAC;wBACtB,MAAM,EAAE,GAAG;wBACX,QAAQ,EAAE,SAAS;wBACnB,OAAO,EAAE,OAAO;qBACwB,CAAA;gBAC5C,CAAC;gBAED,IAAI,MAAM,KAAK,wBAAwB;oBAAE,OAAM;gBAE/C,gBAAgB;gBAChB,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;oBAC/B,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;wBAC9B,IAAI,OAAO,QAAQ,CAAC,gBAAgB,KAAK,SAAS;4BAChD,MAAM,IAAI,wBAAwB,CAChC,IAAI,KAAK,CAAC,yBAAyB,CAAC,CACrC,CAAA;wBACH,MAAM,QAAQ,CAAC,gBAAgB,CAAA;oBACjC,CAAC;oBACD,uDAAuD;oBACvD,MAAM,GAAG,UAAU,CAAA;oBAEnB,MAAM,GAAG,CAAE,MAAiB,CAAC,CAAC,CAAC,EAAG,MAAiB,CAAC,CAAC,CAAC,CAAC,CAAA;gBACzD,CAAC;gBAED,MAAM,IAAI,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;gBAC/B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,CAAA;gBACvD,IAAI,KAAK;oBAAE,MAAM,IAAI,eAAe,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAA;gBAE1D,OAAO,MAAM,CAAA;YACf,CAAC,CAAA;YACD,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAA;QAC/C,CAAC;KACF,CAAC,CAAC,CAAA;AACL,CAAC"}