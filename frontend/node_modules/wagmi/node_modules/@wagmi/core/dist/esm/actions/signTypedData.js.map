{"version": 3, "file": "signTypedData.js", "sourceRoot": "", "sources": ["../../../src/actions/signTypedData.ts"], "names": [], "mappings": "AACA,OAAO,EAIL,aAAa,IAAI,kBAAkB,GACpC,MAAM,cAAc,CAAA;AAMrB,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACjD,OAAO,EAEL,kBAAkB,GACnB,MAAM,yBAAyB,CAAA;AAuBhC,sDAAsD;AACtD,MAAM,CAAC,KAAK,UAAU,aAAa,CAIjC,MAAc,EACd,UAA2D;IAE3D,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,UAAU,CAAA;IAElD,IAAI,MAAc,CAAA;IAClB,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO;QACzD,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAA;;QACxB,MAAM,GAAG,MAAM,kBAAkB,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAA;IAEtE,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,kBAAkB,EAAE,eAAe,CAAC,CAAA;IACrE,OAAO,MAAM,CAAC;QACZ,GAAG,IAAI;QACP,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KACW,CAAC,CAAA;AAC/C,CAAC"}