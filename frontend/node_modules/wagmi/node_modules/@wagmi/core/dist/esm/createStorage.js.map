{"version": 3, "file": "createStorage.js", "sourceRoot": "", "sources": ["../../src/createStorage.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,IAAI,YAAY,EAAE,MAAM,wBAAwB,CAAA;AACpE,OAAO,EAAE,SAAS,IAAI,UAAU,EAAE,MAAM,sBAAsB,CAAA;AA8C9D,MAAM,UAAU,aAAa,CAG3B,UAAmC;IACnC,MAAM,EACJ,WAAW,GAAG,YAAY,EAC1B,GAAG,EAAE,MAAM,GAAG,OAAO,EACrB,SAAS,GAAG,UAAU,EACtB,OAAO,GAAG,WAAW,GACtB,GAAG,UAAU,CAAA;IAEd,SAAS,MAAM,CAAO,KAAW;QAC/B,IAAI,KAAK,YAAY,OAAO;YAAE,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAA;QAC3E,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO;QACL,GAAG,OAAO;QACV,GAAG,EAAE,MAAM;QACX,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,YAAY;YAC7B,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,GAAa,EAAE,CAAC,CAAA;YAC3D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAA;YACrC,IAAI,SAAS;gBAAE,OAAO,WAAW,CAAC,SAAS,CAAC,IAAI,IAAI,CAAA;YACpD,OAAO,CAAC,YAAY,IAAI,IAAI,CAAQ,CAAA;QACtC,CAAC;QACD,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK;YACtB,MAAM,UAAU,GAAG,GAAG,MAAM,IAAI,GAAa,EAAE,CAAA;YAC/C,IAAI,KAAK,KAAK,IAAI;gBAAE,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAA;;gBAC3D,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAClE,CAAC;QACD,KAAK,CAAC,UAAU,CAAC,GAAG;YAClB,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM,IAAI,GAAa,EAAE,CAAC,CAAC,CAAA;QAChE,CAAC;KACF,CAAA;AACH,CAAC;AAED,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI;IACnB,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC;IACjB,UAAU,EAAE,GAAG,EAAE,GAAE,CAAC;CACC,CAAA;AAEvB,MAAM,UAAU,iBAAiB;IAC/B,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE;QACpB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY;YACtD,OAAO,MAAM,CAAC,YAAY,CAAA;QAC5B,OAAO,WAAW,CAAA;IACpB,CAAC,CAAC,EAAE,CAAA;IACJ,OAAO;QACL,OAAO,CAAC,GAAG;YACT,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QAC7B,CAAC;QACD,UAAU,CAAC,GAAG;YACZ,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;QACzB,CAAC;QACD,OAAO,CAAC,GAAG,EAAE,KAAK;YAChB,IAAI,CAAC;gBACH,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;gBAC3B,sEAAsE;YACxE,CAAC;YAAC,MAAM,CAAC,CAAA,CAAC;QACZ,CAAC;KACoB,CAAA;AACzB,CAAC"}