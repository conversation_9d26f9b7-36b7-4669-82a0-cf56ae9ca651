{"version": 3, "file": "infiniteReadContracts.js", "sourceRoot": "", "sources": ["../../../src/query/infiniteReadContracts.ts"], "names": [], "mappings": "AACA,OAAO,EAIL,aAAa,GACd,MAAM,6BAA6B,CAAA;AAQpC,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAA;AAkB/C,MAAM,UAAU,iCAAiC,CAM/C,MAAc,EACd,OAOkE;IAElE,OAAO;QACL,GAAG,OAAO,CAAC,KAAK;QAChB,KAAK,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;YACnC,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAA;YAC7B,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YAChE,OAAO,CAAC,MAAM,aAAa,CAAC,MAAM,EAAE;gBAClC,GAAG,UAAU;gBACb,SAAS,EAAE,SAAS,CAAC,SAAgB,CAAC;aACvC,CAAC,CAAqD,CAAA;QACzD,CAAC;QACD,QAAQ,EAAE,6BAA6B,CAAC,OAAO,CAAC;KAQjD,CAAA;AACH,CAAC;AA4BD,MAAM,UAAU,6BAA6B,CAM3C,OAOkE;IAElE,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE,GAAG,OAAO,CAAA;IAC1D,OAAO,CAAC,uBAAuB,EAAE,kBAAkB,CAAC,UAAU,CAAC,CAAU,CAAA;AAC3E,CAAC"}