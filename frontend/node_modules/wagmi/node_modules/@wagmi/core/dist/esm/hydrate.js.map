{"version": 3, "file": "hydrate.js", "sourceRoot": "", "sources": ["../../src/hydrate.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAA;AAQlD,MAAM,UAAU,OAAO,CAAC,MAAc,EAAE,UAA6B;IACnE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,GAAG,UAAU,CAAA;IAErD,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE;QAC/D,MAAM,CAAC,QAAQ,CAAC;YACd,GAAG,YAAY;YACf,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,OAAO,CAAC;gBAC/D,CAAC,CAAC,YAAY,CAAC,OAAO;gBACtB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;YACvB,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YACpE,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc;SAC3D,CAAC,CAAA;IAEJ,OAAO;QACL,KAAK,CAAC,OAAO;YACX,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;gBACzB,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,CAAA;gBAChD,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;oBAC1B,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,EAAE;wBAClD,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAA;wBACjC,KAAK,MAAM,SAAS,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;4BACzC,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gCACnB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;oCAC9C,CAAC,CAAC,SAAS,CAAC,IAAI;oCAChB,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gCACpB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;oCAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gCACnB,CAAC;4BACH,CAAC;wBACH,CAAC;wBACD,MAAM,cAAc,GAAG,EAAE,CAAA;wBACzB,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAA;wBAC7D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;4BACjC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;gCAAE,SAAQ;4BAC7C,MAAM,WAAW,GACf,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA;4BACjE,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;4BAChE,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;wBAChC,CAAC;wBACD,OAAO,CAAC,GAAG,UAAU,EAAE,GAAG,cAAc,CAAC,CAAA;oBAC3C,CAAC,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;YAED,IAAI,gBAAgB;gBAAE,SAAS,CAAC,MAAM,CAAC,CAAA;iBAClC,IAAI,MAAM,CAAC,OAAO;gBACrB,8DAA8D;gBAC9D,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;oBACtB,GAAG,CAAC;oBACJ,WAAW,EAAE,IAAI,GAAG,EAAE;iBACvB,CAAC,CAAC,CAAA;QACP,CAAC;KACF,CAAA;AACH,CAAC"}