{"version": 3, "file": "verifyTypedData.js", "sourceRoot": "", "sources": ["../../../src/query/verifyTypedData.ts"], "names": [], "mappings": "AAGA,OAAO,EAIL,eAAe,GAChB,MAAM,+BAA+B,CAAA;AAItC,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAA;AAS/C,MAAM,UAAU,2BAA2B,CAKzC,MAAc,EACd,UAAkE,EAAS;IAE3E,OAAO;QACL,KAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE;YACxB,MAAM,EACJ,OAAO,EACP,OAAO,EACP,WAAW,EACX,SAAS,EACT,KAAK,EACL,QAAQ,EAAE,CAAC,EACX,GAAG,UAAU,EACd,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YACf,IAAI,CAAC,OAAO;gBAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;YACpD,IAAI,CAAC,OAAO;gBAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;YACpD,IAAI,CAAC,WAAW;gBAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;YAC5D,IAAI,CAAC,SAAS;gBAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;YACxD,IAAI,CAAC,KAAK;gBAAE,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;YAEhD,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,MAAM,EAAE;gBAC7C,GAAG,UAAU;gBACb,OAAO;gBACP,OAAO;gBACP,WAAW;gBACX,SAAS;gBACT,KAAK;aACuB,CAAC,CAAA;YAC/B,OAAO,QAAQ,IAAI,IAAI,CAAA;QACzB,CAAC;QACD,QAAQ,EAAE,uBAAuB,CAAC,OAAO,CAAC;KAM3C,CAAA;AACH,CAAC;AAMD,MAAM,UAAU,uBAAuB,CAIrC,OAA+D;IAC/D,OAAO,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAU,CAAA;AAClE,CAAC"}