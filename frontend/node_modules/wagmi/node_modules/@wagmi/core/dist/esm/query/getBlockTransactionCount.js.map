{"version": 3, "file": "getBlockTransactionCount.js", "sourceRoot": "", "sources": ["../../../src/query/getBlockTransactionCount.ts"], "names": [], "mappings": "AAEA,OAAO,EAIL,wBAAwB,GACzB,MAAM,wCAAwC,CAAA;AAI/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAA;AAU/C,MAAM,UAAU,oCAAoC,CAIlD,MAAc,EACd,UAA4D,EAAE;IAE9D,OAAO;QACL,KAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE;YACxB,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YAClD,MAAM,qBAAqB,GAAG,MAAM,wBAAwB,CAC1D,MAAM,EACN,UAAU,CACX,CAAA;YACD,OAAO,qBAAqB,IAAI,IAAI,CAAA;QACtC,CAAC;QACD,QAAQ,EAAE,gCAAgC,CAAC,OAAO,CAAC;KAMpD,CAAA;AACH,CAAC;AAOD,MAAM,UAAU,gCAAgC,CAG9C,UAA4D,EAAE;IAC9D,OAAO,CAAC,uBAAuB,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAU,CAAA;AACxE,CAAC"}