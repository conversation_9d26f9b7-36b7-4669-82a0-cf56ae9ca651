import { createClient, custom, } from 'viem';
import { getAddress, parseAccount } from 'viem/utils';
import { ConnectorAccountNotFoundError, ConnectorChainMismatchError, ConnectorNotConnectedError, ConnectorUnavailableReconnectingError, } from '../errors/config.js';
/** https://wagmi.sh/core/api/actions/getConnectorClient */
export async function getConnectorClient(config, parameters = {}) {
    // Get connection
    let connection;
    if (parameters.connector) {
        const { connector } = parameters;
        if (config.state.status === 'reconnecting' &&
            !connector.getAccounts &&
            !connector.getChainId)
            throw new ConnectorUnavailableReconnectingError({ connector });
        const [accounts, chainId] = await Promise.all([
            connector.getAccounts().catch((e) => {
                if (parameters.account === null)
                    return [];
                throw e;
            }),
            connector.getChainId(),
        ]);
        connection = {
            accounts: accounts,
            chainId,
            connector,
        };
    }
    else
        connection = config.state.connections.get(config.state.current);
    if (!connection)
        throw new ConnectorNotConnectedError();
    const chainId = parameters.chainId ?? connection.chainId;
    // Check connector using same chainId as connection
    const connectorChainId = await connection.connector.getChainId();
    if (connectorChainId !== connection.chainId)
        throw new ConnectorChainMismatchError({
            connectionChainId: connection.chainId,
            connectorChainId,
        });
    const connector = connection.connector;
    if (connector.getClient)
        return connector.getClient({ chainId });
    // Default using `custom` transport
    const account = parseAccount(parameters.account ?? connection.accounts[0]);
    if (account)
        account.address = getAddress(account.address); // TODO: Checksum address as part of `parseAccount`?
    // If account was provided, check that it exists on the connector
    if (parameters.account &&
        !connection.accounts.some((x) => x.toLowerCase() === account.address.toLowerCase()))
        throw new ConnectorAccountNotFoundError({
            address: account.address,
            connector,
        });
    const chain = config.chains.find((chain) => chain.id === chainId);
    const provider = (await connection.connector.getProvider({ chainId }));
    return createClient({
        account,
        chain,
        name: 'Connector Client',
        transport: (opts) => custom(provider)({ ...opts, retryCount: 0 }),
    });
}
//# sourceMappingURL=getConnectorClient.js.map