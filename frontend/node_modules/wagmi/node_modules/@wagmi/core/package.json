{"name": "@wagmi/core", "description": "VanillaJS library for Ethereum", "version": "2.17.3", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/wevm/wagmi.git", "directory": "packages/core"}, "files": ["dist/**", "!dist/**/*.tsbuildinfo", "src/**/*.ts", "!src/**/*.test.ts", "!src/**/*.test-d.ts", "/actions", "/chains", "/experimental", "/internal", "/query"], "sideEffects": false, "type": "module", "main": "./dist/esm/exports/index.js", "types": "./dist/types/exports/index.d.ts", "typings": "./dist/types/exports/index.d.ts", "exports": {".": {"types": "./dist/types/exports/index.d.ts", "default": "./dist/esm/exports/index.js"}, "./actions": {"types": "./dist/types/exports/actions.d.ts", "default": "./dist/esm/exports/actions.js"}, "./chains": {"types": "./dist/types/exports/chains.d.ts", "default": "./dist/esm/exports/chains.js"}, "./codegen": {"types": "./dist/types/exports/codegen.d.ts", "default": "./dist/esm/exports/codegen.js"}, "./experimental": {"types": "./dist/types/exports/experimental.d.ts", "default": "./dist/esm/exports/experimental.js"}, "./internal": {"types": "./dist/types/exports/internal.d.ts", "default": "./dist/esm/exports/internal.js"}, "./query": {"types": "./dist/types/exports/query.d.ts", "default": "./dist/esm/exports/query.js"}, "./package.json": "./package.json"}, "typesVersions": {"*": {"actions": ["./dist/types/exports/actions.d.ts"], "chains": ["./dist/types/exports/chains.d.ts"], "codegen": ["./dist/types/exports/codegen.d.ts"], "experimental": ["./dist/types/exports/experimental.d.ts"], "internal": ["./dist/types/exports/internal.d.ts"], "query": ["./dist/types/exports/query.d.ts"]}}, "peerDependencies": {"@tanstack/query-core": ">=5.0.0", "typescript": ">=5.0.4", "viem": "2.x"}, "peerDependenciesMeta": {"@tanstack/query-core": {"optional": true}, "typescript": {"optional": true}}, "dependencies": {"eventemitter3": "5.0.1", "mipd": "0.0.7", "zustand": "5.0.0"}, "contributors": ["awkweb.eth <<EMAIL>>", "jxom.eth <<EMAIL>>"], "funding": "https://github.com/sponsors/wevm", "keywords": ["wagmi", "eth", "ethereum", "dapps", "wallet", "web3"]}