{"name": "@wagmi/connectors", "description": "Collection of connectors for Wagmi", "version": "5.8.5", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/wevm/wagmi.git", "directory": "packages/connectors"}, "files": ["dist/**", "!dist/**/*.tsbuildinfo", "src/**/*.ts", "!src/**/*.test.ts", "!src/**/*.test-d.ts"], "sideEffects": false, "type": "module", "main": "./dist/esm/exports/index.js", "types": "./dist/types/exports/index.d.ts", "typings": "./dist/types/exports/index.d.ts", "exports": {".": {"types": "./dist/types/exports/index.d.ts", "default": "./dist/esm/exports/index.js"}, "./package.json": "./package.json"}, "peerDependencies": {"typescript": ">=5.0.4", "viem": "2.x", "@wagmi/core": "2.17.3"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"@coinbase/wallet-sdk": "4.3.3", "@metamask/sdk": "0.32.0", "@safe-global/safe-apps-provider": "0.18.6", "@safe-global/safe-apps-sdk": "9.1.0", "@walletconnect/ethereum-provider": "2.21.1", "cbw-sdk": "npm:@coinbase/wallet-sdk@3.9.3"}, "contributors": ["awkweb.eth <<EMAIL>>", "jxom.eth <<EMAIL>>"], "funding": "https://github.com/sponsors/wevm", "keywords": ["react", "hooks", "eth", "ethereum", "dapps", "wallet", "web3", "abi"]}