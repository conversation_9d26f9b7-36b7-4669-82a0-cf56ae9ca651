{"version": 3, "file": "walletConnect.d.ts", "sourceRoot": "", "sources": ["../../src/walletConnect.ts"], "names": [], "mappings": "AAOA,OAAO,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,sBAAsB,CAAA;AACvE,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAA;AACxE,OAAO,EAEL,KAAK,OAAO,EACZ,KAAK,mBAAmB,EAOzB,MAAM,MAAM,CAAA;AAOb,KAAK,uBAAuB,GAAG,UAAU,CAAC,CAAC,OAAO,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAE/E,MAAM,MAAM,uBAAuB,GAAG,OAAO,CAC3C;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAA;CAC3B,GAAG,IAAI,CACN,uBAAuB,EACrB,QAAQ,GACR,QAAQ,GACR,gBAAgB,GAChB,gBAAgB,GAChB,iBAAiB,GACjB,SAAS,GACT,QAAQ,GACR,aAAa,CAChB,GACC,YAAY,CAAC,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC,CAC7D,CAAA;AAGD,wBAAgB,aAAa,CAAC,UAAU,EAAE,uBAAuB;yBAKxC;QACnB,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC5B,cAAc,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QACpC,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAClC,GAAG,OAAO,CAAC;QACV,QAAQ,EAAE,SAAS,OAAO,EAAE,CAAA;QAC5B,OAAO,EAAE,MAAM,CAAA;KAChB,CAAC;6BACuB,MAAM,EAAE;6BACR,OAAO,CAAC,MAAM,EAAE,CAAC;qBACzB,OAAO,CAAC,OAAO,CAAC;2BACV,mBAAmB,GAAG,IAAI;sBAC/B,MAAM,GAAG,IAAI;0BACT;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI;kCAChB,MAAM,EAAE,GAAG,IAAI;+BAClB,GAAG,MAAM,kBAAkB;;;GAoXzD;yBAxYe,aAAa"}