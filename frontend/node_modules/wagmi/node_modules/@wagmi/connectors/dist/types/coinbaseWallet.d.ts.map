{"version": 3, "file": "coinbaseWallet.d.ts", "sourceRoot": "", "sources": ["../../src/coinbaseWallet.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,UAAU,EACV,iBAAiB,EACjB,uBAAuB,EACxB,MAAM,sBAAsB,CAAA;AAM7B,OAAO,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,sBAAsB,CAAA;AAClE,OAAO,KAAK,EACV,sBAAsB,IAAI,YAAY,EACtC,iBAAiB,IAAI,OAAO,EAC7B,MAAM,SAAS,CAAA;AAChB,OAAO,EAEL,KAAK,OAAO,EAOb,MAAM,MAAM,CAAA;AAEb,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG,CAAA;AAExB,MAAM,MAAM,wBAAwB,CAAC,OAAO,SAAS,OAAO,GAAG,GAAG,IAChE,OAAO,SAAS,GAAG,GACf,OAAO,CACL;IACE,YAAY,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;IAChC,kCAAkC;IAClC,OAAO,CAAC,EAAE,OAAO,GAAG,GAAG,GAAG,SAAS,CAAA;CACpC,GAAG,kBAAkB,CACvB,GACD,OAAO,CACL;IACE;;OAEG;IACH,YAAY,CAAC,EAAE,IAAI,GAAG,SAAS,CAAA;IAC/B;;;;OAIG;IACH,OAAO,CAAC,EAAE,OAAO,GAAG,GAAG,GAAG,SAAS,CAAA;CACpC,GAAG,kBAAkB,CACvB,CAAA;AAGP,wBAAgB,cAAc,CAAC,OAAO,SAAS,OAAO,EACpD,UAAU,GAAE,wBAAwB,CAAC,OAAO,CAAa,GACxD,OAAO,SAAS,GAAG,GAClB,UAAU,CAAC,OAAO,QAAQ,CAAC,GAC3B,UAAU,CAAC,OAAO,QAAQ,CAAC,CAI9B;yBARe,cAAc;;;AAU9B,KAAK,kBAAkB,GAAG,OAAO,CAC/B,IAAI,CACF,UAAU,CAAC,OAAO,uBAAuB,CAAC,CAAC,CAAC,CAAC,EAC3C,aAAa,GACb,YAAY,CACf,GAAG;IAEF;;;OAGG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,SAAS,CAAA;CACrE,CACF,CAAA;AAED,iBAAS,QAAQ,CAAC,UAAU,EAAE,kBAAkB;cAGlC,IAAI;;yBAGO;QACnB,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC5B,iBAAiB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QACvC,cAAc,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrC,GAAG,OAAO,CAAC;QACV,QAAQ,EAAE,SAAS,OAAO,EAAE,CAAA;QAC5B,OAAO,EAAE,MAAM,CAAA;KAChB,CAAC;4BAiNL;AAED,KAAK,kBAAkB,GAAG,OAAO,CAC/B,IAAI,CACF,qBAAqB,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,EACxC,oBAAoB,CACrB,CACF,GAAG;IACF;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC/B;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC5B;;;OAGG;IACH,kBAAkB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CACzC,CAAA;AAED,iBAAS,QAAQ,CAAC,UAAU,EAAE,kBAAkB,2GA4N/C"}