{"version": 3, "file": "safe.js", "sourceRoot": "", "sources": ["../../src/safe.ts"], "names": [], "mappings": "AAEA,OAAO,EAEL,qBAAqB,EACrB,eAAe,GAChB,MAAM,aAAa,CAAA;AAEpB,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,MAAM,CAAA;AAuB9C,IAAI,CAAC,IAAI,GAAG,MAAe,CAAA;AAC3B,MAAM,UAAU,IAAI,CAAC,aAA6B,EAAE;IAClD,MAAM,EAAE,cAAc,GAAG,KAAK,EAAE,GAAG,UAAU,CAAA;IAM7C,IAAI,SAA+B,CAAA;IAEnC,IAAI,UAAiD,CAAA;IAErD,OAAO,eAAe,CAAoC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACrE,EAAE,EAAE,MAAM;QACV,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,KAAK,CAAC,OAAO;YACX,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,IAAI,CAAC,QAAQ;gBAAE,MAAM,IAAI,qBAAqB,EAAE,CAAA;YAEhD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YAEvC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACzC,QAAQ,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;YACvC,CAAC;YAED,wCAAwC;YACxC,IAAI,cAAc;gBAAE,MAAM,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,mBAAmB,CAAC,CAAA;YAEzE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAA;QAC9B,CAAC;QACD,KAAK,CAAC,UAAU;YACd,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,IAAI,CAAC,QAAQ;gBAAE,MAAM,IAAI,qBAAqB,EAAE,CAAA;YAEhD,IAAI,UAAU,EAAE,CAAC;gBACf,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;gBACjD,UAAU,GAAG,SAAS,CAAA;YACxB,CAAC;YAED,gDAAgD;YAChD,IAAI,cAAc;gBAChB,MAAM,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;QAC5D,CAAC;QACD,KAAK,CAAC,WAAW;YACf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,IAAI,CAAC,QAAQ;gBAAE,MAAM,IAAI,qBAAqB,EAAE,CAAA;YAChD,OAAO,CAAC,MAAM,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,GAAG,CAC7D,UAAU,CACX,CAAA;QACH,CAAC;QACD,KAAK,CAAC,WAAW;YACf,iCAAiC;YACjC,MAAM,QAAQ,GACZ,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,EAAE,MAAM,KAAK,MAAM,CAAA;YAC5D,IAAI,CAAC,QAAQ;gBAAE,OAAM;YAErB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,MAAM,MAAM,CAAC,4BAA4B,CAAC,CAAA;gBACnE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAA;gBAE/B,mDAAmD;gBACnD,kFAAkF;gBAClF,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;oBACvD,OAAO,EAAE,UAAU,CAAC,uBAAuB,IAAI,EAAE;iBAClD,CAAC,CAAA;gBACF,IAAI,CAAC,IAAI;oBAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;gBAC7D,4CAA4C;gBAC5C,kDAAkD;gBAClD,MAAM,eAAe,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE;oBACxC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,iCAAiC,CAAC,CAAA;oBAChE,IACE,OAAO,QAAQ,CAAC,eAAe,KAAK,UAAU;wBAC9C,OAAO,QAAQ,CAAC,OAAO,CAAC,eAAe,KAAK,UAAU;wBAEtD,OAAO,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAA;oBACzC,OAAO,QAAQ,CAAC,eAAe,CAAA;gBACjC,CAAC,CAAC,EAAE,CAAA;gBACJ,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;YAC5C,CAAC;YACD,OAAO,SAAS,CAAA;QAClB,CAAC;QACD,KAAK,CAAC,UAAU;YACd,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;YACzC,IAAI,CAAC,QAAQ;gBAAE,MAAM,IAAI,qBAAqB,EAAE,CAAA;YAChD,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACjC,CAAC;QACD,KAAK,CAAC,YAAY;YAChB,IAAI,CAAC;gBACH,MAAM,cAAc,GAClB,cAAc;oBACd,uDAAuD;oBACvD,CAAC,MAAM,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAA;gBACtD,IAAI,cAAc;oBAAE,OAAO,KAAK,CAAA;gBAEhC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;gBACzC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAA;YAC1B,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,KAAK,CAAA;YACd,CAAC;QACH,CAAC;QACD,iBAAiB;YACf,sEAAsE;QACxE,CAAC;QACD,cAAc;YACZ,wFAAwF;QAC1F,CAAC;QACD,YAAY;YACV,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACnC,CAAC;KACF,CAAC,CAAC,CAAA;AACL,CAAC"}