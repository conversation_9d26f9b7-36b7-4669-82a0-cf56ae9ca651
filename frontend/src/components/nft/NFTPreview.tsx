'use client';

interface NFTPreviewProps {
  tier: 'whitelist' | 'student';
  className?: string;
}

export function NFTPreview({ tier, className = '' }: NFTPreviewProps) {
  const isWhitelist = tier === 'whitelist';
  
  return (
    <div className={`relative aspect-square rounded-2xl overflow-hidden ${className}`}>
      {/* Background Gradient */}
      <div className={`absolute inset-0 ${
        isWhitelist 
          ? 'bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500'
          : 'bg-gradient-to-br from-blue-400 via-purple-500 to-cyan-500'
      }`}>
        {/* Animated Grid Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="grid grid-cols-8 grid-rows-8 h-full w-full">
            {Array.from({ length: 64 }).map((_, i) => (
              <div 
                key={i} 
                className="border border-white/30 animate-pulse"
                style={{ animationDelay: `${i * 0.1}s` }}
              ></div>
            ))}
          </div>
        </div>

        {/* Central Logo Area */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative">
            {/* Outer Ring */}
            <div className={`w-32 h-32 border-4 rounded-full animate-spin-slow ${
              isWhitelist ? 'border-yellow-200' : 'border-cyan-200'
            }`}></div>
            
            {/* Middle Ring */}
            <div className={`absolute inset-4 border-2 rounded-full animate-pulse ${
              isWhitelist ? 'border-orange-200' : 'border-purple-200'
            }`}></div>
            
            {/* Inner Ring */}
            <div className={`absolute inset-8 border rounded-full animate-bounce-slow ${
              isWhitelist ? 'border-red-200' : 'border-blue-200'
            }`}></div>
            
            {/* Center Symbol */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className={`w-8 h-8 rounded-full animate-glow ${
                isWhitelist 
                  ? 'bg-gradient-to-r from-yellow-300 to-orange-300'
                  : 'bg-gradient-to-r from-cyan-300 to-purple-300'
              }`}></div>
            </div>
          </div>
        </div>

        {/* Floating Particles */}
        <div className="absolute top-4 left-4 w-3 h-3 bg-white/80 rounded-full animate-float"></div>
        <div className="absolute top-8 right-8 w-2 h-2 bg-white/60 rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-8 left-8 w-4 h-4 bg-white/70 rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-4 right-4 w-2 h-2 bg-white/50 rounded-full animate-float" style={{ animationDelay: '1.5s' }}></div>
        
        {/* Corner Accents */}
        <div className={`absolute top-0 left-0 w-16 h-16 ${
          isWhitelist ? 'bg-yellow-300/30' : 'bg-blue-300/30'
        } rounded-br-full`}></div>
        <div className={`absolute bottom-0 right-0 w-16 h-16 ${
          isWhitelist ? 'bg-red-300/30' : 'bg-purple-300/30'
        } rounded-tl-full`}></div>

        {/* Tier Badge */}
        <div className="absolute top-4 right-4">
          <div className={`px-3 py-1 rounded-full text-xs font-bold backdrop-blur-sm ${
            isWhitelist 
              ? 'bg-yellow-900/80 text-yellow-100 border border-yellow-400/50'
              : 'bg-blue-900/80 text-blue-100 border border-blue-400/50'
          }`}>
            {isWhitelist ? 'WHITELIST' : 'STUDENT'}
          </div>
        </div>

        {/* Bottom Info */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="backdrop-blur-sm bg-black/30 rounded-lg p-3">
            <div className="text-white text-sm font-bold">AkashaDao Pass</div>
            <div className="text-white/80 text-xs">
              {isWhitelist ? 'Premium Access' : 'Student Access'}
            </div>
          </div>
        </div>

        {/* Holographic Effect */}
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/10 to-transparent opacity-50 animate-pulse"></div>
      </div>
    </div>
  );
}
