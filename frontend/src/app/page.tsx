'use client';

import { useState } from 'react';
import { useAccount } from 'wagmi';
import { WalletButton } from '@/components/wallet/WalletButton';
import { MintCard } from '@/components/nft/MintCard';
import { NFTGrid } from '@/components/nft/NFTCard';
import { PRICES } from '@/lib/web3';

export default function Home() {
  const { address, isConnected } = useAccount();
  const [userNFTs, setUserNFTs] = useState<number[]>([]);

  const handleMintSuccess = (tokenId: number) => {
    setUserNFTs(prev => [...prev, tokenId]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* 导航栏 */}
      <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                AkashaDao
              </h1>
              <span className="ml-2 px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 rounded-full">
                Community Pass
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <a
                href="/mint"
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg font-medium transition-all transform hover:scale-105"
              >
                铸造 NFT
              </a>
              <WalletButton />
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 头部介绍 */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            AkashaDao 社区通行证
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            获得专属NFT通行证，解锁AkashaDao社区的全部权益。
            享受无限制阅读、投票权、专属活动和VC网络连接。
          </p>
        </div>

        {/* NFT铸造卡片 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          <MintCard
            tier="whitelist"
            title="白名单通行证"
            description="早期支持者专属，享受最优价格和全部权益"
            price={PRICES.WHITELIST}
            benefits={[
              '无限制内容阅读权限',
              '社区治理投票权',
              '专属线下活动邀请',
              'VC网络连接机会',
              '优先获得新项目信息',
              '专属Discord频道访问'
            ]}
            onMintSuccess={handleMintSuccess}
          />

          <MintCard
            tier="student"
            title="学生通行证"
            description="学生专享优惠价格，助力学习成长"
            price={PRICES.STUDENT}
            benefits={[
              '无限制内容阅读权限',
              '学习资源优先访问',
              '导师一对一指导机会',
              '实习和工作推荐',
              '学生专属活动',
              '技能认证和证书'
            ]}
            onMintSuccess={handleMintSuccess}
          />
        </div>

        {/* 用户NFT展示 */}
        {isConnected && (
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
              我的通行证
            </h3>
            <NFTGrid tokenIds={userNFTs} />
          </div>
        )}

        {/* 权益说明 */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-16">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
            通行证权益详解
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">无限阅读</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                访问所有付费内容和研究报告
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">投票权</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                参与社区重要决策投票
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">专属活动</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                线下聚会和专属活动邀请
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-amber-600 dark:text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">VC网络</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                连接顶级投资人和创业者
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600 dark:text-gray-400">
            <p>&copy; 2024 AkashaDao. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
