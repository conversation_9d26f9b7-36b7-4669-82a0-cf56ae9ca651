body {
  margin: 0;
  padding: 0;
  font-family: system-ui, -apple-system, sans-serif;
  background: #0a0a0a;
  color: #ededed;
}

* {
  box-sizing: border-box;
}

.min-h-screen {
  min-height: 100vh;
}

.bg-gradient-to-br {
  background: linear-gradient(to bottom right, #0f172a, #581c87, #0f172a);
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.font-bold {
  font-weight: 700;
}

.text-white {
  color: white;
}

.mb-4 {
  margin-bottom: 1rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-slate-300 {
  color: #cbd5e1;
}

.mb-8 {
  margin-bottom: 2rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.bg-gradient-to-r {
  background: linear-gradient(to right, #9333ea, #2563eb);
}

.hover\:from-purple-700:hover {
  background: linear-gradient(to right, #7c3aed, #1d4ed8);
}

.rounded-xl {
  border-radius: 0.75rem;
}

.font-medium {
  font-weight: 500;
}

.transition-all {
  transition: all 0.3s ease;
}

.transform {
  transform: translateZ(0);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

a {
  text-decoration: none;
  cursor: pointer;
}
