'use client';

import { useState } from 'react';
import { useAccount } from 'wagmi';
import { WalletButton } from '@/components/wallet/WalletButton';
import { MintSection } from '@/components/nft/MintSection';
import { NFTPreview } from '@/components/nft/NFTPreview';
import { useWhitelist } from '@/hooks/useWhitelist';

export default function MintPage() {
  const { address, isConnected } = useAccount();
  const { whitelistData, studentData, isLoading } = useWhitelist();
  const [selectedTier, setSelectedTier] = useState<'whitelist' | 'student'>('whitelist');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="flex justify-between items-center p-6 bg-slate-800/50 backdrop-blur-sm border-b border-slate-700">
        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg"></div>
          <h1 className="text-xl font-bold text-white">AkashaDao</h1>
        </div>
        <WalletButton />
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-12">
        <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Left Side - NFT Preview */}
          <div className="space-y-6">
            <NFTPreview tier={selectedTier} />

            {/* Benefits Section */}
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
              <h3 className="text-lg font-bold text-white mb-4">
                {selectedTier === 'whitelist' ? 'Whitelist' : 'Student'} Holder Benefits
              </h3>
              <div className="space-y-2">
                {selectedTier === 'whitelist' ? (
                  <>
                    <div className="flex items-center text-sm text-slate-300">
                      <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                      投票权和治理参与
                    </div>
                    <div className="flex items-center text-sm text-slate-300">
                      <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                      无限制内容访问权限
                    </div>
                    <div className="flex items-center text-sm text-slate-300">
                      <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                      优先客户支持
                    </div>
                    <div className="flex items-center text-sm text-slate-300">
                      <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                      独家活动和空投
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-center text-sm text-slate-300">
                      <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                      学习资源优先访问
                    </div>
                    <div className="flex items-center text-sm text-slate-300">
                      <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                      导师一对一指导
                    </div>
                    <div className="flex items-center text-sm text-slate-300">
                      <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                      实习和工作推荐
                    </div>
                    <div className="flex items-center text-sm text-slate-300">
                      <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                      技能认证和证书
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Right Side - Mint Interface */}
          <div className="space-y-6">
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
              <h2 className="text-2xl font-bold text-white mb-6">AKASHA DAO PASS</h2>
              
              {/* Tier Selection */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-slate-300 mb-3">Status:</label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSelectedTier('whitelist')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                      selectedTier === 'whitelist'
                        ? 'bg-gradient-to-r from-yellow-500 to-orange-500 text-black'
                        : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                    }`}
                  >
                    Whitelist
                  </button>
                  <button
                    onClick={() => setSelectedTier('student')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                      selectedTier === 'student'
                        ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white'
                        : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                    }`}
                  >
                    Student
                  </button>
                </div>
              </div>

              {/* Mint Section */}
              <MintSection 
                tier={selectedTier}
                whitelistData={whitelistData}
                studentData={studentData}
                isLoading={isLoading}
              />
            </div>

            {/* Phase Information */}
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
              <h3 className="text-lg font-bold text-white mb-4">铸造阶段</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-slate-300">Phase 1 (Whitelist):</span>
                  <span className="text-sm text-green-400">进行中</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-slate-300">Phase 2 (Student):</span>
                  <span className="text-sm text-yellow-400">即将开始</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-slate-300">Phase 3 (Public):</span>
                  <span className="text-sm text-slate-500">待定</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
