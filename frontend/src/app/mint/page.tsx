'use client';

import { useState } from 'react';
import { useAccount } from 'wagmi';
import { WalletButton } from '@/components/wallet/WalletButton';
import { MintSection } from '@/components/nft/MintSection';
import { NFTPreview } from '@/components/nft/NFTPreview';
import { useWhitelist } from '@/hooks/useWhitelist';
import { Logo } from '@/components/ui/Logo';

export default function MintPage() {
  const { address, isConnected } = useAccount();
  const { whitelistData, studentData, isLoading } = useWhitelist();
  const [selectedTier, setSelectedTier] = useState<'whitelist' | 'student'>('whitelist');

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 现代化背景系统 */}
      <div className="absolute inset-0">
        {/* 主背景 */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-60"
          style={{
            backgroundImage: 'url(/images/mint-hero-background.webp)',
            backgroundBlendMode: 'multiply'
          }}
        ></div>

        {/* 渐变叠加 */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/90 via-purple-900/80 to-pink-900/90"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>

        {/* 动态装饰元素 */}
        <div
          className="absolute top-10 right-20 w-40 h-40 opacity-30 animate-pulse"
          style={{
            backgroundImage: 'url(/images/nft-holographic-frame.webp)',
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            filter: 'hue-rotate(45deg)'
          }}
        ></div>

        <div
          className="absolute bottom-10 left-20 w-48 h-36 opacity-25 animate-float"
          style={{
            backgroundImage: 'url(/images/mint-particle-effects.webp)',
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat'
          }}
        ></div>

        {/* 光效装饰 */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

        {/* 网格装饰 */}
        <div className="absolute inset-0 opacity-5">
          <div className="h-full w-full" style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}></div>
        </div>
      </div>

      {/* 现代化Header */}
      <header className="relative z-10 flex justify-between items-center p-8 bg-black/20 backdrop-blur-xl border-b border-white/10">
        <Logo size="md" showText={true} href="/" />
        <div className="flex items-center space-x-4">
          <div className="hidden md:flex items-center space-x-2 px-4 py-2 bg-white/5 backdrop-blur-sm rounded-full border border-white/10">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-white/80">Mainnet</span>
          </div>
          <WalletButton />
        </div>
      </header>

      {/* 页面标题区域 */}
      <div className="relative z-10 text-center py-16">
        <div className="container mx-auto px-6">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
            AKASHA DAO
          </h1>
          <p className="text-xl md:text-2xl text-white/80 mb-4 font-light">
            Community Membership Pass
          </p>
          <div className="flex justify-center items-center space-x-4 text-sm text-white/60">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Live Mint</span>
            </div>
            <div className="w-1 h-1 bg-white/30 rounded-full"></div>
            <span>Limited Edition</span>
            <div className="w-1 h-1 bg-white/30 rounded-full"></div>
            <span>Exclusive Access</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-6 pb-20">
        <div className="grid lg:grid-cols-5 gap-12 max-w-7xl mx-auto">
          {/* Left Side - NFT Preview (3 columns) */}
          <div className="lg:col-span-3 space-y-8">
            {/* NFT展示卡片 */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
              <div className="relative bg-black/40 backdrop-blur-xl rounded-3xl p-8 border border-white/10 hover:border-white/20 transition-all duration-500">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">NFT Preview</h2>
                    <p className="text-white/60">Select your membership tier</p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedTier('whitelist')}
                      className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                        selectedTier === 'whitelist'
                          ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-black shadow-lg shadow-amber-500/25'
                          : 'bg-white/10 text-white/70 hover:bg-white/20 border border-white/10'
                      }`}
                    >
                      Whitelist
                    </button>
                    <button
                      onClick={() => setSelectedTier('student')}
                      className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                        selectedTier === 'student'
                          ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg shadow-blue-500/25'
                          : 'bg-white/10 text-white/70 hover:bg-white/20 border border-white/10'
                      }`}
                    >
                      Student
                    </button>
                  </div>
                </div>

                <div className="relative">
                  <NFTPreview tier={selectedTier} />
                </div>
              </div>
            </div>

            {/* Benefits Section - 重新设计 */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-3xl blur-xl"></div>
              <div className="relative bg-black/40 backdrop-blur-xl rounded-3xl p-8 border border-white/10">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">
                      {selectedTier === 'whitelist' ? 'Whitelist' : 'Student'} Benefits
                    </h3>
                    <p className="text-white/60">Exclusive holder privileges</p>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  {selectedTier === 'whitelist' ? (
                    <>
                      <div className="flex items-center p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center mr-3">
                          <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                        </div>
                        <span className="text-white/90">投票权和治理参与</span>
                      </div>
                      <div className="flex items-center p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center mr-3">
                          <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                        </div>
                        <span className="text-white/90">无限制内容访问</span>
                      </div>
                      <div className="flex items-center p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center mr-3">
                          <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                        </div>
                        <span className="text-white/90">优先客户支持</span>
                      </div>
                      <div className="flex items-center p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center mr-3">
                          <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                        </div>
                        <span className="text-white/90">独家活动和空投</span>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex items-center p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3">
                          <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                        </div>
                        <span className="text-white/90">学习资源优先访问</span>
                      </div>
                      <div className="flex items-center p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3">
                          <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                        </div>
                        <span className="text-white/90">导师一对一指导</span>
                      </div>
                      <div className="flex items-center p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3">
                          <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                        </div>
                        <span className="text-white/90">实习和工作推荐</span>
                      </div>
                      <div className="flex items-center p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3">
                          <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                        </div>
                        <span className="text-white/90">技能认证和证书</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Mint Interface (2 columns) */}
          <div className="lg:col-span-2 space-y-8">
            {/* Mint Interface */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
              <div className="relative bg-black/40 backdrop-blur-xl rounded-3xl p-8 border border-white/10 hover:border-white/20 transition-all duration-500">
                <div className="flex items-center mb-8">
                  <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-xl flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">Mint Your Pass</h2>
                    <p className="text-white/60">Join the AkashaDao community</p>
                  </div>
                </div>

                {/* Mint Section */}
                <MintSection
                  tier={selectedTier}
                  whitelistData={whitelistData}
                  studentData={studentData}
                  isLoading={isLoading}
                />
              </div>
            </div>

            {/* Phase Information - 重新设计 */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-3xl blur-xl"></div>
              <div className="relative bg-black/40 backdrop-blur-xl rounded-3xl p-8 border border-white/10">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Mint Phases</h3>
                    <p className="text-white/60">Current minting schedule</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-green-500/10 rounded-xl border border-green-500/20">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                      <div>
                        <span className="text-white font-medium">Phase 1 - Whitelist</span>
                        <p className="text-green-400 text-sm">Currently Active</p>
                      </div>
                    </div>
                    <span className="px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-sm font-medium">Live</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-yellow-500/10 rounded-xl border border-yellow-500/20">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-yellow-400 rounded-full mr-3"></div>
                      <div>
                        <span className="text-white font-medium">Phase 2 - Student</span>
                        <p className="text-yellow-400 text-sm">Coming Soon</p>
                      </div>
                    </div>
                    <span className="px-3 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-sm font-medium">Pending</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-white/30 rounded-full mr-3"></div>
                      <div>
                        <span className="text-white font-medium">Phase 3 - Public</span>
                        <p className="text-white/40 text-sm">To Be Announced</p>
                      </div>
                    </div>
                    <span className="px-3 py-1 bg-white/10 text-white/60 rounded-full text-sm font-medium">TBA</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-3xl blur-xl"></div>
              <div className="relative bg-black/40 backdrop-blur-xl rounded-3xl p-8 border border-white/10">
                <h3 className="text-lg font-bold text-white mb-6">Mint Statistics</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-white/5 rounded-xl">
                    <div className="text-2xl font-bold text-white mb-1">450</div>
                    <div className="text-sm text-white/60">Minted</div>
                  </div>
                  <div className="text-center p-4 bg-white/5 rounded-xl">
                    <div className="text-2xl font-bold text-white mb-1">50</div>
                    <div className="text-sm text-white/60">Remaining</div>
                  </div>
                  <div className="text-center p-4 bg-white/5 rounded-xl">
                    <div className="text-2xl font-bold text-purple-400 mb-1">0.05</div>
                    <div className="text-sm text-white/60">ETH Price</div>
                  </div>
                  <div className="text-center p-4 bg-white/5 rounded-xl">
                    <div className="text-2xl font-bold text-cyan-400 mb-1">90%</div>
                    <div className="text-sm text-white/60">Progress</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
