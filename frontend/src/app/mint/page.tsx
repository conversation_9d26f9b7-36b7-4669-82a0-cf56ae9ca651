'use client';

import { useState } from 'react';
import { useAccount } from 'wagmi';
import { WalletButton } from '@/components/wallet/WalletButton';
import { MintSection } from '@/components/nft/MintSection';
import { NFTPreview } from '@/components/nft/NFTPreview';
import { useWhitelist } from '@/hooks/useWhitelist';
import { Logo } from '@/components/ui/Logo';

export default function MintPage() {
  const { address, isConnected } = useAccount();
  const { whitelistData, studentData, isLoading } = useWhitelist();
  const [selectedTier, setSelectedTier] = useState<'whitelist' | 'student'>('whitelist');

  return (
    <div className="min-h-screen relative">
      {/* 奢华背景系统 */}
      <div className="fixed inset-0 z-0">
        {/* 主背景 */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(/images/luxury-mint-background.webp)',
          }}
        ></div>

        {/* 深色叠加层 */}
        <div className="absolute inset-0 bg-black/60"></div>

        {/* 渐变装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-amber-900/20"></div>

        {/* 动态光效 */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-purple-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-amber-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* 简洁Header */}
      <header className="relative z-20 flex justify-between items-center p-6 bg-black/30 backdrop-blur-xl">
        <Logo size="md" showText={true} href="/" />
        <WalletButton />
      </header>

      {/* 主要内容区域 */}
      <div className="relative z-10 min-h-screen flex items-center">
        <div className="container mx-auto px-6">
          <div className="max-w-7xl mx-auto">

            {/* 顶部标题区域 */}
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
                <span className="bg-gradient-to-r from-amber-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  AKASHA DAO
                </span>
              </h1>
              <p className="text-xl text-white/70 mb-8">Exclusive Community Membership Pass</p>

              {/* 状态指示器 */}
              <div className="flex justify-center items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2 px-4 py-2 bg-green-500/10 rounded-full border border-green-500/20">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 font-medium">Live Mint</span>
                </div>
                <div className="flex items-center space-x-2 px-4 py-2 bg-purple-500/10 rounded-full border border-purple-500/20">
                  <span className="text-purple-400 font-medium">450/500 Minted</span>
                </div>
              </div>
            </div>

            {/* 主要布局 - 居中卡片式设计 */}
            <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
              {/* 左侧 - NFT展示 */}
              <div className="space-y-8">
                {/* NFT预览卡片 */}
                <div className="relative group">
                  {/* 发光效果 */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-amber-500/50 via-purple-500/50 to-cyan-500/50 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000"></div>

                  <div className="relative bg-black/60 backdrop-blur-xl rounded-2xl p-8 border border-white/20">
                    {/* 顶部选择器 */}
                    <div className="flex justify-center mb-8">
                      <div className="flex bg-black/40 rounded-xl p-1 border border-white/10">
                        <button
                          onClick={() => setSelectedTier('whitelist')}
                          className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                            selectedTier === 'whitelist'
                              ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-black shadow-lg'
                              : 'text-white/70 hover:text-white hover:bg-white/10'
                          }`}
                        >
                          Whitelist
                        </button>
                        <button
                          onClick={() => setSelectedTier('student')}
                          className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                            selectedTier === 'student'
                              ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg'
                              : 'text-white/70 hover:text-white hover:bg-white/10'
                          }`}
                        >
                          Student
                        </button>
                      </div>
                    </div>

                    {/* NFT预览 */}
                    <div className="relative">
                      <NFTPreview tier={selectedTier} />
                    </div>

                    {/* 权益展示 */}
                    <div className="mt-8 pt-8 border-t border-white/10">
                      <h3 className="text-lg font-bold text-white mb-6 text-center">
                        {selectedTier === 'whitelist' ? 'Whitelist' : 'Student'} Holder Benefits
                      </h3>

                      <div className="grid grid-cols-1 gap-3">
                        {selectedTier === 'whitelist' ? (
                          <>
                            <div className="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                              <div className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                              <span className="text-white/90 text-sm">投票权和治理参与</span>
                            </div>
                            <div className="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                              <div className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                              <span className="text-white/90 text-sm">无限制内容访问权限</span>
                            </div>
                            <div className="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                              <div className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                              <span className="text-white/90 text-sm">优先客户支持</span>
                            </div>
                            <div className="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
                              <div className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                              <span className="text-white/90 text-sm">独家活动和空投</span>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="flex items-center p-3 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg border border-blue-500/20">
                              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
                              <span className="text-white/90 text-sm">学习资源优先访问</span>
                            </div>
                            <div className="flex items-center p-3 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg border border-blue-500/20">
                              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
                              <span className="text-white/90 text-sm">导师一对一指导</span>
                            </div>
                            <div className="flex items-center p-3 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg border border-blue-500/20">
                              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
                              <span className="text-white/90 text-sm">实习和工作推荐</span>
                            </div>
                            <div className="flex items-center p-3 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg border border-blue-500/20">
                              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
                              <span className="text-white/90 text-sm">技能认证和证书</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 右侧 - 铸造界面 */}
              <div className="space-y-8">
                {/* 主要铸造卡片 */}
                <div className="relative group">
                  {/* 发光效果 */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/50 via-pink-500/50 to-cyan-500/50 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000"></div>

                  <div className="relative bg-black/60 backdrop-blur-xl rounded-2xl p-8 border border-white/20">
                    <div className="text-center mb-8">
                      <h2 className="text-2xl font-bold text-white mb-2">Mint Your Pass</h2>
                      <p className="text-white/60">Join the exclusive AkashaDao community</p>
                    </div>

                    {/* Mint Section */}
                    <MintSection
                      tier={selectedTier}
                      whitelistData={whitelistData}
                      studentData={studentData}
                      isLoading={isLoading}
                    />
                  </div>
                </div>

                {/* 铸造阶段信息 */}
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-green-500/30 to-blue-500/30 rounded-2xl blur opacity-20"></div>

                  <div className="relative bg-black/60 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                    <h3 className="text-lg font-bold text-white mb-6 text-center">Mint Phases</h3>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-4 bg-green-500/10 rounded-xl border border-green-500/20">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                          <div>
                            <span className="text-white font-medium text-sm">Whitelist Phase</span>
                            <p className="text-green-400 text-xs">Active Now</p>
                          </div>
                        </div>
                        <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded-full text-xs font-medium">LIVE</span>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-yellow-500/10 rounded-xl border border-yellow-500/20">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-yellow-400 rounded-full mr-3"></div>
                          <div>
                            <span className="text-white font-medium text-sm">Student Phase</span>
                            <p className="text-yellow-400 text-xs">Coming Soon</p>
                          </div>
                        </div>
                        <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium">SOON</span>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-white/30 rounded-full mr-3"></div>
                          <div>
                            <span className="text-white font-medium text-sm">Public Phase</span>
                            <p className="text-white/40 text-xs">To Be Announced</p>
                          </div>
                        </div>
                        <span className="px-2 py-1 bg-white/10 text-white/60 rounded-full text-xs font-medium">TBA</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 统计信息 */}
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-2xl blur opacity-20"></div>

                  <div className="relative bg-black/60 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                    <h3 className="text-lg font-bold text-white mb-6 text-center">Live Stats</h3>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-500/20">
                        <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-1">450</div>
                        <div className="text-xs text-white/60">Total Minted</div>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-xl border border-cyan-500/20">
                        <div className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1">50</div>
                        <div className="text-xs text-white/60">Remaining</div>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-xl border border-amber-500/20">
                        <div className="text-2xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-1">0.05</div>
                        <div className="text-xs text-white/60">ETH Price</div>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-xl border border-green-500/20">
                        <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-1">90%</div>
                        <div className="text-xs text-white/60">Progress</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
        </div>
      </div>
    </div>
  );
}
