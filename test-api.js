// 简单的API测试脚本
const { MerkleTree } = require('merkletreejs');
const { keccak256 } = require('viem');

// 模拟白名单地址
const WHITELIST_ADDRESSES = [
  '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266',
  '0x70997970C51812dc3A010C7d01b50e0d17dc79C8',
  '0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC',
  '0x90F79bf6EB2c4f870365E785982E1f101E93b906',
  '0x15d34AAf54267DB7D7c367839AAf71A00a2C6A65',
];

const STUDENT_ADDRESSES = [
  '0x9965507D1a55bcC2695C58ba16FB37d819B0A4dc',
  '0x976EA74026E726554dB657fA54763abd0C3a0aa9',
  '******************************************',
  '******************************************',
  '******************************************',
];

// 生成Merkle Tree
function generateMerkleTree(addresses) {
  const leaves = addresses.map(addr => keccak256(addr));
  return new MerkleTree(leaves, keccak256, { sortPairs: true });
}

// 获取Merkle Proof
function getMerkleProof(addresses, targetAddress) {
  const tree = generateMerkleTree(addresses);
  const leaf = keccak256(targetAddress);
  return tree.getHexProof(leaf);
}

// 测试白名单功能
function testWhitelist() {
  console.log('🧪 测试白名单功能...\n');

  // 测试白名单
  const whitelistTree = generateMerkleTree(WHITELIST_ADDRESSES);
  const whitelistRoot = whitelistTree.getHexRoot();
  console.log('📋 白名单 Merkle Root:', whitelistRoot);
  console.log('📋 白名单地址数量:', WHITELIST_ADDRESSES.length);

  // 测试学生名单
  const studentTree = generateMerkleTree(STUDENT_ADDRESSES);
  const studentRoot = studentTree.getHexRoot();
  console.log('🎓 学生名单 Merkle Root:', studentRoot);
  console.log('🎓 学生名单地址数量:', STUDENT_ADDRESSES.length);

  console.log('\n🔍 测试地址验证...');

  // 测试白名单地址
  const testAddress1 = WHITELIST_ADDRESSES[0];
  const proof1 = getMerkleProof(WHITELIST_ADDRESSES, testAddress1);
  console.log(`✅ 地址 ${testAddress1} 在白名单中`);
  console.log('   Merkle Proof:', proof1);

  // 测试学生地址
  const testAddress2 = STUDENT_ADDRESSES[0];
  const proof2 = getMerkleProof(STUDENT_ADDRESSES, testAddress2);
  console.log(`✅ 地址 ${testAddress2} 在学生名单中`);
  console.log('   Merkle Proof:', proof2);

  // 测试不在名单中的地址
  const testAddress3 = '******************************************';
  const isInWhitelist = WHITELIST_ADDRESSES.includes(testAddress3);
  const isInStudentList = STUDENT_ADDRESSES.includes(testAddress3);
  console.log(`❌ 地址 ${testAddress3} 不在任何名单中`);
  console.log('   白名单:', isInWhitelist);
  console.log('   学生名单:', isInStudentList);

  console.log('\n✨ 白名单功能测试完成！');
}

// 运行测试
if (require.main === module) {
  testWhitelist();
}

module.exports = {
  generateMerkleTree,
  getMerkleProof,
  WHITELIST_ADDRESSES,
  STUDENT_ADDRESSES,
};
