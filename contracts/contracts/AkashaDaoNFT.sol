// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/cryptography/MerkleProof.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";


/**
 * @title AkashaDaoNFT
 * @dev AkashaDao社区入场券NFT合约
 * 支持白名单mint、分层权益管理、限购机制、优惠价格和抽奖功能
 */
contract AkashaDaoNFT is ERC721, ERC721URIStorage, Ownable, ReentrancyGuard {
    // NFT层级枚举
    enum NFTTier {
        WHITELIST,  // 白名单层级
        STUDENT     // 底层（学生）
    }

    // NFT信息结构体
    struct NFTInfo {
        NFTTier tier;
        uint256 mintTime;
        bool hasVotingRights;
        bool hasUnlimitedAccess;
    }

    // 计数器
    uint256 private _tokenIdCounter;
    
    // 合约配置
    uint256 public constant MAX_WHITELIST_SUPPLY = 100;
    uint256 public constant WHITELIST_MINT_PERCENTAGE = 80; // 80%白名单mint
    uint256 public constant LOTTERY_PERCENTAGE = 20; // 20%抽奖
    uint256 public constant MAX_MINT_PER_ADDRESS = 1;
    
    // 价格配置
    uint256 public whitelistPrice = 0.05 ether; // 白名单优惠价格
    uint256 public publicPrice = 0.08 ether;    // 公开价格
    uint256 public studentPrice = 0.03 ether;   // 学生价格
    
    // 供应量跟踪
    uint256 public whitelistMinted = 0;
    uint256 public studentMinted = 0;
    uint256 public maxStudentSupply = 500; // 可调整的学生NFT最大供应量
    
    // 白名单和抽奖
    bytes32 public whitelistMerkleRoot;
    bytes32 public lotteryMerkleRoot;
    bytes32 public studentMerkleRoot;
    
    // 映射
    mapping(uint256 => NFTInfo) public nftInfo;
    mapping(address => uint256) public mintedCount;
    mapping(address => bool) public hasClaimedWhitelist;
    mapping(address => bool) public hasClaimedLottery;
    mapping(address => bool) public hasClaimedStudent;
    
    // 销售状态
    bool public whitelistSaleActive = false;
    bool public lotterySaleActive = false;
    bool public studentSaleActive = false;
    
    // 元数据URI
    string private _whitelistTokenURI;
    string private _studentTokenURI;
    
    // 事件
    event WhitelistMint(address indexed to, uint256 indexed tokenId);
    event LotteryMint(address indexed to, uint256 indexed tokenId);
    event StudentMint(address indexed to, uint256 indexed tokenId);
    event SaleStateChanged(string saleType, bool active);
    event PriceUpdated(string priceType, uint256 newPrice);
    event MerkleRootUpdated(string rootType, bytes32 newRoot);

    constructor(
        string memory whitelistTokenURI,
        string memory studentTokenURI
    ) ERC721("AkashaDao Community Pass", "AKASHA") Ownable(msg.sender) {
        _whitelistTokenURI = whitelistTokenURI;
        _studentTokenURI = studentTokenURI;
    }

    /**
     * @dev 白名单mint功能
     */
    function whitelistMint(bytes32[] calldata merkleProof) 
        external 
        payable 
        nonReentrant 
    {
        require(whitelistSaleActive, "Whitelist sale not active");
        require(!hasClaimedWhitelist[msg.sender], "Already claimed whitelist");
        require(mintedCount[msg.sender] < MAX_MINT_PER_ADDRESS, "Mint limit exceeded");
        require(whitelistMinted < (MAX_WHITELIST_SUPPLY * WHITELIST_MINT_PERCENTAGE / 100), "Whitelist supply exceeded");
        require(msg.value >= whitelistPrice, "Insufficient payment");
        
        // 验证白名单
        bytes32 leaf = keccak256(abi.encodePacked(msg.sender));
        require(MerkleProof.verify(merkleProof, whitelistMerkleRoot, leaf), "Invalid whitelist proof");
        
        _mintNFT(msg.sender, NFTTier.WHITELIST);
        hasClaimedWhitelist[msg.sender] = true;
        whitelistMinted++;
        
        emit WhitelistMint(msg.sender, _tokenIdCounter - 1);
    }

    /**
     * @dev 抽奖mint功能
     */
    function lotteryMint(bytes32[] calldata merkleProof) 
        external 
        payable 
        nonReentrant 
    {
        require(lotterySaleActive, "Lottery sale not active");
        require(!hasClaimedLottery[msg.sender], "Already claimed lottery");
        require(mintedCount[msg.sender] < MAX_MINT_PER_ADDRESS, "Mint limit exceeded");
        require(whitelistMinted < MAX_WHITELIST_SUPPLY, "Total whitelist supply exceeded");
        require(msg.value >= publicPrice, "Insufficient payment");
        
        // 验证抽奖资格
        bytes32 leaf = keccak256(abi.encodePacked(msg.sender));
        require(MerkleProof.verify(merkleProof, lotteryMerkleRoot, leaf), "Invalid lottery proof");
        
        _mintNFT(msg.sender, NFTTier.WHITELIST);
        hasClaimedLottery[msg.sender] = true;
        whitelistMinted++;
        
        emit LotteryMint(msg.sender, _tokenIdCounter - 1);
    }

    /**
     * @dev 学生mint功能
     */
    function studentMint(bytes32[] calldata merkleProof) 
        external 
        payable 
        nonReentrant 
    {
        require(studentSaleActive, "Student sale not active");
        require(!hasClaimedStudent[msg.sender], "Already claimed student NFT");
        require(mintedCount[msg.sender] < MAX_MINT_PER_ADDRESS, "Mint limit exceeded");
        require(studentMinted < maxStudentSupply, "Student supply exceeded");
        require(msg.value >= studentPrice, "Insufficient payment");
        
        // 验证学生资格
        bytes32 leaf = keccak256(abi.encodePacked(msg.sender));
        require(MerkleProof.verify(merkleProof, studentMerkleRoot, leaf), "Invalid student proof");
        
        _mintNFT(msg.sender, NFTTier.STUDENT);
        hasClaimedStudent[msg.sender] = true;
        studentMinted++;
        
        emit StudentMint(msg.sender, _tokenIdCounter - 1);
    }

    /**
     * @dev 内部mint函数
     */
    function _mintNFT(address to, NFTTier tier) internal {
        uint256 tokenId = _tokenIdCounter;
        _tokenIdCounter++;
        
        _safeMint(to, tokenId);
        
        // 设置NFT信息
        nftInfo[tokenId] = NFTInfo({
            tier: tier,
            mintTime: block.timestamp,
            hasVotingRights: tier == NFTTier.WHITELIST,
            hasUnlimitedAccess: true
        });
        
        mintedCount[to]++;
        
        // 设置token URI
        if (tier == NFTTier.WHITELIST) {
            _setTokenURI(tokenId, _whitelistTokenURI);
        } else {
            _setTokenURI(tokenId, _studentTokenURI);
        }
    }

    // 管理员功能
    function setWhitelistSaleActive(bool active) external onlyOwner {
        whitelistSaleActive = active;
        emit SaleStateChanged("whitelist", active);
    }
    
    function setLotterySaleActive(bool active) external onlyOwner {
        lotterySaleActive = active;
        emit SaleStateChanged("lottery", active);
    }
    
    function setStudentSaleActive(bool active) external onlyOwner {
        studentSaleActive = active;
        emit SaleStateChanged("student", active);
    }
    
    function setWhitelistMerkleRoot(bytes32 root) external onlyOwner {
        whitelistMerkleRoot = root;
        emit MerkleRootUpdated("whitelist", root);
    }
    
    function setLotteryMerkleRoot(bytes32 root) external onlyOwner {
        lotteryMerkleRoot = root;
        emit MerkleRootUpdated("lottery", root);
    }
    
    function setStudentMerkleRoot(bytes32 root) external onlyOwner {
        studentMerkleRoot = root;
        emit MerkleRootUpdated("student", root);
    }
    
    function updatePrices(
        uint256 _whitelistPrice,
        uint256 _publicPrice,
        uint256 _studentPrice
    ) external onlyOwner {
        whitelistPrice = _whitelistPrice;
        publicPrice = _publicPrice;
        studentPrice = _studentPrice;
        
        emit PriceUpdated("whitelist", _whitelistPrice);
        emit PriceUpdated("public", _publicPrice);
        emit PriceUpdated("student", _studentPrice);
    }
    
    function setMaxStudentSupply(uint256 _maxStudentSupply) external onlyOwner {
        maxStudentSupply = _maxStudentSupply;
    }
    
    function updateTokenURIs(
        string memory whitelistURI,
        string memory studentURI
    ) external onlyOwner {
        _whitelistTokenURI = whitelistURI;
        _studentTokenURI = studentURI;
    }

    // 查询功能
    function getNFTInfo(uint256 tokenId) external view returns (NFTInfo memory) {
        require(_ownerOf(tokenId) != address(0), "Token does not exist");
        return nftInfo[tokenId];
    }

    function hasVotingRights(uint256 tokenId) external view returns (bool) {
        require(_ownerOf(tokenId) != address(0), "Token does not exist");
        return nftInfo[tokenId].hasVotingRights;
    }

    function hasUnlimitedAccess(uint256 tokenId) external view returns (bool) {
        require(_ownerOf(tokenId) != address(0), "Token does not exist");
        return nftInfo[tokenId].hasUnlimitedAccess;
    }
    
    function totalSupply() external view returns (uint256) {
        return _tokenIdCounter;
    }
    
    function remainingWhitelistSupply() external view returns (uint256) {
        uint256 maxWhitelistMint = MAX_WHITELIST_SUPPLY * WHITELIST_MINT_PERCENTAGE / 100;
        return maxWhitelistMint > whitelistMinted ? maxWhitelistMint - whitelistMinted : 0;
    }
    
    function remainingLotterySupply() external view returns (uint256) {
        uint256 maxLotteryMint = MAX_WHITELIST_SUPPLY * LOTTERY_PERCENTAGE / 100;
        uint256 lotteryMinted = whitelistMinted > (MAX_WHITELIST_SUPPLY * WHITELIST_MINT_PERCENTAGE / 100) 
            ? whitelistMinted - (MAX_WHITELIST_SUPPLY * WHITELIST_MINT_PERCENTAGE / 100) : 0;
        return maxLotteryMint > lotteryMinted ? maxLotteryMint - lotteryMinted : 0;
    }
    
    function remainingStudentSupply() external view returns (uint256) {
        return maxStudentSupply > studentMinted ? maxStudentSupply - studentMinted : 0;
    }

    // 提取资金
    function withdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "Withdrawal failed");
    }

    // 重写必要的函数
    function _burn(uint256 tokenId) internal override {
        super._burn(tokenId);
    }

    function tokenURI(uint256 tokenId)
        public
        view
        override(ERC721, ERC721URIStorage)
        returns (string memory)
    {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC721, ERC721URIStorage)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }
}
